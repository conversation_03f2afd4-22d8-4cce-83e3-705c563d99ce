@echo off
REM MonIt项目环境设置脚本 (Windows)

echo ========================================
echo MonIt 电商爬虫系统 - 环境设置
echo ========================================

REM 检查conda是否安装
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到conda命令，请先安装Miniconda或Anaconda
    pause
    exit /b 1
)

echo 1. 检查conda环境...
conda info --envs | findstr "monit" >nul
if %ERRORLEVEL% EQU 0 (
    echo MonIt环境已存在，是否重新创建？ [y/N]
    set /p recreate=
    if /i "%recreate%"=="y" (
        echo 删除现有环境...
        conda env remove -n monit -y
    ) else (
        echo 使用现有环境
        goto activate_env
    )
)

echo 2. 创建conda环境 (Python 3.11)...
conda create -n monit python=3.11 -y
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 创建conda环境失败
    pause
    exit /b 1
)

:activate_env
echo 3. 激活环境...
call conda activate monit
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 激活环境失败
    pause
    exit /b 1
)

echo 4. 升级pip...
python -m pip install --upgrade pip

echo 5. 安装项目依赖...
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 安装依赖失败
    pause
    exit /b 1
)

echo 6. 创建必要目录...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "temp" mkdir temp

echo 7. 检查安装结果...
echo 检查Python版本:
python --version

echo 检查关键包:
python -c "import pandas; print(f'pandas: {pandas.__version__}')"
python -c "import redis; print(f'redis: {redis.__version__}')"
python -c "import celery; print(f'celery: {celery.__version__}')"
python -c "import fastapi; print(f'fastapi: {fastapi.__version__}')"

echo.
echo ========================================
echo 环境设置完成！
echo ========================================
echo.
echo 使用方法:
echo 1. 激活环境: conda activate monit
echo 2. 运行测试: python backend/scripts/quick_test.py
echo 3. 启动服务: python backend/scripts/start_task_manager.py
echo.
echo 注意: 请确保Redis服务正在运行
echo 启动Redis: redis-server
echo.

pause
