#!/usr/bin/env python3
"""
持久化任务管理器启动脚本

启动集成了持久化功能的TaskManager
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.persistent_task_manager import PersistentTaskManager
from backend.app.core.task_manager import TaskManagerConfig
from backend.app.core.task_splitter import SplitterConfig
from backend.app.core.rate_limiter import RateLimitConfig
from backend.app.core.retry_manager import RetryConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/task_manager.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class PersistentTaskManagerService:
    """持久化任务管理器服务"""
    
    def __init__(self):
        self.task_manager: PersistentTaskManager = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # 配置
        self.redis_url = "redis://localhost:6379/0"
        
    async def start(self):
        """启动服务"""
        try:
            logger.info("🚀 Starting Persistent Task Manager Service...")
            
            # 创建配置
            splitter_config = SplitterConfig(
                max_batch_size=50,
                min_batch_size=10,
                adaptive_sizing=True
            )
            
            rate_limiter_config = RateLimitConfig(
                max_concurrent_requests=2,
                requests_per_minute=60,
                redis_url=self.redis_url
            )
            
            retry_config = RetryConfig(
                max_retries=3,
                base_delay=60.0,
                max_delay=3600.0,
                backoff_multiplier=2.0,
                jitter=True
            )
            
            manager_config = TaskManagerConfig(
                max_concurrent_batches=2,
                batch_check_interval=5.0,
                task_timeout=1800,
                retry_failed_interval=300,
                cleanup_interval=3600,
                max_queue_size=10000,
                splitter_config=splitter_config,
                rate_limiter_config=rate_limiter_config,
                retry_config=retry_config
            )
            
            # 创建持久化任务管理器
            self.task_manager = PersistentTaskManager(manager_config, self.redis_url)
            
            # 启动任务管理器
            await self.task_manager.start()
            
            self.is_running = True
            
            logger.info("✅ Persistent Task Manager Service started successfully")
            logger.info(f"📊 Configuration:")
            logger.info(f"   Redis URL: {self.redis_url}")
            logger.info(f"   Max Concurrent Batches: {manager_config.max_concurrent_batches}")
            logger.info(f"   Max Batch Size: {splitter_config.max_batch_size}")
            logger.info(f"   Max Retries: {retry_config.max_retries}")
            
            # 显示持久化状态
            persistence_status = self.task_manager.get_persistence_status()
            logger.info(f"💾 Persistence Status:")
            logger.info(f"   Enabled: {persistence_status['persistence_enabled']}")
            logger.info(f"   Redis Connected: {persistence_status['redis_connected']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start service: {e}")
            return False
    
    async def stop(self):
        """停止服务"""
        try:
            logger.info("🛑 Stopping Persistent Task Manager Service...")
            
            self.is_running = False
            
            if self.task_manager:
                await self.task_manager.stop()
            
            self.shutdown_event.set()
            
            logger.info("✅ Service stopped successfully")
            
        except Exception as e:
            logger.error(f"❌ Error stopping service: {e}")
    
    async def run(self):
        """运行服务"""
        # 设置信号处理器
        self._setup_signal_handlers()
        
        # 启动服务
        if not await self.start():
            return False
        
        try:
            # 主循环
            logger.info("🔄 Service running... Press Ctrl+C to stop")
            
            while self.is_running:
                # 定期显示状态
                await asyncio.sleep(60)  # 每分钟显示一次状态
                
                if self.task_manager and self.is_running:
                    status = self.task_manager.get_status()
                    logger.info(f"📊 Status: {status['pending_batches']} pending, "
                               f"{status['running_batches']} running, "
                               f"{status['completed_batches']} completed")
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Service error: {e}")
        finally:
            await self.stop()
        
        return True
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def submit_test_task(self):
        """提交测试任务"""
        if not self.task_manager:
            logger.error("Task manager not initialized")
            return
        
        try:
            # 创建测试URL
            test_urls = [f"https://mercadolibre.com/test/product/{i}" for i in range(1, 21)]
            
            # 提交任务
            submission_id = await self.task_manager.submit_task(
                task_id=999,
                urls=test_urls,
                platform="mercadolibre",
                priority="normal",
                options={"test": True}
            )
            
            logger.info(f"✅ Test task submitted: {submission_id}")
            return submission_id
            
        except Exception as e:
            logger.error(f"❌ Failed to submit test task: {e}")
            return None


async def main():
    """主函数"""
    print("🚀 MonIt Persistent Task Manager Service")
    print("=" * 50)
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    service = PersistentTaskManagerService()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "test":
            # 测试模式
            logger.info("🧪 Running in test mode")
            
            if await service.start():
                # 提交测试任务
                await service.submit_test_task()
                
                # 运行一段时间
                await asyncio.sleep(30)
                
                # 显示最终状态
                status = service.task_manager.get_status()
                logger.info(f"📊 Final status: {status}")
                
                await service.stop()
            
            return
        
        elif command == "status":
            # 状态检查模式
            logger.info("📊 Checking service status")
            
            # 这里可以添加状态检查逻辑
            # 比如连接Redis检查队列状态
            
            return
    
    # 正常运行模式
    success = await service.run()
    
    if success:
        logger.info("Service completed successfully")
    else:
        logger.error("Service failed")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nService interrupted by user")
    except Exception as e:
        print(f"Service failed: {e}")
        sys.exit(1)
