#!/usr/bin/env python3
"""
测试前端调度配置显示

创建任务并检查前端页面的调度配置显示
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_frontend_schedule_display():
    """测试前端调度配置显示"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试前端调度配置显示")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/frontend-schedule-test",
            "platform": "mercadolibre",
            "title": "前端调度显示测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'frontend_schedule_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'frontend_schedule_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:frontend_schedule_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 创建带有完整调度配置的任务
        print(f"\n📋 步骤2: 创建带有完整调度配置的任务")
        
        create_data = {
            "name": "前端调度显示测试任务",
            "description": "用于测试前端调度配置显示的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "14:30",  # 每日执行时间
                "end_time": "23:45",  # 结束时间
                "max_runs": 50,
                "enable_random_delay": True,
                "random_delay_min": 10,
                "random_delay_max": 30,
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建成功: {task_id}")
            
            # 步骤3: 获取任务详情API响应
            print(f"\n📋 步骤3: 获取任务详情API响应")
            
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if detail_response.status_code == 200:
                detail_result = detail_response.json()
                task_data = detail_result.get('data', {})
                schedule_data = task_data.get('schedule', {})
                
                print(f"  API返回的调度配置:")
                print(f"    执行时间 (time): {schedule_data.get('time')}")
                print(f"    结束时间 (end_time): {schedule_data.get('end_time')}")
                print(f"    调度状态 (enabled): {schedule_data.get('enabled')}")
                print(f"    时区 (timezone): {schedule_data.get('timezone')}")
                print(f"    最大执行次数 (max_runs): {schedule_data.get('max_runs')}")
                print(f"    随机延迟 (enable_random_delay): {schedule_data.get('enable_random_delay')}")
                print(f"    延迟范围: {schedule_data.get('random_delay_min')}-{schedule_data.get('random_delay_max')}分钟")
                
                # 步骤4: 验证前端应该如何显示这些数据
                print(f"\n📋 步骤4: 前端显示验证")
                
                # 验证执行时间
                import re
                time_value = schedule_data.get('time')
                if time_value and isinstance(time_value, str) and re.match(r'^\d{2}:\d{2}$', time_value):
                    print(f"  ✅ 执行时间格式正确: {time_value} (HH:mm格式)")
                else:
                    print(f"  ❌ 执行时间格式错误: {time_value}")

                # 验证结束时间
                end_time_value = schedule_data.get('end_time')
                if end_time_value and isinstance(end_time_value, str) and re.match(r'^\d{2}:\d{2}$', end_time_value):
                    print(f"  ✅ 结束时间格式正确: {end_time_value} (HH:mm格式)")
                elif end_time_value:
                    print(f"  ⚠️ 结束时间格式需要检查: {end_time_value}")
                else:
                    print(f"  ❌ 结束时间为空: {end_time_value}")
                
                # 验证布尔值
                enabled_value = schedule_data.get('enabled')
                if isinstance(enabled_value, bool):
                    print(f"  ✅ 调度状态类型正确: {enabled_value} (布尔值)")
                else:
                    print(f"  ❌ 调度状态类型错误: {enabled_value} (类型: {type(enabled_value)})")
                
                # 验证数值
                max_runs_value = schedule_data.get('max_runs')
                if isinstance(max_runs_value, int) and max_runs_value > 0:
                    print(f"  ✅ 最大执行次数正确: {max_runs_value}")
                else:
                    print(f"  ❌ 最大执行次数错误: {max_runs_value}")
                
                # 步骤5: 提供前端访问链接
                print(f"\n📋 步骤5: 前端访问信息")
                print(f"  🌐 任务详情页面: http://localhost:3000/monitoring-tasks/{task_id}")
                print(f"  📝 任务ID: {task_id}")
                print(f"  💡 请在浏览器中访问上述链接，检查调度配置的显示效果")
                
                # 步骤6: 前端显示预期
                print(f"\n📋 步骤6: 前端显示预期")
                print(f"  预期显示效果:")
                print(f"    - 调度类型: daily")
                print(f"    - 调度状态: 已启用 (绿色标签)")
                print(f"    - 执行时间: 14:30")
                print(f"    - 结束时间: 23:45")
                print(f"    - 时区: Asia/Shanghai")
                print(f"    - 最大执行次数: 50")
                print(f"    - 随机延迟: 启用 (10-30分钟)")
                
                print(f"\n  ⚠️ 如果前端显示有问题，请检查:")
                print(f"    1. formatTimeOnly函数是否正确处理HH:mm格式")
                print(f"    2. 布尔值是否正确显示为标签")
                print(f"    3. 数值是否正确显示")
                print(f"    4. 浏览器控制台是否有错误信息")
                
            else:
                print(f"  ❌ 获取任务详情失败: {detail_response.text}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 前端调度配置显示测试完成!")
        print(f"\n💡 请访问前端页面验证显示效果，任务将保留5分钟供测试")
        
        # 等待用户测试
        input("\n按回车键继续清理测试数据...")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:frontend_schedule_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_frontend_schedule_display()
