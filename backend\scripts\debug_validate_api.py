#!/usr/bin/env python3
"""
调试任务验证API问题

测试/api/v1/tasks/validate端点的请求和响应
"""

import asyncio
import sys
import os
import json
import httpx
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


async def debug_validate_api():
    """调试验证API"""
    base_url = "http://localhost:8000"
    
    print("🔍 调试任务验证API")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        try:
            # 测试1: 检查API文档
            print("\n📋 测试1: 检查API文档")
            docs_response = await client.get(f"{base_url}/docs")
            print(f"  API文档状态码: {docs_response.status_code}")
            
            # 测试2: 检查OpenAPI规范
            print("\n📋 测试2: 检查OpenAPI规范")
            openapi_response = await client.get(f"{base_url}/openapi.json")
            if openapi_response.status_code == 200:
                openapi_data = openapi_response.json()
                paths = openapi_data.get('paths', {})
                validate_path = paths.get('/api/v1/tasks/validate')
                if validate_path:
                    print("  ✅ /api/v1/tasks/validate 端点存在")
                    print(f"  支持的方法: {list(validate_path.keys())}")
                    
                    # 检查POST方法的请求体结构
                    post_method = validate_path.get('post', {})
                    request_body = post_method.get('requestBody', {})
                    if request_body:
                        content = request_body.get('content', {})
                        json_content = content.get('application/json', {})
                        schema = json_content.get('schema', {})
                        print(f"  请求体结构: {json.dumps(schema, indent=2)}")
                else:
                    print("  ❌ /api/v1/tasks/validate 端点不存在")
            
            # 测试3: 发送验证请求
            print("\n📋 测试3: 发送验证请求")
            
            # 准备测试数据
            test_request = {
                "name": "测试任务",
                "url_ids": ["test_url_1", "test_url_2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai"
                }
            }
            
            print(f"  发送请求数据:")
            print(json.dumps(test_request, indent=2, ensure_ascii=False))
            
            validate_response = await client.post(
                f"{base_url}/api/v1/tasks/validate",
                json=test_request
            )
            
            print(f"  响应状态码: {validate_response.status_code}")
            print(f"  响应头: {dict(validate_response.headers)}")
            
            if validate_response.status_code == 200:
                response_data = validate_response.json()
                print(f"  ✅ 验证成功")
                print(f"  响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            else:
                print(f"  ❌ 验证失败")
                try:
                    error_data = validate_response.json()
                    print(f"  错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  错误文本: {validate_response.text}")
            
            # 测试4: 测试不同的请求数据
            print("\n📋 测试4: 测试不同的请求数据")
            
            # 测试缺少必需字段的情况
            invalid_request = {
                "name": "测试任务"
                # 缺少url_ids和schedule
            }
            
            print(f"  发送无效请求数据:")
            print(json.dumps(invalid_request, indent=2, ensure_ascii=False))
            
            invalid_response = await client.post(
                f"{base_url}/api/v1/tasks/validate",
                json=invalid_request
            )
            
            print(f"  响应状态码: {invalid_response.status_code}")
            
            if invalid_response.status_code == 422:
                print(f"  ✅ 正确返回422验证错误")
                try:
                    error_data = invalid_response.json()
                    print(f"  验证错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  错误文本: {invalid_response.text}")
            else:
                print(f"  ⚠️ 意外的状态码: {invalid_response.status_code}")
            
            # 测试5: 测试前端实际发送的数据格式
            print("\n📋 测试5: 模拟前端请求")
            
            frontend_request = {
                "name": "前端测试任务",
                "url_ids": ["url_123", "url_456"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai",
                    "interval": None,
                    "days": None,
                    "max_runs": None
                }
            }
            
            print(f"  发送前端格式请求:")
            print(json.dumps(frontend_request, indent=2, ensure_ascii=False))
            
            frontend_response = await client.post(
                f"{base_url}/api/v1/tasks/validate",
                json=frontend_request
            )
            
            print(f"  响应状态码: {frontend_response.status_code}")
            
            if frontend_response.status_code == 200:
                response_data = frontend_response.json()
                print(f"  ✅ 前端格式验证成功")
                print(f"  响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            else:
                print(f"  ❌ 前端格式验证失败")
                try:
                    error_data = frontend_response.json()
                    print(f"  错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  错误文本: {frontend_response.text}")
            
            print("\n🎉 调试完成!")
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    await debug_validate_api()


if __name__ == "__main__":
    asyncio.run(main())
