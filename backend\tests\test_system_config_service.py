"""
测试系统配置服务
"""

import pytest
from unittest.mock import patch, MagicMock
from app.services.system_config_service import SystemConfigService, SystemCrawlerConfig


class TestSystemConfigService:
    """测试系统配置服务"""
    
    def test_get_crawler_config(self):
        """测试获取爬虫配置"""
        with patch('app.services.system_config_service.settings') as mock_settings:
            # 模拟设置
            mock_settings.CRAWLER_MAX_CONCURRENT_TASKS = 4
            mock_settings.CRAWLER_BATCH_TIMEOUT = 600
            mock_settings.CRAWLER_DEFAULT_CALLBACK_URL = "http://test.com/callback"
            mock_settings.CRAWLER_API_BASE_URL = "http://test.com/api"
            mock_settings.CRAWLER_API_TIMEOUT = 30.0
            mock_settings.CRAWLER_API_MAX_RETRIES = 3
            mock_settings.CRAWLER_API_RATE_LIMIT_REQUESTS = 100
            mock_settings.CRAWLER_API_RATE_LIMIT_PERIOD = 60
            
            config = SystemConfigService.get_crawler_config()
            
            assert isinstance(config, SystemCrawlerConfig)
            assert config.max_concurrent_tasks == 4
            assert config.batch_timeout == 600
            assert config.default_callback_url == "http://test.com/callback"
    
    def test_get_system_config_for_task(self):
        """测试为任务获取完整配置"""
        with patch('app.services.system_config_service.settings') as mock_settings:
            # 模拟设置
            mock_settings.CRAWLER_MAX_CONCURRENT_TASKS = 4
            mock_settings.CRAWLER_BATCH_TIMEOUT = 600
            mock_settings.CRAWLER_DEFAULT_CALLBACK_URL = "http://test.com/callback"
            mock_settings.CRAWLER_API_BASE_URL = "http://test.com/api"
            mock_settings.CRAWLER_API_TIMEOUT = 30.0
            mock_settings.CRAWLER_API_MAX_RETRIES = 3
            mock_settings.CRAWLER_API_RATE_LIMIT_REQUESTS = 100
            mock_settings.CRAWLER_API_RATE_LIMIT_PERIOD = 60
            
            # 用户配置
            user_config = {
                "priority": "high",
                "cache_enabled": True,
                "llm_query": "test query",
                "max_concurrent_tasks": 10,  # 用户设置的值应该被系统配置覆盖
                "callback_url": "http://user.com/callback"  # 用户设置的值
            }
            
            enhanced_config = SystemConfigService.get_system_config_for_task(user_config)
            
            # 验证系统配置覆盖了用户配置
            assert enhanced_config["max_concurrent_tasks"] == 4  # 系统配置
            assert enhanced_config["batch_timeout"] == 600  # 系统配置
            assert enhanced_config["callback_url"] == "http://user.com/callback"  # 用户配置保留
            
            # 验证用户配置保留
            assert enhanced_config["priority"] == "high"
            assert enhanced_config["cache_enabled"] is True
            assert enhanced_config["llm_query"] == "test query"
    
    def test_get_system_config_for_task_with_default_callback(self):
        """测试使用默认回调URL的情况"""
        with patch('app.services.system_config_service.settings') as mock_settings:
            # 模拟设置
            mock_settings.CRAWLER_MAX_CONCURRENT_TASKS = 2
            mock_settings.CRAWLER_BATCH_TIMEOUT = 300
            mock_settings.CRAWLER_DEFAULT_CALLBACK_URL = "http://default.com/callback"
            mock_settings.CRAWLER_API_BASE_URL = "http://test.com/api"
            mock_settings.CRAWLER_API_TIMEOUT = 30.0
            mock_settings.CRAWLER_API_MAX_RETRIES = 3
            mock_settings.CRAWLER_API_RATE_LIMIT_REQUESTS = 100
            mock_settings.CRAWLER_API_RATE_LIMIT_PERIOD = 60
            
            # 用户配置没有callback_url
            user_config = {
                "priority": "medium",
                "cache_enabled": False
            }
            
            enhanced_config = SystemConfigService.get_system_config_for_task(user_config)
            
            # 验证使用了默认回调URL
            assert enhanced_config["callback_url"] == "http://default.com/callback"
    
    def test_get_system_info(self):
        """测试获取系统信息"""
        with patch('app.services.system_config_service.settings') as mock_settings:
            # 模拟设置
            mock_settings.CRAWLER_MAX_CONCURRENT_TASKS = 2
            mock_settings.CRAWLER_BATCH_TIMEOUT = 300
            mock_settings.CRAWLER_DEFAULT_CALLBACK_URL = None
            mock_settings.CRAWLER_API_BASE_URL = "http://test.com/api"
            mock_settings.CRAWLER_API_TIMEOUT = 30.0
            mock_settings.CRAWLER_API_MAX_RETRIES = 3
            mock_settings.CRAWLER_API_RATE_LIMIT_REQUESTS = 100
            mock_settings.CRAWLER_API_RATE_LIMIT_PERIOD = 60
            mock_settings.ENVIRONMENT = "development"
            mock_settings.VERSION = "1.0.0"
            
            system_info = SystemConfigService.get_system_info()
            
            assert "crawler_system_config" in system_info
            assert "environment" in system_info
            assert "version" in system_info
            
            crawler_config = system_info["crawler_system_config"]
            assert crawler_config["max_concurrent_tasks"] == 2
            assert crawler_config["batch_timeout"] == 300
            assert crawler_config["default_callback_url"] is None
            assert system_info["environment"] == "development"
            assert system_info["version"] == "1.0.0"


if __name__ == "__main__":
    pytest.main([__file__])
