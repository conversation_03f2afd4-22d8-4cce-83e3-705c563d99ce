"""
爬虫规则数据模式定义
"""
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, validator

from .common import BaseSchema


class SelectorType(str, Enum):
    """选择器类型枚举"""
    CSS = "css"
    XPATH = "xpath"
    REGEX = "regex"
    JSON_PATH = "json_path"


class DataType(str, Enum):
    """数据类型枚举"""
    TEXT = "text"
    ATTRIBUTE = "attribute"
    HTML = "html"
    LINK = "link"
    IMAGE = "image"
    NUMBER = "number"
    DATE = "date"


class ScrapingRuleBase(BaseModel):
    """爬虫规则基础模式"""
    
    rule_name: str = Field(
        description="规则名称",
        max_length=200
    )
    description: Optional[str] = Field(
        None,
        description="规则描述",
        max_length=500
    )
    target_site: str = Field(
        description="目标站点",
        max_length=100
    )
    
    # 选择器配置
    selectors: Dict[str, Dict[str, Any]] = Field(
        description="选择器配置",
        examples=[{
            "title": {
                "type": "css",
                "selector": "h1.title",
                "data_type": "text",
                "required": True
            },
            "price": {
                "type": "css", 
                "selector": ".price-current",
                "data_type": "number",
                "required": True,
                "post_process": "extract_number"
            }
        }]
    )
    
    # 页面配置
    wait_for_element: Optional[str] = Field(
        None,
        description="等待元素出现的选择器",
        max_length=200
    )
    wait_timeout: int = Field(
        10,
        description="等待超时时间（秒）",
        ge=1,
        le=60
    )
    
    # 分页配置
    pagination_config: Optional[Dict[str, Any]] = Field(
        None,
        description="分页配置"
    )
    
    # 数据清理规则
    data_cleaning_rules: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="数据清理规则"
    )
    
    # 验证规则
    validation_rules: Optional[Dict[str, Any]] = Field(
        None,
        description="数据验证规则"
    )
    
    @validator('selectors')
    def validate_selectors(cls, v):
        """验证选择器配置"""
        if not v:
            raise ValueError("选择器配置不能为空")
        
        valid_types = [t.value for t in SelectorType]
        valid_data_types = [t.value for t in DataType]
        
        for field_name, config in v.items():
            if not isinstance(config, dict):
                raise ValueError(f"选择器配置 {field_name} 必须是字典类型")
            
            # 检查必需字段
            if 'type' not in config:
                raise ValueError(f"选择器 {field_name} 缺少 type 字段")
            if 'selector' not in config:
                raise ValueError(f"选择器 {field_name} 缺少 selector 字段")
            
            # 检查类型有效性
            if config['type'] not in valid_types:
                raise ValueError(f"选择器 {field_name} 的类型 {config['type']} 无效")
            
            if 'data_type' in config and config['data_type'] not in valid_data_types:
                raise ValueError(f"选择器 {field_name} 的数据类型 {config['data_type']} 无效")
        
        return v

    model_config = ConfigDict(from_attributes=True)


class ScrapingRuleCreate(ScrapingRuleBase):
    """创建爬虫规则的数据模式"""
    pass


class ScrapingRuleUpdate(BaseModel):
    """更新爬虫规则的数据模式"""
    
    rule_name: Optional[str] = Field(
        None,
        max_length=200
    )
    description: Optional[str] = Field(
        None,
        max_length=500
    )
    target_site: Optional[str] = Field(
        None,
        max_length=100
    )
    selectors: Optional[Dict[str, Dict[str, Any]]] = None
    wait_for_element: Optional[str] = Field(
        None,
        max_length=200
    )
    wait_timeout: Optional[int] = Field(
        None,
        ge=1,
        le=60
    )
    pagination_config: Optional[Dict[str, Any]] = None
    data_cleaning_rules: Optional[List[Dict[str, Any]]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class ScrapingRuleResponse(BaseSchema):
    """爬虫规则响应数据模式"""
    
    id: int = Field(description="规则ID")
    rule_name: str = Field(description="规则名称")
    description: Optional[str] = Field(description="规则描述")
    target_site: str = Field(description="目标站点")
    
    # 选择器配置
    selectors: Dict[str, Dict[str, Any]] = Field(description="选择器配置")
    
    # 页面配置
    wait_for_element: Optional[str] = Field(description="等待元素选择器")
    wait_timeout: int = Field(description="等待超时时间")
    
    # 分页配置
    pagination_config: Optional[Dict[str, Any]] = Field(description="分页配置")
    
    # 数据清理规则
    data_cleaning_rules: Optional[List[Dict[str, Any]]] = Field(description="数据清理规则")
    
    # 验证规则
    validation_rules: Optional[Dict[str, Any]] = Field(description="数据验证规则")
    
    # 状态信息
    is_active: bool = Field(description="是否激活")
    
    # 统计信息
    usage_count: int = Field(description="使用次数")
    success_rate: float = Field(description="成功率")

    model_config = ConfigDict(from_attributes=True)


class RuleTestRequest(BaseModel):
    """规则测试请求"""
    
    rule_id: int = Field(
        description="规则ID",
        gt=0
    )
    test_url: str = Field(
        description="测试URL",
        max_length=500
    )
    test_selectors: Optional[List[str]] = Field(
        None,
        description="要测试的选择器列表"
    )

    model_config = ConfigDict(from_attributes=True)


class RuleTestResponse(BaseModel):
    """规则测试响应"""
    
    success: bool = Field(description="测试是否成功")
    extracted_data: Dict[str, Any] = Field(description="提取的数据")
    errors: List[str] = Field(description="错误信息列表")
    warnings: List[str] = Field(description="警告信息列表")
    execution_time: float = Field(description="执行时间（秒）")
    
    # 详细信息
    selector_results: Dict[str, Dict[str, Any]] = Field(
        description="各个选择器的执行结果"
    )

    model_config = ConfigDict(from_attributes=True) 