#!/usr/bin/env python3
"""
测试爬虫配置重构
验证系统级配置分离是否正常工作
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any


class ConfigRefactorTester:
    """配置重构测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_system_config_api(self) -> Dict[str, Any]:
        """测试系统配置API"""
        print("🧪 测试系统配置API...")
        
        try:
            url = f"{self.base_url}/api/v1/crawl-configs/system-config"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    print("  ✅ 系统配置API调用成功")
                    print(f"  📊 系统配置数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return {"success": True, "data": data}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 系统配置API调用失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 系统配置API调用异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_config_creation_without_system_params(self) -> Dict[str, Any]:
        """测试创建配置时不包含系统级参数"""
        print("🧪 测试配置创建（不包含系统级参数）...")
        
        try:
            # 创建配置数据（不包含系统级参数）
            config_data = {
                "name": "测试配置_重构验证",
                "description": "验证配置重构后的创建流程",
                "platform": "mercadolibre",
                "llm_query": "提取商品信息",
                "llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}}}',
                "cache_enabled": True,
                "priority": "medium",
                "auth_method": "api_key",
                "api_key_prefix": "test",
                "user_agent": "MonIt-Test/1.0",
                "tags": ["测试", "重构验证"],
                "is_active": True
            }
            
            url = f"{self.base_url}/api/v1/crawl-configs"
            async with self.session.post(url, json=config_data) as response:
                if response.status == 200:
                    data = await response.json()
                    print("  ✅ 配置创建成功（无系统级参数）")
                    print(f"  📝 创建的配置ID: {data.get('data', {}).get('id', 'N/A')}")
                    return {"success": True, "data": data}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 配置创建失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 配置创建异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def verify_system_config_values(self, system_config_data: Dict[str, Any]) -> bool:
        """验证系统配置值是否正确"""
        print("🔍 验证系统配置值...")
        
        try:
            crawler_config = system_config_data["data"]["crawler_system_config"]
            
            # 验证关键配置值
            expected_values = {
                "max_concurrent_tasks": 2,
                "batch_timeout": 300,
                "default_callback_url": "http://backend:8000/api/v1/crawler/callback"
            }
            
            all_correct = True
            for key, expected_value in expected_values.items():
                actual_value = crawler_config.get(key)
                if actual_value == expected_value:
                    print(f"  ✅ {key}: {actual_value} (正确)")
                else:
                    print(f"  ❌ {key}: 期望 {expected_value}, 实际 {actual_value}")
                    all_correct = False
            
            return all_correct
            
        except Exception as e:
            print(f"  ❌ 验证系统配置值异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始爬虫配置重构验证测试...\n")
        
        results = {}
        
        # 1. 测试系统配置API
        system_config_result = await self.test_system_config_api()
        results["system_config_api"] = system_config_result
        
        if system_config_result["success"]:
            # 2. 验证系统配置值
            values_correct = await self.verify_system_config_values(system_config_result["data"])
            results["system_config_values"] = {"success": values_correct}
        
        # 3. 测试配置创建
        config_creation_result = await self.test_config_creation_without_system_params()
        results["config_creation"] = config_creation_result
        
        # 总结测试结果
        print("\n📊 测试结果总结:")
        success_count = sum(1 for result in results.values() if result.get("success"))
        total_count = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result.get("success") else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！配置重构成功！")
        else:
            print("⚠️  部分测试失败，需要检查配置重构实现")
        
        return results


async def main():
    """主函数"""
    async with ConfigRefactorTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
