# MonIt 电商爬虫系统依赖

# 核心框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
celery[redis]>=5.3.0
redis>=5.0.0

# 数据库
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0
psycopg2-binary>=2.9.0

# 时间序列数据库支持
timescaledb>=0.0.4

# 数据处理
pandas>=2.1.0
openpyxl>=3.1.0
numpy>=1.24.0

# HTTP客户端和爬虫
httpx>=0.25.0
aiohttp>=3.9.0
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# 异步支持
asyncio-mqtt>=0.16.0
aiofiles>=23.2.0

# 配置管理
pydantic>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0

# 日志和监控
structlog>=23.2.0
prometheus-client>=0.19.0

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.25.0

# 开发工具
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0

# 安全
cryptography>=41.0.0
bcrypt>=4.1.0

# 其他工具
click>=8.1.0
rich>=13.7.0
typer>=0.9.0

# Jupyter (可选，用于数据分析)
jupyter>=1.0.0
ipykernel>=6.26.0

# 图表和可视化 (可选)
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.17.0
