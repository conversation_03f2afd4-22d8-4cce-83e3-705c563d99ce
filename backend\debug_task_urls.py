#!/usr/bin/env python3
"""
调试脚本：检查任务URL关联关系
"""

import asyncio
import redis.asyncio as redis
import json
import sys


async def debug_task_urls(task_id: str):
    """调试任务URL关联关系"""
    redis_client = redis.from_url("redis://localhost:6379/0")
    
    try:
        print(f"=== 调试任务 {task_id} 的URL关联关系 ===\n")
        
        # 1. 检查任务是否存在
        task_key = f"monitoring_tasks:{task_id}"
        task_exists = await redis_client.exists(task_key)
        print(f"1. 任务存在检查:")
        print(f"   键名: {task_key}")
        print(f"   存在: {task_exists}")
        
        if task_exists:
            task_data = await redis_client.hgetall(task_key)
            print(f"   任务数据: {len(task_data)} 个字段")
            for key, value in task_data.items():
                key_str = key.decode() if isinstance(key, bytes) else key
                value_str = value.decode() if isinstance(value, bytes) else value
                if key_str in ['name', 'status', 'url_count']:
                    print(f"     {key_str}: {value_str}")
        print()
        
        # 2. 检查任务关联的URL
        task_urls_key = f"monitoring_tasks:task_urls:{task_id}"
        url_ids = await redis_client.smembers(task_urls_key)
        print(f"2. 任务关联的URL:")
        print(f"   键名: {task_urls_key}")
        print(f"   URL数量: {len(url_ids)}")
        
        if url_ids:
            print(f"   URL ID列表:")
            for i, url_id in enumerate(url_ids):
                url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
                print(f"     {i+1}. {url_id_str}")
        print()
        
        # 3. 检查每个URL的详细信息
        print(f"3. URL详细信息:")
        valid_urls = 0
        for url_id in url_ids:
            url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
            url_key = f"url_pool:items:{url_id_str}"
            url_data = await redis_client.hgetall(url_key)
            
            if url_data:
                valid_urls += 1
                url_dict = {}
                for key, value in url_data.items():
                    key_str = key.decode() if isinstance(key, bytes) else key
                    value_str = value.decode() if isinstance(value, bytes) else value
                    url_dict[key_str] = value_str
                
                print(f"   URL {valid_urls}:")
                print(f"     ID: {url_dict.get('id')}")
                print(f"     URL: {url_dict.get('url', '')[:80]}...")
                print(f"     平台: {url_dict.get('platform')}")
                print(f"     状态: {url_dict.get('status')}")
                print(f"     来源: {url_dict.get('source_file')}")
            else:
                print(f"   URL {url_id_str}: 数据不存在")
        
        print(f"\n   有效URL数量: {valid_urls}/{len(url_ids)}")
        print()
        
        # 4. 检查所有任务列表
        all_tasks_key = "monitoring_tasks:all_ids"
        all_task_ids = await redis_client.smembers(all_tasks_key)
        print(f"4. 所有任务列表:")
        print(f"   键名: {all_tasks_key}")
        print(f"   任务数量: {len(all_task_ids)}")
        
        if all_task_ids:
            print(f"   任务ID列表:")
            for i, tid in enumerate(all_task_ids):
                tid_str = tid.decode() if isinstance(tid, bytes) else tid
                print(f"     {i+1}. {tid_str}")
                if tid_str == task_id:
                    print(f"        ← 当前查询的任务")
        print()
        
        # 5. 检查URL池中的URL总数
        url_pool_all_key = "url_pool:all_ids"
        all_url_ids = await redis_client.smembers(url_pool_all_key)
        print(f"5. URL池统计:")
        print(f"   键名: {url_pool_all_key}")
        print(f"   总URL数量: {len(all_url_ids)}")
        
        # 检查前几个URL的详情
        if all_url_ids:
            print(f"   前3个URL示例:")
            for i, url_id in enumerate(list(all_url_ids)[:3]):
                url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
                url_key = f"url_pool:items:{url_id_str}"
                url_data = await redis_client.hgetall(url_key)
                
                if url_data:
                    url_dict = {}
                    for key, value in url_data.items():
                        key_str = key.decode() if isinstance(key, bytes) else key
                        value_str = value.decode() if isinstance(value, bytes) else value
                        url_dict[key_str] = value_str
                    
                    print(f"     {i+1}. {url_dict.get('id')}: {url_dict.get('url', '')[:50]}...")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await redis_client.close()


async def list_all_tasks():
    """列出所有任务"""
    redis_client = redis.from_url("redis://localhost:6379/0")
    
    try:
        print("=== 所有监控任务列表 ===\n")
        
        # 获取所有任务ID
        all_tasks_key = "monitoring_tasks:all_ids"
        all_task_ids = await redis_client.smembers(all_tasks_key)
        
        if not all_task_ids:
            print("没有找到任何任务")
            return
        
        print(f"找到 {len(all_task_ids)} 个任务:\n")
        
        for i, task_id in enumerate(all_task_ids):
            task_id_str = task_id.decode() if isinstance(task_id, bytes) else task_id
            task_key = f"monitoring_tasks:{task_id_str}"
            task_data = await redis_client.hgetall(task_key)
            
            if task_data:
                task_dict = {}
                for key, value in task_data.items():
                    key_str = key.decode() if isinstance(key, bytes) else key
                    value_str = value.decode() if isinstance(value, bytes) else value
                    task_dict[key_str] = value_str
                
                print(f"{i+1}. 任务ID: {task_id_str}")
                print(f"   名称: {task_dict.get('name', 'N/A')}")
                print(f"   状态: {task_dict.get('status', 'N/A')}")
                print(f"   URL数量: {task_dict.get('url_count', 'N/A')}")
                print(f"   创建时间: {task_dict.get('created_at', 'N/A')}")
                print()
    
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await redis_client.close()


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python debug_task_urls.py list                    # 列出所有任务")
        print("  python debug_task_urls.py <task_id>              # 调试指定任务")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        await list_all_tasks()
    else:
        task_id = command
        await debug_task_urls(task_id)


if __name__ == "__main__":
    asyncio.run(main())
