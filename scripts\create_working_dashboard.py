#!/usr/bin/env python3
"""
创建一个真正有内容的Grafana面板
"""

import requests
import json
import time

def create_working_dashboard():
    """创建有实际内容的监控面板"""
    
    grafana_url = "http://localhost:3001"
    auth = ("admin", "admin123")
    
    # 创建一个简单但有效的面板
    dashboard_data = {
        "dashboard": {
            "id": None,
            "title": "MonIt Working Dashboard",
            "tags": ["monit", "working"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "Service Status",
                    "type": "stat",
                    "targets": [
                        {
                            "expr": "up",
                            "refId": "A",
                            "legendFormat": "{{job}}"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "mappings": [
                                {
                                    "options": {
                                        "0": {
                                            "text": "DOWN",
                                            "color": "red"
                                        },
                                        "1": {
                                            "text": "UP",
                                            "color": "green"
                                        }
                                    },
                                    "type": "value"
                                }
                            ],
                            "thresholds": {
                                "steps": [
                                    {
                                        "color": "red",
                                        "value": 0
                                    },
                                    {
                                        "color": "green",
                                        "value": 1
                                    }
                                ]
                            }
                        }
                    },
                    "options": {
                        "colorMode": "background",
                        "graphMode": "none",
                        "justifyMode": "auto",
                        "orientation": "horizontal",
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"],
                            "fields": ""
                        },
                        "textMode": "auto"
                    },
                    "gridPos": {
                        "h": 4,
                        "w": 24,
                        "x": 0,
                        "y": 0
                    }
                },
                {
                    "id": 2,
                    "title": "CPU Usage",
                    "type": "timeseries",
                    "targets": [
                        {
                            "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
                            "refId": "A",
                            "legendFormat": "CPU Usage %"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "percent",
                            "min": 0,
                            "max": 100,
                            "thresholds": {
                                "steps": [
                                    {
                                        "color": "green",
                                        "value": 0
                                    },
                                    {
                                        "color": "yellow",
                                        "value": 70
                                    },
                                    {
                                        "color": "red",
                                        "value": 90
                                    }
                                ]
                            }
                        }
                    },
                    "options": {
                        "tooltip": {
                            "mode": "single",
                            "sort": "none"
                        },
                        "legend": {
                            "displayMode": "visible",
                            "placement": "bottom"
                        }
                    },
                    "gridPos": {
                        "h": 8,
                        "w": 12,
                        "x": 0,
                        "y": 4
                    }
                },
                {
                    "id": 3,
                    "title": "Memory Usage",
                    "type": "timeseries",
                    "targets": [
                        {
                            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
                            "refId": "A",
                            "legendFormat": "Memory Usage %"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "percent",
                            "min": 0,
                            "max": 100,
                            "thresholds": {
                                "steps": [
                                    {
                                        "color": "green",
                                        "value": 0
                                    },
                                    {
                                        "color": "yellow",
                                        "value": 80
                                    },
                                    {
                                        "color": "red",
                                        "value": 95
                                    }
                                ]
                            }
                        }
                    },
                    "gridPos": {
                        "h": 8,
                        "w": 12,
                        "x": 12,
                        "y": 4
                    }
                }
            ],
            "time": {
                "from": "now-1h",
                "to": "now"
            },
            "refresh": "30s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        print("📊 创建有内容的监控面板...")
        
        response = requests.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard_data,
            auth=auth,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 面板创建成功: {result['title']}")
            print(f"🔗 访问地址: {grafana_url}{result['url']}")
            return True
        else:
            print(f"❌ 面板创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("创建有实际内容的Grafana面板")
    print("=" * 50)
    
    print("📋 将创建包含以下内容的面板:")
    print("  - 服务状态 (UP/DOWN 显示)")
    print("  - CPU使用率图表")
    print("  - 内存使用率图表")
    print()
    
    if create_working_dashboard():
        print("\n🎉 有内容的监控面板创建成功！")
        print("\n📖 现在您可以:")
        print("  1. 访问 http://localhost:3001")
        print("  2. 查看 'MonIt Working Dashboard'")
        print("  3. 看到实际的监控数据")
    else:
        print("\n❌ 面板创建失败")

if __name__ == "__main__":
    main()
