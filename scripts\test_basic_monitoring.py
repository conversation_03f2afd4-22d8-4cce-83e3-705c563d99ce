#!/usr/bin/env python3
"""
基础监控系统测试脚本
"""

import requests
import time
import json

def test_prometheus():
    """测试Prometheus"""
    try:
        print("🔍 测试Prometheus...")
        response = requests.get("http://localhost:9090/api/v1/query?query=up", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                targets = len(data.get('data', {}).get('result', []))
                print(f"✅ Prometheus正常，监控目标数: {targets}")
                return True
        print(f"⚠️  Prometheus响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Prometheus连接失败: {e}")
        return False

def test_grafana():
    """测试Grafana"""
    try:
        print("🔍 测试Grafana...")
        response = requests.get("http://localhost:3001/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Grafana正常")
            return True
        else:
            # 尝试登录页面
            response = requests.get("http://localhost:3001/login", timeout=5)
            if response.status_code == 200:
                print("✅ Grafana正常 (登录页面)")
                return True
        print(f"⚠️  Grafana响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Grafana连接失败: {e}")
        return False

def test_node_exporter():
    """测试Node Exporter"""
    try:
        print("🔍 测试Node Exporter...")
        response = requests.get("http://localhost:9100/metrics", timeout=5)
        if response.status_code == 200:
            print("✅ Node Exporter正常")
            return True
        print(f"⚠️  Node Exporter响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ Node Exporter连接失败: {e}")
        return False

def main():
    print("🚀 开始基础监控系统测试...")
    print("=" * 40)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(5)
    
    results = []
    results.append(test_prometheus())
    results.append(test_grafana())
    results.append(test_node_exporter())
    
    print("\n" + "=" * 40)
    print("📊 测试总结")
    print("=" * 40)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"📈 成功: {success_count}/{total_count} 服务")
    
    if success_count == total_count:
        print("🎉 基础监控系统运行正常！")
        print("\n🔗 访问地址:")
        print("  - Grafana:    http://localhost:3001 (admin/admin123)")
        print("  - Prometheus: http://localhost:9090")
        print("  - Node Exporter: http://localhost:9100/metrics")
    else:
        print("⚠️  部分服务存在问题")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
