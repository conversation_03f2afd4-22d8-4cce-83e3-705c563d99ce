"""
任务调度管理器

核心任务调度系统，协调任务分片、限流控制和Celery任务执行
解决外部API限制与大量监控任务之间的矛盾
"""

import asyncio
import logging
import uuid
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from app.core.task_splitter import TaskSplitter, TaskBatch, TaskPriority, SplitterConfig
from app.core.rate_limiter import RateLimiter, RateLimitConfig
from app.core.retry_manager import RetryManager, RetryConfig
# 导入Celery任务
try:
    from app.tasks.crawl_batch import crawl_batch_task, crawl_single_url
except ImportError as e:
    logging.warning(f"Failed to import Celery tasks: {e}")
    crawl_batch_task = None
    crawl_single_url = None

# 注意：爬虫池服务已移除，保留变量用于向后兼容
crawler_pool_service = None
config_migration_service = None
from app.database import get_async_session
from app.models.task import CrawlTask
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class TaskManagerStatus(Enum):
    """任务管理器状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class TaskManagerConfig:
    """任务管理器配置"""
    max_concurrent_batches: int = 2        # 最大并发批次数（从系统配置获取）
    batch_check_interval: float = 5.0     # 批次检查间隔（秒）
    task_timeout: int = 1800               # 任务超时时间（秒，从系统配置获取）
    retry_failed_interval: int = 300       # 重试失败任务间隔（秒）
    cleanup_interval: int = 3600           # 清理间隔（秒）
    max_queue_size: int = 10000            # 最大队列大小

    # 子组件配置
    splitter_config: SplitterConfig = None
    rate_limiter_config: RateLimitConfig = None
    retry_config: RetryConfig = None

    @classmethod
    def from_system_settings(cls, settings):
        """从系统设置创建配置"""
        return cls(
            max_concurrent_batches=getattr(settings, 'CRAWLER_MAX_CONCURRENT_TASKS', 2),
            task_timeout=getattr(settings, 'CRAWLER_BATCH_TIMEOUT', 300)
        )


@dataclass
class BatchExecution:
    """批次执行状态"""
    batch_id: str
    celery_task_id: str
    batch: TaskBatch
    started_at: datetime
    status: str = "running"
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TaskManager:
    """任务调度管理器"""
    
    def __init__(self, config: Optional[TaskManagerConfig] = None):
        """初始化任务管理器
        
        Args:
            config: 任务管理器配置
        """
        self.config = config or TaskManagerConfig()
        if not self.config.splitter_config:
            self.config.splitter_config = SplitterConfig()
        if not self.config.rate_limiter_config:
            self.config.rate_limiter_config = RateLimitConfig()
        
        self.logger = logging.getLogger(__name__)
        
        # 状态管理
        self.status = TaskManagerStatus.STOPPED
        self.start_time: Optional[datetime] = None
        
        # 子组件
        self.task_splitter = TaskSplitter(self.config.splitter_config)
        self.rate_limiter = RateLimiter(self.config.rate_limiter_config)
        self.retry_manager = RetryManager(self.config.retry_config)
        
        # 任务队列和执行状态
        self.pending_batches: List[TaskBatch] = []
        self.running_batches: Dict[str, BatchExecution] = {}
        self.completed_batches: List[str] = []
        self.failed_batches: List[str] = []
        
        # 控制循环
        self.manager_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        # 统计信息
        self.stats = {
            "total_tasks_submitted": 0,
            "total_batches_created": 0,
            "total_batches_completed": 0,
            "total_batches_failed": 0,
            "total_urls_processed": 0,
            "avg_batch_processing_time": 0.0
        }
    
    async def start(self) -> None:
        """启动任务管理器"""
        if self.status != TaskManagerStatus.STOPPED:
            self.logger.warning("Task manager is already running or starting")
            return
        
        self.status = TaskManagerStatus.STARTING
        self.start_time = datetime.now()
        
        try:
            # 启动子组件
            await self.rate_limiter.start()

            # 从rate_limiter配置中获取Redis URL传递给retry_manager
            redis_url = self.config.rate_limiter_config.redis_url if self.config.rate_limiter_config else "redis://redis:6379/0"
            await self.retry_manager.start(redis_url)

            # 启动管理循环
            self.is_running = True
            self.manager_task = asyncio.create_task(self._manager_loop())
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            self.retry_task = asyncio.create_task(self._retry_loop())

            self.status = TaskManagerStatus.RUNNING
            self.logger.info("Task manager started successfully")
            
        except Exception as e:
            self.status = TaskManagerStatus.ERROR
            self.logger.error(f"Failed to start task manager: {e}")
            raise
    
    async def stop(self) -> None:
        """停止任务管理器"""
        if self.status == TaskManagerStatus.STOPPED:
            return
        
        self.status = TaskManagerStatus.STOPPING
        self.is_running = False
        
        try:
            # 取消管理循环
            if self.manager_task:
                self.manager_task.cancel()
                try:
                    await self.manager_task
                except asyncio.CancelledError:
                    pass
            
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass

            if hasattr(self, 'retry_task') and self.retry_task:
                self.retry_task.cancel()
                try:
                    await self.retry_task
                except asyncio.CancelledError:
                    pass

            # 等待运行中的批次完成（最多等待30秒）
            if self.running_batches:
                self.logger.info(f"Waiting for {len(self.running_batches)} running batches to complete...")
                await asyncio.sleep(30)

            # 停止子组件
            await self.rate_limiter.stop()
            await self.retry_manager.stop()
            
            self.status = TaskManagerStatus.STOPPED
            self.logger.info("Task manager stopped")
            
        except Exception as e:
            self.status = TaskManagerStatus.ERROR
            self.logger.error(f"Error stopping task manager: {e}")
            raise
    
    async def submit_task(
        self,
        task_id: str,  # 改为字符串类型，支持UUID
        urls: List[str],
        platform: str = "mercadolibre",
        priority: TaskPriority = TaskPriority.NORMAL,
        options: Optional[Dict[str, Any]] = None,
        pool_id: Optional[str] = None
    ) -> str:
        """提交任务

        Args:
            task_id: 任务ID（支持UUID字符串或数字字符串）
            urls: URL列表
            platform: 平台类型
            priority: 任务优先级
            options: 爬取选项
            pool_id: 爬虫池ID（可选，默认使用默认池）

        Returns:
            str: 任务提交ID
        """
        if self.status != TaskManagerStatus.RUNNING:
            raise RuntimeError("Task manager is not running")
        
        if len(urls) == 0:
            raise ValueError("URL list cannot be empty")
        
        if len(self.pending_batches) >= self.config.max_queue_size:
            raise RuntimeError("Task queue is full")
        
        submission_id = str(uuid.uuid4())

        try:
            # 选择爬虫后端
            selected_backend = None
            if crawler_pool_service:
                try:
                    if pool_id:
                        # 使用指定的爬虫池
                        selected_backend = await crawler_pool_service.select_backend(pool_id)
                    else:
                        # 使用默认爬虫池
                        default_pool = await config_migration_service.ensure_default_pool_exists()
                        if default_pool:
                            selected_backend = await crawler_pool_service.select_backend(default_pool.pool_id)
                except Exception as e:
                    self.logger.warning(f"Failed to select backend from pool: {e}")

            # 分片任务
            batches = self.task_splitter.split_urls(
                urls=urls,
                platform=platform,
                priority=priority,
                metadata={
                    "task_id": task_id,
                    "submission_id": submission_id,
                    "options": options or {},
                    "pool_id": pool_id,
                    "selected_backend": selected_backend.dict() if selected_backend else None
                }
            )
            
            # 添加到待处理队列
            self.pending_batches.extend(batches)
            
            # 按优先级排序
            self.pending_batches.sort(
                key=lambda b: self.task_splitter.calculate_priority_score(
                    b.priority, b.created_at, len(b.urls)
                ),
                reverse=True
            )
            
            # 更新统计
            self.stats["total_tasks_submitted"] += 1
            self.stats["total_batches_created"] += len(batches)
            
            self.logger.info(
                f"Task submitted: {len(urls)} URLs split into {len(batches)} batches "
                f"(submission_id: {submission_id})"
            )
            
            return submission_id
            
        except Exception as e:
            self.logger.error(f"Failed to submit task: {e}")
            raise
    
    async def submit_single_url(
        self,
        url: str,
        platform: str = "mercadolibre",
        priority: TaskPriority = TaskPriority.HIGH,
        task_id: Optional[str] = None,  # 改为字符串类型
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """提交单URL任务（高优先级）
        
        Args:
            url: 目标URL
            platform: 平台类型
            priority: 任务优先级
            task_id: 数据库任务ID
            options: 爬取选项
            
        Returns:
            str: Celery任务ID
        """
        if self.status != TaskManagerStatus.RUNNING:
            raise RuntimeError("Task manager is not running")
        
        try:
            # 检查Celery任务是否可用
            if crawl_single_url is None:
                raise RuntimeError("Celery crawl_single_url task is not available")

            # 直接提交到Celery（单URL任务不需要分片）
            celery_task = crawl_single_url.apply_async(
                args=[url, platform, task_id, options],
                queue="crawler_queue_high" if priority == TaskPriority.URGENT else "crawler_queue"
            )
            
            self.logger.info(f"Single URL task submitted: {url} (task_id: {celery_task.id})")
            return celery_task.id
            
        except Exception as e:
            self.logger.error(f"Failed to submit single URL task: {e}")
            raise
    
    async def _manager_loop(self) -> None:
        """管理器主循环"""
        while self.is_running:
            try:
                # 检查是否可以启动新批次
                if (len(self.running_batches) < self.config.max_concurrent_batches and 
                    self.pending_batches):
                    
                    batch = self.pending_batches.pop(0)
                    await self._start_batch(batch)
                
                # 检查运行中的批次状态
                await self._check_running_batches()
                
                # 短暂休眠
                await asyncio.sleep(self.config.batch_check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in manager loop: {e}")
                await asyncio.sleep(10)  # 错误时等待更长时间
    
    async def _start_batch(self, batch: TaskBatch) -> None:
        """启动批次执行
        
        Args:
            batch: 任务批次
        """
        try:
            task_id = batch.metadata.get("task_id")
            options = batch.metadata.get("options", {})
            pool_id = batch.metadata.get("pool_id")
            selected_backend = batch.metadata.get("selected_backend")

            # 检查Celery任务是否可用
            if crawl_batch_task is None:
                raise RuntimeError("Celery crawl_batch_task is not available")

            # 如果有选定的后端，将其信息添加到选项中
            if selected_backend:
                options["crawler_backend"] = selected_backend
                self.logger.info(f"Using selected backend: {selected_backend.get('name', 'unknown')} ({selected_backend.get('base_url', 'unknown')})")

            # 提交到Celery
            queue_name = (
                "crawler_queue_high" if batch.priority == TaskPriority.URGENT
                else "crawler_queue"
            )

            celery_task = crawl_batch_task.apply_async(
                args=[batch.batch_id, batch.urls, batch.platform, task_id, options],
                queue=queue_name
            )
            
            # 记录执行状态
            execution = BatchExecution(
                batch_id=batch.batch_id,
                celery_task_id=celery_task.id,
                batch=batch,
                started_at=datetime.now()
            )
            
            self.running_batches[batch.batch_id] = execution
            
            self.logger.info(
                f"Started batch {batch.batch_id} with {len(batch.urls)} URLs "
                f"(celery_task: {celery_task.id})"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to start batch {batch.batch_id}: {e}")
            self.failed_batches.append(batch.batch_id)
    
    async def _check_running_batches(self) -> None:
        """检查运行中的批次状态"""
        completed_batches = []
        
        for batch_id, execution in self.running_batches.items():
            try:
                # 检查Celery任务状态
                from celery.result import AsyncResult
                celery_result = AsyncResult(execution.celery_task_id)
                
                if celery_result.ready():
                    # 任务完成
                    if celery_result.successful():
                        execution.status = "completed"
                        execution.result = celery_result.result
                        self.completed_batches.append(batch_id)
                        self.stats["total_batches_completed"] += 1
                        
                        # 更新统计
                        if execution.result:
                            self.stats["total_urls_processed"] += execution.result.get("successful_urls", 0)
                        
                        self.logger.info(f"Batch {batch_id} completed successfully")
                    else:
                        execution.status = "failed"
                        execution.error = str(celery_result.info)

                        # 处理失败任务 - 尝试重试
                        will_retry = await self.retry_manager.handle_batch_failure(
                            batch_id=batch_id,
                            task_id=execution.batch.metadata.get("task_id"),
                            urls=execution.batch.urls,
                            error=Exception(execution.error),
                            priority=execution.batch.priority
                        )

                        if not will_retry:
                            self.failed_batches.append(batch_id)
                            self.stats["total_batches_failed"] += 1
                            self.logger.error(f"Batch {batch_id} failed permanently: {execution.error}")
                        else:
                            self.logger.warning(f"Batch {batch_id} failed, scheduled for retry: {execution.error}")

                    completed_batches.append(batch_id)
                
                else:
                    # 检查超时
                    runtime = (datetime.now() - execution.started_at).total_seconds()
                    if runtime > self.config.task_timeout:
                        # 任务超时，撤销
                        celery_result.revoke(terminate=True)
                        execution.status = "timeout"
                        execution.error = f"Task timeout after {runtime:.1f} seconds"
                        self.failed_batches.append(batch_id)
                        self.stats["total_batches_failed"] += 1
                        
                        self.logger.warning(f"Batch {batch_id} timed out after {runtime:.1f}s")
                        completed_batches.append(batch_id)
                
            except Exception as e:
                self.logger.error(f"Error checking batch {batch_id}: {e}")
        
        # 清理已完成的批次
        for batch_id in completed_batches:
            del self.running_batches[batch_id]
    
    async def _retry_loop(self) -> None:
        """重试循环"""
        while self.is_running:
            try:
                await asyncio.sleep(self.config.retry_failed_interval)

                # 获取准备重试的任务
                ready_retries = await self.retry_manager.get_ready_retries()

                for failed_task in ready_retries:
                    # 重新创建批次
                    retry_batch = TaskBatch(
                        batch_id=f"{failed_task.batch_id}_retry_{failed_task.retry_count}",
                        urls=failed_task.urls,
                        priority=failed_task.priority,
                        platform="mercadolibre",  # 默认平台
                        estimated_duration=len(failed_task.urls) * 2.0,
                        created_at=datetime.now(),
                        metadata={
                            "task_id": failed_task.task_id,
                            "original_batch_id": failed_task.batch_id,
                            "retry_count": failed_task.retry_count,
                            "is_retry": True
                        }
                    )

                    # 添加到待处理队列（队列尾端）
                    self.pending_batches.append(retry_batch)

                    self.logger.info(
                        f"Added retry batch {retry_batch.batch_id} to queue "
                        f"(attempt {failed_task.retry_count})"
                    )

            except Exception as e:
                self.logger.error(f"Error in retry loop: {e}")

    async def _cleanup_loop(self) -> None:
        """清理循环"""
        while self.is_running:
            try:
                await asyncio.sleep(self.config.cleanup_interval)

                # 清理旧的完成记录
                cutoff_time = datetime.now() - timedelta(hours=24)

                # 清理已完成的批次记录
                if len(self.completed_batches) > 1000:
                    self.completed_batches = self.completed_batches[-500:]

                # 清理失败的批次记录
                if len(self.failed_batches) > 1000:
                    self.failed_batches = self.failed_batches[-500:]

                self.logger.debug("Cleanup cycle completed")

            except Exception as e:
                self.logger.error(f"Error in cleanup loop: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        uptime = 0.0
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "status": self.status.value,
            "uptime": uptime,
            "pending_batches": len(self.pending_batches),
            "running_batches": len(self.running_batches),
            "completed_batches": len(self.completed_batches),
            "failed_batches": len(self.failed_batches),
            "stats": self.stats,
            "rate_limiter": self.rate_limiter.get_metrics(),
            "task_splitter": self.task_splitter.get_stats(),
            "retry_manager": self.retry_manager.get_stats()
        }
    
    async def get_task_status(self, task_id: int) -> Dict[str, Any]:
        """获取特定任务的状态
        
        Args:
            task_id: 数据库任务ID
            
        Returns:
            Dict[str, Any]: 任务状态
        """
        try:
            async with get_async_session() as session:
                result = await session.execute(
                    select(CrawlTask).where(CrawlTask.id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if not task:
                    return {"error": "Task not found"}
                
                return {
                    "id": task.id,
                    "name": task.name,
                    "status": task.status,
                    "progress": task.progress,
                    "total_products": task.total_products,
                    "success_count": task.success_count,
                    "failed_count": task.failed_count,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get task status: {e}")
            return {"error": str(e)}


# 全局任务管理器实例
_task_manager = None


async def get_task_manager() -> TaskManager:
    """获取任务管理器实例"""
    global _task_manager

    if _task_manager is None:
        _task_manager = TaskManager()
        await _task_manager.start()

    return _task_manager


async def cleanup_task_manager():
    """清理任务管理器"""
    global _task_manager

    if _task_manager:
        await _task_manager.stop()
        _task_manager = None
