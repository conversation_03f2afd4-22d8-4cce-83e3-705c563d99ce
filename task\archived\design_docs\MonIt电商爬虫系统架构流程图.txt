graph TD
    %% 前端用户界面层
    A1[用户浏览器] --> A2[React前端应用<br/>Port: 3000]
    A2 --> A3[Excel文件上传组件<br/>TaskManager页面]
    A2 --> A4[任务列表管理<br/>Dashboard页面]
    A2 --> A5[任务详情查看<br/>TaskDetail页面]
    A2 --> A6[数据分析界面<br/>DataAnalysis页面]

    %% 前端状态管理
    A3 --> B1[Redux Store<br/>状态管理]
    A4 --> B1
    A5 --> B1
    A6 --> B1
    B1 --> B2[TasksSlice<br/>任务状态]
    B1 --> B3[UISlice<br/>界面状态]
    B1 --> B4[SystemSlice<br/>系统状态]
    B1 --> B5[DataSlice<br/>数据状态]

    %% 前后端通信
    A3 --> C1[HTTP API调用<br/>Axios客户端]
    A4 --> C1
    A5 --> C1
    A6 --> C1
    A2 --> C2[WebSocket连接<br/>实时通信]

    %% API层
    C1 --> D1[FastAPI Backend<br/>Port: 8000]
    C2 --> D1
    D1 --> D2[Excel上传API<br/>/api/v1/tasks/upload-excel]
    D1 --> D3[任务提交API<br/>/api/v1/tasks/submit]
    D1 --> D4[任务查询API<br/>/api/v1/tasks/]
    D1 --> D5[WebSocket端点<br/>/ws]

    %% 数据处理层
    D2 --> E1[ExcelProcessor<br/>解析Excel数据]
    E1 --> E2[URL验证器<br/>平台识别]
    E2 --> E3[创建数据库任务<br/>CrawlTask]
    D3 --> E3

    %% 任务调度层 (TaskManager)
    E3 --> F1[TaskManager<br/>任务调度系统]
    F1 --> F2[TaskSplitter<br/>智能分片]
    F2 --> F3[RateLimiter<br/>限流控制]
    F3 --> F4[优先级排序<br/>pending_batches]

    %% 决策点
    F4 --> G1{并发数量检查<br/>< 2个批次?}
    G1 -->|是| G2[启动新批次]
    G1 -->|否| G3[等待队列]
    G3 --> G1

    %% Celery任务提交
    G2 --> H1[提交到Celery<br/>crawl_batch_task]
    H1 --> H2[Redis队列<br/>crawler_queue]

    %% Celery执行层
    H2 --> I1[Celery Worker<br/>任务执行]
    I1 --> I2[CrawlerAPIClient<br/>调用外部API]
    I2 --> I3[外部爬虫服务<br/>Port: 8080]

    %% 结果处理
    I3 --> J1[爬取结果]
    J1 --> J2[数据存储<br/>TimescaleDB]
    J1 --> J3[任务状态更新<br/>Redis + DB]

    %% 实时状态推送
    J3 --> K1[WebSocket广播<br/>状态更新]
    K1 --> C2

    %% 失败处理
    J3 --> L1{任务成功?}
    L1 -->|是| L2[完成统计]
    L1 -->|否| L3[RetryManager<br/>重试管理]
    L3 --> L4{重试次数<br/>< 最大值?}
    L4 -->|是| L5[重新排队<br/>降低优先级]
    L4 -->|否| L6[死信队列<br/>任务放弃]
    L5 --> F4

    %% 监控和维护
    M1[Celery Beat<br/>定时任务] --> M2[系统监控<br/>清理任务]
    M2 --> H2

    %% 数据查询回路
    D4 --> J2
    J2 --> N1[数据查询结果]
    N1 --> C1

    %% 样式定义
    classDef frontendLayer fill:#e3f2fd
    classDef stateLayer fill:#f1f8e9
    classDef communicationLayer fill:#fff8e1
    classDef apiLayer fill:#f3e5f5
    classDef processingLayer fill:#e8f5e8
    classDef taskManagerLayer fill:#e8f5e8
    classDef celeryLayer fill:#fff3e0
    classDef externalLayer fill:#ffebee
    classDef storageLayer fill:#f1f8e9
    classDef realtimeLayer fill:#fce4ec

    class A1,A2,A3,A4,A5,A6 frontendLayer
    class B1,B2,B3,B4,B5 stateLayer
    class C1,C2 communicationLayer
    class D1,D2,D3,D4,D5 apiLayer
    class E1,E2,E3 processingLayer
    class F1,F2,F3,F4,G1,G2,L3,L4,L5,L6 taskManagerLayer
    class H1,H2,I1,M1,M2 celeryLayer
    class I2,I3 externalLayer
    class J2,J3,L2,N1 storageLayer
    class K1 realtimeLayer