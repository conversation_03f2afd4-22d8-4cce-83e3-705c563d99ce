"""
爬虫实例配置管理服务
"""

import json
import uuid
import asyncio
import logging
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path

import httpx
from fastapi import HTTPException

from ..schemas.crawler_instance_config import (
    CrawlerInstanceConfig, CrawlerInstanceConfigCreate, CrawlerInstanceConfigUpdate,
    CrawlerConfigManager, ConnectionTestResult, InstanceStatus, AuthType
)
from ..api.v1.crawler_config import get_redis_client


logger = logging.getLogger(__name__)


class CrawlerInstanceConfigService:
    """爬虫实例配置管理服务"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        
        # Redis键名模板
        self.keys = {
            'config_manager': 'crawler_instance:manager',
            'config_detail': 'crawler_instance:config:{}',
            'config_list': 'crawler_instance:list',
        }
        
        # 创建数据目录
        self.data_dir = Path("data/crawler_instances")
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    async def get_config_manager(self) -> CrawlerConfigManager:
        """获取配置管理器"""
        try:
            # 从Redis获取
            manager_data = self.redis_client.get(self.keys['config_manager'])
            if manager_data:
                manager_dict = json.loads(manager_data)
                return CrawlerConfigManager(**manager_dict)
            
            # 从文件获取
            manager_file = self.data_dir / "config_manager.json"
            if manager_file.exists():
                with open(manager_file, 'r', encoding='utf-8') as f:
                    manager_dict = json.load(f)
                    manager = CrawlerConfigManager(**manager_dict)
                    # 同步到Redis
                    await self._save_config_manager(manager)
                    return manager
            
            # 创建默认管理器
            return await self._create_default_manager()
            
        except Exception as e:
            logger.error(f"Failed to get config manager: {e}")
            return await self._create_default_manager()
    
    async def create_config(self, config_data: CrawlerInstanceConfigCreate) -> CrawlerInstanceConfig:
        """创建爬虫实例配置"""
        try:
            config_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 创建配置实例
            config = CrawlerInstanceConfig(
                config_id=config_id,
                config_name=config_data.config_name,
                description=config_data.description,
                api_endpoint=config_data.api_endpoint,
                timeout=config_data.timeout,
                max_retries=config_data.max_retries,
                auth_config=config_data.auth_config,
                browser=config_data.browser,
                crawler=config_data.crawler,
                llm=config_data.llm,
                schema_extraction=config_data.schema_extraction,
                content_processing=config_data.content_processing,
                link_filtering=config_data.link_filtering,
                scheduler=config_data.scheduler,
                monitor=config_data.monitor,
                max_concurrent=config_data.max_concurrent,
                weight=config_data.weight,
                priority=config_data.priority,
                created_at=now,
                updated_at=now
            )
            
            # 获取配置管理器
            manager = await self.get_config_manager()
            
            # 添加到管理器
            manager.configs.append(config)
            
            # 如果是第一个配置，设为默认
            if len(manager.configs) == 1:
                manager.default_config_id = config_id
            
            # 保存管理器
            await self._save_config_manager(manager)
            
            # 保存单个配置
            await self._save_config(config)
            
            logger.info(f"Created crawler instance config: {config_id} ({config_data.config_name})")
            return config
            
        except Exception as e:
            logger.error(f"Failed to create crawler instance config: {e}")
            raise HTTPException(status_code=500, detail=f"创建爬虫配置失败: {str(e)}")
    
    async def get_config(self, config_id: str) -> Optional[CrawlerInstanceConfig]:
        """获取单个爬虫实例配置"""
        try:
            # 从Redis获取
            config_data = self.redis_client.get(self.keys['config_detail'].format(config_id))
            if config_data:
                config_dict = json.loads(config_data)
                return CrawlerInstanceConfig(**config_dict)
            
            # 从文件获取
            config_file = self.data_dir / f"{config_id}.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    config = CrawlerInstanceConfig(**config_dict)
                    # 同步到Redis
                    await self._save_config(config)
                    return config
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get crawler instance config {config_id}: {e}")
            return None
    
    async def get_all_configs(self) -> List[CrawlerInstanceConfig]:
        """获取所有爬虫实例配置"""
        try:
            manager = await self.get_config_manager()
            return manager.configs
        except Exception as e:
            logger.error(f"Failed to get all crawler instance configs: {e}")
            return []
    
    async def update_config(self, config_id: str, update_data: CrawlerInstanceConfigUpdate) -> Optional[CrawlerInstanceConfig]:
        """更新爬虫实例配置"""
        try:
            config = await self.get_config(config_id)
            if not config:
                return None
            
            # 更新配置
            update_dict = update_data.dict(exclude_unset=True)
            for key, value in update_dict.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            config.updated_at = datetime.now()
            
            # 更新管理器中的配置
            manager = await self.get_config_manager()
            for i, cfg in enumerate(manager.configs):
                if cfg.config_id == config_id:
                    manager.configs[i] = config
                    break
            
            # 保存更新
            await self._save_config_manager(manager)
            await self._save_config(config)
            
            logger.info(f"Updated crawler instance config: {config_id}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to update crawler instance config {config_id}: {e}")
            raise HTTPException(status_code=500, detail=f"更新爬虫配置失败: {str(e)}")
    
    async def delete_config(self, config_id: str) -> bool:
        """删除爬虫实例配置"""
        try:
            manager = await self.get_config_manager()
            
            # 检查是否为默认配置
            if manager.default_config_id == config_id:
                # 如果删除的是默认配置，需要重新设置默认配置
                remaining_configs = [cfg for cfg in manager.configs if cfg.config_id != config_id]
                if remaining_configs:
                    manager.default_config_id = remaining_configs[0].config_id
                else:
                    manager.default_config_id = None
            
            # 从管理器中移除
            manager.configs = [cfg for cfg in manager.configs if cfg.config_id != config_id]
            
            # 保存管理器
            await self._save_config_manager(manager)
            
            # 删除Redis中的配置
            self.redis_client.delete(self.keys['config_detail'].format(config_id))
            
            # 删除文件
            config_file = self.data_dir / f"{config_id}.json"
            if config_file.exists():
                config_file.unlink()
            
            logger.info(f"Deleted crawler instance config: {config_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete crawler instance config {config_id}: {e}")
            return False
    
    async def set_default_config(self, config_id: str) -> bool:
        """设置默认配置"""
        try:
            manager = await self.get_config_manager()
            
            # 检查配置是否存在
            config_exists = any(cfg.config_id == config_id for cfg in manager.configs)
            if not config_exists:
                return False
            
            manager.default_config_id = config_id
            await self._save_config_manager(manager)
            
            logger.info(f"Set default crawler instance config: {config_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set default config {config_id}: {e}")
            return False
    
    async def test_connection(self, config_id: str) -> ConnectionTestResult:
        """测试爬虫实例连接"""
        config = await self.get_config(config_id)
        if not config:
            return ConnectionTestResult(
                config_id=config_id,
                is_connected=False,
                response_time=0.0,
                error_message="配置不存在",
                test_time=datetime.now()
            )
        
        start_time = time.time()
        test_time = datetime.now()
        
        try:
            # 构建健康检查URL
            health_url = f"{config.api_endpoint.rstrip('/')}/health"
            
            # 准备请求头
            headers = {"User-Agent": "MonIt-ConfigTest/1.0"}
            if config.auth_config.auth_type == AuthType.API_KEY and config.auth_config.api_key:
                headers["X-API-Key"] = config.auth_config.api_key
            elif config.auth_config.auth_type == AuthType.BEARER_TOKEN and config.auth_config.api_key:
                headers["Authorization"] = f"Bearer {config.auth_config.api_key}"
            
            # 发送测试请求
            timeout = config.timeout / 1000.0  # 转换为秒
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(health_url, headers=headers)
                
                response_time = (time.time() - start_time) * 1000  # 转换为毫秒
                
                if response.status_code == 200:
                    # 尝试解析响应
                    try:
                        data = response.json()
                        return ConnectionTestResult(
                            config_id=config_id,
                            is_connected=True,
                            response_time=response_time,
                            error_message=None,
                            test_time=test_time,
                            api_version=data.get("version"),
                            server_info=data
                        )
                    except:
                        return ConnectionTestResult(
                            config_id=config_id,
                            is_connected=True,
                            response_time=response_time,
                            error_message=None,
                            test_time=test_time
                        )
                else:
                    return ConnectionTestResult(
                        config_id=config_id,
                        is_connected=False,
                        response_time=response_time,
                        error_message=f"HTTP {response.status_code}: {response.text[:100]}",
                        test_time=test_time
                    )
                    
        except asyncio.TimeoutError:
            response_time = timeout * 1000
            return ConnectionTestResult(
                config_id=config_id,
                is_connected=False,
                response_time=response_time,
                error_message="连接超时",
                test_time=test_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ConnectionTestResult(
                config_id=config_id,
                is_connected=False,
                response_time=response_time,
                error_message=f"连接错误: {str(e)}",
                test_time=test_time
            )
    
    async def _save_config_manager(self, manager: CrawlerConfigManager):
        """保存配置管理器"""
        manager_dict = manager.dict()
        
        # 保存到Redis
        self.redis_client.set(
            self.keys['config_manager'],
            json.dumps(manager_dict, default=str, ensure_ascii=False),
            ex=86400 * 30  # 30天过期
        )
        
        # 保存到文件
        manager_file = self.data_dir / "config_manager.json"
        with open(manager_file, 'w', encoding='utf-8') as f:
            json.dump(manager_dict, f, default=str, ensure_ascii=False, indent=2)
    
    async def _save_config(self, config: CrawlerInstanceConfig):
        """保存单个配置"""
        config_dict = config.dict()
        
        # 保存到Redis
        self.redis_client.set(
            self.keys['config_detail'].format(config.config_id),
            json.dumps(config_dict, default=str, ensure_ascii=False),
            ex=86400 * 30  # 30天过期
        )
        
        # 保存到文件
        config_file = self.data_dir / f"{config.config_id}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, default=str, ensure_ascii=False, indent=2)
    
    async def _create_default_manager(self) -> CrawlerConfigManager:
        """创建默认配置管理器"""
        try:
            # 尝试从现有的单一配置创建默认配置
            from ..api.v1.crawler_config import config_service
            from ..schemas.crawler_instance_config import AuthConfig, AuthType
            
            old_config = await config_service.get_config()
            
            # 创建默认配置
            default_config = CrawlerInstanceConfig(
                config_id="default_config",
                config_name="默认爬虫配置",
                description="从单一配置迁移的默认爬虫配置",
                api_endpoint=old_config.api.base_url,
                timeout=old_config.api.timeout,
                max_retries=old_config.api.max_retries,
                auth_config=AuthConfig(auth_type=AuthType.NONE),
                browser=old_config.browser,
                crawler=old_config.crawler,
                llm=old_config.llm,
                schema_extraction=old_config.schema_extraction,
                content_processing=old_config.content_processing,
                link_filtering=old_config.link_filtering,
                scheduler=old_config.scheduler,
                monitor=old_config.monitor,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            manager = CrawlerConfigManager(
                configs=[default_config],
                default_config_id="default_config"
            )
            
            await self._save_config_manager(manager)
            await self._save_config(default_config)
            
            logger.info("Created default crawler instance config manager")
            return manager
            
        except Exception as e:
            logger.error(f"Failed to create default manager: {e}")
            # 返回空管理器
            return CrawlerConfigManager(configs=[], default_config_id=None)


# 全局服务实例
crawler_instance_config_service = CrawlerInstanceConfigService()
