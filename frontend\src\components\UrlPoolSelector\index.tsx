import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Alert,
  Statistic,
  Row,
  Col,
  Card,
  message,
  Typography,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  SelectOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

import { getUrlPool, getPlatformDisplayName, getStatusDisplay, getAllFilteredUrlIds } from '../../services/urlPoolApi';
import type { UrlPoolItem, UrlPoolQuery } from '../../types/urlPool';

const { Text } = Typography;
const { Option } = Select;

interface UrlPoolSelectorProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (selectedUrls: string[]) => void;
  excludeUrls?: string[]; // 已经在任务中的URL，需要排除
}

const UrlPoolSelector: React.FC<UrlPoolSelectorProps> = ({
  visible,
  onCancel,
  onConfirm,
  excludeUrls = []
}) => {
  const [loading, setLoading] = useState(false);
  const [urls, setUrls] = useState<UrlPoolItem[]>([]);
  const [total, setTotal] = useState(0);
  const [selectedUrls, setSelectedUrls] = useState<string[]>([]);
  const [sourceFiles, setSourceFiles] = useState<string[]>([]);
  const [query, setQuery] = useState<UrlPoolQuery>({
    page: 1,
    page_size: 20,
    sort_by: 'added_at',
    sort_order: 'desc',
    status: 'active' // 默认只显示活跃的URL
  });

  // 加载URL列表
  const loadUrls = useCallback(async () => {
    if (!visible) return;

    setLoading(true);
    try {
      // 构建查询参数，包含排除的URL列表
      const queryWithExcludes = {
        ...query,
        exclude_urls: excludeUrls.length > 0 ? excludeUrls : undefined
      };

      const response = await getUrlPool(queryWithExcludes);

      // 直接使用API返回的数据，无需前端过滤
      setUrls(response.data);
      setTotal(response.total);

      // 提取来源文件列表（去重）
      const sourceFileSet = new Set(response.data.map(item => item.source_file).filter(Boolean));
      const files = Array.from(sourceFileSet);
      setSourceFiles(files);

      console.log(`URL池加载完成: 获取${response.data.length}个URL，总计${response.total}个`);
    } catch (error: any) {
      message.error(`加载URL失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [query, visible, excludeUrls]);

  useEffect(() => {
    loadUrls();
  }, [loadUrls]);

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSelectedUrls([]);
      setQuery(prev => ({ ...prev, page: 1 }));
    }
  }, [visible]);

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: selectedUrls,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedUrls(selectedRowKeys as string[]);
    },
    preserveSelectedRowKeys: true,
    getCheckboxProps: (record: UrlPoolItem) => ({
      name: record.id,
    }),
  };

  // 表格列定义
  const columns: ColumnsType<UrlPoolItem> = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 400,
      ellipsis: {
        showTitle: false
      },
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code style={{ fontSize: 12 }}>
            {url.length > 60 ? `${url.substring(0, 60)}...` : url}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 120,
      render: (platform: string) => (
        <Tag color={platform === 'mercadolibre' ? 'gold' : platform === 'amazon' ? 'orange' : 'blue'}>
          {getPlatformDisplayName(platform)}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const { name, color } = getStatusDisplay(status);
        return <Tag color={color}>{name}</Tag>;
      }
    },
    {
      title: '来源文件',
      dataIndex: 'source_file',
      key: 'source_file',
      width: 150,
      ellipsis: true
    },
    {
      title: '添加时间',
      dataIndex: 'added_at',
      key: 'added_at',
      width: 120,
      render: (time: string) => new Date(time).toLocaleDateString()
    }
  ];

  // 更新查询参数
  const updateQuery = (updates: Partial<UrlPoolQuery>) => {
    setQuery(prev => ({ ...prev, ...updates, page: 1 }));
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    updateQuery({ search: value || undefined });
  };

  // 平台筛选
  const handlePlatformFilter = (value: string) => {
    updateQuery({ platform: value === 'all' ? undefined : value });
  };

  // 状态筛选
  const handleStatusFilter = (value: string) => {
    updateQuery({ status: value === 'all' ? undefined : value as any });
  };

  // 来源文件筛选
  const handleSourceFileFilter = (value: string) => {
    updateQuery({ source_file: value === 'all' ? undefined : value });
  };

  // 表格变化处理
  const handleTableChange = (pagination: any) => {
    setQuery(prev => ({
      ...prev,
      page: pagination.current,
      page_size: pagination.pageSize
    }));
  };

  // 选择所有符合筛选条件的URL
  const handleSelectAllFiltered = async () => {
    try {
      const response = await getAllFilteredUrlIds({
        platform: query.platform,
        status: query.status,
        source_file: query.source_file,
        search: query.search,
        sort_by: query.sort_by,
        sort_order: query.sort_order
      });

      if (response.success) {
        // 过滤掉已经在任务中的URL
        const filteredIds = response.data.filter((id: string) => !excludeUrls.includes(id));
        setSelectedUrls(filteredIds);
        message.success(`已选择 ${filteredIds.length} 个可用URL`);
      } else {
        message.error('获取筛选结果失败');
      }
    } catch (error: any) {
      message.error(`选择失败: ${error.message}`);
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (selectedUrls.length === 0) {
      message.warning('请至少选择一个URL');
      return;
    }
    onConfirm(selectedUrls);
  };

  return (
    <Modal
      title="从URL池添加URL"
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={handleConfirm} disabled={selectedUrls.length === 0}>
          确认添加 ({selectedUrls.length})
        </Button>
      ]}
    >
      {/* 选择统计 */}
      {selectedUrls.length > 0 && (
        <Alert
          message={
            <Statistic
              title="已选择URL"
              value={selectedUrls.length}
              prefix={<CheckCircleOutlined />}
            />
          }
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 筛选工具栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Input.Search
              placeholder="搜索URL..."
              onSearch={handleSearch}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择平台"
              style={{ width: '100%' }}
              onChange={handlePlatformFilter}
              defaultValue="all"
            >
              <Option value="all">所有平台</Option>
              <Option value="mercadolibre">MercadoLibre</Option>
              <Option value="amazon">Amazon</Option>
              <Option value="ebay">eBay</Option>
              <Option value="aliexpress">AliExpress</Option>
              <Option value="shopee">Shopee</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
              defaultValue="active"
            >
              <Option value="all">所有状态</Option>
              <Option value="active">活跃</Option>
              <Option value="disabled">禁用</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="来源文件"
              style={{ width: '100%' }}
              onChange={handleSourceFileFilter}
              defaultValue="all"
            >
              <Option value="all">所有文件</Option>
              {sourceFiles.map(file => (
                <Option key={file} value={file}>{file}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={loadUrls}>
                刷新
              </Button>
              <Button icon={<SelectOutlined />} onClick={handleSelectAllFiltered}>
                选择全部筛选结果
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* URL列表表格 */}
      <Table
        columns={columns}
        dataSource={urls}
        rowKey="id"
        rowSelection={rowSelection}
        loading={loading}
        pagination={{
          current: query.page,
          pageSize: query.page_size,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
        size="small"
      />
    </Modal>
  );
};

export default UrlPoolSelector;
