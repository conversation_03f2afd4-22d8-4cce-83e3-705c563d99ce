#!/usr/bin/env python3
"""
测试调度配置的保存和读取

检查每日执行时间、结束时间的保存和识别问题
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_schedule_config():
    """测试调度配置的保存和读取"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试调度配置的保存和读取")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/schedule-test",
            "platform": "mercadolibre",
            "title": "调度测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'schedule_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'schedule_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:schedule_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 测试不同的调度配置
        print(f"\n📋 步骤2: 测试不同的调度配置")
        
        test_cases = [
            {
                "name": "每日执行-基本配置",
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:30",  # 每日执行时间
                    "timezone": "Asia/Shanghai"
                }
            },
            {
                "name": "每日执行-带结束时间",
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "10:15",  # 每日执行时间
                    "end_time": "18:00",  # 结束时间
                    "timezone": "Asia/Shanghai"
                }
            },
            {
                "name": "每日执行-完整配置",
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "08:45",  # 每日执行时间
                    "end_time": "20:30",  # 结束时间
                    "max_runs": 10,
                    "enable_random_delay": True,
                    "random_delay_min": 5,
                    "random_delay_max": 15,
                    "timezone": "Asia/Shanghai"
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  测试用例 {i}: {test_case['name']}")
            
            # 创建任务
            create_data = {
                "name": f"调度测试任务-{i}",
                "description": f"测试{test_case['name']}的任务",
                "url_ids": [url_id],
                "schedule": test_case['schedule'],
                "config": {
                    "platform": "mercadolibre",
                    "batch_size": 10,
                    "retry_count": 3,
                    "timeout": 300,
                    "priority": "normal",
                    "concurrent_limit": 5,
                    "enable_notifications": True
                }
            }
            
            print(f"    发送的调度配置: {json.dumps(test_case['schedule'], indent=2)}")
            
            create_response = requests.post(
                f"{base_url}/api/v1/tasks/create-from-urls",
                json=create_data,
                timeout=10
            )
            
            if create_response.status_code == 200:
                create_result = create_response.json()
                task_id = create_result.get('task_id')
                task_ids.append(task_id)
                print(f"    ✅ 任务创建成功: {task_id}")
                
                # 获取任务详情，检查调度配置
                detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if detail_response.status_code == 200:
                    detail_result = detail_response.json()
                    task_data = detail_result.get('data', {})
                    saved_schedule = task_data.get('schedule', {})
                    
                    print(f"    保存的调度配置: {json.dumps(saved_schedule, indent=2)}")
                    
                    # 检查关键字段
                    original_schedule = test_case['schedule']
                    
                    # 检查执行时间
                    if 'time' in original_schedule:
                        saved_time = saved_schedule.get('time')
                        original_time = original_schedule['time']
                        if saved_time == original_time:
                            print(f"    ✅ 执行时间保存正确: {saved_time}")
                        else:
                            print(f"    ❌ 执行时间保存错误: 期望 {original_time}, 实际 {saved_time}")
                    
                    # 检查结束时间
                    if 'end_time' in original_schedule:
                        saved_end_time = saved_schedule.get('end_time')
                        original_end_time = original_schedule['end_time']
                        if saved_end_time == original_end_time:
                            print(f"    ✅ 结束时间保存正确: {saved_end_time}")
                        else:
                            print(f"    ❌ 结束时间保存错误: 期望 {original_end_time}, 实际 {saved_end_time}")
                    
                    # 检查其他字段
                    for field in ['enabled', 'timezone', 'max_runs', 'enable_random_delay']:
                        if field in original_schedule:
                            saved_value = saved_schedule.get(field)
                            original_value = original_schedule[field]
                            if saved_value == original_value:
                                print(f"    ✅ {field}保存正确: {saved_value}")
                            else:
                                print(f"    ❌ {field}保存错误: 期望 {original_value}, 实际 {saved_value}")
                    
                    # 检查Redis中的原始数据
                    print(f"    检查Redis中的原始数据:")
                    schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
                    redis_schedule_data = r.hgetall(schedule_key)
                    print(f"    Redis原始数据: {dict(redis_schedule_data)}")
                    
                else:
                    print(f"    ❌ 获取任务详情失败: {detail_response.text}")
            else:
                print(f"    ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 调度配置测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:schedule_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_schedule_config()
