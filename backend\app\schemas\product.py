"""
商品相关Pydantic数据模型
"""

from typing import Optional, List
from pydantic import HttpUrl, Field, validator

from .common import BaseSchema, TimestampSchema


class ProductCategoryBase(BaseSchema):
    """商品分类基础模型"""
    
    name: str = Field(..., min_length=1, max_length=200, description="分类名称")
    parent_id: Optional[int] = Field(None, description="父分类ID")
    level: int = Field(1, ge=1, description="分类层级")
    path: Optional[str] = Field(None, max_length=500, description="分类路径")


class ProductCategoryCreate(ProductCategoryBase):
    """创建商品分类模型"""
    pass


class ProductCategoryUpdate(BaseSchema):
    """更新商品分类模型"""
    
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    parent_id: Optional[int] = None
    level: Optional[int] = Field(None, ge=1)
    path: Optional[str] = Field(None, max_length=500)


class ProductCategoryResponse(ProductCategoryBase, TimestampSchema):
    """商品分类响应模型"""
    
    id: int
    children: Optional[List["ProductCategoryResponse"]] = []
    product_count: Optional[int] = 0


class ProductBase(BaseSchema):
    """商品基础模型"""
    
    name_cn: str = Field(..., min_length=1, max_length=500, description="中文品名")
    name_original: str = Field(..., min_length=1, max_length=500, description="原始西班牙语品名")
    url: HttpUrl = Field(..., description="商品链接")
    domain: str = Field(..., min_length=1, max_length=100, description="所属域名")
    category_id: Optional[int] = Field(None, description="商品分类ID")
    status: str = Field("active", description="状态")
    
    @validator('status')
    def validate_status(cls, v):
        if v not in ('active', 'inactive', 'deleted'):
            raise ValueError('状态必须是 active, inactive, deleted 之一')
        return v
    
    @validator('domain')
    def validate_domain(cls, v):
        # 简单的域名验证
        if not v or '.' not in v:
            raise ValueError('域名格式不正确')
        return v.lower()


class ProductCreate(ProductBase):
    """创建商品模型"""
    pass


class ProductUpdate(BaseSchema):
    """更新商品模型"""
    
    name_cn: Optional[str] = Field(None, min_length=1, max_length=500)
    name_original: Optional[str] = Field(None, min_length=1, max_length=500)
    url: Optional[HttpUrl] = None
    domain: Optional[str] = Field(None, min_length=1, max_length=100)
    category_id: Optional[int] = None
    status: Optional[str] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None and v not in ('active', 'inactive', 'deleted'):
            raise ValueError('状态必须是 active, inactive, deleted 之一')
        return v


class ProductResponse(ProductBase, TimestampSchema):
    """商品响应模型"""
    
    id: int
    category: Optional[ProductCategoryResponse] = None
    
    # 统计信息
    snapshot_count: Optional[int] = 0
    last_crawled_at: Optional[str] = None
    
    # 最新价格信息
    latest_price: Optional[float] = None
    price_trend: Optional[str] = None  # up, down, stable


class ProductListResponse(BaseSchema):
    """商品列表响应模型"""
    
    id: int
    name_cn: str
    name_original: str
    url: str
    domain: str
    status: str
    category_name: Optional[str] = None
    latest_price: Optional[float] = None
    last_crawled_at: Optional[str] = None
    created_at: str


class ProductStatsResponse(BaseSchema):
    """商品统计响应模型"""
    
    total_products: int = 0
    active_products: int = 0
    inactive_products: int = 0
    deleted_products: int = 0
    
    # 按域名分组统计
    domain_stats: dict = {}
    
    # 按分类分组统计
    category_stats: dict = {}
    
    # 最近添加的商品数量
    recent_added: int = 0


class ProductBatchCreate(BaseSchema):
    """批量创建商品模型"""
    
    products: List[ProductCreate] = Field(..., min_items=1, max_items=1000)
    
    @validator('products')
    def validate_unique_urls(cls, v):
        urls = [product.url for product in v]
        if len(urls) != len(set(urls)):
            raise ValueError('商品链接不能重复')
        return v


class ProductImportResponse(BaseSchema):
    """商品导入响应模型"""
    
    total: int
    success: int
    failed: int
    duplicates: int = 0
    errors: List[str] = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return (self.success / self.total) * 100 if self.total > 0 else 0.0


# 解决前向引用问题
ProductCategoryResponse.model_rebuild() 