/**
 * 爬虫配置表单组件
 * 用于创建和编辑爬虫配置
 */

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Card,
  Row,
  Col,
  Alert,
  Tabs,
  Modal,
  message,
  Spin
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import {
  newCrawlerApi,
  CrawlerTaskConfig,
  CrawlerConfigCreate,
  CrawlerConfigUpdate,
  PlatformInfo,
  ConfigTemplate
} from '../../../services/newCrawlerApi';

const { TextArea } = Input;
const { Option } = Select;

// 默认的LLM查询指令
const DEFAULT_LLM_QUERY = `请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量（格式 +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品；
获取商品的各级目录文本、获取商品各级目录的链接；
商品销售商名称、商品销售商链接；
商品第一张图片的最大尺寸版链接；
商品页面的问题相关的内容、问题时间、问题回答；
商品所有问题页面的链接；
商品评分数、商品评分数量；
商品的评论内容、评论评分、评论时间；
商品所有评论页面的链接；
任务开始的时间戳YYYYMMDDHHMMSS`;

// 默认的JSON Schema
const DEFAULT_JSON_SCHEMA = `{
  "type": "object",
  "description": "电商商品完整信息提取结构",
  "properties": {
    "task_info": {
      "type": "object",
      "description": "任务执行信息",
      "properties": {
        "start_timestamp": {
          "type": "string",
          "pattern": "^[0-9]{14}$",
          "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
        }
      },
      "required": ["start_timestamp"]
    },
    "product_basic_info": {
      "type": "object",
      "description": "商品基础信息",
      "properties": {
        "product_url": {
          "type": "string",
          "format": "uri",
          "description": "商品链接"
        },
        "product_name": {
          "type": "string",
          "description": "商品名称"
        },
        "mlm_id": {
          "type": "number",
          "description": "商品MLM-ID中的数值部分"
        }
      },
      "required": ["product_url", "product_name", "mlm_id"]
    },
    "pricing_info": {
      "type": "object",
      "description": "价格和库存信息",
      "properties": {
        "sales_count": {
          "type": "number",
          "description": "商品销量（格式：+xxx vendidos中的数值）"
        },
        "current_price": {
          "type": "number",
          "description": "商品现价（数值）"
        },
        "original_price": {
          "type": "number",
          "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价就是商品原价"
        },
        "discount_rate": {
          "type": "number",
          "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
        },
        "stock_quantity": {
          "type": "number",
          "description": "商品库存数量，如果页面显示Publicación pausada或者没有获取到库存数值，那库存数量就是0"
        },
        "stocktype_IsFull": {
          "type": "number",
          "description": "商品是否属于FULL仓配送商品，判断商品信息是否有FULL相关标识，有就是1，没有就是0"
        }
      },
      "required": ["current_price"]
    },
    "category_info": {
      "type": "object",
      "description": "商品目录分类信息",
      "properties": {
        "category_breadcrumb": {
          "type": "string",
          "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"
        },
        "category_hierarchy": {
          "type": "array",
          "description": "商品各级目录详细信息",
          "items": {
            "type": "object",
            "properties": {
              "category_name": {
                "type": "string",
                "description": "目录名称"
              },
              "category_url": {
                "type": "string",
                "format": "uri",
                "description": "目录链接"
              },
              "level": {
                "type": "integer",
                "description": "目录层级（1为顶级，2为二级，以此类推）"
              }
            },
            "required": ["category_name", "level"]
          }
        }
      },
      "required": ["category_breadcrumb"]
    },
    "seller_info": {
      "type": "object",
      "description": "销售商信息",
      "properties": {
        "seller_name": {
          "type": "string",
          "description": "商品销售商名称"
        },
        "seller_url": {
          "type": "string",
          "format": "uri",
          "description": "商品销售商链接"
        }
      },
      "required": ["seller_name"]
    },
    "media_info": {
      "type": "object",
      "description": "商品媒体信息",
      "properties": {
        "main_image_url": {
          "type": "string",
          "format": "uri",
          "description": "商品第一张图片的最大尺寸版链接"
        }
      },
      "required": ["main_image_url"]
    },
    "qa_section": {
      "type": "object",
      "description": "问答区域信息",
      "properties": {
        "questions": {
          "type": "array",
          "description": "商品页面的问题列表",
          "items": {
            "type": "object",
            "properties": {
              "question_content": {
                "type": "string",
                "description": "问题内容"
              },
              "question_time": {
                "type": "string",
                "description": "问题时间"
              },
              "answer_content": {
                "type": "string",
                "description": "问题回答"
              }
            },
            "required": ["question_content", "answer_content"]
          }
        },
        "all_questions_url": {
          "type": "string",
          "format": "uri",
          "description": "商品所有问题页面的链接，如果商品页还没有问题就没有对应的链接"
        }
      }
    },
    "rating_info": {
      "type": "object",
      "description": "评分信息",
      "properties": {
        "rating_score": {
          "type": "number",
          "minimum": 0,
          "maximum": 5,
          "description": "商品评分数（0-5分）"
        },
        "rating_count": {
          "type": "integer",
          "description": "商品评分数量"
        }
      },
      "required": ["rating_score", "rating_count"]
    },
    "reviews_section": {
      "type": "object",
      "description": "评论区域信息",
      "properties": {
        "reviews": {
          "type": "array",
          "description": "商品评论列表",
          "items": {
            "type": "object",
            "properties": {
              "review_content": {
                "type": "string",
                "description": "评论内容"
              },
              "review_rating": {
                "type": "number",
                "minimum": 0,
                "maximum": 5,
                "description": "评论评分（0-5分）"
              },
              "review_time": {
                "type": "string",
                "description": "评论时间"
              }
            },
            "required": ["review_content", "review_time"]
          }
        },
        "all_reviews_url": {
          "type": "string",
          "format": "uri",
          "description": "商品所有评论页面的链接，如果商品页还没有评论就没有对应的链接"
        }
      }
    }
  },
  "required": ["product_basic_info", "category_info", "pricing_info", "media_info", "rating_info"]
}`;


interface CrawlerConfigFormProps {
  config?: CrawlerTaskConfig | null;
  onSave: () => void;
  onCancel: () => void;
}

const CrawlerConfigForm: React.FC<CrawlerConfigFormProps> = ({
  config,
  onSave,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [platforms, setPlatforms] = useState<PlatformInfo[]>([]);
  const [templates, setTemplates] = useState<ConfigTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ConfigTemplate | null>(null);

  // 应用默认MercadoLibre模板
  const applyDefaultTemplate = () => {
    form.setFieldsValue({
      platform: 'mercadolibre',
      llm_query: DEFAULT_LLM_QUERY,
      llm_schema: DEFAULT_JSON_SCHEMA
    });
    message.success('已应用MercadoLibre默认模板');
  };

  // 应用模板
  const applyTemplate = (template: ConfigTemplate) => {
    setSelectedTemplate(template);
    form.setFieldsValue({
      platform: template.platform,
      llm_query: template.llm_query,
      llm_schema: template.llm_schema
    });
    message.success(`已应用模板: ${template.name}`);
  };

  // Tabs配置
  const tabItems = [
    {
      key: 'basic',
      label: '基本配置',
      children: (
        <>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="配置名称"
                name="name"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="输入配置名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="配置类型"
                name="config_type"
                rules={[{ required: true, message: '请选择配置类型' }]}
              >
                <Select placeholder="选择配置类型">
                  <Option value="batch_task">批量任务</Option>
                  <Option value="single_task">单个任务</Option>
                  <Option value="template">模板</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="配置描述"
            name="description"
          >
            <TextArea rows={2} placeholder="输入配置描述（可选）" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="目标平台"
                name="platform"
                rules={[{ required: true, message: '请选择目标平台' }]}
              >
                <Select placeholder="选择目标平台">
                  {platforms.map(platform => (
                    <Option key={platform.value} value={platform.value}>
                      {platform.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="任务优先级"
                name="priority"
                rules={[{ required: true, message: '请选择任务优先级' }]}
              >
                <Select placeholder="选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="启用状态"
                name="is_active"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
        </>
      )
    },
    {
      key: 'llm',
      label: 'LLM配置',
      children: (
        <>
          <Alert
            message="LLM查询配置"
            description="配置用于数据提取的LLM查询指令和输出格式"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            label="查询指令"
            name="llm_query"
            rules={[{ required: true, message: '请输入LLM查询指令' }]}
          >
            <TextArea
              rows={4}
              placeholder="输入LLM查询指令，例如：提取页面中的商品名称、价格、评分等信息"
            />
          </Form.Item>

          <Form.Item
            label="输出格式 (JSON Schema)"
            name="llm_schema"
          >
            <TextArea
              rows={6}
              placeholder="输入JSON Schema格式，或使用默认的MercadoLibre商品信息提取Schema"
            />
          </Form.Item>

          {/* 默认模板 */}
          <Card title="快速模板" size="small" style={{ marginTop: 16 }}>
            <Space wrap>
              <Button
                size="small"
                type="primary"
                onClick={applyDefaultTemplate}
              >
                MercadoLibre默认模板
              </Button>
            </Space>
          </Card>

          {/* 自定义模板选择 */}
          {templates.length > 0 && (
            <Card title="自定义模板" size="small" style={{ marginTop: 16 }}>
              <Space wrap>
                {templates.map(template => (
                  <Button
                    key={template.id}
                    size="small"
                    onClick={() => applyTemplate(template)}
                    type={selectedTemplate?.id === template.id ? 'primary' : 'default'}
                  >
                    {template.name}
                  </Button>
                ))}
              </Space>
            </Card>
          )}
        </>
      )
    },
    {
      key: 'execution',
      label: '执行配置',
      children: (
        <>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="启用缓存"
                name="cache_enabled"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Alert
            message="系统配置说明"
            description="最大并发任务数、批次超时时间、回调URL等系统级配置已移至后端环境变量管理，无需在此配置。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </>
      )
    }
  ];

  // 加载平台和模板数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const [platformsData, templatesData] = await Promise.all([
          newCrawlerApi.getPlatforms(),
          newCrawlerApi.getTemplates()
        ]);
        setPlatforms(platformsData);
        setTemplates(templatesData);
      } catch (error) {
        message.error(`加载数据失败: ${error}`);
      }
    };
    
    loadData();
  }, []);

  // 初始化表单数据
  useEffect(() => {
    if (config) {
      form.setFieldsValue({
        name: config.name,
        description: config.description,
        config_type: config.config_type,
        platform: config.platform,
        llm_query: config.llm_query,
        llm_schema: config.llm_schema,
        priority: config.priority,
        cache_enabled: config.cache_enabled,
        is_active: config.is_active
      });
    } else {
      // 新建时的默认值
      form.setFieldsValue({
        config_type: 'batch_task',
        platform: 'mercadolibre',
        priority: 'medium',
        cache_enabled: false,
        is_active: true,
        llm_query: DEFAULT_LLM_QUERY,
        llm_schema: DEFAULT_JSON_SCHEMA
      });
    }
  }, [config, form]);





  // 验证配置
  const validateConfig = async () => {
    try {
      // 获取当前表单值进行验证
      const values = await form.validateFields();

      // 验证必要字段
      const validationResults = [];

      // 检查基本配置
      if (!values.name || values.name.trim() === '') {
        validationResults.push('❌ 配置名称不能为空');
      } else {
        validationResults.push('✅ 配置名称有效');
      }

      if (!values.platform) {
        validationResults.push('❌ 目标平台未设置');
      } else {
        validationResults.push(`✅ 目标平台: ${values.platform}`);
      }

      // 检查LLM查询
      if (!values.llm_query || values.llm_query.trim() === '') {
        validationResults.push('❌ LLM查询指令为空');
      } else {
        validationResults.push('✅ LLM查询指令已设置');
      }

      // 检查JSON Schema
      if (values.llm_schema) {
        try {
          JSON.parse(values.llm_schema);
          validationResults.push('✅ JSON Schema格式有效');
        } catch {
          validationResults.push('❌ JSON Schema格式无效');
        }
      } else {
        validationResults.push('⚠️ JSON Schema未设置（可选）');
      }

      // 检查执行参数
      const maxConcurrent = values.max_concurrent_tasks || 2;
      const batchTimeout = values.batch_timeout || 300;

      if (maxConcurrent > 0 && maxConcurrent <= 50) {
        validationResults.push(`✅ 并发任务数: ${maxConcurrent}`);
      } else {
        validationResults.push('❌ 并发任务数超出范围(1-50)');
      }

      if (batchTimeout >= 60 && batchTimeout <= 86400) {
        validationResults.push(`✅ 批次超时: ${batchTimeout}秒`);
      } else {
        validationResults.push('❌ 批次超时超出范围(60-86400秒)');
      }

      // 检查配置类型
      const validTypes = ['batch_task', 'single_task', 'template'];
      if (validTypes.includes(values.config_type)) {
        validationResults.push(`✅ 配置类型: ${values.config_type}`);
      } else {
        validationResults.push('❌ 配置类型无效');
      }

      // 检查优先级
      const validPriorities = ['high', 'medium', 'low'];
      if (validPriorities.includes(values.priority)) {
        validationResults.push(`✅ 优先级: ${values.priority}`);
      } else {
        validationResults.push('❌ 优先级设置无效');
      }

      const hasErrors = validationResults.some(result => result.startsWith('❌'));

      Modal.info({
        title: hasErrors ? '配置验证失败' : '配置验证通过',
        content: (
          <div>
            <p><strong>配置: {values.name}</strong></p>
            <div style={{ marginTop: 12 }}>
              {validationResults.map((result, index) => (
                <p key={index} style={{ margin: '4px 0' }}>{result}</p>
              ))}
            </div>
          </div>
        ),
        width: 500
      });
    } catch (error) {
      message.error(`配置验证失败: ${error}`);
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (config) {
        // 更新配置
        await newCrawlerApi.updateConfig(config.id, values as CrawlerConfigUpdate);
        message.success('配置更新成功');
      } else {
        // 创建配置
        await newCrawlerApi.createConfig(values as CrawlerConfigCreate);
        message.success('配置创建成功');
      }
      
      onSave();
    } catch (error) {
      message.error(`保存配置失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
      >
        <Tabs defaultActiveKey="basic" items={tabItems} />

        {/* 操作按钮 */}
        <div style={{ marginTop: 24, textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel} icon={<CloseOutlined />}>
              取消
            </Button>
            
            <Button onClick={validateConfig} icon={<CheckCircleOutlined />}>
              验证配置
            </Button>
            
            <Button
              type="primary"
              onClick={handleSave}
              icon={<SaveOutlined />}
              loading={loading}
            >
              保存配置
            </Button>
          </Space>
        </div>
      </Form>
    </Spin>
  );
};

export default CrawlerConfigForm;
