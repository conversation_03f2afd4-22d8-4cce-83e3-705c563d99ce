react-dom-client.development.js:13848 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
websocket.ts:42 WebSocket connected
CrawlerConfigForm.tsx:447  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
injectCSS @ dynamicCSS.js:74
updateCSS @ dynamicCSS.js:145
(匿名) @ useStyleRegister.js:343
(匿名) @ useGlobalCache.js:59
(匿名) @ useCompatibleInsertionEffect.js:30
react-stack-bottom-frame @ react-dom-client.development.js:13196
runWithFiberInDEV @ react-dom-client.development.js:1115
commitHookEffectListMount @ react-dom-client.development.js:6105
commitMutationEffectsOnFiber @ react-dom-client.development.js:6708
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6711
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6711
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6823
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
<SubStyle_Form.item-item>
exports.createElement @ react.development.js:634
FormItemInput @ FormItemInput.js:120
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<FormItemInput>
exports.createElement @ react.development.js:634
ItemHolder @ ItemHolder.js:106
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<ItemHolder>
exports.createElement @ react.development.js:634
renderLayout @ index.js:166
(匿名) @ index.js:271
(匿名) @ Field.js:404
render @ Field.js:562
react-stack-bottom-frame @ react-dom-client.development.js:13145
updateClassComponent @ react-dom-client.development.js:5201
beginWork @ react-dom-client.development.js:5624
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<Field>
exports.createElement @ react.development.js:634
WrapperField @ Field.js:606
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<WrapperField>
exports.createElement @ react.development.js:634
InternalFormItem @ index.js:193
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<InternalFormItem>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:221
CrawlerConfigForm @ CrawlerConfigForm.tsx:447
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<CrawlerConfigForm>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:221
CrawlerConfiguration @ CrawlerConfiguration.tsx:369
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performSyncWorkOnRoot @ react-dom-client.development.js:8300
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:8219
processRootScheduleInMicrotask @ react-dom-client.development.js:8239
(匿名) @ react-dom-client.development.js:8311
CrawlerConfigForm.tsx:447  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
injectCSS @ dynamicCSS.js:74
updateCSS @ dynamicCSS.js:145
(匿名) @ useStyleRegister.js:343
(匿名) @ useGlobalCache.js:59
(匿名) @ useCompatibleInsertionEffect.js:30
react-stack-bottom-frame @ react-dom-client.development.js:13196
runWithFiberInDEV @ react-dom-client.development.js:1115
commitHookEffectListMount @ react-dom-client.development.js:6105
commitMutationEffectsOnFiber @ react-dom-client.development.js:6708
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6711
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6711
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6823
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6775
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6896
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
commitMutationEffectsOnFiber @ react-dom-client.development.js:6706
recursivelyTraverseMutationEffects @ react-dom-client.development.js:6696
<SubStyle_Form.item-item>
exports.createElement @ react.development.js:634
FormItemInput @ FormItemInput.js:120
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<FormItemInput>
exports.createElement @ react.development.js:634
ItemHolder @ ItemHolder.js:106
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<ItemHolder>
exports.createElement @ react.development.js:634
renderLayout @ index.js:166
(匿名) @ index.js:271
(匿名) @ Field.js:404
render @ Field.js:562
react-stack-bottom-frame @ react-dom-client.development.js:13145
updateClassComponent @ react-dom-client.development.js:5201
beginWork @ react-dom-client.development.js:5624
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<Field>
exports.createElement @ react.development.js:634
WrapperField @ Field.js:606
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<WrapperField>
exports.createElement @ react.development.js:634
InternalFormItem @ index.js:193
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<InternalFormItem>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:221
CrawlerConfigForm @ CrawlerConfigForm.tsx:447
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:8292
performWorkUntilDeadline @ scheduler.development.js:34
<CrawlerConfigForm>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:221
CrawlerConfiguration @ CrawlerConfiguration.tsx:369
react-stack-bottom-frame @ react-dom-client.development.js:13133
renderWithHooksAgain @ react-dom-client.development.js:3398
renderWithHooks @ react-dom-client.development.js:3349
updateFunctionComponent @ react-dom-client.development.js:5036
beginWork @ react-dom-client.development.js:5622
runWithFiberInDEV @ react-dom-client.development.js:1115
performUnitOfWork @ react-dom-client.development.js:7695
workLoopSync @ react-dom-client.development.js:7588
renderRootSync @ react-dom-client.development.js:7572
performWorkOnRoot @ react-dom-client.development.js:7312
performSyncWorkOnRoot @ react-dom-client.development.js:8300
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:8219
processRootScheduleInMicrotask @ react-dom-client.development.js:8239
(匿名) @ react-dom-client.development.js:8311
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
config/crawler:1  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
config/crawler:1  [Deprecation]-ms-high-contrast is in the process of being deprecated. Please see https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/ for tips on updating to the new Forced Colors Mode standard.
CrawlerConfigForm.tsx:279 表单原始值: {name: 'modified_Crawl4ai2', tags: undefined, description: undefined, browser.headless: true, browser.verbose: false, …}
CrawlerConfigForm.tsx:280 Schema提取相关字段: {enabled: true, schema_type: 'auto', validate_schema: true, return_raw: false, instructions: '请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. …sktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS', …}
CrawlerConfigForm.tsx:420 发送的配置数据: {
  "config_name": "modified_Crawl4ai2",
  "description": "",
  "tags": [],
  "version": "1.0",
  "created_by": "user",
  "browser": {
    "headless": true,
    "verbose": false,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "wait_for": 2,
    "timeout": 30,
    "ignore_https_errors": true,
    "extra_args": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-blink-features=AutomationControlled",
      "--exclude-switches=enable-automation"
    ]
  },
  "crawler": {
    "method": "arun_many",
    "verbose": true,
    "check_robots_txt": false,
    "fetch_ssl_certificate": false,
    "simulate_user": true,
    "magic": true,
    "override_navigator": true,
    "remove_overlay_elements": true,
    "ignore_body_visibility": true,
    "adjust_viewport_to_content": true,
    "wait_until": "domcontentloaded",
    "wait_for_images": false,
    "page_timeout": 60000,
    "delay_before_return_html": 0.1,
    "js_only": false,
    "scan_full_page": true,
    "process_iframes": true,
    "scroll_delay": 0.2,
    "cache_mode": "BYPASS",
    "screenshot": false,
    "pdf": false,
    "capture_mhtml": false,
    "exclude_external_images": false,
    "exclude_all_images": false,
    "image_score_threshold": 50,
    "image_description_min_word_threshold": 50,
    "table_score_threshold": 7,
    "capture_network_requests": false,
    "capture_console_messages": false,
    "log_console": false,
    "bypass_cache": true
  },
  "llm": {
    "query": "",
    "provider": "openai",
    "model": "gpt-4",
    "api_key": "",
    "base_url": "",
    "temperature": 0.7,
    "max_tokens": 4096,
    "top_p": 1
  },
  "schema_extraction": {
    "enabled": true,
    "schema_type": "auto",
    "validate_schema": true,
    "return_raw": false,
    "extraction_schema": {
      "type": "object",
      "description": "电商商品完整信息提取结构",
      "properties": {
        "task_info": {
          "type": "object",
          "description": "任务执行信息",
          "properties": {
            "start_timestamp": {
              "type": "string",
              "pattern": "^[0-9]{14}$",
              "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
            }
          },
          "required": [
            "start_timestamp"
          ]
        },
        "product_basic_info": {
          "type": "object",
          "description": "商品基础信息",
          "properties": {
            "product_url": {
              "type": "string",
              "format": "uri",
              "description": "商品链接"
            },
            "product_name": {
              "type": "string",
              "description": "商品名称"
            },
            "mlm_id": {
              "type": "number",
              "description": "商品MLM-ID中的数值部分"
            }
          },
          "required": [
            "product_url",
            "product_name",
            "mlm_id"
          ]
        },
        "pricing_info": {
          "type": "object",
          "description": "价格和库存信息",
          "properties": {
            "sales_count": {
              "type": "number",
              "description": "商品销量（格式：+xxx vendidos中的数值）"
            },
            "current_price": {
              "type": "number",
              "description": "商品现价（数值）"
            },
            "original_price": {
              "type": "number",
              "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
            },
            "discount_rate": {
              "type": "number",
              "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
            },
            "stock_quantity": {
              "type": "number",
              "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
            }
          },
          "required": [
            "sales_count",
            "current_price",
            "original_price",
            "discount_rate",
            "stock_quantity"
          ]
        }
      },
      "required": [
        "task_info",
        "product_basic_info",
        "pricing_info"
      ]
    },
    "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS"
  },
  "content_processing": {
    "word_count_threshold": 200,
    "css_selector": "",
    "target_elements": [],
    "excluded_tags": [],
    "excluded_selector": "",
    "remove_forms": false,
    "only_text": false,
    "prettify": false,
    "parser_type": "lxml",
    "keep_data_attributes": false,
    "keep_attrs": []
  },
  "link_filtering": {
    "exclude_external_links": false,
    "exclude_internal_links": false,
    "exclude_social_media_links": false,
    "exclude_domains": [],
    "social_media_domains": [],
    "exclude_external_images": false,
    "exclude_all_images": false,
    "image_score_threshold": 3,
    "image_description_min_word_threshold": 50,
    "table_score_threshold": 7
  },
  "monitor": {
    "display_mode": "detailed",
    "show_progress": true,
    "log_errors": true
  }
}
crawlerConfigApi.ts:66   POST http://localhost:8000/api/v1/crawler-configs/ 422 (Unprocessable Entity)
createConfig @ crawlerConfigApi.ts:66
onSave @ CrawlerConfiguration.tsx:382
handleSubmit @ CrawlerConfigForm.tsx:421
await in handleSubmit
(匿名) @ button.js:185
executeDispatch @ react-dom-client.development.js:8372
runWithFiberInDEV @ react-dom-client.development.js:1115
processDispatchQueue @ react-dom-client.development.js:8400
(匿名) @ react-dom-client.development.js:8693
batchedUpdates$1 @ react-dom-client.development.js:1963
dispatchEventForPluginEventSystem @ react-dom-client.development.js:8476
dispatchEvent @ react-dom-client.development.js:10575
dispatchDiscreteEvent @ react-dom-client.development.js:10557
CrawlerConfiguration.tsx:389  保存配置失败: Error: 请求参数验证失败
    at CrawlerConfigApiService.createConfig (crawlerConfigApi.ts:76:1)
    at async onSave (CrawlerConfiguration.tsx:382:1)
    at async Object.handleSubmit [as onClick] (CrawlerConfigForm.tsx:421:1)
onSave @ CrawlerConfiguration.tsx:389
await in onSave
handleSubmit @ CrawlerConfigForm.tsx:421
await in handleSubmit
(匿名) @ button.js:185
executeDispatch @ react-dom-client.development.js:8372
runWithFiberInDEV @ react-dom-client.development.js:1115
processDispatchQueue @ react-dom-client.development.js:8400
(匿名) @ react-dom-client.development.js:8693
batchedUpdates$1 @ react-dom-client.development.js:1963
dispatchEventForPluginEventSystem @ react-dom-client.development.js:8476
dispatchEvent @ react-dom-client.development.js:10575
dispatchDiscreteEvent @ react-dom-client.development.js:10557
CrawlerConfigForm.tsx:423  保存配置失败: Error: 请求参数验证失败
    at CrawlerConfigApiService.createConfig (crawlerConfigApi.ts:76:1)
    at async onSave (CrawlerConfiguration.tsx:382:1)
    at async Object.handleSubmit [as onClick] (CrawlerConfigForm.tsx:421:1)
handleSubmit @ CrawlerConfigForm.tsx:423
await in handleSubmit
(匿名) @ button.js:185
executeDispatch @ react-dom-client.development.js:8372
runWithFiberInDEV @ react-dom-client.development.js:1115
processDispatchQueue @ react-dom-client.development.js:8400
(匿名) @ react-dom-client.development.js:8693
batchedUpdates$1 @ react-dom-client.development.js:1963
dispatchEventForPluginEventSystem @ react-dom-client.development.js:8476
dispatchEvent @ react-dom-client.development.js:10575
dispatchDiscreteEvent @ react-dom-client.development.js:10557
