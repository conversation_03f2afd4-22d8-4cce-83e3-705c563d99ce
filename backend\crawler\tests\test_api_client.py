"""
API客户端测试

测试爬虫API客户端的功能。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
import httpx

from backend.crawler.api_client import CrawlerAPIClient, APIConfig
from backend.crawler.api_client.api_exceptions import (
    APIException, 
    APITimeoutException, 
    APIAuthException
)


@pytest.fixture
def api_config():
    """API配置fixture"""
    return APIConfig(
        base_url="http://test-api.example.com",
        api_version="v1",
        timeout=10.0,
        max_retries=2,
        api_key="test-key",
        auth_type="api_key"
    )


@pytest.fixture
def api_client(api_config):
    """API客户端fixture"""
    return CrawlerAPIClient(api_config)


class TestAPIConfig:
    """API配置测试"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = APIConfig(
            base_url="http://example.com",
            api_key="test-key"
        )
        assert config.base_url == "http://example.com"
        assert config.api_key == "test-key"
        assert "X-API-Key" in config.headers
    
    def test_endpoint_url_generation(self, api_config):
        """测试端点URL生成"""
        crawl_url = api_config.get_crawl_url()
        assert crawl_url == "http://test-api.example.com/api/v1/crawl"
        
        status_url = api_config.get_status_url("task123")
        assert status_url == "http://test-api.example.com/api/v1/tasks/task123/status"
    
    def test_config_validation(self):
        """测试配置验证"""
        config = APIConfig(base_url="http://example.com")
        assert config.validate() is True
        
        # 测试无效配置
        with pytest.raises(ValueError):
            invalid_config = APIConfig(base_url="")
            invalid_config.validate()


class TestCrawlerAPIClient:
    """爬虫API客户端测试"""
    
    @pytest.mark.asyncio
    async def test_client_lifecycle(self, api_client):
        """测试客户端生命周期"""
        # 测试启动
        await api_client.start()
        assert api_client.client is not None
        
        # 测试关闭
        await api_client.close()
        assert api_client.client is None
    
    @pytest.mark.asyncio
    async def test_context_manager(self, api_config):
        """测试上下文管理器"""
        async with CrawlerAPIClient(api_config) as client:
            assert client.client is not None
        # 客户端应该自动关闭
    
    @pytest.mark.asyncio
    async def test_crawl_url_success(self, api_client):
        """测试成功的URL爬取"""
        mock_response = {
            "success": True,
            "data": {
                "title": "Test Product",
                "price": "$99.99"
            },
            "url": "http://example.com/product/123"
        }
        
        with patch.object(api_client, '_retry_request') as mock_request:
            mock_request.return_value.json.return_value = mock_response
            
            result = await api_client.crawl_url(
                "http://example.com/product/123",
                platform="mercadolibre"
            )
            
            assert result == mock_response
            mock_request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_crawl_batch(self, api_client):
        """测试批量爬取"""
        urls = [
            "http://example.com/product/1",
            "http://example.com/product/2"
        ]
        
        mock_response = {
            "task_id": "batch-123",
            "status": "pending",
            "urls_count": 2
        }
        
        with patch.object(api_client, '_retry_request') as mock_request:
            mock_request.return_value.json.return_value = mock_response
            
            result = await api_client.crawl_batch(urls, platform="mercadolibre")
            
            assert result == mock_response
            mock_request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_task_status(self, api_client):
        """测试获取任务状态"""
        mock_response = {
            "task_id": "task-123",
            "status": "completed",
            "progress": 100
        }
        
        with patch.object(api_client, '_retry_request') as mock_request:
            mock_request.return_value.json.return_value = mock_response
            
            result = await api_client.get_task_status("task-123")
            
            assert result == mock_response
    
    @pytest.mark.asyncio
    async def test_api_timeout_exception(self, api_client):
        """测试API超时异常"""
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.side_effect = httpx.TimeoutException("Request timeout")
            
            with pytest.raises(APITimeoutException):
                await api_client.crawl_url("http://example.com/product/123")
    
    @pytest.mark.asyncio
    async def test_api_auth_exception(self, api_client):
        """测试API认证异常"""
        mock_response = AsyncMock()
        mock_response.status_code = 401
        
        with patch.object(api_client, '_make_request') as mock_request:
            mock_request.return_value = mock_response
            
            with pytest.raises(APIAuthException):
                await api_client.crawl_url("http://example.com/product/123")
    
    def test_stats_tracking(self, api_client):
        """测试统计信息跟踪"""
        stats = api_client.get_stats()
        
        assert "requests_sent" in stats
        assert "requests_successful" in stats
        assert "requests_failed" in stats
        assert "success_rate" in stats
        assert "avg_response_time" in stats
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, api_client):
        """测试限流功能"""
        # 这个测试需要模拟大量请求来触发限流
        # 由于测试环境的限制，这里只做基本的功能验证
        await api_client._check_rate_limit()
        # 应该不会抛出异常


if __name__ == "__main__":
    pytest.main([__file__])
