# 🚀 MonIt 项目启动指南

## 📋 快速启动清单

### ✅ 当前状态验证
根据最新测试，您的系统状态：

- **✅ Docker**: 已安装并运行
- **✅ Conda环境**: `monit` 环境已创建 (Python 3.11.13)
- **✅ Redis**: 容器运行中 (v7.4.4)
- **✅ TimescaleDB**: 容器运行中
- **✅ 任务调度系统**: 测试通过 (624个URL, 9.95 URLs/sec)

## 🎯 启动方式选择

### 方式1: 任务调度系统测试 (推荐开始)
```bash
# 检查服务状态
conda activate monit
python scripts/check_services.py

# 运行快速测试
python backend/scripts/quick_test.py

# 运行真实场景测试 (624个URL)
python backend/scripts/run_real_test.py
```

### 方式2: 完整Docker环境
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 访问服务
# - 前端: http://localhost:3000
# - 后端API: http://localhost:8000/docs
# - 健康检查: http://localhost:8000/health
```

### 方式3: 混合开发模式
```bash
# 1. 启动基础设施
docker-compose -f docker-compose.dev.yml up timescaledb redis -d

# 2. 激活环境
conda activate monit

# 3. 启动后端 (终端1)
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 4. 启动Celery Worker (终端2)
celery -A app.celery_app worker --loglevel=info

# 5. 启动任务管理器 (终端3)
python backend/scripts/start_task_manager.py
```

## 🏗️ 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │    │数据库(TimescaleDB)│
│   Port: 3000    │◄──►│   Port: 8000    │◄──►│   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Celery Worker   │◄──►│  Redis (队列)    │    │ 任务调度管理器    │
│  (任务执行)      │    │   Port: 6379    │    │  (Task Manager) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 核心功能验证

### 任务调度系统测试结果
- **Excel处理**: ✅ 626行数据，624个有效URL
- **平台识别**: ✅ 100% MercadoLibre链接
- **任务分片**: ✅ 100个URL → 5个批次 (20个URL/批次)
- **限流控制**: ✅ 严格2并发限制
- **优先级系统**: ✅ 4级优先级 (urgent > high > normal > low)
- **处理性能**: ✅ 9.95 URLs/秒

### 重试系统功能
- **智能失败检测**: 网络错误、超时、API限制、解析错误
- **多种重试策略**: 指数退避、线性退避、固定延迟
- **优先级降级**: 重试任务自动降低优先级
- **死信队列**: 超过重试次数的任务管理

## 🔧 必需服务

### 基础设施 (必需)
1. **Docker** - 容器运行环境
2. **Conda** - Python环境管理
3. **Redis** - 消息队列和缓存

### 应用服务 (可选)
4. **TimescaleDB** - 时间序列数据库
5. **FastAPI Backend** - 后端API服务
6. **React Frontend** - 前端界面
7. **Celery Worker** - 任务执行器

## 🧪 测试命令

### 环境检查
```bash
# 检查所有服务状态
python scripts/check_services.py

# 检查conda环境
conda env list | grep monit

# 检查Docker容器
docker ps | grep monit
```

### 功能测试
```bash
# 快速功能测试 (100个URL)
python backend/scripts/quick_test.py

# 真实场景测试 (624个URL)
python backend/scripts/run_real_test.py

# 重试系统测试
python backend/tests/test_retry_system.py
```

## 🚨 故障排除

### 常见问题

#### 1. Redis连接失败
```bash
# 检查Redis容器
docker ps | grep redis

# 启动Redis
docker run -d --name monit-redis -p 6379:6379 redis:7-alpine

# 测试连接
redis-cli ping
```

#### 2. Conda环境问题
```bash
# 重新创建环境
conda env remove -n monit
conda create -n monit python=3.11 -y
conda activate monit
pip install -r requirements.txt
```

#### 3. 端口被占用
```bash
# 查看端口占用
netstat -an | grep :6379

# Windows杀死进程
taskkill /F /PID <PID>

# Linux/Mac杀死进程
kill -9 <PID>
```

## 📈 性能基准

基于真实测试数据：

| 指标 | 值 | 说明 |
|------|----|----|
| Excel处理 | 624个URL | 2行无效数据自动过滤 |
| 任务分片 | 20个URL/批次 | 智能批次大小计算 |
| 限流控制 | 2并发 | 严格遵守外部API限制 |
| 处理速度 | 9.95 URLs/秒 | 模拟环境性能 |
| 优先级范围 | 1.5-12.0 | 4级优先级分数 |

## 🎯 下一步操作

### 立即可用
1. **运行任务调度测试**: `python backend/scripts/quick_test.py`
2. **检查服务状态**: `python scripts/check_services.py`
3. **真实场景测试**: `python backend/scripts/run_real_test.py`

### 完整开发环境
1. **启动完整系统**: `docker-compose -f docker-compose.dev.yml up -d`
2. **访问前端界面**: http://localhost:3000
3. **查看API文档**: http://localhost:8000/docs

### 生产部署准备
1. **配置环境变量**: 复制并编辑 `.env` 文件
2. **数据库初始化**: 运行数据库迁移脚本
3. **监控设置**: 配置日志和性能监控

## 📚 相关文档

- **环境设置**: [docs/environment_setup_guide.md](docs/environment_setup_guide.md)
- **启动指南**: [docs/startup_guide.md](docs/startup_guide.md)
- **任务调度**: [docs/task_scheduler_guide.md](docs/task_scheduler_guide.md)
- **重试系统**: [docs/retry_system_guide.md](docs/retry_system_guide.md)
- **真实测试**: [docs/real_scenario_test_guide.md](docs/real_scenario_test_guide.md)

---

**🎉 恭喜！您的MonIt系统已经完全就绪！**

现在您可以开始使用强大的任务调度系统来处理大规模的商品监控任务了。
