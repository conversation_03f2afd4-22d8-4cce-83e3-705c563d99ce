"""
重新设计的爬虫API服务
基于外部爬虫API (http://localhost:11238) 的完整集成
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

import httpx
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class TaskPriority(str, Enum):
    """任务优先级"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    PERMANENTLY_FAILED = "permanently_failed"
    CANCELLED = "cancelled"


@dataclass
class AuthContext:
    """认证上下文"""
    authenticated: bool = False
    user_id: Optional[str] = None
    role: Optional[str] = None
    permissions: List[str] = None
    auth_method: str = "api_key"
    client_ip: Optional[str] = None
    authenticated_at: datetime = None

    def __post_init__(self):
        if self.permissions is None:
            self.permissions = []
        if self.authenticated_at is None:
            self.authenticated_at = datetime.now()


class BatchTaskRequest(BaseModel):
    """批量任务请求"""
    urls: List[str] = Field(..., min_items=1, max_items=1000, description="URL列表")
    q: str = Field(..., description="LLM查询指令")
    schema_str: Optional[str] = Field(None, description="LLM输出模式")
    cache: bool = Field(False, description="是否启用缓存")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="任务优先级")
    batch_name: Optional[str] = Field(None, max_length=100, description="批次名称")
    batch_description: Optional[str] = Field(None, max_length=500, description="批次描述")
    callback_url: Optional[str] = Field(None, description="回调URL")
    max_concurrent_tasks: Optional[int] = Field(None, ge=1, le=50, description="最大并发任务数")
    batch_timeout: Optional[int] = Field(None, ge=60, le=86400, description="批次超时时间(秒)")


class LlmJobRequest(BaseModel):
    """单个LLM任务请求（向后兼容）"""
    url: str = Field(..., description="目标URL")
    q: str = Field(..., description="LLM查询指令")
    schema: Optional[str] = Field(None, description="输出模式")
    cache: bool = Field(False, description="是否启用缓存")


class CrawlerAPIService:
    """重新设计的爬虫API服务"""
    
    def __init__(self, base_url: str = "http://localhost:11238", api_key: Optional[str] = None):
        """初始化爬虫API服务
        
        Args:
            base_url: 外部爬虫API的基础URL
            api_key: API密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.client: Optional[httpx.AsyncClient] = None
        
        # 默认认证上下文
        self.auth_context = AuthContext(
            authenticated=True,
            user_id="monit_system",
            role="admin",
            permissions=["read_tasks", "write_tasks", "read_system"],
            auth_method="api_key"
        )
        
        # 统计信息
        self.stats = {
            "requests_sent": 0,
            "requests_successful": 0,
            "requests_failed": 0,
            "total_response_time": 0.0,
            "last_request_time": None
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self) -> None:
        """启动客户端"""
        if self.client is None:
            headers = {}
            if self.api_key:
                headers["X-API-Key"] = self.api_key
            
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(300.0),  # 5分钟超时
                headers=headers
            )
            logger.info(f"Crawler API client started, base_url: {self.base_url}")
    
    async def close(self) -> None:
        """关闭客户端"""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("Crawler API client closed")
    
    def _prepare_request_body(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备请求体，包含认证上下文"""
        return {
            "request": request_data,
            "auth_context": {
                "authenticated": self.auth_context.authenticated,
                "user_id": self.auth_context.user_id,
                "role": self.auth_context.role,
                "permissions": self.auth_context.permissions,
                "auth_method": self.auth_context.auth_method,
                "client_ip": self.auth_context.client_ip,
                "authenticated_at": self.auth_context.authenticated_at.isoformat()
            }
        }
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        if not self.client:
            await self.start()
        
        url = f"{self.base_url}{endpoint}"
        start_time = datetime.now()
        
        try:
            self.stats["requests_sent"] += 1
            
            response = await self.client.request(method, url, **kwargs)
            response.raise_for_status()
            
            result = response.json()
            
            # 更新统计信息
            duration = (datetime.now() - start_time).total_seconds()
            self.stats["requests_successful"] += 1
            self.stats["total_response_time"] += duration
            self.stats["last_request_time"] = datetime.now()
            
            logger.debug(f"API request successful: {method} {endpoint}, duration: {duration:.2f}s")
            return result
            
        except httpx.HTTPStatusError as e:
            self.stats["requests_failed"] += 1
            logger.error(f"API request failed: {method} {endpoint}, status: {e.response.status_code}, error: {e}")
            raise
        except Exception as e:
            self.stats["requests_failed"] += 1
            logger.error(f"API request error: {method} {endpoint}, error: {e}")
            raise
    
    async def create_batch_tasks(self, request: BatchTaskRequest) -> Dict[str, Any]:
        """创建批量任务
        
        Args:
            request: 批量任务请求
            
        Returns:
            批量任务提交响应
        """
        request_data = request.dict()
        body = self._prepare_request_body(request_data)
        
        result = await self._make_request(
            "POST",
            "/api/v1/tasks/batch",
            json=body
        )
        
        logger.info(f"Created batch tasks: batch_id={result.get('batch_id')}, total_tasks={result.get('total_tasks')}")
        return result
    
    async def create_llm_job(self, request: LlmJobRequest) -> Dict[str, Any]:
        """创建单个LLM任务（向后兼容）
        
        Args:
            request: LLM任务请求
            
        Returns:
            任务创建响应
        """
        request_data = request.dict()
        body = self._prepare_request_body(request_data)
        
        result = await self._make_request(
            "POST",
            "/api/v1/llm/job",
            json=body
        )
        
        logger.info(f"Created LLM job: url={request.url}")
        return result
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        body = {
            "auth_context": {
                "authenticated": self.auth_context.authenticated,
                "user_id": self.auth_context.user_id,
                "role": self.auth_context.role,
                "permissions": self.auth_context.permissions,
                "auth_method": self.auth_context.auth_method,
                "client_ip": self.auth_context.client_ip,
                "authenticated_at": self.auth_context.authenticated_at.isoformat()
            }
        }
        
        result = await self._make_request(
            "GET",
            f"/api/v1/tasks/{task_id}",
            json=body
        )
        
        return result
    
    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务结果数据
        """
        body = {
            "auth_context": {
                "authenticated": self.auth_context.authenticated,
                "user_id": self.auth_context.user_id,
                "role": self.auth_context.role,
                "permissions": self.auth_context.permissions,
                "auth_method": self.auth_context.auth_method,
                "client_ip": self.auth_context.client_ip,
                "authenticated_at": self.auth_context.authenticated_at.isoformat()
            }
        }
        
        result = await self._make_request(
            "GET",
            f"/api/v1/tasks/{task_id}/result",
            json=body
        )
        
        return result
    
    async def get_batch_tasks(self, batch_id: str, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取批次任务列表
        
        Args:
            batch_id: 批次ID
            page: 页码
            size: 每页大小
            
        Returns:
            批次任务列表
        """
        body = {
            "auth_context": {
                "authenticated": self.auth_context.authenticated,
                "user_id": self.auth_context.user_id,
                "role": self.auth_context.role,
                "permissions": self.auth_context.permissions,
                "auth_method": self.auth_context.auth_method,
                "client_ip": self.auth_context.client_ip,
                "authenticated_at": self.auth_context.authenticated_at.isoformat()
            }
        }
        
        result = await self._make_request(
            "GET",
            f"/api/v1/tasks/batch/{batch_id}",
            params={"page": page, "size": size},
            json=body
        )
        
        return result
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        body = {
            "auth_context": {
                "authenticated": self.auth_context.authenticated,
                "user_id": self.auth_context.user_id,
                "role": self.auth_context.role,
                "permissions": self.auth_context.permissions,
                "auth_method": self.auth_context.auth_method,
                "client_ip": self.auth_context.client_ip,
                "authenticated_at": self.auth_context.authenticated_at.isoformat()
            }
        }
        
        result = await self._make_request(
            "GET",
            "/api/v1/system/status",
            json=body
        )
        
        return result
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        body = {
            "auth_context": {
                "authenticated": self.auth_context.authenticated,
                "user_id": self.auth_context.user_id,
                "role": self.auth_context.role,
                "permissions": self.auth_context.permissions,
                "auth_method": self.auth_context.auth_method,
                "client_ip": self.auth_context.client_ip,
                "authenticated_at": self.auth_context.authenticated_at.isoformat()
            }
        }
        
        result = await self._make_request(
            "GET",
            "/api/v1/system/metrics",
            json=body
        )
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        avg_response_time = 0.0
        if self.stats["requests_successful"] > 0:
            avg_response_time = self.stats["total_response_time"] / self.stats["requests_successful"]
        
        return {
            **self.stats,
            "avg_response_time": avg_response_time,
            "success_rate": (
                self.stats["requests_successful"] / self.stats["requests_sent"] 
                if self.stats["requests_sent"] > 0 else 0.0
            )
        }


# 全局爬虫API服务实例
crawler_api_service = CrawlerAPIService()


async def get_crawler_api_service() -> CrawlerAPIService:
    """获取爬虫API服务实例"""
    if not crawler_api_service.client:
        await crawler_api_service.start()
    return crawler_api_service
