#!/usr/bin/env python3
"""
测试现有API接口
验证后端服务基本功能正常
"""

import requests
import json
import sys
from datetime import datetime


def test_api_endpoint(method: str, url: str, data: dict = None, expected_status: int = 200) -> dict:
    """测试API端点"""
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, timeout=10)
        else:
            return {"success": False, "error": f"Unsupported method: {method}"}
        
        success = response.status_code == expected_status
        
        try:
            response_data = response.json()
        except:
            response_data = response.text
        
        return {
            "success": success,
            "status": response.status_code,
            "expected_status": expected_status,
            "data": response_data if success else None,
            "error": response_data if not success else None
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def main():
    """主测试流程"""
    print("🧪 测试现有API接口...")
    
    base_url = "http://localhost:8000"
    test_results = []
    
    # 1. 测试健康检查
    print("1. 测试健康检查...")
    result = test_api_endpoint("GET", f"{base_url}/health")
    test_results.append(("健康检查", result))
    print(f"   {'✅' if result['success'] else '❌'} 健康检查: {result['success']}")
    
    # 2. 测试API文档
    print("2. 测试API文档...")
    result = test_api_endpoint("GET", f"{base_url}/docs", expected_status=200)
    test_results.append(("API文档", result))
    print(f"   {'✅' if result['success'] else '❌'} API文档: {result['success']}")
    
    # 3. 测试OpenAPI规范
    print("3. 测试OpenAPI规范...")
    result = test_api_endpoint("GET", f"{base_url}/openapi.json")
    test_results.append(("OpenAPI规范", result))
    print(f"   {'✅' if result['success'] else '❌'} OpenAPI规范: {result['success']}")
    
    # 4. 测试URL池API
    print("4. 测试URL池API...")
    result = test_api_endpoint("GET", f"{base_url}/api/v1/url-pool/")
    test_results.append(("URL池列表", result))
    print(f"   {'✅' if result['success'] else '❌'} URL池列表: {result['success']}")
    
    # 5. 测试任务API
    print("5. 测试任务API...")
    result = test_api_endpoint("GET", f"{base_url}/api/v1/tasks/")
    test_results.append(("任务列表", result))
    print(f"   {'✅' if result['success'] else '❌'} 任务列表: {result['success']}")
    
    # 6. 测试传统爬虫配置API（已废弃，但仍需测试兼容性）
    print("6. 测试传统爬虫配置API（已废弃）...")
    result = test_api_endpoint("GET", f"{base_url}/api/v1/crawler/config")
    test_results.append(("传统爬虫配置（已废弃）", result))
    print(f"   {'⚠️' if result['success'] else '❌'} 传统爬虫配置（已废弃）: {result['success']}")

    # 6a. 测试配置迁移状态API
    print("6a. 测试配置迁移状态API...")
    result = test_api_endpoint("GET", f"{base_url}/api/v1/crawler/migration-status")
    test_results.append(("配置迁移状态", result))
    print(f"   {'✅' if result['success'] else '❌'} 配置迁移状态: {result['success']}")
    
    # 7. 测试爬虫实例配置API
    print("7. 测试爬虫实例配置API...")
    result = test_api_endpoint("GET", f"{base_url}/api/v1/crawler-instance-configs/")
    test_results.append(("爬虫实例配置", result))
    print(f"   {'✅' if result['success'] else '❌'} 爬虫实例配置: {result['success']}")
    
    # 8. 测试爬虫池API
    print("8. 测试爬虫池API...")
    result = test_api_endpoint("GET", f"{base_url}/api/v1/crawler-pools/")
    test_results.append(("爬虫池列表", result))
    print(f"   {'✅' if result['success'] else '❌'} 爬虫池列表: {result['success']}")
    
    # 统计结果
    total_tests = len(test_results)
    successful_tests = len([r for _, r in test_results if r["success"]])
    failed_tests = total_tests - successful_tests
    
    print("\n" + "="*50)
    print("📊 现有API测试报告")
    print("="*50)
    print(f"总测试数: {total_tests}")
    print(f"成功: {successful_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    if failed_tests > 0:
        print("\n❌ 失败的测试:")
        for test_name, result in test_results:
            if not result["success"]:
                print(f"  - {test_name}: {result.get('error', 'Unknown error')}")
    
    # 检查新架构API是否可用
    print("\n🔍 检查新架构API...")
    new_api_tests = [
        ("爬取配置API", f"{base_url}/api/v1/crawler-configs/"),
        ("后端配置API", f"{base_url}/api/v1/backend-configs/"),
        ("Worker API", f"{base_url}/api/v1/crawler-workers/"),
        ("任务分配API", f"{base_url}/api/v1/task-assignments/")
    ]
    
    new_api_available = 0
    for test_name, url in new_api_tests:
        result = test_api_endpoint("GET", url)
        if result["success"]:
            new_api_available += 1
            print(f"   ✅ {test_name}: 可用")
        else:
            print(f"   ❌ {test_name}: 不可用 ({result.get('error', 'Unknown error')})")
    
    print(f"\n📈 新架构API可用性: {new_api_available}/{len(new_api_tests)} ({new_api_available/len(new_api_tests)*100:.1f}%)")
    
    if successful_tests == total_tests:
        print("\n🎉 所有现有API测试通过！后端服务基本功能正常。")
        if new_api_available == len(new_api_tests):
            print("🚀 新架构API也已可用！")
            return 0
        else:
            print("⚠️ 新架构API部分不可用，需要完成构建和重启。")
            return 1
    else:
        print("\n❌ 部分现有API测试失败，后端服务可能存在问题。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
