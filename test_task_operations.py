#!/usr/bin/env python3
"""
测试任务操作功能
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_task_operations():
    """测试任务操作功能"""
    
    async with aiohttp.ClientSession() as session:
        print("🔧 开始测试任务操作功能...")
        
        # 1. 获取任务列表
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        task_status = tasks[0]['status']
                        is_running = tasks[0].get('is_running', False)
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                        print(f"   当前状态: {task_status}, 运行中: {is_running}")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 测试启动任务
        print(f"\n2. 测试启动任务 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/start") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 启动任务成功: {data.get('message')}")
                else:
                    error_data = await response.json()
                    print(f"❌ 启动任务失败: {response.status} - {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 启动任务异常: {e}")
        
        # 3. 测试暂停任务
        print(f"\n3. 测试暂停任务 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/pause") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 暂停任务成功: {data.get('message')}")
                else:
                    error_data = await response.json()
                    print(f"❌ 暂停任务失败: {response.status} - {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 暂停任务异常: {e}")
        
        # 4. 测试删除任务（先创建一个测试任务）
        print(f"\n4. 创建测试任务用于删除测试...")
        test_task_data = {
            "name": "删除测试任务",
            "description": "用于测试删除功能的任务",
            "schedule": {
                "type": "daily",
                "time": "10:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "taobao",
                "priority": "medium",
                "retry_count": 3,
                "timeout": 30,
                "batch_size": 10
            },
            "urls": [
                "https://example.com/delete-test-1",
                "https://example.com/delete-test-2"
            ]
        }
        
        test_task_id = None
        try:
            async with session.post(
                f"{API_BASE_URL}/api/v1/monitoring-tasks/",
                json=test_task_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    test_task_id = data['data']['id']
                    print(f"✅ 创建测试任务成功: {test_task_id}")
                else:
                    error_data = await response.json()
                    print(f"❌ 创建测试任务失败: {response.status} - {error_data}")
        except Exception as e:
            print(f"❌ 创建测试任务异常: {e}")
        
        # 5. 测试删除任务
        if test_task_id:
            print(f"\n5. 测试删除任务 (ID: {test_task_id})...")
            try:
                async with session.delete(f"{API_BASE_URL}/api/v1/monitoring-tasks/{test_task_id}") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 删除任务成功: {data.get('message')}")
                    else:
                        error_data = await response.json()
                        print(f"❌ 删除任务失败: {response.status} - {error_data.get('detail')}")
            except Exception as e:
                print(f"❌ 删除任务异常: {e}")
            
            # 验证任务是否真的被删除
            print(f"\n6. 验证任务删除结果...")
            try:
                async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{test_task_id}") as response:
                    if response.status == 404:
                        print(f"✅ 任务删除验证成功: 任务不存在")
                    else:
                        print(f"❌ 任务删除验证失败: 任务仍然存在 (状态: {response.status})")
            except Exception as e:
                print(f"❌ 任务删除验证异常: {e}")
        
        # 7. 测试获取任务列表（验证操作后状态）
        print(f"\n7. 验证操作后的任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    print(f"✅ 当前任务总数: {len(tasks)}")
                    
                    # 查找原始任务的状态
                    original_task = next((t for t in tasks if t['id'] == task_id), None)
                    if original_task:
                        print(f"   原始任务状态: {original_task['status']}, 运行中: {original_task.get('is_running', False)}")
                    else:
                        print(f"   原始任务未找到")
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
        
        print("\n🎉 任务操作功能测试完成!")
        print("\n📋 测试总结:")
        print("   ✅ 启动任务API - 已测试")
        print("   ✅ 暂停任务API - 已测试")
        print("   ✅ 删除任务API - 已测试")
        print("   ✅ 任务状态验证 - 已测试")

if __name__ == "__main__":
    asyncio.run(test_task_operations())
