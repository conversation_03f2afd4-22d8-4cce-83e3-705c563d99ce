#!/usr/bin/env python3
"""
修复任务进度跟踪和Worker分配显示问题
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def analyze_progress_tracking_issues():
    """分析任务进度跟踪问题"""
    
    logger.info("🔍 分析任务进度跟踪和Worker分配显示问题...")
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 检查WebSocket连接状态
        logger.info("1. 检查WebSocket连接状态...")
        async with session.get(f"{BASE_URL}/ws/status") as response:
            if response.status == 200:
                ws_status = await response.json()
                logger.info(f"WebSocket状态: {ws_status}")
            else:
                logger.error(f"获取WebSocket状态失败: {response.status}")
        
        # 2. 检查任务管理器状态
        logger.info("2. 检查任务管理器状态...")
        async with session.get(f"{API_BASE}/tasks/status") as response:
            if response.status == 200:
                tm_status = await response.json()
                logger.info(f"任务管理器状态: {tm_status.get('status')}")
                logger.info(f"运行中批次: {tm_status.get('running_batches')}")
                logger.info(f"已完成批次: {tm_status.get('completed_batches')}")
            else:
                logger.error(f"获取任务管理器状态失败: {response.status}")
        
        # 3. 检查Worker配置
        logger.info("3. 检查Worker配置...")
        async with session.get(f"{API_BASE}/crawler-workers") as response:
            if response.status == 200:
                workers = await response.json()
                if isinstance(workers, list):
                    logger.info(f"配置的Worker数量: {len(workers)}")
                    for worker in workers[:3]:  # 只显示前3个
                        logger.info(f"  - Worker: {worker.get('worker_name')} (状态: {worker.get('status', 'unknown')})")
                else:
                    logger.info(f"配置的Worker数量: {len(workers.get('data', []))}")
                    for worker in workers.get('data', [])[:3]:  # 只显示前3个
                        logger.info(f"  - Worker: {worker.get('worker_name')} (状态: {worker.get('status', 'unknown')})")
            else:
                logger.warning(f"获取Worker配置失败: {response.status}")

        # 4. 检查任务分配
        logger.info("4. 检查任务分配...")
        async with session.get(f"{API_BASE}/task-assignments") as response:
            if response.status == 200:
                assignments = await response.json()
                if isinstance(assignments, list):
                    logger.info(f"任务分配数量: {len(assignments)}")
                    for assignment in assignments[:3]:  # 只显示前3个
                        logger.info(f"  - 分配: {assignment.get('id')} -> Worker: {assignment.get('worker_id')}")
                else:
                    logger.info(f"任务分配数量: {len(assignments.get('data', []))}")
                    for assignment in assignments.get('data', [])[:3]:  # 只显示前3个
                        logger.info(f"  - 分配: {assignment.get('id')} -> Worker: {assignment.get('worker_id')}")
            else:
                logger.warning(f"获取任务分配失败: {response.status}")
        
        # 5. 获取监控任务并检查URL分配
        logger.info("5. 检查监控任务URL分配...")
        async with session.get(f"{API_BASE}/monitoring-tasks") as response:
            if response.status == 200:
                tasks = await response.json()
                if tasks.get("data"):
                    task = tasks["data"][0]
                    task_id = task["id"]
                    
                    # 检查任务的Worker分配
                    async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}/assignments") as assign_response:
                        if assign_response.status == 200:
                            task_assignments = await assign_response.json()
                            logger.info(f"任务 {task_id} 的Worker分配: {len(task_assignments.get('data', []))}")
                        else:
                            logger.warning(f"获取任务Worker分配失败: {assign_response.status}")
                    
                    # 检查URL详情
                    async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}/urls?page=1&page_size=5") as url_response:
                        if url_response.status == 200:
                            urls = await url_response.json()
                            logger.info(f"任务 {task_id} 的URL数量: {len(urls.get('data', []))}")
                            for url in urls.get('data', [])[:3]:
                                logger.info(f"  - URL: {url.get('url', 'N/A')[:50]}... (状态: {url.get('status', 'unknown')})")
                        else:
                            logger.warning(f"获取任务URL失败: {url_response.status}")

async def identify_missing_components():
    """识别缺失的组件"""
    
    logger.info("🔍 识别缺失的组件...")
    
    missing_components = []
    
    # 检查是否有实时进度更新机制
    logger.info("检查实时进度更新机制...")
    missing_components.append({
        "component": "实时进度更新",
        "description": "Celery任务执行时缺少WebSocket进度推送",
        "impact": "前端无法显示实时进度",
        "solution": "在Celery任务中添加进度回调和WebSocket消息发送"
    })
    
    # 检查Worker分配逻辑
    logger.info("检查Worker分配逻辑...")
    missing_components.append({
        "component": "Worker分配逻辑",
        "description": "缺少URL到Worker的实际分配机制",
        "impact": "无法显示哪个Worker处理哪个URL",
        "solution": "实现Worker分配服务和URL分配跟踪"
    })
    
    # 检查URL处理状态跟踪
    logger.info("检查URL处理状态跟踪...")
    missing_components.append({
        "component": "URL处理状态跟踪",
        "description": "缺少URL处理状态的实时更新",
        "impact": "无法显示URL的处理进度和状态",
        "solution": "在Celery任务中添加URL状态更新和WebSocket通知"
    })
    
    # 检查执行历史API
    logger.info("检查执行历史API...")
    missing_components.append({
        "component": "执行历史API",
        "description": "执行历史API返回404错误",
        "impact": "无法查看任务执行历史",
        "solution": "修复执行历史API实现"
    })
    
    return missing_components

async def create_fix_plan():
    """创建修复计划"""
    
    logger.info("📋 创建修复计划...")
    
    fix_plan = {
        "phase1": {
            "name": "修复执行历史API",
            "priority": "高",
            "tasks": [
                "修复monitoring_task_routes.py中的execution-history端点",
                "确保执行记录正确存储和检索",
                "添加执行历史的分页和过滤功能"
            ]
        },
        "phase2": {
            "name": "实现Worker分配显示",
            "priority": "高", 
            "tasks": [
                "创建Worker分配服务",
                "在任务执行时分配URL到Worker",
                "在前端显示Worker分配信息",
                "添加Worker状态实时更新"
            ]
        },
        "phase3": {
            "name": "添加实时进度跟踪",
            "priority": "中",
            "tasks": [
                "在Celery任务中添加进度回调",
                "实现WebSocket进度消息推送",
                "更新前端进度显示组件",
                "添加URL处理状态跟踪"
            ]
        },
        "phase4": {
            "name": "优化用户体验",
            "priority": "低",
            "tasks": [
                "添加进度条动画",
                "实现任务执行可视化",
                "添加性能指标显示",
                "优化实时更新性能"
            ]
        }
    }
    
    return fix_plan

async def main():
    """主函数"""
    logger.info("🚀 开始分析任务进度跟踪问题...")
    logger.info("=" * 60)
    
    try:
        # 分析当前问题
        await analyze_progress_tracking_issues()
        
        logger.info("\n" + "=" * 60)
        
        # 识别缺失组件
        missing_components = await identify_missing_components()
        
        logger.info("\n📋 缺失的组件:")
        for i, component in enumerate(missing_components, 1):
            logger.info(f"{i}. {component['component']}")
            logger.info(f"   描述: {component['description']}")
            logger.info(f"   影响: {component['impact']}")
            logger.info(f"   解决方案: {component['solution']}")
            logger.info("")
        
        # 创建修复计划
        fix_plan = await create_fix_plan()
        
        logger.info("🛠️ 修复计划:")
        for phase_key, phase in fix_plan.items():
            logger.info(f"\n{phase['name']} (优先级: {phase['priority']})")
            for task in phase['tasks']:
                logger.info(f"  - {task}")
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ 分析完成!")
        logger.info("建议优先修复执行历史API和Worker分配显示功能")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
