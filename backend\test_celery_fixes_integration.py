#!/usr/bin/env python3
"""
集成测试：验证Celery Worker修复效果
通过创建真实的监控任务来测试修复
"""

import asyncio
import json
import uuid
import time
from datetime import datetime
import requests

# 测试配置
BACKEND_URL = "http://localhost:8000"
TEST_URLS = [
    "https://articulo.mercadolibre.com.mx/MLM-3195229646-test-product-1",
    "https://articulo.mercadolibre.com.mx/MLM-3195229647-test-product-2"
]


def create_test_monitoring_task():
    """创建测试监控任务"""
    print("🔍 创建测试监控任务...")
    
    task_data = {
        "name": f"测试任务_{int(time.time())}",
        "description": "用于测试Celery Worker修复的任务",
        "platform": "mercadolibre",
        "schedule": {
            "type": "manual",
            "enabled": False
        },
        "config": {
            "options": {
                "crawler_backend": {
                    "name": "test_worker",
                    "base_url": "http://localhost:11235",
                    "timeout": 30,
                    "max_retries": 3,
                    "auth_type": "none",
                    "api_key": None
                }
            }
        },
        "tags": ["test", "celery_fix"]
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/v1/monitoring-tasks",
            json=task_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            task = response.json()
            task_id = task["id"]
            print(f"✅ 监控任务创建成功: {task_id}")
            return task_id
        else:
            print(f"❌ 监控任务创建失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 创建监控任务时出错: {e}")
        return None


def add_urls_to_task(task_id):
    """向任务添加URL"""
    print(f"🔍 向任务 {task_id} 添加URL...")
    
    url_data = [
        {
            "url": url,
            "enabled": True,
            "tags": ["test"]
        }
        for url in TEST_URLS
    ]
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/v1/monitoring-tasks/{task_id}/urls",
            json=url_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print(f"✅ URL添加成功: {len(TEST_URLS)} 个URL")
            return True
        else:
            print(f"❌ URL添加失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 添加URL时出错: {e}")
        return False


def execute_monitoring_task(task_id):
    """执行监控任务"""
    print(f"🔍 执行监控任务: {task_id}")
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/v1/monitoring-tasks/{task_id}/execute",
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            celery_task_id = result.get("celery_task_id")
            print(f"✅ 监控任务执行成功，Celery任务ID: {celery_task_id}")
            return celery_task_id
        else:
            print(f"❌ 监控任务执行失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 执行监控任务时出错: {e}")
        return None


def wait_for_crawler_requests(timeout=30):
    """等待爬虫请求记录生成"""
    print(f"🔍 等待爬虫请求记录生成（最多等待{timeout}秒）...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{BACKEND_URL}/api/v1/crawler-debug/requests?limit=10")
            
            if response.status_code == 200:
                requests_data = response.json()
                if requests_data and len(requests_data) > 0:
                    print(f"✅ 找到 {len(requests_data)} 个爬虫请求记录")
                    return requests_data
            
            time.sleep(2)
            print("⏳ 等待中...")
            
        except Exception as e:
            print(f"❌ 检查爬虫请求记录时出错: {e}")
            time.sleep(2)
    
    print("❌ 超时：未找到爬虫请求记录")
    return []


def analyze_crawler_request(request_data):
    """分析爬虫请求记录"""
    print("\n🔍 分析爬虫请求记录...")
    
    issues = []
    
    # 检查taskId格式
    task_id = request_data.get("taskId", "")
    try:
        # 尝试解析为UUID
        uuid.UUID(task_id)
        print(f"✅ taskId格式正确（UUID）: {task_id}")
    except ValueError:
        if task_id.isdigit():
            issues.append(f"❌ taskId仍然是数字格式: {task_id}")
        else:
            issues.append(f"❌ taskId格式不正确: {task_id}")
    
    # 检查workerId
    worker_id = request_data.get("workerId", "")
    if worker_id == "unknown":
        issues.append("❌ workerId仍然是'unknown'")
    else:
        print(f"✅ workerId已正确获取: {worker_id}")
    
    # 检查payload格式
    payload = request_data.get("payload", {})
    
    # 检查是否是新的标准API格式
    if "urls" in payload and "browser_config" in payload and "crawler_config" in payload:
        print("✅ 载荷格式符合标准API格式")
        
        # 检查具体字段
        urls = payload.get("urls", [])
        browser_config = payload.get("browser_config", {})
        crawler_config = payload.get("crawler_config", {})
        
        print(f"  - URLs: {len(urls)} 个")
        print(f"  - Browser配置: {len(browser_config)} 个字段")
        print(f"  - Crawler配置: {len(crawler_config)} 个字段")
        
        # 检查关键配置
        if "llm_extraction" in crawler_config:
            print("  ✅ 包含LLM提取配置")
        else:
            issues.append("  ❌ 缺少LLM提取配置")
            
        if "schema_extraction" in crawler_config:
            print("  ✅ 包含Schema提取配置")
        else:
            issues.append("  ❌ 缺少Schema提取配置")
            
    else:
        issues.append("❌ 载荷格式仍然是旧格式")
        print(f"  当前载荷字段: {list(payload.keys())}")
    
    # 检查端点信息
    endpoint = request_data.get("endpoint")
    if endpoint and endpoint != "unknown":
        print(f"✅ 包含端点信息: {endpoint}")
    else:
        issues.append("❌ 缺少端点信息")
    
    return issues


def cleanup_test_task(task_id):
    """清理测试任务"""
    print(f"🔍 清理测试任务: {task_id}")
    
    try:
        response = requests.delete(f"{BACKEND_URL}/api/v1/monitoring-tasks/{task_id}")
        
        if response.status_code == 200:
            print("✅ 测试任务清理成功")
        else:
            print(f"⚠️  测试任务清理失败: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️  清理测试任务时出错: {e}")


def main():
    """主测试函数"""
    print("🚀 开始集成测试：验证Celery Worker修复效果\n")
    
    task_id = None
    try:
        # 1. 创建测试监控任务
        task_id = create_test_monitoring_task()
        if not task_id:
            return False
        
        # 2. 添加URL
        if not add_urls_to_task(task_id):
            return False
        
        # 3. 执行监控任务
        celery_task_id = execute_monitoring_task(task_id)
        if not celery_task_id:
            return False
        
        # 4. 等待爬虫请求记录
        crawler_requests = wait_for_crawler_requests(timeout=60)
        if not crawler_requests:
            return False
        
        # 5. 分析最新的爬虫请求记录
        latest_request = crawler_requests[0]
        print(f"\n📋 最新爬虫请求记录:")
        print(json.dumps(latest_request, indent=2, ensure_ascii=False))
        
        issues = analyze_crawler_request(latest_request)
        
        # 6. 汇总结果
        print("\n" + "="*50)
        print("📊 测试结果汇总:")
        print("="*50)
        
        if issues:
            print("❌ 发现以下问题:")
            for issue in issues:
                print(f"  {issue}")
            return False
        else:
            print("🎉 所有检查通过！Celery Worker修复成功！")
            return True
    
    finally:
        # 清理测试任务
        if task_id:
            cleanup_test_task(task_id)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
