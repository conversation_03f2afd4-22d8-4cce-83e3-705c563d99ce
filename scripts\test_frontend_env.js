#!/usr/bin/env node
/**
 * 测试前端环境变量配置
 */

const axios = require('axios');

async function testFrontendEnvironment() {
    console.log('🧪 测试前端环境变量配置...');
    console.log('=' .repeat(50));
    
    try {
        // 测试前端页面是否可访问
        const frontendResponse = await axios.get('http://localhost:3000', { timeout: 5000 });
        console.log('✅ 前端服务可访问');
        
        // 检查页面内容中是否包含正确的配置
        const pageContent = frontendResponse.data;
        
        // 查找环境变量的痕迹
        if (pageContent.includes('8001')) {
            console.log('⚠️  页面内容中仍包含8001端口引用');
        } else {
            console.log('✅ 页面内容中没有8001端口引用');
        }
        
        // 测试后端8000端口
        try {
            const backendResponse = await axios.get('http://localhost:8000/health', { timeout: 5000 });
            console.log('✅ 后端8000端口可访问');
            console.log(`   状态: ${backendResponse.data.status}`);
        } catch (error) {
            console.log('❌ 后端8000端口不可访问:', error.message);
        }
        
        // 测试8001端口是否已停止
        try {
            await axios.get('http://localhost:8001/health', { timeout: 2000 });
            console.log('⚠️  8001端口仍在运行，这可能导致混乱');
        } catch (error) {
            console.log('✅ 8001端口已停止');
        }
        
        console.log('\n📋 建议操作:');
        console.log('1. 清理浏览器缓存 (Ctrl+F5)');
        console.log('2. 打开开发者工具检查Network标签');
        console.log('3. 查看Console是否还有8001连接错误');
        
        return true;
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
        return false;
    }
}

// 运行测试
testFrontendEnvironment().then(success => {
    console.log('\n' + '='.repeat(50));
    if (success) {
        console.log('🎉 环境配置测试完成!');
        console.log('💡 如果浏览器仍显示8001错误，请清理缓存');
    } else {
        console.log('❌ 环境配置测试失败!');
    }
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
});
