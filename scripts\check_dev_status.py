#!/usr/bin/env python3
"""
检查MonIt混合开发环境状态
"""
import requests
import subprocess
import sys
import socket
from typing import Dict, Tuple

def check_port(host: str, port: int, timeout: int = 5) -> bool:
    """检查端口是否可连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def check_database_connection() -> bool:
    """检查TimescaleDB连接"""
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="monit_crawler_dev",
            user="crawler_user",
            password="crawler_password",
            connect_timeout=5
        )
        conn.close()
        return True
    except Exception:
        # 如果psycopg2不可用，至少检查端口
        return check_port("localhost", 5432)

def check_redis_connection() -> bool:
    """检查Redis连接"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        r.ping()
        return True
    except Exception:
        # 如果redis包不可用，至少检查端口
        return check_port("localhost", 6379)

def check_url(url: str, name: str) -> Tuple[bool, str]:
    """检查URL是否可访问"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            return True, f"✅ {name}: 运行中"
        else:
            return False, f"❌ {name}: HTTP {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, f"❌ {name}: 连接失败"
    except requests.exceptions.Timeout:
        return False, f"❌ {name}: 超时"
    except Exception as e:
        return False, f"❌ {name}: {str(e)}"

def check_docker_services() -> Dict[str, bool]:
    """检查Docker服务状态"""
    try:
        result = subprocess.run(['docker', 'ps', '--format', '{{.Names}}'], 
                              capture_output=True, text=True, check=True)
        running_containers = result.stdout.strip().split('\n')
        
        required_services = ['monit-timescaledb', 'monit-redis']
        status = {}
        
        for service in required_services:
            status[service] = service in running_containers
            
        return status
    except subprocess.CalledProcessError:
        return {}
    except FileNotFoundError:
        return {}

def main():
    print("=" * 50)
    print("    MonIt 混合开发环境状态检查")
    print("=" * 50)
    print()
    
    # 检查Docker服务
    print("🐳 Docker服务:")
    docker_status = check_docker_services()
    
    if not docker_status:
        print("❌ Docker未运行或无法访问")
    else:
        for service, running in docker_status.items():
            status = "✅ 运行中" if running else "❌ 未运行"
            print(f"  {service}: {status}")
    
    print()
    
    # 检查本地服务
    print("💻 本地服务:")
    
    services = [
        ("http://localhost:3000", "前端 (React)"),
        ("http://localhost:8001/health", "后端 (FastAPI)"),
    ]
    
    all_local_ok = True
    for url, name in services:
        ok, message = check_url(url, name)
        print(f"  {message}")
        if not ok:
            all_local_ok = False
    
    print()
    
    # 检查基础设施服务
    print("🔧 基础设施服务:")

    # 检查数据库连接
    db_ok = check_database_connection()
    redis_ok = check_redis_connection()

    print(f"  {'✅' if db_ok else '❌'} TimescaleDB: {'连接正常' if db_ok else '连接失败'}")
    print(f"  {'✅' if redis_ok else '❌'} Redis: {'连接正常' if redis_ok else '连接失败'}")

    # 检查HTTP服务
    http_services = [
        ("http://localhost:3001", "Grafana"),
        ("http://localhost:5601", "Kibana"),
        ("http://localhost:9090", "Prometheus"),
    ]

    for url, name in http_services:
        ok, message = check_url(url, name)
        print(f"  {message}")
    
    print()
    print("=" * 50)
    
    # 总结
    docker_ok = all(docker_status.values()) if docker_status else False
    
    if docker_ok and all_local_ok:
        print("🎉 所有核心服务运行正常！")
        print()
        print("📱 访问地址:")
        print("  🌐 前端应用: http://localhost:3000")
        print("  🔧 后端API:  http://localhost:8001")
        print("  📊 监控面板: http://localhost:3001")
        return 0
    else:
        print("⚠️  部分服务未运行，请检查配置")
        if not docker_ok:
            print("  💡 启动Docker服务: docker-compose -f docker-compose.dev.yml up -d")
        if not all_local_ok:
            print("  💡 启动本地服务: 运行 scripts/start_dev_hybrid.bat")
        return 1

if __name__ == "__main__":
    sys.exit(main())
