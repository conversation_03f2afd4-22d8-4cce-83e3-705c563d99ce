"""
简化的监控功能测试
验证监控系统的核心功能
"""

import pytest
import asyncio
import time
from app.services.crawler_request_monitor import CrawlerRequestMonitor, RequestBuildMetrics


class TestMonitoringSimple:
    """简化的监控测试"""
    
    @pytest.mark.asyncio
    async def test_basic_monitoring_functionality(self):
        """测试基本监控功能"""
        
        # 创建监控器
        monitor = CrawlerRequestMonitor()
        
        # 测试基本功能
        task_id = "simple-test-task"
        execution_id = "simple-exec-123"
        
        # 测试数据流日志
        await monitor.log_data_flow_step(
            step_name="测试步骤",
            task_id=task_id,
            data=["test_data_1", "test_data_2"],
            duration=1.5
        )
        
        # 测试请求结构日志
        test_request = {
            "request": {
                "urls": ["https://test.com/1"],
                "q": "测试查询",
                "priority": "high"
            },
            "auth_context": {
                "user_id": "test_user",
                "auth_method": "api_key"
            }
        }
        
        await monitor.log_request_structure(test_request, task_id)
        
        # 测试错误建议
        suggestions = monitor._get_error_suggestions("ValueError", "Task has no active URLs")
        assert len(suggestions) > 0
        assert any("URL池" in suggestion for suggestion in suggestions)
        
        # 测试指标对象
        metrics = RequestBuildMetrics(
            task_id=task_id,
            execution_id=execution_id,
            start_time=time.time()
        )
        
        metrics.urls_count = 5
        metrics.url_fetch_time = 1.2
        metrics.config_fetch_time = 0.8

        # 等待一小段时间确保有持续时间
        await asyncio.sleep(0.01)
        metrics.mark_completed("success")

        assert metrics.status == "success"
        assert metrics.duration is not None
        assert metrics.duration >= 0  # 允许0或正数
        
        await monitor.close()
        
        print("✅ 基本监控功能测试通过")
    
    @pytest.mark.asyncio
    async def test_performance_threshold_warnings(self):
        """测试性能阈值警告"""
        
        monitor = CrawlerRequestMonitor()
        
        # 创建超过阈值的指标
        metrics = RequestBuildMetrics(
            task_id="threshold-test",
            execution_id="threshold-exec",
            start_time=time.time()
        )
        
        # 设置超过阈值的值
        metrics.url_fetch_time = 3.0  # 超过2.0秒阈值
        metrics.config_fetch_time = 1.5  # 超过1.0秒阈值
        metrics.duration = 6.0  # 超过5.0秒阈值
        metrics.urls_count = 150  # 超过100个阈值
        
        # 测试阈值检查（这会产生警告日志）
        await monitor._check_performance_thresholds(metrics)
        
        await monitor.close()
        
        print("✅ 性能阈值警告测试通过")
    
    @pytest.mark.asyncio
    async def test_error_suggestions_comprehensive(self):
        """测试错误建议的全面性"""
        
        monitor = CrawlerRequestMonitor()
        
        # 测试各种错误类型的建议
        test_cases = [
            {
                "error_type": "ValueError",
                "error_message": "Task has no active URLs",
                "expected_keywords": ["URL池", "监控任务", "启用"]
            },
            {
                "error_type": "ValueError", 
                "error_message": "Task has no crawl config",
                "expected_keywords": ["监控任务", "爬取配置", "config_id"]
            },
            {
                "error_type": "ConnectionError",
                "error_message": "Redis connection failed",
                "expected_keywords": ["Redis连接", "网络连接", "服务依赖"]
            },
            {
                "error_type": "TimeoutError",
                "error_message": "Request timeout",
                "expected_keywords": ["超时时间", "系统负载", "数据库查询"]
            }
        ]
        
        for case in test_cases:
            suggestions = monitor._get_error_suggestions(
                case["error_type"], 
                case["error_message"]
            )
            
            # 验证建议不为空
            assert len(suggestions) > 0, f"No suggestions for {case['error_type']}: {case['error_message']}"
            
            # 验证建议包含预期关键词
            suggestions_text = " ".join(suggestions)
            for keyword in case["expected_keywords"]:
                assert any(keyword in suggestion for suggestion in suggestions), \
                    f"Keyword '{keyword}' not found in suggestions for {case['error_type']}"
        
        await monitor.close()
        
        print("✅ 错误建议全面性测试通过")
    
    @pytest.mark.asyncio
    async def test_metrics_data_structure(self):
        """测试指标数据结构"""
        
        # 创建指标对象
        metrics = RequestBuildMetrics(
            task_id="structure-test-task",
            execution_id="structure-exec-123",
            start_time=time.time()
        )
        
        # 设置各种指标
        metrics.urls_count = 25
        metrics.url_fetch_time = 1.5
        metrics.config_fetch_time = 0.8
        metrics.system_config_time = 0.2
        metrics.request_build_time = 0.5
        metrics.active_urls_ratio = 0.85
        metrics.config_completeness = 0.9
        metrics.config_source = "任务配置"
        
        # 标记完成
        time.sleep(0.1)
        metrics.mark_completed("success")
        
        # 转换为字典
        metrics_dict = metrics.to_dict()
        
        # 验证数据结构
        required_fields = [
            "task_id", "execution_id", "start_time", "end_time", "duration",
            "status", "urls_count", "config_source", "error_message",
            "url_fetch_time", "config_fetch_time", "system_config_time",
            "request_build_time", "active_urls_ratio", "config_completeness"
        ]
        
        for field in required_fields:
            assert field in metrics_dict, f"Missing field: {field}"
        
        # 验证数据类型
        assert isinstance(metrics_dict["task_id"], str)
        assert isinstance(metrics_dict["execution_id"], str)
        assert isinstance(metrics_dict["start_time"], (int, float))
        assert isinstance(metrics_dict["end_time"], (int, float))
        assert isinstance(metrics_dict["duration"], (int, float))
        assert isinstance(metrics_dict["status"], str)
        assert isinstance(metrics_dict["urls_count"], int)
        
        # 验证数值范围
        assert metrics_dict["duration"] > 0
        assert metrics_dict["urls_count"] >= 0
        assert 0 <= metrics_dict["active_urls_ratio"] <= 1
        assert 0 <= metrics_dict["config_completeness"] <= 1
        
        print("✅ 指标数据结构测试通过")
    
    @pytest.mark.asyncio
    async def test_monitoring_report_generation(self):
        """测试监控报告生成"""
        
        monitor = CrawlerRequestMonitor()
        
        # 模拟任务指标数据
        from unittest.mock import patch
        
        mock_metrics = [
            {
                "task_id": "report-test-task",
                "execution_id": "exec-1",
                "start_time": time.time() - 3600,
                "duration": 2.5,
                "status": "success",
                "urls_count": 15
            },
            {
                "task_id": "report-test-task",
                "execution_id": "exec-2", 
                "start_time": time.time() - 1800,
                "duration": 3.2,
                "status": "success",
                "urls_count": 20
            },
            {
                "task_id": "report-test-task",
                "execution_id": "exec-3",
                "start_time": time.time() - 900,
                "duration": None,  # 失败的执行
                "status": "validation_error",
                "urls_count": 0,
                "error_message": "Task has no active URLs"
            }
        ]
        
        with patch.object(monitor, 'get_task_metrics') as mock_get_metrics:
            mock_get_metrics.return_value = mock_metrics
            
            # 生成报告
            report = await monitor.generate_monitoring_report("report-test-task")
            
            # 验证报告内容
            assert "监控报告" in report
            assert "report-test-task" in report
            assert "总执行次数: 3" in report
            assert "成功次数: 2" in report
            assert "成功率: 66.7%" in report
            
            # 验证性能统计
            assert "平均耗时" in report
            assert "最长耗时" in report
            assert "最短耗时" in report
            
            # 验证最近执行信息
            assert "最近执行" in report
            assert "状态: success" in report
        
        await monitor.close()
        
        print("✅ 监控报告生成测试通过")


if __name__ == "__main__":
    pytest.main([__file__])
