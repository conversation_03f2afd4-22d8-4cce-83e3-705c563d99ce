#!/usr/bin/env python3
"""
测试任务执行相关的API接口
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_execution_apis():
    """测试任务执行相关的API接口"""
    
    async with aiohttp.ClientSession() as session:
        print("⚡ 开始测试任务执行相关API接口...")
        
        # 1. 获取任务列表，找到一个任务
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        is_running = tasks[0].get('is_running', False)
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                        print(f"   当前状态: {'运行中' if is_running else '未运行'}")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 测试获取任务执行状态
        print(f"\n2. 测试获取任务执行状态 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execution-status") as response:
                if response.status == 200:
                    data = await response.json()
                    status_info = data.get('data', {})
                    print(f"✅ 获取执行状态成功:")
                    print(f"   任务ID: {status_info.get('task_id')}")
                    print(f"   是否运行中: {status_info.get('is_running')}")
                    print(f"   任务状态: {status_info.get('status')}")
                    print(f"   上次执行: {status_info.get('last_run') or '未执行'}")
                    print(f"   下次执行: {status_info.get('next_run') or '未安排'}")
                    
                    current_execution = status_info.get('current_execution')
                    if current_execution:
                        print(f"   当前执行: {current_execution.get('id')}")
                    
                    last_execution = status_info.get('last_execution')
                    if last_execution:
                        print(f"   最后执行: {last_execution.get('id')} ({last_execution.get('status')})")
                else:
                    print(f"❌ 获取执行状态失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 获取执行状态异常: {e}")
        
        # 3. 测试手动执行任务
        print(f"\n3. 测试手动执行任务 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execute") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 手动执行成功:")
                    print(f"   执行ID: {data.get('execution_id')}")
                    print(f"   消息: {data.get('message')}")
                    print(f"   URL数量: {data.get('url_count')}")
                    
                    execution_id = data.get('execution_id')
                    
                    # 等待一下，然后检查执行状态
                    print("   等待2秒后检查执行状态...")
                    await asyncio.sleep(2)
                    
                    async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execution-status") as status_response:
                        if status_response.status == 200:
                            status_data = await status_response.json()
                            status_info = status_data.get('data', {})
                            print(f"   执行后状态: {'运行中' if status_info.get('is_running') else '未运行'}")
                        
                else:
                    print(f"❌ 手动执行失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
                    execution_id = None
        except Exception as e:
            print(f"❌ 手动执行异常: {e}")
            execution_id = None
        
        # 4. 测试终止任务执行（如果任务正在运行）
        print(f"\n4. 测试终止任务执行 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/terminate") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 终止执行成功:")
                    print(f"   消息: {data.get('message')}")
                    
                    # 等待一下，然后检查执行状态
                    print("   等待1秒后检查执行状态...")
                    await asyncio.sleep(1)
                    
                    async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execution-status") as status_response:
                        if status_response.status == 200:
                            status_data = await status_response.json()
                            status_info = status_data.get('data', {})
                            print(f"   终止后状态: {'运行中' if status_info.get('is_running') else '未运行'}")
                        
                else:
                    print(f"❌ 终止执行失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 终止执行异常: {e}")
        
        # 5. 测试获取执行历史
        print(f"\n5. 测试获取执行历史 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/history") as response:
                if response.status == 200:
                    data = await response.json()
                    history = data.get('data', [])
                    total = data.get('total', 0)
                    print(f"✅ 获取执行历史成功: {total} 条记录")
                    
                    # 显示最近的几条执行记录
                    for i, record in enumerate(history[:3]):
                        print(f"   记录 {i+1}:")
                        print(f"     执行ID: {record.get('id')}")
                        print(f"     状态: {record.get('status')}")
                        print(f"     触发方式: {record.get('trigger')}")
                        print(f"     开始时间: {record.get('start_time')}")
                        print(f"     结束时间: {record.get('end_time') or '未结束'}")
                        print(f"     URL数量: {record.get('url_count')}")
                        print(f"     成功/失败: {record.get('success_count')}/{record.get('error_count')}")
                else:
                    print(f"❌ 获取执行历史失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 获取执行历史异常: {e}")
        
        # 6. 测试重启失败的任务
        print(f"\n6. 测试重启失败的任务 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/restart") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 重启任务成功:")
                    print(f"   执行ID: {data.get('execution_id')}")
                    print(f"   消息: {data.get('message')}")
                    print(f"   URL数量: {data.get('url_count')}")
                else:
                    print(f"❌ 重启任务失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 重启任务异常: {e}")
        
        print("\n🎉 任务执行API接口测试完成!")

if __name__ == "__main__":
    asyncio.run(test_execution_apis())
