#!/usr/bin/env python3
"""
调试任务数据问题
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def debug_task_data():
    """调试任务数据"""
    
    async with aiohttp.ClientSession() as session:
        print("🔍 调试任务数据...")
        
        # 获取任务列表
        print("\n1. 获取任务列表详细信息...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    print(f"任务总数: {len(tasks)}")
                    
                    for i, task in enumerate(tasks):
                        print(f"\n任务 {i+1}:")
                        print(f"  ID: {task.get('id')}")
                        print(f"  名称: {task.get('name')}")
                        print(f"  来源: {task.get('source', 'unknown')}")
                        print(f"  状态: {task.get('status')}")
                        print(f"  创建时间: {task.get('created_at')}")
                        
                        # 尝试获取这个任务的详情
                        task_id = task.get('id')
                        if task_id:
                            print(f"  尝试获取详情...")
                            try:
                                async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as detail_response:
                                    if detail_response.status == 200:
                                        detail_data = await detail_response.json()
                                        print(f"  ✅ 详情获取成功")
                                    else:
                                        print(f"  ❌ 详情获取失败: {detail_response.status}")
                                        # 如果是URL池任务，我们需要创建一个测试任务
                                        if task.get('source') == 'url_pool':
                                            print(f"  这是URL池任务，创建一个内存任务进行测试...")
                                            await create_memory_task(session)
                            except Exception as e:
                                print(f"  ❌ 详情获取异常: {e}")
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")

async def create_memory_task(session):
    """创建一个内存任务用于测试"""
    task_data = {
        "name": "内存测试任务",
        "description": "用于测试任务详情API的内存任务",
        "schedule": {
            "type": "daily",
            "time": "10:00",
            "timezone": "Asia/Shanghai"
        },
        "config": {
            "platform": "taobao",
            "priority": "medium",
            "retry_count": 3,
            "timeout": 30,
            "batch_size": 10
        },
        "urls": [
            "https://example.com/test1",
            "https://example.com/test2",
            "https://example.com/test3"
        ]
    }
    
    try:
        async with session.post(
            f"{API_BASE_URL}/api/v1/monitoring-tasks/",
            json=task_data
        ) as response:
            if response.status == 200:
                data = await response.json()
                task_id = data['data']['id']
                print(f"✅ 创建内存任务成功: {task_id}")
                
                # 测试新创建的任务
                await test_new_task(session, task_id)
                
                return task_id
            else:
                error_data = await response.json()
                print(f"❌ 创建内存任务失败: {response.status} - {error_data}")
                return None
    except Exception as e:
        print(f"❌ 创建内存任务异常: {e}")
        return None

async def test_new_task(session, task_id):
    """测试新创建的任务"""
    print(f"\n🧪 测试新创建的任务: {task_id}")
    
    # 1. 获取任务详情
    try:
        async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 任务详情: {data['data']['name']}")
            else:
                print(f"❌ 任务详情失败: {response.status}")
    except Exception as e:
        print(f"❌ 任务详情异常: {e}")
    
    # 2. 获取任务URL
    try:
        async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 任务URL: {len(data.get('data', []))} 个")
            else:
                print(f"❌ 任务URL失败: {response.status}")
    except Exception as e:
        print(f"❌ 任务URL异常: {e}")
    
    # 3. 获取统计信息
    try:
        async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 统计信息获取成功")
            else:
                print(f"❌ 统计信息失败: {response.status}")
    except Exception as e:
        print(f"❌ 统计信息异常: {e}")
    
    # 4. 手动执行
    try:
        async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execute") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 手动执行成功: {data.get('execution_id')}")
            else:
                error_data = await response.json()
                print(f"❌ 手动执行失败: {response.status} - {error_data.get('detail')}")
    except Exception as e:
        print(f"❌ 手动执行异常: {e}")

if __name__ == "__main__":
    asyncio.run(debug_task_data())
