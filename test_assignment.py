#!/usr/bin/env python3
"""
测试任务分配功能的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def create_task_assignment():
    """创建任务分配"""
    
    # 任务ID和Worker ID
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    worker_id = "f1798516-0d34-4acb-b8dd-2118c15690a6"
    
    # 分配数据
    assignment_data = {
        "task_id": task_id,
        "worker_ids": [worker_id],
        "assignment_name": "测试分配-Worker11235e",
        "description": "通过脚本创建的测试分配",
        "assignment_strategy": "health_based",
        "auto_failover": True,
        "total_urls": 49
    }
    
    print("创建任务分配...")
    print(f"任务ID: {task_id}")
    print(f"Worker ID: {worker_id}")
    print(f"分配数据: {json.dumps(assignment_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/task-assignments/",
            json=assignment_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            assignment = response.json()
            print("✅ 任务分配创建成功!")
            print(f"分配ID: {assignment.get('assignment_id')}")
            return assignment.get('assignment_id')
        else:
            print("❌ 任务分配创建失败!")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def get_task_assignments(task_id):
    """获取任务分配列表"""
    
    print(f"\n获取任务 {task_id} 的分配列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/task-assignments/task/{task_id}/assignments")
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ 获取成功! 找到 {len(assignments)} 个分配")
            
            for i, assignment in enumerate(assignments, 1):
                print(f"\n分配 {i}:")
                print(f"  分配ID: {assignment.get('assignment_id')}")
                print(f"  分配名称: {assignment.get('assignment_name')}")
                print(f"  状态: {assignment.get('status')}")
                print(f"  Worker数量: {assignment.get('worker_count')}")
                print(f"  主要Worker: {assignment.get('primary_worker_name')}")
                print(f"  当前Worker: {assignment.get('current_worker_name')}")
                print(f"  总URL数: {assignment.get('total_urls')}")
                print(f"  已处理: {assignment.get('processed_urls')}")
                print(f"  创建时间: {assignment.get('created_at')}")
            
            return assignments
        else:
            print(f"❌ 获取失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []

def main():
    """主函数"""
    print("🚀 开始测试任务分配功能\n")
    
    # 1. 创建任务分配
    assignment_id = create_task_assignment()
    
    # 2. 获取任务分配列表
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    assignments = get_task_assignments(task_id)
    
    print(f"\n🎉 测试完成!")
    print(f"创建的分配ID: {assignment_id}")
    print(f"查询到的分配数量: {len(assignments)}")

if __name__ == "__main__":
    main()
