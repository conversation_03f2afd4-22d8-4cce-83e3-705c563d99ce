#!/usr/bin/env python3
"""
测试WebSocket连接到Docker backend
"""
import asyncio
import websockets
import json
from datetime import datetime

async def test_websocket():
    uri = "ws://localhost:8000/ws"
    
    try:
        print(f"正在连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 等待欢迎消息
            try:
                welcome_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到欢迎消息: {welcome_message}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到欢迎消息")
            
            # 发送ping消息
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_message))
            print(f"发送ping消息: {ping_message}")
            
            # 等待pong响应
            try:
                pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到pong响应: {pong_response}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到pong响应")
            
            # 发送测试消息
            test_message = {
                "type": "test",
                "data": {"message": "Hello from test client"},
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(test_message))
            print(f"发送测试消息: {test_message}")
            
            # 等待回显
            try:
                echo_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到回显响应: {echo_response}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到回显响应")
                
            print("✅ WebSocket测试完成")
            
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保backend服务正在运行")
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
