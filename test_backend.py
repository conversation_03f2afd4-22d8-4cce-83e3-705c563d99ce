#!/usr/bin/env python3
"""
简单的后端测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__)))

try:
    print("Testing imports...")
    
    # 测试基本导入
    import fastapi
    print(f"✅ FastAPI version: {fastapi.__version__}")
    
    import uvicorn
    print(f"✅ Uvicorn imported successfully")
    
    # 测试配置导入
    from config.settings import settings
    print(f"✅ Settings imported: {settings.APP_NAME}")
    
    # 测试应用导入
    from backend.app.main import app
    print(f"✅ App imported successfully")
    
    print("\n🚀 All imports successful! Starting server...")
    
    # 启动服务器
    uvicorn.run(
        "backend.app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
