import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Task, TaskStatus, PaginationInfo } from '../../types';
import { taskApi } from '../../services/api';

// 异步actions
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (params?: {
    page?: number;
    page_size?: number;
    status?: string;
    priority?: string;
  }) => {
    const response = await taskApi.getTasks(params);
    return response;
  }
);

export const fetchTask = createAsyncThunk(
  'tasks/fetchTask',
  async (id: string) => {
    const response = await taskApi.getTask(id);
    return response;
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (task: Partial<Task>) => {
    const response = await taskApi.createTask(task);
    return response;
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ id, task }: { id: string; task: Partial<Task> }) => {
    const response = await taskApi.updateTask(id, task);
    return response;
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (id: string) => {
    await taskApi.deleteTask(id);
    return id;
  }
);

export const startTask = createAsyncThunk(
  'tasks/startTask',
  async (id: string) => {
    await taskApi.startTask(id);
    return id;
  }
);

export const stopTask = createAsyncThunk(
  'tasks/stopTask',
  async (id: string) => {
    await taskApi.stopTask(id);
    return id;
  }
);

// State类型
interface TasksState {
  tasks: Task[];
  currentTask: Task | null;
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo | null;
  filters: {
    status?: string;
    priority?: string;
    search?: string;
  };
}

// 初始状态
const initialState: TasksState = {
  tasks: [],
  currentTask: null,
  loading: false,
  error: null,
  pagination: null,
  filters: {},
};

// Slice
const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<TasksState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    updateTaskStatus: (state, action: PayloadAction<{ id: string; status: TaskStatus; progress?: number }>) => {
      const task = state.tasks.find(t => t.id === action.payload.id);
      if (task) {
        task.status = action.payload.status;
        if (action.payload.progress !== undefined) {
          task.progress = action.payload.progress;
        }
      }
      if (state.currentTask?.id === action.payload.id) {
        state.currentTask.status = action.payload.status;
        if (action.payload.progress !== undefined) {
          state.currentTask.progress = action.payload.progress;
        }
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // fetchTasks
    builder
      .addCase(fetchTasks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload.data || [];
        state.pagination = action.payload.pagination || null;
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch tasks';
      });

    // fetchTask
    builder
      .addCase(fetchTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTask.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTask = action.payload.data || null;
      })
      .addCase(fetchTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch task';
      });

    // createTask
    builder
      .addCase(createTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTask.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.data) {
          state.tasks.unshift(action.payload.data);
        }
      })
      .addCase(createTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create task';
      });

    // updateTask
    builder
      .addCase(updateTask.fulfilled, (state, action) => {
        if (action.payload.data) {
          const index = state.tasks.findIndex(t => t.id === action.payload.data!.id);
          if (index !== -1) {
            state.tasks[index] = action.payload.data;
          }
          if (state.currentTask?.id === action.payload.data.id) {
            state.currentTask = action.payload.data;
          }
        }
      });

    // deleteTask
    builder
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.tasks = state.tasks.filter(t => t.id !== action.payload);
        if (state.currentTask?.id === action.payload) {
          state.currentTask = null;
        }
      });

    // startTask
    builder
      .addCase(startTask.fulfilled, (state, action) => {
        const task = state.tasks.find(t => t.id === action.payload);
        if (task) {
          task.status = TaskStatus.RUNNING;
        }
        if (state.currentTask?.id === action.payload) {
          state.currentTask.status = TaskStatus.RUNNING;
        }
      });

    // stopTask
    builder
      .addCase(stopTask.fulfilled, (state, action) => {
        const task = state.tasks.find(t => t.id === action.payload);
        if (task) {
          task.status = TaskStatus.CANCELLED;
        }
        if (state.currentTask?.id === action.payload) {
          state.currentTask.status = TaskStatus.CANCELLED;
        }
      });
  },
});

export const { setFilters, clearFilters, updateTaskStatus, clearError } = tasksSlice.actions;
export default tasksSlice.reducer;
