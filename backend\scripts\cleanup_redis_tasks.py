#!/usr/bin/env python3
"""
清理Redis中的任务数据

删除所有任务相关的Redis键
"""

import redis

def cleanup_redis_tasks():
    """清理Redis中的任务数据"""
    print("🔍 清理Redis中的任务数据")
    print("=" * 50)
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 获取所有任务相关的键
        task_keys = r.keys("monitoring_tasks:*")
        
        print(f"\n📋 找到 {len(task_keys)} 个任务相关的键:")
        for key in task_keys:
            print(f"  {key}")
        
        if task_keys:
            # 删除所有任务相关的键
            deleted_count = r.delete(*task_keys)
            print(f"\n✅ 成功删除 {deleted_count} 个键")
        else:
            print(f"\n✅ 没有找到需要清理的键")
        
        print(f"\n🎉 Redis任务数据清理完成!")
        
    except Exception as e:
        print(f"❌ 清理Redis数据时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    cleanup_redis_tasks()
