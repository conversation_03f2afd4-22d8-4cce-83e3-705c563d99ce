#!/usr/bin/env python3
"""
Grafana面板导入脚本
自动导入MonIt监控面板到Grafana
"""

import requests
import json
import os
import time
from pathlib import Path

class GrafanaDashboardImporter:
    """Grafana面板导入器"""
    
    def __init__(self, grafana_url="http://localhost:3001", username="admin", password="admin123"):
        self.grafana_url = grafana_url
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.auth = (username, password)
        
    def wait_for_grafana(self, max_wait=60):
        """等待Grafana服务启动"""
        print("🔍 等待Grafana服务启动...")
        
        for i in range(max_wait):
            try:
                response = self.session.get(f"{self.grafana_url}/api/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Grafana服务已启动")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            if i % 10 == 0:
                print(f"⏳ 等待中... ({i}/{max_wait}秒)")
            time.sleep(1)
        
        print("❌ Grafana服务启动超时")
        return False
    
    def create_folder(self, folder_name="MonIt Dashboards"):
        """创建面板文件夹"""
        print(f"📁 创建文件夹: {folder_name}")
        
        folder_data = {
            "title": folder_name
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/folders",
                json=folder_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                folder_info = response.json()
                print(f"✅ 文件夹创建成功: {folder_info['title']}")
                return folder_info['uid']
            elif response.status_code == 409:
                # 文件夹已存在，获取UID
                response = self.session.get(f"{self.grafana_url}/api/folders")
                folders = response.json()
                for folder in folders:
                    if folder['title'] == folder_name:
                        print(f"📁 文件夹已存在: {folder_name}")
                        return folder['uid']
            else:
                print(f"⚠️  文件夹创建失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 文件夹创建错误: {e}")
            return None
    
    def import_dashboard(self, dashboard_file, folder_uid=None):
        """导入单个面板"""
        print(f"📊 导入面板: {dashboard_file.name}")
        
        try:
            with open(dashboard_file, 'r', encoding='utf-8') as f:
                dashboard_json = json.load(f)
            
            # 准备导入数据
            import_data = {
                "dashboard": dashboard_json["dashboard"],
                "overwrite": True,
                "inputs": []
            }
            
            if folder_uid:
                import_data["folderId"] = folder_uid
            
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=import_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 面板导入成功: {result['title']}")
                return True
            else:
                print(f"⚠️  面板导入失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 面板导入错误: {e}")
            return False
    
    def import_all_dashboards(self):
        """导入所有面板"""
        print("🚀 开始导入MonIt监控面板...")
        
        # 等待Grafana启动
        if not self.wait_for_grafana():
            return False
        
        # 创建文件夹
        folder_uid = self.create_folder("MonIt Dashboards")
        
        # 获取面板文件
        dashboard_dir = Path("monitoring/grafana/dashboards")
        if not dashboard_dir.exists():
            print(f"❌ 面板目录不存在: {dashboard_dir}")
            return False
        
        dashboard_files = list(dashboard_dir.glob("*.json"))
        if not dashboard_files:
            print("❌ 未找到面板文件")
            return False
        
        print(f"📋 找到 {len(dashboard_files)} 个面板文件")
        
        # 导入面板
        success_count = 0
        for dashboard_file in dashboard_files:
            if self.import_dashboard(dashboard_file, folder_uid):
                success_count += 1
            time.sleep(1)  # 避免请求过快
        
        print(f"\n📊 面板导入完成: {success_count}/{len(dashboard_files)} 成功")
        
        if success_count > 0:
            print(f"\n🔗 访问Grafana查看面板:")
            print(f"  URL: {self.grafana_url}")
            print(f"  用户名: {self.username}")
            print(f"  密码: {self.password}")
            print(f"  文件夹: MonIt Dashboards")
        
        return success_count == len(dashboard_files)


def main():
    """主函数"""
    print("=" * 50)
    print("MonIt Grafana面板导入工具")
    print("=" * 50)
    
    importer = GrafanaDashboardImporter()
    
    try:
        success = importer.import_all_dashboards()
        
        if success:
            print("\n🎉 所有面板导入成功！")
            print("\n📋 可用面板:")
            print("  - MonIt System Dashboard (系统监控)")
            print("  - MonIt Crawler Performance (爬虫性能)")
            print("  - MonIt Database Performance (数据库性能)")
            print("  - MonIt API Performance (API性能)")
        else:
            print("\n⚠️  部分面板导入失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  导入被用户中断")
    except Exception as e:
        print(f"\n❌ 导入过程中发生错误: {e}")


if __name__ == "__main__":
    main()
