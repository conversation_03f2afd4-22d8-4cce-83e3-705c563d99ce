#!/usr/bin/env python3
"""
测试API兼容性

验证重构后的API是否能正常响应前端请求
"""

import asyncio
import sys
import os
import json
import httpx
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


async def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试API兼容性")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        try:
            # 测试1: 健康检查
            print("\n📋 测试1: 健康检查")
            response = await client.get(f"{base_url}/health")
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                print("  ✅ 健康检查通过")
            else:
                print("  ❌ 健康检查失败")
            
            # 测试2: 获取监控任务列表
            print("\n📋 测试2: 获取监控任务列表")
            params = {
                "sort_by": "created_at",
                "sort_order": "desc",
                "page": 1,
                "page_size": 20
            }
            response = await client.get(f"{base_url}/api/v1/monitoring-tasks/", params=params)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("  ✅ 任务列表获取成功")
                print(f"  任务总数: {data.get('total', 0)}")
                
                # 检查返回的任务数据结构
                tasks = data.get('tasks', [])
                if tasks:
                    task = tasks[0]
                    print(f"  示例任务字段: {list(task.keys())}")
                    
                    # 检查调度配置
                    schedule = task.get('schedule')
                    if schedule:
                        print(f"  调度配置: {schedule}")
                    else:
                        print("  ⚠️ 缺少调度配置")
                
            else:
                print("  ❌ 任务列表获取失败")
                print(f"  错误信息: {response.text}")
            
            # 测试3: 获取特定任务详情
            print("\n📋 测试3: 获取任务详情")
            
            # 先获取任务列表找到一个任务ID
            list_response = await client.get(f"{base_url}/api/v1/monitoring-tasks/")
            if list_response.status_code == 200:
                list_data = list_response.json()
                tasks = list_data.get('tasks', [])
                
                if tasks:
                    task_id = tasks[0]['id']
                    detail_response = await client.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}")
                    print(f"  状态码: {detail_response.status_code}")
                    
                    if detail_response.status_code == 200:
                        task_detail = detail_response.json()
                        print("  ✅ 任务详情获取成功")
                        print(f"  任务名称: {task_detail.get('name')}")
                        print(f"  任务状态: {task_detail.get('status')}")
                        
                        # 检查调度配置
                        schedule = task_detail.get('schedule')
                        if schedule:
                            print(f"  调度类型: {schedule.get('type')}")
                            print(f"  调度启用: {schedule.get('enabled')}")
                        
                    else:
                        print("  ❌ 任务详情获取失败")
                        print(f"  错误信息: {detail_response.text}")
                else:
                    print("  ℹ️ 没有任务可供测试")
            
            # 测试4: 创建测试任务
            print("\n📋 测试4: 创建测试任务")
            
            test_task_data = {
                "name": "API测试任务",
                "description": "用于测试API兼容性的任务",
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai"
                },
                "config": {
                    "platform": "mercadolibre",
                    "batch_size": 10,
                    "max_retries": 3,
                    "timeout": 300
                },
                "urls": [
                    "https://example.com/product/1",
                    "https://example.com/product/2"
                ]
            }
            
            create_response = await client.post(
                f"{base_url}/api/v1/monitoring-tasks/",
                json=test_task_data
            )
            print(f"  状态码: {create_response.status_code}")
            
            if create_response.status_code in [200, 201]:
                created_task = create_response.json()
                print("  ✅ 任务创建成功")
                print(f"  任务ID: {created_task.get('id')}")
                
                # 清理测试任务
                task_id = created_task.get('id')
                if task_id:
                    delete_response = await client.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}")
                    if delete_response.status_code in [200, 204]:
                        print("  ✅ 测试任务已清理")
                    else:
                        print("  ⚠️ 测试任务清理失败")
            else:
                print("  ❌ 任务创建失败")
                print(f"  错误信息: {create_response.text}")
            
            print("\n🎉 API兼容性测试完成!")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    await test_api_endpoints()


if __name__ == "__main__":
    asyncio.run(main())
