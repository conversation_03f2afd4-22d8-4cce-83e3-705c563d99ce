#!/usr/bin/env python3
"""
测试链接统计显示问题
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_url_stats_issue():
    """测试链接统计显示问题"""
    
    async with aiohttp.ClientSession() as session:
        print("🔗 测试链接统计显示问题...")
        
        # 1. 获取任务列表，检查URL统计数据
        print("\n1. 获取任务列表，检查URL统计数据...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    print(f"✅ 找到 {len(tasks)} 个任务")
                    
                    for i, task in enumerate(tasks):
                        print(f"\n任务 {i+1}: {task.get('name')}")
                        print(f"  任务ID: {task.get('id')}")
                        
                        # 检查URL统计字段
                        print(f"  URL统计字段:")
                        print(f"    total_urls: {task.get('total_urls')} (类型: {type(task.get('total_urls'))})")
                        print(f"    active_urls: {task.get('active_urls')} (类型: {type(task.get('active_urls'))})")
                        
                        # 检查其他可能的URL字段
                        print(f"  其他字段:")
                        for key, value in task.items():
                            if 'url' in key.lower():
                                print(f"    {key}: {value}")
                        
                        # 获取任务详情，对比数据
                        task_id = task.get('id')
                        if task_id:
                            print(f"  获取任务详情进行对比...")
                            try:
                                async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as detail_response:
                                    if detail_response.status == 200:
                                        detail_data = await detail_response.json()
                                        task_detail = detail_data.get('data', {})
                                        print(f"    详情API - total_urls: {task_detail.get('total_urls')}")
                                        print(f"    详情API - active_urls: {task_detail.get('active_urls')}")
                                    else:
                                        print(f"    详情API失败: {detail_response.status}")
                            except Exception as e:
                                print(f"    详情API异常: {e}")
                            
                            # 获取URL列表，验证实际数量
                            print(f"  获取URL列表验证实际数量...")
                            try:
                                async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as urls_response:
                                    if urls_response.status == 200:
                                        urls_data = await urls_response.json()
                                        urls = urls_data.get('data', [])
                                        total = urls_data.get('total', 0)
                                        active_count = len([url for url in urls if url.get('status') == 'active'])
                                        print(f"    URL列表API - 总数: {total}")
                                        print(f"    URL列表API - 活跃数: {active_count}")
                                        print(f"    URL列表API - 返回数量: {len(urls)}")
                                    else:
                                        print(f"    URL列表API失败: {urls_response.status}")
                            except Exception as e:
                                print(f"    URL列表API异常: {e}")
                        
                        if i >= 1:  # 只检查前2个任务
                            break
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 检查任务列表API的实现
        print(f"\n2. 分析问题原因...")
        print(f"可能的问题:")
        print(f"  1. 任务列表API没有返回 total_urls 和 active_urls 字段")
        print(f"  2. 字段值为 null 或 undefined")
        print(f"  3. 数据类型不匹配")
        print(f"  4. 后端计算逻辑有问题")
        
        # 3. 建议的修复方案
        print(f"\n3. 建议的修复方案:")
        print(f"  方案1: 修复后端任务列表API，确保返回正确的URL统计")
        print(f"  方案2: 前端在显示时进行容错处理")
        print(f"  方案3: 前端异步获取URL统计数据")
        
        print("\n🎉 链接统计问题分析完成!")

if __name__ == "__main__":
    asyncio.run(test_url_stats_issue())
