#!/usr/bin/env python3
"""
分析任务统计信息中的简化处理问题
检查哪些统计信息没有正确获取任务真实信息
"""

import asyncio
import aiohttp
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

API_BASE_URL = "http://localhost:8000"

async def analyze_task_stats_issues():
    """分析任务统计信息中的问题"""
    
    # 测试任务ID
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    async with aiohttp.ClientSession() as session:
        print(f"🔍 分析任务统计信息中的简化处理问题")
        print(f"📋 任务ID: {task_id}")
        print("=" * 80)
        
        # 1. 检查执行历史统计
        print(f"\n1. 检查执行历史统计...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    stats = data.get('data', {})
                    execution_stats = stats.get('execution_stats', {})
                    
                    print(f"📊 执行统计信息:")
                    print(f"   总执行次数: {execution_stats.get('total_executions', 0)}")
                    print(f"   成功次数: {execution_stats.get('successful_executions', 0)}")
                    print(f"   失败次数: {execution_stats.get('failed_executions', 0)}")
                    print(f"   成功率: {execution_stats.get('success_rate', 0)}%")
                    print(f"   平均时长: {execution_stats.get('avg_duration', 0)}s")
                    
                    # 分析问题
                    if execution_stats.get('total_executions', 0) == 0:
                        print(f"❌ 问题1: 执行历史统计为空，可能使用了内存临时存储")
                    else:
                        print(f"✅ 执行历史统计有数据")
                        
                else:
                    print(f"❌ 获取任务统计信息失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 获取任务统计信息异常: {e}")
        
        # 2. 检查执行历史API
        print(f"\n2. 检查执行历史API...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/history") as response:
                if response.status == 200:
                    data = await response.json()
                    history = data.get('data', [])
                    total = data.get('total', 0)
                    
                    print(f"📋 执行历史记录:")
                    print(f"   总记录数: {total}")
                    print(f"   返回记录数: {len(history)}")
                    
                    if total == 0:
                        print(f"❌ 问题2: 执行历史记录为空，使用临时内存存储")
                        print(f"   说明: task_execution_history = {{}} 是内存字典")
                    else:
                        print(f"✅ 执行历史记录有数据")
                        for i, record in enumerate(history[:3]):
                            print(f"   记录{i+1}: {record.get('status', 'unknown')} - {record.get('start_time', 'N/A')}")
                            
                else:
                    print(f"❌ 获取执行历史失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 获取执行历史异常: {e}")
        
        # 3. 检查任务信息
        print(f"\n3. 检查任务信息...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    stats = data.get('data', {})
                    task_info = stats.get('task_info', {})
                    
                    print(f"📋 任务信息:")
                    print(f"   创建时间: {task_info.get('created_at', 'N/A')}")
                    print(f"   最后运行: {task_info.get('last_run', 'N/A')}")
                    print(f"   下次运行: {task_info.get('next_run', 'N/A')}")
                    print(f"   状态: {task_info.get('status', 'N/A')}")
                    print(f"   是否运行中: {task_info.get('is_running', False)}")
                    
                    # 分析问题
                    if not task_info.get('last_run'):
                        print(f"❌ 问题3: last_run为空，可能没有真实的执行记录")
                    if not task_info.get('next_run'):
                        print(f"❌ 问题4: next_run为空，可能没有调度信息")
                        
                else:
                    print(f"❌ 获取任务信息失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 获取任务信息异常: {e}")
        
        # 4. 检查Worker分配信息
        print(f"\n4. 检查Worker分配信息...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    task_data = data.get('data', {})
                    
                    print(f"📋 任务详情:")
                    print(f"   总URL数: {task_data.get('total_urls', 0)}")
                    print(f"   活跃URL数: {task_data.get('active_urls', 0)}")
                    
                    # 检查是否有Worker分配信息
                    stats = task_data.get('stats', {})
                    if not stats:
                        print(f"❌ 问题5: 缺少Worker分配统计信息")
                    else:
                        print(f"✅ 有Worker分配统计信息")
                        
                else:
                    print(f"❌ 获取任务详情失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 获取任务详情异常: {e}")
        
        print(f"\n" + "=" * 80)
        print(f"🎯 问题总结")
        print(f"❌ 问题1: 执行历史使用内存临时存储 (task_execution_history = {{}})")
        print(f"❌ 问题2: 平均执行时间计算逻辑简化 (注释: 简化计算，实际应该解析时间戳)")
        print(f"❌ 问题3: 缺少真实的调度信息 (last_run, next_run)")
        print(f"❌ 问题4: 缺少Worker执行统计信息")
        print(f"❌ 问题5: 缺少真实的任务执行状态跟踪")

if __name__ == "__main__":
    asyncio.run(analyze_task_stats_issues())
