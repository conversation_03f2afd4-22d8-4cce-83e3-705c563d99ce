#!/usr/bin/env python3
"""
演示爬虫请求生成的完整数据流
展示从监控任务到爬虫API请求的标准化流程
"""

import json
from datetime import datetime


def demo_data_flow():
    """演示完整的数据流"""
    
    print("🚀 爬虫请求生成数据流演示")
    print("=" * 60)
    
    # 1. 模拟监控任务数据
    print("\n📋 1. 监控任务数据")
    monitoring_task = {
        "id": "monitoring-task-12345",
        "name": "MercadoLibre商品监控",
        "platform": "mercadolibre",
        "config": {
            "config_id": "config-67890",
            "llm_query": "提取商品标题、价格、库存状态、卖家信息",
            "llm_schema": json.dumps({
                "type": "object",
                "properties": {
                    "title": {"type": "string", "description": "商品标题"},
                    "price": {"type": "string", "description": "商品价格"},
                    "stock": {"type": "number", "description": "库存数量"},
                    "seller": {"type": "string", "description": "卖家名称"}
                },
                "required": ["title", "price"]
            }),
            "cache_enabled": True,
            "priority": "high",
            "auth_method": "api_key",
            "api_key_prefix": "prod_ml_key_2025",
            "user_agent": "MonIt-Production/2.0"
        },
        "url_ids": ["url-001", "url-002", "url-003", "url-004"]
    }
    
    print(f"  任务ID: {monitoring_task['id']}")
    print(f"  任务名称: {monitoring_task['name']}")
    print(f"  平台: {monitoring_task['platform']}")
    print(f"  关联URL数量: {len(monitoring_task['url_ids'])}")
    
    # 2. 模拟URL池中的活跃URL
    print("\n🌐 2. URL池中的活跃URL")
    url_pool_data = {
        "url-001": {
            "id": "url-001",
            "url": "https://articulo.mercadolibre.com.mx/MLM-1234567890-smartphone-samsung-galaxy-s24",
            "status": "active",
            "platform": "mercadolibre"
        },
        "url-002": {
            "id": "url-002", 
            "url": "https://articulo.mercadolibre.com.mx/MLM-1234567891-laptop-dell-inspiron-15",
            "status": "active",
            "platform": "mercadolibre"
        },
        "url-003": {
            "id": "url-003",
            "url": "https://articulo.mercadolibre.com.mx/MLM-1234567892-tablet-ipad-air-2024",
            "status": "disabled",  # 非活跃URL，会被过滤
            "platform": "mercadolibre"
        },
        "url-004": {
            "id": "url-004",
            "url": "https://articulo.mercadolibre.com.mx/MLM-1234567893-auriculares-sony-wh1000xm5",
            "status": "active",
            "platform": "mercadolibre"
        }
    }
    
    # 过滤活跃URL
    active_urls = [
        item["url"] for item in url_pool_data.values() 
        if item["status"] == "active"
    ]
    
    print(f"  总URL数量: {len(url_pool_data)}")
    print(f"  活跃URL数量: {len(active_urls)}")
    for i, url in enumerate(active_urls, 1):
        print(f"    {i}. {url}")
    
    # 3. 模拟系统配置
    print("\n⚙️ 3. 系统配置 (来自环境变量)")
    system_config = {
        "default_callback_url": "http://backend:8000/api/v1/crawler/callback",
        "max_concurrent_tasks": 2,
        "batch_timeout": 600
    }
    
    for key, value in system_config.items():
        print(f"  {key}: {value}")
    
    # 4. 模拟执行上下文
    print("\n🔄 4. 执行上下文 (运行时信息)")
    execution_context = {
        "execution_id": f"exec_{monitoring_task['id']}_{int(datetime.now().timestamp())}",
        "client_ip": "**********",  # Docker内部IP
        "celery_task_id": "celery-monitoring-456",
        "execution_type": "scheduled_monitoring",
        "start_time": datetime.now().isoformat()
    }
    
    for key, value in execution_context.items():
        print(f"  {key}: {value}")
    
    # 5. 构建爬虫API请求
    print("\n🔧 5. 构建爬虫API请求")
    print("数据流: 监控任务 + URL池 + 系统配置 + 执行上下文 → 标准化爬虫请求")
    
    # 生成批次信息
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    batch_name = f"Task_{monitoring_task['id']}_{timestamp}"
    batch_description = f"监控任务 {monitoring_task['id']} 的批次执行 - {len(active_urls)} URLs"
    
    # 构建完整的爬虫API请求
    crawler_request = {
        "request": {
            # URL来源：监控任务的活跃URL
            "urls": active_urls,
            
            # 用户配置来源：爬取配置
            "q": monitoring_task["config"]["llm_query"],
            "schema_str": monitoring_task["config"]["llm_schema"],
            "cache": monitoring_task["config"]["cache_enabled"],
            "priority": monitoring_task["config"]["priority"],
            
            # 自动生成字段
            "batch_name": batch_name,
            "batch_description": batch_description,
            
            # 系统配置来源：环境变量
            "callback_url": system_config["default_callback_url"],
            "max_concurrent_tasks": system_config["max_concurrent_tasks"],
            "batch_timeout": system_config["batch_timeout"]
        },
        "auth_context": {
            # 固定值
            "user_id": "monit_system",
            "role": "user",
            "permissions": [],
            
            # 用户配置来源：爬取配置
            "auth_method": monitoring_task["config"]["auth_method"],
            "api_key_prefix": monitoring_task["config"]["api_key_prefix"],
            "user_agent": monitoring_task["config"]["user_agent"],
            
            # 运行时信息来源：执行上下文
            "client_ip": execution_context["client_ip"],
            "celery_task_id": execution_context["celery_task_id"],
            "execution_id": execution_context["execution_id"],
            
            # 自动生成
            "authenticated_at": datetime.now().isoformat()
        }
    }
    
    # 6. 显示生成的请求
    print("\n📤 6. 生成的爬虫API请求")
    print(json.dumps(crawler_request, indent=2, ensure_ascii=False))
    
    # 7. 数据来源总结
    print("\n📊 7. 数据来源总结")
    print("┌─────────────────────────────────────────────────────────────┐")
    print("│                     数据来源映射                              │")
    print("├─────────────────────────────────────────────────────────────┤")
    print("│ 监控任务的活跃URL    → request.urls                          │")
    print("│ 爬取配置.llm_query   → request.q                            │")
    print("│ 爬取配置.llm_schema  → request.schema_str                   │")
    print("│ 爬取配置.cache_enabled → request.cache                      │")
    print("│ 爬取配置.priority    → request.priority                     │")
    print("│ 系统配置.callback_url → request.callback_url                │")
    print("│ 系统配置.max_concurrent → request.max_concurrent_tasks      │")
    print("│ 系统配置.batch_timeout → request.batch_timeout              │")
    print("│ 爬取配置.auth_method → auth_context.auth_method             │")
    print("│ 爬取配置.api_key_prefix → auth_context.api_key_prefix       │")
    print("│ 爬取配置.user_agent  → auth_context.user_agent              │")
    print("│ 执行上下文.client_ip → auth_context.client_ip               │")
    print("│ 执行上下文.celery_task_id → auth_context.celery_task_id     │")
    print("│ 自动生成.batch_name  → request.batch_name                   │")
    print("│ 自动生成.authenticated_at → auth_context.authenticated_at   │")
    print("│ 固定值.user_id       → auth_context.user_id                 │")
    print("└─────────────────────────────────────────────────────────────┘")
    
    # 8. 使用示例
    print("\n💡 8. 在代码中的使用示例")
    print("""
# 在Celery任务中使用
from app.tasks.enhanced_crawl_task import enhanced_crawl_task

# 调用增强的爬取任务
result = enhanced_crawl_task.delay(
    task_id="monitoring-task-12345",
    execution_context={
        "source": "scheduled_monitoring",
        "execution_id": "exec-123"
    }
)

# 在监控执行器中使用
from app.tasks.monitoring_executor import MonitoringTaskExecutor

executor = MonitoringTaskExecutor("monitoring-task-12345")
result = await executor.execute_with_request_builder()

# 直接使用CrawlerRequestBuilder
from app.services.crawler_request_builder import CrawlerRequestBuilder

crawler_request = await CrawlerRequestBuilder.build_request_for_task(
    task_id="monitoring-task-12345",
    execution_context={"source": "manual_trigger"}
)
""")
    
    print("\n🎉 爬虫请求生成数据流演示完成！")
    print("\n✨ 关键优势:")
    print("  • 数据来源清晰：每个字段的来源明确定义")
    print("  • 配置分离：用户配置与系统配置完全分离")
    print("  • 标准化：统一的请求构建逻辑")
    print("  • 可测试性：完整的单元测试覆盖")
    print("  • 可维护性：清晰的服务边界和职责分工")
    print("  • 向后兼容：现有功能不受影响")


if __name__ == "__main__":
    demo_data_flow()
