#!/bin/bash

# MonIt 任务调度系统 - 快速启动脚本 (Linux/Mac)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "===================================================="
echo "🚀 MonIt 任务调度系统 - 快速启动"
echo "===================================================="
echo ""

# 进入项目根目录
cd "$(dirname "$0")/.."

log_info "📦 检查环境..."

# 检查conda
if ! command -v conda &> /dev/null; then
    log_error "Conda未安装或不在PATH中"
    echo "请先安装Miniconda: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

# 检查docker
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装或未启动"
    echo "请先安装Docker: https://www.docker.com/products/docker-desktop"
    exit 1
fi

log_info "✅ 基础环境检查通过"

echo ""
log_info "🔍 检查MonIt环境..."

# 激活conda环境
source "$(conda info --base)/etc/profile.d/conda.sh"
if ! conda activate monit 2>/dev/null; then
    log_error "MonIt conda环境未找到"
    echo "请先运行: ./scripts/setup_environment.sh"
    exit 1
fi

log_info "✅ MonIt环境已激活"

echo ""
log_info "🐳 启动Redis容器..."

# 启动Redis容器
if ! docker run -d --name monit-redis-test -p 6379:6379 redis:7-alpine --appendonly yes 2>/dev/null; then
    log_warn "Redis容器可能已存在，尝试启动现有容器..."
    docker start monit-redis-test >/dev/null 2>&1 || true
fi

echo ""
log_info "⏳ 等待Redis启动 (3秒)..."
sleep 3

echo ""
log_info "🧪 验证Redis连接..."
if ! docker exec monit-redis-test redis-cli ping >/dev/null 2>&1; then
    log_error "Redis连接失败"
    exit 1
fi
log_info "✅ Redis连接正常"

echo ""
log_info "📊 运行快速测试..."
if ! python backend/scripts/quick_test.py; then
    log_error "快速测试失败"
    exit 1
fi

echo ""
echo "===================================================="
log_info "🎉 任务调度系统启动成功！"
echo "===================================================="
echo ""
echo "📌 可用功能:"
echo "   ✅ Excel处理 (624个URL)"
echo "   ✅ 任务分片 (智能批次分解)"
echo "   ✅ 限流控制 (2并发限制)"
echo "   ✅ 优先级调度 (4级优先级)"
echo "   ✅ 重试机制 (智能重试策略)"
echo ""
echo "🚀 下一步操作:"
echo "   1. 运行真实场景测试: python backend/scripts/run_real_test.py"
echo "   2. 启动任务管理器: python backend/scripts/start_task_manager.py"
echo "   3. 启动完整系统: ./scripts/start_dev.sh"
echo ""
echo "🛑 停止服务:"
echo "   docker stop monit-redis-test"
echo "   docker rm monit-redis-test"
echo "===================================================="

# 创建停止脚本
cat > stop_task_system.sh << 'EOF'
#!/bin/bash
echo "🛑 停止MonIt任务调度系统..."
docker stop monit-redis-test 2>/dev/null || true
docker rm monit-redis-test 2>/dev/null || true
echo "✅ 服务已停止"
EOF

chmod +x stop_task_system.sh
log_info "创建了停止脚本: ./stop_task_system.sh"
