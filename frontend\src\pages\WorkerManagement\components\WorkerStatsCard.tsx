/**
 * Worker统计卡片组件
 * 显示Worker的统计信息和监控数据
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Progress,
  Tag,
  Space,
  Typography,
  Tooltip,
  Alert,
  Spin
} from 'antd';
import {
  TeamOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ThunderboltOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';

import { 
  workerA<PERSON>,
  CrawlerWorkerSummary
} from '../../../services';

const { Text } = Typography;

interface WorkerStatsCardProps {
  workers: CrawlerWorkerSummary[];
  loading?: boolean;
}

const WorkerStatsCard: React.FC<WorkerStatsCardProps> = ({ workers, loading = false }) => {
  // 计算统计数据
  const totalWorkers = workers.length;
  const runningWorkers = workers.filter(w => w.status === 'running').length;
  const idleWorkers = workers.filter(w => w.status === 'idle').length;
  const busyWorkers = workers.filter(w => w.status === 'busy').length;
  const errorWorkers = workers.filter(w => w.status === 'error').length;
  const maintenanceWorkers = workers.filter(w => w.status === 'maintenance').length;

  // 计算健康Worker数量
  const healthyWorkers = workers.filter(w => w.health_score >= 0.8).length;
  
  // 计算总任务统计
  const totalTasksCompleted = workers.reduce((sum, w) => sum + w.total_tasks_completed, 0);
  const totalCurrentTasks = workers.reduce((sum, w) => sum + w.current_tasks, 0);
  const totalMaxTasks = workers.reduce((sum, w) => sum + w.max_concurrent_tasks, 0);
  
  // 计算平均成功率
  const avgSuccessRate = workers.length > 0 
    ? workers.reduce((sum, w) => sum + w.success_rate, 0) / workers.length 
    : 0;

  // 计算平均健康评分
  const avgHealthScore = workers.length > 0 
    ? workers.reduce((sum, w) => sum + w.health_score, 0) / workers.length 
    : 0;

  // 优先级分布
  const priorityDistribution = {
    low: workers.filter(w => w.priority === 'low').length,
    normal: workers.filter(w => w.priority === 'normal').length,
    high: workers.filter(w => w.priority === 'high').length,
    urgent: workers.filter(w => w.priority === 'urgent').length,
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* 总体统计 */}
      <Card title="Worker总体统计" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总Worker数"
              value={totalWorkers}
              prefix={<TeamOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="运行中"
              value={runningWorkers}
              prefix={<PlayCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="空闲"
              value={idleWorkers}
              prefix={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="健康Worker"
              value={healthyWorkers}
              prefix={<ThunderboltOutlined style={{ color: '#52c41a' }} />}
            />
          </Col>
        </Row>
      </Card>

      {/* 状态分布 */}
      <Card title="状态分布" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title={<Tag color="green">运行中</Tag>}
                value={runningWorkers}
                suffix={`/ ${totalWorkers}`}
              />
              <Progress
                percent={totalWorkers > 0 ? (runningWorkers / totalWorkers) * 100 : 0}
                size="small"
                status="success"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title={<Tag color="blue">空闲</Tag>}
                value={idleWorkers}
                suffix={`/ ${totalWorkers}`}
              />
              <Progress
                percent={totalWorkers > 0 ? (idleWorkers / totalWorkers) * 100 : 0}
                size="small"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title={<Tag color="orange">忙碌</Tag>}
                value={busyWorkers}
                suffix={`/ ${totalWorkers}`}
              />
              <Progress
                percent={totalWorkers > 0 ? (busyWorkers / totalWorkers) * 100 : 0}
                size="small"
                status="active"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title={<Tag color="red">错误</Tag>}
                value={errorWorkers}
                suffix={`/ ${totalWorkers}`}
              />
              <Progress
                percent={totalWorkers > 0 ? (errorWorkers / totalWorkers) * 100 : 0}
                size="small"
                status="exception"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title={<Tag color="purple">维护中</Tag>}
                value={maintenanceWorkers}
                suffix={`/ ${totalWorkers}`}
              />
              <Progress
                percent={totalWorkers > 0 ? (maintenanceWorkers / totalWorkers) * 100 : 0}
                size="small"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title={<Tag color="cyan">健康</Tag>}
                value={healthyWorkers}
                suffix={`/ ${totalWorkers}`}
              />
              <Progress
                percent={totalWorkers > 0 ? (healthyWorkers / totalWorkers) * 100 : 0}
                size="small"
                status="success"
                showInfo={false}
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 性能统计 */}
      <Card title="性能统计" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="平均成功率"
              value={avgSuccessRate * 100}
              precision={1}
              suffix="%"
            />
            <Progress
              percent={avgSuccessRate * 100}
              size="small"
              status={avgSuccessRate >= 0.9 ? 'success' : avgSuccessRate >= 0.7 ? 'normal' : 'exception'}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="平均健康评分"
              value={avgHealthScore * 100}
              precision={1}
              suffix="%"
            />
            <Progress
              percent={avgHealthScore * 100}
              size="small"
              status={avgHealthScore >= 0.8 ? 'success' : avgHealthScore >= 0.5 ? 'normal' : 'exception'}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="总完成任务"
              value={totalTasksCompleted}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
        </Row>
      </Card>

      {/* 任务负载 */}
      <Card title="任务负载" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="当前总任务"
              value={totalCurrentTasks}
              suffix={`/ ${totalMaxTasks}`}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="总容量"
              value={totalMaxTasks}
              prefix={<TeamOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="负载率"
              value={totalMaxTasks > 0 ? (totalCurrentTasks / totalMaxTasks) * 100 : 0}
              precision={1}
              suffix="%"
            />
          </Col>
        </Row>
        <div style={{ marginTop: 16 }}>
          <Text strong>整体负载情况</Text>
          <Progress
            percent={totalMaxTasks > 0 ? (totalCurrentTasks / totalMaxTasks) * 100 : 0}
            status={totalCurrentTasks / totalMaxTasks > 0.8 ? 'exception' : 'normal'}
            format={(percent) => `${totalCurrentTasks}/${totalMaxTasks} (${percent?.toFixed(1)}%)`}
          />
        </div>
      </Card>

      {/* 优先级分布 */}
      <Card title="优先级分布">
        <Row gutter={16}>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title={
                  <Space>
                    <Tag color="red">紧急</Tag>
                  </Space>
                }
                value={priorityDistribution.urgent}
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title={
                  <Space>
                    <Tag color="orange">高</Tag>
                  </Space>
                }
                value={priorityDistribution.high}
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title={
                  <Space>
                    <Tag color="blue">普通</Tag>
                  </Space>
                }
                value={priorityDistribution.normal}
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title={
                  <Space>
                    <Tag color="default">低</Tag>
                  </Space>
                }
                value={priorityDistribution.low}
                suffix="个"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 警告信息 */}
      {errorWorkers > 0 && (
        <Alert
          message={`有 ${errorWorkers} 个Worker处于错误状态`}
          description="请检查这些Worker的配置和连接状态"
          type="error"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}

      {totalCurrentTasks / totalMaxTasks > 0.8 && (
        <Alert
          message="系统负载较高"
          description={`当前负载率为 ${((totalCurrentTasks / totalMaxTasks) * 100).toFixed(1)}%，建议增加Worker或优化任务分配`}
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );
};

export default WorkerStatsCard;
