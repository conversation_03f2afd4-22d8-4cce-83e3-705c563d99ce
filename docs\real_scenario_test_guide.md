# 真实场景测试指南

## 概述

本测试旨在验证MonIt任务调度与管理系统在真实场景下的性能和可靠性。测试将使用`LinksToMonit/all_task_item.xlsx`文件中的600+商品链接，模拟完整的任务处理流程。

## 测试目标

1. **验证Excel处理能力**: 正确读取和解析Excel文件中的商品链接
2. **验证任务分片功能**: 将600+URL自动分解为合适的批次
3. **验证限流控制**: 严格遵守外部API限制（2并发/100批次）
4. **验证任务调度**: 高效的任务队列管理和执行
5. **验证系统性能**: 处理速度、资源占用、稳定性

## 测试架构

```
Excel文件 → Excel处理器 → 任务管理器 → 任务分片器 → Celery队列 → Worker执行
   ↓            ↓           ↓           ↓           ↓          ↓
600+ URLs → 数据验证 → 任务创建 → 50个URL/批次 → Redis队列 → 模拟爬取
```

## 前置条件

### 1. 环境准备

```bash
# 确保在项目根目录
cd /path/to/MonIt

# 安装Python依赖
pip install pandas openpyxl redis

# 启动Redis服务
redis-server

# 检查Excel文件存在
ls LinksToMonit/all_task_item.xlsx
```

### 2. 数据库准备

```bash
# 启动数据库（如果使用Docker）
docker-compose up timescaledb -d

# 或者确保PostgreSQL/TimescaleDB正在运行
```

### 3. 目录结构检查

```
MonIt/
├── LinksToMonit/
│   └── all_task_item.xlsx          # 测试数据文件
├── backend/
│   ├── app/
│   │   ├── core/                   # 核心模块
│   │   ├── tasks/                  # Celery任务
│   │   └── utils/                  # 工具模块
│   ├── scripts/
│   │   └── run_real_test.py        # 快速测试脚本
│   └── tests/
│       └── test_real_scenario.py   # 完整测试脚本
└── logs/                           # 日志目录（自动创建）
```

## 测试流程

### 快速测试（推荐）

```bash
# 运行快速测试
python backend/scripts/run_real_test.py
```

### 完整测试

```bash
# 运行完整测试
python backend/tests/test_real_scenario.py
```

## 测试步骤详解

### 步骤1: Excel文件处理
- **目标**: 读取和验证Excel文件中的商品链接
- **预期**: 成功解析600+商品URL
- **验证点**:
  - Excel文件结构正确（至少3列）
  - URL格式有效
  - 平台识别准确（主要是MercadoLibre）

### 步骤2: 任务管理器初始化
- **目标**: 配置和启动任务调度系统
- **配置参数**:
  - 批次大小: 50个URL/批次
  - 最大并发: 2个批次
  - 限流设置: 2并发请求，30次/分钟
- **验证点**:
  - 任务管理器成功启动
  - 限流器正常工作
  - Redis连接正常

### 步骤3: 数据库任务创建
- **目标**: 在数据库中创建任务记录
- **验证点**:
  - 任务记录创建成功
  - 任务ID正确分配
  - 状态初始化为"pending"

### 步骤4: 任务提交和分片
- **目标**: 将600+URL提交到任务调度系统
- **预期行为**:
  - 自动分解为~12个批次（600÷50）
  - 任务按优先级排序
  - 批次进入待处理队列
- **验证点**:
  - 分片数量正确
  - 队列状态正常
  - 无URL丢失

### 步骤5: 任务执行监控
- **目标**: 实时监控任务执行过程
- **监控指标**:
  - 待处理批次数量
  - 运行中批次数量
  - 已完成批次数量
  - 失败批次数量
- **预期行为**:
  - 最多2个批次并行执行
  - 批次按序完成
  - 无死锁或卡死

### 步骤6: 结果分析
- **目标**: 分析性能和结果
- **关键指标**:
  - 总处理时间
  - 处理速度（URL/秒）
  - 成功率
  - 资源占用

## 预期结果

### 性能指标
- **处理速度**: 1-3 URLs/秒（受限流控制影响）
- **成功率**: >95%（模拟环境下）
- **并发控制**: 严格遵守2并发限制
- **内存占用**: <500MB

### 分片效果
- **批次数量**: ~12个批次（600÷50）
- **批次大小**: 50个URL/批次（最后一个批次可能较小）
- **队列管理**: 有序处理，无积压

### 限流验证
- **并发限制**: 同时最多2个批次执行
- **频率限制**: 遵守30次/分钟限制
- **熔断机制**: 失败率过高时自动保护

## 故障排除

### 常见问题

**问题1**: Excel文件读取失败
```bash
# 检查文件是否存在
ls -la LinksToMonit/all_task_item.xlsx

# 检查文件权限
chmod 644 LinksToMonit/all_task_item.xlsx

# 检查pandas安装
pip install pandas openpyxl
```

**问题2**: Redis连接失败
```bash
# 启动Redis
redis-server

# 检查Redis状态
redis-cli ping

# 检查端口占用
netstat -an | grep 6379
```

**问题3**: 任务卡死不执行
```bash
# 检查Celery worker状态
celery -A app.celery_app inspect active

# 重启worker
./backend/scripts/start_workers.sh restart

# 检查队列状态
redis-cli llen crawler_queue
```

**问题4**: 内存占用过高
- 减小批次大小（max_batch_size）
- 增加清理频率
- 检查是否有内存泄漏

### 日志分析

测试过程中会生成详细日志：

```bash
# 查看实时日志
tail -f logs/real_scenario_test_*.log

# 查看错误日志
grep ERROR logs/real_scenario_test_*.log

# 查看性能指标
grep "Performance Report" logs/real_scenario_test_*.log
```

## 测试结果解读

### 成功标准
- ✅ 所有600+URL成功读取
- ✅ 任务分片数量正确（~12个批次）
- ✅ 严格遵守并发限制（≤2个批次同时执行）
- ✅ 成功率 >90%
- ✅ 无系统崩溃或死锁

### 性能基准
- **优秀**: >2 URLs/秒，成功率>95%
- **良好**: 1-2 URLs/秒，成功率>90%
- **需优化**: <1 URLs/秒，成功率<90%

### 结果文件
测试完成后会生成：
- `logs/test_results_YYYYMMDD_HHMMSS.json` - 详细测试结果
- `logs/real_scenario_test_YYYYMMDD_HHMMSS.log` - 测试日志

## 下一步

测试成功后，可以进行：
1. **压力测试**: 增加URL数量到1000+
2. **并发测试**: 同时运行多个任务
3. **稳定性测试**: 长时间运行测试
4. **真实API测试**: 连接真实的外部爬虫服务

---

**注意**: 本测试使用模拟的爬虫API，不会实际访问外部网站。在生产环境中使用时，请确保遵守目标网站的robots.txt和使用条款。
