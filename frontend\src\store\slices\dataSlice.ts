import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ProductSnapshot, PaginationInfo } from '../../types';
import { dataApi } from '../../services/api';

// 异步actions
export const fetchProductSnapshots = createAsyncThunk(
  'data/fetchProductSnapshots',
  async (params?: {
    page?: number;
    page_size?: number;
    task_id?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const response = await dataApi.getProductSnapshots(params);
    return response;
  }
);

export const fetchProductTrends = createAsyncThunk(
  'data/fetchProductTrends',
  async (params: {
    url: string;
    start_date: string;
    end_date: string;
  }) => {
    const response = await dataApi.getProductTrends(params);
    return response;
  }
);

export const exportData = createAsyncThunk(
  'data/exportData',
  async (params: {
    task_id?: string;
    start_date?: string;
    end_date?: string;
    format: 'csv' | 'excel';
  }) => {
    const blob = await dataApi.exportData(params);
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `export_${new Date().toISOString().split('T')[0]}.${params.format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    return true;
  }
);

// State类型
interface DataState {
  productSnapshots: ProductSnapshot[];
  productTrends: ProductSnapshot[];
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo | null;
  filters: {
    task_id?: string;
    start_date?: string;
    end_date?: string;
    search?: string;
  };
  selectedProducts: string[];
  exportLoading: boolean;
}

// 初始状态
const initialState: DataState = {
  productSnapshots: [],
  productTrends: [],
  loading: false,
  error: null,
  pagination: null,
  filters: {},
  selectedProducts: [],
  exportLoading: false,
};

// Slice
const dataSlice = createSlice({
  name: 'data',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<DataState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setSelectedProducts: (state, action: PayloadAction<string[]>) => {
      state.selectedProducts = action.payload;
    },
    toggleProductSelection: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      const index = state.selectedProducts.indexOf(productId);
      if (index > -1) {
        state.selectedProducts.splice(index, 1);
      } else {
        state.selectedProducts.push(productId);
      }
    },
    selectAllProducts: (state) => {
      state.selectedProducts = state.productSnapshots.map(p => p.id);
    },
    clearSelection: (state) => {
      state.selectedProducts = [];
    },
    clearError: (state) => {
      state.error = null;
    },
    clearTrends: (state) => {
      state.productTrends = [];
    },
  },
  extraReducers: (builder) => {
    // fetchProductSnapshots
    builder
      .addCase(fetchProductSnapshots.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductSnapshots.fulfilled, (state, action) => {
        state.loading = false;
        state.productSnapshots = action.payload.data || [];
        state.pagination = action.payload.pagination || null;
      })
      .addCase(fetchProductSnapshots.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch product snapshots';
      });

    // fetchProductTrends
    builder
      .addCase(fetchProductTrends.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductTrends.fulfilled, (state, action) => {
        state.loading = false;
        state.productTrends = action.payload.data || [];
      })
      .addCase(fetchProductTrends.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch product trends';
      });

    // exportData
    builder
      .addCase(exportData.pending, (state) => {
        state.exportLoading = true;
        state.error = null;
      })
      .addCase(exportData.fulfilled, (state) => {
        state.exportLoading = false;
      })
      .addCase(exportData.rejected, (state, action) => {
        state.exportLoading = false;
        state.error = action.error.message || 'Failed to export data';
      });
  },
});

export const {
  setFilters,
  clearFilters,
  setSelectedProducts,
  toggleProductSelection,
  selectAllProducts,
  clearSelection,
  clearError,
  clearTrends,
} = dataSlice.actions;

export default dataSlice.reducer;
