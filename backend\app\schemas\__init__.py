"""
Pydantic数据模型模块

包含所有API数据验证模型
"""

from .product import ProductCreate, ProductUpdate, ProductResponse, ProductCategoryCreate, ProductCategoryResponse
from .snapshot import ProductSnapshotCreate, ProductSnapshotResponse
from .task import CrawlTaskCreate, CrawlTaskUpdate, CrawlTaskResponse, CrawlConfigCreate, CrawlConfigResponse
from .rule import ScrapingRuleCreate, ScrapingRuleResponse
from .proxy import ProxyPoolCreate, ProxyPoolResponse, UserAgentCreate, UserAgentResponse
from .log import SystemLogResponse
from .common import PaginatedResponse, ResponseStatus

__all__ = [
    # Product schemas
    "ProductCreate",
    "ProductUpdate", 
    "ProductResponse",
    "ProductCategoryCreate",
    "ProductCategoryResponse",
    
    # Snapshot schemas
    "ProductSnapshotCreate",
    "ProductSnapshotResponse",
    
    # Task schemas
    "CrawlTaskCreate",
    "CrawlTaskUpdate",
    "CrawlTaskResponse",
    "CrawlConfigCreate",
    "CrawlConfigResponse",
    
    # Rule schemas
    "ScrapingRuleCreate",
    "ScrapingRuleResponse",
    
    # Proxy schemas
    "ProxyPoolCreate",
    "ProxyPoolResponse",
    "UserAgentCreate",
    "UserAgentResponse",
    
    # Log schemas
    "SystemLogResponse",
    
    # Common schemas
    "PaginatedResponse",
    "ResponseStatus",
] 