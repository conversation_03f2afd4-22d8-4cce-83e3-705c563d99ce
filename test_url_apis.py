#!/usr/bin/env python3
"""
测试URL相关的API接口
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_url_apis():
    """测试URL相关的API接口"""
    
    async with aiohttp.ClientSession() as session:
        print("🔗 开始测试URL相关API接口...")
        
        # 1. 获取任务列表，找到一个任务
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 测试获取任务URL列表
        print(f"\n2. 测试获取任务URL列表 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as response:
                if response.status == 200:
                    data = await response.json()
                    urls = data.get('data', [])
                    total = data.get('total', 0)
                    print(f"✅ 获取任务URL列表成功: {total} 个URL")
                    
                    # 显示前几个URL的详细信息
                    for i, url in enumerate(urls[:3]):
                        print(f"   URL {i+1}:")
                        print(f"     ID: {url.get('id')}")
                        print(f"     URL: {url.get('url')}")
                        print(f"     状态: {url.get('status')}")
                        print(f"     平台: {url.get('platform')}")
                        print(f"     检查次数: {url.get('check_count')}")
                        print(f"     添加时间: {url.get('added_at')}")
                else:
                    print(f"❌ 获取任务URL列表失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 获取任务URL列表异常: {e}")
        
        # 3. 测试按状态筛选URL
        print(f"\n3. 测试按状态筛选URL (status=active)...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls?status=active") as response:
                if response.status == 200:
                    data = await response.json()
                    urls = data.get('data', [])
                    total = data.get('total', 0)
                    print(f"✅ 获取活跃URL成功: {total} 个URL")
                else:
                    print(f"❌ 获取活跃URL失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取活跃URL异常: {e}")
        
        # 4. 测试分页功能
        print(f"\n4. 测试分页功能 (page=1, page_size=5)...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls?page=1&page_size=5") as response:
                if response.status == 200:
                    data = await response.json()
                    urls = data.get('data', [])
                    total = data.get('total', 0)
                    page = data.get('page', 1)
                    page_size = data.get('page_size', 5)
                    print(f"✅ 分页获取成功: 第{page}页，每页{page_size}个，共{total}个，当前页{len(urls)}个")
                else:
                    print(f"❌ 分页获取失败: {response.status}")
        except Exception as e:
            print(f"❌ 分页获取异常: {e}")
        
        # 5. 测试更新任务URL（添加新URL）
        print(f"\n5. 测试更新任务URL...")
        test_urls = [
            "https://example.com/test-product-1",
            "https://example.com/test-product-2",
            "https://example.com/test-product-3"
        ]
        try:
            async with session.put(
                f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls",
                json={"urls": test_urls}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    added_count = data.get('added_count', 0)
                    duplicate_count = data.get('duplicate_count', 0)
                    print(f"✅ 更新任务URL成功: 新增{added_count}个，重复{duplicate_count}个")
                else:
                    print(f"❌ 更新任务URL失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 更新任务URL异常: {e}")
        
        # 6. 再次获取URL列表，验证更新结果
        print(f"\n6. 验证URL更新结果...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as response:
                if response.status == 200:
                    data = await response.json()
                    urls = data.get('data', [])
                    total = data.get('total', 0)
                    print(f"✅ 验证成功: 当前共有{total}个URL")
                    
                    # 查找刚添加的测试URL
                    test_url_found = 0
                    for url in urls:
                        if url.get('url') in test_urls:
                            test_url_found += 1
                    
                    print(f"   找到测试URL: {test_url_found}/{len(test_urls)}")
                else:
                    print(f"❌ 验证失败: {response.status}")
        except Exception as e:
            print(f"❌ 验证异常: {e}")
        
        print("\n🎉 URL API接口测试完成!")

if __name__ == "__main__":
    asyncio.run(test_url_apis())
