#!/bin/bash
# 完整后端启动脚本

echo "🚀 启动MonIt完整后端系统..."
echo "=================================="

# 检查conda环境
if ! conda info --envs | grep -q "monit"; then
    echo "❌ 未找到monit conda环境，请先创建环境"
    exit 1
fi

# 激活conda环境
echo "📦 激活conda环境..."
eval "$(conda shell.bash hook)"
conda activate monit

# 检查Docker服务
echo "🐳 检查Docker服务..."
if ! docker ps > /dev/null 2>&1; then
    echo "❌ Docker未运行，请启动Docker Desktop"
    exit 1
fi

# 启动基础设施服务
echo "🔧 启动基础设施服务 (PostgreSQL + Redis)..."
docker-compose up -d postgres redis

# 等待服务启动
echo "⏳ 等待数据库服务启动..."
sleep 10

# 检查数据库连接
echo "🔍 检查数据库连接..."
if ! docker exec monit-postgres pg_isready -U monit_user > /dev/null 2>&1; then
    echo "❌ PostgreSQL未就绪，请检查Docker服务"
    exit 1
fi

# 检查Redis连接
echo "🔍 检查Redis连接..."
if ! docker exec monit-redis redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis未就绪，请检查Docker服务"
    exit 1
fi

echo "✅ 基础设施服务启动成功"

# 运行数据库迁移
echo "📊 运行数据库迁移..."
cd backend
python -m alembic upgrade head
if [ $? -ne 0 ]; then
    echo "❌ 数据库迁移失败"
    exit 1
fi
cd ..

echo "✅ 数据库迁移完成"

# 启动服务
echo "🎯 启动后端服务..."

# 1. 启动TaskManager (后台)
echo "📋 启动PersistentTaskManager..."
python backend/scripts/start_task_manager.py &
TASKMANAGER_PID=$!
sleep 5

# 2. 启动Celery Worker (后台)
echo "⚡ 启动Celery Worker..."
cd backend
celery -A app.celery_app worker --loglevel=info --concurrency=2 &
CELERY_PID=$!
cd ..
sleep 5

# 3. 启动FastAPI服务器 (前台)
echo "🌐 启动FastAPI服务器..."
echo "=================================="
echo "🎉 完整后端启动成功！"
echo ""
echo "📋 服务状态:"
echo "  - PostgreSQL: http://localhost:5432"
echo "  - Redis: http://localhost:6379"
echo "  - TaskManager: 后台运行 (PID: $TASKMANAGER_PID)"
echo "  - Celery Worker: 后台运行 (PID: $CELERY_PID)"
echo "  - FastAPI: http://localhost:8000"
echo ""
echo "🔍 监控命令:"
echo "  - 查看Redis任务: ./scripts/check_redis_tasks.sh"
echo "  - 查看TaskManager日志: tail -f logs/task_manager.log"
echo "  - 查看Celery日志: 查看终端输出"
echo ""
echo "⏹️ 停止服务: Ctrl+C 然后运行 ./scripts/stop_full_backend.sh"
echo "=================================="

# 启动FastAPI (前台运行)
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    kill $TASKMANAGER_PID 2>/dev/null
    kill $CELERY_PID 2>/dev/null
    echo "✅ 服务已停止"
}

# 捕获退出信号
trap cleanup EXIT INT TERM
