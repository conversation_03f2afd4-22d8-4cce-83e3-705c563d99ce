#!/bin/bash

# Redis启动脚本 - MonIt生产环境
# 针对TaskManager持久化优化

set -e

# 使脚本可执行
chmod +x "$0"

# 配置变量
REDIS_CONFIG_FILE="config/redis.conf"
REDIS_DATA_DIR="/var/lib/redis"
REDIS_LOG_DIR="/var/log/redis"
REDIS_PID_FILE="/var/run/redis_6379.pid"
REDIS_PORT=6379

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Redis是否已安装
check_redis_installation() {
    log_info "检查Redis安装..."
    
    if ! command -v redis-server &> /dev/null; then
        log_error "Redis未安装，请先安装Redis"
        echo "Ubuntu/Debian: sudo apt-get install redis-server"
        echo "CentOS/RHEL: sudo yum install redis"
        echo "macOS: brew install redis"
        exit 1
    fi
    
    REDIS_VERSION=$(redis-server --version | head -n1)
    log_info "Redis版本: $REDIS_VERSION"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    # 创建数据目录
    if [ ! -d "$REDIS_DATA_DIR" ]; then
        log_debug "创建数据目录: $REDIS_DATA_DIR"
        sudo mkdir -p "$REDIS_DATA_DIR"
        sudo chown redis:redis "$REDIS_DATA_DIR" 2>/dev/null || true
        sudo chmod 755 "$REDIS_DATA_DIR"
    fi
    
    # 创建日志目录
    if [ ! -d "$REDIS_LOG_DIR" ]; then
        log_debug "创建日志目录: $REDIS_LOG_DIR"
        sudo mkdir -p "$REDIS_LOG_DIR"
        sudo chown redis:redis "$REDIS_LOG_DIR" 2>/dev/null || true
        sudo chmod 755 "$REDIS_LOG_DIR"
    fi
    
    # 创建PID目录
    PID_DIR=$(dirname "$REDIS_PID_FILE")
    if [ ! -d "$PID_DIR" ]; then
        log_debug "创建PID目录: $PID_DIR"
        sudo mkdir -p "$PID_DIR"
        sudo chown redis:redis "$PID_DIR" 2>/dev/null || true
        sudo chmod 755 "$PID_DIR"
    fi
}

# 检查配置文件
check_config_file() {
    log_info "检查配置文件..."
    
    if [ ! -f "$REDIS_CONFIG_FILE" ]; then
        log_error "配置文件不存在: $REDIS_CONFIG_FILE"
        exit 1
    fi
    
    log_debug "配置文件: $REDIS_CONFIG_FILE"
    
    # 验证配置文件语法
    if ! redis-server "$REDIS_CONFIG_FILE" --test-memory 1 &>/dev/null; then
        log_error "配置文件语法错误"
        exit 1
    fi
    
    log_info "配置文件验证通过"
}

# 检查端口是否被占用
check_port() {
    log_info "检查端口 $REDIS_PORT..."
    
    if netstat -tuln 2>/dev/null | grep -q ":$REDIS_PORT "; then
        log_warn "端口 $REDIS_PORT 已被占用"
        
        # 检查是否是Redis进程
        if pgrep -f "redis-server.*:$REDIS_PORT" > /dev/null; then
            log_warn "Redis已在端口 $REDIS_PORT 上运行"
            return 1
        else
            log_error "端口 $REDIS_PORT 被其他进程占用"
            exit 1
        fi
    fi
    
    return 0
}

# 停止现有的Redis实例
stop_existing_redis() {
    log_info "停止现有的Redis实例..."
    
    # 通过PID文件停止
    if [ -f "$REDIS_PID_FILE" ]; then
        PID=$(cat "$REDIS_PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_debug "通过PID文件停止Redis (PID: $PID)"
            kill -TERM "$PID"
            
            # 等待进程结束
            for i in {1..10}; do
                if ! kill -0 "$PID" 2>/dev/null; then
                    break
                fi
                sleep 1
            done
            
            # 强制杀死
            if kill -0 "$PID" 2>/dev/null; then
                log_warn "强制停止Redis进程"
                kill -KILL "$PID"
            fi
        fi
        rm -f "$REDIS_PID_FILE"
    fi
    
    # 通过进程名停止
    if pgrep -f "redis-server.*:$REDIS_PORT" > /dev/null; then
        log_debug "通过进程名停止Redis"
        pkill -f "redis-server.*:$REDIS_PORT" || true
        sleep 2
    fi
}

# 优化系统参数
optimize_system() {
    log_info "优化系统参数..."
    
    # 禁用透明大页
    if [ -f /sys/kernel/mm/transparent_hugepage/enabled ]; then
        echo never | sudo tee /sys/kernel/mm/transparent_hugepage/enabled > /dev/null
        log_debug "禁用透明大页"
    fi
    
    # 设置overcommit_memory
    echo 1 | sudo tee /proc/sys/vm/overcommit_memory > /dev/null
    log_debug "设置vm.overcommit_memory=1"
    
    # 增加somaxconn
    echo 1024 | sudo tee /proc/sys/net/core/somaxconn > /dev/null
    log_debug "设置net.core.somaxconn=1024"
    
    # 设置文件描述符限制
    ulimit -n 65535
    log_debug "设置文件描述符限制为65535"
}

# 启动Redis
start_redis() {
    log_info "启动Redis服务器..."
    
    # 启动Redis
    redis-server "$REDIS_CONFIG_FILE" &
    REDIS_PID=$!
    
    # 等待启动
    log_debug "等待Redis启动..."
    for i in {1..30}; do
        if redis-cli -p "$REDIS_PORT" ping &>/dev/null; then
            log_info "Redis启动成功 (PID: $REDIS_PID)"
            break
        fi
        
        if ! kill -0 "$REDIS_PID" 2>/dev/null; then
            log_error "Redis启动失败"
            exit 1
        fi
        
        sleep 1
    done
    
    # 最终检查
    if ! redis-cli -p "$REDIS_PORT" ping &>/dev/null; then
        log_error "Redis启动超时"
        exit 1
    fi
}

# 验证Redis配置
verify_redis_config() {
    log_info "验证Redis配置..."
    
    # 检查持久化设置
    AOF_ENABLED=$(redis-cli -p "$REDIS_PORT" CONFIG GET appendonly | tail -n1)
    log_debug "AOF持久化: $AOF_ENABLED"
    
    # 检查内存策略
    MAXMEMORY_POLICY=$(redis-cli -p "$REDIS_PORT" CONFIG GET maxmemory-policy | tail -n1)
    log_debug "内存策略: $MAXMEMORY_POLICY"
    
    # 检查最大内存
    MAXMEMORY=$(redis-cli -p "$REDIS_PORT" CONFIG GET maxmemory | tail -n1)
    log_debug "最大内存: $MAXMEMORY"
    
    # 检查数据库数量
    DATABASES=$(redis-cli -p "$REDIS_PORT" CONFIG GET databases | tail -n1)
    log_debug "数据库数量: $DATABASES"
    
    log_info "Redis配置验证完成"
}

# 显示状态信息
show_status() {
    log_info "Redis状态信息:"
    echo "----------------------------------------"
    
    # 基本信息
    redis-cli -p "$REDIS_PORT" INFO server | grep -E "redis_version|os|arch_bits|process_id|uptime_in_seconds"
    
    echo ""
    
    # 内存信息
    redis-cli -p "$REDIS_PORT" INFO memory | grep -E "used_memory_human|used_memory_peak_human|maxmemory_human"
    
    echo ""
    
    # 持久化信息
    redis-cli -p "$REDIS_PORT" INFO persistence | grep -E "aof_enabled|rdb_last_save_time|aof_last_rewrite_time_sec"
    
    echo ""
    
    # 连接信息
    redis-cli -p "$REDIS_PORT" INFO clients | grep -E "connected_clients|client_recent_max_input_buffer"
    
    echo "----------------------------------------"
}

# 主函数
main() {
    echo "🚀 MonIt Redis 启动脚本"
    echo "========================================"
    
    # 检查参数
    case "${1:-start}" in
        "start")
            check_redis_installation
            create_directories
            check_config_file
            
            if ! check_port; then
                log_warn "Redis已在运行，是否重启? (y/N)"
                read -r response
                if [[ "$response" =~ ^[Yy]$ ]]; then
                    stop_existing_redis
                else
                    log_info "保持现有Redis实例运行"
                    exit 0
                fi
            fi
            
            optimize_system
            start_redis
            verify_redis_config
            show_status
            
            log_info "Redis启动完成!"
            log_info "连接命令: redis-cli -p $REDIS_PORT"
            ;;
            
        "stop")
            log_info "停止Redis..."
            stop_existing_redis
            log_info "Redis已停止"
            ;;
            
        "restart")
            log_info "重启Redis..."
            stop_existing_redis
            sleep 2
            $0 start
            ;;
            
        "status")
            if redis-cli -p "$REDIS_PORT" ping &>/dev/null; then
                log_info "Redis正在运行"
                show_status
            else
                log_warn "Redis未运行"
                exit 1
            fi
            ;;
            
        "test")
            log_info "测试Redis连接..."
            if redis-cli -p "$REDIS_PORT" ping; then
                log_info "Redis连接正常"
                
                # 测试基本操作
                redis-cli -p "$REDIS_PORT" SET test_key "test_value"
                VALUE=$(redis-cli -p "$REDIS_PORT" GET test_key)
                redis-cli -p "$REDIS_PORT" DEL test_key
                
                if [ "$VALUE" = "test_value" ]; then
                    log_info "Redis读写测试通过"
                else
                    log_error "Redis读写测试失败"
                    exit 1
                fi
            else
                log_error "Redis连接失败"
                exit 1
            fi
            ;;
            
        *)
            echo "用法: $0 {start|stop|restart|status|test}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动Redis服务器"
            echo "  stop    - 停止Redis服务器"
            echo "  restart - 重启Redis服务器"
            echo "  status  - 显示Redis状态"
            echo "  test    - 测试Redis连接"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
