#!/usr/bin/env python3
"""
测试修复后的URL池创建任务配置保存

验证结束时间是否正确保存
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_url_pool_task_creation_fixed():
    """测试修复后的URL池创建任务配置保存"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试修复后的URL池创建任务配置保存")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/url-pool-task-fixed-test",
            "platform": "mercadolibre",
            "title": "修复后URL池任务创建测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'url_pool_task_fixed_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'url_pool_task_fixed_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:url_pool_task_fixed_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 模拟修复后的前端请求（包含end_time）
        print(f"\n📋 步骤2: 模拟修复后的前端请求")
        
        # 修复后的前端应该发送包含end_time的数据
        fixed_request = {
            "name": "修复后URL池任务配置测试",
            "description": "测试修复后的URL池创建任务配置保存",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "16:30",  # 执行时间
                "end_time": "22:15",  # 结束时间（修复后应该包含）
                "timezone": "Asia/Shanghai",
                "max_runs": 30,
                "enable_random_delay": True,
                "random_delay_min": 8,
                "random_delay_max": 20
            },
            "config": {
                "platform": "mercadolibre",
                "priority": "high",
                "batch_size": 20,
                "timeout": 450,
                "retry_count": 4,
                "concurrent_limit": 12,
                "enable_notifications": True
            },
            "tags": ["url_pool_fixed_test"]
        }
        
        print(f"  发送的修复后请求数据:")
        print(f"    基础配置 - 重试次数: {fixed_request['config']['retry_count']}")
        print(f"    基础配置 - 并发限制: {fixed_request['config']['concurrent_limit']}")
        print(f"    调度配置 - 执行时间: {fixed_request['schedule']['time']}")
        print(f"    调度配置 - 结束时间: {fixed_request['schedule']['end_time']} ← 应该保存")
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=fixed_request,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建成功: {task_id}")
            
            # 步骤3: 验证修复效果
            print(f"\n📋 步骤3: 验证修复效果")
            
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if detail_response.status_code == 200:
                detail_result = detail_response.json()
                task_data = detail_result.get('data', {})
                saved_config = task_data.get('config', {})
                saved_schedule = task_data.get('schedule', {})
                
                print(f"  修复后保存的配置:")
                print(f"    基础配置:")
                print(f"      重试次数: {saved_config.get('retry_count')} (期望: 4)")
                print(f"      并发限制: {saved_config.get('concurrent_limit')} (期望: 12)")
                print(f"      批次大小: {saved_config.get('batch_size')} (期望: 20)")
                print(f"      超时时间: {saved_config.get('timeout')} (期望: 450)")
                
                print(f"    调度配置:")
                print(f"      执行时间: {saved_schedule.get('time')} (期望: 16:30)")
                print(f"      结束时间: {saved_schedule.get('end_time')} (期望: 22:15)")
                print(f"      最大执行次数: {saved_schedule.get('max_runs')} (期望: 30)")
                
                # 验证修复效果
                print(f"\n  📊 修复效果验证:")
                
                # 检查基础配置
                config_correct = (
                    saved_config.get('retry_count') == 4 and
                    saved_config.get('concurrent_limit') == 12 and
                    saved_config.get('batch_size') == 20 and
                    saved_config.get('timeout') == 450
                )
                
                # 检查调度配置
                schedule_correct = (
                    saved_schedule.get('time') == '16:30' and
                    saved_schedule.get('end_time') == '22:15' and
                    saved_schedule.get('max_runs') == 30
                )
                
                if config_correct:
                    print(f"    ✅ 基础配置（执行参数）: 完全正确")
                else:
                    print(f"    ❌ 基础配置（执行参数）: 仍有问题")
                
                if schedule_correct:
                    print(f"    ✅ 调度配置（时间设置）: 完全正确")
                    print(f"    🎉 结束时间修复成功！")
                else:
                    print(f"    ❌ 调度配置（时间设置）: 仍有问题")
                    if saved_schedule.get('end_time') != '22:15':
                        print(f"      结束时间仍然有问题: {saved_schedule.get('end_time')}")
                
                # 检查Redis原始数据
                print(f"\n  📋 Redis原始数据:")
                schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
                redis_schedule = r.hgetall(schedule_key)
                print(f"    end_time在Redis中: '{redis_schedule.get('end_time')}'")
                
                if config_correct and schedule_correct:
                    print(f"\n  🎉 所有配置保存问题已修复！")
                else:
                    print(f"\n  ⚠️ 仍有配置保存问题需要进一步修复")
                
            else:
                print(f"  ❌ 获取任务详情失败: {detail_response.text}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 修复后URL池任务创建配置测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:url_pool_task_fixed_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_url_pool_task_creation_fixed()
