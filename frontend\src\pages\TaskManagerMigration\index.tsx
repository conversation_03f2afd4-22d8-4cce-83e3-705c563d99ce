import React from 'react';
import { Card, Button, Steps, Typography, Space, Alert, Divider } from 'antd';
import { 
  DatabaseOutlined, 
  MonitorOutlined, 
  ArrowRightOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import './index.css';

const { Title, Paragraph, Text } = Typography;

const TaskManagerMigration: React.FC = () => {
  const navigate = useNavigate();

  const migrationSteps = [
    {
      title: 'Excel上传与URL管理',
      description: '上传Excel文件，管理URL池',
      icon: <DatabaseOutlined />,
      path: '/url-pool',
      status: 'process'
    },
    {
      title: '创建监控任务',
      description: '从URL池选择链接创建任务',
      icon: <MonitorOutlined />,
      path: '/monitoring',
      status: 'wait'
    }
  ];

  const handleNavigateToStep = (path: string) => {
    navigate(path);
  };

  return (
    <div className="task-manager-migration">
      <div className="migration-header">
        <Title level={2}>
          <InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
          任务管理系统已升级
        </Title>
        <Paragraph>
          为了提供更好的用户体验和更强大的功能，我们已将任务管理系统升级为新的工作流程。
        </Paragraph>
      </div>

      <Alert
        message="重要提示"
        description="旧的任务管理页面已被新的工作流程替代。新流程提供了更好的URL管理、任务调度和监控功能。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Card title="新的工作流程" className="workflow-card">
        <Steps
          direction="vertical"
          current={0}
          items={migrationSteps.map((step, index) => ({
            title: step.title,
            description: step.description,
            icon: step.icon,
            status: step.status as any
          }))}
        />

        <Divider />

        <div className="workflow-details">
          <Title level={4}>新工作流程的优势</Title>
          <ul>
            <li>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              <Text>更好的URL池管理 - 支持批量操作、状态跟踪、平台分类</Text>
            </li>
            <li>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              <Text>强大的任务调度 - 支持定时执行、重复任务、智能分配</Text>
            </li>
            <li>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              <Text>实时监控 - 任务进度跟踪、Worker状态监控、告警通知</Text>
            </li>
            <li>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
              <Text>数据持久化 - Redis存储、任务恢复、历史记录</Text>
            </li>
          </ul>
        </div>

        <Divider />

        <div className="action-buttons">
          <Space size="large">
            <Button 
              type="primary" 
              size="large"
              icon={<DatabaseOutlined />}
              onClick={() => handleNavigateToStep('/url-pool')}
            >
              开始使用 - URL池管理
            </Button>
            <Button 
              size="large"
              icon={<MonitorOutlined />}
              onClick={() => handleNavigateToStep('/monitoring')}
            >
              查看任务管理
            </Button>
          </Space>
        </div>
      </Card>

      <Card title="快速开始指南" style={{ marginTop: 24 }}>
        <div className="quick-start-guide">
          <Title level={5}>1. 上传Excel文件</Title>
          <Paragraph>
            前往 <Text code>URL池管理</Text> 页面，上传包含商品链接的Excel文件。
            系统会自动解析URL并添加到URL池中。
          </Paragraph>

          <Title level={5}>2. 创建监控任务</Title>
          <Paragraph>
            在URL池中选择要监控的链接，点击 <Text code>创建任务</Text> 按钮，
            配置任务参数（调度时间、执行频率等）。
          </Paragraph>

          <Title level={5}>3. 监控任务执行</Title>
          <Paragraph>
            在 <Text code>任务管理</Text> 页面查看任务状态、执行进度和结果。
            支持手动执行、暂停/恢复、编辑配置等操作。
          </Paragraph>
        </div>
      </Card>
    </div>
  );
};

export default TaskManagerMigration;
