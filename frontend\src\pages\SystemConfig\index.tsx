/**
 * 系统配置页面
 * 显示系统级配置信息（只读）
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Alert,
  Spin,
  Typography,
  Space,
  Tag,
  Divider
} from 'antd';
import {
  SettingOutlined,
  InfoCircleOutlined,
  ApiOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { crawlConfigApi } from '../../services/crawlConfigApi';

const { Title, Text } = Typography;

interface SystemConfig {
  crawler_system_config: {
    max_concurrent_tasks: number;
    batch_timeout: number;
    default_callback_url: string;
    api_base_url: string;
    api_timeout: number;
    api_max_retries: number;
    rate_limit_requests: number;
    rate_limit_period: number;
  };
  environment: string;
  version: string;
}

const SystemConfig: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [systemConfig, setSystemConfig] = useState<SystemConfig | null>(null);

  // 加载系统配置
  const loadSystemConfig = async () => {
    try {
      setLoading(true);
      const response = await crawlConfigApi.getSystemConfig();
      if (response.status === 'success') {
        setSystemConfig(response.data);
      }
    } catch (error) {
      console.error('加载系统配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSystemConfig();
  }, []);

  const formatTimeout = (seconds: number) => {
    if (seconds < 60) return `${seconds}秒`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`;
    return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={2}>
            <SettingOutlined /> 系统配置
          </Title>
          <Text type="secondary">
            查看系统级配置参数，这些配置由系统管理员通过环境变量管理
          </Text>
        </div>

        <Alert
          message="配置说明"
          description="这些是系统级配置参数，已从用户配置界面中移除。如需修改，请联系系统管理员更新环境变量配置。"
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
        />

        <Spin spinning={loading} tip="加载系统配置中...">
          {systemConfig && (
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 基本信息 */}
              <Card title="系统信息" size="small">
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="系统版本">
                    <Tag color="blue">{systemConfig.version}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="运行环境">
                    <Tag color={systemConfig.environment === 'production' ? 'red' : 'green'}>
                      {systemConfig.environment}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Card>

              {/* 爬虫配置 */}
              <Card 
                title={
                  <Space>
                    <ApiOutlined />
                    爬虫系统配置
                  </Space>
                } 
                size="small"
              >
                <Descriptions column={2} size="small" bordered>
                  <Descriptions.Item label="最大并发任务数">
                    <Text strong>{systemConfig.crawler_system_config.max_concurrent_tasks}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="批次超时时间">
                    <Space>
                      <ClockCircleOutlined />
                      <Text>{formatTimeout(systemConfig.crawler_system_config.batch_timeout)}</Text>
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="默认回调URL" span={2}>
                    <Text code>
                      {systemConfig.crawler_system_config.default_callback_url || '未设置'}
                    </Text>
                  </Descriptions.Item>
                </Descriptions>

                <Divider />

                <Descriptions column={2} size="small" bordered>
                  <Descriptions.Item label="API基础URL" span={2}>
                    <Text code>{systemConfig.crawler_system_config.api_base_url}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="API超时时间">
                    <Text>{systemConfig.crawler_system_config.api_timeout}秒</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="最大重试次数">
                    <Text>{systemConfig.crawler_system_config.api_max_retries}次</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="速率限制">
                    <Text>
                      {systemConfig.crawler_system_config.rate_limit_requests}请求/
                      {systemConfig.crawler_system_config.rate_limit_period}秒
                    </Text>
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Space>
          )}
        </Spin>
      </Space>
    </div>
  );
};

export default SystemConfig;
