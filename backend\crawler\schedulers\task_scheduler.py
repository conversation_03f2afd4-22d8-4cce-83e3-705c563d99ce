"""
任务调度器

提供任务调度和管理功能。
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """任务数据类"""
    task_id: str
    url: str
    platform: str
    priority: int = 0
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    started_at: datetime = None
    completed_at: datetime = None
    result: Any = None
    error: str = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class TaskScheduler:
    """任务调度器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化任务调度器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.pending_tasks: List[Task] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        # 调度器设置
        self.max_concurrent_tasks = config.get('max_concurrent_tasks', 10)
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        
        # 状态
        self.is_running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        
        self.logger.info("TaskScheduler initialized")
    
    async def start(self) -> None:
        """启动任务调度器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        self.logger.info("TaskScheduler started")
    
    async def stop(self) -> None:
        """停止任务调度器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消调度器任务
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有运行中的任务
        for task in self.running_tasks.values():
            task.cancel()
        
        # 等待所有任务完成
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        self.logger.info("TaskScheduler stopped")
    
    async def add_task(
        self, 
        task_id: str,
        url: str,
        platform: str = "default",
        priority: int = 0,
        **kwargs
    ) -> bool:
        """添加任务
        
        Args:
            task_id: 任务ID
            url: 目标URL
            platform: 平台类型
            priority: 优先级
            **kwargs: 额外参数
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if task_id in self.tasks:
                self.logger.warning(f"Task {task_id} already exists")
                return False
            
            task = Task(
                task_id=task_id,
                url=url,
                platform=platform,
                priority=priority,
                **kwargs
            )
            
            self.tasks[task_id] = task
            self.pending_tasks.append(task)
            
            # 按优先级排序
            self.pending_tasks.sort(key=lambda t: t.priority, reverse=True)
            
            self.logger.info(f"Task {task_id} added to queue")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add task {task_id}: {e}")
            return False
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务结果
        """
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        return {
            'task_id': task.task_id,
            'status': task.status.value,
            'result': task.result,
            'error': task.error,
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'retry_count': task.retry_count
        }
    
    async def get_scheduler_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'total_tasks': len(self.tasks),
            'pending_tasks': len(self.pending_tasks),
            'running_tasks': len(self.running_tasks),
            'completed_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]),
            'failed_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
            'is_running': self.is_running,
            'max_concurrent_tasks': self.max_concurrent_tasks
        }
    
    async def _scheduler_loop(self) -> None:
        """调度器主循环"""
        while self.is_running:
            try:
                # 检查是否有待处理的任务
                if self.pending_tasks and len(self.running_tasks) < self.max_concurrent_tasks:
                    task = self.pending_tasks.pop(0)
                    await self._start_task(task)
                
                # 清理已完成的任务
                await self._cleanup_completed_tasks()
                
                # 短暂休眠
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Scheduler loop error: {e}")
                await asyncio.sleep(1.0)
    
    async def _start_task(self, task: Task) -> None:
        """启动任务
        
        Args:
            task: 任务对象
        """
        try:
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # 创建异步任务
            async_task = asyncio.create_task(self._execute_task(task))
            self.running_tasks[task.task_id] = async_task
            
            self.logger.info(f"Task {task.task_id} started")
            
        except Exception as e:
            self.logger.error(f"Failed to start task {task.task_id}: {e}")
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.completed_at = datetime.now()
    
    async def _execute_task(self, task: Task) -> None:
        """执行任务
        
        Args:
            task: 任务对象
        """
        async with self.semaphore:
            try:
                # 这里应该调用实际的爬取逻辑
                # 为了测试，我们模拟一个简单的任务执行
                await asyncio.sleep(1.0)  # 模拟任务执行时间
                
                task.result = {
                    'url': task.url,
                    'platform': task.platform,
                    'data': f"Mock data for {task.url}"
                }
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                
                self.logger.info(f"Task {task.task_id} completed successfully")
                
            except Exception as e:
                self.logger.error(f"Task {task.task_id} failed: {e}")
                task.status = TaskStatus.FAILED
                task.error = str(e)
                task.completed_at = datetime.now()
                
                # 重试逻辑
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.status = TaskStatus.PENDING
                    self.pending_tasks.append(task)
                    self.logger.info(f"Task {task.task_id} scheduled for retry ({task.retry_count}/{task.max_retries})")
    
    async def _cleanup_completed_tasks(self) -> None:
        """清理已完成的任务"""
        completed_task_ids = []
        
        for task_id, async_task in self.running_tasks.items():
            if async_task.done():
                completed_task_ids.append(task_id)
        
        for task_id in completed_task_ids:
            del self.running_tasks[task_id] 