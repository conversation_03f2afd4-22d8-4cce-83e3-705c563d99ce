#!/usr/bin/env python3
"""
测试确认步骤的调度配置显示

模拟前端确认步骤的数据结构，检查执行时间显示问题
"""

import json


def test_confirm_step_display():
    """测试确认步骤的调度配置显示"""
    
    print("🔍 测试确认步骤的调度配置显示")
    print("=" * 60)
    
    # 模拟用户在调度配置步骤中设置的数据
    print("\n📋 步骤1: 模拟调度配置步骤的数据")
    
    # 这是ScheduleConfigStep.tsx中handleFormChange生成的数据结构
    schedule_config_from_step = {
        "type": "daily",
        "enabled": True,
        "timezone": "Asia/Shanghai",
        "time": "14:30",  # daily类型使用time字段
        "start_time": None,  # daily类型时为None
        "end_time": "22:00",
        "interval": None,
        "cron_expression": None,
        "max_runs": 10,
        "enable_random_delay": True,
        "random_delay_min": 5,
        "random_delay_max": 15
    }
    
    print(f"  调度配置数据结构:")
    print(f"    type: {schedule_config_from_step['type']}")
    print(f"    enabled: {schedule_config_from_step['enabled']}")
    print(f"    time: {schedule_config_from_step['time']}")
    print(f"    start_time: {schedule_config_from_step['start_time']}")
    print(f"    end_time: {schedule_config_from_step['end_time']}")
    
    # 步骤2: 模拟ConfirmStep.tsx中的显示逻辑
    print(f"\n📋 步骤2: 模拟ConfirmStep.tsx中的显示逻辑")
    
    # 模拟ConfirmStep.tsx第304行的条件判断
    has_execution_time = (schedule_config_from_step.get('time') or schedule_config_from_step.get('start_time'))
    print(f"  执行时间显示条件: (time || start_time) = {has_execution_time}")
    print(f"    time值: {schedule_config_from_step.get('time')}")
    print(f"    start_time值: {schedule_config_from_step.get('start_time')}")
    
    if has_execution_time:
        # 模拟第306-310行的显示逻辑
        display_time = schedule_config_from_step.get('time')
        if not display_time and schedule_config_from_step.get('start_time'):
            try:
                from datetime import datetime
                start_time_obj = datetime.fromisoformat(schedule_config_from_step['start_time'])
                display_time = start_time_obj.strftime('%H:%M')
            except:
                display_time = schedule_config_from_step['start_time']
        
        print(f"  ✅ 应该显示执行时间: {display_time}")
    else:
        print(f"  ❌ 不会显示执行时间（条件不满足）")
    
    # 步骤3: 模拟generateScheduleDescription函数
    print(f"\n📋 步骤3: 模拟generateScheduleDescription函数")
    
    def mock_generate_schedule_description(schedule):
        if not schedule.get('enabled'):
            return '调度已禁用'
        
        schedule_type = schedule.get('type')
        if schedule_type == 'daily':
            if schedule.get('time'):
                return f"每日执行，时间：{schedule['time']}"
            elif schedule.get('start_time'):
                # 兼容旧格式
                try:
                    from datetime import datetime
                    time_obj = datetime.fromisoformat(schedule['start_time'])
                    time_str = time_obj.strftime('%H:%M')
                    return f"每日执行，时间：{time_str}"
                except:
                    return f"每日执行，时间：{schedule['start_time']}"
            return '每日执行'
        
        return f'{schedule_type}类型调度'
    
    schedule_description = mock_generate_schedule_description(schedule_config_from_step)
    print(f"  调度描述: {schedule_description}")
    
    # 步骤4: 检查可能的问题
    print(f"\n📋 步骤4: 检查可能的问题")
    
    issues = []
    
    # 检查time字段是否为None或空字符串
    if not schedule_config_from_step.get('time'):
        issues.append("time字段为空或None")
    
    # 检查start_time字段
    if schedule_config_from_step.get('start_time') is None:
        issues.append("start_time字段为None（这对daily类型是正常的）")
    
    # 检查数据类型
    if isinstance(schedule_config_from_step.get('time'), str) and schedule_config_from_step['time'].strip() == '':
        issues.append("time字段是空字符串")
    
    if issues:
        print(f"  发现的问题:")
        for issue in issues:
            print(f"    - {issue}")
    else:
        print(f"  ✅ 数据结构看起来正常")
    
    # 步骤5: 模拟可能的错误情况
    print(f"\n📋 步骤5: 模拟可能的错误情况")
    
    # 情况1: time字段丢失
    broken_config_1 = schedule_config_from_step.copy()
    broken_config_1['time'] = None
    
    has_time_1 = (broken_config_1.get('time') or broken_config_1.get('start_time'))
    print(f"  情况1 - time字段为None:")
    print(f"    显示条件满足: {has_time_1}")
    print(f"    调度描述: {mock_generate_schedule_description(broken_config_1)}")
    
    # 情况2: time字段为空字符串
    broken_config_2 = schedule_config_from_step.copy()
    broken_config_2['time'] = ''
    
    has_time_2 = (broken_config_2.get('time') or broken_config_2.get('start_time'))
    print(f"  情况2 - time字段为空字符串:")
    print(f"    显示条件满足: {has_time_2}")
    print(f"    调度描述: {mock_generate_schedule_description(broken_config_2)}")
    
    # 情况3: 两个字段都为空
    broken_config_3 = schedule_config_from_step.copy()
    broken_config_3['time'] = None
    broken_config_3['start_time'] = None
    
    has_time_3 = (broken_config_3.get('time') or broken_config_3.get('start_time'))
    print(f"  情况3 - 两个字段都为空:")
    print(f"    显示条件满足: {has_time_3}")
    print(f"    调度描述: {mock_generate_schedule_description(broken_config_3)}")
    
    # 步骤6: 建议的修复方案
    print(f"\n📋 步骤6: 问题分析和修复建议")
    
    if schedule_config_from_step.get('time'):
        print(f"  ✅ 正常情况: time字段有值，应该正确显示")
        print(f"  💡 如果前端仍显示'未设置'，可能的原因:")
        print(f"    1. 前端组件状态更新问题")
        print(f"    2. 数据传递过程中丢失")
        print(f"    3. 条件判断逻辑有bug")
        print(f"    4. 渲染时机问题")
    else:
        print(f"  ❌ 异常情况: time字段为空")
        print(f"  🔧 需要检查ScheduleConfigStep中的数据处理")
    
    print(f"\n🎉 确认步骤显示测试完成!")
    
    return schedule_config_from_step


if __name__ == "__main__":
    test_confirm_step_display()
