#!/usr/bin/env python3
"""
测试Celery Worker显示修复
"""

import requests
import json
import time

def test_celery_worker_display():
    """测试Celery Worker显示修复"""
    
    # 1. 创建URL池
    print("1. 创建测试URL池...")
    url_pool_data = {
        "urls": [
            "https://httpbin.org/delay/1",
            "https://httpbin.org/json"
        ],
        "tags": ["test"],
        "platform": "test"
    }
    
    response = requests.post("http://localhost:8000/api/v1/url-pool", json=url_pool_data)
    if response.status_code != 200:
        print(f"创建URL池失败: {response.status_code} - {response.text}")
        return
    
    print("URL池创建成功")
    
    # 2. 创建监控任务
    print("2. 创建监控任务...")
    task_data = {
        "name": "测试Celery Worker显示",
        "description": "测试修复后的Celery Worker显示功能",
        "urls": url_pool_data["urls"],
        "tags": ["test"],
        "platform": "test",
        "schedule": {
            "type": "immediate"
        },
        "crawler_config": {
            "name": "test_config",
            "base_url": "http://localhost:11235",
            "timeout": 30.0,
            "max_retries": 3,
            "auth_type": "none"
        }
    }
    
    response = requests.post("http://localhost:8000/api/v1/url-pool/create-task", json=task_data)
    if response.status_code != 200:
        print(f"创建任务失败: {response.status_code} - {response.text}")
        return
    
    task_result = response.json()
    print(f"任务创建成功: {task_result}")
    
    # 3. 等待任务执行
    print("3. 等待任务执行...")
    time.sleep(5)
    
    # 4. 检查爬虫请求记录
    print("4. 检查爬虫请求记录...")
    response = requests.get("http://localhost:8000/api/v1/crawler-debug/requests?limit=5")
    if response.status_code == 200:
        requests_data = response.json()
        print(f"获取到 {len(requests_data)} 条记录")
        
        for req in requests_data:
            print(f"请求ID: {req['id']}")
            print(f"URL: {req['url']}")
            print(f"Worker ID: {req['worker_id']}")
            print(f"Celery Worker: {req['celery_worker']}")
            print(f"状态: {req['status']}")
            print("---")
            
            if req['celery_worker'] and req['celery_worker'] != 'null':
                print("✅ Celery Worker显示修复成功!")
                return True
    else:
        print(f"获取请求记录失败: {response.status_code}")
    
    print("❌ Celery Worker仍然显示为空")
    return False

if __name__ == "__main__":
    test_celery_worker_display()
