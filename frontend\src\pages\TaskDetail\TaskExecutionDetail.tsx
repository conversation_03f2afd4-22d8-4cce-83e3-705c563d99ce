import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Badge,
  Progress,
  Table,
  Tag,
  Tooltip,
  Statistic,
  Descriptions,
  Modal
} from 'antd';
import {
  ReloadOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  BugOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { useTaskExecutionState, UrlExecutionStatus } from '../../hooks/useTaskExecutionState';

const { Title, Text } = Typography;

const TaskExecutionDetail: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const [selectedUrl, setSelectedUrl] = useState<UrlExecutionStatus | null>(null);

  const {
    loading,
    error,
    refresh,
    urlStatuses,
    summary,
    lastUpdate
  } = useTaskExecutionState({
    taskId,
    autoConnect: true,
    pollInterval: 3000,
    onUrlStatusChange: (url, status) => {
      console.log(`URL ${url} status changed to ${status.status}`);
    },
    onTaskProgress: (progress) => {
      console.log(`Task progress: ${progress.percentage}%`);
    }
  });

  // URL状态表格列定义
  const urlColumns = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code style={{ fontSize: '12px' }}>
            {url.length > 60 ? `${url.substring(0, 60)}...` : url}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
          processing: { color: 'processing', icon: <LoadingOutlined />, text: '处理中' },
          completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
          failed: { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
        return (
          <Badge
            status={config.color as any}
            text={
              <Space>
                {config.icon}
                {config.text}
              </Space>
            }
          />
        );
      }
    },
    {
      title: 'Worker',
      dataIndex: 'workerId',
      key: 'workerId',
      width: 120,
      render: (workerId: string) => workerId ? <Tag color="blue">{workerId}</Tag> : '-'
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 100,
      render: (time: number) => time ? `${time.toFixed(2)}s` : '-'
    },
    {
      title: '开始时间',
      dataIndex: 'startedAt',
      key: 'startedAt',
      width: 160,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '完成时间',
      dataIndex: 'completedAt',
      key: 'completedAt',
      width: 160,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_: any, record: UrlExecutionStatus) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => setSelectedUrl(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  // 获取状态统计
  const getStatusStats = () => {
    return [
      { label: '总计', value: summary.total, color: '#1890ff' },
      { label: '等待中', value: summary.pending, color: '#faad14' },
      { label: '处理中', value: summary.processing, color: '#52c41a' },
      { label: '已完成', value: summary.completed, color: '#52c41a' },
      { label: '失败', value: summary.failed, color: '#ff4d4f' }
    ];
  };

  // 计算进度百分比
  const getProgressPercentage = () => {
    if (summary.total === 0) return 0;
    return Math.round(((summary.completed + summary.failed) / summary.total) * 100);
  };

  // URL详情模态框
  const UrlDetailModal = () => (
    <Modal
      title="URL执行详情"
      open={!!selectedUrl}
      onCancel={() => setSelectedUrl(null)}
      footer={[
        <Button key="debug" icon={<BugOutlined />} onClick={() => window.open(`/crawler-debug?task_id=${taskId}`, '_blank')}>
          调试信息
        </Button>,
        <Button key="close" onClick={() => setSelectedUrl(null)}>
          关闭
        </Button>
      ]}
      width={800}
    >
      {selectedUrl && (
        <div>
          <Descriptions column={2} bordered size="small">
            <Descriptions.Item label="URL">{selectedUrl.url}</Descriptions.Item>
            <Descriptions.Item label="状态">{selectedUrl.status}</Descriptions.Item>
            <Descriptions.Item label="Worker ID">{selectedUrl.workerId || '-'}</Descriptions.Item>
            <Descriptions.Item label="响应时间">{selectedUrl.responseTime ? `${selectedUrl.responseTime.toFixed(2)}s` : '-'}</Descriptions.Item>
            <Descriptions.Item label="开始时间">{selectedUrl.startedAt ? new Date(selectedUrl.startedAt).toLocaleString() : '-'}</Descriptions.Item>
            <Descriptions.Item label="完成时间">{selectedUrl.completedAt ? new Date(selectedUrl.completedAt).toLocaleString() : '-'}</Descriptions.Item>
            <Descriptions.Item label="是否成功">{selectedUrl.success ? '是' : '否'}</Descriptions.Item>
            <Descriptions.Item label="数据提取">{selectedUrl.dataExtracted ? '成功' : '失败'}</Descriptions.Item>
          </Descriptions>

          {selectedUrl.error && (
            <Alert
              message="错误信息"
              description={selectedUrl.error}
              type="error"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}

          {selectedUrl.result && (
            <div style={{ marginTop: 16 }}>
              <Text strong>执行结果:</Text>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: 12, 
                borderRadius: 4, 
                marginTop: 8,
                maxHeight: 200,
                overflow: 'auto'
              }}>
                {JSON.stringify(selectedUrl.result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </Modal>
  );

  if (error) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={refresh}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: 24, background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            任务执行详情
          </Title>
          <Text type="secondary">
            任务ID: {taskId} | 最后更新: {new Date(lastUpdate).toLocaleString()}
          </Text>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={refresh}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              icon={<BugOutlined />}
              onClick={() => window.open(`/crawler-debug?task_id=${taskId}`, '_blank')}
            >
              调试工具
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card>
            <Statistic
              title="执行进度"
              value={getProgressPercentage()}
              suffix="%"
              prefix={<TeamOutlined />}
            />
            <Progress
              percent={getProgressPercentage()}
              status={summary.failed > 0 ? 'exception' : 'active'}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Row gutter={16}>
              {getStatusStats().map((stat, index) => (
                <Col span={4} key={index}>
                  <Statistic
                    title={stat.label}
                    value={stat.value}
                    valueStyle={{ color: stat.color, fontSize: '16px' }}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* URL执行状态表格 */}
      <Card title="URL执行状态" loading={loading}>
        <Table
          columns={urlColumns}
          dataSource={Object.values(urlStatuses)}
          rowKey="url"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
      </Card>

      <UrlDetailModal />
    </div>
  );
};

export default TaskExecutionDetail;
