// 新架构API服务
export { default as crawlerConfigApi } from './crawlerConfigApi';
export { default as newCrawlerApi } from './newCrawlerApi';
export { default as backendConfigApi } from './backendConfigApi';
export { default as workerApi } from './workerApi';

// 导出类型
export * from '../types/newArchitecture';

// 单独导出其他API以避免冲突
export * from './urlPoolApi';

// 监控任务API
export {
  getMonitoringTasks,
  startMonitoringTask,
  pauseMonitoringTask,
  deleteMonitoringTask,
  type MonitoringTask
} from './monitoringTaskApi';

// 任务创建API
export * from './taskCreateApi';
