#!/usr/bin/env python3
"""
测试任务创建向导的完整流程

创建一个任务并检查前端向导的显示效果
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_task_creation_wizard():
    """测试任务创建向导的完整流程"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试任务创建向导的完整流程")
    print("=" * 60)
    
    created_url_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_urls = [
            {
                "url": "https://www.mercadolibre.com.ar/wizard-test-1",
                "platform": "mercadolibre",
                "title": "任务创建向导测试URL 1",
            },
            {
                "url": "https://www.mercadolibre.com.ar/wizard-test-2", 
                "platform": "mercadolibre",
                "title": "任务创建向导测试URL 2",
            }
        ]
        
        for i, test_url in enumerate(test_urls, 1):
            # 生成URL ID和哈希
            url_id = str(uuid4())
            url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
            now = datetime.now().isoformat()
            
            # 创建URL项目数据
            url_item_data = {
                'id': url_id,
                'url': test_url['url'],
                'platform': test_url['platform'],
                'source_file': 'wizard_test',
                'added_at': now,
                'status': 'active',
                'last_check': '',
                'check_count': '0',
                'success_count': '0',
                'error_count': '0',
                'metadata': json.dumps({
                    'url_hash': url_hash,
                    'title': test_url.get('title', ''),
                    'added_via': 'wizard_test'
                })
            }
            
            # 存储到Redis
            url_key = f"url_pool:items:{url_id}"
            r.hset(url_key, mapping=url_item_data)
            
            # 添加到索引
            r.sadd("url_pool:all_ids", url_id)
            r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
            r.sadd("url_pool:status:active", url_id)
            r.sadd("url_pool:source:wizard_test", url_id)
            r.sadd("url_pool:url_hashes", url_hash)
            
            created_url_ids.append(url_id)
            print(f"  ✅ URL {i} 创建成功: {url_id}")
        
        # 步骤2: 提供前端测试信息
        print(f"\n📋 步骤2: 前端测试信息")
        
        print(f"  🌐 前端URL池页面: http://localhost:3000/url-pool")
        print(f"  📝 测试URL数量: {len(created_url_ids)}")
        print(f"  💡 请在前端执行以下测试步骤:")
        
        print(f"\n  📋 测试步骤:")
        print(f"    1. 访问 http://localhost:3000/url-pool")
        print(f"    2. 在URL池中找到测试URL（搜索'wizard-test'）")
        print(f"    3. 选择测试URL")
        print(f"    4. 点击'创建任务'按钮")
        print(f"    5. 在任务创建向导中:")
        print(f"       - 基础配置: 设置任务名称、重试次数、并发限制等")
        print(f"       - 调度配置: 设置每日执行时间（如 15:30）和结束时间（如 23:00）")
        print(f"       - 确认步骤: 检查调度配置是否正确显示")
        
        print(f"\n  🔍 重点检查项目:")
        print(f"    ✅ 确认步骤中的调度配置应该显示:")
        print(f"       - 调度类型: 每天")
        print(f"       - 执行时间: 你设置的时间（如 15:30）← 重点检查")
        print(f"       - 结束时间: 你设置的时间（如 23:00）")
        print(f"       - 时区: Asia/Shanghai")
        
        print(f"\n  ⚠️ 如果仍显示'执行时间: 未设置'，请检查:")
        print(f"    1. 浏览器开发者工具的Console是否有错误")
        print(f"    2. 调试信息中的time字段值")
        print(f"    3. 是否正确选择了每日执行类型")
        print(f"    4. 是否正确设置了执行时间")
        
        # 步骤3: 提供调试信息
        print(f"\n📋 步骤3: 调试信息")
        
        print(f"  📊 期望的数据结构:")
        expected_schedule_config = {
            "type": "daily",
            "enabled": True,
            "time": "15:30",  # 用户设置的执行时间
            "start_time": None,  # daily类型时应该为None
            "end_time": "23:00",  # 用户设置的结束时间
            "timezone": "Asia/Shanghai",
            "max_runs": None,
            "enable_random_delay": False
        }
        
        print(f"    {json.dumps(expected_schedule_config, indent=2, ensure_ascii=False)}")
        
        print(f"\n  🔧 如果确认步骤显示有问题，可能的原因:")
        print(f"    1. ScheduleConfigStep → ConfirmStep 数据传递问题")
        print(f"    2. 组件状态更新时机问题")
        print(f"    3. 条件判断逻辑问题")
        print(f"    4. 数据格式转换问题")
        
        # 等待用户测试
        print(f"\n💡 请按照上述步骤进行前端测试")
        input("测试完成后按回车键继续清理测试数据...")
        
        print(f"\n🎉 任务创建向导测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:wizard_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_task_creation_wizard()
