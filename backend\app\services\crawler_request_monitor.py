"""
爬虫请求构建监控服务
提供详细的监控指标、性能追踪和日志记录
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager
from dataclasses import dataclass, asdict
import redis.asyncio as redis

logger = logging.getLogger(__name__)


@dataclass
class RequestBuildMetrics:
    """请求构建指标"""
    task_id: str
    execution_id: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status: str = "building"  # building, success, validation_error, api_error, failed
    urls_count: int = 0
    config_source: str = ""
    error_message: str = ""
    
    # 详细的性能指标
    url_fetch_time: Optional[float] = None
    config_fetch_time: Optional[float] = None
    system_config_time: Optional[float] = None
    request_build_time: Optional[float] = None
    
    # 数据质量指标
    active_urls_ratio: Optional[float] = None  # 活跃URL比例
    config_completeness: Optional[float] = None  # 配置完整性
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    def calculate_duration(self):
        """计算总耗时"""
        if self.end_time and self.start_time:
            self.duration = self.end_time - self.start_time
    
    def mark_completed(self, status: str, error_message: str = ""):
        """标记完成"""
        self.end_time = time.time()
        self.status = status
        self.error_message = error_message
        self.calculate_duration()


class CrawlerRequestMonitor:
    """爬虫请求构建监控器"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.redis_url = redis_url
        self.redis_client = None
        
        # 监控配置
        self.metrics_retention_days = 7  # 指标保留天数
        self.log_level = logging.INFO
        
        # 性能阈值
        self.performance_thresholds = {
            "url_fetch_warning": 2.0,  # URL获取超过2秒警告
            "config_fetch_warning": 1.0,  # 配置获取超过1秒警告
            "total_build_warning": 5.0,  # 总构建时间超过5秒警告
            "urls_count_warning": 100,  # URL数量超过100个警告
        }
    
    async def get_redis_client(self):
        """获取Redis客户端"""
        if not self.redis_client:
            self.redis_client = redis.from_url(self.redis_url)
        return self.redis_client
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
    
    @asynccontextmanager
    async def monitor_request_building(
        self, 
        task_id: str, 
        execution_id: str,
        context: Optional[Dict[str, Any]] = None
    ):
        """监控请求构建过程的上下文管理器"""
        
        # 创建指标对象
        metrics = RequestBuildMetrics(
            task_id=task_id,
            execution_id=execution_id,
            start_time=time.time()
        )
        
        # 记录开始日志
        logger.info(f"🚀 开始构建爬虫请求 - 任务: {task_id}, 执行: {execution_id}")
        if context:
            logger.info(f"📋 执行上下文: {json.dumps(context, ensure_ascii=False)}")
        
        try:
            yield metrics
            
            # 成功完成
            metrics.mark_completed("success")
            
            # 记录成功日志
            logger.info(f"✅ 爬虫请求构建成功 - 任务: {task_id}")
            logger.info(f"📊 性能指标: 总耗时 {metrics.duration:.3f}s, URL数量 {metrics.urls_count}")
            
            # 检查性能阈值
            await self._check_performance_thresholds(metrics)
            
        except Exception as e:
            # 失败处理
            error_type = type(e).__name__
            if "validation" in str(e).lower() or "no active urls" in str(e).lower():
                metrics.mark_completed("validation_error", str(e))
                logger.warning(f"⚠️ 爬虫请求构建验证失败 - 任务: {task_id}, 错误: {e}")
            elif "api" in str(e).lower():
                metrics.mark_completed("api_error", str(e))
                logger.error(f"🔌 爬虫API调用失败 - 任务: {task_id}, 错误: {e}")
            else:
                metrics.mark_completed("failed", str(e))
                logger.error(f"❌ 爬虫请求构建失败 - 任务: {task_id}, 错误: {e}")
            
            raise
        
        finally:
            # 保存指标到Redis
            await self._save_metrics(metrics)
            
            # 记录详细的性能分析
            await self._log_performance_analysis(metrics)
    
    async def _check_performance_thresholds(self, metrics: RequestBuildMetrics):
        """检查性能阈值并发出警告"""
        
        warnings = []
        
        # 检查URL获取时间
        if metrics.url_fetch_time and metrics.url_fetch_time > self.performance_thresholds["url_fetch_warning"]:
            warnings.append(f"URL获取耗时过长: {metrics.url_fetch_time:.3f}s")
        
        # 检查配置获取时间
        if metrics.config_fetch_time and metrics.config_fetch_time > self.performance_thresholds["config_fetch_warning"]:
            warnings.append(f"配置获取耗时过长: {metrics.config_fetch_time:.3f}s")
        
        # 检查总构建时间
        if metrics.duration and metrics.duration > self.performance_thresholds["total_build_warning"]:
            warnings.append(f"总构建时间过长: {metrics.duration:.3f}s")
        
        # 检查URL数量
        if metrics.urls_count > self.performance_thresholds["urls_count_warning"]:
            warnings.append(f"URL数量过多: {metrics.urls_count}个")
        
        # 记录警告
        if warnings:
            logger.warning(f"⚠️ 性能警告 - 任务: {metrics.task_id}")
            for warning in warnings:
                logger.warning(f"  • {warning}")
    
    async def _save_metrics(self, metrics: RequestBuildMetrics):
        """保存指标到Redis"""
        try:
            redis_client = await self.get_redis_client()
            
            # 保存到时间序列键
            date_key = datetime.now().strftime('%Y%m%d')
            metrics_key = f"crawler_request_metrics:{date_key}"
            
            # 保存指标数据
            metrics_data = metrics.to_dict()
            await redis_client.lpush(metrics_key, json.dumps(metrics_data))
            
            # 设置过期时间
            await redis_client.expire(metrics_key, self.metrics_retention_days * 24 * 3600)
            
            # 保存到任务特定的键
            task_metrics_key = f"task_metrics:{metrics.task_id}"
            await redis_client.lpush(task_metrics_key, json.dumps(metrics_data))
            await redis_client.expire(task_metrics_key, 24 * 3600)  # 24小时过期
            
            logger.debug(f"📊 指标已保存到Redis: {metrics_key}")
            
        except Exception as e:
            logger.error(f"保存指标到Redis失败: {e}")
    
    async def _log_performance_analysis(self, metrics: RequestBuildMetrics):
        """记录详细的性能分析"""
        
        logger.info(f"📈 请求构建性能分析 - 任务: {metrics.task_id}")
        logger.info(f"  🕐 总耗时: {metrics.duration:.3f}s")
        
        if metrics.url_fetch_time:
            logger.info(f"  🌐 URL获取: {metrics.url_fetch_time:.3f}s")
        if metrics.config_fetch_time:
            logger.info(f"  ⚙️ 配置获取: {metrics.config_fetch_time:.3f}s")
        if metrics.system_config_time:
            logger.info(f"  🔧 系统配置: {metrics.system_config_time:.3f}s")
        if metrics.request_build_time:
            logger.info(f"  🔨 请求构建: {metrics.request_build_time:.3f}s")
        
        logger.info(f"  📊 数据质量:")
        logger.info(f"    • URL数量: {metrics.urls_count}")
        if metrics.active_urls_ratio:
            logger.info(f"    • 活跃URL比例: {metrics.active_urls_ratio:.1%}")
        if metrics.config_completeness:
            logger.info(f"    • 配置完整性: {metrics.config_completeness:.1%}")
        
        logger.info(f"  📋 配置来源: {metrics.config_source}")
        logger.info(f"  🎯 最终状态: {metrics.status}")
    
    async def get_metrics_summary(self, days: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            redis_client = await self.get_redis_client()
            
            # 获取最近几天的指标
            all_metrics = []
            for i in range(days):
                date = datetime.now() - timedelta(days=i)
                date_key = date.strftime('%Y%m%d')
                metrics_key = f"crawler_request_metrics:{date_key}"
                
                metrics_data = await redis_client.lrange(metrics_key, 0, -1)
                for data in metrics_data:
                    try:
                        metric = json.loads(data)
                        all_metrics.append(metric)
                    except json.JSONDecodeError:
                        continue
            
            if not all_metrics:
                return {"message": "没有找到指标数据"}
            
            # 计算摘要统计
            total_requests = len(all_metrics)
            successful_requests = len([m for m in all_metrics if m["status"] == "success"])
            validation_errors = len([m for m in all_metrics if m["status"] == "validation_error"])
            api_errors = len([m for m in all_metrics if m["status"] == "api_error"])
            failed_requests = len([m for m in all_metrics if m["status"] == "failed"])
            
            # 性能统计
            durations = [m["duration"] for m in all_metrics if m.get("duration")]
            avg_duration = sum(durations) / len(durations) if durations else 0
            max_duration = max(durations) if durations else 0
            min_duration = min(durations) if durations else 0
            
            # URL统计
            url_counts = [m["urls_count"] for m in all_metrics if m.get("urls_count")]
            avg_urls = sum(url_counts) / len(url_counts) if url_counts else 0
            max_urls = max(url_counts) if url_counts else 0
            
            summary = {
                "period": f"最近 {days} 天",
                "total_requests": total_requests,
                "success_rate": f"{(successful_requests / total_requests * 100):.1f}%" if total_requests > 0 else "0%",
                "status_breakdown": {
                    "successful": successful_requests,
                    "validation_errors": validation_errors,
                    "api_errors": api_errors,
                    "failed": failed_requests
                },
                "performance": {
                    "avg_duration": f"{avg_duration:.3f}s",
                    "max_duration": f"{max_duration:.3f}s",
                    "min_duration": f"{min_duration:.3f}s"
                },
                "url_statistics": {
                    "avg_urls_per_request": f"{avg_urls:.1f}",
                    "max_urls_per_request": max_urls
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取指标摘要失败: {e}")
            return {"error": str(e)}
    
    async def get_task_metrics(self, task_id: str) -> List[Dict[str, Any]]:
        """获取特定任务的指标历史"""
        try:
            redis_client = await self.get_redis_client()
            task_metrics_key = f"task_metrics:{task_id}"
            
            metrics_data = await redis_client.lrange(task_metrics_key, 0, -1)
            metrics = []
            
            for data in metrics_data:
                try:
                    metric = json.loads(data)
                    metrics.append(metric)
                except json.JSONDecodeError:
                    continue
            
            # 按时间排序（最新的在前）
            metrics.sort(key=lambda x: x.get("start_time", 0), reverse=True)
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取任务指标失败: {e}")
            return []
    
    async def log_data_flow_step(
        self, 
        step_name: str, 
        task_id: str, 
        data: Any, 
        duration: Optional[float] = None
    ):
        """记录数据流步骤"""
        
        # 构建日志消息
        log_msg = f"🔄 数据流步骤: {step_name} - 任务: {task_id}"
        
        if duration:
            log_msg += f" (耗时: {duration:.3f}s)"
        
        logger.info(log_msg)
        
        # 记录数据详情（调试级别）
        if isinstance(data, (dict, list)):
            if isinstance(data, list):
                logger.debug(f"  📊 数据: {len(data)} 项")
                if data and len(data) <= 5:  # 只显示前5项
                    for i, item in enumerate(data):
                        logger.debug(f"    {i+1}. {str(item)[:100]}...")
            else:
                logger.debug(f"  📊 数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
        else:
            logger.debug(f"  📊 数据: {str(data)[:200]}...")
    
    async def log_request_structure(self, crawler_request: Dict[str, Any], task_id: str):
        """记录请求结构的详细信息"""
        
        logger.info(f"📤 爬虫请求结构分析 - 任务: {task_id}")
        
        request_data = crawler_request.get("request", {})
        auth_context = crawler_request.get("auth_context", {})
        
        # 记录request部分
        logger.info(f"  🔍 request部分:")
        logger.info(f"    • URLs数量: {len(request_data.get('urls', []))}")
        logger.info(f"    • 查询指令长度: {len(request_data.get('q', ''))} 字符")
        logger.info(f"    • Schema长度: {len(request_data.get('schema_str', ''))} 字符")
        logger.info(f"    • 缓存设置: {request_data.get('cache', False)}")
        logger.info(f"    • 优先级: {request_data.get('priority', 'unknown')}")
        logger.info(f"    • 批次名称: {request_data.get('batch_name', 'unknown')}")
        logger.info(f"    • 最大并发: {request_data.get('max_concurrent_tasks', 0)}")
        logger.info(f"    • 超时时间: {request_data.get('batch_timeout', 0)}秒")
        
        # 记录auth_context部分
        logger.info(f"  🔐 auth_context部分:")
        logger.info(f"    • 用户ID: {auth_context.get('user_id', 'unknown')}")
        logger.info(f"    • 角色: {auth_context.get('role', 'unknown')}")
        logger.info(f"    • 认证方法: {auth_context.get('auth_method', 'unknown')}")
        logger.info(f"    • API密钥前缀: {auth_context.get('api_key_prefix', 'none')[:10]}...")
        logger.info(f"    • 客户端IP: {auth_context.get('client_ip', 'unknown')}")
        logger.info(f"    • 用户代理: {auth_context.get('user_agent', 'unknown')}")
        
        # 记录完整请求（调试级别）
        logger.debug(f"📋 完整请求内容:")
        logger.debug(json.dumps(crawler_request, ensure_ascii=False, indent=2))
    
    async def log_error_details(
        self, 
        error: Exception, 
        task_id: str, 
        context: Dict[str, Any]
    ):
        """记录错误的详细信息"""
        
        error_type = type(error).__name__
        error_message = str(error)
        
        logger.error(f"💥 请求构建错误详情 - 任务: {task_id}")
        logger.error(f"  🏷️ 错误类型: {error_type}")
        logger.error(f"  📝 错误消息: {error_message}")
        logger.error(f"  📋 上下文: {json.dumps(context, ensure_ascii=False, indent=2)}")
        
        # 根据错误类型提供建议
        suggestions = self._get_error_suggestions(error_type, error_message)
        if suggestions:
            logger.error(f"  💡 建议解决方案:")
            for suggestion in suggestions:
                logger.error(f"    • {suggestion}")
    
    def _get_error_suggestions(self, error_type: str, error_message: str) -> List[str]:
        """根据错误类型提供解决建议"""
        
        suggestions = []
        
        if "no active urls" in error_message.lower():
            suggestions.extend([
                "检查URL池中是否有status='active'的URL",
                "验证监控任务是否正确关联了URL",
                "检查URL的启用/禁用状态"
            ])
        
        elif "no crawl config" in error_message.lower():
            suggestions.extend([
                "确保监控任务包含完整的config字段",
                "验证爬取配置是否正确保存",
                "检查config_id是否有效"
            ])
        
        elif "connection" in error_message.lower():
            suggestions.extend([
                "检查Redis连接是否正常",
                "验证网络连接状态",
                "检查服务依赖是否启动"
            ])
        
        elif "timeout" in error_message.lower():
            suggestions.extend([
                "增加超时时间配置",
                "检查系统负载情况",
                "优化数据库查询性能"
            ])
        
        return suggestions
    
    async def generate_monitoring_report(self, task_id: str) -> str:
        """生成监控报告"""
        
        try:
            # 获取任务指标
            metrics = await self.get_task_metrics(task_id)
            
            if not metrics:
                return f"📊 监控报告 - 任务: {task_id}\n❌ 没有找到监控数据"
            
            # 生成报告
            report = f"📊 监控报告 - 任务: {task_id}\n"
            report += "=" * 50 + "\n\n"
            
            # 基本统计
            total_executions = len(metrics)
            successful = len([m for m in metrics if m["status"] == "success"])
            success_rate = (successful / total_executions * 100) if total_executions > 0 else 0
            
            report += f"📈 执行统计:\n"
            report += f"  • 总执行次数: {total_executions}\n"
            report += f"  • 成功次数: {successful}\n"
            report += f"  • 成功率: {success_rate:.1f}%\n\n"
            
            # 性能统计
            durations = [m["duration"] for m in metrics if m.get("duration")]
            if durations:
                avg_duration = sum(durations) / len(durations)
                report += f"⏱️ 性能统计:\n"
                report += f"  • 平均耗时: {avg_duration:.3f}s\n"
                report += f"  • 最长耗时: {max(durations):.3f}s\n"
                report += f"  • 最短耗时: {min(durations):.3f}s\n\n"
            
            # 最近执行
            if metrics:
                latest = metrics[0]
                report += f"🕐 最近执行:\n"
                report += f"  • 时间: {datetime.fromtimestamp(latest['start_time']).strftime('%Y-%m-%d %H:%M:%S')}\n"
                report += f"  • 状态: {latest['status']}\n"
                report += f"  • URL数量: {latest['urls_count']}\n"
                if latest.get("error_message"):
                    report += f"  • 错误: {latest['error_message']}\n"
            
            return report
            
        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
            return f"📊 监控报告生成失败: {str(e)}"


# 全局监控器实例
_global_monitor = None

async def get_crawler_request_monitor() -> CrawlerRequestMonitor:
    """获取全局监控器实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = CrawlerRequestMonitor()
    return _global_monitor
