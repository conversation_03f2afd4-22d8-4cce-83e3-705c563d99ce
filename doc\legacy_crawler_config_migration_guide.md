# 传统爬虫配置迁移指南

## 📋 概述

本文档详细说明了从传统爬虫配置API (`/api/v1/crawler/config`) 迁移到新架构实例配置API的完整过程。

## 🎯 迁移目标

- ✅ **提升性能**：新架构支持多实例并发，提高爬取效率
- ✅ **增强管理**：独立的实例配置管理，更灵活的配置策略
- ✅ **改善监控**：更详细的实例状态监控和日志记录
- ✅ **向后兼容**：保持现有功能不变，平滑过渡

## 🔄 API变更对比

### 传统API (已废弃)
```
GET    /api/v1/crawler/config          # 获取配置
PUT    /api/v1/crawler/config          # 更新配置
GET    /api/v1/crawler/config/default  # 获取默认配置
POST   /api/v1/crawler/test-connection # 测试连接
POST   /api/v1/crawler/build-request   # 构建请求
```

### 新架构API (推荐)
```
GET    /api/v1/crawler/instances/       # 获取实例列表
POST   /api/v1/crawler/instances/       # 创建实例
GET    /api/v1/crawler/instances/{id}   # 获取实例详情
PUT    /api/v1/crawler/instances/{id}   # 更新实例
DELETE /api/v1/crawler/instances/{id}   # 删除实例
POST   /api/v1/crawler/instances/{id}/test # 测试实例连接
```

## 🛠️ 迁移步骤

### Step 1: 备份现有配置
```bash
# 导出当前配置
curl -X GET "http://localhost:8000/api/v1/crawler/config" > legacy_config_backup.json
```

### Step 2: 验证迁移兼容性
```bash
# 检查迁移状态
curl -X GET "http://localhost:8000/api/v1/crawler/migration-status"
```

### Step 3: 执行自动迁移
```bash
# 执行配置迁移
curl -X POST "http://localhost:8000/api/v1/crawler/migrate-to-instance"
```

### Step 4: 验证迁移结果
```bash
# 检查新实例配置
curl -X GET "http://localhost:8000/api/v1/crawler/instances/"
```

## 📊 配置结构对比

### 传统配置结构
```json
{
  "api": {
    "base_url": "http://localhost:11234",
    "timeout": 30000,
    "max_retries": 3
  },
  "browser": { ... },
  "crawler": { ... },
  "llm": { ... },
  "schema_extraction": { ... },
  "content_processing": { ... },
  "link_filtering": { ... },
  "scheduler": { ... },
  "monitor": { ... }
}
```

### 新实例配置结构
```json
{
  "config_id": "instance-uuid",
  "config_name": "爬虫实例配置",
  "description": "从传统配置迁移而来",
  "api_endpoint": "http://localhost:11234",
  "timeout": 30000,
  "max_retries": 3,
  "auth_config": {
    "auth_type": "api_key",
    "api_key": "sk-xxx"
  },
  "max_concurrent": 2,
  "weight": 1,
  "priority": 1,
  "browser": { ... },
  "crawler": { ... },
  "llm": { ... },
  "schema_extraction": { ... },
  "content_processing": { ... },
  "link_filtering": { ... },
  "scheduler": { ... },
  "monitor": { ... }
}
```

## 🔧 前端代码迁移

### 旧代码 (已更新)
```typescript
// 旧的服务调用
const config = await crawlerConfigService.getConfig();
await crawlerConfigService.updateConfig(config);
```

### 新代码 (自动兼容)
```typescript
// 新的服务调用（自动转换）
const config = await crawlerConfigService.getConfig(); // 自动从实例配置获取
await crawlerConfigService.updateConfig(config);       // 自动更新实例配置
```

## ⚠️ 重要注意事项

### 1. 向后兼容性
- ✅ 现有的 `/crawler-settings` 页面继续正常工作
- ✅ 所有配置参数保持不变
- ✅ 用户界面和操作流程不变

### 2. 数据迁移
- 🔄 传统配置自动转换为实例配置
- 💾 原始配置数据保留备份
- 🔒 敏感信息（API密钥）安全处理

### 3. 功能增强
- 🚀 支持多个爬虫实例并发运行
- 📊 更详细的实例性能监控
- ⚖️ 负载均衡和权重分配
- 🔧 独立的实例配置管理

## 🧪 测试验证

### 1. 功能测试
```bash
# 测试传统API兼容性
python scripts/test_existing_apis.py

# 测试新实例API
curl -X GET "http://localhost:8000/api/v1/crawler/instances/"
```

### 2. 前端测试
- 访问 `http://localhost:3000/crawler-settings`
- 验证配置加载、保存、测试功能
- 确认迁移通知正常显示

### 3. 集成测试
- 创建监控任务验证配置生效
- 检查任务执行使用正确的实例配置
- 验证配置变更实时生效

## 🔄 回滚方案

如果迁移过程中出现问题，可以按以下步骤回滚：

### 1. 恢复传统API
```bash
# 重新启用传统API端点（移除 deprecated 标记）
# 修改 backend/app/api/v1/crawler_config.py
```

### 2. 恢复前端服务
```bash
# 回滚前端服务层更改
git checkout HEAD~1 -- frontend/src/services/crawlerConfigService.ts
```

### 3. 恢复配置数据
```bash
# 从备份恢复配置
curl -X PUT "http://localhost:8000/api/v1/crawler/config" \
     -H "Content-Type: application/json" \
     -d @legacy_config_backup.json
```

## 📞 技术支持

如果在迁移过程中遇到问题，请：

1. 📋 检查迁移日志和错误信息
2. 🔍 运行诊断脚本验证系统状态
3. 📧 联系技术支持团队
4. 🐛 提交问题报告到项目仓库

## 📈 迁移时间表

- **Phase 1**: 准备阶段 ✅ (已完成)
- **Phase 2**: 前端迁移 ✅ (已完成)
- **Phase 3**: 后端清理 ✅ (已完成)
- **Phase 4**: 测试验证 🔄 (进行中)
- **Phase 5**: 文档更新 ✅ (已完成)
- **Phase 6**: 生产部署 ⏳ (待定)

## 🎉 迁移完成标志

迁移成功完成的标志：
- ✅ 传统API标记为废弃但仍可用
- ✅ 新实例配置API正常工作
- ✅ 前端配置页面功能正常
- ✅ 监控任务使用新配置执行
- ✅ 所有测试用例通过
- ✅ 用户体验保持一致

---

*最后更新: 2025-08-10*
*版本: v1.0*
