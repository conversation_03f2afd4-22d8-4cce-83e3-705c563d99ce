#!/usr/bin/env python3
"""
测试监控端点是否正确注册
"""

import requests
import json
from datetime import datetime


def test_monitoring_endpoints():
    """测试监控端点"""
    
    print("🔍 测试监控API端点")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # 测试端点列表
    endpoints = [
        {
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/summary?days=1",
            "name": "爬虫请求指标摘要"
        },
        {
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/task/demo-task",
            "name": "任务指标详情"
        },
        {
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/report/demo-task",
            "name": "任务监控报告"
        },
        {
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/performance-analysis?days=7",
            "name": "性能分析"
        },
        {
            "url": f"{base_url}/api/v1/monitoring/system-health",
            "name": "系统健康状态"
        }
    ]
    
    print(f"🌐 测试基础URL: {base_url}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    for endpoint in endpoints:
        print(f"📡 测试: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        
        try:
            response = requests.get(endpoint['url'], timeout=10)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 端点可访问")
                try:
                    data = response.json()
                    if data.get('success'):
                        print("   ✅ 返回数据格式正确")
                    else:
                        print(f"   ⚠️ 返回错误: {data.get('error', '未知错误')}")
                except json.JSONDecodeError:
                    print("   ⚠️ 返回数据不是有效JSON")
            elif response.status_code == 404:
                print("   ❌ 端点未找到 (404)")
            elif response.status_code == 500:
                print("   ❌ 服务器内部错误 (500)")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data.get('detail', '无详情')}")
                except:
                    print(f"   错误内容: {response.text[:200]}")
            else:
                print(f"   ❌ 其他错误: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("   ❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print("   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        print()
    
    print("🎯 测试建议:")
    print("  1. 确保后端服务正在运行 (docker-compose up 或 python main.py)")
    print("  2. 检查Redis服务是否启动")
    print("  3. 验证监控路由是否正确注册")
    print("  4. 查看后端日志了解详细错误信息")


def test_openapi_docs():
    """测试OpenAPI文档中是否包含新端点"""
    
    print("\n📚 检查OpenAPI文档")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8000/openapi.json", timeout=10)
        
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get('paths', {})
            
            # 检查新的监控端点
            monitoring_paths = [
                "/api/v1/monitoring/crawler-request-metrics/summary",
                "/api/v1/monitoring/crawler-request-metrics/task/{task_id}",
                "/api/v1/monitoring/crawler-request-metrics/report/{task_id}",
                "/api/v1/monitoring/crawler-request-metrics/performance-analysis",
                "/api/v1/monitoring/system-health"
            ]
            
            print("📋 检查监控端点在OpenAPI文档中的注册情况:")
            for path in monitoring_paths:
                if path in paths:
                    print(f"   ✅ {path}")
                else:
                    print(f"   ❌ {path} - 未找到")
            
            print(f"\n📊 总路径数: {len(paths)}")
            print(f"📊 监控相关路径数: {len([p for p in paths if 'monitoring' in p])}")
            
        else:
            print(f"❌ 无法获取OpenAPI文档: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查OpenAPI文档失败: {e}")


if __name__ == "__main__":
    test_monitoring_endpoints()
    test_openapi_docs()
