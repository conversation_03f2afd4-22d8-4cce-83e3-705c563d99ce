#!/usr/bin/env python3
"""
真实场景测试脚本

测试完整的任务调度系统，从Excel读取600+链接到任务分片执行
模拟真实的使用场景
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.utils.excel_processor import ExcelProcessor
from backend.app.core.task_manager import TaskManager, TaskManagerConfig
from backend.app.core.task_splitter import SplitterConfig, TaskPriority
from backend.app.core.rate_limiter import RateLimitConfig
from backend.app.database import get_async_session
from backend.app.models.task import CrawlTask
from sqlalchemy import select

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/real_scenario_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)


class RealScenarioTester:
    """真实场景测试器"""
    
    def __init__(self):
        self.excel_processor = ExcelProcessor()
        self.task_manager: TaskManager = None
        self.test_results = {
            'start_time': None,
            'end_time': None,
            'excel_processing': {},
            'task_submission': {},
            'execution_results': {},
            'performance_metrics': {}
        }
    
    async def run_complete_test(self, excel_path: str) -> Dict[str, Any]:
        """运行完整的测试流程
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        self.test_results['start_time'] = datetime.now()
        
        try:
            logger.info("🚀 Starting Real Scenario Test")
            logger.info("=" * 60)
            
            # 步骤1: 验证和读取Excel文件
            await self._step1_process_excel(excel_path)
            
            # 步骤2: 初始化任务管理器
            await self._step2_initialize_task_manager()
            
            # 步骤3: 创建数据库任务记录
            await self._step3_create_database_task()
            
            # 步骤4: 提交任务到调度系统
            await self._step4_submit_tasks()
            
            # 步骤5: 监控任务执行
            await self._step5_monitor_execution()
            
            # 步骤6: 分析结果和性能
            await self._step6_analyze_results()
            
            self.test_results['end_time'] = datetime.now()
            self.test_results['total_duration'] = (
                self.test_results['end_time'] - self.test_results['start_time']
            ).total_seconds()
            
            logger.info("✅ Real Scenario Test Completed Successfully")
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ Real Scenario Test Failed: {e}")
            self.test_results['error'] = str(e)
            self.test_results['end_time'] = datetime.now()
            raise
        finally:
            if self.task_manager:
                await self.task_manager.stop()
    
    async def _step1_process_excel(self, excel_path: str):
        """步骤1: 处理Excel文件"""
        logger.info("📊 Step 1: Processing Excel File")
        
        step_start = datetime.now()
        
        # 验证Excel文件
        validation_result = self.excel_processor.validate_excel_structure(excel_path)
        logger.info(f"Excel validation result: {validation_result}")
        
        if not validation_result['is_valid']:
            raise ValueError(f"Invalid Excel file: {validation_result['errors']}")
        
        # 读取商品链接
        products, stats = self.excel_processor.read_product_links(excel_path)
        
        self.test_results['excel_processing'] = {
            'file_path': excel_path,
            'validation_result': validation_result,
            'products_count': len(products),
            'stats': stats,
            'processing_time': (datetime.now() - step_start).total_seconds(),
            'products_sample': products[:5] if products else []  # 保存前5个作为样本
        }
        
        # 存储产品数据供后续步骤使用
        self.products = products
        
        logger.info(f"✅ Processed {len(products)} products from Excel")
        logger.info(f"Platform distribution: {stats['platforms']}")
    
    async def _step2_initialize_task_manager(self):
        """步骤2: 初始化任务管理器"""
        logger.info("⚙️ Step 2: Initializing Task Manager")
        
        step_start = datetime.now()
        
        # 配置任务分片器 - 针对600+URL优化
        splitter_config = SplitterConfig(
            max_batch_size=50,  # 减小批次大小以便更好地观察分片效果
            max_concurrent_batches=2,  # 遵守外部API限制
            min_batch_size=10,
            adaptive_sizing=True,
            load_balance_enabled=True
        )
        
        # 配置限流器 - 模拟外部API限制
        rate_limiter_config = RateLimitConfig(
            max_concurrent_requests=2,  # 外部API限制
            requests_per_minute=30,     # 降低频率以便观察
            requests_per_hour=1000,
            failure_threshold=3,
            failure_rate_threshold=0.5,
            recovery_timeout=30,
            redis_url="redis://localhost:6379/0"
        )
        
        # 配置任务管理器
        manager_config = TaskManagerConfig(
            max_concurrent_batches=2,
            batch_check_interval=3.0,  # 更频繁的检查
            task_timeout=600,          # 10分钟超时
            max_queue_size=10000,
            splitter_config=splitter_config,
            rate_limiter_config=rate_limiter_config
        )
        
        # 创建并启动任务管理器
        self.task_manager = TaskManager(manager_config)
        await self.task_manager.start()
        
        self.test_results['task_submission']['manager_config'] = {
            'max_batch_size': splitter_config.max_batch_size,
            'max_concurrent_batches': manager_config.max_concurrent_batches,
            'rate_limit_concurrent': rate_limiter_config.max_concurrent_requests,
            'initialization_time': (datetime.now() - step_start).total_seconds()
        }
        
        logger.info("✅ Task Manager initialized successfully")
    
    async def _step3_create_database_task(self):
        """步骤3: 创建数据库任务记录"""
        logger.info("💾 Step 3: Creating Database Task Record")
        
        step_start = datetime.now()
        
        try:
            async with get_async_session() as session:
                # 创建任务记录
                task = CrawlTask(
                    name=f"Real Scenario Test - {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    description=f"Testing task scheduler with {len(self.products)} URLs from Excel",
                    platform="mercadolibre",
                    total_products=len(self.products),
                    status="pending",
                    created_at=datetime.now()
                )
                
                session.add(task)
                await session.commit()
                await session.refresh(task)
                
                self.db_task_id = task.id
                
                self.test_results['task_submission']['database_task'] = {
                    'task_id': task.id,
                    'task_name': task.name,
                    'total_products': task.total_products,
                    'creation_time': (datetime.now() - step_start).total_seconds()
                }
                
                logger.info(f"✅ Created database task record with ID: {task.id}")
                
        except Exception as e:
            logger.error(f"Failed to create database task: {e}")
            raise
    
    async def _step4_submit_tasks(self):
        """步骤4: 提交任务到调度系统"""
        logger.info("📤 Step 4: Submitting Tasks to Scheduler")
        
        step_start = datetime.now()
        
        # 提取URL列表
        urls = [product['product_url'] for product in self.products]
        
        logger.info(f"Submitting {len(urls)} URLs for processing")
        logger.info(f"Expected batches: ~{len(urls) // 50 + (1 if len(urls) % 50 > 0 else 0)}")
        
        # 提交任务
        submission_id = await self.task_manager.submit_task(
            task_id=self.db_task_id,
            urls=urls,
            platform="mercadolibre",
            priority=TaskPriority.NORMAL,
            options={
                'test_mode': True,
                'source': 'real_scenario_test',
                'excel_file': self.test_results['excel_processing']['file_path']
            }
        )
        
        # 获取初始状态
        initial_status = self.task_manager.get_status()
        
        self.test_results['task_submission'].update({
            'submission_id': submission_id,
            'urls_submitted': len(urls),
            'submission_time': (datetime.now() - step_start).total_seconds(),
            'initial_status': initial_status
        })
        
        logger.info(f"✅ Tasks submitted successfully")
        logger.info(f"Submission ID: {submission_id}")
        logger.info(f"Pending batches: {initial_status['pending_batches']}")
    
    async def _step5_monitor_execution(self):
        """步骤5: 监控任务执行"""
        logger.info("👀 Step 5: Monitoring Task Execution")
        
        monitoring_start = datetime.now()
        monitoring_data = []
        
        # 监控循环
        max_monitoring_time = 1800  # 30分钟最大监控时间
        check_interval = 10  # 每10秒检查一次
        
        while True:
            current_time = datetime.now()
            elapsed_time = (current_time - monitoring_start).total_seconds()
            
            # 获取状态
            status = self.task_manager.get_status()
            
            # 记录监控数据
            monitoring_point = {
                'timestamp': current_time.isoformat(),
                'elapsed_time': elapsed_time,
                'status': status
            }
            monitoring_data.append(monitoring_point)
            
            # 日志输出
            logger.info(
                f"⏱️ {elapsed_time:.0f}s - "
                f"Pending: {status['pending_batches']}, "
                f"Running: {status['running_batches']}, "
                f"Completed: {status['completed_batches']}, "
                f"Failed: {status['failed_batches']}"
            )
            
            # 检查完成条件
            if (status['pending_batches'] == 0 and 
                status['running_batches'] == 0):
                logger.info("🎉 All tasks completed!")
                break
            
            # 检查超时
            if elapsed_time > max_monitoring_time:
                logger.warning("⚠️ Monitoring timeout reached")
                break
            
            # 等待下次检查
            await asyncio.sleep(check_interval)
        
        self.test_results['execution_results'] = {
            'monitoring_duration': elapsed_time,
            'monitoring_data': monitoring_data,
            'final_status': status
        }
        
        logger.info(f"✅ Monitoring completed after {elapsed_time:.1f} seconds")
    
    async def _step6_analyze_results(self):
        """步骤6: 分析结果和性能"""
        logger.info("📈 Step 6: Analyzing Results and Performance")
        
        # 获取最终状态
        final_status = self.task_manager.get_status()
        
        # 计算性能指标
        total_urls = self.test_results['task_submission']['urls_submitted']
        completed_batches = final_status['completed_batches']
        failed_batches = final_status['failed_batches']
        total_batches = completed_batches + failed_batches
        
        # 计算处理速度
        execution_time = self.test_results['execution_results']['monitoring_duration']
        urls_per_second = total_urls / execution_time if execution_time > 0 else 0
        
        # 分析监控数据
        monitoring_data = self.test_results['execution_results']['monitoring_data']
        peak_concurrent = max(
            point['status']['running_batches'] 
            for point in monitoring_data
        ) if monitoring_data else 0
        
        self.test_results['performance_metrics'] = {
            'total_urls_processed': total_urls,
            'total_batches_created': total_batches,
            'successful_batches': completed_batches,
            'failed_batches': failed_batches,
            'success_rate': completed_batches / total_batches if total_batches > 0 else 0,
            'execution_time_seconds': execution_time,
            'urls_per_second': urls_per_second,
            'peak_concurrent_batches': peak_concurrent,
            'average_batch_size': total_urls / total_batches if total_batches > 0 else 0,
            'task_manager_stats': final_status['stats']
        }
        
        # 输出性能报告
        logger.info("📊 Performance Report:")
        logger.info(f"  Total URLs: {total_urls}")
        logger.info(f"  Total Batches: {total_batches}")
        logger.info(f"  Success Rate: {self.test_results['performance_metrics']['success_rate']:.2%}")
        logger.info(f"  Execution Time: {execution_time:.1f} seconds")
        logger.info(f"  Processing Speed: {urls_per_second:.2f} URLs/second")
        logger.info(f"  Peak Concurrent Batches: {peak_concurrent}")
        logger.info(f"  Average Batch Size: {self.test_results['performance_metrics']['average_batch_size']:.1f}")


async def main():
    """主函数"""
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # Excel文件路径
    excel_path = "LinksToMonit/all_task_item.xlsx"
    
    if not Path(excel_path).exists():
        logger.error(f"Excel file not found: {excel_path}")
        return
    
    # 创建测试器
    tester = RealScenarioTester()
    
    try:
        # 运行完整测试
        results = await tester.run_complete_test(excel_path)
        
        # 保存测试结果
        import json
        results_file = f"logs/test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换datetime对象为字符串以便JSON序列化
        def datetime_converter(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=datetime_converter, ensure_ascii=False)
        
        logger.info(f"📄 Test results saved to: {results_file}")
        
        # 输出总结
        logger.info("🎯 Test Summary:")
        logger.info(f"  Total Duration: {results['total_duration']:.1f} seconds")
        logger.info(f"  URLs Processed: {results['performance_metrics']['total_urls_processed']}")
        logger.info(f"  Success Rate: {results['performance_metrics']['success_rate']:.2%}")
        logger.info(f"  Processing Speed: {results['performance_metrics']['urls_per_second']:.2f} URLs/sec")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
