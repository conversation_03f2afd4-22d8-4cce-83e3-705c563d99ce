#!/usr/bin/env python3
"""
测试前端配置是否正确指向8000端口
"""

import requests
import json
import time

def test_frontend_api_calls():
    """测试前端API调用"""
    print("🧪 测试前端API配置...")
    print("=" * 50)
    
    # 测试前端是否可访问
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        print("✅ 前端服务可访问")
    except Exception as e:
        print(f"❌ 前端服务不可访问: {e}")
        return False
    
    # 测试后端8000端口
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端8000端口可访问")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 后端8000端口响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 后端8000端口不可访问: {e}")
        return False
    
    # 测试8001端口是否还在运行
    try:
        response = requests.get("http://localhost:8001/health", timeout=2)
        print(f"⚠️  8001端口仍在运行: {response.status_code}")
        print("   这可能导致前端连接混乱")
    except Exception:
        print("✅ 8001端口已停止，配置正确")
    
    # 测试API端点
    test_endpoints = [
        "/health",
        "/api/v1/tasks",
        "/docs"
    ]
    
    print("\n📡 测试API端点...")
    for endpoint in test_endpoints:
        try:
            url = f"http://localhost:8000{endpoint}"
            response = requests.get(url, timeout=5)
            status = "✅" if response.status_code < 400 else "❌"
            print(f"  {status} {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"  ❌ {endpoint}: 连接失败 - {e}")
    
    return True

def test_websocket_config():
    """测试WebSocket配置"""
    print("\n🔌 测试WebSocket配置...")
    
    # 检查WebSocket端点是否可用
    import websockets
    import asyncio
    
    async def test_ws():
        try:
            uri = "ws://localhost:8000/ws"
            async with websockets.connect(uri) as websocket:
                print("✅ WebSocket 8000端口连接成功")
                
                # 发送测试消息
                test_msg = {"type": "ping"}
                await websocket.send(json.dumps(test_msg))
                
                # 等待响应
                response = await websocket.recv()
                print(f"✅ WebSocket响应: {response[:100]}...")
                return True
        except Exception as e:
            print(f"❌ WebSocket 8000端口连接失败: {e}")
            return False
    
    try:
        result = asyncio.run(test_ws())
        return result
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

def main():
    print("🔍 前端配置验证")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    api_ok = test_frontend_api_calls()
    ws_ok = test_websocket_config()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  API配置: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"  WebSocket配置: {'✅ 正常' if ws_ok else '❌ 异常'}")
    
    if api_ok and ws_ok:
        print("\n🎉 前端配置验证通过!")
        print("💡 建议:")
        print("  1. 清理浏览器缓存")
        print("  2. 硬刷新页面 (Ctrl+F5)")
        print("  3. 检查浏览器开发者工具的Network标签")
        return True
    else:
        print("\n❌ 前端配置验证失败!")
        print("💡 建议:")
        print("  1. 检查所有8001端口的配置是否已更新")
        print("  2. 重启前端服务")
        print("  3. 确保后端8000端口正在运行")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
