#!/usr/bin/env python3
"""
测试连接测试API是否正常工作
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_connection_api():
    """测试连接测试API"""
    
    print("🧪 测试连接测试API...")
    
    # 测试数据
    config_data = {
        "backend_name": "连接测试后端",
        "description": "测试连接测试API的后端配置",
        "api_endpoint": "http://connection-test:8080",
        "timeout": 30000,
        "max_retries": 3,
        "auth_config": {
            "auth_type": "none"
        },
        "performance_config": {
            "max_concurrent_tasks": 3,
            "batch_timeout": 300,
            "api_timeout": 30000,
            "api_max_retries": 3,
            "api_retry_delay": 1000,
            "memory_limit_mb": 1024,
            "cpu_limit_percent": 80,
            "rate_limit_requests": 100,
            "rate_limit_period": 60
        },
        "system_auth_config": {
            "auth_config": {
                "auth_type": "none"
            },
            "default_permissions": [],
            "default_role": "user",
            "include_client_ip": True,
            "include_user_agent": True
        },
        "callback_config": {
            "callback_timeout": 30000,
            "callback_retries": 3,
            "callback_verify_ssl": True
        },
        "monitoring_config": {
            "health_check_enabled": True,
            "health_check_interval": 60,
            "health_check_timeout": 10000,
            "response_time_threshold": 30000,
            "error_rate_threshold": 0.1,
            "alert_on_system_failure": True,
            "failure_threshold": 3,
            "recovery_threshold": 2
        },
        "weight": 1,
        "priority": 1,
        "performance_level": "medium"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试连接测试API
            response = await client.post(
                "http://localhost:8000/api/v1/backend-configs/test-connection",
                json=config_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 连接测试API调用成功")
                print(f"   - 成功: {result.get('success')}")
                print(f"   - 消息: {result.get('message')}")
                if 'response_time' in result:
                    print(f"   - 响应时间: {result.get('response_time')}ms")
                return True
            else:
                print(f"❌ 连接测试API调用失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 连接测试API调用异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_connection_api()
    if success:
        print(f"\n✅ 连接测试API工作正常！")
    else:
        print(f"\n❌ 连接测试API存在问题！")

if __name__ == "__main__":
    asyncio.run(main())
