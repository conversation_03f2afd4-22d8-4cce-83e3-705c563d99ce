# Task 02 测试脚本使用指南

## 🎯 一键测试 (推荐)

### 唯一可用版本
```powershell
# 在项目根目录运行
scripts\test_task02_final_working.ps1
```

**特点**: 
- ✅ 100% PowerShell原生，完全兼容
- ✅ 自动启动Docker容器
- ✅ 自动测试所有功能
- ✅ 自动清理环境
- ✅ 彩色输出，详细测试报告
- ✅ 智能目录切换，解决Python路径问题

## 📋 测试内容

### 包含5个核心测试
1. **📋 核心文件检查** - 验证必要文件存在
2. **🐳 Docker环境检查** - 启动TimescaleDB和Redis
3. **📦 Python模块导入** - 测试SQLAlchemy模型导入
4. **💾 数据库连接测试** - 验证TimescaleDB连接
5. **🌐 FastAPI服务测试** - 验证API服务启动

## ⚠️ 使用前提

### 必要条件
- ✅ Docker Desktop 正在运行
- ✅ Conda环境 `monit-crawler` 已创建
- ✅ 在PowerShell中运行（不是CMD）

### 检查命令
```powershell
# 检查Docker
docker --version

# 检查Conda环境
conda env list | findstr monit-crawler

# 检查PowerShell版本
$PSVersionTable.PSVersion
```

## 📊 测试结果解读

### ✅ 成功示例
```
🎉 所有测试通过！Task 02 完全验证成功！
🚀 Task 02 状态: 100% 完成
✅ 准备开始 Task 03: 爬虫核心引擎开发
成功率: 100%
```

### ⚠️ 部分失败示例
```
⚠️ 部分测试失败
成功率: 80%
详细结果:
  ✅ 核心文件检查
  ❌ Docker环境
```

## 🔧 常见问题解决

### 问题1: Docker容器启动失败
```
❌ Docker容器启动失败
```
**解决方案**: 
1. 确保Docker Desktop正在运行
2. 重启Docker Desktop
3. 检查端口5432是否被占用

### 问题2: Python模块导入失败
```
❌ Python模块导入失败
```
**解决方案**:
1. 重新创建conda环境: `conda env create -f environment.yml`
2. 激活环境: `conda activate monit-crawler`
3. 安装依赖: `pip install -r backend/requirements.txt`

### 问题3: 数据库连接失败
```
❌ 数据库连接失败
```
**解决方案**:
1. 等待更长时间让数据库完全启动
2. 检查数据库配置: `config/settings.py`
3. 手动启动容器: `docker-compose -f docker-compose.dev.yml up timescaledb -d`

## 🚀 推荐工作流

### 首次验证
```powershell
# 完整测试 - 确保一切正常
scripts\test_task02_final_working.ps1
```

### 日常开发
```powershell
# 如果测试通过，可以开始开发
cd backend
python -m uvicorn app.main:app --reload
```

### 发布前检查
```powershell
# 停止所有服务
docker-compose -f docker-compose.dev.yml down

# 重新完整测试
scripts\test_task02_final_working.ps1
```

## 📝 说明

此测试脚本是经过多次调试和优化的**最终稳定版本**，解决了：
- ✅ PowerShell与批处理的兼容性问题
- ✅ Python模块路径导入问题
- ✅ Docker容器管理问题
- ✅ 数据库连接验证问题
- ✅ API服务启动测试问题

如果此脚本测试通过，说明Task 02的所有组件都已正确配置和工作。 