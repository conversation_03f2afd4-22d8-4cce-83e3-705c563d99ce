.data-analysis {
  padding: 24px;
}

.data-analysis-header {
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.data-analysis-header h2 {
  margin-bottom: 8px;
  color: #1a1a1a;
  font-weight: 600;
}

.data-analysis-header .ant-typography {
  color: #666666;
  font-size: 16px;
}

/* 数据概览区域 */
.data-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.overview-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.overview-card.price::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.overview-card.sales::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.overview-card.rating::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.overview-card .ant-statistic-title {
  color: #666666 !important;
  font-weight: 500 !important;
}

.overview-card .ant-statistic-content-value {
  color: #1a1a1a !important;
  font-weight: 600 !important;
}

/* 筛选控制区域 */
.data-filters {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 数据表格区域 */
.data-table-section {
  margin-bottom: 32px;
}

.data-table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 商品图片样式 */
.product-image {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #f0f0f0;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-details {
  flex: 1;
}

.product-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-url {
  font-size: 12px;
  color: #666666;
  text-decoration: none;
}

.product-url:hover {
  color: #1890ff;
}

/* 价格显示样式 */
.price-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
}

.price-change {
  font-size: 12px;
  margin-top: 2px;
}

.price-change.positive {
  color: #ff4d4f;
}

.price-change.negative {
  color: #52c41a;
}

/* 评分显示样式 */
.rating-display {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-stars {
  color: #faad14;
}

.rating-count {
  font-size: 12px;
  color: #666666;
}

/* 库存状态样式 */
.stock-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stock-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stock-indicator.in-stock {
  background: #52c41a;
}

.stock-indicator.low-stock {
  background: #faad14;
}

.stock-indicator.out-of-stock {
  background: #ff4d4f;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
}

.chart-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.chart-card .ant-card-head {
  border-bottom: 1px solid #f5f5f5;
}

.chart-card .ant-card-head-title {
  color: #1a1a1a;
  font-weight: 600;
}

.chart-container {
  height: 400px;
  width: 100%;
}

/* 对比分析区域 */
.comparison-section {
  margin-bottom: 32px;
}

.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.comparison-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.comparison-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .data-analysis {
    padding: 16px;
  }
  
  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .comparison-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .product-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .product-image {
    width: 40px;
    height: 40px;
  }
}

/* 动画效果 */
.overview-card {
  animation: fadeInUp 0.3s ease-out;
}

.chart-card {
  animation: fadeInUp 0.4s ease-out;
}

.data-table-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666666;
}

.empty-state-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state-hint {
  font-size: 14px;
  color: #999999;
}

/* 夜间模式适配 */
.app-layout.dark .data-analysis-header {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .data-analysis-header h2 {
  color: #ffffff;
}

.app-layout.dark .data-analysis-header .ant-typography {
  color: #d9d9d9;
}

.app-layout.dark .overview-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .overview-card .ant-statistic-title {
  color: #d9d9d9 !important;
}

.app-layout.dark .overview-card .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

/* 强制移除统计卡片内所有元素的背景 */
.app-layout.dark .overview-card * {
  background: transparent !important;
}

.app-layout.dark .overview-card .ant-statistic {
  background: transparent !important;
}

.app-layout.dark .overview-card .ant-statistic-title {
  background: transparent !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .overview-card .ant-statistic-content {
  background: transparent !important;
}

.app-layout.dark .overview-card .ant-statistic-content-suffix {
  background: transparent !important;
}

.app-layout.dark .overview-card .ant-statistic-content-prefix {
  background: transparent !important;
}

.app-layout.dark .data-filters {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .filter-label {
  color: #d9d9d9;
}

.app-layout.dark .data-table-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .chart-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .chart-card .ant-card-head {
  border-bottom-color: #303030;
}

.app-layout.dark .chart-card .ant-card-head-title {
  color: #ffffff;
}

.app-layout.dark .product-title {
  color: #ffffff;
}

.app-layout.dark .product-url {
  color: #d9d9d9;
}

.app-layout.dark .product-url:hover {
  color: #40a9ff;
}

.app-layout.dark .current-price {
  color: #ffffff;
}

.app-layout.dark .original-price {
  color: #8c8c8c;
}

.app-layout.dark .rating-count {
  color: #d9d9d9;
}

.app-layout.dark .product-image {
  border-color: #303030;
}

.app-layout.dark .comparison-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .comparison-label {
  color: #d9d9d9;
}

.app-layout.dark .empty-state {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-icon {
  color: #595959;
}

.app-layout.dark .empty-state-hint {
  color: #8c8c8c;
}

.app-layout.dark .loading-container {
  color: #d9d9d9;
}
