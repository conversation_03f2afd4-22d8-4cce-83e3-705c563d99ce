"""
时间处理工具模块

提供统一的时间处理功能，确保前后端时间显示一致
"""

import os
from datetime import datetime, timezone
from typing import Optional, Union
import pytz


def get_server_timezone() -> pytz.BaseTzInfo:
    """获取服务器时区
    
    Returns:
        pytz.BaseTzInfo: 服务器时区对象
    """
    # 从环境变量获取时区，默认为系统时区
    tz_name = os.getenv('TZ', 'Asia/Shanghai')  # 默认使用中国时区
    try:
        return pytz.timezone(tz_name)
    except pytz.UnknownTimeZoneError:
        # 如果时区无效，使用系统本地时区
        return pytz.timezone('Asia/Shanghai')


def get_local_now() -> datetime:
    """获取服务器本地时间
    
    Returns:
        datetime: 带时区信息的本地时间
    """
    server_tz = get_server_timezone()
    return datetime.now(server_tz)


def utc_to_local(utc_dt: datetime) -> datetime:
    """将UTC时间转换为服务器本地时间
    
    Args:
        utc_dt: UTC时间
        
    Returns:
        datetime: 本地时间
    """
    if utc_dt.tzinfo is None:
        # 如果没有时区信息，假设是UTC
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)
    
    server_tz = get_server_timezone()
    return utc_dt.astimezone(server_tz)


def local_to_utc(local_dt: datetime) -> datetime:
    """将本地时间转换为UTC时间
    
    Args:
        local_dt: 本地时间
        
    Returns:
        datetime: UTC时间
    """
    if local_dt.tzinfo is None:
        # 如果没有时区信息，假设是服务器本地时区
        server_tz = get_server_timezone()
        local_dt = server_tz.localize(local_dt)
    
    return local_dt.astimezone(timezone.utc)


def format_local_datetime(
    dt: Optional[Union[datetime, str]] = None,
    format_str: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """格式化为本地时间字符串
    
    Args:
        dt: 要格式化的时间，可以是datetime对象或ISO字符串，None表示当前时间
        format_str: 格式化字符串
        
    Returns:
        str: 格式化后的本地时间字符串
    """
    if dt is None:
        dt = get_local_now()
    elif isinstance(dt, str):
        # 解析ISO格式字符串
        try:
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except ValueError:
            # 如果解析失败，使用当前时间
            dt = get_local_now()
    
    # 如果是UTC时间，转换为本地时间
    if dt.tzinfo == timezone.utc:
        dt = utc_to_local(dt)
    elif dt.tzinfo is None:
        # 如果没有时区信息，假设是本地时间
        server_tz = get_server_timezone()
        dt = server_tz.localize(dt)
    
    return dt.strftime(format_str)


def get_local_isoformat(dt: Optional[datetime] = None) -> str:
    """获取本地时间的ISO格式字符串
    
    Args:
        dt: 要格式化的时间，None表示当前时间
        
    Returns:
        str: ISO格式的本地时间字符串
    """
    if dt is None:
        dt = get_local_now()
    elif dt.tzinfo is None:
        # 如果没有时区信息，假设是服务器本地时区
        server_tz = get_server_timezone()
        dt = server_tz.localize(dt)
    
    return dt.isoformat()


def parse_frontend_time(time_str: str) -> datetime:
    """解析前端传来的时间字符串
    
    Args:
        time_str: 前端时间字符串（通常是ISO格式）
        
    Returns:
        datetime: 解析后的本地时间
    """
    try:
        # 解析ISO格式字符串
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        
        # 如果是UTC时间，转换为本地时间
        if dt.tzinfo == timezone.utc:
            return utc_to_local(dt)
        else:
            return dt
    except ValueError:
        # 如果解析失败，返回当前本地时间
        return get_local_now()


def get_timezone_info() -> dict:
    """获取时区信息
    
    Returns:
        dict: 包含时区信息的字典
    """
    server_tz = get_server_timezone()
    local_now = get_local_now()
    
    return {
        "timezone": str(server_tz),
        "timezone_name": server_tz.zone,
        "utc_offset": local_now.strftime('%z'),
        "current_time": format_local_datetime(local_now),
        "current_time_iso": get_local_isoformat(local_now)
    }
