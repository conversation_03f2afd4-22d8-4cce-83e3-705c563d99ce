# Grafana手动创建监控面板详细指南

## 🎯 目标
为MonIt系统创建有实际内容的监控面板，显示真实的系统监控数据。

## 📋 前提条件
- Grafana已运行: http://localhost:3001
- 登录信息: admin/admin123
- Prometheus数据源已配置

## 🔧 步骤1: 创建新的Dashboard

1. **访问Grafana**: 打开 http://localhost:3001
2. **登录**: 使用 admin/admin123
3. **创建Dashboard**: 
   - 点击左侧 "+" 图标
   - 选择 "Dashboard"
   - 点击 "Add panel"

## 📊 步骤2: 创建服务状态面板

### 面板配置
- **面板标题**: Service Status
- **可视化类型**: Stat

### 查询配置
```
Query: up
Legend: {{job}}
```

### 字段配置
- **单位**: 无
- **最小值**: 0
- **最大值**: 1

### 值映射
- **0** → 文本: "DOWN", 颜色: 红色
- **1** → 文本: "UP", 颜色: 绿色

### 阈值
- **0**: 红色
- **1**: 绿色

### 显示选项
- **颜色模式**: Background
- **图形模式**: None

## 📈 步骤3: 创建CPU使用率面板

### 面板配置
- **面板标题**: CPU Usage
- **可视化类型**: Time series

### 查询配置
```
Query: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
Legend: CPU Usage %
```

### 字段配置
- **单位**: Percent (0-100)
- **最小值**: 0
- **最大值**: 100

### 阈值
- **0-70%**: 绿色
- **70-90%**: 黄色
- **90%+**: 红色

## 💾 步骤4: 创建内存使用率面板

### 面板配置
- **面板标题**: Memory Usage
- **可视化类型**: Time series

### 查询配置
```
Query: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100
Legend: Memory Usage %
```

### 字段配置
- **单位**: Percent (0-100)
- **最小值**: 0
- **最大值**: 100

### 阈值
- **0-80%**: 绿色
- **80-95%**: 黄色
- **95%+**: 红色

## 🔄 步骤5: 创建系统负载面板

### 面板配置
- **面板标题**: System Load
- **可视化类型**: Time series

### 查询配置
```
Query A: node_load1
Legend: 1m load

Query B: node_load5  
Legend: 5m load

Query C: node_load15
Legend: 15m load
```

### 字段配置
- **单位**: Short
- **最小值**: 0

## 💿 步骤6: 创建磁盘使用率面板

### 面板配置
- **面板标题**: Disk Usage
- **可视化类型**: Time series

### 查询配置
```
Query: 100 - ((node_filesystem_avail_bytes{mountpoint="/"} * 100) / node_filesystem_size_bytes{mountpoint="/"})
Legend: Root Disk %
```

### 字段配置
- **单位**: Percent (0-100)
- **最小值**: 0
- **最大值**: 100

### 阈值
- **0-80%**: 绿色
- **80-90%**: 黄色
- **90%+**: 红色

## 🌐 步骤7: 创建网络流量面板

### 面板配置
- **面板标题**: Network I/O
- **可视化类型**: Time series

### 查询配置
```
Query A: irate(node_network_receive_bytes_total{device!="lo"}[5m])
Legend: Received {{device}}

Query B: irate(node_network_transmit_bytes_total{device!="lo"}[5m])
Legend: Transmitted {{device}}
```

### 字段配置
- **单位**: bytes/sec

## 💾 步骤8: 保存Dashboard

1. **保存面板**: 点击右上角保存图标
2. **设置名称**: "MonIt System Monitoring"
3. **选择文件夹**: General 或创建新文件夹
4. **点击保存**

## ⚙️ 步骤9: 配置Dashboard设置

### 时间设置
- **时间范围**: Last 1 hour
- **刷新间隔**: 30s
- **自动刷新**: 启用

### 变量设置（可选）
- **实例变量**: `label_values(up, instance)`
- **任务变量**: `label_values(up, job)`

## 🎨 布局建议

```
+----------------------------------+
|        Service Status            |
|         (全宽度)                  |
+------------------+---------------+
|   CPU Usage      | Memory Usage  |
|   (1/2宽度)      |  (1/2宽度)    |
+------------------+---------------+
|  System Load     |  Disk Usage   |
|   (1/2宽度)      |  (1/2宽度)    |
+----------------------------------+
|         Network I/O              |
|         (全宽度)                  |
+----------------------------------+
```

## 🔍 验证步骤

1. **检查数据**: 确保所有面板都显示数据
2. **测试刷新**: 验证30秒自动刷新
3. **检查阈值**: 确认颜色变化正常
4. **测试时间范围**: 切换不同时间范围

## 🚨 故障排查

### 问题1: 面板显示"No data"
- 检查Prometheus是否运行: http://localhost:9090
- 验证查询语法是否正确
- 确认数据源配置正确

### 问题2: 查询错误
- 在Prometheus中测试查询: http://localhost:9090/graph
- 检查指标名称拼写
- 确认标签选择器语法

### 问题3: 颜色不变化
- 检查阈值设置
- 确认字段配置正确
- 验证数据类型匹配

## 📚 可用指标列表

当前可用的Node Exporter指标：
- `up` - 服务状态
- `node_cpu_seconds_total` - CPU时间
- `node_memory_*` - 内存相关指标
- `node_load*` - 系统负载
- `node_filesystem_*` - 文件系统指标
- `node_network_*` - 网络接口指标
- `node_disk_*` - 磁盘I/O指标

## 🎯 预期结果

完成后您将看到：
- ✅ 服务状态显示为绿色"UP"
- ✅ CPU使用率实时图表
- ✅ 内存使用率实时图表
- ✅ 系统负载趋势
- ✅ 磁盘使用情况
- ✅ 网络流量统计

---

**提示**: 如果遇到问题，可以先创建一个简单的面板（只有服务状态），确认基础功能正常后再添加其他面板。
