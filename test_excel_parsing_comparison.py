#!/usr/bin/env python3
"""
测试Excel解析一致性

比较URL池管理页面和任务管理页面的Excel解析结果
"""

import requests
import pandas as pd
import tempfile
import os

BASE_URL = "http://localhost:8000"

def create_test_excel_with_624_urls():
    """创建包含624个URL的测试Excel文件"""
    print("📝 创建包含624个URL的测试Excel文件")
    
    # 创建624个测试URL
    urls = []
    platforms = ['amazon', 'mercadolibre', 'ebay', 'aliexpress', 'shopee']
    
    for i in range(624):
        platform = platforms[i % len(platforms)]
        
        if platform == 'amazon':
            url = f"https://www.amazon.com/dp/B08N5WRWNW{i:03d}"
        elif platform == 'mercadolibre':
            url = f"https://articulo.mercadolibre.com.mx/MLM-{123456789 + i}"
        elif platform == 'ebay':
            url = f"https://www.ebay.com/itm/{123456789 + i}"
        elif platform == 'aliexpress':
            url = f"https://www.aliexpress.com/item/{123456789 + i}.html"
        else:  # shopee
            url = f"https://shopee.com.mx/product/{123456789 + i}"
        
        urls.append({
            'chinese_name': f'测试商品{i+1}',
            'spanish_name': f'Producto de prueba {i+1}',
            'product_url': url
        })
    
    # 创建DataFrame
    df = pd.DataFrame(urls)
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
        filename = tmp_file.name
    
    df.to_excel(filename, index=False)
    
    print(f"✅ 测试文件创建成功:")
    print(f"   - 文件路径: {filename}")
    print(f"   - URL数量: {len(urls)}")
    print(f"   - 平台分布: {df['product_url'].apply(lambda x: 'amazon' if 'amazon' in x else 'mercadolibre' if 'mercadolibre' in x else 'ebay' if 'ebay' in x else 'aliexpress' if 'aliexpress' in x else 'shopee').value_counts().to_dict()}")
    
    return filename

def test_url_pool_api(filename):
    """测试URL池管理页面的API"""
    print(f"\n📤 测试URL池管理页面API: /api/v1/excel/upload-and-parse")
    
    try:
        with open(filename, 'rb') as f:
            files = {
                'file': (os.path.basename(filename), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            response = requests.post(f"{BASE_URL}/api/v1/excel/upload-and-parse", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ URL池管理页面解析成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 新增URL: {result['new_urls']}")
            print(f"   - 重复URL: {result['duplicate_urls']}")
            print(f"   - 无效URL: {result['invalid_urls']}")
            print(f"   - 处理时间: {result.get('processing_time', 'N/A'):.2f}秒" if isinstance(result.get('processing_time'), (int, float)) else f"   - 处理时间: {result.get('processing_time', 'N/A')}")
            
            # 显示前几个URL样本
            if result.get('urls'):
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['urls'][:3]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')[:60]}...")
            
            return result
        else:
            print(f"❌ URL池管理页面解析失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ URL池管理页面解析异常: {e}")
        return None

def test_task_manager_api(filename):
    """测试任务管理页面的API"""
    print(f"\n📤 测试任务管理页面API: /api/v1/excel/parse")
    
    try:
        with open(filename, 'rb') as f:
            files = {
                'file': (os.path.basename(filename), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            response = requests.post(f"{BASE_URL}/api/v1/excel/parse", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务管理页面解析成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 有效URL: {result['valid_urls']}")
            print(f"   - 无效URL: {result['invalid_urls']}")
            print(f"   - 平台分布: {result.get('platforms', {})}")
            print(f"   - 解析时间: {result.get('parse_time', 'N/A')}")
            
            # 显示前几个URL样本
            if result.get('urls'):
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['urls'][:3]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')[:60]}...")
            
            return result
        else:
            print(f"❌ 任务管理页面解析失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任务管理页面解析异常: {e}")
        return None

def compare_results(url_pool_result, task_manager_result):
    """比较两个API的解析结果"""
    print(f"\n📊 解析结果比较")
    print("=" * 50)
    
    if not url_pool_result or not task_manager_result:
        print("❌ 无法比较结果，因为有API调用失败")
        return
    
    # 比较URL数量
    url_pool_count = url_pool_result.get('total_urls', 0)
    task_manager_count = task_manager_result.get('total_urls', 0)
    
    print(f"URL数量比较:")
    print(f"   - URL池管理页面: {url_pool_count}")
    print(f"   - 任务管理页面: {task_manager_count}")
    
    if url_pool_count == task_manager_count:
        print("✅ URL数量一致")
    else:
        print(f"❌ URL数量不一致，差异: {abs(url_pool_count - task_manager_count)}")
    
    # 比较平台分布
    url_pool_platforms = {}
    if url_pool_result.get('urls'):
        for url_item in url_pool_result['urls']:
            platform = url_item.get('platform', 'unknown')
            url_pool_platforms[platform] = url_pool_platforms.get(platform, 0) + 1
    
    task_manager_platforms = task_manager_result.get('platforms', {})
    
    print(f"\n平台分布比较:")
    print(f"   - URL池管理页面: {url_pool_platforms}")
    print(f"   - 任务管理页面: {task_manager_platforms}")
    
    # 检查平台分布是否一致
    platforms_match = url_pool_platforms == task_manager_platforms
    if platforms_match:
        print("✅ 平台分布一致")
    else:
        print("❌ 平台分布不一致")

def cleanup_file(filename):
    """清理测试文件"""
    try:
        if os.path.exists(filename):
            os.unlink(filename)
            print(f"\n🧹 已清理测试文件: {filename}")
    except Exception as e:
        print(f"\n⚠️ 清理文件失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始Excel解析一致性测试")
    print("=" * 60)
    print("目标：验证URL池管理页面和任务管理页面的Excel解析结果是否一致")
    print("=" * 60)
    
    filename = None
    try:
        # 创建测试文件
        filename = create_test_excel_with_624_urls()
        
        # 测试URL池管理页面API
        url_pool_result = test_url_pool_api(filename)
        
        # 测试任务管理页面API
        task_manager_result = test_task_manager_api(filename)
        
        # 比较结果
        compare_results(url_pool_result, task_manager_result)
        
        # 总结
        print(f"\n" + "=" * 60)
        if url_pool_result and task_manager_result:
            url_pool_count = url_pool_result.get('total_urls', 0)
            task_manager_count = task_manager_result.get('total_urls', 0)
            
            if url_pool_count == task_manager_count and url_pool_count == 624:
                print("🎉 测试成功！两个API现在解析结果一致")
                print(f"✅ 都正确解析了 {url_pool_count} 个URL")
            elif url_pool_count == task_manager_count:
                print("✅ 两个API解析结果一致")
                print(f"📊 解析了 {url_pool_count} 个URL")
            else:
                print("⚠️ 两个API解析结果仍然不一致")
                print(f"📊 URL池管理页面: {url_pool_count}, 任务管理页面: {task_manager_count}")
        else:
            print("❌ 测试失败，有API调用出错")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
    
    finally:
        # 清理测试文件
        if filename:
            cleanup_file(filename)
    
    print("测试完成")

if __name__ == "__main__":
    main()
