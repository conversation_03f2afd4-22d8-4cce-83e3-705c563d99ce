name: monit-crawler
channels:
  - defaults
  - conda-forge
dependencies:
  - python=3.10
  - pip
  - nodejs=18
  - redis
  - postgresql
  - pip:
    # Core web framework
    - fastapi==0.104.1
    - uvicorn[standard]==0.24.0
    # Database
    - sqlalchemy==2.0.23
    - alembic==1.13.1
    - psycopg2-binary==2.9.9
    - asyncpg==0.29.0
    # Task queue and caching
    - redis==5.0.1
    - celery==5.3.4
    # HTTP client for API calls (replaces playwright, requests, beautifulsoup4)
    - httpx==0.25.2
    # Data processing
    - pandas==2.1.4
    - openpyxl==3.1.2
    # Configuration and validation
    - pydantic-settings==2.9.1
    # File handling
    - python-multipart==0.0.6
    - aiofiles==23.2.1
    # Authentication
    - python-jose[cryptography]==3.3.0
    - passlib[bcrypt]==1.7.4
    # WebSocket support
    - websockets==12.0
    # Testing
    - pytest==7.4.3
    - pytest-asyncio==0.21.1