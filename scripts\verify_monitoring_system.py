#!/usr/bin/env python3
"""
验证监控系统的完整功能
测试从后端监控服务到前端展示的完整链路
"""

import asyncio
import requests
import json
import time
from datetime import datetime


async def verify_backend_monitoring():
    """验证后端监控功能"""
    
    print("🔍 验证后端监控功能")
    print("=" * 40)
    
    # 设置路径以便导入
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
    
    try:
        from app.services.crawler_request_monitor import CrawlerRequestMonitor, RequestBuildMetrics
        
        # 1. 测试监控器创建
        print("📊 1. 测试监控器创建")
        monitor = CrawlerRequestMonitor()
        print(f"  ✅ 监控器创建成功")
        print(f"  📋 指标保留期: {monitor.metrics_retention_days} 天")
        print(f"  ⚠️ 性能阈值: {monitor.performance_thresholds}")
        
        # 2. 测试指标对象
        print("\n📈 2. 测试指标对象")
        metrics = RequestBuildMetrics(
            task_id="verify-test-task",
            execution_id="verify-exec-123",
            start_time=time.time()
        )
        
        metrics.urls_count = 10
        metrics.url_fetch_time = 1.5
        metrics.config_fetch_time = 0.8
        metrics.config_completeness = 0.9
        
        await asyncio.sleep(0.01)  # 确保有持续时间
        metrics.mark_completed("success")
        
        print(f"  ✅ 指标对象创建成功")
        print(f"  📊 任务ID: {metrics.task_id}")
        print(f"  📊 状态: {metrics.status}")
        print(f"  📊 耗时: {metrics.duration:.3f}s")
        print(f"  📊 URL数量: {metrics.urls_count}")
        
        # 3. 测试数据流日志
        print("\n🔄 3. 测试数据流日志")
        await monitor.log_data_flow_step(
            "验证测试步骤",
            "verify-test-task",
            ["https://test.com/1", "https://test.com/2"],
            1.2
        )
        print("  ✅ 数据流日志记录成功")
        
        # 4. 测试请求结构日志
        print("\n📤 4. 测试请求结构日志")
        test_request = {
            "request": {
                "urls": ["https://test.com/1"],
                "q": "验证测试查询",
                "priority": "high",
                "cache": True
            },
            "auth_context": {
                "user_id": "verify_test",
                "auth_method": "api_key"
            }
        }
        
        await monitor.log_request_structure(test_request, "verify-test-task")
        print("  ✅ 请求结构日志记录成功")
        
        # 5. 测试错误建议
        print("\n💡 5. 测试错误建议")
        suggestions = monitor._get_error_suggestions("ValueError", "Task has no active URLs")
        print(f"  ✅ 错误建议生成成功: {len(suggestions)} 条建议")
        for i, suggestion in enumerate(suggestions[:3], 1):
            print(f"    {i}. {suggestion}")
        
        await monitor.close()
        print("\n✅ 后端监控功能验证完成")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 后端监控功能验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_frontend_access():
    """验证前端页面访问"""
    
    print("\n🌐 验证前端页面访问")
    print("=" * 30)
    
    try:
        # 检查前端服务是否运行
        response = requests.get("http://localhost:3000", timeout=10)
        
        if response.status_code == 200:
            print("  ✅ 前端服务运行正常")
            print(f"  📊 状态码: {response.status_code}")
            
            # 检查监控页面是否可访问
            monitoring_response = requests.get("http://localhost:3000/crawler-request-monitoring", timeout=10)
            
            if monitoring_response.status_code == 200:
                print("  ✅ 监控页面可访问")
                print("  🎯 页面URL: http://localhost:3000/crawler-request-monitoring")
            else:
                print(f"  ⚠️ 监控页面访问异常: {monitoring_response.status_code}")
            
            return True
            
        else:
            print(f"  ❌ 前端服务异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ 前端服务连接失败 - 请确保Docker容器正在运行")
        return False
    except Exception as e:
        print(f"  ❌ 前端验证失败: {e}")
        return False


def verify_api_endpoints():
    """验证监控API端点"""
    
    print("\n📡 验证监控API端点")
    print("=" * 25)
    
    base_url = "http://localhost:8000"
    
    # 测试端点列表
    endpoints = [
        {
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/summary?days=1",
            "name": "指标摘要",
            "critical": True
        },
        {
            "url": f"{base_url}/api/v1/monitoring/system-health",
            "name": "系统健康",
            "critical": True
        },
        {
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/task/demo-task",
            "name": "任务指标",
            "critical": False
        }
    ]
    
    success_count = 0
    
    for endpoint in endpoints:
        print(f"📡 测试: {endpoint['name']}")
        
        try:
            response = requests.get(endpoint['url'], timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ 端点可访问 ({response.status_code})")
                
                try:
                    data = response.json()
                    if data.get('success'):
                        print("  ✅ 返回数据格式正确")
                        success_count += 1
                    else:
                        print(f"  ⚠️ 返回错误: {data.get('error', '未知错误')}")
                        if not endpoint['critical']:
                            success_count += 1  # 非关键端点的预期错误
                except json.JSONDecodeError:
                    print("  ⚠️ 返回数据不是有效JSON")
                    
            elif response.status_code == 404:
                print("  ❌ 端点未找到 (404)")
            elif response.status_code == 500:
                print("  ⚠️ 服务器内部错误 (500) - 可能是预期的（无数据）")
                if not endpoint['critical']:
                    success_count += 1  # 非关键端点的预期错误
            else:
                print(f"  ❌ 其他错误: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("  ❌ 连接失败 - 后端服务可能未启动")
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
        
        print()
    
    print(f"📊 API端点验证结果: {success_count}/{len(endpoints)} 成功")
    return success_count >= len([e for e in endpoints if e['critical']])


def verify_docker_services():
    """验证Docker服务状态"""
    
    print("\n🐳 验证Docker服务状态")
    print("=" * 25)
    
    import subprocess
    
    try:
        # 检查Docker容器状态
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=monit-", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("📋 Docker容器状态:")
            print(result.stdout)
            
            # 检查关键服务
            output = result.stdout
            services_status = {
                "monit-frontend": "monit-frontend" in output and "Up" in output,
                "monit-backend": "monit-backend" in output and "Up" in output,
                "monit-redis": "monit-redis" in output and "Up" in output
            }
            
            print("🔍 关键服务状态:")
            for service, status in services_status.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {service}: {'运行中' if status else '未运行'}")
            
            return all(services_status.values())
            
        else:
            print(f"❌ Docker命令执行失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Docker命令超时")
        return False
    except FileNotFoundError:
        print("❌ Docker命令未找到 - 请确保Docker已安装")
        return False
    except Exception as e:
        print(f"❌ Docker验证失败: {e}")
        return False


async def main():
    """主验证流程"""
    
    print("🎯 爬虫请求构建监控系统验证")
    print("🕐 验证时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
    
    results = {}
    
    # 1. 验证Docker服务
    results['docker'] = verify_docker_services()
    
    # 2. 验证后端监控功能
    results['backend'] = await verify_backend_monitoring()
    
    # 3. 验证API端点
    results['api'] = verify_api_endpoints()
    
    # 4. 验证前端访问
    results['frontend'] = verify_frontend_access()
    
    # 总结
    print("\n🎉 验证结果总结")
    print("=" * 20)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for component, result in results.items():
        status_icon = "✅" if result else "❌"
        print(f"  {status_icon} {component.upper()}: {'通过' if result else '失败'}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("\n🎉 监控系统验证完全成功！")
        print("\n🚀 可以开始使用监控功能:")
        print("  • 前端监控页面: http://localhost:3000/crawler-request-monitoring")
        print("  • API文档: http://localhost:8000/docs")
        print("  • 系统健康检查: http://localhost:8000/api/v1/monitoring/system-health")
    else:
        print("\n⚠️ 部分功能验证失败，请检查:")
        failed_components = [comp for comp, result in results.items() if not result]
        for comp in failed_components:
            print(f"  • {comp.upper()}: 需要检查和修复")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
