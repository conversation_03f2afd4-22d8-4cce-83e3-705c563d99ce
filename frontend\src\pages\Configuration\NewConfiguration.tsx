/**
 * 新架构配置管理页面
 * 集成爬取配置和后端配置管理功能
 * 
 * 注意：此页面已被拆分为独立的爬取配置和后端配置页面
 * 保留此文件作为兼容性重定向
 */

import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const NewConfiguration: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 根据当前路径重定向到对应的独立页面
    if (location.pathname === '/config') {
      // 默认重定向到爬取配置页面
      navigate('/config/crawler', { replace: true });
    }
  }, [navigate, location.pathname]);

  return null; // 重定向中，不显示任何内容
};

export default NewConfiguration;
