import axios from 'axios';
import type {
  ExcelUploadResponse,
  UrlPoolListResponse,
  UrlPoolBatchResponse,
  UrlPoolStatsResponse,
  UrlPoolQuery,
  UrlPoolBatchUpdate,
  UrlPoolItem
} from '../types/urlPool';

// 创建API客户端
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 300000, // 5分钟超时
});

/**
 * Excel上传和解析API
 */
export const uploadAndParseExcel = async (
  formData: FormData,
  queryParams?: string
): Promise<ExcelUploadResponse> => {
  const url = queryParams 
    ? `/api/v1/excel/upload-and-parse?${queryParams}`
    : '/api/v1/excel/upload-and-parse';
    
  const response = await api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 300000, // 5分钟超时
  });
  
  return response.data;
};

/**
 * 获取URL池列表
 */
export const getUrlPool = async (query: Partial<UrlPoolQuery> = {}): Promise<UrlPoolListResponse> => {
  const params = new URLSearchParams();

  // 添加查询参数
  if (query.page) params.append('page', query.page.toString());
  if (query.page_size) params.append('page_size', query.page_size.toString());
  if (query.platform) params.append('platform', query.platform);
  if (query.status) params.append('status', query.status);
  if (query.source_file) params.append('source_file', query.source_file);
  if (query.search) params.append('search', query.search);
  if (query.sort_by) params.append('sort_by', query.sort_by);
  if (query.sort_order) params.append('sort_order', query.sort_order);
  if (query.exclude_urls && query.exclude_urls.length > 0) {
    params.append('exclude_urls', query.exclude_urls.join(','));
  }

  const response = await api.get(`/api/v1/urls/pool?${params.toString()}`);
  return response.data;
};

/**
 * 获取所有符合筛选条件的URL ID列表
 */
export const getAllFilteredUrlIds = async (query: Partial<UrlPoolQuery> = {}): Promise<{
  success: boolean;
  data: string[];
  total: number;
}> => {
  const params = new URLSearchParams();

  // 添加查询参数（不包括分页参数）
  if (query.platform) params.append('platform', query.platform);
  if (query.status) params.append('status', query.status);
  if (query.source_file) params.append('source_file', query.source_file);
  if (query.search) params.append('search', query.search);
  if (query.sort_by) params.append('sort_by', query.sort_by);
  if (query.sort_order) params.append('sort_order', query.sort_order);

  // 设置ids_only参数
  params.append('ids_only', 'true');

  const response = await api.get(`/api/v1/urls/pool?${params.toString()}`);
  return response.data;
};

/**
 * 批量更新URL状态
 */
export const batchUpdateUrls = async (
  request: UrlPoolBatchUpdate
): Promise<UrlPoolBatchResponse> => {
  const response = await api.post('/api/v1/urls/pool/batch-update', request);
  return response.data;
};

/**
 * 获取URL池统计信息
 */
export const getUrlPoolStats = async (): Promise<UrlPoolStatsResponse> => {
  const response = await api.get('/api/v1/urls/pool/stats');
  return response.data;
};

/**
 * 获取支持的平台列表
 */
export const getSupportedPlatforms = async (): Promise<{
  success: boolean;
  platforms: string[];
  total: number;
}> => {
  const response = await api.get('/api/v1/urls/platforms');
  return response.data;
};

/**
 * 根据ID获取URL详情
 */
export const getUrlById = async (urlId: string): Promise<{
  success: boolean;
  data: UrlPoolItem;
}> => {
  const response = await api.get(`/api/v1/urls/pool/${urlId}`);
  return response.data;
};

/**
 * 批量获取URL详情
 */
export const getUrlsByIds = async (urlIds: string[]): Promise<{
  success: boolean;
  data: UrlPoolItem[];
}> => {
  if (urlIds.length === 0) {
    return { success: true, data: [] };
  }

  // 使用Promise.all并发获取所有URL详情
  const promises = urlIds.map(async (urlId) => {
    try {
      const response = await getUrlById(urlId);
      return response.data;
    } catch (error) {
      console.warn(`Failed to fetch URL ${urlId}:`, error);
      return null;
    }
  });

  const results = await Promise.all(promises);
  const validUrls = results.filter((url): url is UrlPoolItem => url !== null);

  return {
    success: true,
    data: validUrls
  };
};

/**
 * 根据ID删除URL
 */
export const deleteUrlById = async (urlId: string): Promise<{
  success: boolean;
  message: string;
}> => {
  const response = await api.delete(`/api/v1/urls/pool/${urlId}`);
  return response.data;
};

/**
 * 获取回收站URL列表
 */
export const getRecycleBin = async (page: number = 1, pageSize: number = 20): Promise<UrlPoolListResponse> => {
  const response = await api.get('/api/v1/urls/recycle-bin', {
    params: { page, page_size: pageSize }
  });
  return response.data;
};

/**
 * 从回收站恢复URL
 */
export const restoreFromRecycleBin = async (urlIds: string[]): Promise<UrlPoolBatchResponse> => {
  const response = await api.post('/api/v1/urls/recycle-bin/restore', {
    url_ids: urlIds
  });
  return response.data;
};

/**
 * 从回收站永久删除URL
 */
export const permanentDeleteFromRecycleBin = async (urlIds: string[]): Promise<UrlPoolBatchResponse> => {
  const response = await api.delete('/api/v1/urls/recycle-bin/permanent', {
    data: { url_ids: urlIds }
  });
  return response.data;
};



/**
 * 获取来源文件列表
 */
export const getSourceFiles = async (): Promise<{
  success: boolean;
  source_files: Array<{
    name: string;
    url_count: number;
  }>;
  total: number;
}> => {
  const response = await api.get('/api/v1/urls/pool/source-files');
  return response.data;
};

/**
 * 批量启用URL
 */
export const batchEnableUrls = async (urlIds: string[]): Promise<UrlPoolBatchResponse> => {
  return batchUpdateUrls({
    url_ids: urlIds,
    action: 'enable'
  });
};

/**
 * 批量禁用URL
 */
export const batchDisableUrls = async (urlIds: string[]): Promise<UrlPoolBatchResponse> => {
  return batchUpdateUrls({
    url_ids: urlIds,
    action: 'disable'
  });
};

/**
 * 批量删除URL
 */
export const batchDeleteUrls = async (urlIds: string[]): Promise<UrlPoolBatchResponse> => {
  return batchUpdateUrls({
    url_ids: urlIds,
    action: 'delete'
  });
};

/**
 * URL池搜索
 */
export const searchUrls = async (
  searchTerm: string,
  options: Partial<UrlPoolQuery> = {}
): Promise<UrlPoolListResponse> => {
  return getUrlPool({
    ...options,
    search: searchTerm
  });
};

/**
 * 按平台筛选URL
 */
export const getUrlsByPlatform = async (
  platform: string,
  options: Partial<UrlPoolQuery> = {}
): Promise<UrlPoolListResponse> => {
  return getUrlPool({
    ...options,
    platform
  });
};

/**
 * 按状态筛选URL
 */
export const getUrlsByStatus = async (
  status: 'active' | 'disabled',
  options: Partial<UrlPoolQuery> = {}
): Promise<UrlPoolListResponse> => {
  return getUrlPool({
    ...options,
    status
  });
};

/**
 * 按来源文件筛选URL
 */
export const getUrlsBySourceFile = async (
  sourceFile: string,
  options: Partial<UrlPoolQuery> = {}
): Promise<UrlPoolListResponse> => {
  return getUrlPool({
    ...options,
    source_file: sourceFile
  });
};

/**
 * 获取URL池概览数据
 */
export const getUrlPoolOverview = async () => {
  const [stats, platforms, sourceFiles] = await Promise.all([
    getUrlPoolStats(),
    getSupportedPlatforms(),
    getSourceFiles()
  ]);
  
  return {
    stats: stats.data,
    platforms: platforms.platforms,
    sourceFiles: sourceFiles.source_files
  };
};

/**
 * 导出URL池数据（预留接口）
 */
export const exportUrlPool = async (
  format: 'excel' | 'csv' | 'json' = 'excel',
  filter?: Partial<UrlPoolQuery>
): Promise<Blob> => {
  // 这里可以实现导出功能
  // 暂时返回空的Blob
  return new Blob();
};

/**
 * 验证URL格式
 */
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return url.startsWith('http://') || url.startsWith('https://');
  } catch {
    return false;
  }
};

/**
 * 获取平台显示名称
 */
export const getPlatformDisplayName = (platform: string): string => {
  const displayNames: Record<string, string> = {
    'mercadolibre': 'MercadoLibre',
    'amazon': 'Amazon',
    'ebay': 'eBay',
    'aliexpress': 'AliExpress',
    'shopee': 'Shopee',
    'unknown': '未知平台'
  };
  
  return displayNames[platform] || platform.charAt(0).toUpperCase() + platform.slice(1);
};

/**
 * 获取状态显示名称和颜色
 */
export const getStatusDisplay = (status: string): { name: string; color: string } => {
  const statusMap: Record<string, { name: string; color: string }> = {
    'active': { name: '活跃', color: 'green' },
    'disabled': { name: '禁用', color: 'red' },
    'deleted': { name: '已删除', color: 'gray' }
  };
  
  return statusMap[status] || { name: status, color: 'default' };
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化处理时间
 */
export const formatProcessingTime = (seconds: number): string => {
  if (seconds < 1) {
    return `${Math.round(seconds * 1000)}ms`;
  } else if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  } else {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
};

/**
 * 生成URL预览文本
 */
export const generateUrlPreview = (url: string, maxLength: number = 50): string => {
  if (url.length <= maxLength) {
    return url;
  }
  
  const start = url.substring(0, maxLength / 2);
  const end = url.substring(url.length - maxLength / 2);
  return `${start}...${end}`;
};
