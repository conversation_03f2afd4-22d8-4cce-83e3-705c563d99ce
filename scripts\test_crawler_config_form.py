#!/usr/bin/env python3
"""
测试爬虫配置表单
验证重复优先级选项修复是否正常
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any


class CrawlerConfigFormTester:
    """爬虫配置表单测试器"""
    
    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.backend_url = backend_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_create_config_with_priority(self) -> Dict[str, Any]:
        """测试创建配置时优先级字段是否正常工作"""
        print("🧪 测试创建配置（包含优先级）...")
        
        try:
            # 创建配置数据，包含优先级设置
            config_data = {
                "name": "测试配置_优先级验证",
                "description": "验证优先级字段修复后的创建流程",
                "config_type": "batch_task",
                "platform": "mercadolibre",
                "llm_query": "提取商品信息",
                "llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}}}',
                "cache_enabled": True,
                "priority": "high",  # 测试优先级字段
                "auth_method": "api_key",
                "api_key_prefix": "test",
                "user_agent": "MonIt-Test/1.0",
                "tags": ["测试", "优先级验证"],
                "is_active": True
            }
            
            url = f"{self.backend_url}/api/v1/crawl-configs"
            async with self.session.post(url, json=config_data) as response:
                if response.status == 200:
                    data = await response.json()
                    created_config = data.get('data', {})
                    
                    # 调试：打印完整的配置数据
                    print(f"  🔍 调试 - 完整响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

                    # 验证优先级是否正确保存
                    actual_priority = created_config.get('priority')
                    if actual_priority == 'high':
                        print("  ✅ 配置创建成功，优先级字段正确保存")
                        print(f"  📝 配置ID: {created_config.get('id', 'N/A')}")
                        print(f"  🎯 优先级: {actual_priority}")
                        return {"success": True, "data": data, "config_id": created_config.get('id')}
                    else:
                        print(f"  ❌ 优先级字段保存错误: 期望 'high', 实际 '{actual_priority}'")
                        print(f"  🔍 调试 - 配置对象keys: {list(created_config.keys())}")
                        return {"success": False, "error": "优先级字段保存错误"}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 配置创建失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 配置创建异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_update_config_priority(self, config_id: str) -> Dict[str, Any]:
        """测试更新配置优先级"""
        print("🔄 测试更新配置优先级...")
        
        try:
            # 更新配置数据，修改优先级
            update_data = {
                "name": "测试配置_优先级验证_更新",
                "description": "验证优先级字段更新功能",
                "config_type": "batch_task",
                "platform": "mercadolibre",
                "llm_query": "提取商品信息",
                "llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}}}',
                "cache_enabled": False,
                "priority": "low",  # 修改优先级从 high 到 low
                "auth_method": "api_key",
                "api_key_prefix": "test",
                "user_agent": "MonIt-Test/1.0",
                "tags": ["测试", "优先级更新"],
                "is_active": True
            }
            
            url = f"{self.backend_url}/api/v1/crawl-configs/{config_id}"
            async with self.session.put(url, json=update_data) as response:
                if response.status == 200:
                    data = await response.json()
                    updated_config = data.get('data', {})
                    
                    # 验证优先级是否正确更新
                    if updated_config.get('priority') == 'low':
                        print("  ✅ 配置更新成功，优先级字段正确更新")
                        print(f"  🎯 新优先级: {updated_config.get('priority', 'N/A')}")
                        return {"success": True, "data": data}
                    else:
                        print(f"  ❌ 优先级字段更新错误: 期望 'low', 实际 '{updated_config.get('priority')}'")
                        return {"success": False, "error": "优先级字段更新错误"}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 配置更新失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 配置更新异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_get_config_with_priority(self, config_id: str) -> Dict[str, Any]:
        """测试获取配置时优先级字段是否正确"""
        print("📖 测试获取配置（验证优先级）...")
        
        try:
            url = f"{self.backend_url}/api/v1/crawl-configs/{config_id}"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    config = data.get('data', {})
                    
                    priority = config.get('priority')
                    if priority in ['high', 'medium', 'low']:
                        print(f"  ✅ 配置获取成功，优先级字段正确: {priority}")
                        return {"success": True, "data": data}
                    else:
                        print(f"  ❌ 优先级字段异常: {priority}")
                        return {"success": False, "error": f"优先级字段异常: {priority}"}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 配置获取失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 配置获取异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup_test_config(self, config_id: str):
        """清理测试配置"""
        print("🧹 清理测试配置...")
        
        try:
            url = f"{self.backend_url}/api/v1/crawl-configs/{config_id}"
            async with self.session.delete(url) as response:
                if response.status == 200:
                    print("  ✅ 测试配置清理成功")
                else:
                    print(f"  ⚠️ 测试配置清理失败: {response.status}")
        except Exception as e:
            print(f"  ⚠️ 测试配置清理异常: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始爬虫配置表单测试...\n")
        
        results = {}
        config_id = None
        
        # 1. 测试创建配置
        create_result = await self.test_create_config_with_priority()
        results["create_config"] = create_result
        
        if create_result["success"]:
            config_id = create_result.get("config_id")
            
            # 2. 测试获取配置
            get_result = await self.test_get_config_with_priority(config_id)
            results["get_config"] = get_result
            
            # 3. 测试更新配置
            update_result = await self.test_update_config_priority(config_id)
            results["update_config"] = update_result
        
        # 清理测试数据
        if config_id:
            await self.cleanup_test_config(config_id)
        
        # 总结测试结果
        print("\n📊 测试结果总结:")
        success_count = sum(1 for result in results.values() if result.get("success"))
        total_count = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result.get("success") else "❌ 失败"
            print(f"  {test_name}: {status}")
            if not result.get("success") and "error" in result:
                print(f"    错误: {result['error']}")
        
        print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！爬虫配置表单优先级字段修复成功！")
        else:
            print("⚠️  部分测试失败，需要检查配置表单实现")
        
        return results


async def main():
    """主函数"""
    async with CrawlerConfigFormTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
