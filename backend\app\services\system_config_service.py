"""
系统配置服务
管理爬虫系统的系统级配置参数
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from config.settings import settings


class SystemCrawlerConfig(BaseModel):
    """系统级爬虫配置"""
    
    # 性能配置
    max_concurrent_tasks: int = Field(description="最大并发任务数")
    batch_timeout: int = Field(description="批次超时时间（秒）")
    
    # 回调配置
    default_callback_url: Optional[str] = Field(description="默认回调URL")
    
    # API配置
    api_base_url: str = Field(description="爬虫API基础URL")
    api_timeout: float = Field(description="API超时时间")
    api_max_retries: int = Field(description="API最大重试次数")
    
    # 限流配置
    rate_limit_requests: int = Field(description="速率限制请求数")
    rate_limit_period: int = Field(description="速率限制周期（秒）")


class SystemConfigService:
    """系统配置服务"""
    
    @staticmethod
    def get_crawler_config() -> SystemCrawlerConfig:
        """获取爬虫系统配置"""
        return SystemCrawlerConfig(
            max_concurrent_tasks=settings.CRAWLER_MAX_CONCURRENT_TASKS,
            batch_timeout=settings.CRAWLER_BATCH_TIMEOUT,
            default_callback_url=settings.CRAWLER_DEFAULT_CALLBACK_URL,
            api_base_url=settings.CRAWLER_API_BASE_URL,
            api_timeout=settings.CRAWLER_API_TIMEOUT,
            api_max_retries=settings.CRAWLER_API_MAX_RETRIES,
            rate_limit_requests=settings.CRAWLER_API_RATE_LIMIT_REQUESTS,
            rate_limit_period=settings.CRAWLER_API_RATE_LIMIT_PERIOD
        )
    
    @staticmethod
    def get_system_config_for_task(user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        为任务获取完整配置（用户配置 + 系统配置）
        
        Args:
            user_config: 用户级配置
            
        Returns:
            完整的任务配置
        """
        system_config = SystemConfigService.get_crawler_config()
        
        # 合并用户配置和系统配置
        task_config = {
            **user_config,
            # 系统级配置覆盖用户配置
            "max_concurrent_tasks": system_config.max_concurrent_tasks,
            "batch_timeout": system_config.batch_timeout,
            # 回调URL：优先使用用户配置，如果用户没有设置则使用系统默认值
            "callback_url": user_config.get("callback_url") or system_config.default_callback_url or "",
        }
        
        return task_config
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """获取系统配置信息（用于前端显示）"""
        config = SystemConfigService.get_crawler_config()
        
        return {
            "crawler_system_config": {
                "max_concurrent_tasks": config.max_concurrent_tasks,
                "batch_timeout": config.batch_timeout,
                "default_callback_url": config.default_callback_url,
                "api_base_url": config.api_base_url,
                "api_timeout": config.api_timeout,
                "api_max_retries": config.api_max_retries,
                "rate_limit_requests": config.rate_limit_requests,
                "rate_limit_period": config.rate_limit_period
            },
            "environment": settings.ENVIRONMENT,
            "version": settings.VERSION
        }
