# MonIt 电商爬虫监控系统设计文档 v2.0

## 文档信息
- **文档版本**: v2.0
- **创建日期**: 2025-08-09
- **最后更新**: 2025-08-09
- **作者**: MonIt架构团队
- **审核人**: 技术总监
- **状态**: 已发布

## 变更历史
| 版本 | 日期 | 作者 | 变更内容 |
|------|------|------|----------|
| v2.0 | 2025-08-09 | 架构团队 | 基于v2.0架构重构的完整设计 |
| v1.0 | 2025-01-01 | 初始团队 | 初始架构设计 |

## 1. 系统架构设计

### 1.1 整体架构概览

MonIt系统采用现代化微服务分层架构，实现高性能、高可用的电商爬虫监控系统。

```mermaid
graph TB
    %% 用户层
    subgraph "👤 用户层"
        User[用户浏览器]
    end

    %% 前端层
    subgraph "🖥️ 前端层"
        WebUI[React Web界面]
        UrlPool[URL池管理]
        TaskMgmt[任务管理]
        Monitor[监控面板]
    end

    %% API层
    subgraph "🌐 API层"
        APIGateway[FastAPI网关]
        Auth[认证授权]
        CORS[跨域处理]
    end

    %% 业务服务层
    subgraph "⚙️ 业务服务层"
        ExcelService[Excel解析服务]
        TaskService[任务管理服务]
        CrawlerService[爬虫配置服务]
        MonitorService[监控服务]
    end

    %% 任务调度层
    subgraph "📋 任务调度层"
        CeleryBeat[Celery Beat定时器]
        CeleryWorker[Celery Worker]
        TaskQueue[任务队列]
    end

    %% 爬虫执行层
    subgraph "🕷️ 爬虫执行层"
        CrawlerEngine[爬虫引擎]
        APIClient[外部爬虫API客户端]
        RateLimit[限流控制器]
    end

    %% 数据存储层
    subgraph "💾 数据存储层"
        TimescaleDB[(TimescaleDB时序数据库)]
        Redis[(Redis统一存储)]
        FileStorage[文件存储]
    end

    %% 监控运维层
    subgraph "📊 监控运维层"
        Prometheus[Prometheus指标收集]
        Grafana[Grafana监控面板]
        ELK[ELK日志分析]
        AlertManager[告警管理]
    end

    %% 连接关系
    User --> WebUI
    WebUI --> APIGateway
    APIGateway --> TaskService
    TaskService --> CeleryBeat
    CeleryBeat --> CeleryWorker
    CeleryWorker --> CrawlerEngine
    CrawlerEngine --> TimescaleDB
    CrawlerEngine --> Redis
    Prometheus --> Grafana
```

### 1.2 核心架构原则

#### 1.2.1 统一数据源原则
- **单一数据源**: 所有任务数据统一存储在Redis中
- **数据一致性**: 避免双重存储逻辑的复杂性
- **性能优化**: Redis高性能缓存提升系统响应速度

#### 1.2.2 异步处理原则
- **分布式任务**: 基于Celery的分布式任务处理
- **非阻塞操作**: FastAPI异步处理提升并发能力
- **事件驱动**: 基于事件的异步通信机制

#### 1.2.3 微服务架构原则
- **服务解耦**: 各服务独立部署和扩展
- **职责分离**: 每个服务专注特定业务领域
- **接口标准**: 统一的API接口规范

### 1.3 新架构设计（v2.0重构）

#### 1.3.1 架构公式
```
CrawlerConfig (爬取配置) + BackendConfig (后端配置) = CrawlerWorker
TaskAssignment = Task + CrawlerWorker集合 (带互斥检查)
```

#### 1.3.2 核心组件关系
```mermaid
graph TD
    A[CrawlerConfig 爬取配置] --> C[CrawlerWorker 爬虫工作器]
    B[BackendConfig 后端配置] --> C
    C --> D[TaskAssignment 任务分配]
    E[Task 任务] --> D
    D --> F[任务执行]
    
    subgraph "配置层"
        A
        B
    end
    
    subgraph "工作器层"
        C
    end
    
    subgraph "任务层"
        E
        D
        F
    end
```

## 2. 核心组件设计

### 2.1 CrawlerConfig（爬取配置）

#### 2.1.1 组件职责
定义爬虫的行为参数和配置模板，支持不同电商平台的个性化配置。

#### 2.1.2 核心配置项
```json
{
  "browser_config": {
    "headless": true,
    "window_size": "1920x1080",
    "timeout": 30000
  },
  "llm_config": {
    "provider": "openai",
    "model": "gpt-4",
    "api_key": "encrypted_key"
  },
  "crawler_config": {
    "max_depth": 3,
    "delay": 1000,
    "concurrent": 2
  },
  "schema_config": {
    "fields": ["name", "price", "rating"],
    "selectors": {...}
  }
}
```

#### 2.1.3 API接口设计
- `GET /api/v1/crawler-configs/` - 获取所有配置
- `POST /api/v1/crawler-configs/` - 创建配置
- `PUT /api/v1/crawler-configs/{id}/` - 更新配置
- `POST /api/v1/crawler-configs/{id}/validate/` - 验证配置

### 2.2 BackendConfig（后端配置）

#### 2.2.1 组件职责
定义后端服务的连接和性能参数，管理与外部服务的集成。

#### 2.2.2 核心配置项
```json
{
  "connection": {
    "host": "api.crawler.com",
    "port": 443,
    "protocol": "https",
    "timeout": 30
  },
  "performance": {
    "max_retries": 3,
    "performance_level": "high"
  },
  "auth": {
    "type": "api_key",
    "credentials": "encrypted_credentials"
  }
}
```

### 2.3 CrawlerWorker（爬虫工作器）

#### 2.3.1 组件职责
CrawlerConfig和BackendConfig的组合体，作为可执行的工作单元。

#### 2.3.2 核心属性
```python
class CrawlerWorker:
    crawler_config_id: str
    backend_config_id: str
    status: WorkerStatus  # active/inactive/error
    priority: int
    max_concurrent_tasks: int
    current_tasks: int
    total_tasks_completed: int
    total_tasks_failed: int
```

### 2.4 TaskAssignment（任务分配）

#### 2.4.1 组件职责
将任务分配给CrawlerWorker集合，支持负载均衡和互斥检查。

#### 2.4.2 分配策略
- **round_robin**: 轮询分配
- **weighted**: 权重分配
- **least_connections**: 最少连接
- **random**: 随机分配

#### 2.4.3 互斥控制
- **URL级别**: 同一URL不能同时被多个Worker处理
- **域名级别**: 同一域名的并发限制
- **Worker级别**: Worker的任务数量限制
- **全局级别**: 系统总体并发限制

## 3. 数据存储设计

### 3.1 Redis统一存储结构

#### 3.1.1 设计目标
- 统一数据源，消除双重存储逻辑
- 高性能缓存，提升系统响应速度
- 分层存储，支持复杂查询需求

#### 3.1.2 存储结构
```redis
# 监控任务
monitoring_tasks/
├── tasks/{task_id}/
│   ├── basic                     # 基本信息 (Hash)
│   ├── config                    # 任务配置 (Hash)
│   ├── schedule                  # 调度配置 (Hash)
│   ├── stats                     # 统计信息 (Hash)
│   └── urls                      # 关联的URL ID列表 (Set)
├── indexes/
│   ├── all_task_ids              # 所有任务ID (Set)
│   ├── active_tasks              # 活跃任务ID (Set)
│   └── by_status/{status}        # 按状态分组的任务ID (Set)
└── url_tasks/{url_id}           # URL反向关联 (Set)

# URL池
url_pool/
├── urls/{url_id}/
│   ├── basic                     # 基本信息 (Hash)
│   ├── metadata                  # 元数据 (Hash)
│   └── stats                     # 统计信息 (Hash)
└── indexes/
    ├── all_url_ids               # 所有URL ID (Set)
    ├── by_platform/{platform}   # 按平台分组 (Set)
    └── by_status/{status}        # 按状态分组 (Set)

# 爬虫配置
crawler_configs/{config_id} -> CrawlerConfigData (JSON)
backend_configs/{backend_id} -> BackendConfigData (JSON)
crawler_workers/{worker_id} -> CrawlerWorker (JSON)
task_assignments/{assignment_id} -> TaskAssignment (JSON)

# 互斥锁
mutex:url:{url_hash} -> {worker_id, timestamp}
mutex:domain:{domain} -> {current_count, last_reset}
mutex:worker:{worker_id} -> {current_tasks, max_tasks}
```

### 3.2 TimescaleDB时间序列存储

#### 3.2.1 表结构设计
```sql
-- 商品快照表（时间序列）
CREATE TABLE product_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,
    product_id INTEGER NOT NULL,
    url TEXT NOT NULL,
    name_cn VARCHAR(500),
    name_original VARCHAR(500),
    price_current DECIMAL(10,2),
    price_original DECIMAL(10,2),
    discount_percentage INTEGER,
    rating DECIMAL(3,2),
    reviews_count INTEGER,
    stock_status VARCHAR(50),
    main_image_url TEXT,
    images JSONB,
    extra_data JSONB,
    crawl_duration_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    PRIMARY KEY (timestamp, product_id)
);

-- 转换为TimescaleDB hypertable
SELECT create_hypertable('product_snapshots', 'timestamp');
```

## 4. API设计

### 4.1 RESTful API规范

#### 4.1.1 URL设计规范
- 使用名词复数形式：`/api/v1/tasks/`
- 版本控制：`/api/v1/`
- 资源嵌套：`/api/v1/tasks/{id}/urls/`

#### 4.1.2 HTTP方法规范
- `GET`: 查询资源
- `POST`: 创建资源
- `PUT`: 更新整个资源
- `PATCH`: 部分更新资源
- `DELETE`: 删除资源

#### 4.1.3 响应格式规范
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2025-08-09T10:00:00Z",
  "request_id": "uuid-string"
}
```

### 4.2 核心API接口

#### 4.2.1 URL池管理API
```
GET    /api/v1/url-pool/                    # 获取URL列表
POST   /api/v1/url-pool/                    # 添加URL
PUT    /api/v1/url-pool/{id}/               # 更新URL
DELETE /api/v1/url-pool/{id}/               # 删除URL
POST   /api/v1/url-pool/batch/              # 批量操作
POST   /api/v1/url-pool/upload-excel/       # Excel上传
```

#### 4.2.2 任务管理API
```
GET    /api/v1/tasks/                       # 获取任务列表
POST   /api/v1/tasks/                       # 创建任务
PUT    /api/v1/tasks/{id}/                  # 更新任务
DELETE /api/v1/tasks/{id}/                  # 删除任务
POST   /api/v1/tasks/{id}/start/            # 启动任务
POST   /api/v1/tasks/{id}/stop/             # 停止任务
GET    /api/v1/tasks/{id}/stats/            # 获取任务统计
```

#### 4.2.3 爬虫配置API
```
GET    /api/v1/crawler-configs/             # 获取爬虫配置
POST   /api/v1/crawler-configs/             # 创建配置
PUT    /api/v1/crawler-configs/{id}/        # 更新配置
POST   /api/v1/crawler-configs/{id}/validate/ # 验证配置

GET    /api/v1/backend-configs/             # 获取后端配置
POST   /api/v1/backend-configs/             # 创建配置
POST   /api/v1/backend-configs/{id}/test/   # 测试连接

GET    /api/v1/crawler-workers/             # 获取Worker
POST   /api/v1/crawler-workers/             # 创建Worker
POST   /api/v1/crawler-workers/check-compatibility/ # 兼容性检查
```

## 5. 前端架构设计

### 5.1 技术栈选择
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design (最新版本)
- **状态管理**: Redux Toolkit
- **路由**: React Router v6
- **HTTP客户端**: Axios
- **图表库**: ECharts

### 5.2 组件架构
```
src/
├── components/                 # 通用组件
│   ├── Layout/                # 布局组件
│   ├── Forms/                 # 表单组件
│   └── Charts/                # 图表组件
├── pages/                     # 页面组件
│   ├── UrlPool/               # URL池管理
│   ├── TaskManagement/        # 任务管理
│   ├── Configuration/         # 配置管理
│   └── Monitoring/            # 监控面板
├── services/                  # API服务
│   ├── urlPoolApi.ts          # URL池API
│   ├── taskApi.ts             # 任务API
│   └── configApi.ts           # 配置API
├── store/                     # Redux状态管理
│   ├── slices/                # 状态切片
│   └── index.ts               # Store配置
└── utils/                     # 工具函数
    ├── request.ts             # HTTP请求封装
    └── constants.ts           # 常量定义
```

### 5.3 状态管理设计
```typescript
interface RootState {
  urlPool: {
    urls: UrlItem[];
    filters: FilterState;
    loading: boolean;
  };
  tasks: {
    tasks: Task[];
    currentTask: Task | null;
    loading: boolean;
  };
  config: {
    crawlerConfigs: CrawlerConfig[];
    backendConfigs: BackendConfig[];
    workers: CrawlerWorker[];
  };
  monitoring: {
    metrics: SystemMetrics;
    alerts: Alert[];
  };
}
```

## 6. 安全设计

### 6.1 认证授权
- **JWT Token**: 基于角色的访问控制
- **API密钥**: 支持多种认证方式
- **权限管理**: 细粒度的操作权限控制

### 6.2 数据安全
- **配置加密**: 敏感配置信息加密存储
- **传输加密**: HTTPS/TLS加密传输
- **输入验证**: 严格的数据验证和清理

### 6.3 系统安全
- **防护机制**: SQL注入、XSS攻击防护
- **访问限制**: IP白名单、频率限制
- **审计日志**: 完整的操作审计记录

## 7. 性能设计

### 7.1 性能目标
- **API响应时间**: < 100ms (95th percentile)
- **并发处理能力**: 1000+ requests/second
- **任务处理速度**: 9.95 URLs/秒
- **系统可用性**: > 99.5%

### 7.2 性能优化策略
- **缓存机制**: Redis多层缓存
- **异步处理**: 非阻塞I/O操作
- **连接池**: 数据库连接池优化
- **负载均衡**: 智能任务分配

### 7.3 监控指标
- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: 响应时间、吞吐量、错误率
- **业务指标**: 任务成功率、数据准确性

## 8. 部署架构

### 8.1 容器化部署
```yaml
# docker-compose.yml
services:
  backend:
    image: monit-backend:latest
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=***************************************/monit
  
  frontend:
    image: monit-frontend:latest
    environment:
      - REACT_APP_API_URL=http://backend:8000
  
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
  
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=monit
      - POSTGRES_USER=monit_user
      - POSTGRES_PASSWORD=strong_password
```

### 8.2 监控部署
```yaml
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  
  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_password
    volumes:
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
```

## 9. 总结

MonIt v2.0设计实现了以下关键目标：

1. **架构简化**: 移除复杂抽象层，采用清晰的组件关系
2. **性能提升**: 统一Redis存储，优化数据访问路径
3. **可维护性**: 模块化设计，代码结构清晰
4. **可扩展性**: 支持水平扩展和功能扩展
5. **可靠性**: 完善的错误处理和故障恢复机制

新架构为MonIt系统的长期发展奠定了坚实的基础，支持未来的功能扩展和性能优化需求。
