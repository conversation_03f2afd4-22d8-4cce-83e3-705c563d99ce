import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse, Task, ProductSnapshot, CrawlRule, SystemMetrics, LogEntry } from '../types';

// API客户端配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证token等
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => response,
      (error) => {
        // 统一错误处理
        if (error.response?.status === 401) {
          // 处理认证失败
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // 通用请求方法
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    params?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.request({
        method,
        url,
        data,
        params,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message);
    }
  }

  // 任务相关API
  async getTasks(params?: {
    page?: number;
    page_size?: number;
    status?: string;
    priority?: string;
  }): Promise<ApiResponse<Task[]>> {
    return this.request('GET', '/tasks', undefined, params);
  }

  async getTask(id: string): Promise<ApiResponse<Task>> {
    return this.request('GET', `/tasks/${id}`);
  }

  async createTask(task: Partial<Task>): Promise<ApiResponse<Task>> {
    return this.request('POST', '/tasks', task);
  }

  async updateTask(id: string, task: Partial<Task>): Promise<ApiResponse<Task>> {
    return this.request('PUT', `/tasks/${id}`, task);
  }

  async deleteTask(id: string): Promise<ApiResponse<void>> {
    return this.request('DELETE', `/tasks/${id}`);
  }

  async startTask(id: string): Promise<ApiResponse<void>> {
    return this.request('POST', `/tasks/${id}/start`);
  }

  async stopTask(id: string): Promise<ApiResponse<void>> {
    return this.request('POST', `/tasks/${id}/stop`);
  }

  async getTaskResults(
    taskId: string,
    params?: { page?: number; page_size?: number }
  ): Promise<ApiResponse<ProductSnapshot[]>> {
    return this.request('GET', `/tasks/${taskId}/results`, undefined, params);
  }

  // 爬虫规则相关API
  async getCrawlRules(params?: {
    page?: number;
    page_size?: number;
    domain?: string;
  }): Promise<ApiResponse<CrawlRule[]>> {
    return this.request('GET', '/crawl-rules', undefined, params);
  }

  async getCrawlRule(id: string): Promise<ApiResponse<CrawlRule>> {
    return this.request('GET', `/crawl-rules/${id}`);
  }

  async createCrawlRule(rule: Partial<CrawlRule>): Promise<ApiResponse<CrawlRule>> {
    return this.request('POST', '/crawl-rules', rule);
  }

  async updateCrawlRule(id: string, rule: Partial<CrawlRule>): Promise<ApiResponse<CrawlRule>> {
    return this.request('PUT', `/crawl-rules/${id}`, rule);
  }

  async deleteCrawlRule(id: string): Promise<ApiResponse<void>> {
    return this.request('DELETE', `/crawl-rules/${id}`);
  }

  // 数据分析相关API
  async getProductSnapshots(params?: {
    page?: number;
    page_size?: number;
    task_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<ProductSnapshot[]>> {
    return this.request('GET', '/product-snapshots', undefined, params);
  }

  async getProductTrends(params: {
    url: string;
    start_date: string;
    end_date: string;
  }): Promise<ApiResponse<ProductSnapshot[]>> {
    return this.request('GET', '/product-trends', undefined, params);
  }

  async exportData(params: {
    task_id?: string;
    start_date?: string;
    end_date?: string;
    format: 'csv' | 'excel';
  }): Promise<Blob> {
    const response = await this.client.get('/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  }

  // 系统监控相关API
  async getSystemMetrics(params?: {
    start_time?: string;
    end_time?: string;
    interval?: string;
  }): Promise<ApiResponse<SystemMetrics[]>> {
    return this.request('GET', '/system/metrics', undefined, params);
  }

  async getSystemStatus(): Promise<ApiResponse<{
    status: string;
    uptime: number;
    version: string;
    services: Record<string, boolean>;
  }>> {
    return this.request('GET', '/system/status');
  }

  // 日志相关API
  async getLogs(params?: {
    page?: number;
    page_size?: number;
    level?: string;
    module?: string;
    task_id?: string;
    start_time?: string;
    end_time?: string;
    keyword?: string;
  }): Promise<ApiResponse<LogEntry[]>> {
    return this.request('GET', '/logs', undefined, params);
  }

  // 文件上传API
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{
    filename: string;
    url: string;
    size: number;
  }>> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  }

  // 配置相关API
  async getConfig(): Promise<ApiResponse<Record<string, any>>> {
    return this.request('GET', '/config');
  }

  async updateConfig(config: Record<string, any>): Promise<ApiResponse<void>> {
    return this.request('PUT', '/config', config);
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient();

// 导出具体的API方法
export const taskApi = {
  getTasks: apiClient.getTasks.bind(apiClient),
  getTask: apiClient.getTask.bind(apiClient),
  createTask: apiClient.createTask.bind(apiClient),
  updateTask: apiClient.updateTask.bind(apiClient),
  deleteTask: apiClient.deleteTask.bind(apiClient),
  startTask: apiClient.startTask.bind(apiClient),
  stopTask: apiClient.stopTask.bind(apiClient),
  getTaskResults: apiClient.getTaskResults.bind(apiClient),
};

export const crawlRuleApi = {
  getCrawlRules: apiClient.getCrawlRules.bind(apiClient),
  getCrawlRule: apiClient.getCrawlRule.bind(apiClient),
  createCrawlRule: apiClient.createCrawlRule.bind(apiClient),
  updateCrawlRule: apiClient.updateCrawlRule.bind(apiClient),
  deleteCrawlRule: apiClient.deleteCrawlRule.bind(apiClient),
};

export const dataApi = {
  getProductSnapshots: apiClient.getProductSnapshots.bind(apiClient),
  getProductTrends: apiClient.getProductTrends.bind(apiClient),
  exportData: apiClient.exportData.bind(apiClient),
};

export const systemApi = {
  getSystemMetrics: apiClient.getSystemMetrics.bind(apiClient),
  getSystemStatus: apiClient.getSystemStatus.bind(apiClient),
  getLogs: apiClient.getLogs.bind(apiClient),
};

export const fileApi = {
  uploadFile: apiClient.uploadFile.bind(apiClient),
};

export const configApi = {
  getConfig: apiClient.getConfig.bind(apiClient),
  updateConfig: apiClient.updateConfig.bind(apiClient),
};
