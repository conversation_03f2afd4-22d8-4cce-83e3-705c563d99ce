/**
 * 爬虫任务执行模态框组件
 * 用于配置和执行爬虫任务
 */

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Alert,
  Card,
  Row,
  Col,
  Tag,
  message,
  Spin,
  Progress,
  Divider,
  Typography
} from 'antd';
import {
  PlayCircleOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { 
  newCrawlerApi, 
  CrawlerTaskConfig, 
  ExecuteTaskRequest,
  ExecutionResult
} from '../../../services/newCrawlerApi';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

interface CrawlerExecutionModalProps {
  config: CrawlerTaskConfig | null;
  onExecute: (result: ExecutionResult) => void;
  onCancel: () => void;
}

const CrawlerExecutionModal: React.FC<CrawlerExecutionModalProps> = ({
  config,
  onExecute,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [loading] = useState(false);
  const [executing, setExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [urlCount, setUrlCount] = useState(0);

  // 初始化表单
  useEffect(() => {
    if (config) {
      form.setFieldsValue({
        execution_mode: 'batch',
        priority: config.priority,
        callback_url: config.callback_url,
        monitoring_task_id: `task_${Date.now()}`,
        urls: '',
        metadata: JSON.stringify({
          config_name: config.name,
          platform: config.platform,
          created_at: new Date().toISOString()
        }, null, 2)
      });
    }
  }, [config, form]);

  // 处理URL输入变化
  const handleUrlsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const urls = e.target.value
      .split('\n')
      .map(url => url.trim())
      .filter(url => url.length > 0);
    setUrlCount(urls.length);
  };

  // 执行任务
  const handleExecute = async () => {
    if (!config) {
      message.error('配置信息不存在');
      return;
    }

    try {
      const values = await form.validateFields();
      setExecuting(true);

      // 解析URL列表
      const urls = values.urls
        .split('\n')
        .map((url: string) => url.trim())
        .filter((url: string) => url.length > 0);

      if (urls.length === 0) {
        message.error('请输入至少一个URL');
        return;
      }

      // 解析元数据
      let metadata = {};
      try {
        if (values.metadata) {
          metadata = JSON.parse(values.metadata);
        }
      } catch (error) {
        message.error('元数据格式错误，请输入有效的JSON');
        return;
      }

      // 构建执行请求
      const executeRequest: ExecuteTaskRequest = {
        monitoring_task_id: values.monitoring_task_id,
        urls: urls,
        config_id: config.id,
        execution_mode: values.execution_mode,
        priority: values.priority,
        callback_url: values.callback_url || undefined,
        metadata: metadata
      };

      // 执行任务
      const result = await newCrawlerApi.executeTask(executeRequest);
      setExecutionResult(result);
      
      message.success(`任务执行成功！执行ID: ${result.execution_id}`);
      
      // 延迟调用回调，让用户看到结果
      setTimeout(() => {
        onExecute(result);
      }, 2000);

    } catch (error) {
      message.error(`任务执行失败: ${error}`);
    } finally {
      setExecuting(false);
    }
  };

  if (!config) {
    return (
      <Alert
        message="配置信息不存在"
        description="无法执行任务，请重新选择配置"
        type="error"
        showIcon
      />
    );
  }

  return (
    <Spin spinning={loading}>
      <div>
        {/* 配置信息展示 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <div>
                <Text strong>配置名称:</Text> {config.name}
              </div>
              <div style={{ marginTop: 4 }}>
                <Text strong>平台:</Text> <Tag color="blue">{config.platform.toUpperCase()}</Tag>
              </div>
            </Col>
            <Col span={12}>
              <div>
                <Text strong>类型:</Text> <Tag>{config.config_type.replace('_', ' ').toUpperCase()}</Tag>
              </div>
              <div style={{ marginTop: 4 }}>
                <Text strong>优先级:</Text> <Tag color="orange">{config.priority.toUpperCase()}</Tag>
              </div>
            </Col>
          </Row>
          
          <Divider style={{ margin: '12px 0' }} />
          
          <div>
            <Text strong>LLM查询:</Text>
            <div style={{ 
              marginTop: 4, 
              padding: '8px', 
              backgroundColor: '#f5f5f5', 
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              {config.llm_query}
            </div>
          </div>
        </Card>

        {/* 执行结果展示 */}
        {executionResult && (
          <Card 
            title={<span><InfoCircleOutlined /> 执行结果</span>}
            size="small" 
            style={{ marginBottom: 16 }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <div><Text strong>执行ID:</Text> {executionResult.execution_id}</div>
                <div><Text strong>批次ID:</Text> {executionResult.batch_id || 'N/A'}</div>
              </Col>
              <Col span={12}>
                <div><Text strong>状态:</Text> <Tag color="processing">{executionResult.status}</Tag></div>
                <div><Text strong>URL数量:</Text> {executionResult.total_urls}</div>
              </Col>
            </Row>
            
            {executionResult.task_ids && executionResult.task_ids.length > 0 && (
              <div style={{ marginTop: 8 }}>
                <Text strong>任务ID列表:</Text>
                <div style={{ marginTop: 4 }}>
                  {executionResult.task_ids.slice(0, 5).map((taskId, index) => (
                    <Tag key={index} style={{ marginBottom: 4 }}>{taskId}</Tag>
                  ))}
                  {executionResult.task_ids.length > 5 && (
                    <Tag>... 还有 {executionResult.task_ids.length - 5} 个</Tag>
                  )}
                </div>
              </div>
            )}
          </Card>
        )}

        {/* 执行配置表单 */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleExecute}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="监控任务ID"
                name="monitoring_task_id"
                rules={[{ required: true, message: '请输入监控任务ID' }]}
              >
                <Input placeholder="输入监控任务ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="执行模式"
                name="execution_mode"
                rules={[{ required: true, message: '请选择执行模式' }]}
              >
                <Select placeholder="选择执行模式">
                  <Option value="batch">批量执行</Option>
                  <Option value="single">单个执行</Option>
                  <Option value="streaming">流式执行</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="任务优先级"
                name="priority"
                rules={[{ required: true, message: '请选择任务优先级' }]}
              >
                <Select placeholder="选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Alert
            message="系统配置说明"
            description="回调URL、最大并发数等系统级配置已移至后端环境变量管理。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            label={
              <span>
                <LinkOutlined /> URL列表 
                {urlCount > 0 && <Tag color="blue" style={{ marginLeft: 8 }}>{urlCount} 个URL</Tag>}
              </span>
            }
            name="urls"
            rules={[{ required: true, message: '请输入要爬取的URL列表' }]}
          >
            <TextArea
              rows={8}
              placeholder="请输入URL列表，每行一个URL"
              onChange={handleUrlsChange}
            />
          </Form.Item>

          <Form.Item
            label={<span><SettingOutlined /> 元数据 (JSON格式)</span>}
            name="metadata"
          >
            <TextArea
              rows={4}
              placeholder="输入任务元数据，JSON格式（可选）"
            />
          </Form.Item>

          {/* 执行进度 */}
          {executing && (
            <div style={{ marginBottom: 16 }}>
              <Progress percent={50} status="active" />
              <Text type="secondary">正在提交任务到爬虫服务...</Text>
            </div>
          )}

          {/* 操作按钮 */}
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onCancel} icon={<CloseOutlined />}>
                取消
              </Button>
              
              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={executing}
                disabled={!!executionResult}
              >
                {executing ? '执行中...' : '执行任务'}
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </Spin>
  );
};

export default CrawlerExecutionModal;
