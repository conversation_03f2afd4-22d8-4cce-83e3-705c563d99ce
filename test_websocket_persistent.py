#!/usr/bin/env python3
"""
持续WebSocket连接测试，模拟前端行为
"""
import asyncio
import websockets
import json
from datetime import datetime

async def persistent_websocket_test():
    uri = "ws://localhost:8000/ws"
    
    try:
        print(f"正在连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 等待欢迎消息
            try:
                welcome_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到欢迎消息: {welcome_message}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到欢迎消息")
            
            # 发送ping消息并保持连接
            ping_count = 0
            while ping_count < 10:  # 发送10次ping，然后退出
                ping_count += 1
                
                # 发送ping消息
                ping_message = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat(),
                    "count": ping_count
                }
                await websocket.send(json.dumps(ping_message))
                print(f"发送ping消息 #{ping_count}: {ping_message}")
                
                # 等待pong响应
                try:
                    pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"收到pong响应 #{ping_count}: {pong_response}")
                except asyncio.TimeoutError:
                    print(f"⚠️ 未收到pong响应 #{ping_count}")
                
                # 等待5秒再发送下一个ping
                await asyncio.sleep(5)
                
            print("✅ 持续WebSocket测试完成")
            
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保backend服务正在运行")
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

if __name__ == "__main__":
    asyncio.run(persistent_websocket_test())
