# Task 02 最终工作版本测试脚本
# UTF-8 编码确保中文显示
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "====================================================" -ForegroundColor Cyan
Write-Host "🧪 Task 02 最终工作版本测试脚本" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host ""

# 切换到项目根目录
Set-Location $PSScriptRoot\..

$totalTests = 0
$passedTests = 0
$results = @()

# 测试1: 核心文件检查
Write-Host "📋 测试1: 核心文件检查" -ForegroundColor Yellow
$totalTests++

$requiredFiles = @(
    "backend\app\models\product.py",
    "backend\app\models\snapshot.py",
    "backend\app\database.py", 
    "backend\app\schemas\snapshot.py",
    "config\settings.py",
    "config\__init__.py"
)

$missingFiles = 0
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles++
        Write-Host "❌ 缺失: $file" -ForegroundColor Red
    }
}

if ($missingFiles -eq 0) {
    Write-Host "✅ 核心文件检查通过" -ForegroundColor Green
    $passedTests++
    $results += "✅ 核心文件检查"
} else {
    Write-Host "❌ 缺失 $missingFiles 个核心文件" -ForegroundColor Red
    $results += "❌ 核心文件检查"
}
Write-Host ""

# 测试2: Docker环境检查
Write-Host "🐳 测试2: Docker环境检查" -ForegroundColor Yellow
$totalTests++

try {
    $containers = docker ps --filter "name=timescaledb" --format "{{.Names}}" 2>$null
    if ($containers -match "timescaledb") {
        Write-Host "✅ TimescaleDB容器运行中" -ForegroundColor Green
        $passedTests++
        $results += "✅ Docker环境"
    } else {
        Write-Host "⚠️ 正在启动Docker容器..." -ForegroundColor Yellow
        docker-compose -f docker-compose.dev.yml up timescaledb redis -d 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker容器启动成功" -ForegroundColor Green
            $passedTests++
            $results += "✅ Docker环境"
            Write-Host "等待数据库启动..." -ForegroundColor Yellow
            Start-Sleep -Seconds 10
        } else {
            Write-Host "❌ Docker容器启动失败" -ForegroundColor Red
            $results += "❌ Docker环境"
        }
    }
} catch {
    Write-Host "❌ Docker检查失败: $($_.Exception.Message)" -ForegroundColor Red
    $results += "❌ Docker环境"
}
Write-Host ""

# 测试3: Python模块导入测试 (在backend目录内)
Write-Host "📦 测试3: Python模块导入测试" -ForegroundColor Yellow
$totalTests++

try {
    Set-Location backend
    $importResult = conda run -n monit-crawler python -c "from app.models import Product, ProductSnapshot; from app.database import check_database_connection; print('SUCCESS')" 2>&1
    Set-Location ..
    
    if ($importResult -like "*SUCCESS*") {
        Write-Host "✅ Python模块导入成功" -ForegroundColor Green
        $passedTests++
        $results += "✅ Python模块导入"
    } else {
        Write-Host "❌ Python模块导入失败" -ForegroundColor Red
        Write-Host "错误: $importResult" -ForegroundColor Red
        $results += "❌ Python模块导入"
    }
} catch {
    Write-Host "❌ Python模块导入测试失败: $($_.Exception.Message)" -ForegroundColor Red
    $results += "❌ Python模块导入"
    Set-Location ..
}
Write-Host ""

# 测试4: 数据库连接测试 (在backend目录内)
Write-Host "💾 测试4: 数据库连接测试" -ForegroundColor Yellow
$totalTests++

try {
    Set-Location backend
    $dbResult = conda run -n monit-crawler python -c "import asyncio; from app.database import check_database_connection; print('SUCCESS' if asyncio.run(check_database_connection()) else 'FAILED')" 2>&1
    Set-Location ..
    
    if ($dbResult -like "*SUCCESS*") {
        Write-Host "✅ 数据库连接成功" -ForegroundColor Green
        $passedTests++
        $results += "✅ 数据库连接"
    } else {
        Write-Host "❌ 数据库连接失败" -ForegroundColor Red
        Write-Host "错误: $dbResult" -ForegroundColor Red
        $results += "❌ 数据库连接"
    }
} catch {
    Write-Host "❌ 数据库连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
    $results += "❌ 数据库连接"
    Set-Location ..
}
Write-Host ""

# 测试5: FastAPI服务启动测试 (在backend目录内)
Write-Host "🌐 测试5: FastAPI服务启动测试" -ForegroundColor Yellow
$totalTests++

try {
    Set-Location backend
    
    # 使用更简单的方法测试FastAPI启动
    Write-Host "正在测试FastAPI服务启动..." -ForegroundColor Yellow
    
    # 创建一个临时的启动测试脚本
    $testScript = @"
import asyncio
import sys
import os
sys.path.insert(0, os.getcwd())

try:
    from app.main import app
    from app.database import check_database_connection
    
    # 测试应用是否可以正常导入和初始化
    print("FastAPI应用导入成功")
    
    # 测试数据库连接
    connected = asyncio.run(check_database_connection())
    if connected:
        print("数据库连接正常")
        print("SUCCESS")
    else:
        print("数据库连接失败")
        print("FAILED")
        
except Exception as e:
    print(f"FastAPI测试失败: {e}")
    print("FAILED")
"@
    
    # 将测试脚本写入临时文件
    $testScript | Out-File -FilePath "test_fastapi.py" -Encoding UTF8
    
    # 运行测试
    $apiResult = conda run -n monit-crawler python test_fastapi.py 2>&1
    
    # 清理临时文件
    Remove-Item "test_fastapi.py" -ErrorAction SilentlyContinue
    
    Set-Location ..
    
    if ($apiResult -like "*SUCCESS*") {
        Write-Host "✅ FastAPI服务组件测试成功" -ForegroundColor Green
        $passedTests++
        $results += "✅ FastAPI服务"
    } else {
        Write-Host "❌ FastAPI服务组件测试失败" -ForegroundColor Red
        Write-Host "错误: $apiResult" -ForegroundColor Red
        $results += "❌ FastAPI服务"
    }
    
} catch {
    Write-Host "❌ FastAPI测试失败: $($_.Exception.Message)" -ForegroundColor Red
    $results += "❌ FastAPI服务"
    Set-Location ..
}
Write-Host ""

# 测试结果汇总
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host "📊 测试结果汇总" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host "总测试数: $totalTests" -ForegroundColor White
Write-Host "通过测试: $passedTests" -ForegroundColor Green
$failedTests = $totalTests - $passedTests
Write-Host "失败测试: $failedTests" -ForegroundColor Red

$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
Write-Host "成功率: $successRate%" -ForegroundColor Yellow

Write-Host ""
Write-Host "详细结果:" -ForegroundColor White
foreach ($result in $results) {
    Write-Host "  $result" -ForegroundColor White
}

Write-Host ""

if ($passedTests -eq $totalTests) {
    Write-Host "🎉 所有测试通过！Task 02 完全验证成功！" -ForegroundColor Green
    Write-Host ""
    Write-Host "✨ 已验证功能:" -ForegroundColor Cyan
    Write-Host "   • 核心文件完整" -ForegroundColor White
    Write-Host "   • Docker环境正常" -ForegroundColor White
    Write-Host "   • Python模块导入" -ForegroundColor White
    Write-Host "   • 数据库连接" -ForegroundColor White
    Write-Host "   • FastAPI服务启动" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 Task 02 状态: 100% 完成" -ForegroundColor Green
    Write-Host "✅ 准备开始 Task 03: 爬虫核心引擎开发" -ForegroundColor Green
} else {
    Write-Host "⚠️ 部分测试失败" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 建议检查:" -ForegroundColor Cyan
    Write-Host "   • 确保Docker Desktop运行中" -ForegroundColor White
    Write-Host "   • 检查conda环境: conda env list" -ForegroundColor White
    Write-Host "   • 在backend目录下手动测试: cd backend && python -m uvicorn app.main:app" -ForegroundColor White
}

Write-Host ""
Write-Host "====================================================" -ForegroundColor Cyan

# 等待用户输入
Write-Host "按任意键继续..."
$Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") | Out-Null 