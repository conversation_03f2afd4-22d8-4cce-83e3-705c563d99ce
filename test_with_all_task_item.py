#!/usr/bin/env python3
"""
使用all_task_item.xlsx测试URL池管理页面
"""

import requests
import os

BASE_URL = "http://localhost:8000"
EXCEL_FILE = "LinksToMonit/all_task_item.xlsx"

def test_url_pool_upload():
    """测试URL池管理页面的Excel上传"""
    print("🚀 测试URL池管理页面Excel上传")
    print(f"📁 使用文件: {EXCEL_FILE}")
    
    if not os.path.exists(EXCEL_FILE):
        print(f"❌ 文件不存在: {EXCEL_FILE}")
        return False
    
    file_size = os.path.getsize(EXCEL_FILE) / 1024 / 1024
    print(f"📊 文件大小: {file_size:.2f} MB")
    
    try:
        with open(EXCEL_FILE, 'rb') as f:
            files = {
                'file': (os.path.basename(EXCEL_FILE), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            print("📤 正在上传文件...")
            response = requests.post(f"{BASE_URL}/api/v1/excel/upload-and-parse", files=files, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ URL池管理页面上传成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 新增URL: {result['new_urls']}")
            print(f"   - 重复URL: {result['duplicate_urls']}")
            print(f"   - 无效URL: {result['invalid_urls']}")
            print(f"   - 处理时间: {result.get('processing_time', 'N/A'):.2f}秒" if isinstance(result.get('processing_time'), (int, float)) else f"   - 处理时间: {result.get('processing_time', 'N/A')}")
            
            # 显示URL样本
            if result.get('urls'):
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['urls'][:5]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')[:60]}...")
            
            return result
        else:
            print(f"❌ URL池管理页面上传失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print(f"❌ 上传超时（文件可能太大）")
        return None
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def test_task_manager_upload():
    """测试任务管理页面的Excel上传"""
    print(f"\n🚀 测试任务管理页面Excel上传")
    print(f"📁 使用文件: {EXCEL_FILE}")
    
    try:
        with open(EXCEL_FILE, 'rb') as f:
            files = {
                'file': (os.path.basename(EXCEL_FILE), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            print("📤 正在上传文件...")
            response = requests.post(f"{BASE_URL}/api/v1/excel/parse", files=files, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务管理页面上传成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 有效URL: {result['valid_urls']}")
            print(f"   - 无效URL: {result['invalid_urls']}")
            print(f"   - 平台分布: {result.get('platforms', {})}")
            print(f"   - 解析时间: {result.get('parse_time', 'N/A')}")
            
            # 显示URL样本
            if result.get('urls'):
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['urls'][:5]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')[:60]}...")
            
            return result
        else:
            print(f"❌ 任务管理页面上传失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print(f"❌ 上传超时（文件可能太大）")
        return None
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def test_url_pool_list():
    """测试URL池列表API"""
    print(f"\n📋 测试URL池列表")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取URL池列表成功:")
            print(f"   - 总数: {result['total']}")
            print(f"   - 当前页数据: {len(result['data'])}")
            print(f"   - 页码: {result['page']}")
            print(f"   - 每页大小: {result['page_size']}")
            print(f"   - 是否有更多: {result['has_more']}")
            
            if result['data']:
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['data'][:3]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')[:50]}...")
            
            return result
        else:
            print(f"❌ 获取URL池列表失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 获取URL池列表异常: {e}")
        return None

def test_url_pool_stats():
    """测试URL池统计API"""
    print(f"\n📊 测试URL池统计")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/urls/pool/stats")
        
        if response.status_code == 200:
            result = response.json()
            stats = result['data']
            print(f"✅ 获取URL池统计成功:")
            print(f"   - 总URL数: {stats['total_urls']}")
            print(f"   - 活跃URL: {stats['active_urls']}")
            print(f"   - 禁用URL: {stats['disabled_urls']}")
            print(f"   - 平台分布: {stats['platforms']}")
            
            return result
        else:
            print(f"❌ 获取URL池统计失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 获取URL池统计异常: {e}")
        return None

def compare_results(url_pool_result, task_manager_result):
    """比较两个API的结果"""
    print(f"\n📊 结果比较")
    print("=" * 50)
    
    if not url_pool_result or not task_manager_result:
        print("❌ 无法比较，因为有API调用失败")
        return
    
    url_pool_count = url_pool_result.get('total_urls', 0)
    task_manager_count = task_manager_result.get('total_urls', 0)
    
    print(f"URL数量比较:")
    print(f"   - URL池管理页面: {url_pool_count}")
    print(f"   - 任务管理页面: {task_manager_count}")
    
    if url_pool_count == task_manager_count:
        print("✅ URL数量一致")
    else:
        print(f"❌ URL数量不一致，差异: {abs(url_pool_count - task_manager_count)}")
    
    # 检查URL样本是否一致
    if url_pool_result.get('urls') and task_manager_result.get('urls'):
        url_pool_sample = [url['url'] for url in url_pool_result['urls'][:3]]
        task_manager_sample = [url['url'] for url in task_manager_result['urls'][:3]]
        
        print(f"\nURL样本比较:")
        print(f"   - URL池管理页面前3个: {url_pool_sample}")
        print(f"   - 任务管理页面前3个: {task_manager_sample}")
        
        if url_pool_sample == task_manager_sample:
            print("✅ URL样本一致")
        else:
            print("❌ URL样本不一致")

def main():
    """主测试函数"""
    print("🚀 使用all_task_item.xlsx测试URL池管理功能")
    print("=" * 60)
    
    # 测试URL池管理页面上传
    url_pool_result = test_url_pool_upload()
    
    # 测试任务管理页面上传（对比）
    task_manager_result = test_task_manager_upload()
    
    # 比较结果
    compare_results(url_pool_result, task_manager_result)
    
    # 测试URL池相关API
    test_url_pool_list()
    test_url_pool_stats()
    
    print(f"\n" + "=" * 60)
    print("测试完成")
    
    # 总结
    if url_pool_result and task_manager_result:
        url_pool_count = url_pool_result.get('total_urls', 0)
        task_manager_count = task_manager_result.get('total_urls', 0)
        
        if url_pool_count > 0 and url_pool_count == task_manager_count:
            print("🎉 测试成功！URL池管理页面工作正常")
            print(f"✅ 成功解析了 {url_pool_count} 个URL")
        elif url_pool_count > 0:
            print("✅ URL池管理页面基本正常")
            print(f"📊 解析了 {url_pool_count} 个URL")
        else:
            print("⚠️ URL池管理页面可能有问题，没有解析到URL")
    else:
        print("❌ 测试失败，请检查API功能")

if __name__ == "__main__":
    main()
