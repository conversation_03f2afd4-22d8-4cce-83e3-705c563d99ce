# MonIt 环境设置指南

## 🎯 概述

MonIt项目现在使用专门的conda环境来管理依赖，确保环境隔离和版本一致性。

## 📋 环境信息

- **环境名称**: `monit`
- **Python版本**: 3.11.13
- **主要依赖**: FastAPI, Celery, Redis, Pandas, SQLAlchemy等
- **总依赖数**: 100+ 个包

## 🚀 快速开始

### 方法1: 自动设置（推荐）

#### Windows用户
```bash
# 运行自动设置脚本
scripts/setup_environment.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x scripts/setup_environment.sh

# 运行自动设置脚本
./scripts/setup_environment.sh
```

### 方法2: 手动设置

```bash
# 1. 创建conda环境
conda create -n monit python=3.11 -y

# 2. 激活环境
conda activate monit

# 3. 安装依赖
pip install -r requirements.txt

# 4. 创建必要目录
mkdir -p logs data temp
```

## ✅ 验证安装

### 基础验证
```bash
# 激活环境
conda activate monit

# 检查Python版本
python --version
# 应该显示: Python 3.11.13

# 验证核心依赖
python -c "import pandas, redis, celery, fastapi; print('✅ 核心依赖验证成功')"
```

### 功能验证
```bash
# 运行快速测试
python backend/scripts/quick_test.py

# 预期输出:
# ✅ Successfully read 624 products
# ✅ Split 100 URLs into 5 batches  
# ✅ Rate limiter working correctly
# ✅ Priority calculation working correctly
# 🎉 Quick Test Completed Successfully!
```

## 📦 核心依赖说明

### Web框架
- **FastAPI**: 现代高性能Web框架
- **Uvicorn**: ASGI服务器

### 任务队列
- **Celery**: 分布式任务队列
- **Redis**: 消息代理和缓存

### 数据库
- **SQLAlchemy**: ORM框架
- **AsyncPG**: PostgreSQL异步驱动
- **TimescaleDB**: 时间序列数据库支持

### 数据处理
- **Pandas**: 数据分析库
- **OpenPyXL**: Excel文件处理
- **NumPy**: 数值计算

### HTTP客户端
- **HTTPX**: 异步HTTP客户端
- **AioHTTP**: 异步HTTP库
- **Requests**: 同步HTTP库

### 开发工具
- **Pytest**: 测试框架
- **Black**: 代码格式化
- **MyPy**: 类型检查

## 🔧 环境管理

### 激活环境
```bash
conda activate monit
```

### 停用环境
```bash
conda deactivate
```

### 查看环境信息
```bash
# 查看已安装的包
conda list

# 查看环境位置
conda info --envs
```

### 更新依赖
```bash
# 激活环境
conda activate monit

# 更新特定包
pip install --upgrade package_name

# 更新所有包
pip install --upgrade -r requirements.txt
```

### 删除环境
```bash
# 如果需要重新创建环境
conda env remove -n monit
```

## 🧪 测试功能

### 1. 快速测试
```bash
python backend/scripts/quick_test.py
```
- 测试Excel处理（624个URL）
- 测试任务分片（100个URL → 5个批次）
- 测试限流控制（2并发限制）
- 测试优先级计算

### 2. 重试系统测试
```bash
python backend/tests/test_retry_system.py
```
- 测试失败类型检测
- 测试重试延迟计算
- 测试优先级降级

### 3. 真实场景测试
```bash
python backend/scripts/run_real_test.py
```
- 使用全部624个URL
- 完整的任务调度流程
- 性能监控和分析

## 📊 性能基准

基于快速测试的结果：

- **Excel处理**: 624个URL，2行无效数据
- **任务分片**: 100个URL → 5个批次（20个URL/批次）
- **限流控制**: 正确限制2并发，第3个请求被拒绝
- **处理速度**: 9.95 URLs/秒（模拟环境）
- **优先级**: urgent(12.0) > high(6.0) > normal(3.0) > low(1.5)

## 🚨 常见问题

### 问题1: conda命令未找到
```bash
# 解决方案: 安装Miniconda或Anaconda
# 下载地址: https://docs.conda.io/en/latest/miniconda.html
```

### 问题2: 依赖安装失败
```bash
# 解决方案: 更新pip和conda
pip install --upgrade pip
conda update conda

# 然后重新安装
pip install -r requirements.txt
```

### 问题3: Redis连接失败
```bash
# 解决方案: 启动Redis服务
redis-server

# 或者在Windows上安装Redis
# 下载地址: https://github.com/microsoftarchive/redis/releases
```

### 问题4: 权限错误
```bash
# Linux/Mac解决方案
chmod +x scripts/*.sh

# Windows解决方案: 以管理员身份运行
```

## 🔄 日常使用流程

### 开始工作
```bash
# 1. 激活环境
conda activate monit

# 2. 进入项目目录
cd /path/to/MonIt

# 3. 验证环境
python -c "print('MonIt环境已激活')"
```

### 运行测试
```bash
# 快速功能测试
python backend/scripts/quick_test.py

# 完整系统测试
python backend/scripts/run_real_test.py
```

### 启动服务
```bash
# 启动任务管理器
python backend/scripts/start_task_manager.py

# 启动Celery workers
./backend/scripts/start_workers.sh start
```

## 📈 下一步

环境设置完成后，您可以：

1. **运行真实场景测试**: 使用624个URL测试完整系统
2. **启动Redis服务**: 为任务队列提供支持
3. **配置数据库**: 设置PostgreSQL/TimescaleDB
4. **开发新功能**: 基于现有的任务调度框架

## 💡 最佳实践

1. **始终使用conda环境**: 避免依赖冲突
2. **定期更新依赖**: 保持安全性和性能
3. **运行测试验证**: 确保功能正常
4. **监控资源使用**: 特别是内存和CPU
5. **备份重要数据**: 包括Excel文件和配置

---

**恭喜！** 您的MonIt开发环境已经成功设置完成！🎉

现在您可以开始使用强大的任务调度系统来处理大规模的商品监控任务了。
