#!/usr/bin/env python3
"""
创建测试Worker的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def create_test_worker():
    """创建测试Worker"""
    
    print("🔧 创建测试Worker...")
    
    worker_data = {
        "worker_name": "测试Worker-多选",
        "description": "用于测试多Worker选择的Worker",
        "crawler_config_id": "default",
        "backend_config_id": "default",
        "priority": "normal",
        "allocated_concurrent": 5,
        "max_concurrent": 10
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/crawler-workers/",
            json=worker_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            worker = response.json()
            print("✅ Worker创建成功!")
            print(f"  Worker ID: {worker.get('worker_id')}")
            print(f"  Worker名称: {worker.get('worker_name')}")
            return worker.get('worker_id')
        else:
            print(f"❌ Worker创建失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def create_multi_worker_assignment():
    """创建多Worker分配"""
    
    print(f"\n🔧 创建多Worker分配...")
    
    # 获取可用Worker
    try:
        workers_response = requests.get(f"{BASE_URL}/crawler-workers/")
        if workers_response.status_code == 200:
            workers = workers_response.json()
            print(f"✅ 找到 {len(workers)} 个Worker")
            
            if len(workers) >= 2:
                # 选择最后两个Worker（避免重复）
                worker_ids = [workers[-2]['worker_id'], workers[-1]['worker_id']]
                worker_names = [workers[-2]['worker_name'], workers[-1]['worker_name']]
                
                print(f"选择的Worker:")
                for i, (wid, wname) in enumerate(zip(worker_ids, worker_names), 1):
                    print(f"  {i}. {wname} ({wid})")
                
                # 创建分配
                assignment_data = {
                    "task_id": "20baf174-bb0d-4d45-b931-d1580bac02da",
                    "worker_ids": worker_ids,
                    "assignment_name": "多Worker测试分配",
                    "description": "测试多Worker显示功能",
                    "assignment_strategy": "health_based",
                    "auto_failover": True,
                    "total_urls": 49
                }
                
                response = requests.post(
                    f"{BASE_URL}/task-assignments/",
                    json=assignment_data,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    assignment = response.json()
                    print("✅ 多Worker分配创建成功!")
                    print(f"  分配ID: {assignment.get('assignment_id')}")
                    print(f"  Worker IDs: {assignment.get('worker_ids')}")
                    print(f"  Worker数量: {len(assignment.get('worker_ids', []))}")
                    return assignment.get('assignment_id')
                else:
                    print(f"❌ 分配创建失败: {response.text}")
                    return None
            else:
                print("❌ 可用Worker数量不足")
                return None
        else:
            print(f"❌ 获取Worker列表失败: {workers_response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 创建测试数据\n")
    
    # 1. 创建新的测试Worker
    worker_id = create_test_worker()
    
    # 2. 创建多Worker分配
    if worker_id:
        assignment_id = create_multi_worker_assignment()
        
        if assignment_id:
            print(f"\n🎉 测试数据创建完成!")
            print(f"  新Worker ID: {worker_id}")
            print(f"  新分配ID: {assignment_id}")
        else:
            print(f"\n❌ 分配创建失败")
    else:
        print(f"\n❌ Worker创建失败")

if __name__ == "__main__":
    main()
