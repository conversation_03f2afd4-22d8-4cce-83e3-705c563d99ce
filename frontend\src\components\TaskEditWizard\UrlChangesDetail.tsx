import React, { useState, useEffect } from 'react';
import { Card, List, Tag, Typography, Spin, Alert, Tooltip } from 'antd';
import { PlusOutlined, MinusOutlined, LinkOutlined } from '@ant-design/icons';
import { getUrlsByIds } from '../../services/urlPoolApi';
import type { UrlPoolItem } from '../../types/urlPool';

const { Text, Title } = Typography;

interface UrlChangesDetailProps {
  originalUrls: string[];
  currentUrls: string[];
}

interface UrlChangeItem {
  id: string;
  url: string;
  platform: string;
  status: 'added' | 'removed' | 'unchanged';
  source_file?: string;
}

const UrlChangesDetail: React.FC<UrlChangesDetailProps> = ({
  originalUrls,
  currentUrls
}) => {
  const [loading, setLoading] = useState(true);
  const [urlChanges, setUrlChanges] = useState<UrlChangeItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadUrlDetails();
  }, [originalUrls, currentUrls]);

  const loadUrlDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      // 获取所有相关的URL ID
      const allUrlIds = Array.from(new Set([...originalUrls, ...currentUrls]));
      
      if (allUrlIds.length === 0) {
        setUrlChanges([]);
        setLoading(false);
        return;
      }

      // 批量获取URL详情
      const response = await getUrlsByIds(allUrlIds);
      const urlDetailsMap = new Map<string, UrlPoolItem>();
      
      response.data.forEach(url => {
        urlDetailsMap.set(url.id, url);
      });

      // 分析URL变更
      const changes: UrlChangeItem[] = [];

      // 添加删除的URL
      originalUrls.forEach(urlId => {
        if (!currentUrls.includes(urlId)) {
          const urlDetail = urlDetailsMap.get(urlId);
          changes.push({
            id: urlId,
            url: urlDetail?.url || `URL ID: ${urlId}`,
            platform: urlDetail?.platform || 'unknown',
            status: 'removed',
            source_file: urlDetail?.source_file
          });
        }
      });

      // 添加新增的URL
      currentUrls.forEach(urlId => {
        if (!originalUrls.includes(urlId)) {
          const urlDetail = urlDetailsMap.get(urlId);
          changes.push({
            id: urlId,
            url: urlDetail?.url || `URL ID: ${urlId}`,
            platform: urlDetail?.platform || 'unknown',
            status: 'added',
            source_file: urlDetail?.source_file
          });
        }
      });

      // 按状态排序：先显示删除的，再显示新增的
      changes.sort((a, b) => {
        if (a.status === 'removed' && b.status === 'added') return -1;
        if (a.status === 'added' && b.status === 'removed') return 1;
        return 0;
      });

      setUrlChanges(changes);
    } catch (err: any) {
      console.error('加载URL详情失败:', err);
      setError(err.message || '加载URL详情失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'added':
        return <PlusOutlined style={{ color: '#52c41a' }} />;
      case 'removed':
        return <MinusOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LinkOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'added':
        return 'success';
      case 'removed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'added':
        return '新增';
      case 'removed':
        return '删除';
      default:
        return '未变更';
    }
  };

  const formatUrl = (url: string) => {
    if (url.length > 60) {
      return url.substring(0, 60) + '...';
    }
    return url;
  };

  if (loading) {
    return (
      <Card title="URL变更详情" size="small">
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 8 }}>加载URL详情中...</div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card title="URL变更详情" size="small">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (urlChanges.length === 0) {
    return (
      <Card title="URL变更详情" size="small">
        <Alert
          message="无URL变更"
          description="监控URL列表没有任何变更"
          type="info"
          showIcon
        />
      </Card>
    );
  }

  const addedCount = urlChanges.filter(item => item.status === 'added').length;
  const removedCount = urlChanges.filter(item => item.status === 'removed').length;

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span>URL变更详情</span>
          <Tag color="success" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>
            +{addedCount}
          </Tag>
          <Tag color="error" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>
            -{removedCount}
          </Tag>
        </div>
      }
      size="small"
    >
      <List
        size="small"
        dataSource={urlChanges}
        style={{ maxHeight: 300, overflowY: 'auto' }}
        renderItem={(item) => (
          <List.Item
            style={{
              padding: '8px 0',
              borderBottom: '1px solid #f0f0f0'
            }}
          >
            <div style={{ width: '100%', display: 'flex', alignItems: 'center', gap: 8 }}>
              <div style={{ minWidth: 20 }}>
                {getStatusIcon(item.status)}
              </div>
              <div style={{ flex: 1, minWidth: 0 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                  <Tag
                    color={getStatusColor(item.status)}
                    style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}
                  >
                    {getStatusText(item.status)}
                  </Tag>
                  <Tag
                    color="blue"
                    style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}
                  >
                    {item.platform}
                  </Tag>
                  {item.source_file && (
                    <Tag
                      color="default"
                      style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}
                    >
                      {item.source_file}
                    </Tag>
                  )}
                </div>
                <Tooltip title={item.url} placement="topLeft">
                  <Text 
                    style={{ 
                      fontSize: '12px', 
                      color: '#666',
                      display: 'block',
                      wordBreak: 'break-all'
                    }}
                  >
                    {formatUrl(item.url)}
                  </Text>
                </Tooltip>
              </div>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default UrlChangesDetail;
