#!/usr/bin/env python3
"""
修复过期的下次执行时间脚本

目的：
1. 检测所有下次执行时间为过去时间的任务
2. 重新计算正确的下次执行时间
3. 更新Redis中的数据
"""

import redis
import json
import pytz
from datetime import datetime, timezone, timedelta
import random
import sys


class OutdatedNextRunFixer:
    def __init__(self, redis_host='redis', redis_port=6379, redis_db=0):
        """初始化Redis连接"""
        self.redis = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
    
    def calculate_next_run_time(self, schedule_config: dict) -> datetime:
        """计算下次运行时间"""
        # 获取用户设置的时区
        user_timezone = pytz.timezone(schedule_config.get('timezone', 'Asia/Shanghai'))
        now_utc = datetime.now(timezone.utc)
        now_local = now_utc.astimezone(user_timezone)
        
        print(f"    当前时间: {now_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        schedule_type = schedule_config.get('type', 'daily')
        
        if schedule_type == 'daily':
            # 解析用户设置的执行时间
            time_str = schedule_config.get('time')
            if time_str:
                try:
                    time_parts = time_str.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                except (ValueError, IndexError):
                    # 如果时间格式错误，使用默认时间
                    hour, minute = 9, 0
            else:
                hour, minute = 9, 0
            
            print(f"    设置的执行时间: {hour:02d}:{minute:02d}")
            
            # 在用户时区中计算下次执行时间
            next_run_local = now_local.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # 如果今天的执行时间已过，安排到明天
            if next_run_local <= now_local:
                next_run_local = next_run_local + timedelta(days=1)
                print(f"    今天时间已过，安排到明天")
            
            print(f"    基础下次执行时间: {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            
            # 应用随机延迟
            if schedule_config.get('enable_random_delay', False):
                delay_min = schedule_config.get('random_delay_min', 0)
                delay_max = schedule_config.get('random_delay_max', 180)
                
                # 确保延迟范围合理
                delay_min = max(0, delay_min)  # 防止负数
                delay_max = max(delay_min, delay_max)  # 确保最大值不小于最小值
                
                random_delay = random.randint(delay_min, delay_max)
                next_run_local = next_run_local + timedelta(minutes=random_delay)
                
                print(f"    应用随机延迟: {random_delay} 分钟 (范围: {delay_min}-{delay_max})")
                print(f"    最终下次执行时间: {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            
            # 转换回UTC时间
            next_run = next_run_local.astimezone(timezone.utc)
        else:
            # 其他调度类型的处理
            next_run = now_utc + timedelta(hours=1)  # 默认1小时后
        
        return next_run
    
    def fix_task_next_run(self, task_id: str) -> dict:
        """修复单个任务的下次运行时间"""
        try:
            # 获取任务数据
            task_key = f'monitoring_tasks:{task_id}'
            task_data = self.redis.hgetall(task_key)
            
            if not task_data:
                return {'success': False, 'error': 'Task not found'}
            
            task_name = task_data.get('name', '未知')
            current_next_run = task_data.get('next_run')
            
            print(f"🔧 修复任务: {task_name} ({task_id[:8]}...)")
            print(f"    当前下次执行时间: {current_next_run}")
            
            # 解析调度配置
            schedule_raw = task_data.get('schedule', '{}')
            try:
                schedule_config = json.loads(schedule_raw)
            except json.JSONDecodeError:
                return {'success': False, 'error': 'Invalid schedule config'}
            
            # 计算新的下次运行时间
            next_run = self.calculate_next_run_time(schedule_config)
            
            # 更新Redis中的下次运行时间
            self.redis.hset(task_key, 'next_run', next_run.isoformat())
            
            # 获取用户时区用于显示
            user_timezone = pytz.timezone(schedule_config.get('timezone', 'Asia/Shanghai'))
            next_run_local = next_run.astimezone(user_timezone)
            
            print(f"    ✅ 修复完成")
            print(f"    新的下次执行时间: {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            print(f"    UTC时间: {next_run.isoformat()}")
            
            return {
                'success': True,
                'task_name': task_name,
                'old_next_run': current_next_run,
                'new_next_run_utc': next_run.isoformat(),
                'new_next_run_local': next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z'),
                'schedule_time': schedule_config.get('time', '未设置'),
                'timezone': schedule_config.get('timezone', 'Asia/Shanghai'),
                'random_delay': schedule_config.get('enable_random_delay', False)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def find_outdated_tasks(self) -> list:
        """查找所有下次执行时间为过去时间的任务"""
        print("🔍 查找过期的下次执行时间...")
        
        # 获取所有任务ID
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        if not task_ids:
            print("❌ 未找到任何任务")
            return []
        
        now_utc = datetime.now(timezone.utc)
        outdated_tasks = []
        
        for task_id in task_ids:
            task_key = f'monitoring_tasks:{task_id}'
            task_data = self.redis.hgetall(task_key)
            
            if not task_data:
                continue
            
            task_name = task_data.get('name', '未知')
            next_run = task_data.get('next_run')
            
            if next_run:
                try:
                    next_run_dt = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                    
                    # 检查是否为过去时间
                    if next_run_dt < now_utc:
                        time_diff = now_utc - next_run_dt
                        outdated_tasks.append({
                            'task_id': task_id,
                            'task_name': task_name,
                            'next_run': next_run,
                            'hours_overdue': time_diff.total_seconds() / 3600
                        })
                        
                        print(f"  ❌ {task_name}: {next_run} (过期 {time_diff.days}天 {time_diff.seconds//3600}小时)")
                
                except Exception as e:
                    print(f"  ⚠️ {task_name}: 时间解析错误 - {e}")
        
        print(f"\n📊 找到 {len(outdated_tasks)} 个过期任务")
        return outdated_tasks
    
    def fix_all_outdated_tasks(self) -> dict:
        """修复所有过期的任务"""
        outdated_tasks = self.find_outdated_tasks()
        
        if not outdated_tasks:
            print("✅ 没有找到过期的任务")
            return {'total_tasks': 0, 'fixed_tasks': 0, 'failed_tasks': 0}
        
        print(f"\n🔧 开始修复 {len(outdated_tasks)} 个过期任务...\n")
        
        results = {
            'total_tasks': len(outdated_tasks),
            'fixed_tasks': 0,
            'failed_tasks': 0,
            'task_results': []
        }
        
        for i, task_info in enumerate(outdated_tasks, 1):
            print(f"[{i}/{len(outdated_tasks)}] 修复任务...")
            
            result = self.fix_task_next_run(task_info['task_id'])
            results['task_results'].append({
                'task_id': task_info['task_id'],
                **result
            })
            
            if result['success']:
                results['fixed_tasks'] += 1
            else:
                results['failed_tasks'] += 1
                print(f"    ❌ 修复失败: {result['error']}")
            
            print()
        
        return results


def main():
    """主函数"""
    print("🚀 过期下次执行时间修复工具")
    print("=" * 50)
    
    fixer = OutdatedNextRunFixer()
    
    try:
        # 测试Redis连接
        fixer.redis.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return
    
    # 如果提供了特定任务ID，只修复该任务
    if len(sys.argv) > 1:
        task_id = sys.argv[1]
        print(f"\n🎯 修复特定任务: {task_id}")
        result = fixer.fix_task_next_run(task_id)
        
        if result['success']:
            print(f"\n✅ 任务修复成功!")
        else:
            print(f"\n❌ 任务修复失败: {result['error']}")
    else:
        # 修复所有过期任务
        results = fixer.fix_all_outdated_tasks()
        
        print("📋 修复结果:")
        print(f"  总任务数: {results['total_tasks']}")
        print(f"  修复成功: {results['fixed_tasks']}")
        print(f"  修复失败: {results['failed_tasks']}")
        
        if results['fixed_tasks'] > 0:
            print(f"\n✅ 成功修复 {results['fixed_tasks']} 个任务的下次执行时间")
        
        if results['failed_tasks'] > 0:
            print(f"\n❌ {results['failed_tasks']} 个任务修复失败")


if __name__ == "__main__":
    main()
