.logs {
  padding: 24px;
}

.logs-header {
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.logs-header h2 {
  margin-bottom: 8px;
  color: #1a1a1a;
  font-weight: 600;
}

.logs-header .ant-typography {
  color: #666666;
  font-size: 16px;
}

/* 日志统计区域 */
.logs-stats {
  margin-bottom: 32px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stats-card.info::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.stats-card.warning::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.stats-card.error::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.stats-card.success::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

/* 日志控制区域 */
.logs-controls {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.controls-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.control-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.control-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 实时日志区域 */
.logs-viewer {
  margin-bottom: 32px;
}

.logs-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.logs-card .ant-card-head {
  border-bottom: 1px solid #f5f5f5;
}

.logs-card .ant-card-head-title {
  color: #1a1a1a;
  font-weight: 600;
}

.logs-container {
  height: 500px;
  background: #1e1e1e;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.logs-content {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.logs-content::-webkit-scrollbar {
  width: 8px;
}

.logs-content::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.logs-content::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 4px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: #777777;
}

.log-entry {
  margin-bottom: 2px;
  padding: 2px 0;
  word-wrap: break-word;
  transition: background-color 0.2s;
}

.log-entry:hover {
  background: rgba(255, 255, 255, 0.05);
}

.log-entry.info {
  color: #61dafb;
}

.log-entry.warning {
  color: #ffc107;
}

.log-entry.error {
  color: #ff6b6b;
}

.log-entry.success {
  color: #51cf66;
}

.log-entry.debug {
  color: #868e96;
}

.log-timestamp {
  color: #adb5bd;
  margin-right: 8px;
}

.log-level {
  display: inline-block;
  width: 60px;
  text-align: center;
  margin-right: 8px;
  font-weight: 600;
}

.log-message {
  flex: 1;
}

.log-source {
  color: #868e96;
  font-size: 11px;
  margin-left: 8px;
}

/* 日志工具栏 */
.logs-toolbar {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.toolbar-button {
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toolbar-button:hover {
  background: rgba(0, 0, 0, 0.8);
}

.toolbar-button.active {
  background: #1890ff;
}

/* 错误分析区域 */
.error-analysis {
  margin-bottom: 32px;
}

.error-chart-container {
  height: 300px;
  width: 100%;
}

.error-list {
  margin-top: 16px;
}

.error-item {
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.error-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.error-message {
  font-weight: 500;
  color: #ff4d4f;
  margin-bottom: 4px;
}

.error-details {
  font-size: 12px;
  color: #666666;
  font-family: 'Courier New', monospace;
}

.error-count {
  background: #ff4d4f;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 性能监控区域 */
.performance-monitor {
  margin-bottom: 32px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  padding: 16px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #666666;
}

.metric-trend {
  font-size: 12px;
  margin-top: 4px;
}

.metric-trend.up {
  color: #52c41a;
}

.metric-trend.down {
  color: #ff4d4f;
}

.metric-trend.stable {
  color: #666666;
}

/* 日志导出区域 */
.logs-export {
  margin-bottom: 32px;
}

.export-options {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.export-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.export-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logs {
    padding: 16px;
  }
  
  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .controls-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .logs-container {
    height: 400px;
  }
  
  .logs-toolbar {
    position: static;
    margin-bottom: 12px;
    justify-content: center;
  }
  
  .performance-metrics {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .export-options {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

/* 动画效果 */
.stats-card {
  animation: fadeInUp 0.3s ease-out;
}

.logs-card {
  animation: fadeInUp 0.4s ease-out;
}

.error-item {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 实时更新指示器 */
.live-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #52c41a;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 搜索高亮 */
.search-highlight {
  background: #fff566;
  color: #000000;
  padding: 0 2px;
  border-radius: 2px;
}

/* 夜间模式适配 */
.app-layout.dark .logs-header {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .logs-header h2 {
  color: #ffffff !important;
}

.app-layout.dark .logs-header .ant-typography {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs-header .ant-typography-title {
  color: #ffffff !important;
  border-bottom: none !important;
}

.app-layout.dark .logs-header .ant-typography-paragraph {
  color: #d9d9d9 !important;
  background: transparent !important;
}

/* 强制移除所有文字元素的背景色 */
.app-layout.dark .logs-header * {
  background: transparent !important;
}

.app-layout.dark .logs-header h1,
.app-layout.dark .logs-header h2,
.app-layout.dark .logs-header h3,
.app-layout.dark .logs-header h4,
.app-layout.dark .logs-header h5,
.app-layout.dark .logs-header h6 {
  background: transparent !important;
  color: #ffffff !important;
}

.app-layout.dark .logs-header p,
.app-layout.dark .logs-header span,
.app-layout.dark .logs-header div {
  background: transparent !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .stats-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .stats-card .ant-statistic-title {
  color: #d9d9d9 !important;
}

.app-layout.dark .stats-card .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

/* 强制移除统计卡片内所有元素的背景 */
.app-layout.dark .stats-card * {
  background: transparent !important;
}

.app-layout.dark .stats-card .ant-statistic {
  background: transparent !important;
}

.app-layout.dark .stats-card .ant-statistic-title {
  background: transparent !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .stats-card .ant-statistic-content {
  background: transparent !important;
}

.app-layout.dark .stats-card .ant-statistic-content-suffix {
  background: transparent !important;
}

.app-layout.dark .stats-card .ant-statistic-content-prefix {
  background: transparent !important;
}

/* 针对特定类名组合的强制修复 */
.app-layout.dark .stats-card.info * {
  background: transparent !important;
}

.app-layout.dark .stats-card.success * {
  background: transparent !important;
}

.app-layout.dark .stats-card.warning * {
  background: transparent !important;
}

.app-layout.dark .stats-card.error * {
  background: transparent !important;
}

.app-layout.dark .stats-card.info .ant-statistic-title {
  color: #d9d9d9 !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.success .ant-statistic-title {
  color: #d9d9d9 !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.warning .ant-statistic-title {
  color: #d9d9d9 !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.error .ant-statistic-title {
  color: #d9d9d9 !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.info .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.success .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.warning .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

.app-layout.dark .stats-card.error .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

.app-layout.dark .logs-filters {
  background: #262626 !important;
  border-color: #303030 !important;
}

.app-layout.dark .filter-label {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs-filters .ant-input {
  background: #1f1f1f !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .logs-filters .ant-input::placeholder {
  color: #8c8c8c !important;
}

.app-layout.dark .logs-filters .ant-input:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .logs-filters .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .logs-filters .ant-select {
  background: #1f1f1f !important;
}

.app-layout.dark .logs-filters .ant-select .ant-select-selector {
  background: #1f1f1f !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .logs-filters .ant-select .ant-select-selection-placeholder {
  color: #8c8c8c !important;
}

.app-layout.dark .logs-filters .ant-btn {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .logs-filters .ant-btn:hover {
  background: #303030 !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
}

.app-layout.dark .logs-filters .ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.app-layout.dark .logs-filters .ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

.app-layout.dark .logs-content {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .logs-content .ant-card-head {
  border-bottom-color: #303030;
}

.app-layout.dark .logs-content .ant-card-head-title {
  color: #ffffff;
}

.app-layout.dark .log-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .log-item:hover {
  background: #303030;
}

.app-layout.dark .log-timestamp {
  color: #8c8c8c;
}

.app-layout.dark .log-level {
  color: #d9d9d9;
}

.app-layout.dark .log-message {
  color: #ffffff;
}

.app-layout.dark .log-source {
  color: #40a9ff;
}

.app-layout.dark .log-details {
  background: #1f1f1f;
  border-color: #434343;
}

.app-layout.dark .log-details-content {
  color: #d9d9d9;
}

.app-layout.dark .search-highlight {
  background: #faad14;
  color: #000000;
}

.app-layout.dark .empty-state {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-icon {
  color: #595959;
}

.app-layout.dark .empty-state-text {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-hint {
  color: #8c8c8c;
}

/* 修复所有文字内容的夜间模式 */
.app-layout.dark .logs .ant-typography {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-card-head-title {
  color: #ffffff !important;
}

.app-layout.dark .logs .ant-card-body {
  background: #262626 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-tabs-tab {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-tabs-tab-active {
  color: #1890ff !important;
}

.app-layout.dark .logs .ant-tabs-content-holder {
  background: #262626 !important;
}

.app-layout.dark .logs .ant-tabs-tabpane {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-space-item {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-tooltip-inner {
  background: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .logs .ant-badge-count {
  background: #ff4d4f !important;
  color: #ffffff !important;
}

.app-layout.dark .logs .ant-progress-text {
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-switch {
  background: #434343 !important;
}

.app-layout.dark .logs .ant-switch-checked {
  background: #1890ff !important;
}

.app-layout.dark .logs .ant-tag {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .logs .ant-tag-blue {
  background: #111a2c !important;
  border-color: #153450 !important;
  color: #91d5ff !important;
}

.app-layout.dark .logs .ant-tag-green {
  background: #162312 !important;
  border-color: #274916 !important;
  color: #95de64 !important;
}

.app-layout.dark .logs .ant-tag-orange {
  background: #2b2111 !important;
  border-color: #613400 !important;
  color: #ffc53d !important;
}

.app-layout.dark .logs .ant-tag-red {
  background: #2a1215 !important;
  border-color: #58181c !important;
  color: #ff7875 !important;
}
