#!/usr/bin/env python3
"""
测试任务编辑配置修复效果

验证监控任务编辑页面是否正确读取配置
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_task_edit_config_fix():
    """测试任务编辑配置修复效果"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试任务编辑配置修复效果")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/edit-config-fix-test",
            "platform": "mercadolibre",
            "title": "编辑配置修复测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'edit_config_fix_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'edit_config_fix_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:edit_config_fix_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 创建任务（使用特定的配置值）
        print(f"\n📋 步骤2: 创建任务（使用特定的配置值）")
        
        create_data = {
            "name": "编辑配置修复测试任务",
            "description": "用于测试编辑页面配置读取修复的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "18:30",  # 每日执行时间
                "end_time": "22:45",  # 结束时间
                "timezone": "Asia/Shanghai",
                "max_runs": 35,
                "enable_random_delay": True,
                "random_delay_min": 15,
                "random_delay_max": 60
            },
            "config": {
                "platform": "mercadolibre",
                "priority": "high",
                "batch_size": 25,
                "timeout": 720,  # 超时时间
                "retry_count": 8,  # 重试次数
                "concurrent_limit": 20,  # 并发限制
                "enable_notifications": True
            },
            "tags": ["edit_config_fix_test"]
        }
        
        print(f"  创建任务时的配置:")
        print(f"    基础配置:")
        print(f"      重试次数: {create_data['config']['retry_count']}")
        print(f"      超时时间: {create_data['config']['timeout']}")
        print(f"      并发限制: {create_data['config']['concurrent_limit']}")
        print(f"    调度配置:")
        print(f"      每日执行时间: {create_data['schedule']['time']}")
        print(f"      结束时间: {create_data['schedule']['end_time']}")
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建成功: {task_id}")
            
            # 步骤3: 验证API返回的配置
            print(f"\n📋 步骤3: 验证API返回的配置")
            
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if detail_response.status_code == 200:
                detail_result = detail_response.json()
                task_data = detail_result.get('data', {})
                api_config = task_data.get('config', {})
                api_schedule = task_data.get('schedule', {})
                
                print(f"  API返回的配置:")
                print(f"    基础配置:")
                print(f"      重试次数: {api_config.get('retry_count')} (期望: 8)")
                print(f"      超时时间: {api_config.get('timeout')} (期望: 720)")
                print(f"      并发限制: {api_config.get('concurrent_limit')} (期望: 20)")
                print(f"      批次大小: {api_config.get('batch_size')} (期望: 25)")
                print(f"      通知设置: {api_config.get('enable_notifications')} (期望: True)")
                print(f"    调度配置:")
                print(f"      每日执行时间: {api_schedule.get('time')} (期望: 18:30)")
                print(f"      结束时间: {api_schedule.get('end_time')} (期望: 22:45)")
                print(f"      最大执行次数: {api_schedule.get('max_runs')} (期望: 35)")
                print(f"      随机延迟: {api_schedule.get('enable_random_delay')} (期望: True)")
                
                # 步骤4: 验证修复效果
                print(f"\n📋 步骤4: 验证修复效果")
                
                # 检查基础配置
                config_checks = [
                    ("重试次数", api_config.get('retry_count'), 8),
                    ("超时时间", api_config.get('timeout'), 720),
                    ("并发限制", api_config.get('concurrent_limit'), 20),
                    ("批次大小", api_config.get('batch_size'), 25),
                    ("通知设置", api_config.get('enable_notifications'), True)
                ]
                
                # 检查调度配置
                schedule_checks = [
                    ("每日执行时间", api_schedule.get('time'), "18:30"),
                    ("结束时间", api_schedule.get('end_time'), "22:45"),
                    ("最大执行次数", api_schedule.get('max_runs'), 35),
                    ("随机延迟", api_schedule.get('enable_random_delay'), True)
                ]
                
                print(f"  🔍 基础配置验证:")
                config_issues = []
                for name, actual, expected in config_checks:
                    if actual == expected:
                        print(f"    ✅ {name}: {actual}")
                    else:
                        print(f"    ❌ {name}: 实际 {actual}, 期望 {expected}")
                        config_issues.append(name)
                
                print(f"  🔍 调度配置验证:")
                schedule_issues = []
                for name, actual, expected in schedule_checks:
                    if actual == expected:
                        print(f"    ✅ {name}: {actual}")
                    else:
                        print(f"    ❌ {name}: 实际 {actual}, 期望 {expected}")
                        schedule_issues.append(name)
                
                # 步骤5: 前端测试指导
                print(f"\n📋 步骤5: 前端测试指导")
                
                if not config_issues and not schedule_issues:
                    print(f"  ✅ 后端API完全正常，现在测试前端编辑页面:")
                    print(f"    🌐 监控任务页面: http://localhost:3000/monitoring-tasks")
                    print(f"    📝 任务ID: {task_id}")
                    print(f"    💡 测试步骤:")
                    print(f"      1. 访问监控任务页面")
                    print(f"      2. 找到测试任务（搜索'编辑配置修复测试任务'）")
                    print(f"      3. 点击'编辑'按钮")
                    print(f"      4. 检查基础配置步骤:")
                    print(f"         - 重试次数应该显示: 8")
                    print(f"         - 超时时间应该显示: 720")
                    print(f"         - 并发限制应该显示: 20")
                    print(f"      5. 检查调度配置步骤:")
                    print(f"         - 每日执行时间应该显示: 18:30")
                    print(f"         - 结束时间应该显示: 22:45")
                    
                    print(f"\n  🎯 修复前的问题:")
                    print(f"    ❌ 并发限制硬编码为5（应该是20）")
                    print(f"    ❌ 通知设置硬编码为true（应该读取API值）")
                    print(f"    ❌ 时间格式转换可能有问题")
                    
                    print(f"\n  ✅ 修复后的期望:")
                    print(f"    ✅ 并发限制正确显示为20")
                    print(f"    ✅ 所有配置都从API正确读取")
                    print(f"    ✅ 时间格式正确转换和显示")
                    
                else:
                    print(f"  ❌ 后端API仍有问题，需要先修复后端:")
                    if config_issues:
                        print(f"    基础配置问题: {', '.join(config_issues)}")
                    if schedule_issues:
                        print(f"    调度配置问题: {', '.join(schedule_issues)}")
                
                # 等待用户测试
                print(f"\n💡 请按照上述指导进行前端测试")
                input("测试完成后按回车键继续清理测试数据...")
                
            else:
                print(f"  ❌ 获取任务详情失败: {detail_response.text}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 任务编辑配置修复测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:edit_config_fix_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_task_edit_config_fix()
