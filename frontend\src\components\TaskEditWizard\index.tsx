import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Steps,
  Button,
  Card,
  Space,
  Typography,
  message,
  Spin
} from 'antd';
import {
  LinkOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  Arrow<PERSON>eftOutlined
} from '@ant-design/icons';

import { getMonitoringTask, updateMonitoringTask, getTaskUrls, addUrlsToTask, removeUrlsBatch } from '../../services/monitoringTaskApi';
import type { MonitoringTask } from '../../services/monitoringTaskApi';
import type { TaskConfig, ScheduleConfig } from '../../types/taskCreate';
import { DEFAULT_TASK_CONFIG, DEFAULT_SCHEDULE_CONFIG } from '../../types/taskCreate';
import { convertTimeForBackend, convertTimeForFrontend } from '../../utils/timeUtils';
import dayjs from 'dayjs';

import UrlSelectionStep from '../TaskCreateWizard/UrlSelectionStep';
import BasicConfigStep from '../TaskCreateWizard/BasicConfigStep';
import ScheduleConfigStep from '../TaskCreateWizard/ScheduleConfigStep';
import EditConfirmStep from './EditConfirmStep';

const { Title } = Typography;

// 使用统一的时间处理工具函数

// 编辑步骤枚举
enum EditStep {
  URL_MANAGEMENT = 0,
  BASIC_CONFIG = 1,
  SCHEDULE_CONFIG = 2,
  CONFIRM = 3
}

interface TaskEditWizardProps {
  taskId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface EditWizardData {
  selectedUrls: string[];
  basicConfig: {
    name: string;
    description: string;
    platform: string;
    priority: string;
    tags: string[];
  };
  taskConfig: TaskConfig;
  scheduleConfig: ScheduleConfig;
}

const TaskEditWizard: React.FC<TaskEditWizardProps> = ({
  taskId,
  onSuccess,
  onCancel
}) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<EditStep>(EditStep.URL_MANAGEMENT);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [originalTask, setOriginalTask] = useState<MonitoringTask | null>(null);
  const [originalUrls, setOriginalUrls] = useState<string[]>([]);

  // 编辑向导数据状态（使用默认配置确保一致性）
  const [wizardData, setWizardData] = useState<EditWizardData>({
    selectedUrls: [],
    basicConfig: {
      name: '',
      description: '',
      platform: 'mercadolibre',
      priority: 'normal',
      tags: []
    },
    taskConfig: { ...DEFAULT_TASK_CONFIG },
    scheduleConfig: { ...DEFAULT_SCHEDULE_CONFIG }
  });

  // 加载任务数据
  const loadTaskData = async () => {
    try {
      setInitialLoading(true);
      const response = await getMonitoringTask(taskId);
      if (response.success) {
        const task = response.data;
        setOriginalTask(task);

        // 获取任务关联的URL (分页获取所有URL)
        let taskUrls: string[] = [];
        try {
          let page = 1;
          const pageSize = 100; // 使用最大允许的页面大小
          let hasMore = true;

          while (hasMore) {
            const urlsResponse = await getTaskUrls(taskId, {
              page: page,
              page_size: pageSize
            });

            if (urlsResponse.success) {
              const pageUrls = urlsResponse.data.map((url: any) => url.id);
              taskUrls.push(...pageUrls);

              // 检查是否还有更多页面
              hasMore = urlsResponse.data.length === pageSize;
              page++;
            } else {
              hasMore = false;
            }
          }

          setOriginalUrls([...taskUrls]); // 保存原始URL列表
          console.log(`Loaded ${taskUrls.length} URLs for task ${taskId} across ${page - 1} pages`);
        } catch (error) {
          console.warn('Failed to load task URLs:', error);
        }

        // 转换任务数据为向导格式
        // 转换优先级：后端的 'medium' 对应前端的 'normal'
        const convertPriority = (backendPriority: string) => {
          if (backendPriority === 'medium') return 'normal';
          return backendPriority as any;
        };

        // 添加调试日志
        console.log('Loading task data:', {
          taskId,
          schedule: task.schedule,
          timeFields: {
            time: task.schedule.time,
            start_time: task.schedule.start_time,
            end_time: task.schedule.end_time
          }
        });

        setWizardData({
          selectedUrls: taskUrls,
          basicConfig: {
            name: task.name,
            description: task.description || '',
            platform: task.config.platform,
            priority: convertPriority(task.config.priority),
            tags: task.tags || []
          },
          taskConfig: {
            enable_notifications: task.config.enable_notifications !== undefined ? task.config.enable_notifications : true,
            notification_config: task.config.notification_config || undefined,
            platform: task.config.platform,
            priority: convertPriority(task.config.priority)
          },
          scheduleConfig: {
            type: task.schedule.type as any,
            // 修复字段映射：优先使用time字段，如果没有则使用start_time字段
            time: (() => {
              const timeValue = task.schedule.time || task.schedule.start_time;
              console.log('Processing time field:', { type: task.schedule.type, timeValue });

              if (task.schedule.type === 'daily' && timeValue && timeValue !== 'Invalid Date' && timeValue.trim() !== '') {
                // 对于daily类型，直接返回原始值，让ScheduleConfigStep处理
                // ScheduleConfigStep能正确处理HH:mm格式和ISO格式
                return timeValue;
              }
              return undefined;
            })(),
            start_time: (() => {
              const timeValue = task.schedule.time || task.schedule.start_time;
              console.log('Processing start_time field:', { type: task.schedule.type, timeValue });

              if (task.schedule.type !== 'daily' && timeValue && timeValue !== 'Invalid Date' && timeValue.trim() !== '') {
                // 对于非daily类型，直接返回原始值，让ScheduleConfigStep处理
                return timeValue;
              }
              return undefined;
            })(),
            days: task.schedule.days || undefined,
            interval: task.schedule.interval || 60,
            cron_expression: task.schedule.cron_expression || undefined,
            timezone: task.schedule.timezone,
            enabled: task.schedule.enabled !== false,
            end_time: (() => {
              const endTimeValue = task.schedule.end_time;
              console.log('Processing end_time field:', { endTimeValue });

              if (endTimeValue && endTimeValue !== 'Invalid Date' && endTimeValue.trim() !== '') {
                // 直接返回原始值，让ScheduleConfigStep处理
                // ScheduleConfigStep能正确处理HH:mm格式
                return endTimeValue;
              }
              return undefined;
            })(),
            max_runs: task.schedule.max_runs,
            // 反爬虫配置
            enable_random_delay: task.schedule.enable_random_delay || false,
            random_delay_min: task.schedule.random_delay_min || 0,
            random_delay_max: task.schedule.random_delay_max || 0
          }
        });
      }
    } catch (error) {
      console.error('Failed to load task data:', error);
      message.error('加载任务数据失败');
    } finally {
      setInitialLoading(false);
    }
  };

  // 更新向导数据
  const updateWizardData = (updates: Partial<EditWizardData>) => {
    setWizardData(prev => ({ ...prev, ...updates }));
  };

  // 步骤配置
  const steps = [
    {
      title: 'URL管理',
      description: '管理任务包含的监控URL',
      icon: <LinkOutlined />,
      content: (
        <UrlSelectionStep
          selectedUrls={wizardData.selectedUrls}
          onSelectionChange={(urls) => updateWizardData({ selectedUrls: urls })}
        />
      )
    },
    {
      title: '基础配置',
      description: '修改任务名称和基本参数',
      icon: <SettingOutlined />,
      content: (
        <BasicConfigStep
          basicConfig={wizardData.basicConfig}
          taskConfig={wizardData.taskConfig}
          onConfigChange={(basic, task) => updateWizardData({
            basicConfig: basic,
            taskConfig: task
          })}
        />
      )
    },
    {
      title: '调度配置',
      description: '修改任务执行时间和频率',
      icon: <ClockCircleOutlined />,
      content: (
        <ScheduleConfigStep
          scheduleConfig={wizardData.scheduleConfig}
          onConfigChange={(schedule) => updateWizardData({ scheduleConfig: schedule })}
        />
      ),
      disabled: originalTask?.is_running // 运行中的任务不能修改调度
    },
    {
      title: '确认修改',
      description: '确认修改内容并保存',
      icon: <CheckCircleOutlined />,
      content: (
        <EditConfirmStep
          originalTask={originalTask}
          wizardData={wizardData}
          originalUrls={originalUrls}
        />
      )
    }
  ];

  // 下一步
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  // 上一步
  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 保存修改
  const handleSave = async () => {
    try {
      setLoading(true);

      console.log('=== TaskEditWizard 保存调试信息 ===');
      console.log('originalTask:', originalTask);
      console.log('wizardData:', wizardData);

      const updateData: any = {
        name: wizardData.basicConfig.name,
        description: wizardData.basicConfig.description,
        tags: wizardData.basicConfig.tags
      };

      console.log('基本信息 updateData:', updateData);

      // 如果任务未运行，可以更新调度和配置
      if (!originalTask?.is_running) {
        console.log('任务未运行，可以更新调度和配置');

        // 使用统一的时间转换逻辑（复用TaskCreateWizard的逻辑）
        // 根据调度类型选择正确的时间字段
        const timeValue = wizardData.scheduleConfig.type === 'daily'
          ? wizardData.scheduleConfig.time
          : wizardData.scheduleConfig.start_time;

        console.log('时间字段选择:');
        console.log('  scheduleType:', wizardData.scheduleConfig.type);
        console.log('  scheduleConfig.time:', wizardData.scheduleConfig.time);
        console.log('  scheduleConfig.start_time:', wizardData.scheduleConfig.start_time);
        console.log('  选择的timeValue:', timeValue);

        const convertedTime = convertTimeForBackend(timeValue, wizardData.scheduleConfig.type);
        console.log('  转换后的时间:', convertedTime);

        const convertedSchedule = {
          ...wizardData.scheduleConfig,
          time: convertedTime
        };

        console.log('转换前的scheduleConfig:', wizardData.scheduleConfig);
        console.log('转换后的convertedSchedule:', convertedSchedule);

        // 移除前端特有的字段，保留后端需要的字段
        const { start_time, ...backendSchedule } = convertedSchedule;

        console.log('最终的backendSchedule:', backendSchedule);
        updateData.schedule = backendSchedule;

        // 优先级转换：确保使用后端接受的值
        const convertPriorityToBackend = (frontendPriority: string): string => {
          // 后端接受: 'low', 'normal', 'high', 'urgent'
          // 前端可能的值需要映射到后端接受的值
          const priorityMap: Record<string, string> = {
            'low': 'low',
            'normal': 'normal',
            'medium': 'normal',  // 如果前端使用medium，映射到normal
            'high': 'high',
            'urgent': 'urgent'
          };

          const mappedPriority = priorityMap[frontendPriority] || 'normal';
          console.log(`优先级转换: ${frontendPriority} → ${mappedPriority}`);
          return mappedPriority;
        };

        console.log('任务配置转换:');
        console.log('  basicConfig.platform:', wizardData.basicConfig.platform);
        console.log('  basicConfig.priority:', wizardData.basicConfig.priority);
        console.log('  taskConfig:', wizardData.taskConfig);

        // 完整的配置更新（复用TaskCreateWizard的逻辑）
        updateData.config = {
          ...wizardData.taskConfig,
          platform: wizardData.basicConfig.platform,
          priority: convertPriorityToBackend(wizardData.basicConfig.priority)
        };

        console.log('最终的config:', updateData.config);
      }

      console.log('最终发送给后端的updateData:', updateData);
      console.log('任务ID:', taskId);

      await updateMonitoringTask(taskId, updateData);

      // 如果URL有变更，需要更新任务的URL关联
      if (originalUrls.length !== wizardData.selectedUrls.length ||
          !originalUrls.every(url => wizardData.selectedUrls.includes(url))) {

        // 实现URL更新逻辑
        // 比较原始URL和新URL，计算需要添加和删除的URL
        const originalUrlIds = new Set(originalUrls); // originalUrls已经是字符串数组
        const newUrlIds = new Set(wizardData.selectedUrls);

        // 计算需要添加的URL
        const urlsToAdd = Array.from(newUrlIds).filter(id => !originalUrlIds.has(id));

        // 计算需要删除的URL
        const urlsToRemove = Array.from(originalUrlIds).filter(id => !newUrlIds.has(id));

        console.log('URL更新:', { urlsToAdd, urlsToRemove });

        // 执行URL更新操作
        if (urlsToAdd.length > 0) {
          try {
            const addResponse = await addUrlsToTask(taskId, urlsToAdd);
            console.log('添加URL结果:', addResponse);
          } catch (error) {
            console.error('添加URL失败:', error);
            throw new Error(`添加URL失败: ${error}`);
          }
        }

        if (urlsToRemove.length > 0) {
          try {
            const removeResponse = await removeUrlsBatch(taskId, urlsToRemove);
            console.log('移除URL结果:', removeResponse);
          } catch (error) {
            console.error('移除URL失败:', error);
            throw new Error(`移除URL失败: ${error}`);
          }
        }
      }

      message.success('任务修改成功');
      
      if (onSuccess) {
        onSuccess();
      } else {
        navigate('/monitoring');
      }
    } catch (error) {
      console.error('Failed to update task:', error);
      message.error('任务修改失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/monitoring');
    }
  };

  // 组件挂载时加载任务数据
  useEffect(() => {
    if (taskId) {
      loadTaskData();
    }
  }, [taskId]);

  if (initialLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ 
      maxWidth: 1200, 
      margin: '0 auto', 
      padding: '24px',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <Card>
        {/* 页面标题 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleCancel}
              type="text"
            >
              返回
            </Button>
            <Title level={2} style={{ margin: 0 }}>
              编辑任务: {originalTask?.name}
            </Title>
          </Space>
        </div>

        {/* 步骤导航 */}
        <Steps
          current={currentStep}
          style={{ marginBottom: 32 }}
          items={steps.map((step, index) => ({
            title: step.title,
            description: step.description,
            icon: step.icon,
            disabled: step.disabled
          }))}
        />

        {/* 步骤内容 */}
        <div style={{ minHeight: 400, marginBottom: 24 }}>
          {steps[currentStep].content}
        </div>

        {/* 操作按钮 */}
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>
              取消
            </Button>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {currentStep < steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={handleNext}
                disabled={steps[currentStep + 1]?.disabled}
              >
                下一步
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={handleSave}
                loading={loading}
              >
                保存修改
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default TaskEditWizard;
