#!/usr/bin/env python3
"""
测试前端和后端数据结构兼容性
验证API返回的数据结构是否与前端类型定义匹配
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_backend_config_structure():
    """测试后端配置数据结构"""
    
    print("🧪 测试后端配置数据结构兼容性...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 1. 测试后端配置摘要结构
            print("\n1. 测试后端配置摘要结构...")
            response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            
            if response.status_code == 200:
                configs = response.json()
                if configs:
                    config = configs[0]
                    print(f"   ✅ 获取到后端配置摘要")
                    
                    # 检查前端期望的字段
                    expected_fields = [
                        'backend_id', 'backend_name', 'description', 'api_endpoint',
                        'status', 'performance_level', 'max_concurrent_tasks',
                        'current_load', 'available_capacity', 'health_score',
                        'success_rate', 'avg_response_time', 'has_callback_config',
                        'health_check_enabled', 'api_timeout', 'batch_timeout',
                        'auth_method', 'has_api_key_prefix', 'has_custom_user_agent'
                    ]
                    
                    missing_fields = []
                    for field in expected_fields:
                        if field not in config:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"   ❌ 缺少字段: {missing_fields}")
                    else:
                        print(f"   ✅ 所有期望字段都存在")
                    
                    # 显示实际字段
                    print(f"   📋 实际字段: {list(config.keys())}")
                    
                else:
                    print(f"   ⚠️  没有后端配置数据")
            else:
                print(f"   ❌ 获取后端配置失败: {response.status_code}")
            
            # 2. 测试后端配置详情结构
            print("\n2. 测试后端配置详情结构...")
            if configs:
                config_id = configs[0]['backend_id']
                response = await client.get(f"http://localhost:8000/api/v1/backend-configs/{config_id}")
                
                if response.status_code == 200:
                    config_detail = response.json()
                    print(f"   ✅ 获取到后端配置详情")
                    
                    # 检查嵌套配置结构
                    nested_configs = [
                        'performance_config', 'system_auth_config', 
                        'callback_config', 'monitoring_config'
                    ]
                    
                    for nested_config in nested_configs:
                        if nested_config in config_detail:
                            print(f"   ✅ {nested_config} 存在")
                            if nested_config == 'performance_config':
                                perf_config = config_detail[nested_config]
                                if 'max_concurrent_tasks' in perf_config:
                                    print(f"      ✅ max_concurrent_tasks: {perf_config['max_concurrent_tasks']}")
                                else:
                                    print(f"      ❌ max_concurrent_tasks 缺失")
                        else:
                            print(f"   ❌ {nested_config} 缺失")
                else:
                    print(f"   ❌ 获取后端配置详情失败: {response.status_code}")
            
            # 3. 测试爬取配置结构
            print("\n3. 测试爬取配置结构...")
            response = await client.get("http://localhost:8000/api/v1/crawler-configs/")
            
            if response.status_code == 200:
                crawler_configs = response.json()
                if crawler_configs:
                    config = crawler_configs[0]
                    print(f"   ✅ 获取到爬取配置")
                    
                    # 检查前端期望的字段
                    expected_fields = [
                        'config_id', 'config_name', 'description', 'status',
                        'version', 'tags', 'usage_count', 'last_used',
                        'created_at', 'updated_at'
                    ]
                    
                    missing_fields = []
                    for field in expected_fields:
                        if field not in config:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"   ❌ 缺少字段: {missing_fields}")
                    else:
                        print(f"   ✅ 所有期望字段都存在")
                    
                    print(f"   📋 状态字段: {config.get('status')}")
                    
                else:
                    print(f"   ⚠️  没有爬取配置数据")
            else:
                print(f"   ❌ 获取爬取配置失败: {response.status_code}")
            
            # 4. 测试Worker结构
            print("\n4. 测试Worker结构...")
            response = await client.get("http://localhost:8000/api/v1/crawler-workers/")
            
            if response.status_code == 200:
                workers = response.json()
                print(f"   ✅ Worker API正常 (当前Worker数量: {len(workers)})")
            else:
                print(f"   ❌ 获取Worker列表失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_backend_config_structure()
    print(f"\n✅ 前后端兼容性测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
