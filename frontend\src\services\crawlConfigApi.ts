/**
 * 爬取配置管理API服务
 */

import axios from 'axios';
import {
  CrawlConfigTemplate,
  CrawlConfigFormData,
  CrawlerAPIRequest,
  CrawlRequestConfig,
  AuthContext
} from '../types/crawlerConfig';

// 创建API客户端
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
});

export interface CrawlConfigListResponse {
  success: boolean;
  data: {
    configs: CrawlConfigTemplate[];
    total: number;
    page: number;
    page_size: number;
  };
  message: string;
}

export interface CrawlConfigResponse {
  success: boolean;
  data: CrawlConfigTemplate;
  message: string;
}

export interface CrawlConfigValidationResponse {
  success: boolean;
  data: {
    is_valid: boolean;
    errors: string[];
    warnings: string[];
  };
  message: string;
}

class CrawlConfigAPI {
  private baseUrl = '/api/v1/crawler-configs';

  /**
   * 获取爬取配置列表
   */
  async listConfigs(params?: {
    platform?: string;
    is_active?: boolean;
    tags?: string[];
    page?: number;
    page_size?: number;
  }): Promise<CrawlConfigListResponse> {
    const response = await api.get(this.baseUrl, { params });
    return response.data;
  }

  /**
   * 获取单个爬取配置
   */
  async getConfig(configId: string): Promise<CrawlConfigResponse> {
    const response = await api.get(`${this.baseUrl}/${configId}`);
    return response.data;
  }

  /**
   * 创建爬取配置
   */
  async createConfig(data: CrawlConfigFormData): Promise<CrawlConfigResponse> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * 更新爬取配置
   */
  async updateConfig(configId: string, data: Partial<CrawlConfigFormData>): Promise<CrawlConfigResponse> {
    const response = await api.put(`${this.baseUrl}/${configId}`, data);
    return response.data;
  }

  /**
   * 删除爬取配置
   */
  async deleteConfig(configId: string): Promise<{ success: boolean; message: string }> {
    const response = await api.delete(`${this.baseUrl}/${configId}`);
    return response.data;
  }

  /**
   * 验证爬取配置
   */
  async validateConfig(configId: string): Promise<CrawlConfigValidationResponse> {
    const response = await api.post(`${this.baseUrl}/${configId}/validate`);
    return response.data;
  }

  /**
   * 测试爬取配置
   */
  async testConfig(configId: string, testUrls: string[]): Promise<any> {
    const response = await api.post(`${this.baseUrl}/${configId}/test`, { urls: testUrls });
    return response.data;
  }

  /**
   * 复制爬取配置
   */
  async cloneConfig(configId: string, newName: string): Promise<CrawlConfigResponse> {
    const response = await api.post(`${this.baseUrl}/${configId}/clone`, { name: newName });
    return response.data;
  }

  /**
   * 获取平台默认Schema
   */
  async getPlatformSchema(platform: string): Promise<{ success: boolean; data: any; message: string }> {
    const response = await api.get(`${this.baseUrl}/schemas/${platform}`);
    return response.data;
  }

  /**
   * 构建API请求预览
   */
  async buildRequestPreview(
    urls: string[],
    configId: string
  ): Promise<{ success: boolean; data: CrawlerAPIRequest; message: string }> {
    const response = await api.post(`${this.baseUrl}/${configId}/build-request`, { urls });
    return response.data;
  }

  /**
   * 获取配置使用统计
   */
  async getConfigStats(configId: string): Promise<{
    success: boolean;
    data: {
      usage_count: number;
      last_used_at: string;
      success_rate: number;
      avg_response_time: number;
    };
    message: string;
  }> {
    const response = await api.get(`${this.baseUrl}/${configId}/stats`);
    return response.data;
  }

  /**
   * 获取系统配置信息
   */
  async getSystemConfig(): Promise<{
    status: string;
    data: any;
    message: string;
  }> {
    const response = await api.get('/api/v1/crawl-configs/system-config');
    return response.data;
  }
}

export const crawlConfigApi = new CrawlConfigAPI();
export default crawlConfigApi;
