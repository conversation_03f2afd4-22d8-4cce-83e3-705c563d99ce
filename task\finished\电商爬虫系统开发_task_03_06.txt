# 子任务03-06: 高级反爬虫和代理池管理

## 任务描述
实现高级反爬虫检测规避技术和完善的代理池管理系统，包括高级指纹伪装、代理轮换、IP池管理等，进一步提高爬虫的隐蔽性和成功率。

## 具体任务内容

### 1. 高级指纹反检测
- Canvas和WebGL指纹随机化
- 音频指纹和字体指纹伪装
- 屏幕分辨率和设备像素比伪装
- 时区、语言和地理位置伪装
- WebRTC和媒体设备指纹处理

### 2. 代理池管理系统
- 代理服务器连接和验证
- 代理健康检查和性能监控
- 代理自动轮换和负载均衡
- 失效代理自动移除和补充
- 代理质量评分和智能选择

### 3. 高级行为模拟
- 鼠标轨迹和点击模式模拟
- 键盘输入节奏和模式
- 页面滚动和缩放行为
- 多标签页和窗口操作
- 浏览历史和书签模拟

### 4. 动态反检测策略
- 实时检测规避调整
- A/B测试不同策略效果
- 机器学习驱动的策略优化
- 检测特征库自动更新
- 策略有效性评估和反馈

### 5. 高级会话管理
- 多账户和身份管理
- 登录状态维护和轮换
- 购物车和用户偏好模拟
- 社交媒体集成和关联

### 6. MercadoLibre专用高级策略
- ML网站WAF规则深度分析和绕过
- 阿根廷/巴西等地区IP池管理  
- ML特有的反爬虫检测点识别
- 动态价格抓取的时序策略
- ML API接口的逆向和模拟

## 技术实现

### 代理池管理器
```python
import asyncio
import aiohttp
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import random

class ProxyStatus(Enum):
    """代理状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    FAILED = "failed"

@dataclass
class ProxyInfo:
    """代理信息"""
    ip: str
    port: int
    protocol: str = "http"
    username: Optional[str] = None
    password: Optional[str] = None
    status: ProxyStatus = ProxyStatus.INACTIVE
    response_time: float = 0.0
    success_count: int = 0
    failure_count: int = 0
    last_used: float = 0.0
    last_check: float = 0.0
    quality_score: float = 0.0

class ProxyPoolManager:
    """代理池管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.proxies: List[ProxyInfo] = []
        self.active_proxies: List[ProxyInfo] = []
        self.check_interval = config.get('check_interval', 300)
        self.max_failures = config.get('max_failures', 5)
    
    async def start(self):
        """启动代理池管理"""
        await self.load_proxies()
        asyncio.create_task(self.health_check_loop())
    
    async def load_proxies(self):
        """加载代理列表"""
        proxy_sources = self.config.get('sources', [])
        for source in proxy_sources:
            await self._load_from_source(source)
    
    async def get_proxy(self) -> Optional[ProxyInfo]:
        """获取可用代理"""
        if not self.active_proxies:
            await self.refresh_active_proxies()
        
        if self.active_proxies:
            # 基于质量评分选择代理
            proxy = self._select_best_proxy()
            proxy.last_used = time.time()
            return proxy
        
        return None
    
    def _select_best_proxy(self) -> ProxyInfo:
        """选择最佳代理"""
        # 考虑响应时间、成功率、最后使用时间等因素
        weights = []
        for proxy in self.active_proxies:
            weight = self._calculate_proxy_weight(proxy)
            weights.append(weight)
        
        return random.choices(self.active_proxies, weights=weights)[0]
    
    async def health_check_loop(self):
        """健康检查循环"""
        while True:
            await self.check_all_proxies()
            await asyncio.sleep(self.check_interval)

class FingerprintManager:
    """指纹管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.fingerprint_cache = {}
    
    async def apply_fingerprint_evasion(self, page):
        """应用指纹规避"""
        await self._randomize_canvas_fingerprint(page)
        await self._randomize_webgl_fingerprint(page)
        await self._randomize_audio_fingerprint(page)
        await self._set_viewport_and_screen(page)
        await self._set_timezone_and_locale(page)
    
    async def _randomize_canvas_fingerprint(self, page):
        """随机化Canvas指纹"""
        canvas_script = """
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
            const context = originalGetContext.call(this, contextType, contextAttributes);
            if (contextType === '2d') {
                const originalFillText = context.fillText;
                context.fillText = function(text, x, y, maxWidth) {
                    // 添加微小的随机偏移
                    const offsetX = (Math.random() - 0.5) * 0.1;
                    const offsetY = (Math.random() - 0.5) * 0.1;
                    return originalFillText.call(this, text, x + offsetX, y + offsetY, maxWidth);
                };
            }
            return context;
        };
        """
        await page.add_init_script(canvas_script)

class MercadoLibreAdvancedStrategies:
    """MercadoLibre高级反爬虫策略"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.waf_rules = self._load_waf_rules()
        self.regional_proxies = self._setup_regional_proxies()
        self.api_endpoints = self._discover_api_endpoints()
    
    async def bypass_waf_protection(self, page, url: str) -> bool:
        """绕过WAF保护"""
        try:
            # 检测WAF类型
            waf_type = await self._detect_waf_type(page, url)
            
            if waf_type == "cloudflare":
                return await self._bypass_cloudflare(page, url)
            elif waf_type == "custom_ml":
                return await self._bypass_ml_custom_waf(page, url)
            
            return True
        except Exception as e:
            print(f"WAF bypass failed: {e}")
            return False
    
    async def _bypass_cloudflare(self, page, url: str) -> bool:
        """绕过CloudFlare保护"""
        # 等待CloudFlare challenge
        try:
            await page.wait_for_selector('.cf-browser-verification', timeout=5000)
            # 等待自动通过
            await page.wait_for_load_state('networkidle', timeout=30000)
            return True
        except:
            # 没有遇到challenge或已经通过
            return True
    
    def _setup_regional_proxies(self) -> Dict[str, List[ProxyInfo]]:
        """设置地区代理池"""
        return {
            'argentina': self._load_argentina_proxies(),
            'brazil': self._load_brazil_proxies(),
            'mexico': self._load_mexico_proxies(),
            'colombia': self._load_colombia_proxies()
        }
    
    def _load_argentina_proxies(self) -> List[ProxyInfo]:
        """加载阿根廷代理"""
        # 阿根廷常用代理服务器
        argentina_proxies = [
            ProxyInfo(ip="************", port=8080, protocol="http"),
            ProxyInfo(ip="************", port=3128, protocol="http"),
            ProxyInfo(ip="*************", port=8080, protocol="http"),
            # 更多阿根廷代理...
        ]
        return argentina_proxies
    
    async def detect_anti_crawling_mechanisms(self, page) -> Dict[str, bool]:
        """检测反爬虫机制"""
        mechanisms = {
            'captcha': False,
            'rate_limiting': False,
            'ip_blocking': False,
            'js_challenge': False,
            'behavioral_analysis': False
        }
        
        # 检测CAPTCHA
        captcha_selectors = [
            '.g-recaptcha',
            '.h-captcha', 
            '#captcha',
            '[data-sitekey]'
        ]
        
        for selector in captcha_selectors:
            try:
                await page.wait_for_selector(selector, timeout=1000)
                mechanisms['captcha'] = True
                break
            except:
                continue
        
        # 检测频率限制
        try:
            error_text = await page.text_content('body')
            if 'too many requests' in error_text.lower():
                mechanisms['rate_limiting'] = True
        except:
            pass
        
        return mechanisms
    
    async def implement_dynamic_pricing_strategy(self, page, product_url: str) -> Dict:
        """实现动态价格抓取策略"""
        pricing_data = {
            'current_price': None,
            'original_price': None,
            'discount_percentage': None,
            'price_history': [],
            'timestamp': time.time()
        }
        
        # 多次采样价格以检测动态变化
        for i in range(3):
            await page.goto(product_url)
            await page.wait_for_load_state('networkidle')
            
            # 提取价格数据
            price_selectors = [
                '.price-tag-amount',
                '.price-current',
                '[data-testid="price"]',
                '.ui-pdp-price__second-line'
            ]
            
            for selector in price_selectors:
                try:
                    price_element = await page.wait_for_selector(selector, timeout=5000)
                    price_text = await price_element.text_content()
                    
                    if price_text:
                        current_price = self._parse_price(price_text)
                        if current_price:
                            pricing_data['price_history'].append({
                                'price': current_price,
                                'timestamp': time.time(),
                                'sample': i + 1
                            })
                            
                            if not pricing_data['current_price']:
                                pricing_data['current_price'] = current_price
                    break
                except:
                    continue
            
            # 随机延迟避免检测
            await asyncio.sleep(random.uniform(2, 5))
        
        return pricing_data
    
    def _parse_price(self, price_text: str) -> Optional[float]:
        """解析价格文本"""
        import re
        # 移除货币符号和格式化
        cleaned = re.sub(r'[^\d,.]', '', price_text.replace(',', ''))
        try:
            return float(cleaned)
        except ValueError:
            return None
```

## 配置文件结构
```yaml
# advanced_anti_detection_config.yaml
proxy_pool:
  sources:
    - type: "file"
      path: "proxies.txt"
    - type: "api"
      url: "https://proxy-api.com/list"
      auth_token: "your_token"
  
  health_check:
    interval: 300  # 5分钟
    timeout: 10
    test_url: "https://httpbin.org/ip"
  
  rotation:
    strategy: "quality_based"  # random, round_robin, quality_based
    max_uses_per_proxy: 100
    cooldown_time: 3600  # 1小时

fingerprint_evasion:
  canvas:
    enabled: true
    noise_level: 0.1
  
  webgl:
    enabled: true
    vendor_override: true
    renderer_override: true
  
  audio:
    enabled: true
    noise_frequency: 0.001
  
  screen:
    randomize_resolution: true
    resolution_pool:
      - [1920, 1080]
      - [1366, 768]
      - [1280, 720]
      - [1440, 900]

# MercadoLibre专用高级配置
mercadolibre_advanced:
  waf_bypass:
    cloudflare:
      enabled: true
      challenge_timeout: 30
      max_retries: 3
      solve_method: "auto"  # auto, manual, service
    
    custom_ml_waf:
      enabled: true
      detection_patterns:
        - "blocked"
        - "access denied"
        - "too many requests"
  
  regional_proxies:
    argentina:
      enabled: true
      min_proxies: 10
      proxy_sources:
        - "premium_argentina.txt"
        - "free_argentina.txt"
    
    brazil:
      enabled: true
      min_proxies: 8
      proxy_sources:
        - "premium_brazil.txt"
    
    mexico:
      enabled: true
      min_proxies: 5
  
  dynamic_pricing:
    enabled: true
    sample_count: 3
    sample_interval: [2, 5]  # 秒
    price_change_threshold: 0.05  # 5%变化阈值
  
  api_endpoints:
    search: "/api/sites/{site_id}/search"
    product: "/api/items/{item_id}"
    shipping: "/api/items/{item_id}/shipping_options"
    reviews: "/api/reviews/item/{item_id}"
  
  rate_limiting:
         requests_per_minute: 30
     burst_requests: 5
          cooldown_after_block: 1800  # 30分钟

advanced_behavior:
  mouse_movement:
    enabled: true
    natural_curves: true
    speed_variation: 0.3
  
  typing_simulation:
    enabled: true
    wpm_range: [40, 80]
    error_rate: 0.02
  
  scroll_patterns:
    enabled: true
    reading_pauses: true
    random_directions: true
```

## 预期产出物
1. **代理池管理器** (`proxy_pool_manager.py`)
2. **高级指纹管理器** (`advanced_fingerprint_manager.py`)
3. **行为模拟增强器** (`advanced_behavior_simulator.py`)
4. **动态策略调整器** (`dynamic_strategy_adjuster.py`)
5. **代理质量评估器** (`proxy_quality_assessor.py`)
6. **MercadoLibre高级策略模块** (`mercadolibre_advanced_strategies.py`)
7. **WAF绕过工具** (`waf_bypass_manager.py`)
8. **地区代理池管理** (`regional_proxy_manager.py`)
9. **动态价格抓取器** (`dynamic_pricing_extractor.py`)
10. **反检测配置管理** (`advanced_config_manager.py`)
11. **单元测试文件** (`tests/test_advanced_anti_detection.py`)
12. **MercadoLibre高级测试** (`tests/test_mercadolibre_advanced.py`)
13. **性能测试用例** (`tests/performance/test_proxy_pool.py`)

## 验收标准
- [ ] 代理池管理功能完善
- [ ] 高级指纹伪装有效
- [ ] 行为模拟更加逼真
- [ ] 动态策略调整机制运行正常
- [ ] 代理质量评估准确
- [ ] 系统整体检测规避率提升
- [ ] 通过所有单元测试
- [ ] 性能测试满足要求

## 性能指标
- **代理切换时间**: ≤2秒
- **指纹生成时间**: ≤100ms
- **代理池可用率**: ≥80%
- **检测规避率**: ≥95%
- **系统稳定性**: 连续运行24小时无故障

## 预估工时
1.5天

## 依赖关系
- 前置任务: Task 03-03, Task 03-05
- 为Task 03-07提供高级反检测能力

## 状态
待开始 