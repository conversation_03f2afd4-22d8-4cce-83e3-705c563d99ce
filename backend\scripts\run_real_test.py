#!/usr/bin/env python3
"""
快速启动真实场景测试

简化的测试启动脚本，用于快速验证任务调度系统
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def quick_test():
    """快速测试函数"""
    
    logger.info("🚀 Starting Quick Real Scenario Test")
    
    try:
        # 检查Excel文件
        excel_path = project_root / "LinksToMonit" / "all_task_item.xlsx"
        
        if not excel_path.exists():
            logger.error(f"❌ Excel file not found: {excel_path}")
            logger.info("Please ensure the Excel file exists in LinksToMonit/all_task_item.xlsx")
            return
        
        logger.info(f"✅ Found Excel file: {excel_path}")
        
        # 导入测试模块
        from backend.tests.test_real_scenario import RealScenarioTester
        
        # 创建测试器
        tester = RealScenarioTester()
        
        # 运行测试
        results = await tester.run_complete_test(str(excel_path))
        
        # 输出简要结果
        logger.info("🎉 Test Completed Successfully!")
        logger.info("=" * 50)
        logger.info(f"📊 Results Summary:")
        logger.info(f"  • Total URLs: {results['performance_metrics']['total_urls_processed']}")
        logger.info(f"  • Total Batches: {results['performance_metrics']['total_batches_created']}")
        logger.info(f"  • Success Rate: {results['performance_metrics']['success_rate']:.1%}")
        logger.info(f"  • Execution Time: {results['performance_metrics']['execution_time_seconds']:.1f}s")
        logger.info(f"  • Processing Speed: {results['performance_metrics']['urls_per_second']:.2f} URLs/sec")
        logger.info(f"  • Peak Concurrent: {results['performance_metrics']['peak_concurrent_batches']} batches")
        
        return results
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("Please ensure all dependencies are installed:")
        logger.info("  pip install pandas openpyxl")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


async def check_dependencies():
    """检查依赖项"""
    
    logger.info("🔍 Checking Dependencies...")
    
    missing_deps = []
    
    # 检查pandas
    try:
        import pandas as pd
        logger.info(f"✅ pandas: {pd.__version__}")
    except ImportError:
        missing_deps.append("pandas")
    
    # 检查openpyxl
    try:
        import openpyxl
        logger.info(f"✅ openpyxl: {openpyxl.__version__}")
    except ImportError:
        missing_deps.append("openpyxl")
    
    # 检查Redis连接
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        logger.info("✅ Redis: Connected")
    except Exception as e:
        logger.warning(f"⚠️ Redis: {e}")
        logger.info("Please start Redis server: redis-server")
    
    if missing_deps:
        logger.error(f"❌ Missing dependencies: {missing_deps}")
        logger.info(f"Install with: pip install {' '.join(missing_deps)}")
        return False
    
    return True


async def main():
    """主函数"""
    
    print("=" * 60)
    print("🧪 MonIt Task Scheduler - Real Scenario Test")
    print("=" * 60)
    
    # 检查依赖
    if not await check_dependencies():
        return
    
    # 检查工作目录
    if not (project_root / "LinksToMonit").exists():
        logger.error("❌ LinksToMonit directory not found")
        logger.info("Please ensure you're running from the correct directory")
        return
    
    # 创建日志目录
    os.makedirs(project_root / "logs", exist_ok=True)
    
    # 运行测试
    try:
        await quick_test()
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
