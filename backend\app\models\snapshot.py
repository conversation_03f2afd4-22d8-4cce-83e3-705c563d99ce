"""
商品快照时间序列数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import DateTime, Integer, ForeignKey, DECIMAL, Boolean, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class ProductSnapshot(Base):
    """商品快照时间序列表 (TimescaleDB hypertable)"""
    
    __tablename__ = "product_snapshots"
    
    # 主键 (时间戳 + 商品ID)
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        primary_key=True,
        comment="抓取时间戳"
    )
    product_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("products.id", ondelete="CASCADE"),
        primary_key=True,
        comment="商品ID"
    )
    
    # 任务关联
    task_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("crawl_tasks.id", ondelete="CASCADE"),
        nullable=False,
        comment="任务ID"
    )
    
    # 价格信息
    current_price: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(12, 2),
        nullable=True,
        comment="商品当前售价"
    )
    original_price: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(12, 2),
        nullable=True,
        comment="商品原价"
    )
    discount_rate: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(5, 2),
        nullable=True,
        comment="折扣率(百分比)"
    )
    
    # 库存和销售信息
    stock_quantity: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="库存数量"
    )
    sold_quantity: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="销售数量"
    )
    
    # 评价信息
    rating: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(3, 2),
        nullable=True,
        comment="商品评分(0-5)"
    )
    review_count: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="评分数量"
    )
    
    # 媒体信息
    main_image_url: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="商品首图URL"
    )
    images: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="其他图片URL数组"
    )
    
    # 扩展字段
    extra_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="扩展数据"
    )
    
    # 元数据
    crawl_duration_ms: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="爬取耗时(毫秒)"
    )
    success: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        comment="是否成功抓取"
    )
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 关系
    product: Mapped["Product"] = relationship(
        "Product",
        back_populates="snapshots"
    )
    task: Mapped["CrawlTask"] = relationship(
        "CrawlTask",
        back_populates="snapshots"
    )
    
    def __repr__(self) -> str:
        return f"<ProductSnapshot(product_id={self.product_id}, timestamp={self.timestamp}, current_price={self.current_price})>"
    
    @property
    def is_price_changed(self) -> bool:
        """价格是否有变化"""
        return self.current_price != self.original_price if self.current_price and self.original_price else False
    
    @property 
    def price_change_rate(self) -> Optional[float]:
        """价格变化率"""
        if not self.current_price or not self.original_price or self.original_price == 0:
            return None
        return float((self.current_price - self.original_price) / self.original_price * 100)
    
    @property
    def availability_status(self) -> str:
        """库存状态"""
        if self.stock_quantity is None:
            return "unknown"
        elif self.stock_quantity == 0:
            return "out_of_stock"
        elif self.stock_quantity < 10:
            return "low_stock"
        else:
            return "in_stock" 