"""
爬虫系统异常模块

导出所有自定义异常类和工具函数
"""

from .crawler_exceptions import (
    # 基础异常
    CrawlerException,
    
    # 浏览器异常
    BrowserException,
    BrowserStartupException,
    BrowserTimeoutException,
    PageNavigationException,
    
    # 数据提取异常
    ExtractionException,
    SelectorNotFoundException,
    DataValidationException,
    
    # 任务异常
    TaskException,
    TaskTimeoutException,
    TaskRetryExhaustedException,
    
    # 会话异常
    SessionException,
    SessionExpiredException,
    LoginException,
    
    # 代理异常
    ProxyException,
    ProxyConnectionException,
    
    # 反检测异常
    AntiDetectionException,
    CaptchaDetectedException,
    RateLimitException,
    
    # 配置异常
    ConfigurationException,
    
    # 工具函数
    create_exception,
    EXCEPTION_MAP
)

__all__ = [
    # 基础异常
    'CrawlerException',
    
    # 浏览器异常
    'BrowserException',
    'BrowserStartupException',
    'BrowserTimeoutException',
    'PageNavigationException',
    
    # 数据提取异常
    'ExtractionException',
    'SelectorNotFoundException',
    'DataValidationException',
    
    # 任务异常
    'TaskException',
    'TaskTimeoutException',
    'TaskRetryExhaustedException',
    
    # 会话异常
    'SessionException',
    'SessionExpiredException',
    'LoginException',
    
    # 代理异常
    'ProxyException',
    'ProxyConnectionException',
    
    # 反检测异常
    'AntiDetectionException',
    'CaptchaDetectedException',
    'RateLimitException',
    
    # 配置异常
    'ConfigurationException',
    
    # 工具函数
    'create_exception',
    'EXCEPTION_MAP'
] 