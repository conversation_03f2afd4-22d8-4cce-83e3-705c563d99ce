"""
监控任务API路由

提供监控任务的CRUD操作和管理功能
"""

import json
import logging
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from app.core.task_manager import TaskPriority

from app.models.monitoring_task import (
    MonitoringTaskCreate,
    MonitoringTaskUpdate,
    MonitoringTaskResponse,
    MonitoringTaskListResponse,
    UrlBatchAddRequest,
    UrlBatchAddResponse,
    TaskStatus,
    ScheduleConfig,
    TaskConfig,
    TaskStats,
    MonitoringUrl,
    generate_task_id,
    generate_url_id,
    calculate_next_run,
    validate_schedule_config
)
from app.utils.datetime_utils import get_local_now, format_local_datetime, get_local_isoformat

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/monitoring-tasks", tags=["monitoring-tasks"])

# 获取TaskStorageService
async def get_task_storage():
    """获取任务存储服务"""
    from app.services.task_storage_service import TaskStorageService
    return TaskStorageService()


# URL池任务集成函数
async def get_url_pool_created_tasks():
    """获取通过URL池创建的任务"""
    try:
        import redis.asyncio as redis

        # 连接Redis
        redis_client = redis.from_url("redis://redis:6379/0")

        # 获取所有任务ID
        task_ids = await redis_client.smembers("monitoring_tasks:all_ids")

        tasks = []
        for task_id in task_ids:
            task_key = f"monitoring_tasks:{task_id.decode()}"
            task_data = await redis_client.hgetall(task_key)

            if task_data:
                # 转换Redis数据为字典
                task_dict = {}
                for key, value in task_data.items():
                    key_str = key.decode() if isinstance(key, bytes) else key
                    value_str = value.decode() if isinstance(value, bytes) else value
                    task_dict[key_str] = value_str

                # 转换为监控任务格式
                converted_task = await convert_redis_task_to_monitoring_task(task_dict)
                if converted_task:
                    tasks.append(converted_task)

        await redis_client.close()
        return tasks

    except Exception as e:
        logger.error(f"Error getting URL pool tasks: {e}")
        return []


async def convert_redis_task_to_monitoring_task(redis_task: dict):
    """将Redis任务数据转换为监控任务格式"""
    try:
        import json
        import redis.asyncio as redis
        from datetime import datetime

        # 解析JSON字段 - 支持字符串和字典格式
        schedule_raw = redis_task.get('schedule', {})
        if isinstance(schedule_raw, str):
            schedule_data = json.loads(schedule_raw) if schedule_raw else {}
        else:
            schedule_data = schedule_raw or {}

        config_raw = redis_task.get('config', {})
        if isinstance(config_raw, str):
            config_data = json.loads(config_raw) if config_raw else {}
        else:
            config_data = config_raw or {}

        # 修复schedule数据结构 - 从next_run或start_time提取时间
        if 'time' not in schedule_data or not schedule_data['time']:
            # 首先尝试从next_run字段提取时间
            next_run = redis_task.get('next_run')
            if next_run and schedule_data.get('type') == 'daily':
                try:
                    from datetime import datetime
                    # 解析next_run时间并提取HH:mm格式
                    dt = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                    schedule_data['time'] = dt.strftime('%H:%M')
                    logger.info(f"Extracted time from next_run: {schedule_data['time']}")
                except Exception as e:
                    logger.warning(f"Failed to extract time from next_run: {e}")

            # 如果next_run提取失败，尝试从start_time提取
            if ('time' not in schedule_data or not schedule_data['time']) and 'start_time' in schedule_data and schedule_data['start_time']:
                try:
                    start_time = schedule_data['start_time']
                    if isinstance(start_time, str):
                        # 尝试解析时间并提取HH:mm格式
                        dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        schedule_data['time'] = dt.strftime('%H:%M')
                        logger.info(f"Extracted time from start_time: {schedule_data['time']}")
                except Exception as e:
                    logger.warning(f"Failed to convert start_time to time: {e}")

            # 最后的默认值（只有在无法从任何地方提取时间时才使用）
            if ('time' not in schedule_data or not schedule_data['time']) and schedule_data.get('type') == 'daily':
                schedule_data['time'] = '09:00'  # 默认上午9点
                logger.warning(f"Using default time 09:00 for task {redis_task.get('id')}")

        # 修复优先级映射
        if 'priority' in config_data:
            priority_map = {'normal': 'medium', 'high': 'high', 'low': 'low'}
            config_data['priority'] = priority_map.get(config_data['priority'], 'medium')

        # 获取任务的URL统计信息和来源信息
        task_id = redis_task.get('id')
        total_urls = 0
        active_urls = 0
        task_source = "manual"  # 默认来源

        if task_id:
            try:
                redis_client = redis.from_url("redis://redis:6379/0")

                # 获取任务关联的URL ID列表（使用新的分层结构）
                task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
                url_ids = await redis_client.smembers(task_urls_key)

                total_urls = len(url_ids)

                # 如果有URL关联，说明是从URL池创建的
                if total_urls > 0:
                    task_source = "url_pool"

                    # 统计活跃URL数量
                    for url_id in url_ids:
                        url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
                        url_key = f"url_pool:items:{url_id_str}"
                        url_data = await redis_client.hgetall(url_key)

                        if url_data:
                            url_status = url_data.get(b'status', b'active').decode()
                            if url_status == 'active':
                                active_urls += 1
                else:
                    # 如果没有URL关联，检查是否有url_count字段（旧数据）
                    url_count = redis_task.get('url_count')
                    if url_count and int(url_count) > 0:
                        total_urls = int(url_count)
                        active_urls = total_urls  # 假设都是活跃的
                        task_source = "url_pool"

                await redis_client.close()
            except Exception as e:
                logger.warning(f"Failed to get URL stats for task {task_id}: {e}")

        return {
            "id": redis_task.get('id'),
            "name": redis_task.get('name'),
            "description": redis_task.get('description', ''),
            "schedule": schedule_data,
            "config": config_data,
            "status": redis_task.get('status', 'draft'),
            "is_running": redis_task.get('is_running', 'false') == 'true',
            "created_at": redis_task.get('created_at'),
            "updated_at": redis_task.get('updated_at'),
            "last_run": redis_task.get('last_run') or None,
            "next_run": redis_task.get('next_run') or None,
            "total_urls": total_urls,  # 添加URL统计
            "active_urls": active_urls,  # 添加活跃URL统计
            "stats": {
                "total_runs": int(redis_task.get('total_runs', 0)),
                "success_rate": float(redis_task.get('success_rate', 0.0)),
                "avg_response_time": float(redis_task.get('avg_duration', 0.0)),
                "last_success_time": redis_task.get('last_success_time') or None
            },
            "source": task_source,  # 使用动态检测的来源
            "source_info": {
                "url_count": total_urls,  # 使用实际统计的URL数量
                "created_from": "URL池" if task_source == "url_pool" else "手动创建"
            }
        }
    except Exception as e:
        logger.error(f"Error converting Redis task: {e}")
        return None

# 内存存储已移除，统一使用Redis存储


class MonitoringTaskListResponse(BaseModel):
    """监控任务列表响应"""
    success: bool
    data: List[MonitoringTaskResponse]
    total: int


class MonitoringTaskDetailResponse(BaseModel):
    """监控任务详情响应"""
    success: bool
    data: MonitoringTaskResponse


@router.post("/", response_model=MonitoringTaskDetailResponse)
async def create_monitoring_task(
    task_data: MonitoringTaskCreate,
    task_storage: object = Depends(get_task_storage)
):
    """创建新的监控任务"""
    try:
        logger.info(f"Creating new monitoring task: {task_data.name}")

        # 生成任务ID
        task_id = generate_task_id()

        # 准备任务数据
        now = get_local_now()

        # 计算下次执行时间
        next_run = None
        if task_data.schedule.enabled:
            next_run = calculate_next_run(task_data.schedule)

        # 构建完整的任务数据
        full_task_data = {
            "id": task_id,
            "name": task_data.name,
            "description": task_data.description or "",
            "status": "draft",
            "platform": task_data.config.platform,
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "schedule": task_data.schedule.model_dump(),
            "config": task_data.config.model_dump(),
            "next_run": next_run.isoformat() if next_run else "",
            "last_run": "",
            "is_running": False,
            "total_runs": 0,
            "success_count": 0,
            "failed_count": 0,
            "success_rate": 0.0,
            "avg_duration": 0.0
        }

        # 保存任务
        success = await task_storage.save_task(task_id, full_task_data)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save task")

        # 返回创建的任务
        created_task = await task_storage.get_task(task_id)
        if not created_task:
            raise HTTPException(status_code=500, detail="Failed to retrieve created task")

        # 转换为响应格式
        task_response = await convert_redis_task_to_monitoring_task(created_task)
        if not task_response:
            raise HTTPException(status_code=500, detail="Failed to format task response")

        logger.info(f"Successfully created monitoring task: {task_id}")
        return MonitoringTaskDetailResponse(
            success=True,
            data=task_response
        )

    except Exception as e:
        logger.error(f"Failed to create monitoring task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=MonitoringTaskListResponse)
async def get_monitoring_tasks(
    status: Optional[TaskStatus] = Query(None, description="按状态筛选"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    source: Optional[str] = Query(None, description="任务来源"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取监控任务列表 - 增强版"""
    try:
        logger.info(f"Getting monitoring tasks with enhanced filters: status={status}, platform={platform}, search={search}, source={source}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 准备过滤条件
        filters = {}
        if status:
            filters['status'] = status.value
        if platform:
            filters['platform'] = platform
        if search:
            filters['search'] = search
        if source:
            filters['source'] = source
        filters['sort_by'] = sort_by
        filters['sort_order'] = sort_order

        # 获取任务列表（支持分页和过滤）
        result = await task_storage.list_tasks(filters, page, page_size)
        all_tasks = result['tasks']

        # TaskStorageService已经处理了过滤、排序和分页
        total = result['total']
        paginated_tasks = all_tasks
        
        # 转换为响应格式
        task_responses = []
        for task_data in paginated_tasks:
            # 获取URL数量
            task_id = task_data["id"]
            url_ids = await task_storage.get_task_urls(task_id)
            total_urls = len(url_ids)
            active_urls = total_urls  # 简化处理，假设所有URL都是活跃的

            # 添加任务来源信息
            task_source = task_data.get("source", "manual")
            source_info = task_data.get("source_info", {})

            # 确保tags字段是正确的列表类型
            tags_value = task_data.get("tags", [])
            if isinstance(tags_value, str):
                # 如果是字符串，尝试解析
                try:
                    import json
                    tags_value = json.loads(tags_value)
                except (json.JSONDecodeError, ValueError):
                    # JSON解析失败，尝试ast.literal_eval
                    try:
                        import ast
                        tags_value = ast.literal_eval(tags_value)
                    except (ValueError, SyntaxError):
                        # 都失败了，包装成列表
                        tags_value = [tags_value] if tags_value else []
            elif not isinstance(tags_value, list):
                tags_value = []

            task_response = MonitoringTaskResponse(
                id=task_data["id"],
                name=task_data["name"],
                description=task_data.get("description"),
                tags=tags_value,  # 使用处理后的tags值
                schedule=ScheduleConfig(**task_data["schedule"]),
                config=TaskConfig(**task_data["config"]),
                total_urls=total_urls,
                active_urls=active_urls,
                status=TaskStatus(task_data["status"]),
                is_running=task_data.get("is_running", False),
                last_run=task_data.get("last_run"),
                source=task_source,
                source_info=source_info,
                next_run=task_data.get("next_run"),
                stats=TaskStats(**task_data.get("stats", {})),
                created_at=task_data["created_at"],
                updated_at=task_data["updated_at"]
            )

            # 将响应转换为字典并添加扩展信息
            response_dict = task_response.model_dump()
            response_dict["source"] = task_source
            response_dict["source_info"] = source_info

            task_responses.append(response_dict)
        
        return MonitoringTaskListResponse(
            success=True,
            data=task_responses,
            total=total
        )
        
    except Exception as e:
        logger.error(f"Failed to get monitoring tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 创建任务API已移除，请使用URL池流程：/api/v1/task-create/create-from-urls




@router.get("/{task_id}", response_model=MonitoringTaskDetailResponse)
async def get_monitoring_task(task_id: str):
    """获取监控任务详情"""
    try:
        logger.info(f"Getting monitoring task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取任务关联的URL数量
        url_ids = await task_storage.get_task_urls(task_id)
        url_count = len(url_ids)

        # 构建统计信息
        stats = TaskStats(
            total_runs=int(task_data.get("total_runs", 0)),
            success_count=int(task_data.get("success_count", 0)),
            failed_count=int(task_data.get("failed_count", 0)),
            success_rate=float(task_data.get("success_rate", 0.0)),
            avg_duration=float(task_data.get("avg_duration", 0.0)),
            last_success_time=task_data.get("last_success_time")
        )

        # 确保tags字段是正确的列表类型
        tags_value = task_data.get("tags", [])
        if isinstance(tags_value, str):
            # 如果是字符串，尝试解析
            try:
                import json
                tags_value = json.loads(tags_value)
            except (json.JSONDecodeError, ValueError):
                # JSON解析失败，尝试ast.literal_eval
                try:
                    import ast
                    tags_value = ast.literal_eval(tags_value)
                except (ValueError, SyntaxError):
                    # 都失败了，包装成列表
                    tags_value = [tags_value] if tags_value else []
        elif not isinstance(tags_value, list):
            tags_value = []

        response_data = MonitoringTaskResponse(
            id=task_data["id"],
            name=task_data["name"],
            description=task_data.get("description", ""),
            tags=tags_value,  # 使用处理后的tags值
            status=task_data["status"],
            schedule=task_data["schedule"],
            config=task_data["config"],
            total_urls=url_count,
            active_urls=url_count,  # 假设所有URL都是活跃的，后续可以优化
            created_at=task_data["created_at"],
            updated_at=task_data.get("updated_at"),
            last_run=task_data.get("last_run"),
            next_run=task_data.get("next_run"),
            is_running=task_data.get("is_running", False),
            stats=stats
        )

        return MonitoringTaskDetailResponse(success=True, data=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get monitoring task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/start")
async def start_monitoring_task(task_id: str):
    """启动监控任务"""
    try:
        logger.info(f"Starting monitoring task: {task_id}")
        
        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")
        
        # 检查任务状态
        current_status = task_data.get("status")
        if current_status == "active" or current_status == TaskStatus.ACTIVE.value:
            raise HTTPException(status_code=400, detail="Task is already active")

        # 更新任务状态
        now = get_local_isoformat()

        # 重新计算下次执行时间
        from ..models.monitoring_task import calculate_next_run, ScheduleConfig
        schedule = ScheduleConfig(**task_data["schedule"])
        next_run = calculate_next_run(schedule)

        # 更新任务
        updates = {
            "status": TaskStatus.ACTIVE.value,
            "updated_at": now,
            "next_run": next_run.isoformat() if next_run else None
        }

        success = await task_storage.update_task(task_id, updates)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update task status")

        # 注册任务到Celery Beat调度
        try:
            from ..core.schedule_manager import get_schedule_manager
            schedule_manager = await get_schedule_manager()

            schedule_success = await schedule_manager.register_monitoring_task(task_id, schedule)
            if schedule_success:
                logger.info(f"Task {task_id} registered to Celery Beat successfully")
            else:
                logger.warning(f"Failed to register task {task_id} to Celery Beat")
        except Exception as e:
            logger.error(f"Failed to register task {task_id} to Celery Beat: {e}")
            # 不阻塞任务启动，只记录错误

        logger.info(f"Monitoring task started successfully: {task_id}")
        return {"success": True, "message": f"Monitoring task {task_id} started successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start monitoring task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/pause")
async def pause_monitoring_task(task_id: str):
    """暂停监控任务"""
    try:
        logger.info(f"Pausing monitoring task: {task_id}")
        
        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 检查任务状态
        current_status = task_data.get("status")
        if current_status != "active" and current_status != TaskStatus.ACTIVE.value:
            raise HTTPException(status_code=400, detail="Task is not active")

        # 更新任务状态
        now = get_local_isoformat()

        # 更新任务
        updates = {
            "status": TaskStatus.PAUSED.value,
            "updated_at": now
        }

        success = await task_storage.update_task(task_id, updates)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update task status")

        # 从Celery Beat调度中暂停任务
        try:
            from ..core.schedule_manager import get_schedule_manager
            schedule_manager = await get_schedule_manager()

            pause_success = await schedule_manager.pause_monitoring_task(task_id)
            if pause_success:
                logger.info(f"Task {task_id} paused in Celery Beat successfully")
            else:
                logger.warning(f"Failed to pause task {task_id} in Celery Beat")
        except Exception as e:
            logger.error(f"Failed to pause task {task_id} in Celery Beat: {e}")
            # 不阻塞任务暂停，只记录错误

        logger.info(f"Monitoring task paused successfully: {task_id}")
        return {"success": True, "message": f"Monitoring task {task_id} paused successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to pause monitoring task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{task_id}")
async def update_monitoring_task(task_id: str, request: MonitoringTaskUpdate):
    """更新监控任务"""
    try:
        logger.info(f"Updating monitoring task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 检查任务是否正在运行（运行中的任务限制编辑）
        is_running = task_data.get("is_running", False)
        if isinstance(is_running, str):
            is_running = is_running.lower() == "true"

        # 准备更新数据
        now = get_local_isoformat()
        updates = {"updated_at": now}

        # 更新基本信息
        if request.name is not None:
            updates["name"] = request.name
        if request.description is not None:
            updates["description"] = request.description

        # 更新调度配置（运行中的任务不能修改调度）
        if request.schedule is not None and not is_running:
            updates["schedule"] = request.schedule.model_dump()

            # 重新计算下次执行时间
            try:
                from ..models.monitoring_task import calculate_next_run
                next_run = calculate_next_run(request.schedule)
                if next_run:
                    updates["next_run"] = next_run.isoformat()
                    logger.info(f"Updated next_run for task {task_id}: {next_run}")
                else:
                    updates["next_run"] = None
                    logger.info(f"Cleared next_run for task {task_id}")
            except Exception as e:
                logger.error(f"Failed to calculate next_run for task {task_id}: {e}")
                # 不抛出异常，允许其他更新继续

        # 更新任务配置（运行中的任务不能修改配置）
        if request.config is not None and not is_running:
            updates["config"] = request.config.model_dump()

        # 保存更新
        success = await task_storage.update_task(task_id, updates)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update task")

        # 获取更新后的任务数据
        updated_task_data = await task_storage.get_task(task_id)

        logger.info(f"Monitoring task updated successfully: {task_id}")

        # 获取URL数量
        url_ids = await task_storage.get_task_urls(task_id)
        url_count = len(url_ids)

        # 构建统计信息
        stats = TaskStats(
            total_runs=int(updated_task_data.get("total_runs", 0)),
            success_count=int(updated_task_data.get("success_count", 0)),
            failed_count=int(updated_task_data.get("failed_count", 0)),
            success_rate=float(updated_task_data.get("success_rate", 0.0)),
            avg_duration=float(updated_task_data.get("avg_duration", 0.0)),
            last_success_time=updated_task_data.get("last_success_time")
        )

        # 确保tags字段是正确的列表类型
        tags_value = updated_task_data.get("tags", [])
        if isinstance(tags_value, str):
            # 如果是字符串，尝试解析
            try:
                import json
                tags_value = json.loads(tags_value)
            except (json.JSONDecodeError, ValueError):
                # JSON解析失败，尝试ast.literal_eval
                try:
                    import ast
                    tags_value = ast.literal_eval(tags_value)
                except (ValueError, SyntaxError):
                    # 都失败了，包装成列表
                    tags_value = [tags_value] if tags_value else []
        elif not isinstance(tags_value, list):
            tags_value = []

        # 返回更新后的任务数据
        response_data = MonitoringTaskResponse(
            id=updated_task_data["id"],
            name=updated_task_data["name"],
            description=updated_task_data.get("description", ""),
            tags=tags_value,  # 使用处理后的tags值
            status=updated_task_data["status"],
            schedule=updated_task_data["schedule"],
            config=updated_task_data["config"],
            total_urls=url_count,
            active_urls=url_count,  # 假设所有URL都是活跃的
            created_at=updated_task_data["created_at"],
            updated_at=updated_task_data.get("updated_at"),
            last_run=updated_task_data.get("last_run"),
            next_run=updated_task_data.get("next_run"),
            is_running=updated_task_data.get("is_running", False),
            stats=stats
        )

        return MonitoringTaskDetailResponse(success=True, data=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update monitoring task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}")
async def delete_monitoring_task(task_id: str):
    """删除监控任务"""
    try:
        logger.info(f"Deleting monitoring task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 检查任务状态
        is_running = task_data.get("is_running", False)
        if is_running:
            raise HTTPException(status_code=400, detail="Cannot delete running task")

        # 从Celery Beat调度中移除任务
        try:
            from ..core.schedule_manager import get_schedule_manager
            schedule_manager = await get_schedule_manager()

            unregister_success = await schedule_manager.unregister_monitoring_task(task_id)
            if unregister_success:
                logger.info(f"Task {task_id} unregistered from Celery Beat successfully")
            else:
                logger.warning(f"Failed to unregister task {task_id} from Celery Beat")
        except Exception as e:
            logger.error(f"Failed to unregister task {task_id} from Celery Beat: {e}")
            # 继续删除任务，不阻塞

        # 删除任务
        success = await task_storage.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete task")

        logger.info(f"Monitoring task deleted successfully: {task_id}")
        return {"success": True, "message": f"Monitoring task {task_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete monitoring task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/urls")
async def get_task_urls(
    task_id: str,
    status: Optional[str] = Query(None, description="URL状态筛选"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取任务关联的URL列表"""
    try:
        logger.info(f"Getting URLs for task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 检查任务是否存在
        task_exists = await task_storage.task_exists(task_id)
        if not task_exists:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取任务关联的URL ID列表
        url_ids = await task_storage.get_task_urls(task_id)

        # 获取URL详细信息
        task_urls = []
        if url_ids:
            try:
                import redis.asyncio as redis
                redis_client = redis.from_url("redis://redis:6379/0")

                # 获取每个URL的详细信息
                for url_id in url_ids:
                    # 确保URL ID是字符串格式
                    url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
                    url_key = f"url_pool:items:{url_id_str}"
                    url_data = await redis_client.hgetall(url_key)

                    if url_data:
                        # 转换Redis数据为字典
                        url_dict = {}
                        for key, value in url_data.items():
                            key_str = key.decode() if isinstance(key, bytes) else key
                            value_str = value.decode() if isinstance(value, bytes) else value
                            url_dict[key_str] = value_str

                        # 转换为统一格式
                        converted_url = {
                            "id": url_dict.get("id"),
                            "url": url_dict.get("url"),
                            "status": url_dict.get("status", "active"),
                            "added_at": url_dict.get("added_at"),
                            "last_check": url_dict.get("last_check") or None,
                            "check_count": int(url_dict.get("check_count", 0)),
                            "error_count": int(url_dict.get("error_count", 0)),
                            "success_count": int(url_dict.get("success_count", 0)),
                            "platform": url_dict.get("platform"),
                            "source_file": url_dict.get("source_file"),
                            "metadata": url_dict.get("metadata"),
                            # Worker分配相关字段
                            "assigned_worker_id": url_dict.get("assigned_worker_id"),
                            "processing_status": url_dict.get("processing_status", "pending"),
                            "last_processed_at": url_dict.get("last_processed_at")
                        }
                        task_urls.append(converted_url)

                await redis_client.close()

            except Exception as e:
                logger.error(f"Error getting URL details from Redis: {e}")
                # 返回基础URL信息
                task_urls = [{"id": url_id, "url": f"URL {url_id}", "status": "unknown"} for url_id in url_ids]

        # 状态筛选
        if status:
            task_urls = [url for url in task_urls if url.get("status") == status]

        # 排序（按添加时间倒序）
        task_urls.sort(key=lambda x: x.get("added_at", ""), reverse=True)

        # 分页
        total = len(task_urls)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_urls = task_urls[start_idx:end_idx]

        return {
            "success": True,
            "data": paginated_urls,
            "total": total,
            "page": page,
            "page_size": page_size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get URLs for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{task_id}/urls")
async def update_task_urls(task_id: str, request: UrlBatchAddRequest):
    """更新任务关联的URL"""
    try:
        logger.info(f"Updating URLs for task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取当前任务的URL
        current_url_ids = await task_storage.get_task_urls(task_id)
        current_urls = set()

        # 获取当前URL的详细信息
        if current_url_ids:
            try:
                import redis.asyncio as redis
                redis_client = redis.from_url("redis://redis:6379/0")

                for url_id in current_url_ids:
                    url_key = f"url_pool:items:{url_id}"
                    url_data = await redis_client.hgetall(url_key)

                    if url_data:
                        url_str = url_data.get(b'url', b'').decode() if isinstance(url_data.get(b'url'), bytes) else url_data.get('url', '')
                        current_urls.add(url_str)

                await redis_client.close()
            except Exception as e:
                logger.error(f"Error getting current URLs from Redis: {e}")

        # 添加新URL
        new_urls = []
        new_url_ids = []
        now = get_local_isoformat()

        for url in request.urls:
            if url not in current_urls:
                # 为任务添加URL到URL池
                try:
                    import redis.asyncio as redis
                    from uuid import uuid4
                    import hashlib
                    import json

                    redis_client = redis.from_url("redis://redis:6379/0")

                    # 创建新的URL项目
                    url_id = str(uuid4())
                    url_hash = hashlib.md5(url.encode()).hexdigest()

                    # 存储URL到URL池
                    url_key = f"url_pool:items:{url_id}"
                    config = task_data.get('config', {})
                    platform = config.get('platform', 'unknown') if isinstance(config, dict) else 'unknown'

                    url_data = {
                        'id': url_id,
                        'url': url,
                        'platform': platform,
                        'source_file': f'task_{task_id}_manual_add',
                        'added_at': now,
                        'status': 'active',
                        'last_check': '',
                        'check_count': '0',
                        'success_count': '0',
                        'error_count': '0',
                        'metadata': json.dumps({'url_hash': url_hash, 'added_via': 'api'})
                    }
                    await redis_client.hset(url_key, mapping=url_data)

                    # 添加到索引
                    await redis_client.sadd('url_pool:all_ids', url_id)
                    await redis_client.sadd(f'url_pool:platform:{platform}', url_id)
                    await redis_client.sadd('url_pool:status:active', url_id)
                    await redis_client.sadd('url_pool:url_hashes', url_hash)

                    await redis_client.close()

                    new_urls.append(url)
                    new_url_ids.append(url_id)

                except Exception as e:
                    logger.error(f"Error adding URL to URL pool: {e}")
                    continue

        # 批量添加URL到任务
        if new_url_ids:
            success = await task_storage.add_urls_to_task(task_id, new_url_ids)
            if not success:
                logger.error(f"Failed to associate URLs with task {task_id}")
                # 不抛出异常，URL已经添加到池中

        # 更新任务的更新时间
        await task_storage.update_task(task_id, {"updated_at": now})

        # 计算总URL数量
        total_urls_count = len(current_urls) + len(new_urls)

        return UrlBatchAddResponse(
            added_count=len(new_urls),
            duplicate_count=len(request.urls) - len(new_urls),
            error_count=0,  # 目前没有错误处理，设为0
            total_urls=total_urls_count
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update URLs for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 临时存储执行历史（后续会替换为数据库）
task_execution_history = {}


@router.get("/{task_id}/history")
async def get_task_execution_history(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取任务执行历史"""
    try:
        logger.info(f"Getting execution history for task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 检查任务是否存在
        task_exists = await task_storage.task_exists(task_id)
        if not task_exists:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取执行历史
        history_records = task_execution_history.get(task_id, [])

        # 排序（按执行时间倒序）
        history_records.sort(key=lambda x: x.get("start_time", ""), reverse=True)

        # 分页
        total = len(history_records)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_history = history_records[start_idx:end_idx]

        return {
            "success": True,
            "data": paginated_history,
            "total": total,
            "page": page,
            "page_size": page_size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get execution history for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/stats")
async def get_task_stats(task_id: str):
    """获取任务统计信息"""
    try:
        logger.info(f"Getting stats for task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取任务关联的URL数量
        url_ids = await task_storage.get_task_urls(task_id)
        total_urls = len(url_ids)

        # 正确统计URL状态
        active_urls = 0
        disabled_urls = 0
        error_urls = 0

        if url_ids:
            try:
                import redis.asyncio as redis
                redis_client = redis.from_url("redis://redis:6379/0")

                # 遍历每个URL，获取其实际状态
                for url_id in url_ids:
                    url_key = f"url_pool:items:{url_id}"
                    url_data = await redis_client.hgetall(url_key)

                    if url_data:
                        # 处理bytes类型的状态值
                        status = url_data.get(b'status', b'active')
                        if isinstance(status, bytes):
                            status = status.decode()

                        if status == 'active':
                            active_urls += 1
                        elif status == 'disabled':
                            disabled_urls += 1
                        elif status == 'error':
                            error_urls += 1
                        else:
                            # 默认状态为active
                            active_urls += 1
                    else:
                        # 如果URL数据不存在，可能是错误状态
                        error_urls += 1

                await redis_client.close()

            except Exception as e:
                logger.warning(f"Failed to get URL status details for task {task_id}: {e}")
                # 如果获取状态失败，使用简化统计
                active_urls = total_urls
                disabled_urls = 0
                error_urls = 0

        # 获取真实的执行历史统计
        total_executions = 0
        successful_executions = 0
        failed_executions = 0
        avg_duration = 0

        try:
            import redis.asyncio as redis
            redis_client = redis.from_url("redis://redis:6379/0")

            # 获取任务的执行记录ID列表
            task_executions_key = f"task_execution:task:{task_id}:executions"
            execution_ids = await redis_client.lrange(task_executions_key, 0, -1)

            if execution_ids:
                total_executions = len(execution_ids)
                durations = []

                # 遍历每个执行记录
                for execution_id_bytes in execution_ids:
                    execution_id = execution_id_bytes.decode() if isinstance(execution_id_bytes, bytes) else execution_id_bytes
                    execution_key = f"task_execution:history:{execution_id}"
                    execution_data = await redis_client.get(execution_key)

                    if execution_data:
                        try:
                            execution_record = json.loads(execution_data)
                            status = execution_record.get("status", "unknown")

                            # 统计状态
                            if status in ["completed", "success"]:
                                successful_executions += 1
                            elif status in ["failed", "error", "cancelled"]:
                                failed_executions += 1

                            # 计算执行时间
                            started_at = execution_record.get("started_at")
                            completed_at = execution_record.get("completed_at")

                            if started_at and completed_at:
                                try:
                                    from datetime import datetime
                                    start_dt = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                                    end_dt = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
                                    duration = (end_dt - start_dt).total_seconds()
                                    durations.append(duration)
                                except Exception as e:
                                    logger.warning(f"Failed to parse execution time for {execution_id}: {e}")
                                    # 尝试使用execution_time_seconds字段
                                    exec_time = execution_record.get("execution_time_seconds")
                                    if exec_time and isinstance(exec_time, (int, float)):
                                        durations.append(exec_time)

                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse execution record {execution_id}: {e}")

                # 计算平均执行时间
                avg_duration = sum(durations) / len(durations) if durations else 0

            await redis_client.close()

        except Exception as e:
            logger.warning(f"Failed to get real execution history for task {task_id}: {e}")
            # 回退到临时存储（兼容性）
            history_records = task_execution_history.get(task_id, [])
            total_executions = len(history_records)
            successful_executions = len([h for h in history_records if h.get("status") == "success"])
            failed_executions = len([h for h in history_records if h.get("status") == "failed"])
            avg_duration = 0

        # 计算成功率
        success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0

        stats = {
            "url_stats": {
                "total_urls": total_urls,
                "active_urls": active_urls,
                "disabled_urls": disabled_urls,
                "error_urls": error_urls
            },
            "execution_stats": {
                "total_executions": total_executions,
                "successful_executions": successful_executions,
                "failed_executions": failed_executions,
                "success_rate": round(success_rate, 2),
                "avg_duration": round(avg_duration, 2)
            },
            "task_info": {
                "created_at": task_data.get("created_at"),
                "last_run": task_data.get("last_run"),
                "next_run": task_data.get("next_run"),
                "status": task_data.get("status"),
                "is_running": task_data.get("is_running", False)
            }
        }

        return {
            "success": True,
            "data": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get stats for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/execute")
async def execute_task_manually(task_id: str):
    """手动立即执行任务"""
    try:
        logger.info(f"Manually executing task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 首先检查Worker分配 - 强制要求Worker分配
        try:
            from ..services.task_assignment_service import task_assignment_service
            task_assignments = await task_assignment_service.list_assignments(task_id=task_id)

            if not task_assignments:
                raise HTTPException(
                    status_code=400,
                    detail=f"任务 {task_id} 没有分配Worker。请先在任务管理页面为任务分配爬虫Worker后再执行。"
                )

            # 使用第一个分配的Worker
            assignment = task_assignments[0]
            if not assignment.worker_ids:
                raise HTTPException(
                    status_code=400,
                    detail=f"任务 {task_id} 的Worker分配无效。请重新分配爬虫Worker。"
                )

            primary_worker_id = assignment.worker_ids[0]

            # 获取Worker的后端配置
            from ..services.crawler_worker_service import crawler_worker_service
            worker = await crawler_worker_service.get_worker(primary_worker_id)

            if not worker:
                raise HTTPException(
                    status_code=400,
                    detail=f"分配的Worker {primary_worker_id} 不存在。请重新分配有效的爬虫Worker。"
                )

            # 获取后端配置
            from ..services.backend_config_service import backend_config_service
            backend_config = await backend_config_service.get_config(worker.backend_config_id)

            if not backend_config:
                raise HTTPException(
                    status_code=400,
                    detail=f"Worker {worker.worker_name} 的后端配置不存在。请检查Worker配置。"
                )

            # 准备Worker后端配置
            worker_backend_config = {
                "name": f"{worker.worker_name}",
                "base_url": backend_config.api_endpoint,
                "timeout": backend_config.timeout / 1000,  # 转换为秒
                "max_retries": backend_config.max_retries,
                "auth_type": backend_config.auth_config.auth_type,
                "api_key": backend_config.auth_config.api_key
            }

            logger.info(f"Worker validation passed: {worker.worker_name} ({backend_config.api_endpoint})")

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"Failed to validate worker assignment for task {task_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"验证任务Worker分配失败: {str(e)}"
            )

        # 然后检查任务状态
        is_running = task_data.get("is_running", False)
        if isinstance(is_running, str):
            is_running = is_running.lower() == "true"

        if is_running:
            raise HTTPException(status_code=400, detail="Task is already running")

        # 获取任务关联的活跃URL
        url_ids = await task_storage.get_task_urls(task_id)
        task_urls = []

        if url_ids:
            try:
                import redis.asyncio as redis
                import os
                redis_url = os.getenv('REDIS_URL', 'redis://redis:6379/0')
                redis_client = redis.from_url(redis_url)

                # 获取活跃的URL
                for url_id in url_ids:
                    url_key = f"url_pool:items:{url_id}"
                    url_data = await redis_client.hgetall(url_key)

                    if url_data:
                        url_status = url_data.get(b'status', b'').decode() if isinstance(url_data.get(b'status'), bytes) else url_data.get('status', '')
                        if url_status == 'active':
                            url_str = url_data.get(b'url', b'').decode() if isinstance(url_data.get(b'url'), bytes) else url_data.get('url', '')
                            task_urls.append({"id": url_id, "url": url_str})

                await redis_client.close()
            except Exception as e:
                logger.error(f"Error getting URL details from Redis: {e}")
                # 使用基础URL信息
                task_urls = [{"id": url_id, "url": f"URL {url_id}"} for url_id in url_ids]

        if not task_urls:
            raise HTTPException(status_code=400, detail="No active URLs found for this task")

        # 更新任务状态为运行中
        now = get_local_isoformat()

        updates = {
            "is_running": True,
            "last_run": now,
            "updated_at": now
        }

        success = await task_storage.update_task(task_id, updates)
        if not success:
            logger.error(f"Failed to update task {task_id} status to running")

        # 创建执行记录
        execution_id = f"exec_{task_id}_{int(datetime.now().timestamp())}"
        execution_record = {
            "id": execution_id,
            "task_id": task_id,
            "start_time": now,
            "end_time": None,
            "status": "running",
            "trigger": "manual",
            "url_count": len(task_urls),
            "processed_count": 0,
            "success_count": 0,
            "error_count": 0,
            "duration": 0,
            "error_message": None
        }

        if task_id not in task_execution_history:
            task_execution_history[task_id] = []
        task_execution_history[task_id].append(execution_record)

        # 调用任务管理器执行任务
        try:
            from ..api.task_routes import get_task_manager
            task_manager = await get_task_manager()

            # 准备URL列表
            urls = [url_info["url"] for url_info in task_urls]

            # 分配URL到Worker
            from ..services.simple_worker_assignment import get_worker_assignment_service
            worker_service = await get_worker_assignment_service()
            assignments = await worker_service.assign_urls_to_workers(task_id, urls)

            logger.info(f"Assigned URLs to workers: {len(assignments)} workers for {len(urls)} URLs")

            # 获取任务配置
            platform = task_data.get("platform", "mercadolibre")
            options = task_data.get("options", {})

            # 使用之前验证的Worker配置
            options["crawler_backend"] = worker_backend_config

            # 提交任务到任务管理器
            # 确保options中包含监控任务UUID
            if not options:
                options = {}
            options["monitoring_task_id"] = task_id  # 保存原始监控任务UUID

            submission_id = await task_manager.submit_task(
                task_id=task_id,  # 直接使用监控任务UUID，不要转换
                urls=urls,
                platform=platform,
                priority=TaskPriority.HIGH,  # 手动执行使用高优先级
                options=options
            )

            logger.info(f"Task submitted to TaskManager: {task_id}, submission_id: {submission_id}")

            # 更新执行记录，添加submission_id和worker分配信息
            execution_record["submission_id"] = submission_id
            execution_record["worker_assignments"] = assignments

        except Exception as e:
            logger.error(f"Failed to submit task to TaskManager: {e}")
            # 如果任务管理器提交失败，更新执行记录状态
            execution_record["status"] = "failed"
            execution_record["error_message"] = f"Failed to submit to TaskManager: {str(e)}"
            execution_record["end_time"] = get_local_isoformat()

            # 恢复任务状态
            await task_storage.update_task(task_id, {"is_running": False})

            raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")

        logger.info(f"Task execution started: {task_id}, execution_id: {execution_id}")
        return {
            "success": True,
            "execution_id": execution_id,
            "submission_id": submission_id,
            "message": f"Task {task_id} execution started manually",
            "url_count": len(task_urls)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to execute task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/terminate")
async def terminate_task_execution(task_id: str):
    """终止正在执行的任务"""
    try:
        logger.info(f"Terminating task execution: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 检查任务是否正在运行
        is_running = task_data.get("is_running", False)
        if isinstance(is_running, str):
            is_running = is_running.lower() == "true"

        if not is_running:
            raise HTTPException(status_code=400, detail="Task is not running")

        # 更新任务状态
        now = get_local_isoformat()

        updates = {
            "is_running": False,
            "updated_at": now
        }

        success = await task_storage.update_task(task_id, updates)
        if not success:
            logger.error(f"Failed to update task {task_id} status")

        # 更新最新的执行记录
        history_records = task_execution_history.get(task_id, [])
        if history_records:
            latest_execution = history_records[-1]
            if latest_execution.get("status") == "running":
                latest_execution["status"] = "terminated"
                latest_execution["end_time"] = now
                latest_execution["error_message"] = "Task terminated manually"

        # TODO: 这里应该调用实际的任务终止逻辑

        logger.info(f"Task execution terminated: {task_id}")
        return {
            "success": True,
            "message": f"Task {task_id} execution terminated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to terminate task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/restart")
async def restart_failed_task(task_id: str):
    """重启失败的任务"""
    try:
        logger.info(f"Restarting failed task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 检查任务是否存在
        task_exists = await task_storage.task_exists(task_id)

        if not task_exists:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 检查任务状态
        is_running = task_data.get("is_running", False)
        if isinstance(is_running, str):
            is_running = is_running.lower() == "true"

        if is_running:
            raise HTTPException(status_code=400, detail="Task is already running")

        # 检查是否有失败的执行记录
        history_records = task_execution_history.get(task_id, [])
        has_failed_execution = any(h.get("status") in ["failed", "terminated"] for h in history_records)

        # 如果没有失败记录，也允许重启（可能是首次执行）
        if not has_failed_execution and history_records:
            # 如果有执行记录但都不是失败的，提示用户
            raise HTTPException(status_code=400, detail="No failed executions found for this task")

        # 重启任务（实际上是重新执行）
        return await execute_task_manually(task_id)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to restart task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/assignments")
async def get_task_worker_assignments(task_id: str):
    """获取任务的Worker分配信息"""
    try:
        logger.info(f"Getting worker assignments for task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 检查任务是否存在
        task_exists = await task_storage.task_exists(task_id)
        if not task_exists:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取Worker分配信息
        from ..services.simple_worker_assignment import get_worker_assignment_service
        worker_service = await get_worker_assignment_service()
        assignments = await worker_service.get_task_assignments(task_id)

        return {
            "success": True,
            "data": assignments
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get worker assignments for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/execution-status")
async def get_task_execution_status(task_id: str):
    """获取任务执行状态"""
    try:
        logger.info(f"Getting execution status for task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 检查任务是否存在
        task_exists = await task_storage.task_exists(task_id)

        if not task_exists:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 获取最新的执行记录
        history_records = task_execution_history.get(task_id, [])
        latest_execution = None
        if history_records:
            latest_execution = max(history_records, key=lambda x: x.get("start_time", ""))

        status_info = {
            "task_id": task_id,
            "is_running": task_data.get("is_running", False) == True or task_data.get("is_running") == "true",
            "status": task_data.get("status"),
            "last_run": task_data.get("last_run"),
            "next_run": task_data.get("next_run"),
            "current_execution": latest_execution if latest_execution and latest_execution.get("status") == "running" else None,
            "last_execution": latest_execution if latest_execution and latest_execution.get("status") != "running" else None
        }

        return {
            "success": True,
            "data": status_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get execution status for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== URL管理API ====================

class UrlBatchStatusRequest(BaseModel):
    """批量URL状态操作请求"""
    url_ids: List[str] = Field(..., description="URL ID列表")
    status: str = Field(..., description="目标状态: active 或 disabled")

class UrlStatusRequest(BaseModel):
    """单个URL状态操作请求"""
    status: str = Field(..., description="目标状态: active 或 disabled")

class UrlRemoveRequest(BaseModel):
    """URL移除请求"""
    url_ids: List[str] = Field(..., description="要移除的URL ID列表")

class UrlOperationResponse(BaseModel):
    """URL操作响应"""
    success: bool
    message: str
    affected_count: int = 0
    failed_urls: List[str] = []


@router.put("/{task_id}/urls/batch-status")
async def update_urls_batch_status(task_id: str, request: UrlBatchStatusRequest):
    """批量更新任务中URL的状态"""
    try:
        logger.info(f"Batch updating URL status for task {task_id}: {len(request.url_ids)} URLs to {request.status}")

        # 验证状态值
        if request.status not in ['active', 'disabled']:
            raise HTTPException(status_code=400, detail="Status must be 'active' or 'disabled'")

        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 使用TaskStorageService检查任务是否存在
            from ..services.task_storage_service import get_task_storage
            task_storage = await get_task_storage()

            task_exists = await task_storage.task_exists(task_id)
            if not task_exists:
                raise HTTPException(status_code=404, detail="Task not found")

            # 获取任务关联的URL
            task_url_ids = await task_storage.get_task_urls(task_id)
            task_url_ids_str = set(task_url_ids)

            affected_count = 0
            failed_urls = []

            # 批量更新URL状态
            for url_id in request.url_ids:
                # 检查URL是否属于该任务
                if url_id not in task_url_ids_str:
                    failed_urls.append(f"URL {url_id} not found in task")
                    continue

                # 更新URL状态
                url_key = f"url_pool:items:{url_id}"
                url_exists = await redis_client.exists(url_key)

                if url_exists:
                    await redis_client.hset(url_key, "status", request.status)
                    affected_count += 1
                    logger.info(f"Updated URL {url_id} status to {request.status}")
                else:
                    failed_urls.append(f"URL {url_id} not found in pool")

            # 更新任务的更新时间
            await task_storage.update_task(task_id, {"updated_at": get_local_isoformat()})

            return UrlOperationResponse(
                success=True,
                message=f"Successfully updated {affected_count} URLs to {request.status}",
                affected_count=affected_count,
                failed_urls=failed_urls
            )

        finally:
            await redis_client.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to batch update URL status for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{task_id}/urls/{url_id}/status")
async def update_url_status(task_id: str, url_id: str, request: UrlStatusRequest):
    """更新任务中单个URL的状态"""
    try:
        logger.info(f"Updating URL {url_id} status to {request.status} for task {task_id}")

        # 验证状态值
        if request.status not in ['active', 'disabled']:
            raise HTTPException(status_code=400, detail="Status must be 'active' or 'disabled'")

        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 使用TaskStorageService检查任务是否存在
            from ..services.task_storage_service import get_task_storage
            task_storage = await get_task_storage()

            task_exists = await task_storage.task_exists(task_id)
            if not task_exists:
                raise HTTPException(status_code=404, detail="Task not found")

            # 检查URL是否属于该任务（使用新的分层结构）
            task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            is_task_url = await redis_client.sismember(task_urls_key, url_id)
            if not is_task_url:
                raise HTTPException(status_code=404, detail="URL not found in task")

            # 检查URL是否存在
            url_key = f"url_pool:items:{url_id}"
            url_exists = await redis_client.exists(url_key)
            if not url_exists:
                raise HTTPException(status_code=404, detail="URL not found in pool")

            # 更新URL状态
            await redis_client.hset(url_key, "status", request.status)

            # 使用TaskStorageService更新任务的更新时间
            await task_storage.update_task(task_id, {"updated_at": get_local_isoformat()})

            logger.info(f"Successfully updated URL {url_id} status to {request.status}")

            return UrlOperationResponse(
                success=True,
                message=f"Successfully updated URL status to {request.status}",
                affected_count=1
            )

        finally:
            await redis_client.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update URL {url_id} status for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/urls/execution-status")
async def get_task_url_execution_status(task_id: str):
    """获取任务中所有URL的执行状态"""
    try:
        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 获取任务级别的URL状态索引
            task_url_status_key = f"task_url_status:{task_id}"
            url_statuses = await redis_client.hgetall(task_url_status_key)

            # 获取每个URL的详细执行状态
            url_execution_details = {}

            for url_bytes, status_bytes in url_statuses.items():
                url = url_bytes.decode() if isinstance(url_bytes, bytes) else url_bytes
                status = status_bytes.decode() if isinstance(status_bytes, bytes) else status_bytes

                # 获取URL的详细执行信息
                url_execution_key = f"url_execution:{task_id}:{url}"
                execution_data = await redis_client.get(url_execution_key)

                if execution_data:
                    try:
                        execution_info = json.loads(execution_data)
                        url_execution_details[url] = execution_info
                    except json.JSONDecodeError:
                        url_execution_details[url] = {
                            "url": url,
                            "status": status,
                            "error": "Failed to parse execution data"
                        }
                else:
                    # 如果没有详细信息，返回基本状态
                    url_execution_details[url] = {
                        "url": url,
                        "status": status,
                        "updated_at": datetime.now().isoformat()
                    }

            return {
                "task_id": task_id,
                "url_count": len(url_execution_details),
                "url_executions": url_execution_details,
                "summary": {
                    "total": len(url_execution_details),
                    "processing": sum(1 for details in url_execution_details.values() if details.get("status") == "processing"),
                    "completed": sum(1 for details in url_execution_details.values() if details.get("status") == "completed"),
                    "failed": sum(1 for details in url_execution_details.values() if details.get("status") == "failed")
                }
            }

        finally:
            await redis_client.close()

    except Exception as e:
        logger.error(f"Failed to get URL execution status for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/urls/{url}/execution-status")
async def get_url_execution_status(task_id: str, url: str):
    """获取单个URL的详细执行状态"""
    try:
        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 获取URL的详细执行信息
            url_execution_key = f"url_execution:{task_id}:{url}"
            execution_data = await redis_client.get(url_execution_key)

            if execution_data:
                try:
                    execution_info = json.loads(execution_data)
                    return execution_info
                except json.JSONDecodeError:
                    raise HTTPException(status_code=500, detail="Failed to parse execution data")
            else:
                raise HTTPException(status_code=404, detail="URL execution status not found")

        finally:
            await redis_client.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get URL execution status for {task_id}:{url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}/urls/batch")
async def remove_urls_batch(task_id: str, request: UrlRemoveRequest):
    """批量从任务中移除URL"""
    try:
        logger.info(f"Batch removing URLs from task {task_id}: {len(request.url_ids)} URLs")

        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 使用TaskStorageService检查任务是否存在
            from ..services.task_storage_service import get_task_storage
            task_storage = await get_task_storage()

            task_exists = await task_storage.task_exists(task_id)
            if not task_exists:
                raise HTTPException(status_code=404, detail="Task not found")

            # 获取任务关联的URL（使用新的分层结构）
            task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            task_url_ids = await redis_client.smembers(task_urls_key)
            task_url_ids_str = {url_id.decode() if isinstance(url_id, bytes) else url_id for url_id in task_url_ids}

            affected_count = 0
            failed_urls = []

            # 批量移除URL
            for url_id in request.url_ids:
                # 检查URL是否属于该任务
                if url_id not in task_url_ids_str:
                    failed_urls.append(f"URL {url_id} not found in task")
                    continue

                # 从任务中移除URL关联
                await redis_client.srem(task_urls_key, url_id)
                affected_count += 1
                logger.info(f"Removed URL {url_id} from task {task_id}")

            # 使用TaskStorageService更新任务信息
            if affected_count > 0:
                # 更新任务的更新时间
                await task_storage.update_task(task_id, {"updated_at": get_local_isoformat()})

            return UrlOperationResponse(
                success=True,
                message=f"Successfully removed {affected_count} URLs from task",
                affected_count=affected_count,
                failed_urls=failed_urls
            )

        finally:
            await redis_client.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to batch remove URLs from task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}/urls/{url_id}")
async def remove_url(task_id: str, url_id: str):
    """从任务中移除单个URL"""
    try:
        logger.info(f"Removing URL {url_id} from task {task_id}")

        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 使用TaskStorageService检查任务是否存在
            from ..services.task_storage_service import get_task_storage
            task_storage = await get_task_storage()

            task_exists = await task_storage.task_exists(task_id)
            if not task_exists:
                raise HTTPException(status_code=404, detail="Task not found")

            # 检查URL是否属于该任务（使用新的分层结构）
            task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            is_task_url = await redis_client.sismember(task_urls_key, url_id)
            if not is_task_url:
                raise HTTPException(status_code=404, detail="URL not found in task")

            # 从任务中移除URL关联
            await redis_client.srem(task_urls_key, url_id)

            # 使用TaskStorageService更新任务的更新时间
            await task_storage.update_task(task_id, {"updated_at": get_local_isoformat()})

            logger.info(f"Successfully removed URL {url_id} from task {task_id}")

            return UrlOperationResponse(
                success=True,
                message="Successfully removed URL from task",
                affected_count=1
            )

        finally:
            await redis_client.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove URL {url_id} from task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


class UrlAddRequest(BaseModel):
    """添加URL到任务请求"""
    url_ids: List[str] = Field(..., description="要添加的URL ID列表")


@router.post("/{task_id}/urls/add")
async def add_urls_to_task(task_id: str, request: UrlAddRequest):
    """从URL池添加URL到任务"""
    try:
        logger.info(f"Adding URLs to task {task_id}: {len(request.url_ids)} URLs")

        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        try:
            # 使用TaskStorageService检查任务是否存在
            from ..services.task_storage_service import get_task_storage
            task_storage = await get_task_storage()

            task_exists = await task_storage.task_exists(task_id)
            if not task_exists:
                raise HTTPException(status_code=404, detail="Task not found")

            # 获取任务当前关联的URL（使用新的分层结构）
            task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            current_url_ids = await redis_client.smembers(task_urls_key)
            current_url_ids_str = {url_id.decode() if isinstance(url_id, bytes) else url_id for url_id in current_url_ids}

            added_count = 0
            failed_urls = []
            skipped_urls = []

            # 添加URL到任务
            for url_id in request.url_ids:
                # 检查URL是否已经在任务中
                if url_id in current_url_ids_str:
                    skipped_urls.append(f"URL {url_id} already in task")
                    continue

                # 检查URL是否存在于URL池中
                url_key = f"url_pool:items:{url_id}"
                url_exists = await redis_client.exists(url_key)

                if url_exists:
                    # 添加URL到任务
                    await redis_client.sadd(task_urls_key, url_id)
                    added_count += 1
                    logger.info(f"Added URL {url_id} to task {task_id}")
                else:
                    failed_urls.append(f"URL {url_id} not found in pool")

            # 使用TaskStorageService更新任务信息
            if added_count > 0:
                # 更新任务的更新时间
                await task_storage.update_task(task_id, {"updated_at": get_local_isoformat()})

            # 构建响应消息
            message_parts = []
            if added_count > 0:
                message_parts.append(f"Successfully added {added_count} URLs")
            if skipped_urls:
                message_parts.append(f"Skipped {len(skipped_urls)} existing URLs")
            if failed_urls:
                message_parts.append(f"Failed to add {len(failed_urls)} URLs")

            return UrlOperationResponse(
                success=True,
                message="; ".join(message_parts) if message_parts else "No URLs processed",
                affected_count=added_count,
                failed_urls=failed_urls + skipped_urls
            )

        finally:
            await redis_client.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add URLs to task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/execute")
async def execute_monitoring_task_now(task_id: str):
    """立即执行监控任务"""
    try:
        logger.info(f"Executing monitoring task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 获取任务数据
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="Monitoring task not found")

        # 检查任务状态
        if task_data.get("status") != TaskStatus.ACTIVE.value:
            raise HTTPException(status_code=400, detail="Task is not active")

        # 检查是否正在运行
        if task_data.get("is_running", False):
            raise HTTPException(status_code=400, detail="Task is already running")

        # 使用新的监控任务执行器
        from ..tasks.monitoring_executor import execute_monitoring_task

        # 异步执行Celery任务
        celery_task = execute_monitoring_task.delay(task_id)

        logger.info(f"Monitoring task submitted to Celery: {task_id}, celery_task_id: {celery_task.id}")
        return {
            "success": True,
            "message": f"Monitoring task {task_id} submitted for execution",
            "celery_task_id": celery_task.id,
            "status": "submitted"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to execute monitoring task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))