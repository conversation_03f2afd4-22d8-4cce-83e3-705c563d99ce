# 任务重试系统指南

## 概述

MonIt任务调度系统现在包含了一个完整的重试和失败处理机制，能够智能地处理任务失败、自动重试和队列管理。

## 🎯 核心功能

### 1. **智能失败类型检测**
系统能够自动识别不同类型的失败原因：

- **网络错误** (`NETWORK_ERROR`): 连接超时、网络不可达
- **超时错误** (`TIMEOUT_ERROR`): 请求超时
- **API限制错误** (`API_LIMIT_ERROR`): 速率限制、429错误
- **解析错误** (`PARSE_ERROR`): JSON解析失败、数据格式错误
- **未知错误** (`UNKNOWN_ERROR`): 其他未分类错误

### 2. **多种重试策略**
根据失败类型采用不同的重试策略：

- **立即重试** (`IMMEDIATE`): 立即重新执行
- **指数退避** (`EXPONENTIAL_BACKOFF`): 延迟时间指数增长
- **线性退避** (`LINEAR_BACKOFF`): 延迟时间线性增长
- **固定延迟** (`FIXED_DELAY`): 固定时间间隔重试
- **不重试** (`NO_RETRY`): 直接放弃

### 3. **失败任务队列管理**
- **自动移动到队列尾端**: 失败的任务会被重新调度到队列末尾
- **优先级降级**: 重试任务的优先级会自动降低
- **死信队列**: 超过最大重试次数的任务进入死信队列

## 🔧 配置说明

### 重试配置 (RetryConfig)

```python
from app.core.retry_manager import RetryConfig, FailureType, RetryStrategy

retry_config = RetryConfig(
    max_retries=3,                    # 最大重试次数
    base_delay=60.0,                  # 基础延迟时间（秒）
    max_delay=3600.0,                 # 最大延迟时间（秒）
    backoff_multiplier=2.0,           # 退避倍数
    jitter=True,                      # 是否添加随机抖动
    
    # 自定义错误策略
    error_strategies={
        FailureType.NETWORK_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
        FailureType.API_LIMIT_ERROR: RetryStrategy.FIXED_DELAY,
        FailureType.PARSE_ERROR: RetryStrategy.NO_RETRY,
    }
)
```

### 任务管理器集成

```python
from app.core.task_manager import TaskManager, TaskManagerConfig

manager_config = TaskManagerConfig(
    retry_config=retry_config,        # 重试配置
    retry_failed_interval=300,        # 重试检查间隔（秒）
    # ... 其他配置
)

task_manager = TaskManager(manager_config)
```

## 📊 重试流程

### 1. 任务失败检测
```
任务执行失败 → 错误分析 → 失败类型识别 → 重试策略选择
```

### 2. 重试决策流程
```python
if retry_count < max_retries:
    if failure_type in retry_strategies:
        strategy = retry_strategies[failure_type]
        if strategy != NO_RETRY:
            calculate_delay() → schedule_retry() → move_to_queue_end()
        else:
            move_to_dead_letter_queue()
    else:
        move_to_dead_letter_queue()
else:
    move_to_dead_letter_queue()
```

### 3. 重试执行
```
重试时间到达 → 创建新批次 → 降低优先级 → 添加到队列尾端 → 等待执行
```

## 🚀 使用示例

### 基本使用

```python
import asyncio
from app.core.task_manager import TaskManager
from app.core.task_splitter import TaskPriority

async def main():
    # 创建任务管理器（包含重试功能）
    manager = TaskManager()
    await manager.start()
    
    # 提交任务
    urls = ["https://example.com/product/1", "https://example.com/product/2"]
    submission_id = await manager.submit_task(
        task_id=1,
        urls=urls,
        platform="mercadolibre",
        priority=TaskPriority.NORMAL
    )
    
    # 系统会自动处理失败和重试
    # 无需额外代码
    
    await manager.stop()
```

### 监控重试状态

```python
# 获取重试统计信息
status = manager.get_status()
retry_stats = status['retry_manager']

print(f"总失败次数: {retry_stats['total_failures']}")
print(f"总重试次数: {retry_stats['total_retries']}")
print(f"成功重试次数: {retry_stats['successful_retries']}")
print(f"放弃任务数: {retry_stats['abandoned_tasks']}")
print(f"重试队列大小: {retry_stats['retry_queue_size']}")
print(f"死信队列大小: {retry_stats['dead_letter_queue_size']}")
```

## 📈 重试策略详解

### 指数退避 (Exponential Backoff)
```
重试1: 60秒
重试2: 120秒  (60 × 2¹)
重试3: 240秒  (60 × 2²)
重试4: 480秒  (60 × 2³)
```

### 线性退避 (Linear Backoff)
```
重试1: 60秒
重试2: 120秒  (60 × 2)
重试3: 180秒  (60 × 3)
重试4: 240秒  (60 × 4)
```

### 固定延迟 (Fixed Delay)
```
重试1: 60秒
重试2: 60秒
重试3: 60秒
重试4: 60秒
```

## 🔍 监控和调试

### 日志信息
重试系统会产生详细的日志信息：

```
INFO - Scheduled retry for task batch_123 (attempt 2/3) in 120.0 seconds
WARNING - Batch batch_456 failed, scheduled for retry: Connection timeout
ERROR - Batch batch_789 failed permanently: Max retries exceeded
WARNING - Task batch_abc moved to dead letter queue after 3 retries
```

### 统计指标
```python
{
    "total_failures": 45,           # 总失败次数
    "total_retries": 32,            # 总重试次数
    "successful_retries": 28,       # 成功重试次数
    "abandoned_tasks": 4,           # 放弃任务数
    "failure_types": {              # 失败类型分布
        "network_error": 20,
        "timeout_error": 15,
        "api_limit_error": 8,
        "parse_error": 2
    },
    "retry_queue_size": 5,          # 重试队列大小
    "dead_letter_queue_size": 4     # 死信队列大小
}
```

## ⚙️ 高级配置

### 自定义重试策略

```python
# 为特定错误类型设置不同策略
custom_strategies = {
    FailureType.NETWORK_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
    FailureType.TIMEOUT_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
    FailureType.API_LIMIT_ERROR: RetryStrategy.FIXED_DELAY,
    FailureType.PARSE_ERROR: RetryStrategy.NO_RETRY,  # 解析错误不重试
    FailureType.UNKNOWN_ERROR: RetryStrategy.LINEAR_BACKOFF
}

retry_config = RetryConfig(
    max_retries=5,                    # 增加重试次数
    base_delay=30.0,                  # 减少基础延迟
    max_delay=1800.0,                 # 30分钟最大延迟
    error_strategies=custom_strategies
)
```

### 优先级降级规则

```python
# 重试时优先级自动降级
URGENT → HIGH → NORMAL → LOW
HIGH → NORMAL → LOW
NORMAL → LOW
LOW → LOW (不再降级)
```

## 🧪 测试重试系统

```bash
# 运行重试系统测试
python backend/tests/test_retry_system.py

# 测试内容包括：
# - 失败类型检测
# - 重试延迟计算
# - 优先级降级
# - 重试管理器功能
```

## 🚨 注意事项

### 1. **资源管理**
- 重试任务会占用额外的队列空间
- 长时间的重试延迟可能影响系统响应
- 建议定期清理死信队列

### 2. **外部API限制**
- 重试时仍需遵守API限制（2并发/100批次）
- API限制错误建议使用固定延迟策略
- 避免重试加剧API限制问题

### 3. **监控告警**
- 监控重试队列大小，避免无限增长
- 关注死信队列，及时处理无法恢复的任务
- 设置失败率告警阈值

## 📋 最佳实践

1. **合理设置重试次数**: 通常3-5次足够
2. **选择合适的延迟策略**: 网络错误用指数退避，API限制用固定延迟
3. **及时处理死信队列**: 定期检查和清理无法恢复的任务
4. **监控重试效果**: 关注成功重试率和系统性能影响
5. **调整重试参数**: 根据实际运行情况优化配置

---

重试系统为MonIt提供了强大的容错能力，确保在面对网络波动、API限制等问题时，系统能够自动恢复并继续处理任务。
