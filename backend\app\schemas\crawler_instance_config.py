"""
爬虫实例配置数据模型
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum

from ..api.v1.crawler_config import (
    BrowserConfig, CrawlerConfig, LLMConfig, SchemaExtractionConfig,
    ContentProcessingConfig, LinkFilteringConfig, SchedulerConfig, MonitorConfig
)


class AuthType(str, Enum):
    """认证类型枚举"""
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"


class AuthConfig(BaseModel):
    """认证配置"""
    auth_type: AuthType = Field(default=AuthType.NONE, description="认证类型")
    api_key: Optional[str] = Field(None, description="API密钥")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    token: Optional[str] = Field(None, description="Token")
    
    @validator('api_key')
    def validate_api_key(cls, v, values):
        """验证API密钥"""
        auth_type = values.get('auth_type')
        if auth_type in [AuthType.API_KEY, AuthType.BEARER_TOKEN] and not v:
            raise ValueError(f"认证类型为{auth_type}时，API密钥不能为空")
        return v
    
    @validator('username')
    def validate_username(cls, v, values):
        """验证用户名"""
        auth_type = values.get('auth_type')
        if auth_type == AuthType.BASIC_AUTH and not v:
            raise ValueError("基础认证时，用户名不能为空")
        return v


class InstanceStatus(str, Enum):
    """实例状态枚举"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    MAINTENANCE = "maintenance" # 维护中
    ERROR = "error"            # 错误状态


class CrawlerInstanceConfig(BaseModel):
    """单个爬虫实例的完整配置"""
    
    # 基本信息
    config_id: str = Field(description="配置唯一标识")
    config_name: str = Field(description="配置名称", max_length=100)
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    
    # 连接信息
    api_endpoint: str = Field(description="API端点地址")
    timeout: int = Field(default=30000, ge=1000, le=300000, description="超时时间(ms)")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")
    
    # 认证配置
    auth_config: AuthConfig = Field(description="认证配置")
    
    # 完整的爬虫配置
    browser: BrowserConfig = Field(description="浏览器配置")
    crawler: CrawlerConfig = Field(description="爬虫配置")
    llm: LLMConfig = Field(description="LLM配置")
    schema_extraction: SchemaExtractionConfig = Field(description="模式提取配置")
    content_processing: ContentProcessingConfig = Field(description="内容处理配置")
    link_filtering: LinkFilteringConfig = Field(description="链接过滤配置")
    scheduler: SchedulerConfig = Field(description="调度器配置")
    monitor: MonitorConfig = Field(description="监控配置")
    
    # 性能配置
    max_concurrent: int = Field(default=2, ge=1, le=500, description="最大并发数")
    weight: int = Field(default=1, ge=1, le=100, description="负载均衡权重")
    priority: int = Field(default=1, ge=1, le=10, description="优先级")
    
    # 状态信息
    status: InstanceStatus = Field(default=InstanceStatus.ACTIVE, description="实例状态")
    health_score: float = Field(default=1.0, ge=0.0, le=1.0, description="健康评分")
    last_check: Optional[datetime] = Field(None, description="最后检查时间")
    
    # 统计信息
    total_requests: int = Field(default=0, ge=0, description="总请求数")
    success_requests: int = Field(default=0, ge=0, description="成功请求数")
    failed_requests: int = Field(default=0, ge=0, description="失败请求数")
    avg_response_time: float = Field(default=0.0, ge=0.0, description="平均响应时间(ms)")
    
    # 时间戳
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    @validator('api_endpoint')
    def validate_api_endpoint(cls, v):
        """验证API端点"""
        if not v or not v.strip():
            raise ValueError("API端点不能为空")
        
        v = v.strip()
        if not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError("API端点必须以http://或https://开头")
        
        return v
    
    @validator('success_requests')
    def validate_success_requests(cls, v, values):
        """验证成功请求数不能超过总请求数"""
        if 'total_requests' in values and v > values['total_requests']:
            raise ValueError("成功请求数不能超过总请求数")
        return v
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.success_requests / self.total_requests
    
    @property
    def is_healthy(self) -> bool:
        """判断实例是否健康"""
        return (
            self.status == InstanceStatus.ACTIVE and 
            self.health_score >= 0.5
        )


class CrawlerInstanceConfigCreate(BaseModel):
    """创建爬虫实例配置的数据模型"""
    
    config_name: str = Field(description="配置名称", max_length=100)
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    api_endpoint: str = Field(description="API端点地址")
    timeout: int = Field(default=30000, ge=1000, le=300000)
    max_retries: int = Field(default=3, ge=0, le=10)
    auth_config: AuthConfig = Field(description="认证配置")
    browser: BrowserConfig
    crawler: CrawlerConfig
    llm: LLMConfig
    schema_extraction: SchemaExtractionConfig
    content_processing: ContentProcessingConfig
    link_filtering: LinkFilteringConfig
    scheduler: SchedulerConfig
    monitor: MonitorConfig
    max_concurrent: int = Field(default=2, ge=1, le=500)
    weight: int = Field(default=1, ge=1, le=100)
    priority: int = Field(default=1, ge=1, le=10)


class CrawlerInstanceConfigUpdate(BaseModel):
    """更新爬虫实例配置的数据模型"""
    
    config_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    api_endpoint: Optional[str] = None
    timeout: Optional[int] = Field(None, ge=1000, le=300000)
    max_retries: Optional[int] = Field(None, ge=0, le=10)
    auth_config: Optional[AuthConfig] = None
    browser: Optional[BrowserConfig] = None
    crawler: Optional[CrawlerConfig] = None
    llm: Optional[LLMConfig] = None
    schema_extraction: Optional[SchemaExtractionConfig] = None
    content_processing: Optional[ContentProcessingConfig] = None
    link_filtering: Optional[LinkFilteringConfig] = None
    scheduler: Optional[SchedulerConfig] = None
    monitor: Optional[MonitorConfig] = None
    max_concurrent: Optional[int] = Field(None, ge=1, le=20)
    weight: Optional[int] = Field(None, ge=1, le=100)
    priority: Optional[int] = Field(None, ge=1, le=10)
    status: Optional[InstanceStatus] = None


class CrawlerConfigManager(BaseModel):
    """爬虫配置管理器"""
    
    configs: List[CrawlerInstanceConfig] = Field(description="爬虫配置列表")
    default_config_id: Optional[str] = Field(None, description="默认配置ID")
    
    @validator('configs')
    def validate_configs(cls, v):
        """验证配置列表"""
        if not v:
            return v
        
        # 检查ID唯一性
        config_ids = [config.config_id for config in v]
        if len(config_ids) != len(set(config_ids)):
            raise ValueError("配置ID必须唯一")
        
        return v
    
    @validator('default_config_id')
    def validate_default_config_id(cls, v, values):
        """验证默认配置ID"""
        if v and 'configs' in values:
            config_ids = [config.config_id for config in values['configs']]
            if v not in config_ids:
                raise ValueError("默认配置ID必须存在于配置列表中")
        return v
    
    def get_config(self, config_id: str) -> Optional[CrawlerInstanceConfig]:
        """根据ID获取配置"""
        for config in self.configs:
            if config.config_id == config_id:
                return config
        return None
    
    def get_default_config(self) -> Optional[CrawlerInstanceConfig]:
        """获取默认配置"""
        if self.default_config_id:
            return self.get_config(self.default_config_id)
        elif self.configs:
            return self.configs[0]
        return None
    
    def get_active_configs(self) -> List[CrawlerInstanceConfig]:
        """获取活跃的配置列表"""
        return [config for config in self.configs if config.status == InstanceStatus.ACTIVE]
    
    def get_healthy_configs(self) -> List[CrawlerInstanceConfig]:
        """获取健康的配置列表"""
        return [config for config in self.configs if config.is_healthy]


class ConnectionTestResult(BaseModel):
    """连接测试结果"""
    
    config_id: str = Field(description="配置ID")
    is_connected: bool = Field(description="是否连接成功")
    response_time: float = Field(description="响应时间(ms)")
    error_message: Optional[str] = Field(None, description="错误信息")
    test_time: datetime = Field(description="测试时间")
    api_version: Optional[str] = Field(None, description="API版本")
    server_info: Optional[Dict[str, Any]] = Field(None, description="服务器信息")
