/**
 * 实时更新Hook - 简化版本，使用原有的WebSocket服务
 * 提供任务执行状态、Worker分配状态、URL处理进度的实时更新功能
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { wsService } from '../services/websocket';
import { WebSocketMessage } from '../types';

export interface TaskProgress {
  taskId: string;
  totalUrls: number;
  completedUrls: number;
  failedUrls: number;
  processingUrls: number;
  progress: number;
  estimatedTimeRemaining?: number;
  currentPhase?: string;
}

export interface WorkerStatus {
  workerId: string;
  status: 'online' | 'offline' | 'busy' | 'idle';
  currentLoad: number;
  maxConcurrent: number;
  assignedUrls: string[];
  performance: {
    successRate: number;
    avgResponseTime: number;
    tasksCompleted: number;
  };
}

export interface UrlProcessingStatus {
  urlId: string;
  url: string;
  status: 'pending' | 'assigned' | 'processing' | 'completed' | 'failed';
  workerId?: string;
  startTime?: string;
  endTime?: string;
  processingTime?: number;
  result?: any;
  error?: string;
}

export interface RealTimeState {
  taskProgress: Record<string, TaskProgress>;
  workerStatus: Record<string, WorkerStatus>;
  urlStatus: Record<string, UrlProcessingStatus>;
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error';
  lastUpdate: string;
}

export interface UseRealTimeUpdatesOptions {
  taskId?: string;
  workerId?: string;
  autoConnect?: boolean;
  onTaskProgress?: (progress: TaskProgress) => void;
  onWorkerStatusChange?: (status: WorkerStatus) => void;
  onUrlStatusChange?: (status: UrlProcessingStatus) => void;
  onConnectionChange?: (status: string) => void;
}

export function useRealTimeUpdates(options: UseRealTimeUpdatesOptions = {}) {
  const {
    taskId,
    workerId,
    autoConnect = true,
    onTaskProgress,
    onWorkerStatusChange,
    onUrlStatusChange,
    onConnectionChange
  } = options;

  const [state, setState] = useState<RealTimeState>({
    taskProgress: {},
    workerStatus: {},
    urlStatus: {},
    connectionStatus: 'disconnected',
    lastUpdate: new Date().toISOString()
  });

  const unsubscribeRefs = useRef<(() => void)[]>([]);

  // 使用useRef存储回调函数，避免useEffect依赖问题
  const callbacksRef = useRef({
    onTaskProgress,
    onWorkerStatusChange,
    onUrlStatusChange,
    onConnectionChange
  });

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = {
      onTaskProgress,
      onWorkerStatusChange,
      onUrlStatusChange,
      onConnectionChange
    };
  });

  // 更新状态的辅助函数 - 使用useCallback确保稳定性
  const updateState = useCallback((updater: (prevState: RealTimeState) => RealTimeState) => {
    setState(prevState => {
      const newState = updater(prevState);
      return {
        ...newState,
        lastUpdate: new Date().toISOString()
      };
    });
  }, []);

  // 这些处理函数现在直接在消息处理器中内联处理

  // 连接状态监控
  useEffect(() => {
    const checkConnection = () => {
      const isConnected = wsService.isConnected();
      const status = isConnected ? 'connected' : 'disconnected';
      setState(prevState => ({
        ...prevState,
        connectionStatus: status as any
      }));
      callbacksRef.current.onConnectionChange?.(status);
    };

    // 初始检查
    checkConnection();

    // 定期检查连接状态
    const interval = setInterval(checkConnection, 1000);

    return () => clearInterval(interval);
  }, []); // 移除onConnectionChange依赖

  // 设置事件监听器
  useEffect(() => {
    if (!autoConnect) return;

    const setupSubscriptions = async () => {
      try {
        // 确保WebSocket连接
        await wsService.connect();

        // 清理之前的订阅
        unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
        unsubscribeRefs.current = [];

        // 订阅各种事件类型
        const messageHandler = (message: WebSocketMessage) => {
          const { type, data } = message;
          const callbacks = callbacksRef.current;

          switch (type) {
            case 'task_progress':
            case 'task_started':
            case 'task_completed':
            case 'task_failed':
              if (callbacks.onTaskProgress && data) {
                callbacks.onTaskProgress(data as TaskProgress);
                // 更新本地状态
                updateState(prevState => ({
                  ...prevState,
                  taskProgress: {
                    ...prevState.taskProgress,
                    [data.taskId]: data as TaskProgress
                  }
                }));
              }
              break;

            case 'worker_online':
            case 'worker_offline':
            case 'worker_load_changed':
              if (callbacks.onWorkerStatusChange && data) {
                callbacks.onWorkerStatusChange(data as WorkerStatus);
                // 更新本地状态
                updateState(prevState => ({
                  ...prevState,
                  workerStatus: {
                    ...prevState.workerStatus,
                    [data.workerId]: data as WorkerStatus
                  }
                }));
              }
              break;

            case 'url_assigned':
            case 'url_processing':
            case 'url_completed':
            case 'url_failed':
              if (callbacks.onUrlStatusChange && data) {
                callbacks.onUrlStatusChange(data as UrlProcessingStatus);
                // 更新本地状态
                updateState(prevState => ({
                  ...prevState,
                  urlStatus: {
                    ...prevState.urlStatus,
                    [data.urlId]: data as UrlProcessingStatus
                  }
                }));
              }
              break;
          }
        };

        // 订阅消息
        unsubscribeRefs.current.push(
          wsService.subscribe('task_progress', messageHandler),
          wsService.subscribe('task_started', messageHandler),
          wsService.subscribe('task_completed', messageHandler),
          wsService.subscribe('task_failed', messageHandler),
          wsService.subscribe('worker_online', messageHandler),
          wsService.subscribe('worker_offline', messageHandler),
          wsService.subscribe('worker_load_changed', messageHandler),
          wsService.subscribe('url_assigned', messageHandler),
          wsService.subscribe('url_processing', messageHandler),
          wsService.subscribe('url_completed', messageHandler),
          wsService.subscribe('url_failed', messageHandler)
        );

        // 如果指定了taskId，发送订阅消息
        if (taskId) {
          wsService.send({
            action: 'subscribe_task',
            task_id: taskId
          });
        }

        // 如果指定了workerId，发送订阅消息
        if (workerId) {
          wsService.send({
            action: 'subscribe_worker',
            worker_id: workerId
          });
        }

        setState(prevState => ({
          ...prevState,
          connectionStatus: 'connected'
        }));

      } catch (error) {
        console.error('Failed to setup WebSocket subscriptions:', error);
        setState(prevState => ({
          ...prevState,
          connectionStatus: 'error'
        }));
      }
    };

    setupSubscriptions();

    return () => {
      // 清理订阅
      unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
      unsubscribeRefs.current = [];
    };
  }, [
    autoConnect,
    taskId,
    workerId
  ]);

  // 手动连接函数
  const connect = useCallback(async () => {
    try {
      await wsService.connect();
      setState(prevState => ({
        ...prevState,
        connectionStatus: 'connected'
      }));
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setState(prevState => ({
        ...prevState,
        connectionStatus: 'error'
      }));
      throw error;
    }
  }, []);

  // 断开连接函数
  const disconnect = useCallback(() => {
    wsService.disconnect();
    unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
    unsubscribeRefs.current = [];
    setState(prevState => ({
      ...prevState,
      connectionStatus: 'disconnected'
    }));
  }, []);

  // 获取特定任务的进度
  const getTaskProgress = useCallback((taskId: string): TaskProgress | undefined => {
    return state.taskProgress[taskId];
  }, [state.taskProgress]);

  // 获取特定Worker的状态
  const getWorkerStatus = useCallback((workerId: string): WorkerStatus | undefined => {
    return state.workerStatus[workerId];
  }, [state.workerStatus]);

  // 获取特定URL的状态
  const getUrlStatus = useCallback((urlId: string): UrlProcessingStatus | undefined => {
    return state.urlStatus[urlId];
  }, [state.urlStatus]);

  return {
    // 状态
    ...state,

    // 方法
    connect,
    disconnect,

    // 计算属性
    isConnected: state.connectionStatus === 'connected',
    totalTasks: Object.keys(state.taskProgress).length,
    totalWorkers: Object.keys(state.workerStatus).length,
    totalUrls: Object.keys(state.urlStatus).length
  };
}
