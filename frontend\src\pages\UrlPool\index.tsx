import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,

  Modal,
  message,
  Tooltip,
  Statistic,
  Row,
  Col,
  Typography,
  Dropdown,
  Progress,
  Alert
} from 'antd';
import {
  FilterOutlined,
  ReloadOutlined,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  StopOutlined,
  EyeOutlined,
  MoreOutlined,
  PlusOutlined,
  RocketOutlined,
  RollbackOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  getUrlPool,
  getAllFilteredUrlIds,
  getUrlPoolStats,
  getSupportedPlatforms,
  getSourceFiles,
  batchEnableUrls,
  batchDisableUrls,
  batchDeleteUrls,
  restoreFromRecycleBin,
  permanentDeleteFromRecycleBin,
  getPlatformDisplayName,
  getStatusDisplay
} from '../../services/urlPoolApi';
import type {
  UrlPoolItem,
  UrlPoolQuery,
  UrlPoolStats,
  SourceFile
} from '../../types/urlPool';
import type { TaskCreateResponse } from '../../types/taskCreate';
import ExcelUpload from '../../components/ExcelUpload';
import UrlDetailModal from '../../components/UrlDetailModal';
import TaskCreateWizard from '../../components/TaskCreateWizard';

const { Title, Text } = Typography;
const { Option } = Select;

interface UrlPoolPageState {
  loading: boolean;
  data: UrlPoolItem[];
  total: number;
  selectedRowKeys: string[];
  selectedRows: UrlPoolItem[];
  query: UrlPoolQuery;
  stats: UrlPoolStats | null;
  platforms: string[];
  sourceFiles: SourceFile[];
  showUploadModal: boolean;
  showFilters: boolean;
  showDetailModal: boolean;
  showTaskCreateModal: boolean;
  selectedUrl: UrlPoolItem | null;
}

const UrlPoolPage: React.FC = () => {
  const navigate = useNavigate();
  const [state, setState] = useState<UrlPoolPageState>({
    loading: false,
    data: [],
    total: 0,
    selectedRowKeys: [],
    selectedRows: [],
    query: {
      page: 1,
      page_size: 20,
      sort_by: 'added_at',
      sort_order: 'desc'
    },
    stats: null,
    platforms: [],
    sourceFiles: [],
    showUploadModal: false,
    showFilters: false,
    showDetailModal: false,
    showTaskCreateModal: false,
    selectedUrl: null
  });

  // 加载数据
  const loadData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const [urlResponse, statsResponse] = await Promise.all([
        getUrlPool(state.query),
        getUrlPoolStats()
      ]);

      setState(prev => ({
        ...prev,
        data: urlResponse.data,
        total: urlResponse.total,
        stats: statsResponse.data,
        loading: false
      }));
    } catch (error: any) {
      message.error(`加载数据失败: ${error.message}`);
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [state.query]);

  // 加载基础数据
  const loadBaseData = useCallback(async () => {
    try {
      const [platformsResponse, sourceFilesResponse] = await Promise.all([
        getSupportedPlatforms(),
        getSourceFiles()
      ]);

      setState(prev => ({
        ...prev,
        platforms: platformsResponse.platforms,
        sourceFiles: sourceFilesResponse.source_files
      }));
    } catch (error: any) {
      console.error('加载基础数据失败:', error);
    }
  }, []);

  useEffect(() => {
    loadBaseData();
  }, [loadBaseData]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // 查询参数更新
  const updateQuery = (updates: Partial<UrlPoolQuery>) => {
    setState(prev => ({
      ...prev,
      query: { ...prev.query, ...updates, page: 1 }, // 重置到第一页
      selectedRowKeys: [],
      selectedRows: []
    }));
  };

  // 分页变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const updates: Partial<UrlPoolQuery> = {
      page: pagination.current,
      page_size: pagination.pageSize
    };

    if (sorter.field) {
      updates.sort_by = sorter.field;
      updates.sort_order = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    setState(prev => ({
      ...prev,
      query: { ...prev.query, ...updates }
    }));
  };

  // 行选择
  const rowSelection = {
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: UrlPoolItem[]) => {
      setState(prev => ({
        ...prev,
        selectedRowKeys: selectedRowKeys as string[],
        selectedRows
      }));
    },
    onSelectAll: (selected: boolean, selectedRows: UrlPoolItem[], changeRows: UrlPoolItem[]) => {
      console.log('Select all:', selected, selectedRows, changeRows);
    }
  };

  // 批量操作
  const handleBatchAction = async (action: 'enable' | 'disable' | 'delete') => {
    if (state.selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的URL');
      return;
    }

    const actionNames = {
      enable: '启用',
      disable: '禁用', 
      delete: '删除'
    };

    Modal.confirm({
      title: `确认${actionNames[action]}`,
      content: `确定要${actionNames[action]} ${state.selectedRowKeys.length} 个URL吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          let response;
          switch (action) {
            case 'enable':
              response = await batchEnableUrls(state.selectedRowKeys);
              break;
            case 'disable':
              response = await batchDisableUrls(state.selectedRowKeys);
              break;
            case 'delete':
              response = await batchDeleteUrls(state.selectedRowKeys);
              break;
          }

          message.success(response.message);
          setState(prev => ({
            ...prev,
            selectedRowKeys: [],
            selectedRows: []
          }));
          loadData();
        } catch (error: any) {
          message.error(`${actionNames[action]}失败: ${error.message}`);
        }
      }
    });
  };

  // 回收站恢复操作
  const handleRestoreAction = async () => {
    if (state.selectedRowKeys.length === 0) {
      message.warning('请先选择要恢复的URL');
      return;
    }

    Modal.confirm({
      title: '确认恢复',
      content: `确定要恢复 ${state.selectedRowKeys.length} 个URL吗？`,
      okText: '确认恢复',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await restoreFromRecycleBin(state.selectedRowKeys);
          message.success(response.message);
          setState(prev => ({
            ...prev,
            selectedRowKeys: [],
            selectedRows: []
          }));
          loadData();
        } catch (error: any) {
          message.error(`恢复失败: ${error.message}`);
        }
      }
    });
  };

  // 永久删除操作
  const handlePermanentDeleteAction = async () => {
    if (state.selectedRowKeys.length === 0) {
      message.warning('请先选择要永久删除的URL');
      return;
    }

    Modal.confirm({
      title: '确认永久删除',
      content: (
        <div>
          <p>确定要永久删除 {state.selectedRowKeys.length} 个URL吗？</p>
          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
            ⚠️ 此操作不可恢复！
          </p>
        </div>
      ),
      okText: '确认永久删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await permanentDeleteFromRecycleBin(state.selectedRowKeys);
          message.success(response.message);
          setState(prev => ({
            ...prev,
            selectedRowKeys: [],
            selectedRows: []
          }));
          loadData();
        } catch (error: any) {
          message.error(`永久删除失败: ${error.message}`);
        }
      }
    });
  };

  // 检查是否在回收站状态
  const isInRecycleBin = state.query.status === 'deleted';

  // 选择所有符合筛选条件的URL（纯前端功能）
  const handleSelectAllFiltered = async () => {
    try {
      // 获取所有符合筛选条件的URL ID
      const response = await getAllFilteredUrlIds({
        platform: state.query.platform,
        status: state.query.status,
        source_file: state.query.source_file,
        search: state.query.search,
        sort_by: state.query.sort_by,
        sort_order: state.query.sort_order
      });

      if (response.success) {
        // 只设置选中的ID，不设置selectedRows（因为我们没有所有URL的完整数据）
        setState(prev => ({
          ...prev,
          selectedRowKeys: response.data,
          selectedRows: [] // 清空selectedRows，因为我们只有ID
        }));

        message.success(`已选择所有符合筛选条件的 ${response.total} 个URL`);
      } else {
        message.error('获取筛选结果失败');
      }
    } catch (error: any) {
      message.error(`选择失败: ${error.message}`);
    }
  };

  // 清除所有选择
  const handleClearSelection = () => {
    setState(prev => ({
      ...prev,
      selectedRowKeys: [],
      selectedRows: []
    }));
  };

  // 表格列定义
  const columns: ColumnsType<UrlPoolItem> = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 400,
      ellipsis: {
        showTitle: false
      },
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code style={{ fontSize: 12 }}>
            {url.length > 60 ? `${url.substring(0, 60)}...` : url}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 120,
      filters: state.platforms.map(platform => ({
        text: getPlatformDisplayName(platform),
        value: platform
      })),
      render: (platform: string) => (
        <Tag color={platform === 'mercadolibre' ? 'gold' : platform === 'amazon' ? 'orange' : 'blue'}>
          {getPlatformDisplayName(platform)}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '活跃', value: 'active' },
        { text: '禁用', value: 'disabled' }
      ],
      render: (status: string) => {
        const { name, color } = getStatusDisplay(status);
        return <Tag color={color}>{name}</Tag>;
      }
    },
    {
      title: '来源文件',
      dataIndex: 'source_file',
      key: 'source_file',
      width: 150,
      ellipsis: true,
      filters: state.sourceFiles.slice(0, 10).map(file => ({
        text: `${file.name} (${file.url_count})`,
        value: file.name
      }))
    },
    {
      title: '添加时间',
      dataIndex: 'added_at',
      key: 'added_at',
      width: 150,
      sorter: true,
      render: (date: string) => new Date(date).toLocaleString('zh-CN')
    },
    {
      title: '检查统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: 12 }}>
            总计: {record.check_count}
          </Text>
          <Text style={{ fontSize: 12 }}>
            成功: {record.success_count}
          </Text>
          {record.check_count > 0 && (
            <Progress
              percent={Math.round((record.success_count / record.check_count) * 100)}
              size="small"
              showInfo={false}
            />
          )}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => setState(prev => ({
                ...prev,
                showDetailModal: true,
                selectedUrl: record
              }))}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: isInRecycleBin ? [
                // 回收站状态下的操作
                {
                  key: 'restore',
                  label: '恢复',
                  icon: <RollbackOutlined />,
                },
                { type: 'divider' },
                {
                  key: 'permanent-delete',
                  label: '永久删除',
                  icon: <ExclamationCircleOutlined />,
                  danger: true
                }
              ] : [
                // 正常状态下的操作
                {
                  key: 'enable',
                  label: '启用',
                  icon: <CheckCircleOutlined />,
                  disabled: record.status === 'active'
                },
                {
                  key: 'disable',
                  label: '禁用',
                  icon: <StopOutlined />,
                  disabled: record.status === 'disabled'
                },
                { type: 'divider' },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true
                }
              ],
              onClick: ({ key }) => {
                if (isInRecycleBin) {
                  // 回收站操作
                  setState(prev => ({
                    ...prev,
                    selectedRowKeys: [record.id],
                    selectedRows: [record]
                  }));
                  if (key === 'restore') {
                    handleRestoreAction();
                  } else if (key === 'permanent-delete') {
                    handlePermanentDeleteAction();
                  }
                } else {
                  // 正常操作
                  setState(prev => ({
                    ...prev,
                    selectedRowKeys: [record.id],
                    selectedRows: [record]
                  }));
                  if (key === 'delete') {
                    handleBatchAction('delete');
                  } else {
                    handleBatchAction(key as 'enable' | 'disable');
                  }
                }
              }
            }}
            trigger={['click']}
          >
            <Button type="text" size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      )
    }
  ];

  // 渲染统计卡片
  const renderStatsCards = () => {
    if (!state.stats) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="总URL数"
              value={state.stats.total_urls}
              prefix={<PlusOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="活跃URL"
              value={state.stats.active_urls}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="禁用URL"
              value={state.stats.disabled_urls}
              valueStyle={{ color: '#cf1322' }}
              prefix={<StopOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="平台数量"
              value={Object.keys(state.stats.platforms).length}
              prefix={<FilterOutlined />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>URL池管理</Title>
      
      {/* 统计卡片 */}
      {renderStatsCards()}

      {/* 主要内容卡片 */}
      <Card>
        {/* 工具栏 */}
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={() => setState(prev => ({ ...prev, showUploadModal: true }))}
                >
                  上传Excel
                </Button>
                <Button
                  type="default"
                  icon={<RocketOutlined />}
                  onClick={() => setState(prev => ({ ...prev, showTaskCreateModal: true }))}
                  disabled={state.selectedRowKeys.length === 0}
                >
                  创建任务 ({state.selectedRowKeys.length})
                </Button>
                <Button
                  icon={<DeleteOutlined />}
                  onClick={() => updateQuery({ status: 'deleted' })}
                >
                  回收站
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadData}
                  loading={state.loading}
                >
                  刷新
                </Button>
                <Button
                  icon={<FilterOutlined />}
                  onClick={() => setState(prev => ({ ...prev, showFilters: !prev.showFilters }))}
                >
                  筛选 {state.showFilters ? '收起' : '展开'}
                </Button>
              </Space>
            </Col>
            <Col>
              <Space>
                <Input.Search
                  placeholder="搜索URL..."
                  style={{ width: 250 }}
                  onSearch={(value) => updateQuery({ search: value || undefined })}
                  allowClear
                />
                <Button icon={<DownloadOutlined />}>
                  导出
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 筛选器 */}
        {state.showFilters && (
          <Card size="small" style={{ marginBottom: 16, backgroundColor: '#fafafa' }}>
            <Row gutter={16}>
              <Col span={4}>
                <Text strong>平台:</Text>
                <Select
                  style={{ width: '100%', marginTop: 4 }}
                  placeholder="选择平台"
                  allowClear
                  value={state.query.platform}
                  onChange={(value) => updateQuery({ platform: value })}
                >
                  <Option value="">全部平台</Option>
                  {state.platforms.map(platform => (
                    <Option key={platform} value={platform}>
                      {getPlatformDisplayName(platform)}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <Text strong>状态:</Text>
                <Select
                  style={{ width: '100%', marginTop: 4 }}
                  placeholder="选择状态"
                  allowClear
                  value={state.query.status}
                  onChange={(value) => updateQuery({ status: value })}
                >
                  <Option value="">全部状态</Option>
                  <Option value="active">活跃</Option>
                  <Option value="disabled">禁用</Option>
                  <Option value="deleted">已删除</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Text strong>来源文件:</Text>
                <Select
                  style={{ width: '100%', marginTop: 4 }}
                  placeholder="选择来源文件"
                  allowClear
                  value={state.query.source_file}
                  onChange={(value) => updateQuery({ source_file: value })}
                >
                  <Option value="">全部文件</Option>
                  {state.sourceFiles.map(file => (
                    <Option key={file.name} value={file.name}>
                      {file.name} ({file.url_count})
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Text strong>排序:</Text>
                <Select
                  style={{ width: '100%', marginTop: 4 }}
                  value={`${state.query.sort_by}_${state.query.sort_order}`}
                  onChange={(value) => {
                    const [sort_by, sort_order] = value.split('_');
                    updateQuery({ sort_by, sort_order: sort_order as 'asc' | 'desc' });
                  }}
                >
                  <Option value="added_at_desc">添加时间 (新到旧)</Option>
                  <Option value="added_at_asc">添加时间 (旧到新)</Option>
                  <Option value="check_count_desc">检查次数 (多到少)</Option>
                  <Option value="check_count_asc">检查次数 (少到多)</Option>
                </Select>
              </Col>
            </Row>
          </Card>
        )}

        {/* 选择操作栏 */}
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={handleSelectAllFiltered}
              disabled={state.loading || state.total === 0}
            >
              选择所有筛选结果
            </Button>
            <Button
              size="small"
              onClick={handleClearSelection}
              disabled={state.selectedRowKeys.length === 0}
            >
              清除选择
            </Button>
            <Text type="secondary">
              {state.total > 0 && `共 ${state.total} 个URL符合筛选条件`}
            </Text>
          </Space>
          <Space>
            <Text type="secondary">
              当前页: {state.data.length} 个URL
            </Text>
            {state.selectedRowKeys.length > 0 && (
              <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>
                已选择: {state.selectedRowKeys.length} 个URL
              </Text>
            )}
          </Space>
        </div>

        {/* 批量操作栏 */}
        {state.selectedRowKeys.length > 0 && (
          <Alert
            message={
              <Space>
                <Text>已选择 {state.selectedRowKeys.length} 个URL</Text>
                {isInRecycleBin ? (
                  // 回收站状态下的批量操作
                  <>
                    <Button
                      size="small"
                      type="primary"
                      icon={<RollbackOutlined />}
                      onClick={handleRestoreAction}
                    >
                      批量恢复
                    </Button>
                    <Button
                      size="small"
                      danger
                      icon={<ExclamationCircleOutlined />}
                      onClick={handlePermanentDeleteAction}
                    >
                      永久删除
                    </Button>
                  </>
                ) : (
                  // 正常状态下的批量操作
                  <>
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => handleBatchAction('enable')}
                    >
                      批量启用
                    </Button>
                    <Button
                      size="small"
                      onClick={() => handleBatchAction('disable')}
                    >
                      批量禁用
                    </Button>
                    <Button
                      size="small"
                      danger
                      onClick={() => handleBatchAction('delete')}
                    >
                      批量删除
                    </Button>
                  </>
                )}
                <Button
                  size="small"
                  onClick={handleClearSelection}
                >
                  清除选择
                </Button>
              </Space>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={state.data}
          rowKey="id"
          rowSelection={rowSelection}
          loading={state.loading}
          pagination={{
            current: state.query.page,
            pageSize: state.query.page_size,
            total: state.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>

      {/* Excel上传模态框 */}
      <Modal
        title="上传Excel文件"
        open={state.showUploadModal}
        onCancel={() => setState(prev => ({ ...prev, showUploadModal: false }))}
        footer={null}
        width={800}
      >
        <ExcelUpload
          onUploadSuccess={(response) => {
            message.success(`成功上传 ${response.new_urls} 个新URL`);
            setState(prev => ({ ...prev, showUploadModal: false }));
            loadData();
          }}
          onUploadError={(error) => {
            message.error(`上传失败: ${error}`);
          }}
        />
      </Modal>

      {/* URL详情模态框 */}
      <UrlDetailModal
        visible={state.showDetailModal}
        url={state.selectedUrl}
        onClose={() => setState(prev => ({
          ...prev,
          showDetailModal: false,
          selectedUrl: null
        }))}
        onEdit={(url) => {
          // TODO: 实现编辑功能
          console.log('编辑URL:', url);
        }}
        onDelete={(url) => {
          handleBatchAction('delete');
          setState(prev => ({
            ...prev,
            showDetailModal: false,
            selectedUrl: null
          }));
        }}
      />

      {/* 任务创建模态框 */}
      <Modal
        title="创建监控任务"
        open={state.showTaskCreateModal}
        onCancel={() => setState(prev => ({ ...prev, showTaskCreateModal: false }))}
        footer={null}
        width={1200}
        destroyOnHidden
      >
        <TaskCreateWizard
          initialUrls={state.selectedRowKeys}
          onSuccess={(response: TaskCreateResponse) => {
            message.success(`任务 "${response.task_name}" 创建成功！`);
            setState(prev => ({
              ...prev,
              showTaskCreateModal: false,
              selectedRowKeys: [],
              selectedRows: []
            }));
            // 跳转到监控任务管理页面
            setTimeout(() => {
              navigate('/monitoring', {
                state: {
                  newTaskId: response.task_id,
                  message: `任务 "${response.task_name}" 创建成功`
                }
              });
            }, 1000);
          }}
          onCancel={() => setState(prev => ({ ...prev, showTaskCreateModal: false }))}
        />
      </Modal>
    </div>
  );
};

export default UrlPoolPage;
