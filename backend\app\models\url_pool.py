"""
URL池数据模型

定义URL池相关的数据结构和验证规则
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class UrlStatus(str, Enum):
    """URL状态枚举"""
    ACTIVE = "active"
    DISABLED = "disabled"
    DELETED = "deleted"  # 已删除（回收站）


class UrlPoolItem(BaseModel):
    """URL池项目"""
    id: str = Field(..., description="唯一标识符")
    url: str = Field(..., description="URL地址")
    platform: str = Field(..., description="平台类型")
    source_file: str = Field(..., description="来源Excel文件名")
    added_at: datetime = Field(..., description="添加时间")
    status: UrlStatus = Field(default=UrlStatus.ACTIVE, description="URL状态")
    last_check: Optional[datetime] = Field(None, description="最后检查时间")
    check_count: int = Field(default=0, description="检查次数")
    success_count: int = Field(default=0, description="成功次数")
    error_count: int = Field(default=0, description="错误次数")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    
    @validator('url')
    def validate_url(cls, v):
        """验证URL格式"""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL必须以http://或https://开头')
        return v
    
    @validator('platform')
    def validate_platform(cls, v):
        """验证平台名称"""
        if not v or len(v.strip()) == 0:
            raise ValueError('平台名称不能为空')
        return v.lower()
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.check_count == 0:
            return 0.0
        return self.success_count / self.check_count
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UrlPoolQuery(BaseModel):
    """URL池查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    platform: Optional[str] = Field(None, description="平台筛选")
    status: Optional[UrlStatus] = Field(None, description="状态筛选")
    source_file: Optional[str] = Field(None, description="来源文件筛选")
    search: Optional[str] = Field(None, description="搜索关键词")
    sort_by: Optional[str] = Field(default="added_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")
    exclude_urls: Optional[List[str]] = Field(None, description="排除的URL ID列表")


class UrlPoolBatchUpdate(BaseModel):
    """URL池批量更新请求"""
    url_ids: List[str] = Field(..., description="URL ID列表")
    action: str = Field(..., pattern="^(enable|disable|delete)$", description="操作类型")
    
    @validator('url_ids')
    def validate_url_ids(cls, v):
        """验证URL ID列表"""
        if not v:
            raise ValueError('URL ID列表不能为空')
        if len(v) > 1000:
            raise ValueError('单次操作不能超过1000个URL')
        return v


class UrlPoolStats(BaseModel):
    """URL池统计信息"""
    total_urls: int = Field(..., description="总URL数量")
    active_urls: int = Field(..., description="活跃URL数量")
    disabled_urls: int = Field(..., description="禁用URL数量")
    platforms: Dict[str, int] = Field(..., description="各平台URL数量")
    last_updated: datetime = Field(..., description="最后更新时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ExcelUploadRequest(BaseModel):
    """Excel上传请求"""
    platform_filter: Optional[str] = Field(None, description="平台筛选")
    auto_detect: bool = Field(default=True, description="自动检测平台")


class ExcelUploadResponse(BaseModel):
    """Excel上传响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    total_urls: int = Field(..., description="总URL数量")
    new_urls: int = Field(..., description="新增URL数量")
    duplicate_urls: int = Field(..., description="重复URL数量")
    invalid_urls: int = Field(..., description="无效URL数量")
    urls: List[UrlPoolItem] = Field(..., description="URL列表")
    processing_time: float = Field(..., description="处理时间(秒)")


class UrlPoolListResponse(BaseModel):
    """URL池列表响应"""
    success: bool = Field(..., description="是否成功")
    data: List[UrlPoolItem] = Field(..., description="URL列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    has_more: bool = Field(..., description="是否有更多数据")


class UrlPoolIdsResponse(BaseModel):
    """URL池ID列表响应"""
    success: bool = Field(..., description="是否成功")
    data: List[str] = Field(..., description="URL ID列表")
    total: int = Field(..., description="总数量")


class UrlPoolBatchResponse(BaseModel):
    """URL池批量操作响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    affected_count: int = Field(..., description="影响的URL数量")
    total_count: int = Field(..., description="总操作数量")


class UrlPoolStatsResponse(BaseModel):
    """URL池统计响应"""
    success: bool = Field(..., description="是否成功")
    data: UrlPoolStats = Field(..., description="统计数据")


# 支持的平台列表
SUPPORTED_PLATFORMS = [
    'mercadolibre',
    'amazon',
    'ebay',
    'aliexpress',
    'shopee',
    'unknown'
]


# 平台显示名称映射
PLATFORM_DISPLAY_NAMES = {
    'mercadolibre': 'MercadoLibre',
    'amazon': 'Amazon',
    'ebay': 'eBay',
    'aliexpress': 'AliExpress',
    'shopee': 'Shopee',
    'unknown': '未知平台'
}


def get_platform_display_name(platform: str) -> str:
    """获取平台显示名称"""
    return PLATFORM_DISPLAY_NAMES.get(platform, platform.title())


class UrlPoolItemCreate(BaseModel):
    """创建URL池项目请求"""
    url: str = Field(..., description="URL地址")
    platform: Optional[str] = Field(None, description="平台类型")
    source_file: str = Field(..., description="来源文件")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    
    @validator('url')
    def validate_url(cls, v):
        """验证URL格式"""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL必须以http://或https://开头')
        return v


class UrlPoolItemUpdate(BaseModel):
    """更新URL池项目请求"""
    status: Optional[UrlStatus] = Field(None, description="状态")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class UrlPoolFilter(BaseModel):
    """URL池筛选器"""
    platforms: List[str] = Field(default_factory=list, description="平台列表")
    statuses: List[UrlStatus] = Field(default_factory=list, description="状态列表")
    source_files: List[str] = Field(default_factory=list, description="来源文件列表")
    date_range: Optional[Dict[str, datetime]] = Field(None, description="日期范围")
    
    def is_empty(self) -> bool:
        """检查筛选器是否为空"""
        return (
            not self.platforms and
            not self.statuses and
            not self.source_files and
            not self.date_range
        )


class UrlPoolExportRequest(BaseModel):
    """URL池导出请求"""
    format: str = Field(default="excel", pattern="^(excel|csv|json)$", description="导出格式")
    filter: Optional[UrlPoolFilter] = Field(None, description="筛选条件")
    include_metadata: bool = Field(default=False, description="是否包含元数据")


class UrlPoolImportRequest(BaseModel):
    """URL池导入请求"""
    urls: List[UrlPoolItemCreate] = Field(..., description="URL列表")
    source_file: str = Field(..., description="来源文件名")
    overwrite_duplicates: bool = Field(default=False, description="是否覆盖重复项")
    
    @validator('urls')
    def validate_urls(cls, v):
        """验证URL列表"""
        if not v:
            raise ValueError('URL列表不能为空')
        if len(v) > 10000:
            raise ValueError('单次导入不能超过10000个URL')
        return v
