import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Alert,
  Statistic,
  Row,
  Col,
  Card,
  message,
  Typography,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SelectOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

import { getUrlPool, getPlatformDisplayName, getStatusDisplay, getAllFilteredUrlIds } from '../../services/urlPoolApi';
import { previewSelectedUrls } from '../../services/taskCreateApi';
import type { UrlPoolItem, UrlPoolQuery } from '../../types/urlPool';
import type { UrlPreviewResponse } from '../../types/taskCreate';

const { Text } = Typography;
const { Option } = Select;

interface UrlSelectionStepProps {
  selectedUrls: string[];
  onSelectionChange: (urls: string[]) => void;
}

const UrlSelectionStep: React.FC<UrlSelectionStepProps> = ({
  selectedUrls,
  onSelectionChange
}) => {
  const [loading, setLoading] = useState(false);
  const [urls, setUrls] = useState<UrlPoolItem[]>([]);
  const [total, setTotal] = useState(0);
  const [previewData, setPreviewData] = useState<UrlPreviewResponse | null>(null);
  const [sourceFiles, setSourceFiles] = useState<string[]>([]);
  const [query, setQuery] = useState<UrlPoolQuery>({
    page: 1,
    page_size: 20,
    sort_by: 'added_at',
    sort_order: 'desc',
    status: 'active' // 默认只显示活跃的URL
  });

  // 加载URL列表
  const loadUrls = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getUrlPool(query);
      setUrls(response.data);
      setTotal(response.total);

      // 提取来源文件列表（去重）
      const sourceFileSet = new Set(response.data.map(item => item.source_file).filter(Boolean));
      const files = Array.from(sourceFileSet);
      setSourceFiles(files);
    } catch (error: any) {
      message.error(`加载URL失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [query]);

  // 预览选中的URL
  const loadPreview = useCallback(async () => {
    if (selectedUrls.length === 0) {
      setPreviewData(null);
      return;
    }

    try {
      const response = await previewSelectedUrls({ url_ids: selectedUrls });
      setPreviewData(response);
    } catch (error: any) {
      console.error('预览URL失败:', error);
    }
  }, [selectedUrls]);

  useEffect(() => {
    loadUrls();
  }, [loadUrls]);

  useEffect(() => {
    loadPreview();
  }, [loadPreview]);

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: selectedUrls,
    onChange: (selectedRowKeys: React.Key[]) => {
      onSelectionChange(selectedRowKeys as string[]);
    },
    preserveSelectedRowKeys: true,
    getCheckboxProps: (record: UrlPoolItem) => ({
      name: record.id,
    }),
    onSelectAll: (selected: boolean, selectedRows: UrlPoolItem[], changeRows: UrlPoolItem[]) => {
      console.log('Select all:', selected, selectedRows, changeRows);
    }
  };

  // 表格列定义
  const columns: ColumnsType<UrlPoolItem> = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 400,
      ellipsis: {
        showTitle: false
      },
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code style={{ fontSize: 12 }}>
            {url.length > 60 ? `${url.substring(0, 60)}...` : url}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 120,
      render: (platform: string) => (
        <Tag color={platform === 'mercadolibre' ? 'gold' : platform === 'amazon' ? 'orange' : 'blue'}>
          {getPlatformDisplayName(platform)}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const { name, color } = getStatusDisplay(status);
        return <Tag color={color}>{name}</Tag>;
      }
    },
    {
      title: '来源文件',
      dataIndex: 'source_file',
      key: 'source_file',
      width: 150,
      ellipsis: true
    },
    {
      title: '添加时间',
      dataIndex: 'added_at',
      key: 'added_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString('zh-CN')
    }
  ];

  // 处理表格变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQuery(prev => ({
      ...prev,
      page: pagination.current,
      page_size: pagination.pageSize,
      sort_by: sorter.field || 'added_at',
      sort_order: sorter.order === 'ascend' ? 'asc' : 'desc'
    }));
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setQuery(prev => ({
      ...prev,
      search: value || undefined,
      page: 1
    }));
  };

  // 处理平台筛选
  const handlePlatformFilter = (platform: string) => {
    setQuery(prev => ({
      ...prev,
      platform: platform === 'all' ? undefined : platform,
      page: 1
    }));
  };

  // 处理状态筛选
  const handleStatusFilter = (status: string) => {
    setQuery(prev => ({
      ...prev,
      status: status === 'all' ? undefined : (status as 'active' | 'disabled' | 'deleted'),
      page: 1
    }));
  };

  // 处理来源文件筛选
  const handleSourceFileFilter = (sourceFile: string) => {
    setQuery(prev => ({
      ...prev,
      source_file: sourceFile === 'all' ? undefined : sourceFile,
      page: 1
    }));
  };

  // 选择所有符合筛选条件的URL（纯前端功能）
  const handleSelectAllFiltered = async () => {
    try {
      // 获取所有符合筛选条件的URL ID
      const response = await getAllFilteredUrlIds({
        platform: query.platform,
        status: query.status,
        source_file: query.source_file,
        search: query.search,
        sort_by: query.sort_by,
        sort_order: query.sort_order
      });

      if (response.success) {
        // 合并当前选择和筛选结果
        const combinedUrls = [...selectedUrls, ...response.data];
        const uniqueUrlSet = new Set(combinedUrls);
        const newSelectedUrls = Array.from(uniqueUrlSet);
        onSelectionChange(newSelectedUrls);
        message.success(`已选择所有符合筛选条件的 ${response.total} 个URL`);
      } else {
        message.error('获取筛选结果失败');
      }
    } catch (error: any) {
      message.error(`选择失败: ${error.message}`);
    }
  };

  // 清空筛选条件
  const handleClearFilters = () => {
    setQuery({
      page: 1,
      page_size: 20,
      sort_by: 'added_at',
      sort_order: 'desc',
      status: 'active'
    });
  };

  // 验证选择
  const canProceed = selectedUrls.length > 0;

  return (
    <div>
      {/* 选择统计 */}
      {previewData && (
        <Alert
          message={
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="已选择URL"
                  value={previewData.total_count}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="有效URL"
                  value={previewData.valid_count}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="无效URL"
                  value={previewData.invalid_count}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平台数量"
                  value={Object.keys(previewData.platform_distribution).length}
                />
              </Col>
            </Row>
          }
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 筛选工具栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          {/* 第一行：搜索和筛选 */}
          <Col span={6}>
            <Input.Search
              placeholder="搜索URL..."
              onSearch={handleSearch}
              allowClear
              value={query.search || ''}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择平台"
              style={{ width: '100%' }}
              onChange={handlePlatformFilter}
              value={query.platform || 'all'}
              allowClear
            >
              <Option value="all">所有平台</Option>
              <Option value="mercadolibre">MercadoLibre</Option>
              <Option value="amazon">Amazon</Option>
              <Option value="ebay">eBay</Option>
              <Option value="aliexpress">AliExpress</Option>
              <Option value="shopee">Shopee</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
              value={query.status || 'all'}
              allowClear
            >
              <Option value="all">所有状态</Option>
              <Option value="active">活跃</Option>
              <Option value="disabled">禁用</Option>
              <Option value="deleted">已删除</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="来源文件"
              style={{ width: '100%' }}
              onChange={handleSourceFileFilter}
              value={query.source_file || 'all'}
              allowClear
              showSearch
              optionFilterProp="children"
            >
              <Option value="all">所有文件</Option>
              {sourceFiles.map(file => (
                <Option key={file} value={file}>{file}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleClearFilters}
                title="清空筛选条件"
              >
                重置
              </Button>
              <Button
                size="small"
                icon={<SelectOutlined />}
                onClick={handleSelectAllFiltered}
                title="选择所有符合筛选条件的URL"
              >
                选择所有筛选结果
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 第二行：选择状态和操作 */}
        <Row gutter={16} align="middle" style={{ marginTop: 12 }}>
          <Col span={12}>
            <Space>
              <Text type="secondary">
                已选择 {selectedUrls.length} 个URL
              </Text>
              <Text type="secondary">
                当前页显示 {urls.length} 个，共 {total} 个
              </Text>
            </Space>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Space>
              {selectedUrls.length > 0 && (
                <Button
                  size="small"
                  onClick={() => onSelectionChange([])}
                >
                  清空选择
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* URL列表表格 */}
      <Table
        columns={columns}
        dataSource={urls}
        rowKey="id"
        rowSelection={rowSelection}
        loading={loading}
        pagination={{
          current: query.page,
          pageSize: query.page_size,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
        size="small"
      />

      {/* 验证提示 */}
      {!canProceed && (
        <Alert
          message="请至少选择一个URL"
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}


    </div>
  );
};

export default UrlSelectionStep;
