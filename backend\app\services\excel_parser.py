"""
Excel文件解析服务

提供Excel文件上传、URL提取、平台识别等功能
"""

import pandas as pd
import re
import hashlib
import logging
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
import tempfile
import os
from pathlib import Path

logger = logging.getLogger(__name__)


class ExcelUrlParser:
    """Excel URL解析器"""
    
    def __init__(self):
        # 支持的平台域名模式
        self.platform_patterns = {
            'mercadolibre': [
                r'mercadolibre\.com\.mx',
                r'mercadolibre\.com\.ar',
                r'mercadolibre\.com\.co',
                r'mercadolibre\.cl',
                r'mercadolibre\.com\.pe',
                r'mercadolibre\.com\.uy',
                r'mercadolibre\.com\.ve',
                r'mercadolibre\.com\.ec',
                r'mercadolibre\.com\.bo',
                r'mercadolibre\.com\.py',
                r'mercadolibre\.com\.cr',
                r'mercadolibre\.com\.pa',
                r'mercadolibre\.com\.ni',
                r'mercadolibre\.com\.sv',
                r'mercadolibre\.com\.gt',
                r'mercadolibre\.com\.hn',
                r'mercadolibre\.com\.do'
            ],
            'amazon': [
                r'amazon\.com',
                r'amazon\.com\.mx',
                r'amazon\.es',
                r'amazon\.co\.uk',
                r'amazon\.de',
                r'amazon\.fr',
                r'amazon\.it',
                r'amazon\.ca',
                r'amazon\.com\.br',
                r'amazon\.in',
                r'amazon\.co\.jp',
                r'amazon\.com\.au'
            ],
            'ebay': [
                r'ebay\.com',
                r'ebay\.com\.mx',
                r'ebay\.es',
                r'ebay\.co\.uk',
                r'ebay\.de',
                r'ebay\.fr',
                r'ebay\.it',
                r'ebay\.ca',
                r'ebay\.com\.au'
            ],
            'aliexpress': [
                r'aliexpress\.com',
                r'aliexpress\.us'
            ],
            'shopee': [
                r'shopee\.com\.mx',
                r'shopee\.com\.co',
                r'shopee\.cl',
                r'shopee\.com\.br',
                r'shopee\.sg',
                r'shopee\.ph',
                r'shopee\.com\.my',
                r'shopee\.co\.th',
                r'shopee\.vn',
                r'shopee\.co\.id',
                r'shopee\.tw'
            ]
        }
        
        # URL正则表达式
        self.url_pattern = re.compile(
            r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?',
            re.IGNORECASE
        )
    
    async def parse_excel_file(self, file_path: str, source_filename: str) -> List[Dict]:
        """
        解析Excel文件并提取URL
        
        Args:
            file_path: Excel文件路径
            source_filename: 原始文件名
            
        Returns:
            解析出的URL列表
        """
        try:
            logger.info(f"开始解析Excel文件: {source_filename}")
            
            # 读取Excel文件
            df = await self._read_excel_file(file_path)
            
            # 检测URL列
            url_columns = self._detect_url_columns(df)
            logger.info(f"检测到URL列: {url_columns}")
            
            # 提取所有URL
            urls = []
            for col in url_columns:
                column_urls = self._extract_urls_from_column(df, col, source_filename)
                urls.extend(column_urls)
            
            # 去重和验证
            unique_urls = self._deduplicate_urls(urls)
            
            logger.info(f"解析完成: 总计{len(urls)}个URL，去重后{len(unique_urls)}个")
            return unique_urls
            
        except Exception as e:
            logger.error(f"解析Excel文件失败: {e}")
            raise
    
    async def _read_excel_file(self, file_path: str) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            # 尝试读取第一个工作表
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path, engine='openpyxl')
            elif file_path.endswith('.xls'):
                df = pd.read_excel(file_path, engine='xlrd')
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")
            
            logger.info(f"成功读取Excel文件，行数: {len(df)}, 列数: {len(df.columns)}")
            return df
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def _detect_url_columns(self, df: pd.DataFrame) -> List[str]:
        """检测包含URL的列"""
        url_columns = []
        
        for col in df.columns:
            # 检查列名是否包含URL相关关键词
            col_name_lower = str(col).lower()
            if any(keyword in col_name_lower for keyword in ['url', 'link', 'enlace', 'liga']):
                url_columns.append(col)
                continue
            
            # 检查列内容是否包含URL
            sample_size = min(10, len(df))
            sample_data = df[col].dropna().head(sample_size)

            if len(sample_data) == 0:
                continue

            url_count = 0
            for value in sample_data:
                value_str = str(value).strip() if value is not None else ""
                if value_str and self.url_pattern.search(value_str):
                    url_count += 1

            # 如果超过50%的样本包含URL，认为是URL列
            if url_count / len(sample_data) > 0.5:
                url_columns.append(col)
        
        return url_columns
    
    def _extract_urls_from_column(self, df: pd.DataFrame, column: str, source_filename: str) -> List[Dict]:
        """从指定列提取URL"""
        urls = []

        for index, value in df[column].items():
            if pd.isna(value) or value is None:
                continue

            # 转换为字符串
            value_str = str(value).strip()
            if not value_str:
                continue

            # 查找URL
            found_urls = self.url_pattern.findall(value_str)

            for url in found_urls:
                # 清理URL
                cleaned_url = self._clean_url(url)
                if not cleaned_url:
                    continue

                # 识别平台
                platform = self._detect_platform(cleaned_url)

                # 验证URL
                if not self._validate_url(cleaned_url):
                    continue

                url_data = {
                    'url': cleaned_url,
                    'platform': platform,
                    'source_file': source_filename,
                    'original_row': index + 2,  # Excel行号从1开始，加上表头
                    'original_column': column,
                    'url_hash': self._generate_url_hash(cleaned_url)
                }

                urls.append(url_data)

        return urls
    
    def _clean_url(self, url: str) -> Optional[str]:
        """清理和标准化URL"""
        try:
            # 移除首尾空白
            url = url.strip()
            
            # 确保有协议
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # 解析URL
            parsed = urlparse(url)
            
            # 重构URL（移除fragment等）
            cleaned_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            if parsed.query:
                cleaned_url += f"?{parsed.query}"
            
            return cleaned_url
            
        except Exception as e:
            logger.warning(f"清理URL失败: {url}, 错误: {e}")
            return None
    
    def _detect_platform(self, url: str) -> str:
        """检测URL所属平台"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            for platform, patterns in self.platform_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, domain):
                        return platform
            
            return 'unknown'
            
        except Exception as e:
            logger.warning(f"检测平台失败: {url}, 错误: {e}")
            return 'unknown'
    
    def _validate_url(self, url: str) -> bool:
        """验证URL是否有效"""
        try:
            parsed = urlparse(url)
            
            # 检查基本结构
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # 检查协议
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # 检查域名格式
            if not re.match(r'^[a-zA-Z0-9.-]+$', parsed.netloc):
                return False
            
            return True
            
        except Exception:
            return False
    
    def _generate_url_hash(self, url: str) -> str:
        """生成URL的哈希值用于去重"""
        return hashlib.md5(url.encode('utf-8')).hexdigest()
    
    def _deduplicate_urls(self, urls: List[Dict]) -> List[Dict]:
        """URL去重"""
        seen_hashes = set()
        unique_urls = []
        
        for url_data in urls:
            url_hash = url_data['url_hash']
            if url_hash not in seen_hashes:
                seen_hashes.add(url_hash)
                unique_urls.append(url_data)
        
        return unique_urls
    
    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return list(self.platform_patterns.keys())


# 全局解析器实例
excel_parser = ExcelUrlParser()


async def save_uploaded_file(file_content: bytes, filename: str) -> str:
    """保存上传的文件到临时目录"""
    try:
        # 创建临时文件
        suffix = Path(filename).suffix
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
            tmp_file.write(file_content)
            tmp_path = tmp_file.name
        
        logger.info(f"文件保存到临时路径: {tmp_path}")
        return tmp_path
        
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise


def cleanup_temp_file(file_path: str):
    """清理临时文件"""
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
            logger.info(f"清理临时文件: {file_path}")
    except Exception as e:
        logger.warning(f"清理临时文件失败: {file_path}, 错误: {e}")
