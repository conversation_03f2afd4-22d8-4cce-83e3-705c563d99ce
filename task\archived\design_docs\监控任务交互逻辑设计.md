# 监控任务交互逻辑设计

**创建时间**: 2025年7月5日  
**状态**: 设计中  

## 🎯 当前问题分析

### 1. 任务列表页面问题
- ❌ **编辑任务按钮**: 只有消息提示，没有实际功能
- ❌ **暂停任务按钮**: 调用了`handleTaskAction`但可能API不匹配
- ❌ **配置任务按钮**: 没有绑定任何事件处理函数
- ❌ **详情按钮**: 没有绑定任何事件处理函数（与查看详情重复）
- ❌ **删除按钮**: 没有绑定任何事件处理函数

### 2. 任务详情页面问题
- ❌ **编辑任务按钮**: 没有实际功能
- ❌ **任务统计错误**: 数据显示不正确
- ❌ **URL管理问题**: 没有正确显示任务包含的URL

### 3. 数据一致性问题
- ❌ **API接口不匹配**: 前端调用的API与后端实现不一致
- ❌ **状态管理混乱**: 任务状态更新后页面没有刷新
- ❌ **数据格式不统一**: 不同来源的任务数据格式不一致

## 🏗️ 监控任务交互逻辑设计

### 1. 任务生命周期管理

#### 任务状态定义
```typescript
type TaskStatus = 'draft' | 'active' | 'paused' | 'stopped' | 'error';

interface TaskState {
  status: TaskStatus;
  is_running: boolean;
  last_run?: string;
  next_run?: string;
  error_message?: string;
}
```

#### 状态转换规则
```
draft → active (启动任务)
active → paused (暂停任务)
active → stopped (停止任务)
paused → active (恢复任务)
paused → stopped (停止任务)
stopped → active (重新启动)
error → active (重试任务)
```

### 2. 任务列表页面交互设计

#### 操作按钮设计
```typescript
interface TaskAction {
  icon: ReactNode;
  tooltip: string;
  onClick: (taskId: string) => void;
  visible: (task: Task) => boolean;
  disabled: (task: Task) => boolean;
}

const taskActions: TaskAction[] = [
  {
    icon: <EyeOutlined />,
    tooltip: "查看详情",
    onClick: handleViewTask,
    visible: () => true,
    disabled: () => false
  },
  {
    icon: <EditOutlined />,
    tooltip: "编辑任务",
    onClick: handleEditTask,
    visible: () => true,
    disabled: (task) => task.is_running
  },
  {
    icon: <PlayCircleOutlined />,
    tooltip: "启动任务",
    onClick: (id) => handleTaskAction(id, 'start'),
    visible: (task) => task.status !== 'active',
    disabled: (task) => task.is_running
  },
  {
    icon: <PauseCircleOutlined />,
    tooltip: "暂停任务",
    onClick: (id) => handleTaskAction(id, 'pause'),
    visible: (task) => task.status === 'active',
    disabled: (task) => !task.is_running
  },
  {
    icon: <StopOutlined />,
    tooltip: "停止任务",
    onClick: (id) => handleTaskAction(id, 'stop'),
    visible: (task) => task.status === 'active' || task.status === 'paused',
    disabled: () => false
  },
  {
    icon: <SettingOutlined />,
    tooltip: "任务配置",
    onClick: handleConfigTask,
    visible: () => true,
    disabled: (task) => task.is_running
  },
  {
    icon: <DeleteOutlined />,
    tooltip: "删除任务",
    onClick: handleDeleteTask,
    visible: () => true,
    disabled: (task) => task.is_running
  }
];
```

### 3. 任务详情页面交互设计

#### 页面结构
```
任务详情页面
├── 头部操作区
│   ├── 返回按钮
│   ├── 任务标题 + 状态标签
│   └── 操作按钮组
│       ├── 立即执行
│       ├── 暂停/恢复
│       ├── 停止任务
│       ├── 编辑任务
│       └── 删除任务
├── Tab页面区域
│   ├── 基本信息Tab
│   │   ├── 任务配置信息
│   │   └── 实时统计卡片
│   ├── URL管理Tab
│   │   ├── URL列表表格
│   │   ├── 批量操作工具栏
│   │   └── 添加URL功能
│   ├── 执行历史Tab
│   │   ├── 执行记录表格
│   │   └── 执行详情查看
│   └── 统计图表Tab
│       ├── URL状态分布图
│       ├── 执行成功率趋势
│       └── 性能指标图表
```

#### 实时数据更新
```typescript
// 定时刷新机制
const useTaskDetailRefresh = (taskId: string) => {
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
  
  useEffect(() => {
    // 如果任务正在运行，每5秒刷新一次
    if (task?.is_running) {
      setRefreshInterval(5000);
    } else {
      setRefreshInterval(null);
    }
  }, [task?.is_running]);
  
  useInterval(() => {
    if (refreshInterval) {
      fetchTaskDetail();
      fetchExecutionStatus();
    }
  }, refreshInterval);
};
```

### 4. API接口标准化

#### 统一响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

interface TaskOperationResponse {
  success: boolean;
  message: string;
  task_id: string;
  new_status?: TaskStatus;
}
```

#### 任务操作API
```typescript
// 启动任务
POST /api/v1/monitoring-tasks/{id}/start
// 暂停任务  
POST /api/v1/monitoring-tasks/{id}/pause
// 停止任务
POST /api/v1/monitoring-tasks/{id}/stop
// 恢复任务
POST /api/v1/monitoring-tasks/{id}/resume
// 删除任务
DELETE /api/v1/monitoring-tasks/{id}
// 更新任务
PUT /api/v1/monitoring-tasks/{id}
```

### 5. 错误处理和用户反馈

#### 操作反馈设计
```typescript
interface OperationFeedback {
  loading: boolean;
  success: boolean;
  error: string | null;
  message: string | null;
}

const useTaskOperation = () => {
  const [feedback, setFeedback] = useState<OperationFeedback>({
    loading: false,
    success: false,
    error: null,
    message: null
  });
  
  const executeOperation = async (operation: () => Promise<any>) => {
    setFeedback({ loading: true, success: false, error: null, message: null });
    try {
      const result = await operation();
      setFeedback({ 
        loading: false, 
        success: true, 
        error: null, 
        message: result.message 
      });
      message.success(result.message);
    } catch (error) {
      setFeedback({ 
        loading: false, 
        success: false, 
        error: error.message, 
        message: null 
      });
      message.error(error.message);
    }
  };
  
  return { feedback, executeOperation };
};
```

## 🔄 重构计划

### 阶段1: 修复任务列表页面操作按钮
1. 实现所有操作按钮的事件处理函数
2. 添加操作确认对话框
3. 实现操作后的状态刷新

### 阶段2: 完善任务详情页面
1. 修复任务统计数据显示
2. 修复URL管理功能
3. 实现编辑任务功能

### 阶段3: 统一API接口
1. 标准化所有任务操作API
2. 修复数据一致性问题
3. 实现实时状态更新

### 阶段4: 优化用户体验
1. 添加加载状态指示
2. 优化错误处理和反馈
3. 实现操作历史记录

---

**设计者**: Augment Agent  
**创建时间**: 2025年7月5日
