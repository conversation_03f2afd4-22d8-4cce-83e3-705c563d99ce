#!/usr/bin/env python3
"""
完整的监控功能演示
展示爬虫请求构建监控系统的所有功能
"""

import asyncio
import requests
import json
import time
from datetime import datetime


def demo_frontend_features():
    """演示前端监控功能"""
    
    print("🖥️ 前端监控功能演示")
    print("=" * 30)
    
    print("📋 监控页面功能:")
    print("  • 实时指标展示（总请求数、成功率、平均耗时、平均URL数）")
    print("  • 状态分布统计（成功、验证错误、API错误、失败）")
    print("  • 任务详细监控（选择特定任务查看执行历史）")
    print("  • 性能趋势分析（简化版本，显示统计信息）")
    print("  • 错误提示和加载状态")
    print("  • 演示数据加载功能")
    
    print("\n🎯 访问方式:")
    print("  1. 打开浏览器访问: http://localhost:3000")
    print("  2. 在左侧菜单中点击 '爬虫请求监控'")
    print("  3. 或直接访问: http://localhost:3000/crawler-request-monitoring")
    
    print("\n💡 使用提示:")
    print("  • 点击 '加载演示数据' 按钮查看示例监控数据")
    print("  • 选择不同的查询天数查看不同时间范围的数据")
    print("  • 选择任务ID查看特定任务的详细执行历史")
    print("  • 表格支持分页、排序和搜索功能")


def demo_api_features():
    """演示API监控功能"""
    
    print("\n📡 API监控功能演示")
    print("=" * 25)
    
    base_url = "http://localhost:8000"
    
    # 演示API调用
    api_examples = [
        {
            "name": "获取指标摘要",
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/summary?days=7",
            "description": "获取最近7天的爬虫请求构建指标摘要"
        },
        {
            "name": "获取任务指标",
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/task/demo-task-123",
            "description": "获取特定任务的详细执行历史"
        },
        {
            "name": "获取监控报告",
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/report/demo-task-123",
            "description": "生成任务的格式化监控报告"
        },
        {
            "name": "性能分析",
            "url": f"{base_url}/api/v1/monitoring/crawler-request-metrics/performance-analysis?days=7",
            "description": "获取性能分析和优化建议"
        },
        {
            "name": "系统健康检查",
            "url": f"{base_url}/api/v1/monitoring/system-health",
            "description": "检查监控系统和相关服务的健康状态"
        }
    ]
    
    print("📋 可用的监控API端点:")
    for i, api in enumerate(api_examples, 1):
        print(f"\n{i}. {api['name']}")
        print(f"   URL: {api['url']}")
        print(f"   说明: {api['description']}")
    
    print("\n💻 使用示例 (curl命令):")
    print(f"""
# 获取指标摘要
curl "{base_url}/api/v1/monitoring/crawler-request-metrics/summary?days=1"

# 检查系统健康
curl "{base_url}/api/v1/monitoring/system-health"

# 获取任务监控报告
curl "{base_url}/api/v1/monitoring/crawler-request-metrics/report/your-task-id"
""")


async def demo_backend_integration():
    """演示后端集成功能"""
    
    print("\n🔧 后端集成功能演示")
    print("=" * 25)
    
    print("📋 CrawlerRequestBuilder集成:")
    print("  • 自动监控所有请求构建过程")
    print("  • 详细的性能指标收集（URL获取、配置获取、请求构建）")
    print("  • 完整的数据流日志记录")
    print("  • 智能的性能阈值检查")
    print("  • 错误分析和解决建议")
    
    print("\n📋 Celery任务集成:")
    print("  • enhanced_crawl_task 自动包含监控")
    print("  • 执行上下文传递和追踪")
    print("  • 分布式环境下的监控数据收集")
    
    print("\n📋 监控数据存储:")
    print("  • Redis时间序列存储")
    print("  • 按日期组织的指标数据")
    print("  • 任务特定的指标历史")
    print("  • 自动数据过期和清理")
    
    # 演示代码使用
    print("\n💻 代码使用示例:")
    print("""
# 1. 使用CrawlerRequestBuilder（自动监控）
from app.services.crawler_request_builder import CrawlerRequestBuilder

result = await CrawlerRequestBuilder.build_request_for_task(
    task_id="your-task-id",
    execution_context={
        "execution_id": "exec-123",
        "source": "scheduled_monitoring"
    }
)

# 2. 使用增强的Celery任务
from app.tasks.enhanced_crawl_task import enhanced_crawl_task

result = enhanced_crawl_task.delay(
    task_id="your-task-id",
    execution_context={"source": "celery_worker"}
)

# 3. 直接使用监控器
from app.services.crawler_request_monitor import get_crawler_request_monitor

monitor = await get_crawler_request_monitor()
async with monitor.monitor_request_building(task_id, exec_id) as metrics:
    # 你的逻辑
    metrics.urls_count = len(urls)
    metrics.url_fetch_time = fetch_duration
""")


def demo_monitoring_benefits():
    """演示监控系统的优势"""
    
    print("\n✨ 监控系统优势")
    print("=" * 20)
    
    benefits = [
        {
            "category": "🔍 可观测性",
            "items": [
                "完全可见的请求构建过程",
                "实时性能监控和历史趋势",
                "详细的错误追踪和诊断"
            ]
        },
        {
            "category": "🚀 运维效率",
            "items": [
                "自动化的性能阈值检查",
                "智能的错误分析和建议",
                "便捷的监控API和界面"
            ]
        },
        {
            "category": "🛡️ 系统稳定性",
            "items": [
                "早期发现性能瓶颈",
                "完整的错误上下文记录",
                "数据驱动的优化决策"
            ]
        },
        {
            "category": "📊 数据洞察",
            "items": [
                "请求构建性能趋势分析",
                "URL处理效率统计",
                "系统使用模式识别"
            ]
        }
    ]
    
    for benefit in benefits:
        print(f"\n{benefit['category']}:")
        for item in benefit['items']:
            print(f"  • {item}")


def demo_next_steps():
    """演示后续步骤"""
    
    print("\n🔮 后续扩展计划")
    print("=" * 20)
    
    print("📋 短期计划:")
    print("  • 实时监控仪表板（WebSocket更新）")
    print("  • 告警系统集成（性能异常自动告警）")
    print("  • 监控数据导出（CSV/Excel）")
    print("  • 自定义阈值配置")
    
    print("\n📋 长期计划:")
    print("  • Prometheus集成（指标导出）")
    print("  • Grafana仪表板（专业可视化）")
    print("  • ELK Stack集成（日志聚合）")
    print("  • 多渠道告警通知")
    
    print("\n🎯 使用建议:")
    print("  1. 定期查看监控页面，了解系统运行状况")
    print("  2. 关注性能警告，及时优化瓶颈")
    print("  3. 分析错误模式，改进系统稳定性")
    print("  4. 利用监控数据进行容量规划")


def main():
    """主演示函数"""
    
    print("🎯 爬虫请求构建监控系统完整演示")
    print("🕐 演示时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
    
    # 演示各个功能模块
    demo_frontend_features()
    demo_api_features()
    asyncio.run(demo_backend_integration())
    demo_monitoring_benefits()
    demo_next_steps()
    
    print("\n🎉 监控系统演示完成！")
    print("\n📚 相关文档:")
    print("  • docs/crawler_request_monitoring_guide.md - 监控系统指南")
    print("  • docs/crawler_request_builder_usage_guide.md - 使用指南")
    print("  • backend/app/services/crawler_request_monitor.py - 监控服务")
    print("  • frontend/src/pages/Monitoring/CrawlerRequestMonitoring.tsx - 前端页面")
    
    print("\n🚀 立即体验:")
    print("  1. 访问前端监控页面: http://localhost:3000/crawler-request-monitoring")
    print("  2. 点击 '加载演示数据' 查看监控效果")
    print("  3. 查看API文档: http://localhost:8000/docs")
    print("  4. 运行实际的监控任务查看真实数据")


if __name__ == "__main__":
    main()
