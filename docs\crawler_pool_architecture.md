# 爬虫池架构重构设计

## 概述

将现有的单一爬虫配置重构为爬虫池架构，支持多个爬虫后端的管理、负载均衡和故障转移。

## 当前问题分析

### 现有架构问题
1. **单一爬虫配置**：`CrawlerFullConfig` 只支持一个爬虫后端
2. **硬编码API地址**：配置中只有一个 `api.base_url`
3. **缺少负载均衡**：无法在多个爬虫后端之间分配任务
4. **无健康检查**：无法监控各个爬虫后端的状态
5. **扩展性差**：添加新的爬虫后端需要修改核心配置

### 目标架构特性
1. **多后端支持**：支持配置和管理多个爬虫后端
2. **负载均衡**：智能分配任务到不同后端
3. **健康检查**：实时监控后端状态
4. **故障转移**：自动处理后端故障
5. **动态管理**：运行时添加/移除后端

## 新架构设计

### 1. 数据模型层

#### 1.1 爬虫后端模型
```python
class CrawlerBackend(BaseModel):
    """单个爬虫后端配置"""
    id: str = Field(description="后端唯一标识")
    name: str = Field(description="后端名称")
    base_url: str = Field(description="API基础URL")
    timeout: int = Field(default=30000, description="超时时间(ms)")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 认证配置
    auth_type: str = Field(default="none", description="认证类型")
    api_key: Optional[str] = Field(None, description="API密钥")
    
    # 性能配置
    max_concurrent: int = Field(default=2, description="最大并发数")
    weight: int = Field(default=1, description="负载均衡权重")
    
    # 状态信息
    status: str = Field(default="active", description="后端状态")
    health_score: float = Field(default=1.0, description="健康评分")
    last_check: Optional[datetime] = Field(None, description="最后检查时间")
    
    # 统计信息
    total_requests: int = Field(default=0, description="总请求数")
    success_requests: int = Field(default=0, description="成功请求数")
    avg_response_time: float = Field(default=0.0, description="平均响应时间")
```

#### 1.2 爬虫池配置模型
```python
class CrawlerPoolConfig(BaseModel):
    """爬虫池配置"""
    pool_name: str = Field(description="池名称")
    description: Optional[str] = Field(None, description="池描述")
    
    # 后端列表
    backends: List[CrawlerBackend] = Field(description="爬虫后端列表")
    
    # 负载均衡配置
    load_balance_strategy: str = Field(default="round_robin", description="负载均衡策略")
    health_check_interval: int = Field(default=60, description="健康检查间隔(秒)")
    failure_threshold: int = Field(default=3, description="故障阈值")
    
    # 全局配置（继承原有配置）
    browser: BrowserConfig
    crawler: CrawlerConfig
    llm: LLMConfig
    schema_extraction: SchemaExtractionConfig
    content_processing: ContentProcessingConfig
    link_filtering: LinkFilteringConfig
    scheduler: SchedulerConfig
    monitor: MonitorConfig
```

### 2. 负载均衡策略

#### 2.1 策略类型
- **round_robin**: 轮询分配
- **weighted_round_robin**: 加权轮询
- **least_connections**: 最少连接数
- **health_based**: 基于健康评分
- **response_time**: 基于响应时间

#### 2.2 策略实现
```python
class LoadBalancer:
    """负载均衡器"""
    
    def select_backend(
        self, 
        backends: List[CrawlerBackend], 
        strategy: str = "round_robin"
    ) -> Optional[CrawlerBackend]:
        """选择最佳后端"""
        
    def update_backend_stats(
        self, 
        backend_id: str, 
        response_time: float, 
        success: bool
    ):
        """更新后端统计信息"""
```

### 3. 健康检查机制

#### 3.1 检查类型
- **连接检查**：验证API连接性
- **功能检查**：测试基本爬取功能
- **性能检查**：监控响应时间和成功率

#### 3.2 健康评分算法
```python
def calculate_health_score(backend: CrawlerBackend) -> float:
    """计算健康评分 (0.0 - 1.0)"""
    # 基于成功率、响应时间、连接状态等因素
    success_rate = backend.success_requests / max(backend.total_requests, 1)
    response_factor = min(1.0, 1000 / max(backend.avg_response_time, 100))
    
    return (success_rate * 0.6 + response_factor * 0.4)
```

### 4. API接口设计

#### 4.1 爬虫池管理API
```
GET    /api/v1/crawler/pools                 # 获取所有爬虫池
POST   /api/v1/crawler/pools                 # 创建爬虫池
GET    /api/v1/crawler/pools/{pool_id}       # 获取特定爬虫池
PUT    /api/v1/crawler/pools/{pool_id}       # 更新爬虫池
DELETE /api/v1/crawler/pools/{pool_id}       # 删除爬虫池

GET    /api/v1/crawler/pools/{pool_id}/backends        # 获取后端列表
POST   /api/v1/crawler/pools/{pool_id}/backends        # 添加后端
PUT    /api/v1/crawler/pools/{pool_id}/backends/{id}   # 更新后端
DELETE /api/v1/crawler/pools/{pool_id}/backends/{id}   # 删除后端

GET    /api/v1/crawler/pools/{pool_id}/health          # 获取池健康状态
POST   /api/v1/crawler/pools/{pool_id}/health/check    # 手动健康检查
```

#### 4.2 兼容性API
保持现有API兼容性，内部使用默认爬虫池：
```
GET    /api/v1/crawler/config     # 返回默认池的配置
PUT    /api/v1/crawler/config     # 更新默认池的配置
```

## 实施计划

### 阶段1：数据模型重构
1. 创建新的爬虫池数据模型
2. 实现数据迁移逻辑
3. 保持向后兼容性

### 阶段2：负载均衡实现
1. 实现负载均衡器
2. 集成健康检查机制
3. 更新任务调度逻辑

### 阶段3：前端界面开发
1. 爬虫池管理界面
2. 后端状态监控
3. 配置管理功能

### 阶段4：测试和优化
1. 功能测试
2. 性能测试
3. 故障转移测试

## 兼容性考虑

### 向后兼容
1. 保持现有API接口不变
2. 自动将单一配置转换为默认池
3. 渐进式迁移策略

### 配置迁移
```python
def migrate_single_config_to_pool(old_config: CrawlerFullConfig) -> CrawlerPoolConfig:
    """将单一配置迁移到爬虫池配置"""
    default_backend = CrawlerBackend(
        id="default",
        name="默认爬虫后端",
        base_url=old_config.api.base_url,
        timeout=old_config.api.timeout,
        max_retries=old_config.api.max_retries
    )
    
    return CrawlerPoolConfig(
        pool_name="default",
        description="默认爬虫池",
        backends=[default_backend],
        browser=old_config.browser,
        crawler=old_config.crawler,
        llm=old_config.llm,
        # ... 其他配置
    )
```

## 优势分析

### 1. 可扩展性
- 支持动态添加爬虫后端
- 水平扩展爬取能力
- 支持不同类型的爬虫服务

### 2. 高可用性
- 故障自动转移
- 健康检查和监控
- 负载均衡分散风险

### 3. 性能优化
- 智能任务分配
- 并发能力提升
- 响应时间优化

### 4. 运维友好
- 实时状态监控
- 配置热更新
- 详细的统计信息

## 下一步行动

1. **立即开始**：后端数据模型重构
2. **并行进行**：负载均衡算法实现
3. **随后开发**：前端管理界面
4. **最后完成**：测试和优化
