"""
MonIt 爬虫引擎模块 - API客户端模式

基于外部爬虫API服务的电商数据采集系统。

主要组件:
- engine: 爬虫引擎核心 (API客户端模式)
- api_client: HTTP API客户端
- schedulers: 任务调度
- exceptions: 异常处理
"""

__version__ = "2.0.0"
__author__ = "MonIt Development Team"

# 导入核心组件
from .engine.interfaces import ICrawlerEngine, ITaskScheduler, TaskStatus
from .engine.base import BaseCrawlerEngine
from .engine.crawler_engine import CrawlerEngine

# 导入API客户端
from .api_client import CrawlerAPIClient, APIConfig

# 导入异常类
from .exceptions.crawler_exceptions import CrawlerException

__all__ = [
    # Core interfaces
    'ICrawlerEngine',
    'ITaskScheduler',
    'TaskStatus',

    # Base classes
    'BaseCrawlerEngine',

    # Main engine
    'CrawlerEngine',

    # API client
    'CrawlerAPIClient',
    'APIConfig',

    # Exceptions
    'CrawlerException'
]