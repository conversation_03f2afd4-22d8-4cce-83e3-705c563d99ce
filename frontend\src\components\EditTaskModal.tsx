import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  TimePicker,
  Switch,
  message,
  Tabs,
  Row,
  Col,
  Alert,
  Table,
  Tag,
  Pagination,
  Spin
} from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getMonitoringTask, updateMonitoringTask, type MonitoringTask } from '../services/monitoringTaskApi';

const { Option } = Select;
const { TextArea } = Input;

interface EditTaskModalProps {
  visible: boolean;
  taskId: string | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const EditTaskModal: React.FC<EditTaskModalProps> = ({
  visible,
  taskId,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [task, setTask] = useState<MonitoringTask | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  // URL管理相关状态
  const [urlList, setUrlList] = useState([]);
  const [urlLoading, setUrlLoading] = useState(false);
  const [urlPagination, setUrlPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // 获取任务详情
  const fetchTaskDetail = async () => {
    if (!taskId) return;
    
    try {
      setLoading(true);
      const response = await getMonitoringTask(taskId);
      if (response.success) {
        const taskData = response.data;
        setTask(taskData);
        
        // 预填充表单
        form.setFieldsValue({
          name: taskData.name,
          description: taskData.description,
          scheduleType: taskData.schedule.type,
          scheduleTime: taskData.schedule.time ? dayjs(taskData.schedule.time, 'HH:mm') : null,
          scheduleInterval: taskData.schedule.interval,
          scheduleTimezone: taskData.schedule.timezone,
          enableRandomDelay: taskData.schedule.enable_random_delay || false,
          randomDelayMin: taskData.schedule.random_delay_min || 0,
          randomDelayMax: taskData.schedule.random_delay_max || 180,
          platform: taskData.config.platform,
          priority: taskData.config.priority,
          retryCount: taskData.config.retry_count,
          timeout: taskData.config.timeout,
          batchSize: taskData.config.batch_size
        });
      }
    } catch (error) {
      console.error('Failed to fetch task detail:', error);
      message.error('获取任务详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取任务URL列表
  const fetchTaskUrls = async (page = 1, pageSize = 20) => {
    if (!taskId) return;

    try {
      setUrlLoading(true);
      const response = await fetch(`/api/v1/monitoring-tasks/${taskId}/urls?page=${page}&page_size=${pageSize}`);
      const data = await response.json();

      if (data.success) {
        setUrlList(data.data || []);
        setUrlPagination({
          current: page,
          pageSize: pageSize,
          total: data.total || 0
        });
      } else {
        message.error('获取URL列表失败');
      }
    } catch (error) {
      console.error('获取URL列表失败:', error);
      message.error('获取URL列表失败');
    } finally {
      setUrlLoading(false);
    }
  };

  // 提交更新
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const updateData: any = {
        name: values.name,
        description: values.description
      };

      // 如果任务未运行，可以更新调度和配置
      if (!task?.is_running) {
        updateData.schedule = {
          type: values.scheduleType,
          time: values.scheduleTime ? values.scheduleTime.format('HH:mm') : undefined,
          interval: values.scheduleInterval,
          timezone: values.scheduleTimezone,
          enable_random_delay: values.enableRandomDelay || false,
          random_delay_min: values.randomDelayMin || 0,
          random_delay_max: values.randomDelayMax || 180
        };

        updateData.config = {
          platform: values.platform,
          priority: values.priority,
          enable_notifications: values.enableNotifications
        };
      }

      await updateMonitoringTask(taskId!, updateData);
      message.success('任务更新成功');
      onSuccess();
      handleCancel();
    } catch (error) {
      console.error('Failed to update task:', error);
      message.error('任务更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    form.resetFields();
    setTask(null);
    setActiveTab('basic');
    onCancel();
  };

  // 当弹窗打开时获取任务详情
  useEffect(() => {
    if (visible && taskId) {
      fetchTaskDetail();
    }
  }, [visible, taskId]);

  // 当切换到URL管理Tab时加载URL列表
  useEffect(() => {
    if (activeTab === 'urls' && taskId) {
      fetchTaskUrls(1, 20);
    }
  }, [activeTab, taskId]);

  return (
    <Modal
      title="编辑任务"
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={800}
      destroyOnHidden
    >
      {task?.is_running && (
        <Alert
          message="运行中的任务"
          description="任务正在运行中，只能编辑基本信息，不能修改调度配置和任务配置。"
          type="warning"
          icon={<ExclamationCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          scheduleType: 'daily',
          scheduleTimezone: 'Asia/Shanghai',
          platform: 'taobao',
          priority: 'medium',
          retryCount: 3,
          timeout: 300,
          batchSize: 10
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'basic',
              label: '基本信息',
              children: (
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item
                      label="任务名称"
                      name="name"
                      rules={[
                        { required: true, message: '请输入任务名称' },
                        { min: 1, max: 100, message: '任务名称长度为1-100个字符' }
                      ]}
                    >
                      <Input placeholder="请输入任务名称" />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item
                      label="任务描述"
                      name="description"
                      rules={[
                        { max: 500, message: '任务描述最多500个字符' }
                      ]}
                    >
                      <TextArea
                        rows={3}
                        placeholder="请输入任务描述（可选）"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )
            },
            {
              key: 'schedule',
              label: '调度配置',
              disabled: task?.is_running,
              children: (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="调度类型"
                        name="scheduleType"
                        rules={[{ required: true, message: '请选择调度类型' }]}
                      >
                        <Select placeholder="请选择调度类型">
                          <Option value="daily">每天</Option>
                          <Option value="weekly">每周</Option>
                          <Option value="hourly">每小时</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="时区"
                        name="scheduleTimezone"
                        rules={[{ required: true, message: '请选择时区' }]}
                      >
                        <Select placeholder="请选择时区">
                          <Option value="Asia/Shanghai">Asia/Shanghai</Option>
                          <Option value="UTC">UTC</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) =>
                      prevValues.scheduleType !== currentValues.scheduleType
                    }
                  >
                    {({ getFieldValue }) => {
                      const scheduleType = getFieldValue('scheduleType');

                      if (scheduleType === 'hourly') {
                        return (
                          <Form.Item
                            label="执行间隔（小时）"
                            name="scheduleInterval"
                            rules={[
                              { required: true, message: '请输入执行间隔' },
                              { type: 'number', min: 1, max: 24, message: '间隔时间为1-24小时' }
                            ]}
                          >
                            <InputNumber
                              min={1}
                              max={24}
                              placeholder="请输入间隔小时数"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        );
                      } else {
                        return (
                          <Form.Item
                            label="执行时间"
                            name="scheduleTime"
                            rules={[{ required: true, message: '请选择执行时间' }]}
                          >
                            <TimePicker
                              format="HH:mm"
                              placeholder="请选择执行时间"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        );
                      }
                    }}
                  </Form.Item>

                  {/* 随机延迟配置 */}
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        label="启用随机延迟"
                        name="enableRandomDelay"
                        valuePropName="checked"
                      >
                        <Switch
                          checkedChildren="启用"
                          unCheckedChildren="禁用"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="最小延迟(分钟)"
                        name="randomDelayMin"
                        rules={[
                          { type: 'number', min: 0, max: 1440, message: '延迟时间为0-1440分钟' }
                        ]}
                      >
                        <InputNumber
                          min={0}
                          max={1440}
                          placeholder="最小延迟"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="最大延迟(分钟)"
                        name="randomDelayMax"
                        rules={[
                          { type: 'number', min: 0, max: 1440, message: '延迟时间为0-1440分钟' }
                        ]}
                      >
                        <InputNumber
                          min={0}
                          max={1440}
                          placeholder="最大延迟"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )
            },
            {
              key: 'config',
              label: '任务配置',
              disabled: task?.is_running,
              children: (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="平台类型"
                      name="platform"
                      rules={[{ required: true, message: '请选择平台类型' }]}
                    >
                      <Select placeholder="请选择平台类型">
                        <Option value="taobao">淘宝</Option>
                        <Option value="tmall">天猫</Option>
                        <Option value="jd">京东</Option>
                        <Option value="pdd">拼多多</Option>
                        <Option value="mercadolibre">MercadoLibre</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="优先级"
                      name="priority"
                      rules={[{ required: true, message: '请选择优先级' }]}
                    >
                      <Select placeholder="请选择优先级">
                        <Option value="high">高</Option>
                        <Option value="medium">中</Option>
                        <Option value="low">低</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="启用通知"
                      name="enableNotifications"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              )
            },
            {
              key: 'urls',
              label: 'URL管理',
              children: (
                <div>
                  <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>管理任务包含的监控URL</span>
                    <span style={{ color: '#666' }}>
                      总计: {urlPagination.total} 个URL
                    </span>
                  </div>

                  <Spin spinning={urlLoading}>
                    <Table
                      dataSource={urlList}
                      rowKey="id"
                      pagination={false}
                      size="small"
                      scroll={{ y: 400 }}
                      columns={[
                        {
                          title: 'URL',
                          dataIndex: 'url',
                          key: 'url',
                          ellipsis: true,
                          render: (url: string) => (
                            <a href={url} target="_blank" rel="noopener noreferrer" title={url}>
                              {url.length > 80 ? `${url.substring(0, 80)}...` : url}
                            </a>
                          )
                        },
                        {
                          title: '平台',
                          dataIndex: 'platform',
                          key: 'platform',
                          width: 100,
                          render: (platform: string) => (
                            <Tag color={platform === 'mercadolibre' ? 'blue' : 'default'}>
                              {platform}
                            </Tag>
                          )
                        },
                        {
                          title: '状态',
                          dataIndex: 'status',
                          key: 'status',
                          width: 80,
                          render: (status: string) => (
                            <Tag color={status === 'active' ? 'green' : 'red'}>
                              {status === 'active' ? '活跃' : '禁用'}
                            </Tag>
                          )
                        },
                        {
                          title: '添加时间',
                          dataIndex: 'added_at',
                          key: 'added_at',
                          width: 150,
                          render: (time: string) => {
                            try {
                              return new Date(time).toLocaleString('zh-CN');
                            } catch {
                              return time;
                            }
                          }
                        }
                      ]}
                    />
                  </Spin>

                  {urlPagination.total > urlPagination.pageSize && (
                    <div style={{ marginTop: 16, textAlign: 'center' }}>
                      <Pagination
                        current={urlPagination.current}
                        pageSize={urlPagination.pageSize}
                        total={urlPagination.total}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                        onChange={(page, pageSize) => {
                          fetchTaskUrls(page, pageSize);
                        }}
                      />
                    </div>
                  )}
                </div>
              )
            }
          ]}
        />
      </Form>
    </Modal>
  );
};

export default EditTaskModal;
