#!/usr/bin/env python3
"""
简化的持久化TaskManager测试

测试核心持久化功能，避免复杂的依赖问题
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class SimplePersistenceTest:
    """简化的持久化测试"""
    
    def __init__(self):
        self.redis_url = "redis://localhost:6379/0"
        self.redis_client = None
        
        # Redis键名
        self.pending_queue_key = "taskmanager:pending_batches"
        self.running_hash_key = "taskmanager:running_batches"
        self.completed_set_key = "taskmanager:completed_batches"
        self.failed_set_key = "taskmanager:failed_batches"
        
        # 测试数据
        self.test_batches = []
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up test environment...")
        
        # 连接Redis
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        logger.info("✅ Connected to Redis")
        
        # 清理测试数据
        await self._cleanup_test_data()
        
        # 生成测试批次数据
        self._generate_test_batches()
        
        logger.info("✅ Test environment setup completed")
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 Cleaning up test environment...")
        
        if self.redis_client:
            await self._cleanup_test_data()
            await self.redis_client.close()
        
        logger.info("✅ Test environment cleaned up")
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        test_keys = [
            self.pending_queue_key,
            self.running_hash_key,
            self.completed_set_key,
            self.failed_set_key
        ]
        
        for key in test_keys:
            await self.redis_client.delete(key)
    
    def _generate_test_batches(self):
        """生成测试批次数据"""
        self.test_batches = []
        
        for i in range(5):
            batch_data = {
                "batch_id": f"test_batch_{i:03d}_{int(time.time())}",
                "urls": [f"https://example.com/product/{j}" for j in range(i*10, (i+1)*10)],
                "platform": "mercadolibre",
                "priority": ["urgent", "high", "normal", "low"][i % 4],
                "estimated_duration": 30.0 + i * 10,
                "created_at": datetime.now().isoformat(),
                "metadata": {
                    "task_id": i + 1,
                    "submission_id": f"submission_{i}",
                    "test_batch": True,
                    "batch_index": i
                }
            }
            self.test_batches.append(batch_data)
        
        logger.info(f"✅ Generated {len(self.test_batches)} test batches")
    
    async def test_redis_queue_operations(self):
        """测试1: Redis队列操作"""
        logger.info("\n📤 Test 1: Redis Queue Operations")
        logger.info("=" * 60)
        
        try:
            # 测试待处理队列
            logger.info("Testing pending queue operations...")
            
            # 添加批次到待处理队列
            for batch in self.test_batches:
                await self.redis_client.lpush(self.pending_queue_key, json.dumps(batch))
            
            # 检查队列长度
            queue_length = await self.redis_client.llen(self.pending_queue_key)
            logger.info(f"✅ Added {len(self.test_batches)} batches, queue length: {queue_length}")
            
            # 从队列中取出批次
            retrieved_batches = []
            for i in range(len(self.test_batches)):
                batch_data = await self.redis_client.rpop(self.pending_queue_key)
                if batch_data:
                    batch = json.loads(batch_data)
                    retrieved_batches.append(batch)
            
            logger.info(f"✅ Retrieved {len(retrieved_batches)} batches from queue")
            
            # 验证数据一致性
            original_ids = {batch["batch_id"] for batch in self.test_batches}
            retrieved_ids = {batch["batch_id"] for batch in retrieved_batches}
            
            consistency_check = original_ids == retrieved_ids
            logger.info(f"✅ Data consistency check: {'PASS' if consistency_check else 'FAIL'}")
            
            self.test_results["queue_operations"] = {
                "success": True,
                "original_count": len(self.test_batches),
                "retrieved_count": len(retrieved_batches),
                "consistency": consistency_check
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Queue operations test failed: {e}")
            self.test_results["queue_operations"] = {"success": False, "error": str(e)}
            return False
    
    async def test_running_batch_hash(self):
        """测试2: 运行批次哈希操作"""
        logger.info("\n⚙️ Test 2: Running Batch Hash Operations")
        logger.info("=" * 60)
        
        try:
            # 模拟运行批次数据
            running_batches = {}
            
            for i, batch in enumerate(self.test_batches[:3]):  # 只用前3个
                execution_data = {
                    "batch_id": batch["batch_id"],
                    "celery_task_id": f"celery_task_{i}_{int(time.time())}",
                    "batch": batch,
                    "started_at": datetime.now().isoformat(),
                    "status": "running",
                    "result": None,
                    "error": None
                }
                
                running_batches[batch["batch_id"]] = execution_data
                
                # 添加到Redis哈希
                await self.redis_client.hset(
                    self.running_hash_key,
                    batch["batch_id"],
                    json.dumps(execution_data)
                )
            
            logger.info(f"✅ Added {len(running_batches)} running batches to hash")
            
            # 检查哈希长度
            hash_length = await self.redis_client.hlen(self.running_hash_key)
            logger.info(f"✅ Hash length: {hash_length}")
            
            # 获取所有运行批次
            all_running = await self.redis_client.hgetall(self.running_hash_key)
            retrieved_running = {}
            
            for batch_id, execution_data in all_running.items():
                batch_id = batch_id.decode() if isinstance(batch_id, bytes) else batch_id
                execution = json.loads(execution_data)
                retrieved_running[batch_id] = execution
            
            logger.info(f"✅ Retrieved {len(retrieved_running)} running batches")
            
            # 验证数据一致性
            original_ids = set(running_batches.keys())
            retrieved_ids = set(retrieved_running.keys())
            
            consistency_check = original_ids == retrieved_ids
            logger.info(f"✅ Running batch consistency: {'PASS' if consistency_check else 'FAIL'}")
            
            # 模拟完成一些批次
            completed_batches = []
            failed_batches = []
            
            for i, batch_id in enumerate(list(running_batches.keys())[:2]):
                # 从运行哈希中移除
                await self.redis_client.hdel(self.running_hash_key, batch_id)
                
                # 添加到完成或失败集合
                if i % 2 == 0:
                    await self.redis_client.sadd(self.completed_set_key, batch_id)
                    completed_batches.append(batch_id)
                else:
                    await self.redis_client.sadd(self.failed_set_key, batch_id)
                    failed_batches.append(batch_id)
            
            # 检查最终状态
            final_running = await self.redis_client.hlen(self.running_hash_key)
            final_completed = await self.redis_client.scard(self.completed_set_key)
            final_failed = await self.redis_client.scard(self.failed_set_key)
            
            logger.info(f"✅ Final state: {final_running} running, {final_completed} completed, {final_failed} failed")
            
            self.test_results["running_hash"] = {
                "success": True,
                "original_running": len(running_batches),
                "final_running": final_running,
                "completed": final_completed,
                "failed": final_failed,
                "consistency": consistency_check
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Running batch hash test failed: {e}")
            self.test_results["running_hash"] = {"success": False, "error": str(e)}
            return False
    
    async def test_data_format_validation(self):
        """测试3: 数据格式验证"""
        logger.info("\n🔍 Test 3: Data Format Validation")
        logger.info("=" * 60)
        
        try:
            # 检查待处理队列中的数据格式
            pending_sample = await self.redis_client.lrange(self.pending_queue_key, 0, 0)
            
            format_checks = {}
            
            if pending_sample:
                sample_data = json.loads(pending_sample[0])
                logger.info(f"📊 Pending batch sample: {sample_data['batch_id']}")
                
                # 检查必需字段
                required_fields = ["batch_id", "urls", "platform", "priority", "created_at", "metadata"]
                for field in required_fields:
                    format_checks[f"has_{field}"] = field in sample_data
                
                # 检查数据类型
                format_checks["urls_is_list"] = isinstance(sample_data.get("urls"), list)
                format_checks["metadata_is_dict"] = isinstance(sample_data.get("metadata"), dict)
                format_checks["priority_is_string"] = isinstance(sample_data.get("priority"), str)
                
                logger.info("✅ Pending batch format validation completed")
            
            # 检查运行哈希中的数据格式
            running_sample = await self.redis_client.hgetall(self.running_hash_key)
            
            if running_sample:
                first_key = list(running_sample.keys())[0]
                sample_data = json.loads(running_sample[first_key])
                logger.info(f"📊 Running batch sample: {sample_data['batch_id']}")
                
                # 检查执行数据字段
                execution_fields = ["batch_id", "celery_task_id", "batch", "started_at", "status"]
                for field in execution_fields:
                    format_checks[f"execution_has_{field}"] = field in sample_data
                
                # 检查嵌套批次数据
                if "batch" in sample_data:
                    batch_data = sample_data["batch"]
                    format_checks["nested_batch_valid"] = isinstance(batch_data, dict) and "batch_id" in batch_data
                
                logger.info("✅ Running batch format validation completed")
            
            # 检查集合数据
            completed_sample = await self.redis_client.srandmember(self.completed_set_key, 1)
            failed_sample = await self.redis_client.srandmember(self.failed_set_key, 1)
            
            if completed_sample:
                format_checks["completed_is_string"] = isinstance(completed_sample[0], (str, bytes))
            
            if failed_sample:
                format_checks["failed_is_string"] = isinstance(failed_sample[0], (str, bytes))
            
            # 统计格式检查结果
            total_checks = len(format_checks)
            passed_checks = sum(1 for check in format_checks.values() if check)
            
            logger.info(f"✅ Format validation: {passed_checks}/{total_checks} checks passed")
            
            # 显示详细检查结果
            for check_name, passed in format_checks.items():
                status = "✅" if passed else "❌"
                logger.info(f"  {status} {check_name}")
            
            self.test_results["format_validation"] = {
                "success": True,
                "total_checks": total_checks,
                "passed_checks": passed_checks,
                "format_checks": format_checks
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Data format validation test failed: {e}")
            self.test_results["format_validation"] = {"success": False, "error": str(e)}
            return False
    
    async def test_persistence_simulation(self):
        """测试4: 持久化模拟"""
        logger.info("\n💾 Test 4: Persistence Simulation")
        logger.info("=" * 60)
        
        try:
            # 模拟完整的任务生命周期
            
            # 1. 添加新任务到待处理队列
            new_batch = {
                "batch_id": f"simulation_batch_{int(time.time())}",
                "urls": ["https://example.com/sim1", "https://example.com/sim2"],
                "platform": "mercadolibre",
                "priority": "normal",
                "created_at": datetime.now().isoformat(),
                "metadata": {"simulation": True}
            }
            
            await self.redis_client.lpush(self.pending_queue_key, json.dumps(new_batch))
            logger.info(f"✅ Added simulation batch: {new_batch['batch_id']}")
            
            # 2. 模拟任务开始执行
            batch_data = await self.redis_client.rpop(self.pending_queue_key)
            batch = json.loads(batch_data)
            
            execution_data = {
                "batch_id": batch["batch_id"],
                "celery_task_id": f"sim_celery_{int(time.time())}",
                "batch": batch,
                "started_at": datetime.now().isoformat(),
                "status": "running"
            }
            
            await self.redis_client.hset(
                self.running_hash_key,
                batch["batch_id"],
                json.dumps(execution_data)
            )
            logger.info(f"✅ Started execution: {batch['batch_id']}")
            
            # 3. 模拟任务完成
            await asyncio.sleep(1)  # 模拟处理时间
            
            await self.redis_client.hdel(self.running_hash_key, batch["batch_id"])
            await self.redis_client.sadd(self.completed_set_key, batch["batch_id"])
            logger.info(f"✅ Completed execution: {batch['batch_id']}")
            
            # 4. 验证最终状态
            final_pending = await self.redis_client.llen(self.pending_queue_key)
            final_running = await self.redis_client.hlen(self.running_hash_key)
            final_completed = await self.redis_client.scard(self.completed_set_key)
            
            logger.info(f"✅ Final simulation state: {final_pending} pending, {final_running} running, {final_completed} completed")
            
            self.test_results["persistence_simulation"] = {
                "success": True,
                "simulation_batch_id": batch["batch_id"],
                "final_state": {
                    "pending": final_pending,
                    "running": final_running,
                    "completed": final_completed
                }
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Persistence simulation test failed: {e}")
            self.test_results["persistence_simulation"] = {"success": False, "error": str(e)}
            return False
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 SIMPLE PERSISTENCE TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        logger.info("\n📋 Test Details:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            logger.info(f"  {status} {test_name}")
            
            if not result.get("success", False) and "error" in result:
                logger.info(f"    Error: {result['error']}")
        
        # 显示关键指标
        if "format_validation" in self.test_results and self.test_results["format_validation"]["success"]:
            validation = self.test_results["format_validation"]
            logger.info(f"\n🔍 Format Validation: {validation['passed_checks']}/{validation['total_checks']} checks passed")


async def main():
    """主测试函数"""
    print("🧪 Simple Persistence Test Suite")
    print("=" * 60)
    
    test_suite = SimplePersistenceTest()
    
    try:
        # 设置测试环境
        await test_suite.setup()
        
        # 运行测试
        test1_success = await test_suite.test_redis_queue_operations()
        test2_success = await test_suite.test_running_batch_hash()
        test3_success = await test_suite.test_data_format_validation()
        test4_success = await test_suite.test_persistence_simulation()
        
        # 打印测试总结
        test_suite.print_test_summary()
        
        # 总体结果
        all_passed = test1_success and test2_success and test3_success and test4_success
        
        if all_passed:
            logger.info("\n🎉 ALL TESTS PASSED! Redis persistence operations are working correctly!")
            logger.info("\n💡 This confirms that:")
            logger.info("   ✅ Redis queue operations work properly")
            logger.info("   ✅ Data format is consistent and valid")
            logger.info("   ✅ Task lifecycle simulation works")
            logger.info("   ✅ Ready for PersistentTaskManager integration!")
        else:
            logger.info("\n⚠️ Some tests failed. Please check the details above.")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试环境
        await test_suite.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
