# MonIt项目当前任务拆分 - Task 03: 完成任务执行控制功能

## 任务概述
完成监控任务系统工作流重新设计的Task 04阶段3，实现任务执行控制功能，包括手动执行、任务终止和执行状态监控。

## 当前状态
- 状态: 待开始
- 依赖: Task 02 定时调度系统集成完成
- 预计完成时间: 2025年7月12日

## 任务背景
当前任务管理功能已完成90%，还需要完成最后的任务执行控制功能：
- 手动执行任务功能
- 任务终止和暂停功能  
- 执行状态实时监控
- 执行结果展示和分析

## 主要功能模块

### 1. 手动执行任务功能 (40%)
**目标**: 支持用户手动触发任务执行
**具体任务**:
- 实现立即执行API接口
- 添加执行前置条件检查
- 支持部分URL执行
- 实现执行队列管理

**API设计**:
```typescript
// 手动执行接口
POST /api/v1/monitoring-tasks/{id}/execute
{
  "execution_type": "full" | "partial",
  "url_ids"?: string[],        // 部分执行时指定URL
  "priority": "high" | "normal" | "low",
  "force": boolean             // 是否强制执行
}
```

**前端交互**:
- 任务详情页添加"立即执行"按钮
- 支持选择部分URL执行
- 显示执行前置条件检查结果
- 实时显示执行进度

### 2. 任务终止和暂停功能 (30%)
**目标**: 支持任务的终止和暂停操作
**具体任务**:
- 实现任务终止API接口
- 添加任务暂停/恢复功能
- 支持优雅停止和强制停止
- 实现终止状态管理

**状态管理**:
```typescript
interface TaskExecutionStatus {
  id: string;
  task_id: string;
  status: 'pending' | 'running' | 'paused' | 'terminated' | 'completed' | 'failed';
  started_at: string;
  progress: {
    total_urls: number;
    processed_urls: number;
    successful_urls: number;
    failed_urls: number;
    current_batch: number;
    total_batches: number;
  };
  can_terminate: boolean;
  can_pause: boolean;
}
```

### 3. 执行状态实时监控 (20%)
**目标**: 实时监控任务执行状态和进度
**具体任务**:
- WebSocket实时状态推送
- 执行进度可视化展示
- 错误信息实时显示
- 性能指标实时监控

**监控指标**:
- 执行进度百分比
- 当前处理的URL
- 成功/失败URL统计
- 执行速度 (URLs/分钟)
- 错误率趋势
- 资源使用情况

### 4. 执行结果展示和分析 (10%)
**目标**: 详细的执行结果展示和分析功能
**具体任务**:
- 执行结果详情页面
- 错误分析和分类
- 性能分析报告
- 历史执行对比

## 技术实现设计

### 执行控制器架构
```python
class TaskExecutionController:
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.execution_id = str(uuid.uuid4())
        self.status_manager = TaskStatusManager()
        self.websocket_manager = WebSocketManager()
    
    async def execute_task(self, execution_config: ExecutionConfig):
        """执行任务主流程"""
        try:
            # 1. 前置条件检查
            await self.pre_execution_check()
            
            # 2. 初始化执行状态
            await self.initialize_execution()
            
            # 3. 执行任务
            await self.run_task_execution()
            
            # 4. 完成处理
            await self.finalize_execution()
            
        except Exception as e:
            await self.handle_execution_error(e)
    
    async def terminate_task(self, force: bool = False):
        """终止任务执行"""
        if force:
            await self.force_terminate()
        else:
            await self.graceful_terminate()
```

### WebSocket状态推送
```python
class ExecutionStatusWebSocket:
    async def send_status_update(self, execution_id: str, status_data: dict):
        """推送执行状态更新"""
        message = {
            "type": "execution_status_update",
            "execution_id": execution_id,
            "timestamp": datetime.utcnow().isoformat(),
            "data": status_data
        }
        await self.broadcast_to_subscribers(execution_id, message)
    
    async def send_progress_update(self, execution_id: str, progress: dict):
        """推送执行进度更新"""
        message = {
            "type": "execution_progress_update", 
            "execution_id": execution_id,
            "progress": progress
        }
        await self.broadcast_to_subscribers(execution_id, message)
```

### 前端状态管理
```typescript
// 执行状态管理Store
interface ExecutionState {
  activeExecutions: Map<string, TaskExecutionStatus>;
  executionHistory: ExecutionRecord[];
  websocketConnected: boolean;
}

class ExecutionManager {
  async startExecution(taskId: string, config: ExecutionConfig) {
    // 发起执行请求
    const response = await api.post(`/monitoring-tasks/${taskId}/execute`, config);
    
    // 订阅WebSocket状态更新
    this.subscribeToExecution(response.execution_id);
    
    return response;
  }
  
  async terminateExecution(executionId: string, force: boolean = false) {
    await api.post(`/executions/${executionId}/terminate`, { force });
  }
  
  subscribeToExecution(executionId: string) {
    this.websocket.subscribe(`execution:${executionId}`, (message) => {
      this.handleExecutionUpdate(message);
    });
  }
}
```

## 前端界面设计

### 任务详情页执行控制区域
```tsx
const TaskExecutionControls: React.FC<{task: MonitoringTask}> = ({task}) => {
  const [executionStatus, setExecutionStatus] = useState<TaskExecutionStatus | null>(null);
  
  return (
    <Card title="执行控制">
      <Space direction="vertical" style={{width: '100%'}}>
        {/* 执行按钮区域 */}
        <Space>
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={handleManualExecute}
            disabled={executionStatus?.status === 'running'}
          >
            立即执行
          </Button>
          
          <Button 
            danger
            icon={<StopOutlined />}
            onClick={handleTerminate}
            disabled={!executionStatus || executionStatus.status !== 'running'}
          >
            终止执行
          </Button>
          
          <Button 
            icon={<PauseCircleOutlined />}
            onClick={handlePause}
            disabled={!executionStatus?.can_pause}
          >
            暂停执行
          </Button>
        </Space>
        
        {/* 执行状态显示 */}
        {executionStatus && (
          <ExecutionStatusDisplay status={executionStatus} />
        )}
        
        {/* 执行进度条 */}
        {executionStatus?.status === 'running' && (
          <Progress 
            percent={executionStatus.progress.processed_urls / executionStatus.progress.total_urls * 100}
            status="active"
            format={(percent) => `${executionStatus.progress.processed_urls}/${executionStatus.progress.total_urls}`}
          />
        )}
      </Space>
    </Card>
  );
};
```

### 执行历史页面
```tsx
const ExecutionHistory: React.FC = () => {
  return (
    <div>
      <Table 
        columns={executionHistoryColumns}
        dataSource={executionHistory}
        expandable={{
          expandedRowRender: (record) => <ExecutionDetails execution={record} />
        }}
      />
    </div>
  );
};
```

## 实施计划

### 第1阶段: 手动执行功能 (2天)
**7月10日-7月11日**
- [ ] 实现手动执行API接口
- [ ] 添加前置条件检查逻辑
- [ ] 开发前端执行控制界面
- [ ] 测试手动执行功能

### 第2阶段: 终止和暂停功能 (1天)
**7月12日**
- [ ] 实现任务终止API接口
- [ ] 添加暂停/恢复功能
- [ ] 完善状态管理机制
- [ ] 测试终止和暂停功能

### 第3阶段: 状态监控和结果展示 (1天)
**7月12日**
- [ ] 完善WebSocket状态推送
- [ ] 实现执行结果详情页
- [ ] 添加性能分析功能
- [ ] 集成测试和优化

## 验收标准

### 功能验收
- [ ] 手动执行功能正常工作
- [ ] 任务终止和暂停功能正常
- [ ] 执行状态实时更新准确
- [ ] 执行结果展示完整

### 用户体验验收
- [ ] 界面操作直观易用
- [ ] 状态反馈及时准确
- [ ] 错误信息清晰明确
- [ ] 执行进度可视化良好

### 性能验收
- [ ] 状态更新延迟 < 2秒
- [ ] 终止操作响应 < 5秒
- [ ] 界面渲染流畅
- [ ] 内存使用合理

## 风险评估

### 技术风险
- **并发控制**: 多个执行请求的并发控制
- **状态一致性**: 分布式环境下状态一致性
- **资源竞争**: 执行资源的竞争和分配

### 用户体验风险
- **操作复杂度**: 执行控制操作可能过于复杂
- **状态理解**: 用户可能难以理解执行状态
- **错误处理**: 错误情况下的用户引导

## 测试策略

### 功能测试
- 手动执行各种场景测试
- 终止和暂停功能测试
- 状态更新准确性测试
- 错误处理机制测试

### 集成测试
- 与定时调度系统集成测试
- WebSocket通信测试
- 前后端集成测试

### 用户体验测试
- 界面交互测试
- 响应时间测试
- 错误提示测试

## 下一步行动
1. 立即开始手动执行API开发
2. 并行开发前端执行控制界面
3. 持续测试和用户体验优化
4. 准备完整功能集成测试

## 依赖关系
- 依赖: Task 02 定时调度系统集成完成
- 被依赖: 系统集成与测试
- 关联: 监控任务系统工作流重新设计完成

## 成功指标
- 执行控制功能完整度: 100%
- 用户操作成功率: >95%
- 状态更新准确率: >99%
- 用户满意度: >90%
