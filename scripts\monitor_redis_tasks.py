#!/usr/bin/env python3
"""
Redis任务监控脚本
用于实时查看Redis中的任务数据
"""

import redis
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class RedisTaskMonitor:
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        """初始化Redis连接"""
        self.redis_client = redis.from_url(redis_url)
        
    def get_all_task_keys(self) -> List[str]:
        """获取所有任务相关的键"""
        patterns = [
            "task:*",
            "taskmanager:*",
            "*task*",
            "celery-task-meta-*"
        ]
        
        all_keys = set()
        for pattern in patterns:
            keys = self.redis_client.keys(pattern)
            all_keys.update([key.decode() if isinstance(key, bytes) else key for key in keys])
        
        return sorted(list(all_keys))
    
    def get_key_info(self, key: str) -> Dict[str, Any]:
        """获取键的详细信息"""
        try:
            key_type = self.redis_client.type(key).decode()
            ttl = self.redis_client.ttl(key)
            
            info = {
                "key": key,
                "type": key_type,
                "ttl": ttl if ttl > 0 else "永久",
                "data": None
            }
            
            if key_type == "string":
                data = self.redis_client.get(key)
                if data:
                    try:
                        info["data"] = json.loads(data.decode())
                    except:
                        info["data"] = data.decode()
            
            elif key_type == "hash":
                hash_data = self.redis_client.hgetall(key)
                info["data"] = {k.decode(): v.decode() for k, v in hash_data.items()}
            
            elif key_type == "list":
                list_data = self.redis_client.lrange(key, 0, -1)
                info["data"] = [item.decode() for item in list_data]
            
            elif key_type == "set":
                set_data = self.redis_client.smembers(key)
                info["data"] = [item.decode() for item in set_data]
            
            elif key_type == "zset":
                zset_data = self.redis_client.zrange(key, 0, -1, withscores=True)
                info["data"] = [(item.decode(), score) for item, score in zset_data]
            
            return info
            
        except Exception as e:
            return {
                "key": key,
                "type": "error",
                "ttl": "N/A",
                "data": f"Error: {str(e)}"
            }
    
    def print_task_summary(self):
        """打印任务摘要"""
        keys = self.get_all_task_keys()
        
        print(f"\n{'='*60}")
        print(f"Redis任务监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        if not keys:
            print("🔍 Redis中暂无任务相关数据")
            return
        
        print(f"📊 发现 {len(keys)} 个任务相关键:")
        
        # 按类型分组
        task_types = {}
        for key in keys:
            if key.startswith("task:"):
                task_types.setdefault("Celery任务结果", []).append(key)
            elif key.startswith("taskmanager:"):
                task_types.setdefault("TaskManager数据", []).append(key)
            elif key.startswith("celery-task-meta-"):
                task_types.setdefault("Celery元数据", []).append(key)
            else:
                task_types.setdefault("其他任务数据", []).append(key)
        
        for task_type, type_keys in task_types.items():
            print(f"\n📋 {task_type} ({len(type_keys)}个):")
            for key in type_keys[:5]:  # 只显示前5个
                info = self.get_key_info(key)
                print(f"  🔑 {key}")
                print(f"     类型: {info['type']}, TTL: {info['ttl']}")
                
                # 显示部分数据
                if info['data']:
                    if isinstance(info['data'], dict):
                        if 'status' in info['data']:
                            print(f"     状态: {info['data']['status']}")
                        if 'url' in info['data']:
                            url = info['data']['url']
                            if isinstance(url, str) and len(url) > 50:
                                url = url[:50] + "..."
                            print(f"     URL: {url}")
                    elif isinstance(info['data'], str) and len(info['data']) > 100:
                        print(f"     数据: {info['data'][:100]}...")
                    else:
                        print(f"     数据: {info['data']}")
            
            if len(type_keys) > 5:
                print(f"  ... 还有 {len(type_keys) - 5} 个键")
    
    def print_detailed_info(self, key_pattern: str = None):
        """打印详细信息"""
        keys = self.get_all_task_keys()
        
        if key_pattern:
            keys = [k for k in keys if key_pattern in k]
        
        for key in keys:
            info = self.get_key_info(key)
            print(f"\n🔑 键: {key}")
            print(f"   类型: {info['type']}")
            print(f"   TTL: {info['ttl']}")
            print(f"   数据: {json.dumps(info['data'], indent=2, ensure_ascii=False)}")
    
    def monitor_continuous(self, interval: int = 5):
        """持续监控模式"""
        print("🔄 开始持续监控模式 (Ctrl+C 退出)")
        try:
            while True:
                self.print_task_summary()
                print(f"\n⏰ {interval}秒后刷新...")
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")

def main():
    monitor = RedisTaskMonitor()
    
    import sys
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "summary":
            monitor.print_task_summary()
        elif command == "detail":
            pattern = sys.argv[2] if len(sys.argv) > 2 else None
            monitor.print_detailed_info(pattern)
        elif command == "monitor":
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 5
            monitor.monitor_continuous(interval)
        else:
            print("用法: python monitor_redis_tasks.py [summary|detail|monitor] [参数]")
    else:
        monitor.print_task_summary()

if __name__ == "__main__":
    main()
