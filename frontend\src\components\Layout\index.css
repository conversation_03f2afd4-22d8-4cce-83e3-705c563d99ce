.app-layout {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  background: #ffffff !important;
}

.app-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border-bottom: 1px solid #f5f5f5;
  margin-bottom: 16px;
  background: #fafafa;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: 700;
  margin-right: 12px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.app-logo span {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  letter-spacing: 0.5px;
}

.app-main {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.app-layout .ant-layout-sider-collapsed + .app-main {
  margin-left: 80px;
}

.app-header {
  background: #ffffff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 99;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.sidebar-trigger:hover {
  color: #1890ff;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  animation: pulse 2s infinite;
}

.status-indicator.connected {
  background-color: #52c41a;
}

.status-indicator.disconnected {
  background-color: #ff4d4f;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.app-content {
  margin: 24px;
  padding: 0;
  background: transparent;
  min-height: calc(100vh - 112px);
  width: calc(100% - 48px);
  max-width: calc(100% - 48px);
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 暗色主题 */
.app-layout.dark .app-header {
  background: #262626;
  border-bottom: 1px solid #303030;
}

.app-layout.dark .app-content {
  background: #1f1f1f !important;
  color: #fff !important;
}

.app-layout.dark .ant-layout-content {
  background: #1f1f1f !important;
  color: #fff !important;
}

.app-layout.dark .app-logo {
  background: #262626 !important;
  border-bottom: 1px solid #303030;
  color: #d9d9d9 !important;
}

.app-layout.dark .app-logo span {
  color: #1890ff !important;
}

.app-layout.dark .app-logo .anticon {
  color: #1890ff !important;
}

.app-layout.dark .app-sider {
  background: #262626 !important;
}

.app-layout.dark .ant-layout-sider {
  background: #262626 !important;
}

.app-layout.dark .ant-layout-sider-children {
  background: #262626 !important;
}

.app-layout.dark .ant-menu {
  background: #262626 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-menu-item {
  background: #262626 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-menu-item:hover {
  background: #303030 !important;
  color: #1890ff !important;
}

.app-layout.dark .ant-menu-item-selected {
  background: #1890ff !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-menu-item-selected:hover {
  background: #40a9ff !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-menu-item .anticon {
  color: inherit !important;
}

.app-layout.dark .ant-menu-inline {
  background: #262626 !important;
  border-right: none !important;
}

.app-layout.dark .user-info {
  color: #d9d9d9;
}

.app-layout.dark .user-info:hover {
  background-color: #303030;
}

.app-layout.dark .user-info .ant-typography {
  color: #d9d9d9 !important;
}

.app-layout.dark .user-info .ant-avatar {
  background: #434343;
  color: #d9d9d9;
}

.app-layout.dark .sidebar-trigger {
  color: #d9d9d9;
}

.app-layout.dark .sidebar-trigger:hover {
  color: #1890ff;
  background: #303030;
}

.app-layout.dark .header-left {
  color: #d9d9d9;
}

.app-layout.dark .header-left .ant-btn {
  color: #d9d9d9;
}

.app-layout.dark .header-left .ant-btn:hover {
  color: #1890ff;
  background: #303030;
}

.app-layout.dark .connection-status {
  color: #d9d9d9;
}

.app-layout.dark .connection-status .ant-typography {
  color: #d9d9d9 !important;
}

.app-layout.dark .status-indicator {
  border: 1px solid #434343;
}

.app-layout.dark .status-indicator.connected {
  background: #52c41a;
  box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);
}

.app-layout.dark .status-indicator.disconnected {
  background: #ff4d4f;
  box-shadow: 0 0 6px rgba(255, 77, 79, 0.4);
}

.app-layout.dark .header-right {
  color: #d9d9d9;
}

.app-layout.dark .header-right .ant-btn {
  color: #d9d9d9;
}

.app-layout.dark .header-right .ant-btn:hover {
  color: #1890ff;
  background: #303030;
}

.app-layout.dark .header-right .anticon {
  color: #d9d9d9;
}

.app-layout.dark .header-right .ant-badge {
  color: #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sider {
    position: fixed;
    z-index: 1000;
    /* 侧边栏在小屏幕下覆盖在内容上方 */
  }

  .app-main {
    /* 始终为收起的侧边栏留出空间 */
    margin-left: 80px;
  }

  .app-layout .ant-layout-sider-collapsed + .app-main {
    /* 收起状态下保持80px的左边距 */
    margin-left: 80px;
  }

  /* 当侧边栏展开时，不改变主内容区域的位置 */
  .app-layout .ant-layout-sider:not(.ant-layout-sider-collapsed) + .app-main {
    margin-left: 80px; /* 保持80px，让侧边栏覆盖在上方 */
  }

  .app-header {
    padding: 0 16px;
  }

  .app-content {
    margin: 8px;
    padding: 0;
    /* 计算宽度时考虑80px的侧边栏空间 */
    width: calc(100% - 16px);
    max-width: calc(100% - 16px);
  }

  .header-left {
    gap: 8px;
  }

  .connection-status {
    display: none;
  }
}

/* 超小屏幕优化 */
@media (max-width: 576px) {
  .app-main {
    /* 在超小屏幕下，为收起的侧边栏留出更少空间 */
    margin-left: 60px;
  }

  .app-layout .ant-layout-sider-collapsed + .app-main {
    margin-left: 60px;
  }

  .app-layout .ant-layout-sider:not(.ant-layout-sider-collapsed) + .app-main {
    margin-left: 60px;
  }

  .app-content {
    margin: 4px;
    width: calc(100% - 8px);
    max-width: calc(100% - 8px);
  }

  .app-header {
    padding: 0 8px;
  }
}

/* 菜单样式优化 */
.ant-menu-item {
  margin: 4px 8px;
  border-radius: 8px;
  height: 44px;
  line-height: 44px;
  transition: all 0.2s ease;
}

.ant-menu-item:hover {
  background-color: #f0f0f0 !important;
}

.ant-menu-item-selected {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
  font-weight: 600 !important;
}

.ant-menu-item-selected .ant-menu-item-icon,
.ant-menu-item-selected span {
  color: #1890ff !important;
}

.ant-menu-item .ant-menu-item-icon {
  font-size: 16px;
}

.ant-menu-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条 */
.app-layout.dark ::-webkit-scrollbar-track {
  background: #262626;
}

.app-layout.dark ::-webkit-scrollbar-thumb {
  background: #434343;
}

.app-layout.dark ::-webkit-scrollbar-thumb:hover {
  background: #595959;
}
