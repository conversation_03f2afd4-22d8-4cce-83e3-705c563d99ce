#!/usr/bin/env python3
"""
测试URL池状态逻辑修复

验证禁用URL不消失和回收站功能
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_url_list_with_status():
    """测试不同状态的URL列表"""
    print("📋 测试URL列表状态筛选")
    
    # 测试默认列表（应该显示active和disabled，不显示deleted）
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 默认列表: {result['total']} 个URL")
        
        # 显示状态分布
        if result['data']:
            status_count = {}
            for url in result['data']:
                status = url.get('status', 'unknown')
                status_count[status] = status_count.get(status, 0) + 1
            print(f"   状态分布: {status_count}")
        
        return result['data']
    else:
        print(f"❌ 获取默认列表失败: {response.status_code}")
        return []

def test_status_filtering():
    """测试状态筛选功能"""
    print(f"\n🔍 测试状态筛选功能")
    
    statuses = ['active', 'disabled', 'deleted']
    
    for status in statuses:
        response = requests.get(f"{BASE_URL}/api/v1/urls/pool?status={status}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {status}状态: {result['total']} 个URL")
        else:
            print(f"❌ 获取{status}状态失败: {response.status_code}")

def test_disable_url_logic():
    """测试禁用URL逻辑"""
    print(f"\n🚫 测试禁用URL逻辑")
    
    # 先获取一个活跃的URL
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?status=active&page_size=1")
    if response.status_code != 200:
        print("❌ 无法获取活跃URL进行测试")
        return None
    
    result = response.json()
    if not result['data']:
        print("❌ 没有活跃URL可用于测试")
        return None
    
    url_id = result['data'][0]['id']
    url_info = result['data'][0]['url'][:50]
    print(f"📝 选择URL进行测试: {url_info}...")
    
    # 禁用URL
    disable_payload = {
        "url_ids": [url_id],
        "action": "disable"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/urls/pool/batch-update", json=disable_payload)
    if response.status_code == 200:
        print(f"✅ URL已禁用")
        
        # 检查默认列表中是否还能看到这个URL
        response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
        if response.status_code == 200:
            result = response.json()
            found_disabled = False
            for url in result['data']:
                if url['id'] == url_id:
                    found_disabled = True
                    print(f"✅ 禁用的URL仍在默认列表中，状态: {url['status']}")
                    break
            
            if not found_disabled:
                print("❌ 禁用的URL从默认列表中消失了")
        
        return url_id
    else:
        print(f"❌ 禁用URL失败: {response.status_code}")
        return None

def test_delete_url_logic(url_id):
    """测试删除URL逻辑（软删除到回收站）"""
    print(f"\n🗑️ 测试删除URL逻辑")
    
    if not url_id:
        print("❌ 没有URL可用于删除测试")
        return
    
    # 删除URL（应该是软删除）
    delete_payload = {
        "url_ids": [url_id]
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/urls/pool/batch-update", json={
        "url_ids": [url_id],
        "action": "delete"
    })
    if response.status_code == 200:
        print(f"✅ URL已删除")
        
        # 检查默认列表中是否还能看到这个URL
        response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
        if response.status_code == 200:
            result = response.json()
            found_in_default = False
            for url in result['data']:
                if url['id'] == url_id:
                    found_in_default = True
                    break
            
            if not found_in_default:
                print(f"✅ 删除的URL已从默认列表中移除")
            else:
                print(f"❌ 删除的URL仍在默认列表中")
        
        # 检查回收站中是否能看到这个URL
        response = requests.get(f"{BASE_URL}/api/v1/urls/recycle-bin")
        if response.status_code == 200:
            result = response.json()
            found_in_recycle = False
            for url in result['data']:
                if url['id'] == url_id:
                    found_in_recycle = True
                    print(f"✅ 删除的URL已进入回收站，状态: {url['status']}")
                    break
            
            if not found_in_recycle:
                print(f"❌ 删除的URL未进入回收站")
        else:
            print(f"❌ 获取回收站失败: {response.status_code}")
        
        return True
    else:
        print(f"❌ 删除URL失败: {response.status_code}")
        return False

def test_recycle_bin_operations(url_id):
    """测试回收站操作"""
    print(f"\n♻️ 测试回收站操作")
    
    if not url_id:
        print("❌ 没有URL可用于回收站测试")
        return
    
    # 测试从回收站恢复
    restore_payload = {
        "url_ids": [url_id]
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/urls/recycle-bin/restore", json=restore_payload)
    if response.status_code == 200:
        print(f"✅ URL已从回收站恢复")
        
        # 检查是否回到默认列表
        response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
        if response.status_code == 200:
            result = response.json()
            found_restored = False
            for url in result['data']:
                if url['id'] == url_id:
                    found_restored = True
                    print(f"✅ 恢复的URL已回到默认列表，状态: {url['status']}")
                    break
            
            if not found_restored:
                print(f"❌ 恢复的URL未回到默认列表")
    else:
        print(f"❌ 从回收站恢复失败: {response.status_code}")

def main():
    """主测试函数"""
    print("🚀 测试URL池状态逻辑修复")
    print("=" * 50)
    
    # 测试1: URL列表状态筛选
    urls = test_url_list_with_status()
    
    # 测试2: 状态筛选功能
    test_status_filtering()
    
    # 测试3: 禁用URL逻辑
    url_id = test_disable_url_logic()
    
    # 测试4: 删除URL逻辑
    if url_id:
        deleted = test_delete_url_logic(url_id)
        
        # 测试5: 回收站操作
        if deleted:
            test_recycle_bin_operations(url_id)
    
    print(f"\n" + "=" * 50)
    print("测试完成")
    
    print(f"\n📊 测试总结:")
    print(f"✅ 预期行为:")
    print(f"   1. 禁用的URL仍在默认列表中显示，状态标记为'禁用'")
    print(f"   2. 删除的URL从默认列表移除，进入回收站")
    print(f"   3. 可以从回收站恢复URL到默认列表")
    print(f"   4. 支持按状态筛选URL列表")

if __name__ == "__main__":
    main()
