#!/usr/bin/env python3
"""
测试URL池管理API功能

验证URL池的各种操作功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_url_pool_list():
    """测试URL池列表API"""
    print("📋 测试URL池列表功能")
    
    # 测试基本列表
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 基本列表: 总计 {data['total']} 个URL")
        
        if data['data']:
            print("   前3个URL:")
            for i, url in enumerate(data['data'][:3]):
                print(f"   {i+1}. [{url['platform'].upper()}] {url['url'][:50]}...")
    else:
        print(f"❌ 基本列表失败: {response.status_code}")
        return False
    
    # 测试分页
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?page=1&page_size=2")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 分页测试: 第1页，每页2条，共 {data['total']} 条")
    else:
        print(f"❌ 分页测试失败: {response.status_code}")
    
    # 测试平台筛选
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?platform=amazon")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 平台筛选: Amazon平台有 {len(data['data'])} 个URL")
    else:
        print(f"❌ 平台筛选失败: {response.status_code}")
    
    # 测试搜索
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?search=amazon")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 搜索功能: 搜索'amazon'找到 {len(data['data'])} 个URL")
    else:
        print(f"❌ 搜索功能失败: {response.status_code}")
    
    return True

def test_url_pool_stats():
    """测试URL池统计API"""
    print("\n📊 测试URL池统计功能")
    
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool/stats")
    if response.status_code == 200:
        data = response.json()
        stats = data['data']
        
        print(f"✅ 统计信息:")
        print(f"   - 总URL数: {stats['total_urls']}")
        print(f"   - 活跃URL: {stats['active_urls']}")
        print(f"   - 禁用URL: {stats['disabled_urls']}")
        print(f"   - 平台分布: {stats['platforms']}")
        
        return True
    else:
        print(f"❌ 统计功能失败: {response.status_code}")
        return False

def test_batch_operations():
    """测试批量操作功能"""
    print("\n🔧 测试批量操作功能")
    
    # 先获取一些URL ID
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?page_size=2")
    if response.status_code != 200:
        print("❌ 无法获取URL列表进行批量操作测试")
        return False
    
    data = response.json()
    if not data['data']:
        print("❌ 没有URL可用于批量操作测试")
        return False
    
    url_ids = [url['id'] for url in data['data']]
    print(f"📝 选择 {len(url_ids)} 个URL进行批量操作测试")
    
    # 测试批量禁用
    payload = {
        "url_ids": url_ids,
        "action": "disable"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/urls/pool/batch-update", json=payload)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 批量禁用: {result['message']}")
    else:
        print(f"❌ 批量禁用失败: {response.status_code}")
        return False
    
    # 等待一下
    time.sleep(1)
    
    # 测试批量启用
    payload = {
        "url_ids": url_ids,
        "action": "enable"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/urls/pool/batch-update", json=payload)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 批量启用: {result['message']}")
    else:
        print(f"❌ 批量启用失败: {response.status_code}")
        return False
    
    return True

def test_supported_platforms():
    """测试支持的平台列表"""
    print("\n🌐 测试支持的平台列表")
    
    response = requests.get(f"{BASE_URL}/api/v1/urls/platforms")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 支持的平台: {', '.join(data['platforms'])}")
        print(f"   总计 {data['total']} 个平台")
        return True
    else:
        print(f"❌ 获取平台列表失败: {response.status_code}")
        return False

def test_source_files():
    """测试来源文件列表"""
    print("\n📁 测试来源文件列表")
    
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool/source-files")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 来源文件:")
        for file in data['source_files']:
            print(f"   - {file['name']}: {file['url_count']} 个URL")
        return True
    else:
        print(f"❌ 获取来源文件列表失败: {response.status_code}")
        return False

def test_url_detail():
    """测试URL详情功能"""
    print("\n🔍 测试URL详情功能")
    
    # 先获取一个URL ID
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?page_size=1")
    if response.status_code != 200:
        print("❌ 无法获取URL列表")
        return False
    
    data = response.json()
    if not data['data']:
        print("❌ 没有URL可用于详情测试")
        return False
    
    url_id = data['data'][0]['id']
    
    # 获取URL详情
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool/{url_id}")
    if response.status_code == 200:
        result = response.json()
        url_data = result['data']
        print(f"✅ URL详情:")
        print(f"   - ID: {url_data['id']}")
        print(f"   - URL: {url_data['url'][:50]}...")
        print(f"   - 平台: {url_data['platform']}")
        print(f"   - 状态: {url_data['status']}")
        print(f"   - 来源: {url_data['source_file']}")
        return True
    else:
        print(f"❌ 获取URL详情失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试URL池管理API功能")
    print("=" * 50)
    
    tests = [
        test_url_pool_list,
        test_url_pool_stats,
        test_supported_platforms,
        test_source_files,
        test_url_detail,
        test_batch_operations,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ 测试失败")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！URL池管理功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
