"""
数据库模型模块

包含所有SQLAlchemy ORM模型定义
"""

from .base import Base
from .product import Product, ProductCategory
from .snapshot import ProductSnapshot
from .task import CrawlTask, CrawlConfig
from .rule import ScrapingRule
from .proxy import ProxyPool, UserAgent
from .log import SystemLog

__all__ = [
    "Base",
    "Product",
    "ProductCategory", 
    "ProductSnapshot",
    "CrawlTask",
    "CrawlConfig",
    "ScrapingRule",
    "ProxyPool",
    "UserAgent",
    "SystemLog",
] 