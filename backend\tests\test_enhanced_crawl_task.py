"""
测试增强的爬取任务
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from app.tasks.enhanced_crawl_task import _enhanced_crawl_async, _enhanced_single_url_async


class TestEnhancedCrawlTask:
    """测试增强的爬取任务"""
    
    @pytest.mark.asyncio
    async def test_enhanced_crawl_async_success(self):
        """测试增强爬取任务成功场景"""
        
        # 模拟Celery任务对象
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-task-123"
        
        task_id = "test-task-456"
        execution_context = {"test_key": "test_value"}
        
        # 模拟CrawlerRequestBuilder
        mock_crawler_request = {
            "request": {
                "urls": ["https://example.com/1", "https://example.com/2"],
                "q": "提取商品信息",
                "priority": "high",
                "batch_name": "Task_test-task-456_20250809_123456"
            },
            "auth_context": {
                "user_id": "monit_system"
            }
        }
        
        # 模拟API响应
        mock_api_result = {
            "status": "success",
            "batch_id": "batch-789",
            "submitted_urls": 2
        }
        
        with patch('app.tasks.enhanced_crawl_task.CrawlerRequestBuilder') as mock_builder, \
             patch('app.tasks.enhanced_crawl_task.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值（异步方法需要使用AsyncMock）
            mock_builder.build_request_for_task = AsyncMock(return_value=mock_crawler_request)
            
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.return_value = mock_api_result
            mock_api_service.return_value = mock_api_instance
            
            # 执行测试
            result = await _enhanced_crawl_async(
                mock_celery_task, task_id, execution_context
            )
            
            # 验证结果
            assert result["status"] == "success"
            assert result["task_id"] == task_id
            assert result["celery_task_id"] == "celery-task-123"
            assert result["urls_count"] == 2
            assert result["priority"] == "high"
            assert "start_time" in result
            assert "end_time" in result
            assert "duration" in result
            assert result["crawler_api_result"] == mock_api_result
            
            # 验证调用
            mock_builder.build_request_for_task.assert_called_once()
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)
    
    @pytest.mark.asyncio
    async def test_enhanced_crawl_async_validation_error(self):
        """测试增强爬取任务验证错误场景"""
        
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-task-123"
        
        task_id = "test-task-no-urls"
        execution_context = {}
        
        with patch('app.tasks.enhanced_crawl_task.CrawlerRequestBuilder') as mock_builder:
            # 模拟验证错误（异步方法）
            mock_builder.build_request_for_task = AsyncMock(side_effect=ValueError("Task has no active URLs"))
            
            # 执行测试
            result = await _enhanced_crawl_async(
                mock_celery_task, task_id, execution_context
            )
            
            # 验证结果
            assert result["status"] == "validation_error"
            assert result["task_id"] == task_id
            assert "Task has no active URLs" in result["error"]
            assert "start_time" in result
            assert "end_time" in result
    
    @pytest.mark.asyncio
    async def test_enhanced_single_url_async_success(self):
        """测试增强单URL爬取任务成功场景"""
        
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-single-123"
        
        task_id = "test-task-789"
        url = "https://example.com/test-product"
        execution_context = {"test_key": "test_value"}
        
        # 模拟爬取配置
        mock_crawl_config = {
            "llm_query": "提取商品信息",
            "priority": "medium",
            "auth_method": "api_key"
        }
        
        # 模拟构建的请求
        mock_crawler_request = {
            "request": {
                "urls": [url],
                "q": "提取商品信息",
                "priority": "medium"
            },
            "auth_context": {
                "user_id": "monit_system"
            }
        }
        
        # 模拟API响应
        mock_api_result = {
            "status": "success",
            "url": url,
            "data": {"title": "测试商品"}
        }
        
        with patch('app.tasks.enhanced_crawl_task.CrawlerRequestBuilder') as mock_builder, \
             patch('app.tasks.enhanced_crawl_task.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值（异步方法）
            mock_builder.get_crawl_config_for_task = AsyncMock(return_value=mock_crawl_config)
            mock_builder.build_crawler_request = AsyncMock(return_value=mock_crawler_request)
            
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.return_value = mock_api_result
            mock_api_service.return_value = mock_api_instance
            
            # 执行测试
            result = await _enhanced_single_url_async(
                mock_celery_task, task_id, url, execution_context
            )
            
            # 验证结果
            assert result["status"] == "success"
            assert result["task_id"] == task_id
            assert result["url"] == url
            assert result["celery_task_id"] == "celery-single-123"
            assert result["priority"] == "medium"
            assert result["crawler_api_result"] == mock_api_result
            
            # 验证调用
            mock_builder.get_crawl_config_for_task.assert_called_once_with(task_id)
            mock_builder.build_crawler_request.assert_called_once()
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)
    
    @pytest.mark.asyncio
    async def test_enhanced_single_url_async_no_config(self):
        """测试增强单URL爬取任务无配置场景"""
        
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-single-456"
        
        task_id = "test-task-no-config"
        url = "https://example.com/test-product"
        execution_context = {}
        
        with patch('app.tasks.enhanced_crawl_task.CrawlerRequestBuilder') as mock_builder:
            # 模拟无配置错误（异步方法）
            mock_builder.get_crawl_config_for_task = AsyncMock(return_value={})
            
            # 执行测试，应该抛出Exception（因为代码会重新包装异常）
            with pytest.raises(Exception, match="Enhanced single URL crawl failed"):
                await _enhanced_single_url_async(
                    mock_celery_task, task_id, url, execution_context
                )


if __name__ == "__main__":
    pytest.main([__file__])
