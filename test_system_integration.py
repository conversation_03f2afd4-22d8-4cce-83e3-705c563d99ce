#!/usr/bin/env python3
"""
系统集成测试 - 验证前端、后端、WebSocket连接
"""
import asyncio
import websockets
import json
import requests
from datetime import datetime
import time

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    base_url = "http://localhost:8000"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查端点正常")
        else:
            print(f"❌ 健康检查端点失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查端点连接失败: {e}")
    
    # 测试系统状态
    try:
        response = requests.get(f"{base_url}/system/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统状态端点正常: {data['status']}")
        else:
            print(f"❌ 系统状态端点失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 系统状态端点连接失败: {e}")
    
    # 测试任务列表
    try:
        response = requests.get(f"{base_url}/tasks?page=1&page_size=100", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 任务列表端点正常: {data['success']}")
        else:
            print(f"❌ 任务列表端点失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 任务列表端点连接失败: {e}")
    
    # 测试WebSocket状态
    try:
        response = requests.get(f"{base_url}/ws/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ WebSocket状态端点正常: {data['active_connections']} 个连接")
        else:
            print(f"❌ WebSocket状态端点失败: {response.status_code}")
    except Exception as e:
        print(f"❌ WebSocket状态端点连接失败: {e}")

async def test_websocket_connection():
    """测试WebSocket连接"""
    print("\n🔍 测试WebSocket连接...")
    
    uri = "ws://localhost:8000/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 等待欢迎消息
            try:
                welcome_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                welcome_data = json.loads(welcome_message)
                if welcome_data.get("type") == "system_message":
                    print("✅ 收到欢迎消息")
                else:
                    print("⚠️ 欢迎消息格式异常")
            except asyncio.TimeoutError:
                print("⚠️ 未收到欢迎消息")
            
            # 测试ping/pong
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_message))
            
            try:
                pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                pong_data = json.loads(pong_response)
                if pong_data.get("type") == "pong":
                    print("✅ Ping/Pong机制正常")
                else:
                    print("⚠️ Ping/Pong响应异常")
            except asyncio.TimeoutError:
                print("❌ Ping/Pong超时")
            
            # 测试消息回显
            test_message = {
                "type": "test",
                "data": {"message": "Integration test"},
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(test_message))
            
            try:
                echo_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                echo_data = json.loads(echo_response)
                if echo_data.get("type") == "echo":
                    print("✅ 消息回显正常")
                else:
                    print("⚠️ 消息回显异常")
            except asyncio.TimeoutError:
                print("❌ 消息回显超时")
                
    except ConnectionRefusedError:
        print("❌ WebSocket连接被拒绝")
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

def test_frontend_access():
    """测试前端访问"""
    print("\n🔍 测试前端访问...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            if "MonIt" in response.text or "React" in response.text:
                print("✅ 前端内容正常")
            else:
                print("⚠️ 前端内容可能异常")
        else:
            print(f"❌ 前端访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")

def test_docker_services():
    """测试Docker服务状态"""
    print("\n🔍 测试Docker服务状态...")
    
    import subprocess
    
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=monit-", "--format", "table {{.Names}}\t{{.Status}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 有标题行
                print("✅ Docker服务状态:")
                for line in lines[1:]:  # 跳过标题行
                    print(f"   {line}")
            else:
                print("⚠️ 未找到MonIt相关容器")
        else:
            print(f"❌ Docker命令执行失败: {result.stderr}")
    except Exception as e:
        print(f"❌ Docker状态检查失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 MonIt系统集成测试开始")
    print("=" * 50)
    
    # 测试Docker服务
    test_docker_services()
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试WebSocket连接
    await test_websocket_connection()
    
    # 测试前端访问
    test_frontend_access()
    
    print("\n" + "=" * 50)
    print("🎉 系统集成测试完成")
    print("\n📋 测试总结:")
    print("   - 如果所有项目都显示 ✅，说明系统运行正常")
    print("   - 如果有 ⚠️ 项目，说明功能可用但可能有小问题")
    print("   - 如果有 ❌ 项目，说明该功能存在问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
