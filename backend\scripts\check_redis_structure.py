#!/usr/bin/env python3
"""
检查当前Redis存储结构

分析当前实际使用的Redis键结构，对比设计文档中的优化方案
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis


async def check_redis_structure():
    """检查Redis存储结构"""
    try:
        # 连接Redis
        redis_client = redis.from_url("redis://localhost:6379/0")
        await redis_client.ping()
        
        print("🔍 Redis存储结构分析")
        print("=" * 50)
        
        # 获取所有键
        all_keys = await redis_client.keys("*")
        print(f"📊 总键数量: {len(all_keys)}")
        
        # 分析键的模式
        key_patterns = {}
        for key in all_keys:
            key_str = key.decode() if isinstance(key, bytes) else key
            
            # 分析键的前缀模式
            if key_str.startswith("monitoring_tasks:"):
                pattern = "monitoring_tasks"
                if ":task_urls:" in key_str:
                    pattern = "monitoring_tasks:task_urls"
                elif ":all_ids" in key_str:
                    pattern = "monitoring_tasks:all_ids"
                elif ":active_tasks" in key_str:
                    pattern = "monitoring_tasks:active_tasks"
                elif key_str.count(":") == 1:  # monitoring_tasks:{task_id}
                    pattern = "monitoring_tasks:{task_id}"
            elif key_str.startswith("url_pool:"):
                pattern = "url_pool"
            elif key_str.startswith("celery"):
                pattern = "celery_queues"
            else:
                pattern = "other"
            
            if pattern not in key_patterns:
                key_patterns[pattern] = []
            key_patterns[pattern].append(key_str)
        
        # 显示键模式统计
        print("\n📋 键模式分析:")
        for pattern, keys in key_patterns.items():
            print(f"  {pattern}: {len(keys)}个键")
            if len(keys) <= 5:  # 如果键不多，显示所有
                for key in keys[:5]:
                    print(f"    - {key}")
            else:  # 如果键很多，只显示前几个
                for key in keys[:3]:
                    print(f"    - {key}")
                print(f"    ... 还有{len(keys)-3}个键")
        
        # 检查任务相关的键结构
        print("\n🎯 任务存储结构分析:")
        
        # 检查任务索引
        task_ids = await redis_client.smembers('monitoring_tasks:all_ids')
        print(f"  任务索引 (monitoring_tasks:all_ids): {len(task_ids)}个任务")
        
        # 检查活跃任务索引
        active_tasks = await redis_client.smembers('monitoring_tasks:active_tasks')
        print(f"  活跃任务索引 (monitoring_tasks:active_tasks): {len(active_tasks)}个任务")
        
        # 检查具体任务的存储结构
        if task_ids:
            sample_task_id = list(task_ids)[0]
            if isinstance(sample_task_id, bytes):
                sample_task_id = sample_task_id.decode()
            
            print(f"\n📝 示例任务 ({sample_task_id}) 存储结构:")
            
            # 检查任务基本数据
            task_key = f"monitoring_tasks:{sample_task_id}"
            task_data = await redis_client.hgetall(task_key)
            print(f"  基本数据 ({task_key}): {len(task_data)}个字段")
            if task_data:
                for field, value in list(task_data.items())[:3]:
                    field_str = field.decode() if isinstance(field, bytes) else field
                    value_str = value.decode() if isinstance(value, bytes) else value
                    print(f"    {field_str}: {value_str[:50]}...")
            
            # 检查任务URL关联
            url_key = f"monitoring_tasks:task_urls:{sample_task_id}"
            url_ids = await redis_client.smembers(url_key)
            print(f"  URL关联 ({url_key}): {len(url_ids)}个URL")
        
        # 检查URL池结构
        print("\n🔗 URL池存储结构:")
        url_pool_keys = [k for k in all_keys if k.decode().startswith("url_pool:")]
        print(f"  URL池相关键: {len(url_pool_keys)}个")
        
        # 检查设计文档中提到的优化结构是否存在
        print("\n🎯 设计文档结构检查:")
        
        # 检查是否有分层结构 (tasks/{task_id}/basic, config, etc.)
        layered_keys = [k for k in all_keys if b":tasks:" in k and (b":basic" in k or b":config" in k or b":schedule" in k)]
        print(f"  分层结构键 (tasks/{{task_id}}/basic等): {len(layered_keys)}个")
        
        # 检查是否有索引结构 (indexes/*)
        index_keys = [k for k in all_keys if b":indexes:" in k]
        print(f"  索引结构键 (indexes/*): {len(index_keys)}个")
        
        # 检查是否有平台分组
        platform_keys = [k for k in all_keys if b":by_platform:" in k]
        print(f"  平台分组键 (by_platform/*): {len(platform_keys)}个")
        
        # 检查是否有状态分组
        status_keys = [k for k in all_keys if b":by_status:" in k]
        print(f"  状态分组键 (by_status/*): {len(status_keys)}个")
        
        # 总结当前结构 vs 设计结构
        print("\n📊 结构对比总结:")
        print("  当前实现:")
        print("    ✅ 基础任务存储 (monitoring_tasks:{task_id})")
        print("    ✅ 任务索引 (monitoring_tasks:all_ids)")
        print("    ✅ 活跃任务索引 (monitoring_tasks:active_tasks)")
        print("    ✅ URL关联 (monitoring_tasks:task_urls:{task_id})")
        
        print("  设计文档中的优化结构:")
        if layered_keys:
            print("    ✅ 分层存储结构")
        else:
            print("    ❌ 分层存储结构 (未实现)")
        
        if index_keys:
            print("    ✅ 完整索引系统")
        else:
            print("    ❌ 完整索引系统 (未实现)")
        
        if platform_keys:
            print("    ✅ 平台分组索引")
        else:
            print("    ❌ 平台分组索引 (未实现)")
        
        if status_keys:
            print("    ✅ 状态分组索引")
        else:
            print("    ❌ 状态分组索引 (未实现)")
        
        await redis_client.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")


async def main():
    """主函数"""
    await check_redis_structure()


if __name__ == "__main__":
    asyncio.run(main())
