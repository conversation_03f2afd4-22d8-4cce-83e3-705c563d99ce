#!/usr/bin/env python3
"""
创建多Worker分配的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def get_available_tasks():
    """获取可用任务"""
    
    print("📋 获取可用任务...")
    
    try:
        response = requests.get(f"{BASE_URL}/monitoring-tasks/")
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 找到 {len(tasks)} 个任务")
            return tasks
        else:
            print(f"❌ 获取任务失败: {response.text}")
            return []
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []

def create_multi_worker_assignment_for_task(task_id, task_name):
    """为指定任务创建多Worker分配"""
    
    print(f"\n🔧 为任务 '{task_name}' 创建多Worker分配...")
    
    # 获取可用Worker
    try:
        workers_response = requests.get(f"{BASE_URL}/crawler-workers/")
        if workers_response.status_code == 200:
            workers = workers_response.json()
            print(f"✅ 找到 {len(workers)} 个Worker")
            
            if len(workers) >= 2:
                # 选择前两个Worker
                worker_ids = [workers[0]['worker_id'], workers[1]['worker_id']]
                worker_names = [workers[0]['worker_name'], workers[1]['worker_name']]
                
                print(f"选择的Worker:")
                for i, (wid, wname) in enumerate(zip(worker_ids, worker_names), 1):
                    print(f"  {i}. {wname} ({wid})")
                
                # 创建分配
                assignment_data = {
                    "task_id": task_id,
                    "worker_ids": worker_ids,
                    "assignment_name": f"多Worker分配-{task_name}",
                    "description": "测试多Worker显示功能",
                    "assignment_strategy": "health_based",
                    "auto_failover": True,
                    "total_urls": 49
                }
                
                response = requests.post(
                    f"{BASE_URL}/task-assignments/",
                    json=assignment_data,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    assignment = response.json()
                    print("✅ 多Worker分配创建成功!")
                    print(f"  分配ID: {assignment.get('assignment_id')}")
                    print(f"  Worker IDs: {assignment.get('worker_ids')}")
                    print(f"  Worker数量: {len(assignment.get('worker_ids', []))}")
                    return assignment.get('assignment_id')
                elif response.status_code == 400:
                    error_detail = response.json().get('detail', '')
                    print(f"ℹ️ 分配被拒绝: {error_detail}")
                    return None
                else:
                    print(f"❌ 分配创建失败: {response.text}")
                    return None
            else:
                print("❌ 可用Worker数量不足")
                return None
        else:
            print(f"❌ 获取Worker列表失败: {workers_response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def check_assignment_display(task_id):
    """检查分配显示"""
    
    print(f"\n📊 检查任务 {task_id} 的分配显示...")
    
    try:
        response = requests.get(f"{BASE_URL}/task-assignments/task/{task_id}/assignments")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ 当前有 {len(assignments)} 个分配")
            
            for i, assignment in enumerate(assignments, 1):
                print(f"\n分配 {i}:")
                print(f"  ID: {assignment.get('assignment_id')}")
                print(f"  名称: {assignment.get('assignment_name')}")
                print(f"  状态: {assignment.get('status')}")
                print(f"  Worker数量: {assignment.get('worker_count')}")
                print(f"  Worker IDs: {assignment.get('worker_ids')}")
                print(f"  Worker名称: {assignment.get('worker_names')}")
                
                # 检查是否是多Worker分配
                worker_count = assignment.get('worker_count', 0)
                if worker_count > 1:
                    print(f"  🎯 这是一个多Worker分配!")
                    worker_names = assignment.get('worker_names', [])
                    print(f"  📋 包含的Worker: {', '.join(worker_names)}")
            
            return assignments
        else:
            print(f"❌ 获取失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []

def main():
    """主函数"""
    print("🚀 创建多Worker分配测试\n")
    
    # 1. 获取可用任务
    tasks = get_available_tasks()
    
    if not tasks:
        print("❌ 没有可用任务")
        return
    
    # 2. 尝试为每个任务创建多Worker分配
    success_count = 0
    for task in tasks:
        if isinstance(task, str):
            task_id = task
            task_name = f"Task-{task_id[:8]}"
        else:
            task_id = task.get('task_id')
            task_name = task.get('task_name', 'Unknown')
        
        print(f"\n{'='*60}")
        print(f"处理任务: {task_name} ({task_id})")
        
        assignment_id = create_multi_worker_assignment_for_task(task_id, task_name)
        
        if assignment_id:
            success_count += 1
            # 检查分配显示
            check_assignment_display(task_id)
            break  # 只创建一个成功的分配用于测试
    
    # 3. 总结结果
    print(f"\n{'='*60}")
    print(f"🎯 测试结果总结:")
    print(f"  尝试的任务数量: {len(tasks)}")
    print(f"  成功创建的分配数量: {success_count}")
    
    if success_count > 0:
        print(f"  ✅ 多Worker分配创建成功!")
        print(f"  💡 现在可以在前端查看多Worker分配的显示效果")
    else:
        print(f"  ❌ 没有成功创建多Worker分配")

if __name__ == "__main__":
    main()
