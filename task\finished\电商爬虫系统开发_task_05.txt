# 任务05: 任务调度与管理系统

## 任务描述
设计并实现一个高性能的任务调度与管理系统，解决外部爬虫API限制问题（2并发/100批次）与1000+监控任务需求之间的矛盾。

## 核心挑战
- **外部API限制**: 最多2个URL并行处理，单次API调用最多100个URL
- **业务需求**: 需要处理1000+商品URL的监控任务
- **性能要求**: 高并发、低延迟、高可靠性
- **扩展性**: 支持分布式部署和水平扩展

## 具体任务内容

### 1. 任务调度架构设计
```
用户请求 → 任务分片器 → 任务队列 → 工作进程 → 外部API → 结果聚合
    ↓           ↓          ↓        ↓         ↓         ↓
  1000+ URLs  100个/片   Redis队列  Celery   2并发限制  数据库存储
```

#### 1.1 任务分片策略
- **智能分片**: 将1000+URL任务分解为100个URL的小批次
- **优先级队列**: 支持高优先级任务插队
- **负载均衡**: 根据工作进程负载动态分配任务
- **失败重试**: 指数退避重试机制

#### 1.2 限流控制机制
- **API并发限制**: 严格控制同时调用外部API的数量（≤2）
- **速率限制**: 控制API调用频率，避免触发外部服务限制
- **熔断机制**: API失败率过高时自动熔断保护
- **降级策略**: 外部服务不可用时的降级处理

### 2. Celery分布式任务队列
#### 2.1 Celery配置
```python
# celery_config.py
broker_url = 'redis://localhost:6379/0'
result_backend = 'redis://localhost:6379/1'
task_serializer = 'json'
result_serializer = 'json'
accept_content = ['json']
timezone = 'UTC'
enable_utc = True

# 任务路由配置
task_routes = {
    'crawler.tasks.crawl_batch': {'queue': 'crawler_queue'},
    'crawler.tasks.process_results': {'queue': 'processor_queue'},
    'crawler.tasks.monitor_tasks': {'queue': 'monitor_queue'},
}

# 工作进程配置
worker_concurrency = 4
worker_prefetch_multiplier = 1
task_acks_late = True
worker_max_tasks_per_child = 1000
```

#### 2.2 核心任务类型
- **crawl_batch**: 批量爬取任务（100个URL/批次）
- **monitor_task**: 定时监控任务
- **retry_failed**: 失败任务重试
- **aggregate_results**: 结果聚合和处理
- **cleanup_expired**: 过期任务清理

### 3. Redis消息队列和状态管理
#### 3.1 队列设计
```
crawler_queue:high     # 高优先级爬取任务
crawler_queue:normal   # 普通优先级爬取任务
crawler_queue:low      # 低优先级爬取任务
processor_queue        # 数据处理任务
monitor_queue          # 监控任务
retry_queue           # 重试任务
```

#### 3.2 状态管理
```python
# 任务状态枚举
class TaskStatus:
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 执行中
    SUCCESS = "success"      # 成功完成
    FAILURE = "failure"      # 执行失败
    RETRY = "retry"          # 重试中
    CANCELLED = "cancelled"  # 已取消
```

#### 3.3 Redis数据结构
```
task:{task_id}:status     # 任务状态
task:{task_id}:progress   # 任务进度
task:{task_id}:result     # 任务结果
task:{task_id}:error      # 错误信息
api_limiter:calls         # API调用计数器
api_limiter:concurrent    # 并发调用计数
```

### 4. 智能任务分片器
#### 4.1 分片算法
```python
class TaskSplitter:
    def split_urls(self, urls: List[str], batch_size: int = 100) -> List[List[str]]:
        """将URL列表分片为小批次"""

    def calculate_priority(self, task: Task) -> int:
        """计算任务优先级"""

    def estimate_completion_time(self, batch: List[str]) -> float:
        """估算批次完成时间"""
```

#### 4.2 动态调度策略
- **负载感知**: 根据当前系统负载调整分片大小
- **时间窗口**: 在业务低峰期增加并发度
- **资源预留**: 为紧急任务预留处理能力

### 5. 任务监控和状态追踪
#### 5.1 实时监控指标
```python
# 监控指标
class TaskMetrics:
    total_tasks: int           # 总任务数
    pending_tasks: int         # 等待任务数
    running_tasks: int         # 执行中任务数
    completed_tasks: int       # 已完成任务数
    failed_tasks: int          # 失败任务数
    avg_processing_time: float # 平均处理时间
    api_call_rate: float       # API调用速率
    success_rate: float        # 成功率
```

#### 5.2 告警机制
- **任务堆积告警**: 等待队列长度超过阈值
- **失败率告警**: 任务失败率超过阈值
- **API限制告警**: 触发外部API限制
- **系统资源告警**: CPU/内存使用率过高

### 6. 容错和恢复机制
#### 6.1 故障处理
- **任务重试**: 指数退避重试策略
- **死信队列**: 处理无法恢复的失败任务
- **状态恢复**: 系统重启后恢复任务状态
- **数据一致性**: 确保任务状态与实际执行状态一致

#### 6.2 高可用设计
- **多实例部署**: 支持多个Celery worker实例
- **故障转移**: 工作进程故障时自动转移任务
- **数据备份**: 关键状态数据的备份和恢复
## 预期产出物
1. **任务调度核心模块**
   - TaskScheduler: 任务调度器
   - TaskSplitter: 任务分片器
   - RateLimiter: 限流控制器
   - TaskMonitor: 任务监控器

2. **Celery任务定义**
   - crawl_batch.py: 批量爬取任务
   - monitor_tasks.py: 监控任务
   - result_processor.py: 结果处理任务

3. **Redis配置和管理**
   - redis_config.py: Redis配置
   - queue_manager.py: 队列管理器
   - state_manager.py: 状态管理器

4. **监控和告警系统**
   - metrics_collector.py: 指标收集器
   - alert_manager.py: 告警管理器
   - dashboard_data.py: 监控面板数据

## 验收标准
- [ ] 能够处理1000+URL的监控任务
- [ ] 严格遵守外部API限制（2并发/100批次）
- [ ] 任务分片和调度功能正常
- [ ] 失败任务自动重试机制有效
- [ ] 实时监控和状态追踪准确
- [ ] 系统故障时能够自动恢复
- [ ] 性能满足业务需求（处理速度、资源占用）
- [ ] 通过压力测试和稳定性测试

## 预估工时
3-4天

## 依赖关系
- 前置任务: 任务01 (环境搭建)、任务02 (数据模型)、任务03 (爬虫引擎)
- 为任务04 (API服务) 提供任务管理接口
- 为任务07 (前端界面) 提供实时状态数据

## 风险点
1. **外部API限制**: 可能比预期更严格
2. **任务调度复杂度**: 分片和限流逻辑复杂
3. **Redis性能**: 大量任务状态存储的性能问题
4. **Celery稳定性**: 长时间运行的稳定性
5. **内存占用**: 大量任务状态的内存占用

## 状态
✅ 已完成

## 完成时间
2024年12月29日

## 实际产出物
1. **任务调度核心模块** ✅
   - TaskSplitter: 智能任务分片器 (`app/core/task_splitter.py`)
   - RateLimiter: 限流控制器 (`app/core/rate_limiter.py`)
   - TaskManager: 任务管理器 (`app/core/task_manager.py`)

2. **Celery任务定义** ✅
   - crawl_batch.py: 批量爬取任务 (`app/tasks/crawl_batch.py`)
   - monitor_tasks.py: 监控任务 (`app/tasks/monitor_tasks.py`)
   - result_processor.py: 结果处理任务 (`app/tasks/result_processor.py`)
   - cleanup_tasks.py: 清理任务 (`app/tasks/cleanup_tasks.py`)

3. **Celery应用配置** ✅
   - celery_app.py: Celery应用配置 (`app/celery_app.py`)

4. **运维脚本** ✅
   - start_task_manager.py: 任务管理器启动脚本 (`scripts/start_task_manager.py`)
   - start_workers.sh: Celery worker启动脚本 (`scripts/start_workers.sh`)

5. **测试和文档** ✅
   - test_task_scheduler.py: 完整测试套件 (`tests/test_task_scheduler.py`)
   - task_scheduler_guide.md: 使用指南 (`docs/task_scheduler_guide.md`)

## 核心功能验证
- ✅ 智能任务分片：1000+URL自动分解为100个URL的小批次
- ✅ 限流控制：严格控制2并发/100批次限制
- ✅ 分布式任务调度：Celery + Redis 多队列架构
- ✅ 任务优先级和重试机制
- ✅ 实时任务状态监控
- ✅ 容错和恢复机制
- ✅ 系统监控和告警

## 测试用例
测试用例将在任务完成后创建，主要验证：
- 大批量任务处理能力（1000+ URLs）
- API限流控制有效性
- 任务分片和调度正确性
- 故障恢复和重试机制
- 系统性能和稳定性