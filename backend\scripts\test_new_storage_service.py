#!/usr/bin/env python3
"""
测试新的分层TaskStorageService

验证分层存储和索引系统是否正常工作
"""

import asyncio
import sys
import os
import json
import uuid
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.services.task_storage_service import TaskStorageService


async def test_new_storage_service():
    """测试新的存储服务"""
    print("🧪 测试新的分层TaskStorageService")
    print("=" * 50)
    
    # 创建存储服务实例
    storage = TaskStorageService()
    
    try:
        # 测试1: 创建测试任务
        print("\n📝 测试1: 创建测试任务")
        
        task_id = str(uuid.uuid4())
        task_data = {
            "id": task_id,
            "name": "测试任务",
            "description": "这是一个测试任务",
            "status": "active",
            "platform": "mercadolibre",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "config": {
                "platform": "mercadolibre",
                "batch_size": 50,
                "max_retries": 3,
                "timeout": 300
            },
            "schedule": {
                "type": "daily",
                "time": "01:00",
                "timezone": "Asia/Shanghai",
                "enabled": True
            },
            "next_run": "2025-07-14T01:00:00+08:00",
            "last_run": "",
            "is_running": False,
            "total_runs": 0,
            "success_count": 0,
            "failed_count": 0
        }
        
        # 保存任务
        success = await storage.save_task(task_id, task_data)
        print(f"  保存任务: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试2: 读取任务
        print("\n📖 测试2: 读取任务")
        
        retrieved_task = await storage.get_task(task_id)
        if retrieved_task:
            print("  ✅ 任务读取成功")
            print(f"  任务名称: {retrieved_task.get('name')}")
            print(f"  任务状态: {retrieved_task.get('status')}")
            print(f"  配置信息: {retrieved_task.get('config')}")
            print(f"  调度信息: {retrieved_task.get('schedule')}")
        else:
            print("  ❌ 任务读取失败")
        
        # 测试3: 列出任务
        print("\n📋 测试3: 列出任务")
        
        task_list = await storage.list_tasks()
        print(f"  任务总数: {task_list.get('total', 0)}")
        
        if task_list.get('tasks'):
            for task in task_list['tasks']:
                print(f"    - {task.get('name')} ({task.get('status')})")
        
        # 测试4: 按状态过滤
        print("\n🔍 测试4: 按状态过滤")
        
        active_tasks = await storage.list_tasks(filters={"status": "active"})
        print(f"  活跃任务数: {active_tasks.get('total', 0)}")
        
        # 测试5: 按平台过滤
        print("\n🌐 测试5: 按平台过滤")
        
        ml_tasks = await storage.list_tasks(filters={"platform": "mercadolibre"})
        print(f"  MercadoLibre任务数: {ml_tasks.get('total', 0)}")
        
        # 测试6: 验证Redis结构
        print("\n🔍 测试6: 验证Redis结构")
        
        redis_client = await storage.get_redis_client()
        
        # 检查分层数据
        basic_exists = await redis_client.exists(f"monitoring_tasks:tasks:{task_id}:basic")
        config_exists = await redis_client.exists(f"monitoring_tasks:tasks:{task_id}:config")
        schedule_exists = await redis_client.exists(f"monitoring_tasks:tasks:{task_id}:schedule")
        stats_exists = await redis_client.exists(f"monitoring_tasks:tasks:{task_id}:stats")
        
        print(f"  分层数据存在性:")
        print(f"    basic: {'✅' if basic_exists else '❌'}")
        print(f"    config: {'✅' if config_exists else '❌'}")
        print(f"    schedule: {'✅' if schedule_exists else '❌'}")
        print(f"    stats: {'✅' if stats_exists else '❌'}")
        
        # 检查索引
        all_tasks_count = await redis_client.scard("monitoring_tasks:indexes:all_task_ids")
        active_tasks_count = await redis_client.scard("monitoring_tasks:indexes:active_tasks")
        ml_tasks_count = await redis_client.scard("monitoring_tasks:indexes:by_platform:mercadolibre")
        active_status_count = await redis_client.scard("monitoring_tasks:indexes:by_status:active")
        
        print(f"  索引统计:")
        print(f"    总任务索引: {all_tasks_count}")
        print(f"    活跃任务索引: {active_tasks_count}")
        print(f"    MercadoLibre平台索引: {ml_tasks_count}")
        print(f"    活跃状态索引: {active_status_count}")
        
        await redis_client.aclose()
        
        # 测试7: 更新任务
        print("\n✏️ 测试7: 更新任务")
        
        updates = {
            "name": "更新后的测试任务",
            "status": "paused",
            "description": "这是一个更新后的测试任务"
        }
        
        update_success = await storage.update_task(task_id, updates)
        print(f"  更新任务: {'✅ 成功' if update_success else '❌ 失败'}")
        
        # 验证更新
        updated_task = await storage.get_task(task_id)
        if updated_task:
            print(f"  更新后名称: {updated_task.get('name')}")
            print(f"  更新后状态: {updated_task.get('status')}")
        
        # 测试8: 删除任务
        print("\n🗑️ 测试8: 删除任务")
        
        delete_success = await storage.delete_task(task_id)
        print(f"  删除任务: {'✅ 成功' if delete_success else '❌ 失败'}")
        
        # 验证删除
        deleted_task = await storage.get_task(task_id)
        print(f"  删除验证: {'✅ 任务已删除' if not deleted_task else '❌ 任务仍存在'}")
        
        print("\n🎉 所有测试完成!")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await storage.close()


async def main():
    """主函数"""
    success = await test_new_storage_service()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
