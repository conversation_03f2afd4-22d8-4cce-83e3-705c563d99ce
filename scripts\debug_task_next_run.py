#!/usr/bin/env python3
"""
调试任务下次执行时间脚本

用于分析特定任务的下次执行时间计算是否正确
"""

import redis
import json
import pytz
from datetime import datetime, timezone, timedelta
import sys


def debug_task_next_run(task_id: str):
    """调试指定任务的下次执行时间"""
    
    # 连接Redis
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    
    try:
        # 获取任务数据
        task_key = f'monitoring_tasks:{task_id}'
        task_data = redis_client.hgetall(task_key)
        
        if not task_data:
            print(f"❌ 任务 {task_id} 不存在")
            return
        
        print(f"🔍 调试任务: {task_id}")
        print(f"📝 任务名称: {task_data.get('name', '未知')}")
        print(f"📊 任务状态: {task_data.get('status', '未知')}")
        print()
        
        # 解析调度配置
        schedule_str = task_data.get('schedule', '{}')
        schedule = json.loads(schedule_str)
        
        print("⚙️ 调度配置:")
        print(f"   类型: {schedule.get('type', '未设置')}")
        print(f"   执行时间: {schedule.get('time', '未设置')}")
        print(f"   时区: {schedule.get('timezone', '未设置')}")
        print(f"   启用随机延迟: {schedule.get('enable_random_delay', False)}")
        if schedule.get('enable_random_delay'):
            print(f"   随机延迟范围: {schedule.get('random_delay_min', 0)}-{schedule.get('random_delay_max', 0)} 分钟")
        print()
        
        # 获取当前存储的下次执行时间
        stored_next_run = task_data.get('next_run')
        print(f"💾 存储的下次执行时间: {stored_next_run}")
        
        if stored_next_run:
            try:
                # 解析UTC时间
                next_run_utc = datetime.fromisoformat(stored_next_run.replace('Z', '+00:00'))
                
                # 转换为用户时区
                user_timezone = pytz.timezone(schedule.get('timezone', 'Asia/Shanghai'))
                next_run_local = next_run_utc.astimezone(user_timezone)
                
                print(f"🌍 UTC时间: {next_run_utc.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                print(f"🏠 本地时间: {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                print()
            except Exception as e:
                print(f"❌ 时间解析错误: {e}")
                print()
        
        # 重新计算下次执行时间
        print("🔄 重新计算下次执行时间:")
        
        user_timezone = pytz.timezone(schedule.get('timezone', 'Asia/Shanghai'))
        now_utc = datetime.now(timezone.utc)
        now_local = now_utc.astimezone(user_timezone)
        
        print(f"   当前UTC时间: {now_utc.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"   当前本地时间: {now_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        if schedule.get('type') == 'daily':
            # 解析执行时间
            time_str = schedule.get('time', '09:00')
            try:
                time_parts = time_str.split(':')
                hour = int(time_parts[0])
                minute = int(time_parts[1]) if len(time_parts) > 1 else 0
            except (ValueError, IndexError):
                hour, minute = 9, 0
            
            print(f"   设置的执行时间: {hour:02d}:{minute:02d}")
            
            # 计算基础下次执行时间
            next_run_local = now_local.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # 如果今天的执行时间已过，安排到明天
            if next_run_local <= now_local:
                next_run_local = next_run_local + timedelta(days=1)
                print(f"   今天时间已过，安排到明天")
            
            print(f"   基础下次执行时间: {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            
            # 应用随机延迟
            if schedule.get('enable_random_delay', False):
                delay_min = schedule.get('random_delay_min', 0)
                delay_max = schedule.get('random_delay_max', 0)
                
                # 注意：这里我们不生成新的随机数，而是分析可能的延迟范围
                print(f"   随机延迟范围: {delay_min}-{delay_max} 分钟")
                
                # 计算可能的时间范围
                earliest = next_run_local + timedelta(minutes=delay_min)
                latest = next_run_local + timedelta(minutes=delay_max)
                
                print(f"   可能的执行时间范围:")
                print(f"     最早: {earliest.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                print(f"     最晚: {latest.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                
                # 检查存储的时间是否在合理范围内
                if stored_next_run:
                    try:
                        stored_utc = datetime.fromisoformat(stored_next_run.replace('Z', '+00:00'))
                        stored_local = stored_utc.astimezone(user_timezone)
                        
                        if earliest <= stored_local <= latest:
                            delay_applied = (stored_local - next_run_local).total_seconds() / 60
                            print(f"   ✅ 存储的时间在合理范围内，应用的延迟: {delay_applied:.0f} 分钟")
                        else:
                            print(f"   ❌ 存储的时间超出合理范围")
                    except Exception as e:
                        print(f"   ❌ 验证时间范围时出错: {e}")
            else:
                # 转换为UTC时间
                next_run_utc = next_run_local.astimezone(timezone.utc)
                print(f"   计算的下次执行时间(UTC): {next_run_utc.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                print(f"   计算的下次执行时间(本地): {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        print()
        print("📋 总结:")
        if stored_next_run:
            try:
                stored_utc = datetime.fromisoformat(stored_next_run.replace('Z', '+00:00'))
                stored_local = stored_utc.astimezone(user_timezone)
                
                # 计算与设置时间的差异
                set_time = schedule.get('time', '09:00')
                time_parts = set_time.split(':')
                set_hour = int(time_parts[0])
                set_minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                
                # 获取存储时间的小时和分钟
                stored_hour = stored_local.hour
                stored_minute = stored_local.minute
                
                # 计算时间差异（分钟）
                set_total_minutes = set_hour * 60 + set_minute
                stored_total_minutes = stored_hour * 60 + stored_minute
                
                # 处理跨天的情况
                if stored_total_minutes < set_total_minutes:
                    stored_total_minutes += 24 * 60  # 加一天
                
                time_diff = stored_total_minutes - set_total_minutes
                
                print(f"   设置的执行时间: {set_hour:02d}:{set_minute:02d}")
                print(f"   实际的执行时间: {stored_hour:02d}:{stored_minute:02d}")
                print(f"   时间差异: {time_diff} 分钟")
                
                if schedule.get('enable_random_delay', False):
                    delay_min = schedule.get('random_delay_min', 0)
                    delay_max = schedule.get('random_delay_max', 0)
                    if delay_min <= time_diff <= delay_max:
                        print(f"   ✅ 时间差异在随机延迟范围内 ({delay_min}-{delay_max} 分钟)")
                    else:
                        print(f"   ❌ 时间差异超出随机延迟范围 ({delay_min}-{delay_max} 分钟)")
                else:
                    if time_diff == 0:
                        print(f"   ✅ 时间完全匹配")
                    else:
                        print(f"   ❌ 时间不匹配，但未启用随机延迟")
                        
            except Exception as e:
                print(f"   ❌ 分析时间差异时出错: {e}")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
    finally:
        redis_client.close()


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python debug_task_next_run.py <task_id>")
        print("示例: python debug_task_next_run.py fe2ae36c-cffa-460e-942a-4c95a78122fe")
        sys.exit(1)
    
    task_id = sys.argv[1]
    debug_task_next_run(task_id)
