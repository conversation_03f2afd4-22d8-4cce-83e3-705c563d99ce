# 随机启动延迟功能

## 功能概述

为了规避反爬虫检测，MonIt系统新增了随机启动延迟功能。该功能允许在设定的任务执行时间基础上，增加随机的延迟时间，使任务执行时间更加自然和不可预测。

## 功能特性

### 1. 灵活的延迟范围配置
- **最小延迟时间**: 0-1440分钟（0-24小时）
- **最大延迟时间**: 1-1440分钟（1-24小时）
- **智能验证**: 确保最大延迟时间大于最小延迟时间

### 2. 反爬虫优化
- **随机性**: 每次任务执行时间都在指定范围内随机选择
- **自然行为**: 模拟人工操作的不规律性
- **检测规避**: 避免固定时间执行被识别为机器人行为

### 3. 用户友好的界面
- **开关控制**: 可以轻松启用/禁用随机延迟功能
- **实时预览**: 在调度预览中显示延迟范围信息
- **智能提示**: 提供详细的功能说明和使用建议

## 使用方法

### 1. 启用随机延迟
1. 在任务创建向导的"调度配置"步骤中
2. 找到"反爬虫配置"卡片
3. 开启"启用随机启动延迟"开关

### 2. 配置延迟范围
1. 设置"最小延迟时间"（分钟）
2. 设置"最大延迟时间"（分钟）
3. 系统会自动验证配置的合理性

### 3. 查看预览
在"调度预览"区域可以看到：
- 调度类型
- 随机延迟范围
- 延迟时间的小时换算（当超过60分钟时）

## 配置示例

### 示例1：短时间随机延迟
```
启用随机启动延迟: 是
最小延迟时间: 0分钟
最大延迟时间: 30分钟

效果: 在设定时间后0-30分钟内随机执行
```

### 示例2：中等时间随机延迟
```
启用随机启动延迟: 是
最小延迟时间: 30分钟
最大延迟时间: 180分钟

效果: 在设定时间后30分钟-3小时内随机执行
```

### 示例3：长时间随机延迟
```
启用随机启动延迟: 是
最小延迟时间: 60分钟
最大延迟时间: 720分钟

效果: 在设定时间后1-12小时内随机执行
```

## 实际应用场景

### 每日价格监控
- **设定时间**: 每日上午9:00
- **随机延迟**: 0-180分钟
- **实际执行**: 9:00-12:00之间随机时间
- **优势**: 避免在固定时间访问被检测

### 周期性数据采集
- **设定时间**: 每6小时执行一次
- **随机延迟**: 15-45分钟
- **实际执行**: 在每个周期后15-45分钟内随机执行
- **优势**: 打破规律性访问模式

### 竞品监控
- **设定时间**: 每日晚上22:00
- **随机延迟**: 0-360分钟
- **实际执行**: 22:00-次日4:00之间随机时间
- **优势**: 分散访问时间，降低被发现概率

## 技术实现

### 前端配置
- 新增 `enable_random_delay`、`random_delay_min`、`random_delay_max` 字段
- 实时表单验证和预览更新
- 智能UI显示（仅在启用时显示配置选项）

### 后端支持
- 扩展 `ScheduleConfig` 模型支持随机延迟字段
- 添加数据验证确保配置合理性
- 更新API模板包含默认随机延迟配置

### 调度执行
- 任务调度器在计算执行时间时考虑随机延迟
- 使用安全的随机数生成器确保真正的随机性
- 记录实际执行时间用于监控和分析

## 注意事项

1. **延迟范围设置**: 建议根据目标网站的访问频率和检测敏感度设置合理的延迟范围
2. **资源规划**: 随机延迟可能导致任务执行时间不可预测，需要合理规划系统资源
3. **监控调整**: 建议定期监控任务执行效果，根据需要调整延迟范围
4. **合规使用**: 确保爬虫行为符合目标网站的robots.txt和使用条款

## 默认配置

```typescript
{
  enable_random_delay: false,
  random_delay_min: 0,
  random_delay_max: 180  // 默认3小时
}
```

## 更新日志

- **2025-07-05**: 初始版本发布
  - 添加随机启动延迟功能
  - 支持0-1440分钟延迟范围配置
  - 集成到任务创建向导
  - 添加实时预览和验证
