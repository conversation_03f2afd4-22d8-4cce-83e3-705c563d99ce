#!/usr/bin/env python3
"""
测试Excel上传和URL解析功能

创建测试Excel文件并测试上传API
"""

import pandas as pd
import requests
import json
from pathlib import Path

def create_test_excel():
    """创建测试Excel文件"""
    test_data = {
        'Product Name': [
            'iPhone 15 Pro',
            'Samsung Galaxy S24',
            'MacBook Pro M3',
            'Dell XPS 13',
            'Sony WH-1000XM5'
        ],
        'URL': [
            'https://articulo.mercadolibre.com.mx/MLM-123456789-iphone-15-pro-256gb-titanio-natural',
            'https://www.amazon.com.mx/Samsung-Galaxy-S24-Ultra-256GB/dp/B0ABCD1234',
            'https://articulo.mercadolibre.com.mx/MLM-987654321-macbook-pro-m3-14-pulgadas-512gb',
            'https://www.amazon.com/Dell-XPS-13-9320-Laptop/dp/B09EFGH5678',
            'https://articulo.mercadolibre.com.mx/MLM-555666777-audifonos-sony-wh-1000xm5-bluetooth'
        ],
        'Price': [
            '$25,999',
            '$28,999',
            '$45,999',
            '$1,299',
            '$7,999'
        ],
        'Category': [
            'Electronics',
            'Electronics', 
            'Computers',
            'Computers',
            'Audio'
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    excel_path = Path('test_urls.xlsx')
    df.to_excel(excel_path, index=False)
    
    print(f"测试Excel文件已创建: {excel_path}")
    print(f"包含 {len(df)} 个URL")
    
    return excel_path

def test_upload_excel(excel_path):
    """测试Excel上传API"""
    url = "http://localhost:8000/api/v1/excel/upload-and-parse"
    
    try:
        with open(excel_path, 'rb') as f:
            files = {'file': (excel_path.name, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            params = {
                'auto_detect': True
            }
            
            print(f"正在上传文件: {excel_path}")
            response = requests.post(url, files=files, params=params, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 上传成功!")
                print(f"📊 统计信息:")
                print(f"   - 总URL数: {result['total_urls']}")
                print(f"   - 新增URL: {result['new_urls']}")
                print(f"   - 重复URL: {result['duplicate_urls']}")
                print(f"   - 处理时间: {result['processing_time']:.2f}秒")
                
                if result['urls']:
                    print(f"\n📋 解析的URL样本:")
                    for i, url_item in enumerate(result['urls'][:3]):
                        print(f"   {i+1}. {url_item['platform'].upper()}: {url_item['url']}")
                    
                    if len(result['urls']) > 3:
                        print(f"   ... 还有 {len(result['urls']) - 3} 个URL")
                
                return True
            else:
                print(f"❌ 上传失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return False

def test_url_pool_stats():
    """测试URL池统计API"""
    url = "http://localhost:8000/api/v1/urls/pool/stats"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            stats = result['data']
            
            print(f"\n📈 URL池统计:")
            print(f"   - 总URL数: {stats['total_urls']}")
            print(f"   - 活跃URL: {stats['active_urls']}")
            print(f"   - 禁用URL: {stats['disabled_urls']}")
            
            if stats['platforms']:
                print(f"   - 平台分布:")
                for platform, count in stats['platforms'].items():
                    print(f"     * {platform}: {count}")
            
            return True
        else:
            print(f"❌ 获取统计失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取统计异常: {e}")
        return False

def test_url_pool_list():
    """测试URL池列表API"""
    url = "http://localhost:8000/api/v1/urls/pool"
    
    try:
        params = {
            'page': 1,
            'page_size': 10
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n📋 URL池列表:")
            print(f"   - 总数: {result['total']}")
            print(f"   - 当前页: {result['page']}")
            print(f"   - 每页大小: {result['page_size']}")
            
            if result['data']:
                print(f"   - URL列表:")
                for i, url_item in enumerate(result['data'][:5]):
                    print(f"     {i+1}. [{url_item['platform'].upper()}] {url_item['url'][:60]}...")
            
            return True
        else:
            print(f"❌ 获取列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取列表异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Excel上传和URL解析功能")
    print("=" * 50)
    
    # 1. 创建测试Excel文件
    excel_path = create_test_excel()
    
    # 2. 测试上传功能
    print(f"\n📤 测试Excel上传功能")
    upload_success = test_upload_excel(excel_path)
    
    if upload_success:
        # 3. 测试统计API
        print(f"\n📊 测试URL池统计API")
        test_url_pool_stats()
        
        # 4. 测试列表API
        print(f"\n📋 测试URL池列表API")
        test_url_pool_list()
    
    # 5. 清理测试文件
    try:
        excel_path.unlink()
        print(f"\n🧹 已清理测试文件: {excel_path}")
    except:
        pass
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    main()
