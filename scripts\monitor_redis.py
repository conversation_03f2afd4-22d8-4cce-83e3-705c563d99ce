#!/usr/bin/env python3
"""
Redis监控脚本

监控Redis性能指标和TaskManager队列状态
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class RedisMonitor:
    """Redis监控器"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self.redis_client = None
        
        # TaskManager队列键
        self.taskmanager_keys = {
            "pending_queue": "taskmanager:pending_batches",
            "running_hash": "taskmanager:running_batches", 
            "completed_set": "taskmanager:completed_batches",
            "failed_set": "taskmanager:failed_batches",
            "manager_state": "taskmanager:state"
        }
        
        # 监控指标历史
        self.metrics_history = []
        self.max_history_size = 100
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info(f"Connected to Redis: {self.redis_url}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            return False
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.aclose()
            logger.info("Disconnected from Redis")
    
    async def collect_redis_metrics(self) -> Dict[str, Any]:
        """收集Redis基础指标"""
        try:
            # 获取Redis信息
            info = await self.redis_client.info()
            
            # 提取关键指标
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "server": {
                    "redis_version": info.get("redis_version"),
                    "uptime_in_seconds": info.get("uptime_in_seconds"),
                    "connected_clients": info.get("connected_clients"),
                    "blocked_clients": info.get("blocked_clients")
                },
                "memory": {
                    "used_memory": info.get("used_memory"),
                    "used_memory_human": info.get("used_memory_human"),
                    "used_memory_peak": info.get("used_memory_peak"),
                    "used_memory_peak_human": info.get("used_memory_peak_human"),
                    "maxmemory": info.get("maxmemory"),
                    "maxmemory_human": info.get("maxmemory_human"),
                    "mem_fragmentation_ratio": info.get("mem_fragmentation_ratio")
                },
                "persistence": {
                    "aof_enabled": info.get("aof_enabled"),
                    "rdb_last_save_time": info.get("rdb_last_save_time"),
                    "aof_last_rewrite_time_sec": info.get("aof_last_rewrite_time_sec"),
                    "aof_current_size": info.get("aof_current_size"),
                    "rdb_changes_since_last_save": info.get("rdb_changes_since_last_save")
                },
                "stats": {
                    "total_connections_received": info.get("total_connections_received"),
                    "total_commands_processed": info.get("total_commands_processed"),
                    "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec"),
                    "keyspace_hits": info.get("keyspace_hits"),
                    "keyspace_misses": info.get("keyspace_misses"),
                    "expired_keys": info.get("expired_keys"),
                    "evicted_keys": info.get("evicted_keys")
                }
            }
            
            # 计算命中率
            hits = metrics["stats"]["keyspace_hits"] or 0
            misses = metrics["stats"]["keyspace_misses"] or 0
            total = hits + misses
            
            if total > 0:
                metrics["stats"]["hit_rate"] = hits / total
            else:
                metrics["stats"]["hit_rate"] = 0
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect Redis metrics: {e}")
            return {}
    
    async def collect_taskmanager_metrics(self) -> Dict[str, Any]:
        """收集TaskManager队列指标"""
        try:
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "queues": {},
                "total_items": 0
            }
            
            # 检查待处理队列
            pending_count = await self.redis_client.llen(self.taskmanager_keys["pending_queue"])
            metrics["queues"]["pending"] = {
                "count": pending_count,
                "type": "list"
            }
            metrics["total_items"] += pending_count
            
            # 检查运行哈希
            running_count = await self.redis_client.hlen(self.taskmanager_keys["running_hash"])
            metrics["queues"]["running"] = {
                "count": running_count,
                "type": "hash"
            }
            metrics["total_items"] += running_count
            
            # 检查完成集合
            completed_count = await self.redis_client.scard(self.taskmanager_keys["completed_set"])
            metrics["queues"]["completed"] = {
                "count": completed_count,
                "type": "set"
            }
            metrics["total_items"] += completed_count
            
            # 检查失败集合
            failed_count = await self.redis_client.scard(self.taskmanager_keys["failed_set"])
            metrics["queues"]["failed"] = {
                "count": failed_count,
                "type": "set"
            }
            metrics["total_items"] += failed_count
            
            # 检查管理器状态
            manager_state = await self.redis_client.get(self.taskmanager_keys["manager_state"])
            if manager_state:
                try:
                    state_data = json.loads(manager_state)
                    metrics["manager_state"] = state_data
                except json.JSONDecodeError:
                    metrics["manager_state"] = None
            else:
                metrics["manager_state"] = None
            
            # 获取队列样本数据
            if pending_count > 0:
                sample = await self.redis_client.lrange(self.taskmanager_keys["pending_queue"], 0, 2)
                metrics["queues"]["pending"]["samples"] = len(sample)
            
            if running_count > 0:
                sample_keys = await self.redis_client.hkeys(self.taskmanager_keys["running_hash"])
                metrics["queues"]["running"]["samples"] = len(sample_keys[:3])
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect TaskManager metrics: {e}")
            return {}
    
    async def collect_all_metrics(self) -> Dict[str, Any]:
        """收集所有指标"""
        redis_metrics = await self.collect_redis_metrics()
        taskmanager_metrics = await self.collect_taskmanager_metrics()
        
        combined_metrics = {
            "timestamp": datetime.now().isoformat(),
            "redis": redis_metrics,
            "taskmanager": taskmanager_metrics
        }
        
        # 添加到历史记录
        self.metrics_history.append(combined_metrics)
        
        # 限制历史记录大小
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]
        
        return combined_metrics
    
    def analyze_trends(self) -> Dict[str, Any]:
        """分析指标趋势"""
        if len(self.metrics_history) < 2:
            return {"error": "Insufficient data for trend analysis"}
        
        current = self.metrics_history[-1]
        previous = self.metrics_history[-2]
        
        trends = {
            "timestamp": datetime.now().isoformat(),
            "period_seconds": 60,  # 假设每分钟收集一次
            "redis_trends": {},
            "taskmanager_trends": {}
        }
        
        # Redis趋势分析
        if current["redis"] and previous["redis"]:
            redis_current = current["redis"]["stats"]
            redis_previous = previous["redis"]["stats"]
            
            trends["redis_trends"] = {
                "ops_per_sec_change": redis_current.get("instantaneous_ops_per_sec", 0) - redis_previous.get("instantaneous_ops_per_sec", 0),
                "commands_processed_change": redis_current.get("total_commands_processed", 0) - redis_previous.get("total_commands_processed", 0),
                "hit_rate_change": redis_current.get("hit_rate", 0) - redis_previous.get("hit_rate", 0)
            }
        
        # TaskManager趋势分析
        if current["taskmanager"] and previous["taskmanager"]:
            tm_current = current["taskmanager"]["queues"]
            tm_previous = previous["taskmanager"]["queues"]
            
            trends["taskmanager_trends"] = {
                "pending_change": tm_current.get("pending", {}).get("count", 0) - tm_previous.get("pending", {}).get("count", 0),
                "running_change": tm_current.get("running", {}).get("count", 0) - tm_previous.get("running", {}).get("count", 0),
                "completed_change": tm_current.get("completed", {}).get("count", 0) - tm_previous.get("completed", {}).get("count", 0),
                "failed_change": tm_current.get("failed", {}).get("count", 0) - tm_previous.get("failed", {}).get("count", 0)
            }
        
        return trends
    
    def check_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查告警条件"""
        alerts = []
        
        if not metrics.get("redis") or not metrics.get("taskmanager"):
            return alerts
        
        redis_metrics = metrics["redis"]
        tm_metrics = metrics["taskmanager"]
        
        # Redis内存使用率告警
        if redis_metrics.get("memory", {}).get("mem_fragmentation_ratio", 0) > 2.0:
            alerts.append({
                "type": "warning",
                "component": "redis",
                "metric": "memory_fragmentation",
                "value": redis_metrics["memory"]["mem_fragmentation_ratio"],
                "threshold": 2.0,
                "message": "Redis内存碎片率过高"
            })
        
        # 连接数告警
        connected_clients = redis_metrics.get("server", {}).get("connected_clients", 0)
        if connected_clients > 1000:
            alerts.append({
                "type": "warning",
                "component": "redis",
                "metric": "connected_clients",
                "value": connected_clients,
                "threshold": 1000,
                "message": "Redis连接数过多"
            })
        
        # TaskManager队列积压告警
        pending_count = tm_metrics.get("queues", {}).get("pending", {}).get("count", 0)
        if pending_count > 100:
            alerts.append({
                "type": "warning",
                "component": "taskmanager",
                "metric": "pending_queue",
                "value": pending_count,
                "threshold": 100,
                "message": "TaskManager待处理队列积压"
            })
        
        # 失败任务告警
        failed_count = tm_metrics.get("queues", {}).get("failed", {}).get("count", 0)
        if failed_count > 50:
            alerts.append({
                "type": "error",
                "component": "taskmanager",
                "metric": "failed_queue",
                "value": failed_count,
                "threshold": 50,
                "message": "TaskManager失败任务过多"
            })
        
        return alerts
    
    def print_metrics_summary(self, metrics: Dict[str, Any]):
        """打印指标摘要"""
        print("\n" + "="*60)
        print(f"📊 Redis & TaskManager 监控报告")
        print(f"⏰ 时间: {metrics['timestamp']}")
        print("="*60)
        
        # Redis指标
        if metrics.get("redis"):
            redis_metrics = metrics["redis"]
            print(f"\n🔧 Redis 状态:")
            print(f"   版本: {redis_metrics['server']['redis_version']}")
            print(f"   运行时间: {redis_metrics['server']['uptime_in_seconds']}秒")
            print(f"   连接数: {redis_metrics['server']['connected_clients']}")
            print(f"   内存使用: {redis_metrics['memory']['used_memory_human']}")
            print(f"   内存峰值: {redis_metrics['memory']['used_memory_peak_human']}")
            print(f"   碎片率: {redis_metrics['memory']['mem_fragmentation_ratio']:.2f}")
            print(f"   命中率: {redis_metrics['stats']['hit_rate']:.2%}")
            print(f"   QPS: {redis_metrics['stats']['instantaneous_ops_per_sec']}")
        
        # TaskManager指标
        if metrics.get("taskmanager"):
            tm_metrics = metrics["taskmanager"]
            print(f"\n📋 TaskManager 队列状态:")
            print(f"   待处理: {tm_metrics['queues']['pending']['count']}")
            print(f"   运行中: {tm_metrics['queues']['running']['count']}")
            print(f"   已完成: {tm_metrics['queues']['completed']['count']}")
            print(f"   失败: {tm_metrics['queues']['failed']['count']}")
            print(f"   总计: {tm_metrics['total_items']}")
            
            if tm_metrics.get("manager_state"):
                state = tm_metrics["manager_state"]
                print(f"   管理器状态: {state.get('status', 'unknown')}")
                print(f"   保存时间: {state.get('saved_at', 'unknown')}")
        
        print("="*60)
    
    async def run_continuous_monitoring(self, interval: int = 60):
        """运行连续监控"""
        logger.info(f"开始连续监控 (间隔: {interval}秒)")
        
        try:
            while True:
                # 收集指标
                metrics = await self.collect_all_metrics()
                
                # 打印摘要
                self.print_metrics_summary(metrics)
                
                # 检查告警
                alerts = self.check_alerts(metrics)
                if alerts:
                    print(f"\n🚨 告警信息:")
                    for alert in alerts:
                        print(f"   [{alert['type'].upper()}] {alert['component']}: {alert['message']}")
                        print(f"   当前值: {alert['value']}, 阈值: {alert['threshold']}")
                
                # 分析趋势
                if len(self.metrics_history) >= 2:
                    trends = self.analyze_trends()
                    if trends.get("taskmanager_trends"):
                        tm_trends = trends["taskmanager_trends"]
                        print(f"\n📈 趋势分析 (过去{trends['period_seconds']}秒):")
                        print(f"   待处理变化: {tm_trends['pending_change']:+d}")
                        print(f"   运行中变化: {tm_trends['running_change']:+d}")
                        print(f"   完成变化: {tm_trends['completed_change']:+d}")
                        print(f"   失败变化: {tm_trends['failed_change']:+d}")
                
                # 等待下次监控
                await asyncio.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("监控被用户中断")
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Redis & TaskManager 监控工具")
    parser.add_argument("--redis-url", default="redis://localhost:6379/0", help="Redis连接URL")
    parser.add_argument("--interval", type=int, default=60, help="监控间隔(秒)")
    parser.add_argument("--once", action="store_true", help="只运行一次")
    
    args = parser.parse_args()
    
    monitor = RedisMonitor(args.redis_url)
    
    if not await monitor.connect():
        sys.exit(1)
    
    try:
        if args.once:
            # 单次监控
            metrics = await monitor.collect_all_metrics()
            monitor.print_metrics_summary(metrics)
            
            alerts = monitor.check_alerts(metrics)
            if alerts:
                print(f"\n🚨 告警信息:")
                for alert in alerts:
                    print(f"   [{alert['type'].upper()}] {alert['component']}: {alert['message']}")
        else:
            # 连续监控
            await monitor.run_continuous_monitoring(args.interval)
    
    finally:
        await monitor.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
