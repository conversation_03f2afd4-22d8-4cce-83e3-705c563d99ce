.task-manager-migration {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.migration-header {
  text-align: center;
  margin-bottom: 32px;
}

.migration-header .ant-typography-title {
  margin-bottom: 16px;
}

.workflow-card {
  margin-bottom: 24px;
}

.workflow-card .ant-steps {
  margin-bottom: 24px;
}

.workflow-details ul {
  list-style: none;
  padding: 0;
}

.workflow-details li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.action-buttons {
  text-align: center;
  margin-top: 24px;
}

.quick-start-guide .ant-typography-title {
  color: #1890ff;
  margin-top: 24px;
  margin-bottom: 12px;
}

.quick-start-guide .ant-typography-title:first-child {
  margin-top: 0;
}

.quick-start-guide .ant-typography-paragraph {
  margin-bottom: 16px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-manager-migration {
    padding: 16px;
  }
  
  .action-buttons .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
    margin-bottom: 12px;
  }
}
