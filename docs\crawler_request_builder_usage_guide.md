# CrawlerRequestBuilder 使用指南

## 概述

CrawlerRequestBuilder是MonIt系统中用于构建标准化爬虫API请求的核心服务。它统一了从监控任务到爬虫API的数据流，确保请求格式的一致性和数据来源的清晰性。

## 核心功能

### 1. 标准化请求构建
- 自动从多个数据源收集配置信息
- 生成符合爬虫API规范的请求格式
- 处理数据类型转换和验证

### 2. 数据来源管理
- **监控任务**：活跃URL列表
- **爬取配置**：用户级参数（查询、Schema、缓存等）
- **系统配置**：性能参数（并发数、超时等）
- **执行上下文**：运行时信息（IP、任务ID等）

### 3. 错误处理
- 验证必需的配置是否存在
- 处理URL获取失败的情况
- 提供详细的错误信息

## 使用方法

### 基本用法

```python
from app.services.crawler_request_builder import CrawlerRequestBuilder

# 为监控任务构建完整的爬虫请求
async def execute_monitoring_task(task_id: str):
    try:
        # 构建执行上下文
        execution_context = {
            "execution_id": f"exec_{task_id}_{int(time.time())}",
            "client_ip": "**********",
            "celery_task_id": "celery-123"
        }
        
        # 构建爬虫请求
        crawler_request = await CrawlerRequestBuilder.build_request_for_task(
            task_id=task_id,
            execution_context=execution_context
        )
        
        # 发送到爬虫API
        crawler_api = CrawlerAPIService()
        result = await crawler_api.submit_batch_request(crawler_request)
        
        return result
        
    except ValueError as ve:
        # 处理验证错误（如没有活跃URL、没有配置等）
        logger.warning(f"Validation error: {ve}")
        return {"status": "validation_error", "error": str(ve)}
    except Exception as e:
        # 处理其他错误
        logger.error(f"Request building failed: {e}")
        raise
```

### 在Celery任务中使用

```python
from app.tasks.enhanced_crawl_task import enhanced_crawl_task

# 方式1：使用专门的增强任务
@celery_app.task
def my_monitoring_task(task_id: str):
    # 直接调用增强的爬取任务
    return enhanced_crawl_task.delay(task_id, {"source": "monitoring"})

# 方式2：在现有任务中集成
@celery_app.task
def my_existing_task(task_id: str, use_enhanced: bool = True):
    if use_enhanced:
        # 使用新的请求构建方式
        return _run_async_in_thread(_crawl_with_request_builder(task_id))
    else:
        # 使用原有方式（向后兼容）
        return _run_async_in_thread(_crawl_legacy_way(task_id))
```

### 在监控执行器中使用

```python
from app.tasks.monitoring_executor import MonitoringTaskExecutor

# 创建执行器并使用增强方式
executor = MonitoringTaskExecutor(task_id)

# 方式1：使用增强的执行方法
result = await executor.execute_with_request_builder()

# 方式2：使用原有方法（向后兼容）
result = await executor.execute()
```

## 可用的Celery任务

### 新增的增强任务

1. **enhanced_crawl_task**
   - 队列：`crawler_queue`
   - 功能：使用CrawlerRequestBuilder的完整爬取任务
   - 参数：`task_id`, `execution_context`

2. **enhanced_single_url_crawl**
   - 队列：`crawler_queue`
   - 功能：单URL爬取，使用标准化请求构建
   - 参数：`task_id`, `url`, `execution_context`

3. **crawl_batch_task_enhanced**
   - 队列：`crawler_queue`
   - 功能：批量爬取的增强版本
   - 参数：`task_id`, `execution_context`

4. **execute_monitoring_task_enhanced**
   - 队列：`monitor_queue`
   - 功能：监控任务执行的增强版本
   - 参数：`task_id`

### 现有任务的增强

现有的 `crawl_batch_task` 现在支持通过 `options.use_request_builder=True` 来使用新的请求构建方式。

## 配置要求

### 环境变量

确保以下环境变量已正确配置：

```bash
# 爬虫API配置
CRAWLER_DEFAULT_CALLBACK_URL=http://backend:8000/api/v1/crawler/callback
CRAWLER_MAX_CONCURRENT_TASKS=2
CRAWLER_BATCH_TIMEOUT=300

# Redis配置
REDIS_URL=redis://redis:6379/0
```

### 爬取配置

监控任务必须包含完整的爬取配置：

```json
{
  "config": {
    "llm_query": "提取商品信息",
    "llm_schema": "{\"type\": \"object\", \"properties\": {...}}",
    "cache_enabled": true,
    "priority": "high",
    "auth_method": "api_key",
    "api_key_prefix": "your_key_prefix",
    "user_agent": "MonIt-Crawler/1.0"
  }
}
```

## 错误处理

### 常见错误和解决方案

1. **"Task has no active URLs"**
   - 原因：监控任务没有关联活跃的URL
   - 解决：检查URL池中的URL状态，确保有status='active'的URL

2. **"Task has no crawl config"**
   - 原因：监控任务缺少爬取配置
   - 解决：确保任务的config字段包含必需的配置项

3. **"SystemConfigService not configured"**
   - 原因：系统配置服务未正确初始化
   - 解决：检查环境变量配置

### 调试技巧

1. **启用详细日志**：
```python
import logging
logging.getLogger('app.services.crawler_request_builder').setLevel(logging.DEBUG)
```

2. **检查构建的请求**：
```python
crawler_request = await CrawlerRequestBuilder.build_request_for_task(task_id)
print(json.dumps(crawler_request, indent=2, ensure_ascii=False))
```

3. **验证数据来源**：
```python
# 检查活跃URL
urls = await CrawlerRequestBuilder.get_active_urls_for_task(task_id)
print(f"Active URLs: {len(urls)}")

# 检查爬取配置
config = await CrawlerRequestBuilder.get_crawl_config_for_task(task_id)
print(f"Crawl config: {config}")
```

## 迁移指南

### 从旧方式迁移到新方式

1. **识别现有的爬取任务调用**
2. **替换为新的增强任务**
3. **更新调用参数**
4. **测试验证**

#### 迁移示例

```python
# 旧方式
crawl_batch_task.delay(
    batch_id="batch-123",
    urls=["url1", "url2"],
    platform="mercadolibre",
    task_id="task-456",
    options={"priority": "high", "cache": True}
)

# 新方式
enhanced_crawl_task.delay(
    task_id="task-456",
    execution_context={"source": "monitoring", "batch_id": "batch-123"}
)
```

## 性能考虑

### 优化建议

1. **批量URL获取**：对于大量URL的任务，考虑实现批量获取优化
2. **配置缓存**：频繁访问的配置可以考虑缓存
3. **异步处理**：所有数据库和Redis操作都使用异步方式

### 监控指标

建议监控以下指标：
- 请求构建时间
- URL获取成功率
- 配置获取成功率
- API调用成功率

## 最佳实践

1. **始终提供执行上下文**：包含足够的调试信息
2. **处理验证错误**：优雅地处理配置缺失等情况
3. **记录详细日志**：便于问题排查
4. **使用适当的任务**：根据场景选择合适的Celery任务
5. **测试覆盖**：为自定义的请求构建逻辑编写测试
