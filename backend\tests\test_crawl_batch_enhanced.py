"""
测试增强的批量爬取任务
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from app.tasks.crawl_batch import _crawl_batch_with_request_builder


class TestCrawlBatchEnhanced:
    """测试增强的批量爬取任务"""
    
    @pytest.mark.asyncio
    async def test_crawl_batch_with_request_builder_success(self):
        """测试使用请求构建器的批量爬取成功场景"""
        
        # 模拟Celery任务对象
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-batch-123"
        
        task_id = "test-batch-task-456"
        execution_context = {"test_key": "test_value"}
        
        # 模拟CrawlerRequestBuilder返回的请求
        mock_crawler_request = {
            "request": {
                "urls": [
                    "https://example.com/1", 
                    "https://example.com/2",
                    "https://example.com/3"
                ],
                "q": "提取商品信息",
                "priority": "high",
                "batch_name": "Task_test-batch-task-456_20250809_123456",
                "max_concurrent_tasks": 2,
                "batch_timeout": 300
            },
            "auth_context": {
                "user_id": "monit_system",
                "auth_method": "api_key"
            }
        }
        
        # 模拟爬虫API响应
        mock_api_result = {
            "status": "success",
            "batch_id": "batch-789",
            "submitted_urls": 3,
            "estimated_completion": "2025-08-09T15:00:00"
        }
        
        with patch('app.tasks.crawl_batch.CrawlerRequestBuilder') as mock_builder, \
             patch('app.services.crawler_api_service.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值
            mock_builder.build_request_for_task = AsyncMock(return_value=mock_crawler_request)
            
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.return_value = mock_api_result
            mock_api_service.return_value = mock_api_instance
            
            # 执行测试
            result = await _crawl_batch_with_request_builder(
                mock_celery_task, task_id, execution_context
            )
            
            # 验证结果
            assert result["status"] == "success"
            assert result["task_id"] == task_id
            assert result["celery_task_id"] == "celery-batch-123"
            assert result["urls_count"] == 3
            assert result["priority"] == "high"
            assert result["batch_name"] == "Task_test-batch-task-456_20250809_123456"
            assert result["method"] == "request_builder"  # 确认使用了新方法
            assert "start_time" in result
            assert "end_time" in result
            assert "duration" in result
            assert result["crawler_api_result"] == mock_api_result
            
            # 验证调用
            mock_builder.build_request_for_task.assert_called_once()
            call_args = mock_builder.build_request_for_task.call_args
            assert call_args[1]["task_id"] == task_id
            assert "celery_task_id" in call_args[1]["execution_context"]
            
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)
    
    @pytest.mark.asyncio
    async def test_crawl_batch_with_request_builder_no_urls(self):
        """测试批量爬取任务没有活跃URL的场景"""
        
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-batch-456"
        
        task_id = "test-batch-task-no-urls"
        execution_context = {}
        
        with patch('app.tasks.crawl_batch.CrawlerRequestBuilder') as mock_builder:
            # 模拟没有活跃URL的错误
            mock_builder.build_request_for_task = AsyncMock(
                side_effect=ValueError("Task has no active URLs")
            )
            
            # 执行测试
            result = await _crawl_batch_with_request_builder(
                mock_celery_task, task_id, execution_context
            )
            
            # 验证结果
            assert result["status"] == "validation_error"
            assert result["task_id"] == task_id
            assert "Task has no active URLs" in result["error"]
            assert result["method"] == "request_builder"
            assert "start_time" in result
            assert "end_time" in result
    
    @pytest.mark.asyncio
    async def test_crawl_batch_with_request_builder_api_error(self):
        """测试批量爬取任务API调用失败的场景"""
        
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-batch-789"
        
        task_id = "test-batch-task-api-error"
        execution_context = {}
        
        # 模拟正常的请求构建
        mock_crawler_request = {
            "request": {
                "urls": ["https://example.com/1"],
                "q": "提取商品信息",
                "priority": "medium",
                "batch_name": "Test_Batch"
            },
            "auth_context": {"user_id": "monit_system"}
        }
        
        with patch('app.tasks.crawl_batch.CrawlerRequestBuilder') as mock_builder, \
             patch('app.services.crawler_api_service.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值
            mock_builder.build_request_for_task = AsyncMock(return_value=mock_crawler_request)
            
            # 模拟API调用失败
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.side_effect = Exception("API connection failed")
            mock_api_service.return_value = mock_api_instance
            
            # 执行测试，应该抛出异常
            with pytest.raises(Exception, match="Batch crawl with request builder failed"):
                await _crawl_batch_with_request_builder(
                    mock_celery_task, task_id, execution_context
                )
            
            # 验证调用
            mock_builder.build_request_for_task.assert_called_once()
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)


if __name__ == "__main__":
    pytest.main([__file__])
