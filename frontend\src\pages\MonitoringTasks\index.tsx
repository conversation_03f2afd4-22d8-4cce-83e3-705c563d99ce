import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Table,
  Tag,
  Space,
  Statistic,
  Row,
  Col,
  Modal,
  Input,
  Select,
  message,
  Tooltip,
  Progress,
  Typography,
  DatePicker,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  LinkOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  getMonitoringTasks,
  startMonitoringTask,
  pauseMonitoringTask,
  deleteMonitoringTask,
  type MonitoringTask
} from '../../services/monitoringTaskApi';
import { generateScheduleDescription } from '../../utils/timeUtils';

const { Title, Text } = Typography;
const { Option } = Select;

// 使用导入的类型定义

// 筛选器接口
interface TaskFilters {
  status?: string;
  platform?: string;
  source?: string;
  search?: string;
  dateRange?: [string, string];
}

// 分页接口
interface Pagination {
  page: number;
  pageSize: number;
  total: number;
}

const MonitoringTasks: React.FC = () => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<MonitoringTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 新增状态管理
  const [filters, setFilters] = useState<TaskFilters>({});
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 20,
    total: 0
  });
  const [sortBy] = useState('created_at');
  const [sortOrder] = useState<'asc' | 'desc'>('desc');

  // 获取监控任务列表 - 增强版
  const fetchTasks = useCallback(async (customFilters?: TaskFilters, customPagination?: Partial<Pagination>) => {
    setLoading(true);
    try {
      const currentFilters = customFilters || filters;
      const currentPagination = customPagination ? { ...pagination, ...customPagination } : pagination;

      // 构建查询参数
      const params: any = {
        page: currentPagination.page,
        page_size: currentPagination.pageSize,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      // 添加筛选参数
      if (currentFilters.status) params.status = currentFilters.status;
      if (currentFilters.platform) params.platform = currentFilters.platform;
      if (currentFilters.source) params.source = currentFilters.source;
      if (currentFilters.search) params.search = currentFilters.search;

      const response = await getMonitoringTasks(params);
      setTasks(response.data);
      setPagination(prev => ({
        ...prev,
        total: response.total || 0
      }));
    } catch (error) {
      console.error('获取监控任务失败:', error);
      message.error('获取监控任务失败');
      setTasks([]);
    } finally {
      setLoading(false);
    }
  }, []); // 移除依赖项，避免无限循环

  useEffect(() => {
    fetchTasks();
  }, []); // 只在组件挂载时执行一次

  // 筛选处理函数
  const handleFilterChange = useCallback((key: keyof TaskFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // 重置到第一页并重新获取数据
    const newPagination = { ...pagination, page: 1 };
    setPagination(newPagination);
    fetchTasks(newFilters, newPagination);
  }, [filters, pagination]);

  // 重置筛选
  const handleResetFilters = useCallback(() => {
    const newFilters = {};
    const newPagination = { ...pagination, page: 1 };
    setFilters(newFilters);
    setPagination(newPagination);
    fetchTasks(newFilters, newPagination);
  }, [pagination]);

  // 批量操作处理
  const handleBatchOperation = useCallback(async (operation: 'start' | 'pause' | 'delete') => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的任务');
      return;
    }

    try {
      setLoading(true);
      const promises = selectedRowKeys.map(async (taskId) => {
        switch (operation) {
          case 'start':
            return await startMonitoringTask(taskId as string);
          case 'pause':
            return await pauseMonitoringTask(taskId as string);
          case 'delete':
            return await deleteMonitoringTask(taskId as string);
          default:
            return Promise.resolve();
        }
      });

      await Promise.all(promises);

      message.success(`批量${operation === 'start' ? '启动' : operation === 'pause' ? '暂停' : '删除'}成功`);
      setSelectedRowKeys([]);
      fetchTasks();
    } catch (error) {
      console.error(`批量${operation}操作失败:`, error);
      message.error(`批量${operation}操作失败`);
    } finally {
      setLoading(false);
    }
  }, [selectedRowKeys]);

  // 查看任务详情
  const handleViewTask = useCallback((taskId: string) => {
    navigate(`/monitoring/tasks/${taskId}`);
  }, [navigate]);

  // 编辑任务
  const handleEditTask = useCallback((taskId: string) => {
    navigate(`/monitoring/tasks/${taskId}/edit`);
  }, [navigate]);



  // 删除任务
  const handleDeleteTask = useCallback((taskId: string) => {
    Modal.confirm({
      title: '确认删除任务',
      content: '删除任务后将无法恢复，确定要删除这个任务吗？',
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          await deleteMonitoringTask(taskId);
          message.success('任务删除成功');
          await fetchTasks();
        } catch (error) {
          console.error('Failed to delete task:', error);
          message.error('删除任务失败');
        } finally {
          setLoading(false);
        }
      }
    });
  }, [fetchTasks]);

  // 任务操作处理
  const handleTaskAction = async (taskId: string, action: 'start' | 'pause' | 'stop') => {
    try {
      if (action === 'start') {
        await startMonitoringTask(taskId);
        message.success('任务启动成功');
      } else if (action === 'pause') {
        await pauseMonitoringTask(taskId);
        message.success('任务暂停成功');
      } else {
        // TODO: 实现停止功能
        message.success('任务停止成功');
      }
      await fetchTasks();
    } catch (error) {
      console.error('任务操作失败:', error);
      message.error(`任务操作失败: ${(error as Error).message}`);
    }
  };



  // 表格列定义 - 增强版
  const columns: ColumnsType<MonitoringTask> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
          )}
          {/* 显示任务ID (调试用) */}
          <div style={{ fontSize: '10px', color: '#666', marginTop: '1px', fontFamily: 'monospace' }}>
            ID: {record.id}
          </div>
          {/* 显示任务来源 */}
          <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
            来源: {(() => {
              const source = record.source || (record as any).source;
              switch (source) {
                case 'url_pool':
                  return 'URL池';
                case 'manual':
                  return '手动创建';
                case 'api':
                  return 'API创建';
                case 'excel':
                  return 'Excel导入';
                default:
                  // 如果没有source字段，尝试从其他信息推断
                  if (record.source_info?.created_from) {
                    return record.source_info.created_from;
                  }
                  return source || '未知';
              }
            })()}
          </div>
        </div>
      )
    },
    {
      title: '平台',
      dataIndex: ['config', 'platform'],
      key: 'platform',
      width: 100,
      render: (platform: string) => (
        <Tag color={
          platform === 'amazon' ? 'orange' :
          platform === 'mercadolibre' ? 'blue' :
          platform === 'ebay' ? 'green' :
          platform === 'shopee' ? 'red' : 'default'
        }>
          {platform?.toUpperCase() || '未知'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: MonitoringTask) => {
        const statusConfig: Record<string, { color: string; text: string }> = {
          active: { color: 'green', text: '运行中' },
          paused: { color: 'orange', text: '已暂停' },
          stopped: { color: 'red', text: '已停止' },
          draft: { color: 'default', text: '草稿' }
        };
        const config = statusConfig[status] || { color: 'default', text: '未知' };
        return (
          <div>
            <Tag color={config.color}>{config.text}</Tag>
            {record.is_running && <Tag color="blue">执行中</Tag>}
          </div>
        );
      }
    },
    {
      title: '定时配置',
      key: 'schedule',
      width: 150,
      render: (_, record) => {
        const { schedule } = record;
        let scheduleText = '';

        // 使用统一的调度描述生成函数
        scheduleText = generateScheduleDescription(schedule);

        // 格式化下次执行时间
        const formatNextRun = (nextRun: string) => {
          try {
            const date = new Date(nextRun);
            return date.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            });
          } catch (error) {
            console.warn('Failed to format next run time:', nextRun, error);
            return nextRun;
          }
        };

        return (
          <div>
            <div><ClockCircleOutlined /> {scheduleText}</div>
            {record.next_run && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                下次: {formatNextRun(record.next_run)}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '链接统计',
      key: 'urls',
      width: 120,
      render: (_, record) => (
        <div>
          <div><LinkOutlined /> {record.active_urls}/{record.total_urls}</div>
          <Progress
            percent={record.total_urls > 0 ? Math.round((record.active_urls / record.total_urls) * 100) : 0}
            size="small"
            showInfo={false}
          />
        </div>
      )
    },
    {
      title: '成功率',
      dataIndex: ['stats', 'success_rate'],
      key: 'success_rate',
      width: 100,
      render: (rate) => (
        <div>
          <Progress
            type="circle"
            size={50}
            percent={Math.round(rate || 0)}
            format={percent => `${percent}%`}
          />
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewTask(record.id)}
            />
          </Tooltip>
          <Tooltip title="编辑任务">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditTask(record.id)}
            />
          </Tooltip>
          {record.status === 'active' ? (
            <Tooltip title="暂停任务">
              <Button
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handleTaskAction(record.id, 'pause')}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动任务">
              <Button
                size="small"
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={() => handleTaskAction(record.id, 'start')}
              />
            </Tooltip>
          )}
          <Tooltip title="删除任务">
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteTask(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 统计数据
  const stats = {
    totalTasks: tasks.length,
    activeTasks: tasks.filter(t => t.status === 'active').length,
    pausedTasks: tasks.filter(t => t.status === 'paused').length,
    totalUrls: tasks.reduce((sum, t) => sum + t.total_urls, 0)
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>监控任务管理</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总任务数" value={stats.totalTasks} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="活跃任务" value={stats.activeTasks} valueStyle={{ color: '#3f8600' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="暂停任务" value={stats.pausedTasks} valueStyle={{ color: '#cf1322' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总链接数" value={stats.totalUrls} />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮和筛选器 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Space size="middle">
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchTasks()}
              >
                刷新
              </Button>
              {selectedRowKeys.length > 0 && (
                <>
                  <Divider type="vertical" />
                  <Text type="secondary">已选择 {selectedRowKeys.length} 项</Text>
                  <Button size="small" onClick={() => setSelectedRowKeys([])}>
                    取消选择
                  </Button>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => handleBatchOperation('start')}
                  >
                    批量启动
                  </Button>
                  <Button
                    size="small"
                    onClick={() => handleBatchOperation('pause')}
                  >
                    批量暂停
                  </Button>
                  <Button
                    size="small"
                    danger
                    onClick={() => handleBatchOperation('delete')}
                  >
                    批量删除
                  </Button>
                </>
              )}
            </Space>
          </Col>
        </Row>

        {/* 筛选和搜索区域 */}
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={6}>
            <Input
              placeholder="搜索任务名称或描述"
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="draft">草稿</Option>
              <Option value="active">活跃</Option>
              <Option value="paused">暂停</Option>
              <Option value="stopped">停止</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="平台筛选"
              value={filters.platform}
              onChange={(value) => handleFilterChange('platform', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="amazon">Amazon</Option>
              <Option value="mercadolibre">MercadoLibre</Option>
              <Option value="ebay">eBay</Option>
              <Option value="shopee">Shopee</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="来源筛选"
              value={filters.source}
              onChange={(value) => handleFilterChange('source', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="url_pool">URL池</Option>
              <Option value="manual">手动创建</Option>
              <Option value="api">API创建</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={handleResetFilters}
              >
                重置筛选
              </Button>
              <Text type="secondary">
                共 {pagination.total} 个任务
              </Text>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 任务列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            preserveSelectedRowKeys: true,
          }}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 个任务`,
            onChange: (page, pageSize) => {
              const newPagination = { ...pagination, page, pageSize };
              setPagination(newPagination);
              fetchTasks(filters, newPagination);
            },
            onShowSizeChange: (current, size) => {
              const newPagination = { ...pagination, page: 1, pageSize: size };
              setPagination(newPagination);
              fetchTasks(filters, newPagination);
            }
          }}
          scroll={{ x: 1200 }}
        />
      </Card>




    </div>
  );
};

export default MonitoringTasks;
