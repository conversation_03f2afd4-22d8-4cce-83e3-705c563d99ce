# 子任务03-04: 数据提取和解析器开发

## 任务描述
开发高效、准确的数据提取和解析系统，支持多种选择器策略、数据清洗和格式化，确保提取数据的准确性和完整性。

## 具体任务内容

### 1. 配置文件解析器
- 解析选择器配置JSON文件
- 支持多种选择器类型 (CSS、XPath、Regex等)
- 实现选择器优先级和enable状态管理
- 动态选择器策略切换和热重载

### 2. CSS选择器引擎
- 实现复杂CSS选择器解析和执行
- 支持XPath表达式处理
- 多选择器策略并行验证
- 选择器优先级和fallback机制

### 3. 多选择器交叉验证
- 实现多选择器并行提取和对比
- 结果置信度评估和最优选择
- 选择器失效自动检测和切换
- 提取结果一致性验证

### 4. 数据清洗和格式化
- 价格数据标准化处理(去除货币符号、格式化)
- 文本内容清理(去除多余空格、特殊字符)
- 数字数据类型转换和验证
- 图片URL完整性验证和补全

### 5. 结构化数据提取
- JSON-LD数据解析
- Microdata和RDFa数据提取
- Schema.org产品数据识别
- Open Graph和Twitter Cards数据提取

### 6. 动态内容处理
- AJAX加载内容等待和提取
- 懒加载图片和内容处理
- 无限滚动数据收集
- JavaScript生成内容提取

### 7. 数据验证和质量控制
- 数据完整性检查
- 格式验证和类型检查
- 异常值检测和处理
- 数据一致性验证

## 技术实现

### 配置文件解析器
```python
import json
import yaml
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime

@dataclass
class SelectorConfig:
    """选择器配置模型"""
    name: str
    strategy: str  # css, xpath, regex, json_ld
    selector: str
    attribute: Optional[str] = None
    priority: int = 0
    enabled: bool = True
    confidence: float = 0.5
    timeout: int = 5000
    multiple: bool = False
    fallback: Optional[List[Dict]] = None
    validation: Optional[Dict] = None

class SelectorConfigParser:
    """选择器配置解析器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.selectors: Dict[str, List[SelectorConfig]] = {}
        self.last_modified = None
    
    def load_config(self) -> Dict[str, List[SelectorConfig]]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.endswith('.yaml') or self.config_path.endswith('.yml'):
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)
            
            self.selectors = self._parse_config(config_data)
            self.last_modified = datetime.now()
            return self.selectors
            
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}
    
    def _parse_config(self, config_data: Dict) -> Dict[str, List[SelectorConfig]]:
        """解析配置数据"""
        parsed_selectors = {}
        
        for field_name, field_config in config_data.get('selectors', {}).items():
            selectors_list = []
            
            if isinstance(field_config, list):
                # 多个选择器配置
                for selector_data in field_config:
                    if selector_data.get('enabled', True):
                        selector_config = SelectorConfig(
                            name=f"{field_name}_{len(selectors_list)}",
                            **selector_data
                        )
                        selectors_list.append(selector_config)
            else:
                # 单个选择器配置
                if field_config.get('enabled', True):
                    selector_config = SelectorConfig(
                        name=field_name,
                        **field_config
                    )
                    selectors_list.append(selector_config)
            
            # 按优先级排序
            selectors_list.sort(key=lambda x: x.priority, reverse=True)
            parsed_selectors[field_name] = selectors_list
        
        return parsed_selectors
    
    def get_field_selectors(self, field_name: str) -> List[SelectorConfig]:
        """获取字段的选择器列表"""
        return self.selectors.get(field_name, [])
    
    def hot_reload(self) -> bool:
        """热重载配置"""
        try:
            current_mtime = os.path.getmtime(self.config_path)
            if not self.last_modified or current_mtime > self.last_modified.timestamp():
                self.load_config()
                return True
            return False
        except Exception:
            return False

class MultiSelectorValidator:
    """多选择器交叉验证器"""
    
    def __init__(self):
        self.confidence_threshold = 0.6
        self.max_parallel_selectors = 5
    
    async def validate_extraction(
        self, 
        page_content: str, 
        field_name: str, 
        selectors: List[SelectorConfig]
    ) -> Dict[str, Any]:
        """多选择器并行提取和验证"""
        results = []
        
        # 限制并行选择器数量
        active_selectors = selectors[:self.max_parallel_selectors]
        
        for selector_config in active_selectors:
            try:
                result = await self._extract_with_selector(page_content, selector_config)
                if result and self._is_valid_result(result, selector_config):
                    results.append({
                        'value': result,
                        'confidence': selector_config.confidence,
                        'selector_name': selector_config.name,
                        'strategy': selector_config.strategy
                    })
            except Exception as e:
                # 记录失败的选择器
                print(f"Selector {selector_config.name} failed: {e}")
        
        return self._choose_best_result(results, field_name)
    
    def _choose_best_result(self, results: List[Dict], field_name: str) -> Dict[str, Any]:
        """选择最佳结果"""
        if not results:
            return {'value': None, 'confidence': 0.0, 'source': 'none'}
        
        # 按置信度排序
        results.sort(key=lambda x: x['confidence'], reverse=True)
        
        best_result = results[0]
        
        # 如果有多个高置信度结果，进行一致性检查
        if len(results) > 1:
            high_confidence_results = [
                r for r in results 
                if r['confidence'] >= self.confidence_threshold
            ]
            
            if len(high_confidence_results) > 1:
                # 检查结果一致性
                values = [str(r['value']) for r in high_confidence_results]
                if len(set(values)) == 1:
                    # 结果一致，提高置信度
                    best_result['confidence'] = min(1.0, best_result['confidence'] * 1.2)
                else:
                    # 结果不一致，降低置信度
                    best_result['confidence'] *= 0.8
        
        return {
            'value': best_result['value'],
            'confidence': best_result['confidence'],
            'source': best_result['selector_name'],
            'strategy': best_result['strategy'],
            'alternatives': len(results) - 1
        }

### 选择器引擎
```python
from typing import Dict, List, Any, Optional, Union
from bs4 import BeautifulSoup
from lxml import html, etree
import re
import json

class SelectorEngine:
    """选择器引擎"""
    
    def __init__(self):
        self.strategies = {
            'css': self._extract_css,
            'xpath': self._extract_xpath,
            'regex': self._extract_regex,
            'json_ld': self._extract_json_ld
        }
    
    async def extract_data(self, content: str, selectors: Dict[str, Any]) -> Dict[str, Any]:
        """提取数据"""
        result = {}
        for field_name, selector_config in selectors.items():
            try:
                value = await self._extract_field(content, selector_config)
                result[field_name] = value
            except Exception as e:
                result[field_name] = None
                # 记录错误日志
        return result
    
    async def _extract_field(self, content: str, config: Dict) -> Any:
        """提取单个字段"""
        if isinstance(config, dict):
            strategy = config.get('strategy', 'css')
            selector = config.get('selector')
            multiple = config.get('multiple', False)
            
            if strategy in self.strategies:
                return await self.strategies[strategy](content, selector, multiple)
        
        return None

class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.price_patterns = [
            r'[\$\€\£\¥]?\s*(\d+(?:[,\.]\d{3})*(?:[,\.]\d{2})?)',
            r'(\d+(?:[,\.]\d{3})*(?:[,\.]\d{2})?)\s*[\$\€\£\¥]'
        ]
    
    def clean_price(self, price_text: str) -> Optional[float]:
        """清洗价格数据"""
        if not price_text:
            return None
        
        # 移除空格和特殊字符
        cleaned = re.sub(r'[^\d,.\$\€\£\¥]', '', price_text)
        
        for pattern in self.price_patterns:
            match = re.search(pattern, cleaned)
            if match:
                number_str = match.group(1)
                # 处理千位分隔符
                number_str = re.sub(r'[,\s]', '', number_str)
                try:
                    return float(number_str)
                except ValueError:
                    continue
        
        return None
    
    def clean_text(self, text: str) -> str:
        """清洗文本数据"""
        if not text:
            return ""
        
        # 移除多余空格和换行
        cleaned = re.sub(r'\s+', ' ', text.strip())
        # 移除特殊字符
        cleaned = re.sub(r'[^\w\s\-.,!?()]', '', cleaned)
        return cleaned
    
    def clean_number(self, number_text: str) -> Optional[int]:
        """清洗数字数据"""
        if not number_text:
            return None
        
        # 提取数字
        numbers = re.findall(r'\d+', number_text)
        if numbers:
            return int(''.join(numbers))
        
        return None
```

## 配置文件结构
```yaml
# extraction_config.yaml
selectors:
  price:
    strategy: "css"
    selector: ".price-current"
    fallback:
      - strategy: "xpath"
        selector: "//span[@class='price']"
      - strategy: "regex"
        pattern: '\$(\d+\.\d{2})'
    
  title:
    strategy: "css"
    selector: "h1.product-title"
    fallback:
      - strategy: "json_ld"
        path: "name"
    
  images:
    strategy: "css"
    selector: ".product-images img"
    attribute: "src"
    multiple: true

data_cleaning:
  price:
    currency_symbols: ["$", "€", "£", "¥"]
    decimal_places: 2
    
  text:
    max_length: 1000
    remove_html: true
    normalize_spaces: true
    
  numbers:
    range:
      min: 0
      max: 1000000
```

## 预期产出物
1. **配置文件解析器** (`selector_config_parser.py`)
2. **多选择器验证器** (`multi_selector_validator.py`)
3. **选择器引擎** (`selector_engine.py`)
4. **数据清洗器** (`data_cleaner.py`)
5. **数据验证器** (`data_validator.py`)
6. **结构化数据解析器** (`structured_data_parser.py`)
7. **动态内容处理器** (`dynamic_content_processor.py`)
8. **提取配置管理** (`extraction_config.py`)
9. **MercadoLibre选择器配置** (`mercadolibre_selectors.json`)
10. **提取规则配置** (`extraction_rules.yaml`)
11. **单元测试文件** (`tests/test_data_extraction.py`)
12. **集成测试用例** (`tests/integration/test_extraction.py`)

## 验收标准
- [ ] 数据提取准确率≥95%
- [ ] 支持多种选择器策略
- [ ] 数据清洗效果良好
- [ ] 结构化数据提取正常
- [ ] 动态内容处理有效
- [ ] 数据验证机制完善
- [ ] 通过所有单元测试
- [ ] 性能测试满足要求

## 性能指标
- **提取速度**: 单页面≤5秒
- **准确率**: ≥95%
- **覆盖率**: 支持90%的常见电商网站结构
- **错误处理**: 优雅降级，无崩溃
- **内存使用**: 处理大页面≤100MB

## 预估工时
1天

## 依赖关系
- 前置任务: Task 03-02 (Playwright浏览器自动化)
- 为Task 03-05提供数据提取基础

## 状态
待开始 