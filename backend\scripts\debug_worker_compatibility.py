#!/usr/bin/env python3
"""
调试Worker兼容性检查问题
"""

import asyncio
import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.services.crawler_worker_service import crawler_worker_service

async def debug_worker_compatibility():
    """调试Worker兼容性检查"""
    
    worker_id = "bdfc7391-3c0f-4627-acee-10761740c139"
    
    print(f"🔍 调试Worker兼容性检查...")
    print(f"Worker ID: {worker_id}")
    
    try:
        # 1. 测试get_worker方法
        print(f"\n1. 测试get_worker方法...")
        worker = await crawler_worker_service.get_worker(worker_id)
        
        if worker:
            print(f"✅ Worker存在:")
            print(f"   - ID: {worker.worker_id}")
            print(f"   - 名称: {worker.worker_name}")
            print(f"   - 爬取配置ID: {worker.crawler_config_id}")
            print(f"   - 后端配置ID: {worker.backend_config_id}")
            print(f"   - 状态: {worker.status}")
        else:
            print(f"❌ get_worker返回None")
            return
        
        # 2. 测试配置检查方法
        print(f"\n2. 测试配置检查方法...")
        config_exists, config_active, config_name = await crawler_worker_service._get_crawler_config_info(worker.crawler_config_id)
        
        print(f"   爬取配置检查:")
        print(f"   - 存在: {config_exists}")
        print(f"   - 活跃: {config_active}")
        print(f"   - 名称: {config_name}")
        
        # 3. 测试完整的兼容性检查
        print(f"\n3. 测试完整的兼容性检查...")
        compatibility_check = await crawler_worker_service.check_worker_compatibility(worker_id)
        
        if compatibility_check:
            print(f"✅ 兼容性检查成功:")
            print(f"   - Worker ID: {compatibility_check.worker_id}")
            print(f"   - 兼容: {compatibility_check.is_compatible}")
            print(f"   - 配置存在: {compatibility_check.config_exists}")
            print(f"   - 后端可用: {compatibility_check.backend_available}")
            print(f"   - 资源冲突: {compatibility_check.resource_conflict}")
            print(f"   - 错误: {compatibility_check.errors}")
            print(f"   - 警告: {compatibility_check.warnings}")
        else:
            print(f"❌ check_worker_compatibility返回None")
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await debug_worker_compatibility()
    print(f"\n🏁 调试完成！")

if __name__ == "__main__":
    asyncio.run(main())
