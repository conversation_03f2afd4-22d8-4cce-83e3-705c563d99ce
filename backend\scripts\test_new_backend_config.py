#!/usr/bin/env python3
"""
测试新的后端配置创建
验证新增的配置字段是否正确工作
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.services.backend_config_service import backend_config_service
from app.schemas.backend_config import BackendConfigCreate

async def test_create_enhanced_backend_config():
    """测试创建增强的后端配置"""
    
    config_data = {
        "backend_name": "测试增强后端v2",
        "description": "测试新配置字段的后端配置v2",
        "api_endpoint": "http://test-enhanced-v2:8080",
        "auth_config": {
            "auth_type": "api_key",
            "api_key": "test-enhanced-key"
        },
        "max_concurrent": 10,
        "pool_size": 20,
        "memory_threshold": 1024,
        "mean_delay": 800,
        "max_range": 1500,
        "weight": 8,
        "priority": 1,
        "performance_level": "high",
        "health_check_interval": 45,
        "health_check_timeout": 8000,
        "failure_threshold": 2,
        "recovery_threshold": 3,
        "crawler_request_config": {
            "default_llm_query": "测试提取关键信息",
            "default_llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}, "price": {"type": "number"}}}',
            "cache_strategy": "smart",
            "cache_ttl": 7200,
            "default_priority": "high",
            "priority_boost_factor": 1.5,
            "api_key_prefix": "test_enhanced",
            "default_auth_method": "api_key",
            "user_agent": "MonIt-Enhanced-Test/1.0",
            "user_agent_rotation": True,
            "batch_name_template": "Enhanced_{task_id}_{timestamp}",
            "batch_description_template": "增强测试任务 {task_id} - {url_count} URLs",
            "max_urls_per_batch": 150,
            "request_interval": 600
        },
        "callback_config": {
            "callback_url": "http://backend:8000/api/v1/crawler/callback/enhanced",
            "callback_timeout": 25000,
            "callback_retries": 4,
            "callback_headers": {
                "X-Test-Mode": "enhanced",
                "X-Priority": "high"
            }
        },
        "monitoring_config": {
            "enable_request_logging": True,
            "enable_performance_monitoring": True,
            "enable_error_tracking": True,
            "response_time_threshold": 25000,
            "error_rate_threshold": 0.08,
            "alert_on_failure": True,
            "alert_webhook_url": "http://monitoring:9093/api/v1/alerts/enhanced",
            "log_level": "DEBUG",
            "log_retention_days": 60
        }
    }
    
    try:
        config = BackendConfigCreate(**config_data)
        result = await backend_config_service.create_config(config)
        print(f"✅ 创建增强后端配置成功: {result.backend_name}")
        print(f"   - 配置ID: {result.backend_id}")
        print(f"   - 默认优先级: {result.crawler_request_config.default_priority}")
        print(f"   - 缓存策略: {result.crawler_request_config.cache_strategy}")
        print(f"   - 回调URL: {result.callback_config.callback_url}")
        print(f"   - 自定义User-Agent: {result.crawler_request_config.user_agent}")
        print(f"   - 告警Webhook: {result.monitoring_config.alert_webhook_url}")
        
        # 测试获取配置摘要
        summary = await backend_config_service.list_configs(limit=1)
        if summary:
            latest_summary = summary[0]
            print(f"\n📊 配置摘要验证:")
            print(f"   - 默认优先级: {latest_summary.default_priority}")
            print(f"   - 缓存策略: {latest_summary.cache_strategy}")
            print(f"   - 有回调配置: {latest_summary.has_callback_config}")
            print(f"   - 有自定义User-Agent: {latest_summary.has_custom_user_agent}")
        
        return result
        
    except Exception as e:
        print(f"❌ 创建增强后端配置失败: {e}")
        return None

async def main():
    """主函数"""
    print("🧪 开始测试增强后端配置...")
    
    result = await test_create_enhanced_backend_config()
    
    if result:
        print(f"\n✅ 测试完成！新配置字段工作正常。")
    else:
        print(f"\n❌ 测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
