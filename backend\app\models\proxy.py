"""
代理池和User Agent数据模型
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import String, Integer, Boolean, Text, DateTime, CheckConstraint
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, IDMixin, TimestampMixin


class ProxyPool(Base, IDMixin, TimestampMixin):
    """代理池表"""
    
    __tablename__ = "proxy_pools"
    __table_args__ = (
        CheckConstraint(
            "status IN ('active', 'inactive', 'banned')",
            name="proxy_pools_status_check"
        ),
        CheckConstraint(
            "proxy_type IN ('http', 'https', 'socks4', 'socks5')",
            name="proxy_pools_type_check"
        ),
    )
    
    # 代理信息
    host: Mapped[str] = mapped_column(String(255), nullable=False, comment="代理主机")
    port: Mapped[int] = mapped_column(Integer, nullable=False, comment="代理端口")
    username: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="用户名")
    password: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="密码")
    proxy_type: Mapped[str] = mapped_column(
        String(20),
        default="http",
        comment="代理类型: http, https, socks4, socks5"
    )
    
    # 状态信息
    status: Mapped[str] = mapped_column(
        String(20),
        default="active",
        comment="状态: active, inactive, banned"
    )
    success_count: Mapped[int] = mapped_column(Integer, default=0, comment="成功次数")
    failed_count: Mapped[int] = mapped_column(Integer, default=0, comment="失败次数")
    
    # 性能信息
    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后使用时间"
    )
    last_checked_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后检查时间"
    )
    response_time_ms: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="响应时间(毫秒)"
    )
    
    def __repr__(self) -> str:
        return f"<ProxyPool(id={self.id}, host='{self.host}', port={self.port}, status='{self.status}')>"
    
    @property
    def proxy_url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"{self.proxy_type}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.proxy_type}://{self.host}:{self.port}"
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.failed_count
        if total == 0:
            return 0.0
        return (self.success_count / total) * 100
    
    @property
    def is_available(self) -> bool:
        """是否可用"""
        return self.status == "active"
    
    @property  
    def is_fast(self) -> bool:
        """是否为快速代理(响应时间<3秒)"""
        return self.response_time_ms is not None and self.response_time_ms < 3000
    
    def mark_success(self, response_time_ms: int):
        """标记成功使用"""
        self.success_count += 1
        self.response_time_ms = response_time_ms
        self.last_used_at = datetime.utcnow()
        if self.status == "inactive":
            self.status = "active"
    
    def mark_failed(self):
        """标记失败"""
        self.failed_count += 1
        self.last_used_at = datetime.utcnow()
        
        # 连续失败过多时标记为不可用
        if self.failed_count > self.success_count * 2 and self.failed_count >= 5:
            self.status = "banned"


class UserAgent(Base, IDMixin, TimestampMixin):
    """User Agent池表"""
    
    __tablename__ = "user_agents"
    
    # User Agent信息
    user_agent: Mapped[str] = mapped_column(Text, nullable=False, unique=True, comment="User Agent字符串")
    browser: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="浏览器类型")
    version: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="版本号")
    platform: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="平台类型")
    
    # 特性
    mobile: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否移动端")
    weight: Mapped[int] = mapped_column(Integer, default=1, comment="权重(使用频率)")
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    def __repr__(self) -> str:
        ua_preview = self.user_agent[:50] + "..." if len(self.user_agent) > 50 else self.user_agent
        return f"<UserAgent(id={self.id}, browser='{self.browser}', user_agent='{ua_preview}')>"
    
    @property
    def is_chrome(self) -> bool:
        """是否为Chrome浏览器"""
        return "Chrome" in self.user_agent and "Chromium" not in self.user_agent
    
    @property
    def is_firefox(self) -> bool:
        """是否为Firefox浏览器"""
        return "Firefox" in self.user_agent
    
    @property
    def is_safari(self) -> bool:
        """是否为Safari浏览器"""
        return "Safari" in self.user_agent and "Chrome" not in self.user_agent
    
    @property
    def is_modern(self) -> bool:
        """是否为现代浏览器(Chrome > 100, Firefox > 90)"""
        if self.is_chrome:
            try:
                version = self.user_agent.split("Chrome/")[1].split()[0]
                major_version = int(version.split(".")[0])
                return major_version >= 100
            except (IndexError, ValueError):
                return False
        elif self.is_firefox:
            try:
                version = self.user_agent.split("Firefox/")[1].split()[0]
                major_version = int(version.split(".")[0])
                return major_version >= 90
            except (IndexError, ValueError):
                return False
        return False
    
    @classmethod
    def get_random_desktop_ua(cls) -> str:
        """获取随机桌面User Agent"""
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    @classmethod
    def get_random_mobile_ua(cls) -> str:
        """获取随机移动端User Agent"""
        return "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1" 