import React, { useState, useEffect, useCallback } from 'react';
import {
  Form,
  Radio,
  DatePicker,
  TimePicker,
  InputNumber,
  Input,
  Switch,
  Button,
  Space,
  Card,
  Row,
  Col,
  Typography,
  Alert,
  Tooltip
} from 'antd';
import {
  ClockCircleOutlined,
  CalendarOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

import type { ScheduleConfig, ScheduleType } from '../../types/taskCreate';
import { SCHEDULE_TYPE_OPTIONS } from '../../types/taskCreate';
import {
  convertTimeForBackend,
  convertTimeForFrontend,
  shouldUseTimePicker,
  shouldUseDatePicker,
  getTimeFieldValue
} from '../../utils/timeUtils';

const { Title, Text } = Typography;

interface ScheduleConfigStepProps {
  scheduleConfig: ScheduleConfig;
  onConfigChange: (config: ScheduleConfig) => void;
}

const ScheduleConfigStep: React.FC<ScheduleConfigStepProps> = ({
  scheduleConfig,
  onConfigChange
}) => {
  const [form] = Form.useForm();
  const [scheduleType, setScheduleType] = useState<ScheduleType>(scheduleConfig.type);

  // 同步外部配置变化到表单
  useEffect(() => {
    // 使用统一的时间字段处理函数
    const timeValue = getTimeFieldValue(scheduleConfig);
    const startTime = convertTimeForFrontend(timeValue, scheduleConfig.type);

    // 处理end_time的解析
    let endTime: any = undefined;
    if (scheduleConfig.end_time && scheduleConfig.end_time !== 'Invalid Date') {
      try {
        if (scheduleConfig.end_time.match(/^\d{2}:\d{2}$/)) {
          // HH:mm格式，创建今天的时间
          const [hours, minutes] = scheduleConfig.end_time.split(':');
          const parsedHours = parseInt(hours, 10);
          const parsedMinutes = parseInt(minutes, 10);
          if (parsedHours >= 0 && parsedHours <= 23 && parsedMinutes >= 0 && parsedMinutes <= 59) {
            endTime = dayjs().hour(parsedHours).minute(parsedMinutes).second(0).millisecond(0);
          }
        } else {
          // 其他情况按ISO字符串解析
          const parsed = dayjs(scheduleConfig.end_time);
          if (parsed.isValid()) {
            endTime = parsed;
          }
        }
      } catch (error) {
        console.warn('Failed to parse end_time:', scheduleConfig.end_time, error);
      }
    }

    const formValues = {
      type: scheduleConfig.type,
      enabled: scheduleConfig.enabled,
      timezone: scheduleConfig.timezone,
      start_time: startTime,
      end_time: endTime,
      interval: scheduleConfig.interval,
      cron_expression: scheduleConfig.cron_expression,
      max_runs: scheduleConfig.max_runs,
      enable_random_delay: scheduleConfig.enable_random_delay,
      random_delay_min: scheduleConfig.random_delay_min,
      random_delay_max: scheduleConfig.random_delay_max
    };

    form.setFieldsValue(formValues);
    setScheduleType(scheduleConfig.type);
  }, [scheduleConfig, form]);



  // 处理表单值变化，实时更新配置（使用防抖避免过于频繁的更新）
  const handleFormChange = useCallback(() => {
    // 获取当前表单值，不进行验证
    const values = form.getFieldsValue();

    // 使用统一的时间处理函数
    const startTime = convertTimeForBackend(values.start_time, values.type || scheduleConfig.type);
    const endTime = convertTimeForBackend(values.end_time, 'daily'); // 结束时间统一使用daily格式

    const updatedConfig: ScheduleConfig = {
      type: values.type || scheduleConfig.type,
      enabled: values.enabled !== undefined ? values.enabled : scheduleConfig.enabled,
      timezone: values.timezone || 'Asia/Shanghai',
      time: values.type === 'daily' ? startTime : undefined,  // daily类型使用time字段
      start_time: values.type !== 'daily' ? startTime : undefined,  // 其他类型使用start_time字段
      end_time: endTime || undefined,  // 确保空字符串转为undefined
      interval: values.interval || undefined,
      cron_expression: values.cron_expression || undefined,
      max_runs: values.max_runs || undefined,
      enable_random_delay: values.enable_random_delay !== undefined ? values.enable_random_delay : scheduleConfig.enable_random_delay,
      random_delay_min: values.random_delay_min !== undefined ? values.random_delay_min : scheduleConfig.random_delay_min,
      random_delay_max: values.random_delay_max !== undefined ? values.random_delay_max : scheduleConfig.random_delay_max
    };

    // 调试信息
    console.log('ScheduleConfigStep - handleFormChange:', {
      formValues: values,
      updatedConfig,
      currentScheduleConfig: scheduleConfig
    });

    onConfigChange(updatedConfig);
  }, [form, scheduleConfig, onConfigChange]);

  // 渲染调度类型选项
  const renderScheduleTypeOptions = () => (
    <Radio.Group
      value={scheduleType}
      onChange={(e) => setScheduleType(e.target.value)}
      style={{ width: '100%' }}
    >
      <Row gutter={[16, 16]}>
        {SCHEDULE_TYPE_OPTIONS.map(option => (
          <Col span={12} key={option.value}>
            <Card
              size="small"
              hoverable
              style={{
                border: scheduleType === option.value ? '2px solid #1890ff' : '1px solid #d9d9d9'
              }}
            >
              <Radio value={option.value} style={{ width: '100%' }}>
                <Space direction="vertical" size={4}>
                  <Space>
                    <span style={{ fontSize: 16 }}>{option.icon}</span>
                    <Text strong>{option.label}</Text>
                  </Space>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {option.description}
                  </Text>
                </Space>
              </Radio>
            </Card>
          </Col>
        ))}
      </Row>
    </Radio.Group>
  );

  // 渲染调度参数配置
  const renderScheduleParams = () => {
    switch (scheduleType) {
      case 'once':
        return (
          <Form.Item
            name="start_time"
            label="执行时间"
            rules={[{ required: true, message: '请选择执行时间' }]}
          >
            <DatePicker
              showTime
              placeholder="请选择执行时间"
              style={{ width: '100%' }}
              disabledDate={(current) => current && current < dayjs().startOf('day')}
            />
          </Form.Item>
        );

      case 'daily':
        return (
          <Form.Item
            name="start_time"
            label="每日执行时间"
            rules={[{ required: true, message: '请选择执行时间' }]}
          >
            <TimePicker
              format="HH:mm"
              placeholder="请选择执行时间"
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'weekly':
        return (
          <Form.Item
            name="start_time"
            label="每周执行时间"
            rules={[{ required: true, message: '请选择执行时间' }]}
          >
            <TimePicker
              format="HH:mm"
              placeholder="请选择执行时间"
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'hourly':
        return (
          <Form.Item
            name="interval"
            label={
              <Space>
                执行间隔(分钟)
                <Tooltip title="每隔多少分钟执行一次">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            rules={[
              { required: true, message: '请输入执行间隔' },
              { type: 'number', min: 1, max: 1440, message: '间隔时间必须在1-1440分钟之间' }
            ]}
          >
            <InputNumber
              min={1}
              max={1440}
              placeholder="请输入间隔时间"
              style={{ width: '100%' }}
              addonAfter="分钟"
            />
          </Form.Item>
        );

      case 'custom':
        return (
          <Form.Item
            name="cron_expression"
            label={
              <Space>
                Cron表达式
                <Tooltip title="使用标准的Cron表达式格式，如：0 9 * * * 表示每天9点执行">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            rules={[{ required: true, message: '请输入Cron表达式' }]}
          >
            <Input
              placeholder="例如：0 9 * * * (每天9点执行)"
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <Title level={4}>调度配置</Title>
      <Text type="secondary">设置任务的执行时间和频率</Text>

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        initialValues={{
          type: scheduleConfig.type,
          enabled: scheduleConfig.enabled,
          timezone: scheduleConfig.timezone,
          start_time: (() => {
            const timeValue = scheduleConfig.time || scheduleConfig.start_time;
            if (!timeValue) return undefined;
            if (scheduleConfig.type === 'daily' && timeValue.match(/^\d{2}:\d{2}$/)) {
              const [hours, minutes] = timeValue.split(':');
              return dayjs().hour(parseInt(hours, 10)).minute(parseInt(minutes, 10)).second(0).millisecond(0);
            }
            return dayjs(timeValue);
          })(),
          end_time: (() => {
            if (!scheduleConfig.end_time || scheduleConfig.end_time === 'Invalid Date') return undefined;
            try {
              if (scheduleConfig.end_time.match(/^\d{2}:\d{2}$/)) {
                const [hours, minutes] = scheduleConfig.end_time.split(':');
                const parsedHours = parseInt(hours, 10);
                const parsedMinutes = parseInt(minutes, 10);
                if (parsedHours >= 0 && parsedHours <= 23 && parsedMinutes >= 0 && parsedMinutes <= 59) {
                  return dayjs().hour(parsedHours).minute(parsedMinutes).second(0).millisecond(0);
                }
              } else {
                const parsed = dayjs(scheduleConfig.end_time);
                if (parsed.isValid()) {
                  return parsed;
                }
              }
            } catch (error) {
              console.warn('Failed to parse end_time for form:', scheduleConfig.end_time, error);
            }
            return undefined;
          })(),
          interval: scheduleConfig.interval,
          cron_expression: scheduleConfig.cron_expression,
          max_runs: scheduleConfig.max_runs,
          enable_random_delay: scheduleConfig.enable_random_delay,
          random_delay_min: scheduleConfig.random_delay_min,
          random_delay_max: scheduleConfig.random_delay_max
        }}
        style={{ marginTop: 24 }}
      >
        {/* 启用调度 */}
        <Card title="调度设置" size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="enabled"
            label="启用调度"
            valuePropName="checked"
            tooltip="关闭后任务将不会自动执行"
          >
            <Switch />
          </Form.Item>

          {/* 调度类型选择 */}
          <Form.Item
            name="type"
            label="调度类型"
            rules={[{ required: true, message: '请选择调度类型' }]}
          >
            {renderScheduleTypeOptions()}
          </Form.Item>

          {/* 调度参数 */}
          {renderScheduleParams()}
        </Card>

        {/* 高级选项 */}
        <Card title="高级选项" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="end_time"
                label="结束时间"
                tooltip="每日任务的最晚执行时间，超过此时间将不再执行"
              >
                <TimePicker
                  format="HH:mm"
                  placeholder="请选择结束时间（可选）"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_runs"
                label="最大执行次数"
                tooltip="任务的最大执行次数，达到后将自动停止"
              >
                <InputNumber
                  min={1}
                  placeholder="请输入最大执行次数（可选）"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="timezone"
            label="时区"
          >
            <Input disabled />
          </Form.Item>
        </Card>

        {/* 反爬虫配置 */}
        <Card title="反爬虫配置" size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="enable_random_delay"
            label="启用随机启动延迟"
            valuePropName="checked"
            tooltip="在设定时间基础上增加随机延迟，避免被检测为机器人行为"
          >
            <Switch />
          </Form.Item>

          <Form.Item dependencies={['enable_random_delay']}>
            {({ getFieldValue }) => {
              const enableRandomDelay = getFieldValue('enable_random_delay');
              return enableRandomDelay ? (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="random_delay_min"
                      label="最小延迟时间"
                      rules={[
                        { required: true, message: '请输入最小延迟时间' },
                        { type: 'number', min: 0, max: 1440, message: '延迟时间必须在0-1440分钟之间' }
                      ]}
                    >
                      <InputNumber
                        min={0}
                        max={1440}
                        placeholder="0"
                        style={{ width: '100%' }}
                        addonAfter="分钟"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="random_delay_max"
                      label="最大延迟时间"
                      rules={[
                        { required: true, message: '请输入最大延迟时间' },
                        { type: 'number', min: 1, max: 1440, message: '延迟时间必须在1-1440分钟之间' },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const minDelay = getFieldValue('random_delay_min');
                            if (value && minDelay && value <= minDelay) {
                              return Promise.reject(new Error('最大延迟时间必须大于最小延迟时间'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <InputNumber
                        min={1}
                        max={1440}
                        placeholder="180"
                        style={{ width: '100%' }}
                        addonAfter="分钟"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ) : null;
            }}
          </Form.Item>

          {/* 随机延迟说明 */}
          <Form.Item dependencies={['enable_random_delay']}>
            {({ getFieldValue }) => {
              const enableRandomDelay = getFieldValue('enable_random_delay');
              return enableRandomDelay ? (
                <Alert
                  message="随机延迟说明"
                  description="系统将在设定的执行时间基础上，随机增加指定范围内的延迟时间。例如：设定每日9:00执行，随机延迟0-180分钟，实际执行时间将在9:00-12:00之间随机选择。"
                  type="info"
                  showIcon
                  style={{ marginTop: 8 }}
                />
              ) : null;
            }}
          </Form.Item>
        </Card>

        {/* 调度预览 */}
        <Form.Item dependencies={['enable_random_delay', 'random_delay_min', 'random_delay_max']}>
          {({ getFieldValue }) => {
            const enableRandomDelay = getFieldValue('enable_random_delay');
            const randomDelayMin = getFieldValue('random_delay_min') || 0;
            const randomDelayMax = getFieldValue('random_delay_max') || 180;

            return (
              <Alert
                message="调度预览"
                description={
                  <div>
                    <p>根据当前配置，任务将按以下规则执行：</p>
                    <ul>
                      <li>调度类型: {SCHEDULE_TYPE_OPTIONS.find(opt => opt.value === scheduleType)?.label}</li>
                      <li>状态: {scheduleConfig.enabled ? '启用' : '禁用'}</li>
                      <li>时区: Asia/Shanghai</li>
                      {enableRandomDelay && (
                        <li>
                          随机延迟: {randomDelayMin}-{randomDelayMax}分钟
                          {randomDelayMax > 60 && (
                            <span style={{ color: '#666', marginLeft: 8 }}>
                              (约{Math.floor(randomDelayMin/60)}-{Math.floor(randomDelayMax/60)}小时)
                            </span>
                          )}
                        </li>
                      )}
                    </ul>
                  </div>
                }
                type="info"
                showIcon
                icon={<ClockCircleOutlined />}
                style={{ marginBottom: 24 }}
              />
            );
          }}
        </Form.Item>
      </Form>
    </div>
  );
};

export default ScheduleConfigStep;
