"""
爬取规则数据模型
"""

from typing import Optional
from sqlalchemy import String, Integer, ForeignKey, Boolean, Text, CheckConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, IDMixin, TimestampMixin


class ScrapingRule(Base, IDMixin, TimestampMixin):
    """爬取规则表 (细粒度规则管理)"""
    
    __tablename__ = "scraping_rules"
    __table_args__ = (
        CheckConstraint(
            "data_type IN ('text', 'number', 'decimal', 'boolean', 'url', 'json')",
            name="scraping_rules_data_type_check"
        ),
    )
    
    # 关联配置
    config_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("crawl_configs.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联配置ID"
    )
    
    # 字段配置
    field_name: Mapped[str] = mapped_column(String(100), nullable=False, comment="字段名称")
    selector: Mapped[str] = mapped_column(String(500), nullable=False, comment="CSS选择器")
    attribute: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="提取属性")
    regex_pattern: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="正则表达式")
    
    # 数据处理
    data_type: Mapped[str] = mapped_column(
        String(50),
        default="text",
        comment="数据类型: text, number, decimal, boolean, url, json"
    )
    required: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否必填")
    default_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="默认值")
    
    # 状态和优先级
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    priority: Mapped[int] = mapped_column(Integer, default=1, comment="优先级")
    
    # 关系
    config: Mapped["CrawlConfig"] = relationship(
        "CrawlConfig",
        back_populates="rules"
    )
    
    def __repr__(self) -> str:
        return f"<ScrapingRule(id={self.id}, field_name='{self.field_name}', selector='{self.selector[:50]}...')>"
    
    @property
    def is_attribute_extraction(self) -> bool:
        """是否为属性提取"""
        return self.attribute is not None
    
    @property
    def has_regex_processing(self) -> bool:
        """是否有正则处理"""
        return self.regex_pattern is not None
    
    @property
    def is_numeric_type(self) -> bool:
        """是否为数值类型"""
        return self.data_type in ("number", "decimal")
    
    def validate_extracted_value(self, value: str) -> bool:
        """验证提取的值是否符合数据类型要求"""
        if not value and self.required:
            return False
        
        if not value:
            return True  # 非必填字段可以为空
        
        try:
            if self.data_type == "number":
                int(value)
            elif self.data_type == "decimal":
                float(value)
            elif self.data_type == "boolean":
                value.lower() in ("true", "false", "1", "0", "yes", "no")
            elif self.data_type == "url":
                # 简单URL验证
                return value.startswith(("http://", "https://"))
            return True
        except (ValueError, AttributeError):
            return False 