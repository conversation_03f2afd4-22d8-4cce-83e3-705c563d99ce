#!/usr/bin/env python3
"""
测试任务管理器与Docker Redis的集成

验证任务管理器能否正常使用Docker中的Redis服务
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_task_manager_components():
    """测试任务管理器组件"""
    
    logger.info("🔧 Testing Task Manager Components with Docker Redis")
    logger.info("=" * 60)
    
    try:
        # 导入任务管理器组件
        from backend.app.core.task_splitter import TaskSplitter, SplitterConfig, TaskPriority
        from backend.app.core.rate_limiter import RateLimiter, RateLimitConfig
        from backend.app.core.retry_manager import RetryManager, RetryConfig
        
        logger.info("✅ Successfully imported task manager components")
        
        # 测试任务分片器
        logger.info("\n📊 Testing Task Splitter...")
        splitter_config = SplitterConfig(
            max_batch_size=20,
            min_batch_size=5,
            adaptive_sizing=True
        )
        
        splitter = TaskSplitter(splitter_config)
        
        # 创建测试URL
        test_urls = [f"https://example.com/product/{i}" for i in range(50)]
        
        # 分片测试
        batches = splitter.split_urls(
            urls=test_urls,
            platform="mercadolibre",
            priority=TaskPriority.NORMAL
        )
        
        logger.info(f"✅ Split {len(test_urls)} URLs into {len(batches)} batches")
        for i, batch in enumerate(batches):
            logger.info(f"   Batch {i+1}: {len(batch.urls)} URLs, ID: {batch.batch_id}")
        
        # 测试限流器
        logger.info("\n🚦 Testing Rate Limiter with Docker Redis...")
        rate_limiter_config = RateLimitConfig(
            max_concurrent_requests=2,
            requests_per_minute=60,
            redis_url="redis://localhost:6379/0"
        )
        
        rate_limiter = RateLimiter(rate_limiter_config)
        
        try:
            await rate_limiter.start()
            logger.info("✅ Rate limiter connected to Docker Redis")
            
            # 测试并发限制
            req1 = await rate_limiter.acquire("test_req_1")
            req2 = await rate_limiter.acquire("test_req_2")
            req3 = await rate_limiter.acquire("test_req_3")  # 应该失败
            
            logger.info(f"   Concurrent test: req1={req1}, req2={req2}, req3={req3}")
            
            if req1 and req2 and not req3:
                logger.info("✅ Rate limiter working correctly with Docker Redis")
            else:
                logger.warning("⚠️ Rate limiter behavior unexpected")
            
            # 释放资源
            if req1:
                await rate_limiter.release("test_req_1", True, 1.0)
            if req2:
                await rate_limiter.release("test_req_2", True, 1.0)
            
            await rate_limiter.stop()
            
        except Exception as e:
            logger.error(f"❌ Rate limiter test failed: {e}")
            return False
        
        # 测试重试管理器
        logger.info("\n🔄 Testing Retry Manager with Docker Redis...")
        retry_config = RetryConfig(
            max_retries=3,
            base_delay=5.0,
            max_delay=60.0
        )
        
        retry_manager = RetryManager(retry_config)
        
        try:
            await retry_manager.start("redis://localhost:6379/0")
            logger.info("✅ Retry manager connected to Docker Redis")
            
            # 模拟失败任务
            test_error = ConnectionError("Connection timeout")
            will_retry = await retry_manager.handle_batch_failure(
                batch_id="test_batch_001",
                task_id=1,
                urls=["https://example.com/test"],
                error=test_error,
                priority=TaskPriority.NORMAL
            )
            
            logger.info(f"   Retry test: will_retry={will_retry}")
            
            if will_retry:
                logger.info("✅ Retry manager working correctly with Docker Redis")
            else:
                logger.warning("⚠️ Retry manager behavior unexpected")
            
            await retry_manager.stop()
            
        except Exception as e:
            logger.error(f"❌ Retry manager test failed: {e}")
            return False
        
        logger.info("\n🎉 All task manager components working with Docker Redis!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Component test failed: {e}")
        return False


async def test_task_submission_simulation():
    """模拟任务提交流程"""
    
    logger.info("\n📤 Testing Task Submission Simulation")
    logger.info("=" * 60)
    
    try:
        from backend.app.core.task_splitter import TaskSplitter, SplitterConfig, TaskPriority
        from backend.app.utils.excel_processor import ExcelProcessor
        
        # 读取真实Excel数据
        excel_processor = ExcelProcessor()
        excel_path = "LinksToMonit/all_task_item.xlsx"
        
        if not Path(excel_path).exists():
            logger.warning(f"Excel file not found: {excel_path}")
            return False
        
        products, stats = excel_processor.read_product_links(excel_path)
        logger.info(f"✅ Loaded {len(products)} products from Excel")
        
        # 取前100个URL进行测试
        test_urls = [product['product_url'] for product in products[:100]]
        
        # 配置任务分片器
        splitter_config = SplitterConfig(
            max_batch_size=20,
            min_batch_size=5,
            adaptive_sizing=True
        )
        
        splitter = TaskSplitter(splitter_config)
        
        # 分片任务
        batches = splitter.split_urls(
            urls=test_urls,
            platform="mercadolibre",
            priority=TaskPriority.NORMAL,
            metadata={
                "task_id": 1,
                "submission_id": "test_submission_001",
                "options": {"timeout": 30}
            }
        )
        
        logger.info(f"✅ Created {len(batches)} batches for task submission")
        
        # 模拟任务提交到Redis队列
        import redis.asyncio as redis
        
        r = redis.from_url("redis://localhost:6379/0")
        
        # 清理测试队列
        await r.delete("test_task_queue")
        
        # 提交任务到队列
        for i, batch in enumerate(batches):
            task_data = {
                "batch_id": batch.batch_id,
                "urls": batch.urls,
                "priority": batch.priority.value,
                "platform": batch.platform,
                "metadata": batch.metadata,
                "created_at": batch.created_at.isoformat(),
                "estimated_duration": batch.estimated_duration
            }
            
            await r.lpush("test_task_queue", str(task_data))
            logger.info(f"   Submitted batch {i+1}: {batch.batch_id}")
        
        # 检查队列状态
        queue_length = await r.llen("test_task_queue")
        logger.info(f"✅ Queue contains {queue_length} tasks")
        
        # 模拟任务处理
        logger.info("\n⚙️ Simulating task processing...")
        
        processed_count = 0
        while queue_length > 0 and processed_count < 3:  # 只处理前3个任务
            task_data = await r.rpop("test_task_queue")
            if task_data:
                # 模拟处理时间
                await asyncio.sleep(0.1)
                processed_count += 1
                logger.info(f"   Processed task {processed_count}: {task_data.decode()[:50]}...")
            queue_length = await r.llen("test_task_queue")
        
        logger.info(f"✅ Processed {processed_count} tasks, {queue_length} remaining in queue")
        
        # 清理测试数据
        await r.delete("test_task_queue")
        await r.aclose()
        
        logger.info("🎉 Task submission simulation completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Task submission simulation failed: {e}")
        return False


async def main():
    """主函数"""
    
    print("🧪 MonIt Task Manager & Docker Redis Integration Test")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试任务管理器组件
    if await test_task_manager_components():
        success_count += 1
    
    # 测试任务提交模拟
    if await test_task_submission_simulation():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 Integration Test Summary")
    print("=" * 60)
    print(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 Task Manager fully compatible with Docker Redis!")
        print("\n💡 Ready for Production:")
        print("   ✅ Task splitting and batching")
        print("   ✅ Rate limiting with Redis")
        print("   ✅ Retry management with Redis")
        print("   ✅ Task queue operations")
        print("\n🚀 Next Steps:")
        print("   1. Start full Task Manager: python backend/scripts/start_task_manager.py")
        print("   2. Start Celery Worker: celery -A app.celery_app worker")
        print("   3. Run real scenario test: python backend/scripts/run_real_test.py")
    else:
        print("❌ Some integration tests failed. Please check configuration.")
    
    return success_count == total_tests


if __name__ == "__main__":
    asyncio.run(main())
