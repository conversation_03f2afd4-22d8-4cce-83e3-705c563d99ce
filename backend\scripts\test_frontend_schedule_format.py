#!/usr/bin/env python3
"""
测试前端可能发送的调度格式

模拟前端可能发送的各种数据格式，找出导致422错误的原因
"""

import requests
import json

def test_schedule_formats():
    """测试各种调度格式"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试前端可能的调度格式")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "测试1: 标准daily格式",
            "data": {
                "name": "测试任务1",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai"
                }
            }
        },
        {
            "name": "测试2: 包含null值的格式",
            "data": {
                "name": "测试任务2",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "start_time": None,
                    "end_time": None,
                    "interval": None,
                    "cron_expression": None,
                    "timezone": "Asia/Shanghai",
                    "max_runs": None,
                    "enable_random_delay": False,
                    "random_delay_min": None,
                    "random_delay_max": None
                }
            }
        },
        {
            "name": "测试3: 包含undefined的格式（用null模拟）",
            "data": {
                "name": "测试任务3",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai",
                    "days": None
                }
            }
        },
        {
            "name": "测试4: 前端可能的完整格式",
            "data": {
                "name": "测试任务4",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "start_time": None,
                    "days": None,
                    "interval": None,
                    "cron_expression": None,
                    "timezone": "Asia/Shanghai",
                    "end_time": None,
                    "max_runs": None,
                    "enable_random_delay": False,
                    "random_delay_min": 0,
                    "random_delay_max": 180
                }
            }
        },
        {
            "name": "测试5: 缺少可选字段",
            "data": {
                "name": "测试任务5",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00"
                }
            }
        },
        {
            "name": "测试6: 错误的字段类型",
            "data": {
                "name": "测试任务6",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": "true",  # 字符串而不是布尔值
                    "time": "09:00",
                    "timezone": "Asia/Shanghai"
                }
            }
        },
        {
            "name": "测试7: 空字符串字段",
            "data": {
                "name": "测试任务7",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "start_time": "",
                    "end_time": "",
                    "cron_expression": "",
                    "timezone": "Asia/Shanghai"
                }
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 {test_case['name']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/v1/tasks/validate",
                json=test_case['data'],
                timeout=10
            )
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ 验证成功")
                if not result.get('success'):
                    print(f"  验证结果: {result}")
            else:
                print(f"  ❌ 验证失败")
                try:
                    error_data = response.json()
                    print(f"  错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  错误文本: {response.text}")
                    
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    test_schedule_formats()
