import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Tabs,
  Badge,
  Tooltip,
  Divider
} from 'antd';
import {
  BellOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SettingOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { formatRelativeTime } from '../../utils';
import './index.css';

const { Title, Text } = Typography;
const { Option } = Select;

// 类型定义
interface AlertRule {
  name: string;
  component: string;
  metric: string;
  operator: string;
  threshold: number;
  severity: string;
  enabled: boolean;
  description?: string;
  cooldown_minutes?: number;
}

interface ActiveAlert {
  id: string;
  rule_name: string;
  component: string;
  metric: string;
  current_value: number;
  threshold: number;
  severity: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

interface AlertStats {
  total_alerts: number;
  critical_alerts: number;
  warning_alerts: number;
  info_alerts: number;
  error_alerts: number;
}

const AlertManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeAlerts, setActiveAlerts] = useState<ActiveAlert[]>([]);
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  const [alertStats, setAlertStats] = useState<AlertStats>({
    total_alerts: 0,
    critical_alerts: 0,
    warning_alerts: 0,
    info_alerts: 0,
    error_alerts: 0
  });
  const [activeTab, setActiveTab] = useState('alerts');
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null);
  const [form] = Form.useForm();

  // 加载活跃告警
  const loadActiveAlerts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/monitoring/alerts');
      if (response.ok) {
        const data = await response.json();
        setActiveAlerts(data.alerts || []);
        
        // 计算统计信息
        const stats = data.alerts.reduce((acc: AlertStats, alert: ActiveAlert) => {
          acc.total_alerts++;
          switch (alert.severity) {
            case 'critical':
              acc.critical_alerts++;
              break;
            case 'error':
              acc.error_alerts++;
              break;
            case 'warning':
              acc.warning_alerts++;
              break;
            case 'info':
              acc.info_alerts++;
              break;
          }
          return acc;
        }, {
          total_alerts: 0,
          critical_alerts: 0,
          warning_alerts: 0,
          info_alerts: 0,
          error_alerts: 0
        });
        
        setAlertStats(stats);
      }
    } catch (error) {
      console.error('Failed to load active alerts:', error);
      message.error('加载告警信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载告警规则
  const loadAlertRules = async () => {
    try {
      // 这里应该调用获取告警规则的API
      // 目前使用模拟数据
      const mockRules: AlertRule[] = [
        {
          name: 'redis_memory_fragmentation',
          component: 'redis',
          metric: 'mem_fragmentation_ratio',
          operator: 'gt',
          threshold: 2.0,
          severity: 'warning',
          enabled: true,
          description: 'Redis内存碎片率过高',
          cooldown_minutes: 5
        },
        {
          name: 'redis_connected_clients',
          component: 'redis',
          metric: 'connected_clients',
          operator: 'gt',
          threshold: 1000,
          severity: 'warning',
          enabled: true,
          description: 'Redis连接数过多',
          cooldown_minutes: 5
        },
        {
          name: 'taskmanager_pending_queue',
          component: 'taskmanager',
          metric: 'pending_count',
          operator: 'gt',
          threshold: 100,
          severity: 'warning',
          enabled: true,
          description: 'TaskManager待处理队列积压',
          cooldown_minutes: 5
        },
        {
          name: 'taskmanager_failed_queue',
          component: 'taskmanager',
          metric: 'failed_count',
          operator: 'gt',
          threshold: 50,
          severity: 'error',
          enabled: true,
          description: 'TaskManager失败任务过多',
          cooldown_minutes: 5
        }
      ];
      setAlertRules(mockRules);
    } catch (error) {
      console.error('Failed to load alert rules:', error);
      message.error('加载告警规则失败');
    }
  };

  // 确认告警
  const acknowledgeAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/v1/monitoring/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      });
      if (response.ok) {
        message.success('告警已确认');
        loadActiveAlerts();
      }
    } catch (error) {
      console.error('Failed to acknowledge alert:', error);
      message.error('确认告警失败');
    }
  };

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'red';
      case 'error':
        return 'volcano';
      case 'warning':
        return 'orange';
      case 'info':
        return 'blue';
      default:
        return 'default';
    }
  };

  // 获取严重程度图标
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff7a45' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#fa8c16' }} />;
      case 'info':
        return <CheckCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <BellOutlined />;
    }
  };

  // 保存告警规则
  const saveAlertRule = async (values: any) => {
    try {
      const ruleData = {
        ...values,
        enabled: values.enabled ?? true,
        cooldown_minutes: values.cooldown_minutes ?? 5
      };

      if (editingRule) {
        // 更新规则
        message.success('告警规则更新成功');
      } else {
        // 创建新规则
        message.success('告警规则创建成功');
      }

      setRuleModalVisible(false);
      setEditingRule(null);
      form.resetFields();
      loadAlertRules();
    } catch (error) {
      console.error('Failed to save alert rule:', error);
      message.error('保存告警规则失败');
    }
  };

  // 删除告警规则
  const deleteAlertRule = (ruleName: string) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除告警规则 "${ruleName}" 吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          message.success('告警规则删除成功');
          loadAlertRules();
        } catch (error) {
          console.error('Failed to delete alert rule:', error);
          message.error('删除告警规则失败');
        }
      }
    });
  };

  // 切换规则启用状态
  const toggleRuleEnabled = async (ruleName: string, enabled: boolean) => {
    try {
      // 这里应该调用API更新规则状态
      const updatedRules = alertRules.map(rule =>
        rule.name === ruleName ? { ...rule, enabled } : rule
      );
      setAlertRules(updatedRules);
      message.success(`告警规则已${enabled ? '启用' : '禁用'}`);
    } catch (error) {
      console.error('Failed to toggle rule:', error);
      message.error('更新规则状态失败');
    }
  };

  // 告警规则表格列
  const ruleColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name: string, record: AlertRule) => (
        <div>
          <Text strong>{name}</Text>
          {record.description && (
            <div className="alert-rule-description">
              {record.description}
            </div>
          )}
        </div>
      )
    },
    {
      title: '组件',
      dataIndex: 'component',
      key: 'component',
      width: 100,
      render: (component: string) => (
        <Tag color="blue">{component}</Tag>
      )
    },
    {
      title: '指标',
      dataIndex: 'metric',
      key: 'metric',
      width: 150
    },
    {
      title: '条件',
      key: 'condition',
      width: 150,
      render: (_: any, record: AlertRule) => (
        <Text code>
          {record.operator} {record.threshold}
        </Text>
      )
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)}>
          {severity.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 100,
      render: (enabled: boolean, record: AlertRule) => (
        <Switch
          checked={enabled}
          onChange={(checked) => toggleRuleEnabled(record.name, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '冷却时间',
      dataIndex: 'cooldown_minutes',
      key: 'cooldown_minutes',
      width: 100,
      render: (minutes: number) => (
        <Text>{minutes} 分钟</Text>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: AlertRule) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingRule(record);
              form.setFieldsValue(record);
              setRuleModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => deleteAlertRule(record.name)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 活跃告警表格列
  const alertColumns = [
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Space>
          {getSeverityIcon(severity)}
          <Tag color={getSeverityColor(severity)}>
            {severity.toUpperCase()}
          </Tag>
        </Space>
      )
    },
    {
      title: '组件',
      dataIndex: 'component',
      key: 'component',
      width: 100,
      render: (component: string) => (
        <Tag color="blue">{component}</Tag>
      )
    },
    {
      title: '指标',
      dataIndex: 'metric',
      key: 'metric',
      width: 150
    },
    {
      title: '当前值',
      dataIndex: 'current_value',
      key: 'current_value',
      width: 100,
      render: (value: number) => (
        <Text strong>{value.toFixed(2)}</Text>
      )
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      width: 100,
      render: (threshold: number) => (
        <Text type="secondary">{threshold}</Text>
      )
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (timestamp: string) => (
        <Tooltip title={new Date(timestamp).toLocaleString()}>
          <Text type="secondary">
            {formatRelativeTime(timestamp)}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: ActiveAlert) => (
        <Space>
          {!record.acknowledged && (
            <Button
              type="link"
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => acknowledgeAlert(record.id)}
            >
              确认
            </Button>
          )}
        </Space>
      )
    }
  ];

  useEffect(() => {
    loadActiveAlerts();
    loadAlertRules();
    
    // 设置定时刷新
    const interval = setInterval(loadActiveAlerts, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="alert-management">
      <div className="alert-header">
        <Title level={2}>
          <BellOutlined style={{ marginRight: 8 }} />
          告警管理
        </Title>
        <Text type="secondary">监控系统告警和规则管理</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总告警数"
              value={alertStats.total_alerts}
              prefix={<BellOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="严重告警"
              value={alertStats.critical_alerts}
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="错误告警"
              value={alertStats.error_alerts}
              prefix={<ExclamationCircleOutlined style={{ color: '#ff7a45' }} />}
              valueStyle={{ color: '#ff7a45' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="警告告警"
              value={alertStats.warning_alerts}
              prefix={<WarningOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'alerts',
              label: (
                <span>
                  <BellOutlined />
                  活跃告警
                  {alertStats.total_alerts > 0 && (
                    <Badge count={alertStats.total_alerts} style={{ marginLeft: 8 }} />
                  )}
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
                    <Space>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={loadActiveAlerts}
                        loading={loading}
                      >
                        刷新
                      </Button>
                    </Space>
                  </div>

                  {alertStats.total_alerts === 0 ? (
                    <Alert
                      message="暂无活跃告警"
                      description="系统运行正常，没有触发任何告警规则"
                      type="success"
                      showIcon
                    />
                  ) : (
                    <Table
                      columns={alertColumns}
                      dataSource={activeAlerts}
                      rowKey="id"
                      loading={loading}
                      pagination={{
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条告警`
                      }}
                    />
                  )}
                </div>
              )
            },
            {
              key: 'rules',
              label: (
                <span>
                  <SettingOutlined />
                  告警规则
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
                    <Space>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                          setEditingRule(null);
                          form.resetFields();
                          setRuleModalVisible(true);
                        }}
                      >
                        新建规则
                      </Button>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={loadAlertRules}
                      >
                        刷新
                      </Button>
                    </Space>
                  </div>

                  <Table
                    columns={ruleColumns}
                    dataSource={alertRules}
                    rowKey="name"
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条规则`
                    }}
                  />
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 告警规则编辑模态框 */}
      <Modal
        title={editingRule ? '编辑告警规则' : '新建告警规则'}
        open={ruleModalVisible}
        onCancel={() => {
          setRuleModalVisible(false);
          setEditingRule(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveAlertRule}
          className="alert-rule-form"
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" disabled={!!editingRule} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="component"
                label="组件"
                rules={[{ required: true, message: '请选择组件' }]}
              >
                <Select placeholder="请选择组件">
                  <Option value="redis">Redis</Option>
                  <Option value="taskmanager">TaskManager</Option>
                  <Option value="system">System</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="metric"
                label="指标"
                rules={[{ required: true, message: '请输入指标名称' }]}
              >
                <Input placeholder="请输入指标名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="operator"
                label="操作符"
                rules={[{ required: true, message: '请选择操作符' }]}
              >
                <Select placeholder="请选择操作符">
                  <Option value="gt">大于 (&gt;)</Option>
                  <Option value="gte">大于等于 (&gt;=)</Option>
                  <Option value="lt">小于 (&lt;)</Option>
                  <Option value="lte">小于等于 (&lt;=)</Option>
                  <Option value="eq">等于 (=)</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="threshold"
                label="阈值"
                rules={[{ required: true, message: '请输入阈值' }]}
              >
                <InputNumber
                  placeholder="请输入阈值"
                  style={{ width: '100%' }}
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="severity"
                label="严重程度"
                rules={[{ required: true, message: '请选择严重程度' }]}
              >
                <Select placeholder="请选择严重程度">
                  <Option value="info">信息</Option>
                  <Option value="warning">警告</Option>
                  <Option value="error">错误</Option>
                  <Option value="critical">严重</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea
              placeholder="请输入规则描述"
              rows={3}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cooldown_minutes"
                label="冷却时间（分钟）"
                initialValue={5}
              >
                <InputNumber
                  placeholder="冷却时间"
                  style={{ width: '100%' }}
                  min={1}
                  max={60}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label="启用状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default AlertManagement;
