{
  "monitoring_tasks:url_tasks:e1fb7c40-da23-4e0f-8fb1-80f399f0b819": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:78071445-fcf6-4a15-9d2b-5d25b52b5901": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.640639+00:00",
      "id": "78071445-fcf6-4a15-9d2b-5d25b52b5901",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 218, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"d5c431a3135f91f7b3492a55a1176ad6\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3359670408-foco-calor-para-reptiles-tortugas-lampara-uva-uvb-50w25w-_JM#polycard_client=search-nordic&position=34&search_layout=grid&type=item&tracking_id=8b47aa2b-1a08-4aaa-ab45-6ce9eb0bbb00&wid=MLM3359670408&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:db35b2ba-318e-4d2f-af2e-7bd96caefda9": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.589988+00:00",
      "id": "db35b2ba-318e-4d2f-af2e-7bd96caefda9",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 115, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"da5d370f5831983f1fc92f59da9399c4\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1985181997-servitoallas-portarrollos-de-papel-para-cocina-bano-toallero-_JM?searchVariation=179324167960#polycard_client=search-nordic&searchVariation=179324167960&position=20&search_layout=grid&type=item&tracking_id=6608db11-aa0c-4667-87c7-5aa721c79e37",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:7d9bdebc-7bb2-4916-ae7e-abeca44992e5": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:bfc53740-e3c7-4c4b-b272-8ff4bfe2429a": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.544288+00:00",
      "id": "bfc53740-e3c7-4c4b-b272-8ff4bfe2429a",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 27, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"99ac3cf158f4a0526bd9998389960607\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2187174633-juego-de-mesa-clasico-conecta-4-para-ninos-_JM#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:142dff07-4f0b-4004-a8ce-e6031dd80556": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T20:10:21.801688+08:00",
      "id": "142dff07-4f0b-4004-a8ce-e6031dd80556",
      "last_check": "",
      "source_file": "task_169b8f75-5a29-41f9-9519-8f4f61bc0753_manual_add",
      "platform": "unknown",
      "success_count": "0",
      "metadata": "{\"url_hash\": \"49ce45dae2e2a37747abbe4e94c07dd2\", \"added_via\": \"api\"}",
      "error_count": "0",
      "url": "https://example.com/test-product-3",
      "check_count": "0"
    }
  },
  "url_pool:items:894258ef-52bf-457f-9619-99efeef09d2c": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.684960+00:00",
      "id": "894258ef-52bf-457f-9619-99efeef09d2c",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 313, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0c122e290c2cf36c0c135a8b11bfc606\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2249313859-dados-juego-gonggi-calamar-2-juegos-regalo-coleccionable-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=7&reco_backend_type=low_level&reco_id=4fda14ac-01b6-4039-8458-d32e1d3e2827",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:5bc3974d-77ab-407f-8b20-799cfb13a2cc": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:ddd62b84-9287-4d06-bd6f-1bf48743a37b": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:acac48d5-2a55-438a-ab87-3d184a1b1de1": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.568246+00:00",
      "id": "acac48d5-2a55-438a-ab87-3d184a1b1de1",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 72, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"fdae306abbc0bc8dd2c0e0f3c0aa8f85\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2163506917-collar-isabelino-de-cono-para-mascotas-plegable-y-ajustable-_JM?searchVariation=181942344608&vip_filters=shipping%3Afulfillment#reviews",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:b256d4dc-37c4-4073-a039-02c86de3569f": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:37412ee5-7329-44aa-b805-facfb0462b04": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.761579+00:00",
      "id": "37412ee5-7329-44aa-b805-facfb0462b04",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 475, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"de64ec39c75dc57fcef320a871d9d9ee\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/kit-de-pistola-de-soldadura-de-plastico-portatil-c200-grapa/p/MLM45334926?pdp_filters=adult_content%3Ayes%7Cseller_id%3A1432217327&searchVariation=MLM45334926&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=MLM45334926&wid=MLM3523556876&position=2&search_layout=stack&type=product&tracking_id=cfb352d0-ebc0-46ca-8626-c9cf982e2c1c&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:8477a250-9ac7-4e92-8ebf-60f8694c5648": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.655077+00:00",
      "id": "8477a250-9ac7-4e92-8ebf-60f8694c5648",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 249, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"9320a9a899435ada90905215c6be17e2\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3619307266-lindo-sacapuntas-para-ninos-con-forma-de-capibara-_JM?searchVariation=183268645184#is_advertising=true&searchVariation=183268645184&backend_model=search-backend&position=12&search_layout=grid&type=pad&tracking_id=0e6d17ec-22b0-466c-af84-29f9b46aa619&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=12&ad_click_id=MWQ4OTljODktYzI2Mi00MWYxLTgwNDYtNGYwMzJkMDczZmNm",
      "check_count": "0"
    }
  },
  "url_pool:items:ff9db766-bdd9-48e7-ae04-cf41fa6838ad": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.782959+00:00",
      "id": "ff9db766-bdd9-48e7-ae04-cf41fa6838ad",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 520, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"22410e81eb48996fed2747e87b6cf5ca\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/jardin-de-animales-solar-ahuyentador-ultrasonico-para-gatoshotel-parque-granja-campo-de-arroz-granja-otros-y-otros-lugares/p/MLM48599096?pdp_filters=seller_id%3A730605346&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=MLM48599096&wid=MLM3704972738&position=2&search_layout=grid&type=product&tracking_id=644c49c6-a7f5-45cd-b97f-8a6096f6a1cf&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:92ee341d-cf1c-4eec-ac61-579715c11396": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.571745+00:00",
      "id": "92ee341d-cf1c-4eec-ac61-579715c11396",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 79, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"7ca848babac2b28cd54617c506ace821\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3440346830-soporte-de-montaje-universal-para-alforjas-de-motocicleta-_JM?searchVariation=182089296578#is_advertising=true&searchVariation=182089296578&position=2&search_layout=stack&type=pad&tracking_id=a6748df6-4d0b-4b4a-8f1f-169dc6cfa927&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=2&ad_click_id=ZTNhMWViNjMtN2E5NS00ODFlLWIyM2EtZTRmNDAzNTgzYzVi",
      "check_count": "0"
    }
  },
  "url_pool:items:741808f5-5592-4f92-b446-fe2752f3f785": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.557138+00:00",
      "id": "741808f5-5592-4f92-b446-fe2752f3f785",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 51, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"a26d2a74a8d8d5af8930c5b7f912104e\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3555663238-funda-impermeable-para-ferula-yeso-para-brazo-para-ducha-_JM#polycard_client=recommendations_vip-pads-up&reco_backend=vip-pads-up-experimental-a_marketplace&reco_model=ranker_entity_v2_retrieval_system_vip_pads_up&reco_client=vip-pads-up&reco_item_pos=1&reco_backend_type=low_level&reco_id=82984390-2289-4d42-99af-ce4f51f65631&wid=MLM3555663238&sid=recos&is_advertising=true&ad_domain=VIPDESKTOP_UP&ad_position=2&ad_click_id=MzJlZWY3NjEtMjQ0OC00MTQ1LWJlYTgtNjYyYTU4ZGQ5ODU2",
      "check_count": "0"
    }
  },
  "url_pool:items:74b61dc4-df30-482c-af56-40ac34182e01": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.578472+00:00",
      "id": "74b61dc4-df30-482c-af56-40ac34182e01",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 93, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"12990e70aafb4e23548677166bdc1e67\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2091558367-lampara-calentadora-de-velas-aromaticas-regulable-con-tempor-_JM?searchVariation=180902551610#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:ae6db036-8cd1-4e1f-9fc3-119cc0b64dc6": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.581610+00:00",
      "id": "ae6db036-8cd1-4e1f-9fc3-119cc0b64dc6",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 99, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"14fe26f7ec38a8a83d2aaa27f3e16894\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1965704957-comedero-plastico-plegable-comedero-elevado-perro-con-tazone-_JM?searchVariation=180308839085#polycard_client=search-nordic&searchVariation=180308839085&position=30&search_layout=grid&type=item&tracking_id=cdd57a7a-4e52-426f-913d-ad844bdc0d61",
      "check_count": "0"
    }
  },
  "url_pool:items:d3412f83-615d-48d2-bcce-bef57e36bedd": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.626234+00:00",
      "id": "d3412f83-615d-48d2-bcce-bef57e36bedd",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 189, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"cf1c79e6fe64a97f26db486714f73a98\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3600475420-2pcs-controles-universales-for-porton-electrico-433mhz-_JM#polycard_client=recommendations_pdp-pads-up&reco_backend=pdp_pads_up_merge_rars_v2_with_default&reco_client=pdp-pads-up&reco_item_pos=14&reco_backend_type=low_level&reco_id=bd5ee9f8-45fc-4cb2-b85d-fe2263c42303&is_advertising=true&ad_domain=PDPDESKTOP_UP&ad_position=15&ad_click_id=NWIwMWQ3NTYtNzFkMC00NWI0LWI5MDEtOWM1MWI1OWJiNjZm",
      "check_count": "0"
    }
  },
  "url_pool:items:fb7f9c23-ff00-4538-a43b-3ba89d3ccaca": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.541980+00:00",
      "id": "fb7f9c23-ff00-4538-a43b-3ba89d3ccaca",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 22, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"1d132d1eb757855aed5337db117c5b1c\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2119409865-pulsera-audio-ritmica-fiesta-neon-led-concierto-10-pzas-_JM?searchVariation=184231811803#polycard_client=search-nordic&searchVariation=184231811803&position=19&search_layout=grid&type=item&tracking_id=e33dcd33-5e7b-4b25-8c0f-a4c121caac33",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:8bf71b6e-eb93-430f-ba97-1b544d2df840": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:86e29d66-5d29-49bf-8e05-b77ed4e5bbcc": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.758875+00:00",
      "id": "86e29d66-5d29-49bf-8e05-b77ed4e5bbcc",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 469, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"e8bafbee8fbc75c87df1e541fb1fd0eb\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2206477045-stickers-etiquetas-vinilo-pizarron-frascos-especiero-refrige-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=f2777a75-295f-4e17-a734-98443c9b807f",
      "check_count": "0"
    }
  },
  "url_pool:all_ids": {
    "type": "set",
    "data": [
      "e833c635-0c93-4fe9-a449-bf62303a6853",
      "672d77c7-7dc8-4c1d-8710-437bd4a6f250",
      "dc13ccf3-9492-479d-b3cd-b4304d07303f",
      "4ab20e64-b3df-43f7-9470-eb3da2634b2f",
      "9f36776c-c551-4de3-ba42-9a87ad921ea3",
      "7f914cf4-e837-44ca-afb8-1d00dc75aa3e",
      "92f2bf3e-a68f-4a3f-b8c3-a70d5eb650f5",
      "6954c748-5393-402c-946e-ada4b90fa6ee",
      "62bcf9b3-7c32-4b47-9f12-31528c6467f7",
      "7e219924-e4ce-40dc-b731-c0410d3d516c",
      "cc9ade3d-af1a-447a-8c59-89b09cae4066",
      "7366b5ba-e2e0-42d8-8ded-8cc8abe62903",
      "bb7b9c9f-ff42-4dc1-a314-18c857a7e6b2",
      "a66f75f4-7fe4-401a-9162-cdebca513f1b",
      "d0973e89-135a-4616-8077-5f4ae3a74cca",
      "9c1fdfe9-6ff1-4576-856c-7cec5f547761",
      "f3c4b350-aaa9-4aab-8718-7caa3a87b172",
      "2a4e82c0-7d1e-4fd8-853a-1f778fc29df3",
      "946a4899-0500-41c9-894e-97bf28f7556d",
      "dd1cec1e-66e0-47e0-b01c-155cd21d67a9",
      "587bb8f9-1aa5-436d-9147-5a76bde6a287",
      "e54b6763-6827-4ee8-bddb-387e14891811",
      "baefd8e7-d2f1-47bf-abc9-ad39b634a1c3",
      "747f5827-15bb-467c-9d99-2f2d584f3d94",
      "94669a74-8a10-4ae5-b71e-a67878e58cc4",
      "f8ca87a3-b281-4bfb-a00a-383c11af844c",
      "d133972a-69bb-4191-b66b-4714b241e775",
      "78da0a7a-280d-4b28-b3da-7e4afe71c3b2",
      "9f8b4fbc-cd5b-4444-ac41-6a20ec08c486",
      "31a7dd40-9cf8-4100-9352-12c5a37b8049",
      "6e8a7df6-037b-4d13-824e-1924e7bdf855",
      "83b6d665-d6ad-44d5-9d9d-328c176960e0",
      "a0fc875e-7e7d-4678-af41-2073f288977b",
      "614cc2a4-70c3-448e-8827-a715aba0feee",
      "3a86beb5-c897-4bb0-8aa4-9c60b2019726",
      "1d534d57-dfeb-422d-950d-76c6dc54d3a2",
      "69f0cdd1-79df-406e-9051-67d80b1c817e",
      "c3b9c5a3-6ba3-4331-b7e2-c0f9bda675f0",
      "5124a881-0d79-459b-805c-9fa63b2e9f45",
      "9f81d14a-8d63-414d-8ddb-8940ea835449",
      "fee13e6f-2529-45eb-9b40-baf1a68f49d2",
      "13e579e9-2882-4a9a-ba0d-cb8b228488d4",
      "c0c184cc-e57f-49b4-9182-897062359fc3",
      "6050121f-9e34-4e27-bbf6-9dde6c437daf",
      "d5200ba0-a406-4332-9cae-8c794cd513a0",
      "eac7d796-f853-44d5-9ee2-8bb9c0d36669",
      "90f89afe-2921-4e63-831a-916ff68cbae2",
      "f3463c5d-f0ce-43ba-9411-ab60062b9e56",
      "3319d7b1-596e-4f13-9fde-b44ee9100a6a",
      "4b7f2c68-2c20-4df8-90c9-2bdacbbdddb1",
      "b2c1c8a9-f064-46cf-8563-98ac9afdd030",
      "85b202cc-f91f-452e-9982-5a6f2605f08d",
      "4bbd9d9f-e158-496f-a50f-d07abb1a42d6",
      "76010da4-3531-44d8-b4f6-23fd620a29b8",
      "27d67ad0-f4df-4b34-bfbd-b226fd36c402",
      "175a0357-0bc6-4f76-b036-d3ffe74f7efc",
      "fc45789c-49a4-4885-8678-c743a3a18125",
      "b4252436-5740-406f-ae78-68b37c9fd72b",
      "2ba3e535-39ff-41d6-85b4-94bad37f8039",
      "0b514166-5712-47ee-9a66-f2d001b0d211",
      "491227af-c24d-4903-a2f7-0ff7fe26d2d6",
      "07b73ebb-e96e-431b-a1f7-bab5e0b670b0",
      "da033aaa-4618-4606-8db2-25587d585095",
      "e35cd4ef-25fa-4359-9ae9-d962bfc584e0",
      "fb7f9c23-ff00-4538-a43b-3ba89d3ccaca",
      "7f86fd85-bdaf-4d1f-a846-758fd58729cb",
      "817df345-6baf-4af9-9342-698994f0f4a2",
      "ddb4c850-75b8-4131-bd8c-db27b174fc62",
      "78071445-fcf6-4a15-9d2b-5d25b52b5901",
      "99d6725c-773e-4446-9455-f2afdd3870d5",
      "67f2dce2-b299-40da-9b31-8e8b1198e275",
      "91722c88-b708-4e0a-8287-99c336d1fc6a",
      "808b5360-371d-4a2f-a9af-fd6b124bb5fb",
      "2184140b-1263-4060-95f3-c4516113f316",
      "396614ca-77b3-4e63-8e68-5aac4c162797",
      "75720eed-a972-400b-82b1-f9e78be247b0",
      "be94cc69-9b22-4d27-bb30-92790c93b091",
      "c2e56158-bb88-4dc8-b908-1f6e32cfe97e",
      "003fd43e-aa38-4caf-abd2-05fd7982738b",
      "c094352a-fdc9-4931-bc13-c7ee0fbeba2d",
      "7a1c129a-673b-4789-bad7-985afe7c6c6b",
      "7f23bdda-644d-4060-b16a-7a2b0a0d25e4",
      "e718534f-e9ec-4afc-8366-2ea16ce314c1",
      "db5ccca2-7490-4edd-83de-c46654a1b49f",
      "150d5b16-4c68-4647-a170-443ce4275af6",
      "e34b84ce-163d-446d-aff4-c5b5411f2133",
      "67cb07d6-e281-4a70-93f5-3179137f8f0d",
      "4deedd1a-352d-4c04-9250-9e79096a7d63",
      "4bfbb5d9-b000-4f0e-aace-39775f648f5a",
      "cfc9d3df-2578-44bd-97ce-1e64033b7436",
      "92d475e7-ca9e-4fc2-83da-b390a35f7d76",
      "2f389a03-1d8f-437f-8d24-f4fd6c0202ca",
      "d51abf9d-7c50-4141-8a46-5809a3203ac3",
      "8dd4e411-6aa0-46c9-af64-d1ea09622044",
      "e48c6232-115e-481f-82d2-5225eab0326a",
      "0358c823-9779-4246-b040-4a8165998e47",
      "2e883a20-6765-4b4f-a662-9ccf12101b57",
      "021f523d-783c-4fbc-a96b-986625c81ad1",
      "cb39ec6c-c5b4-4082-820f-9cf96415fb81",
      "fc54d526-a6c3-48c6-ba99-78ebc147a27e",
      "85fd341d-6dd1-41a5-96a2-429fbb9d1458",
      "bcaa757c-50cb-47e9-8b5c-8d85188c10eb",
      "83a913d0-9c84-4112-989e-4e0bc38fa8ca",
      "2e3722ac-8e78-4887-a6ba-106696cae09d",
      "e1d03d48-1569-49d5-9590-9922371f3654",
      "5abd798a-ac98-4bf0-be8d-3927a8c1bd15",
      "bfc53740-e3c7-4c4b-b272-8ff4bfe2429a",
      "e48fabc0-ed8a-43a7-9d0b-08260b7acaff",
      "b49c8d27-2535-4066-a310-0c27bb9cacd1",
      "23640fa5-2b88-4b48-a87f-9911e09e643c",
      "b5b5a1c4-ed12-4fe7-b68c-72491d2f4bef",
      "49a162c6-bf56-4a77-8e2b-9df98da8703d",
      "2b41ba71-bfcc-43ab-9225-9bcb7bbd0e0e",
      "2bae22bf-dc5d-4690-9ca9-1ead28491117",
      "dc64784f-2b91-4d23-a9c4-0ed97b1a10f2",
      "e7ab2145-12f7-4948-a152-f9b8417fdf11",
      "7c38233f-bee8-4634-b523-66c82ed75418",
      "4578872b-83c8-40bd-b181-d8c598a8862f",
      "593dac49-9fc8-4857-af4e-fab2d7eadf79",
      "95a50c5a-386a-4221-ba59-91dc9c583dce",
      "37e31b0f-2ebb-4b1c-8b20-ff6a610d1b59",
      "c25f0f31-4e0c-4f75-a952-f1ede03d776e",
      "1725793c-0ade-4693-a282-6f1f6713cb6c",
      "a12a878c-5b19-4b79-b238-0e14f158660c",
      "50e72435-a3a4-4e1e-8ae0-0edc2f4f206a",
      "0efe6597-0854-41a8-a84c-9247b74343ac",
      "85c33226-d782-4dd8-bad8-807f97dc48b1",
      "ea39299c-6294-44dc-aa3e-09f64a8fcd34",
      "7d40ab7d-f820-4c23-a0a6-a06ece6919c9",
      "362c3a06-5c79-48a4-92a9-000f5ab018a1",
      "f582343e-377b-413c-a376-14e03e7003e0",
      "2a43aeac-316b-4d0c-a401-80f667f1d7fe",
      "beccf5c5-7bf3-4987-8f3d-eb83abcc77c4",
      "d770c68b-edea-44f3-842a-2d032979efaa",
      "655ad75b-b4dd-44a8-8700-fc46b71bbefc",
      "f8dba5a2-4dae-432a-bbbf-f7de183c8809",
      "b03b6052-3fec-4092-84fc-80d8c042b568",
      "27d0da44-6ebf-4b69-b7ec-14dc11789ad0",
      "cc60737d-4050-46c1-ad07-4d5d77b5df48",
      "dee0c6a6-2da1-4d1d-b8e5-f239bbb54736",
      "c43c7297-a92d-4063-9ed4-37dda9bdbdf4",
      "ec4b624d-5fcf-4ab1-b0d5-e573594590bc",
      "ea2201c4-08ee-402d-a733-715985d1d216",
      "db35b2ba-318e-4d2f-af2e-7bd96caefda9",
      "23c02e6c-ce66-465c-92c8-9caa45ac3acd",
      "563abb7f-a686-406b-9a99-e743bf814462",
      "e13cdb35-b3d9-400b-90e6-50c8594f8f96",
      "32066db5-aca2-4ed5-9d5e-f955de0d0919",
      "2a9bec61-0fa0-41ea-ac02-be4f71825ecf",
      "fda847dc-1a65-4848-9490-e493e2b85f8d",
      "441852f7-4b34-4b98-9fca-e930515128d4",
      "34920b9a-f9fb-445a-9fc6-90f0a36f539d",
      "a41b2f12-a0c5-4f0b-8329-ecb854f6ac59",
      "447812a5-7bd5-4d39-ad62-e179b4967cbd",
      "fd7f93e3-ff43-4593-968a-46df66503ecd",
      "1ef50d6a-71b1-47d1-af07-2488429cf6dc",
      "cc62addf-670b-4967-b8de-0c110cc2e846",
      "656ff6f1-e286-4e12-a1a6-4397b49b238f",
      "48bcbd5a-9f6f-4f8c-a543-b58c060a4100",
      "4a84791d-b7f7-4525-8fe8-d5c184786aa8",
      "37412ee5-7329-44aa-b805-facfb0462b04",
      "d011ffb6-9cc2-4e11-9c0a-da2cfa9c00e5",
      "bed76989-3b5f-4a36-9278-66be1056518b",
      "1c2dd143-fd4d-438e-8136-ce878702ace0",
      "dc8c4819-aae8-458b-99f6-105788f7b753",
      "c6f2e0a1-aae4-4c73-b800-866d44de657b",
      "77c94d25-cbec-4bb4-ae0c-b9accd1d7d32",
      "5bc3974d-77ab-407f-8b20-799cfb13a2cc",
      "b306c0b0-6b7e-4d0b-a6e8-e0ebc6d626bf",
      "4e9d3d17-f40d-44a0-972d-45edd4473260",
      "21ae10a6-c196-4185-9f0e-2e5af7bedc1d",
      "5058c048-4260-43d6-ba17-8c6e7dc7e482",
      "18668208-d8f9-42b6-b920-992d873e3f25",
      "76f87a84-f67f-48e7-a19d-dfb174039b46",
      "86b1d7f6-1fa1-4f3a-a6e2-7e0e3623f227",
      "32c9fe79-d001-4b79-a9c6-b76bac673615",
      "3262a673-920f-4a74-ac37-0b1cd4a7a7c0",
      "df6688cb-0dec-43ca-a9c0-5388ff0b02e7",
      "84e206f4-c0d2-4115-8c20-c9a1a6244e59",
      "8a8ffa9e-1edc-4eeb-8138-183640b579fe",
      "d30ca988-38c3-46f4-b70d-8966e83db91e",
      "fb70f5b8-a6b8-4e3d-8a3e-0ade719ff2fa",
      "4a5643f0-594c-4276-80e3-d33202b61c3e",
      "87e3a2b5-e6c7-4e0c-b178-d6fd67a27e61",
      "18808850-89b6-4803-975a-df16bfc277f9",
      "eed66063-7c2c-452c-9b26-6113bbd29d8c",
      "b256d4dc-37c4-4073-a039-02c86de3569f",
      "bd7b21b4-9110-4a6f-86eb-b280256387d2",
      "b284c2fa-02e2-45e9-9001-2be1b69f4329",
      "494b953d-8586-4665-b4a0-003ae66f9207",
      "53241595-0209-4dde-8d5a-7b4bdba1d327",
      "450ba497-c73a-4eb9-a598-f14de84cb3bb",
      "4ae4f60d-51ac-44d1-a2e3-e806dc5370fb",
      "9c27539a-d518-44a2-bb61-cf8724959e2f",
      "ca52f635-68e8-4123-bdc1-8b23e446971d",
      "a85dcdd2-6449-4b57-8f5c-c437f85920b7",
      "58ba2d6b-918d-4d7b-bfb3-3ffaf76b23b8",
      "e3d1e561-9a0f-4638-bd23-997dedb96f14",
      "8150c675-9fad-4442-904b-0a3e51d82473",
      "b8b60e9d-5101-478a-9ad6-445f19a787a4",
      "8833da2a-5fa3-4a60-94c4-0abd3e0129c5",
      "7500d8c9-ab3c-4918-83a9-d65c77721c5f",
      "b43150e6-0fd9-4872-b5e6-c681a589adb9",
      "ad15455c-9dbb-4363-be0f-958b4c8a08f5",
      "8bbd3487-fd1a-4701-a58f-97e2f97c6d3a",
      "8546a6c8-6111-47dc-9b00-d5024b6093a4",
      "b3ee5359-64c1-40a5-b4e7-43f17556fcd5",
      "b11f1718-63c6-4636-9063-db66e39ac11c",
      "112215ab-5783-48e8-b414-204d73d2b96b",
      "a0ddeb23-4148-4f4e-8610-4b9a9acaaa5b",
      "ada6d385-7a7f-4be6-a699-91663854a6f9",
      "097b5f06-649c-412a-ba74-02611b7ca521",
      "78aa59cb-824f-4f35-8ac7-310b82b487f2",
      "2f5f9ed1-4f9a-4614-8e41-5c026f2e680b",
      "41a1cb5e-c276-4015-883b-54c46218ae51",
      "1fe9f8bc-a3d4-4eac-8dca-018d90d7b2e1",
      "f073574b-e1aa-4cd0-892f-17edd275dbd9",
      "4d5376d7-a468-46f0-b06a-c381ad499e34",
      "f1e27709-e2ab-4861-b221-a16f1837f6ae",
      "2a84f1d3-a846-4762-aea2-d8ae92945129",
      "250db918-993a-46a0-9ae9-84f3402a470b",
      "8477a250-9ac7-4e92-8ebf-60f8694c5648",
      "a6c516f2-4701-4f56-b2f8-617c8823ed6a",
      "99a907a6-f305-457a-9dc8-375f504ee9fd",
      "1fe2c05e-ab41-41d4-a6cb-795215a777b5",
      "0c8508ec-3ff2-4ecd-b156-1900f78b28d0",
      "d3412f83-615d-48d2-bcce-bef57e36bedd",
      "713579a0-0b89-4e1a-bee7-faf3d055de92",
      "80de1e41-0bb8-4d10-8c21-849e75725309",
      "6c380a90-94a1-4774-90e2-9339f35f98db",
      "92ee341d-cf1c-4eec-ac61-579715c11396",
      "fe1dec72-facc-4367-9069-5691a7f2c231",
      "e276b13a-c6b1-4bb8-94aa-3a6645344bcb",
      "2b0af047-c5a4-48b9-91ec-16ff789d57bf",
      "b3a2425a-2d04-4764-9fbc-9d554cc10e42",
      "4fe6af19-72be-4b47-88b7-a5e9c28d6993",
      "14ac291d-162c-4f98-bfcf-6ee2c59965b1",
      "64941fe1-5ea8-413d-bf77-56518c0e7a0c",
      "ae6db036-8cd1-4e1f-9fc3-119cc0b64dc6",
      "a056ba7c-3e6a-4be9-971f-388480113059",
      "76033dcb-af11-4e3c-9086-88b4e73c8221",
      "5f656686-7f35-489b-bfc3-8fa1a725eaa6",
      "970e8c15-4d52-439d-a3ed-8a2941a98c0a",
      "2a7a1908-572a-48cf-b3e2-2447b8657f96",
      "e89a23e3-0eb0-4970-a308-aff11fb86939",
      "93c5df07-c650-4dc6-850a-47b04fc8762c",
      "5d6c1a6a-7b21-4765-b359-288fb8bf2dba",
      "485d9bdb-2208-49f3-a6c7-3c6dba55b155",
      "6425594d-edb6-42b8-9979-a8689a216a15",
      "ea3013ee-9975-4283-b16f-f12515861678",
      "3304ceb2-51c6-4afa-b80e-f3af3f1bb25f",
      "beb4587f-3fcd-4320-abb9-698e4f9af7eb",
      "48a42c61-9ea6-4989-8b7c-5d41f4d347a9",
      "d4986f4b-5629-474f-8a0f-79270de19844",
      "6172b2c1-9295-4748-aec4-032ebeaa74dc",
      "4352328a-72e6-4cc6-837b-92f11c9ab822",
      "43e01a7f-04dd-413c-a215-73d7958fc50a",
      "e1fb7c40-da23-4e0f-8fb1-80f399f0b819",
      "4ac3e4f6-e01e-4f6a-aebc-32e86cae556a",
      "245810d3-47ac-46d1-b274-9199f4eae7ee",
      "c53311ef-32fc-41d6-9f79-7352515045f8",
      "92b89e38-c393-4ba7-9e21-b865c91c4694",
      "ed499675-ed86-4ec1-9317-d208eaa4cc8c",
      "eeef7e56-ef16-4e6b-841d-ece03435d0e3",
      "76f04c9f-9994-44f3-8d37-1985a6bb51f8",
      "74ee89e7-52f4-45af-9bb5-a7a4dec8af5f",
      "1bb39cb3-444b-4a07-9fc3-b9a06ded1e19",
      "887c95f9-8155-4f58-ae6f-48aa35073082",
      "b64a58ff-f2b4-4a64-84d7-6919342d3719",
      "4a99f47e-30ac-4b64-b347-7172857d4b4e",
      "e04ed9ff-d9c3-43c5-8d6c-413d47df6d86",
      "47411789-0322-4677-bbf5-56d3e2795fa0",
      "1dce8086-458d-4bdd-8216-1f72422c551e",
      "b1525cf1-41bd-4003-aa01-81fbd40d2906",
      "80ed09af-fdeb-4fc3-b5f9-e06a6306341e",
      "6c499066-62d3-4938-b5bd-6777598cd51b",
      "b3859bce-bc79-41ac-9ee4-1ce2b60bdc53",
      "c5b2d1a3-dc30-4299-8a1d-4cfe084d4173",
      "53e79342-c8e3-4923-ad51-a7c327e3d23c",
      "ccda614e-6e00-4283-9856-c5fe22b42be9",
      "7d9bdebc-7bb2-4916-ae7e-abeca44992e5",
      "769f0075-fe62-4e40-ab22-556d28766622",
      "d3a0cf80-a058-4eb9-abb8-a978b90514c6",
      "d21561ab-0886-4867-a642-b3c09651667b",
      "8959a23b-079b-4238-97cd-b271b8096f60",
      "379c0f47-ce7c-4fd8-a7d1-719f2184d172",
      "37c9ac1e-4dda-4103-bb32-93645dc08254",
      "6aa101f6-7219-4fc2-a1c1-06464cdb73f8",
      "8c53710b-1779-409a-b64f-c69e5c5e6382",
      "5a626b7d-838d-4773-8dee-46134bb2f815",
      "2f88d18d-29a4-41eb-9ed2-565d3e7d18e0",
      "df67a925-ba6b-4737-bdba-6e6622a33547",
      "91505854-9c5c-4623-b5e2-a0dae89f386b",
      "55de7260-6716-4cc0-9e0d-5a1da99b4c39",
      "d99a2f66-47dd-42d7-8f2a-fbb6c6c73032",
      "1a147a1c-9e2a-4bb7-8c94-8e2ec4f5e6d7",
      "3bb7bcb7-0a95-4558-9510-f652a93bb0e5",
      "b7675573-37da-48ce-9e26-92fd94e73b8d",
      "3bd70939-1fbd-45fe-b690-abaea829890b",
      "d4a46db9-4bce-42ff-9dd1-cb9aee62788e",
      "690b4c42-1d0b-4c47-9cba-6abed88e2c9b",
      "2603397b-4c6c-4503-b41e-3d3eb5c8beb7",
      "1d10e5a4-e461-48b4-a55f-9309ea53d707",
      "0874b960-b46a-4352-b195-cda7ea6ff4ba",
      "b99a711c-aae5-4084-b6d6-051f08c22fc6",
      "30eb62b8-3ebc-4081-a081-35b4badbc234",
      "87e04b31-0d42-469e-bd2f-c342f6b8a8df",
      "f36a5bfc-d71a-46ee-bfa8-9d8b4525afaf",
      "40973603-486b-44a6-9864-b5a28dc3e59f",
      "c2f05330-e0e6-4972-9254-ffcd7182748e",
      "9d1cca60-59c3-46e2-bf7e-105d467f2ef9",
      "266cdacc-5b7e-4a61-b954-c7b7a54a2be8",
      "11b00297-d9c3-45d0-87f5-0e3e3bc497fa",
      "6c0a8f26-0ba0-4a4a-92c2-697ea04cc113",
      "9cead1f4-cf23-4b8a-9827-b0eb469bd39a",
      "566fbb06-def2-4437-b208-c9508612d609",
      "bec0ce6b-acfa-4457-83e0-3711968d4937",
      "de49f594-00a7-437c-9582-f2f1dbf1cae5",
      "de03c240-3663-4d8d-af41-0547fd53b505",
      "521cd3c2-129c-46ed-b357-1f705273beeb",
      "9dcf6980-3315-4a8b-9cec-c82b07109094",
      "186d3279-581d-40e6-9f3c-2be337b967d9",
      "acac48d5-2a55-438a-ab87-3d184a1b1de1",
      "13debc83-447d-4529-a8e9-fd5a3120989e",
      "d98fb04a-3cad-4fd8-ba19-196f588747bd",
      "10adf58a-af02-4b59-9c73-f20d24db5f08",
      "d4c7ec19-8921-454d-a4f7-a69fefafb033",
      "b1d8fa13-198d-4048-ab95-e954e5883b9f",
      "b351aa92-6895-4a37-b35c-fb19698254f2",
      "4d491d36-0f5e-4f8a-86b8-9d2b3f56962a",
      "6686e587-0b5f-40af-94d8-9bd1cfe70bfa",
      "64b7b83f-4a30-4de9-8c75-b2b7d892f747",
      "59c975ae-ab92-40a0-a38e-8ed1dd4e844b",
      "5394e668-e39b-45e7-918d-3d39959fe2f8",
      "a630ea28-5582-4d27-b0b5-262d3b754ee6",
      "bb2d7309-05c2-4fb1-aa36-e5aefeb52a86",
      "b880dc5d-6b4b-4f4a-bbe6-1b5108254e67",
      "37a2df8a-9da5-4d76-a860-b831578275d0",
      "cb84b643-30cc-4d58-ac14-64d87f2626c5",
      "3a43bdbd-b07d-4377-a5db-f6976011226e",
      "dbb71df3-acac-4626-96ce-8aea475f926d",
      "b3abb05c-29c5-474d-bac1-b7e9b91374bb",
      "0ac34e4f-01f4-4f94-882d-e27acb6ca5f5",
      "aa7a2304-3507-4663-af0d-80914cded17d",
      "e77560a1-ba73-4b0a-876b-43e941a768a4",
      "999e2e5c-329a-4dcd-818a-24679f0d6fca",
      "84d2c3b4-1f55-402a-9603-2603fb3471d0",
      "0cb80f5d-e7ae-440c-b19f-b046f5c6db90",
      "b6a1a08a-ad8e-4879-a7c0-c3a66d168eea",
      "906f9445-d1ff-4600-a101-f3c3c72fe902",
      "81645980-b570-4649-a6f2-db38ab0a4135",
      "62c6823a-e5e4-458f-b332-ca0f23c0f3ba",
      "20815335-6862-418c-ae9d-2d8f13704038",
      "27bef139-f150-419a-b656-a98d35f5317c",
      "4b1312e5-5e02-415b-b5c4-9cf1eb439e95",
      "3e2ca227-015a-4919-bbb2-7be5de9af33a",
      "054c7612-fb88-421f-a74e-a38babb9d0d5",
      "6c62724a-144e-4bd6-ba40-d35b5a0a2fdb",
      "0c1e60e1-d226-4421-887d-d1a0cea38688",
      "926a5f46-2725-4f96-b6c9-adf6d67faed8",
      "d6311889-591c-458f-8cf4-614868ea5329",
      "ff9db766-bdd9-48e7-ae04-cf41fa6838ad",
      "19e8cba8-0be1-473a-9263-ab9bc3f3c619",
      "c2f99e78-5126-451f-8d75-e4b5c5f1d452",
      "61990b09-c35e-4235-a899-c8bb5c32ab30",
      "741808f5-5592-4f92-b446-fe2752f3f785",
      "367a5c9d-4b6e-43e5-8f78-92f89d7fc9b6",
      "f883c391-5518-4fe1-94e2-519ae8fc6917",
      "afac7a96-8aed-474f-b14b-1e0bcff9f95d",
      "894258ef-52bf-457f-9619-99efeef09d2c",
      "6cecd239-a69b-48a0-914d-ed592612b491",
      "fc306814-7492-4335-82a2-0957b15a954c",
      "ae5eb396-b269-4de2-97bc-cea9d9151f05",
      "8bf71b6e-eb93-430f-ba97-1b544d2df840",
      "3c9a21a1-b035-437a-a020-5124944e8e0e",
      "60694279-f463-414d-b531-43810e3f3b8c",
      "205a4e4f-4d57-4b9f-b624-cf6ad1876200",
      "9a4c37e2-3a4c-40e5-8e61-21aee8641fc6",
      "142dff07-4f0b-4004-a8ce-e6031dd80556",
      "0553f5f3-f9aa-4097-8911-db0b5fc12db9",
      "e2833013-b77d-451b-8791-bf31b08dfd5e",
      "f189c157-1e08-4b88-a2c7-3cf3fcc83425",
      "f09f8124-434c-458c-870e-57d2a96e0a01",
      "17a9fc88-04f0-44bd-a313-f7be8cc8dd7f",
      "197b7b57-2187-4f77-a3f5-937eaabd2756",
      "ddd62b84-9287-4d06-bd6f-1bf48743a37b",
      "10f0da4b-59f7-4fa3-84cf-95a1bddab45c",
      "10217c3a-db21-488e-9990-4e4b75628aa6",
      "b33b52db-e54a-4997-893e-4362cff627dc",
      "6a91e5f4-f0d8-4bbf-8ef3-7600c76e1b46",
      "fdf158ee-e6fe-4024-bae5-f0bc3760c48a",
      "66adc338-3f6d-4561-be7b-5bcf6eb63d1b",
      "f903b686-b63b-4cde-9858-ff25b4117850",
      "229c65fb-86d2-4e63-b561-2c52695a963a",
      "2922099f-02d4-43da-b1c1-9f69bcdff571",
      "0ae18e29-2a73-4378-b354-04f8bc50abe1",
      "17c16c8d-2b84-494a-8d16-398d952d9ec3",
      "39ca2ee2-0ff8-4caa-863f-8249605e5a90",
      "5a323552-0e07-410a-9177-2440c184c79a",
      "cd2b15f0-3984-4669-8bdc-7f959fbf1186",
      "6a0ceb3b-f1e8-4006-9dc1-17f62bfb68b9",
      "81324f5d-33c4-4b58-9966-914f1cacc837",
      "bfb45f43-4d41-4ac1-9737-873168f9761e",
      "1ca2ad8a-badb-487b-a004-e40dcef2a13b",
      "7312d47c-f611-4603-a439-3aa63b991cc6",
      "b48ce372-a417-4d0c-8cba-a097cc4b44ca",
      "d8299dbd-a682-4d07-9f9a-33aefc607ed4",
      "6111fd34-89aa-4e8e-a1db-ab5378f33f66",
      "6ee742e9-4b78-44d7-936d-f63b4da0da94",
      "cec34841-3e73-4371-bd4d-9970a8f28296",
      "11997ab7-e4b6-429d-8840-f9be9403fbf9",
      "b8d6d723-dca5-4564-b7ce-d81a6d6d1986",
      "9a7e6eb3-3d8a-44d5-900d-351eed599a97",
      "7b4bc7e4-c426-437d-b86e-7c8be6e1369f",
      "3f312e20-3a5d-4f24-9430-b12e0ba91a27",
      "a608e491-ffbb-42c1-96c4-7a97b9e5633f",
      "2da3cb48-cac7-42bb-bb94-4cff5fb135e8",
      "d19784e2-1f91-4aa5-8c7d-e2a1f81292e6",
      "a6543a36-1fd1-4849-83db-ca77622989cd",
      "cf48d2de-881a-497d-bfe9-455cf06be2d8",
      "247cf279-d2a6-4110-8398-079037ef9397",
      "dbc40a8a-5ee1-4465-b8d3-c77d3b9d323f",
      "f3de0e73-fc82-4cad-99c1-003d396c61f7",
      "09dc52ee-9d7f-41a5-8e5f-55d579b58cf1",
      "d6495207-006e-4b6f-9e9d-e129c03416fa",
      "6847635f-a87f-43d8-92d1-a5a5fee65fbf",
      "4297cf69-6e4a-4f10-88d9-f808824a05d3",
      "a2d8f258-b68a-41b7-a779-8dc15d9d454c",
      "ce61e32c-d5a0-4511-9582-bc281ac47214",
      "d89606d2-0c3e-4296-aa77-7f24184128f3",
      "79a32971-dffe-4db0-a8e2-6bc13525ef09",
      "a757097f-950a-427f-b4ec-b691f9d3fe8a",
      "b3ffac6a-6e48-4577-acca-aca6b0f30fcc",
      "3c88372d-0a83-4bc8-9590-25dc9db11d59",
      "5c08ad59-73d3-4e6c-b36a-ab815220dfcc",
      "5efe0d7a-99eb-43e9-85e1-e8c4927dc095",
      "07aa7a51-902d-4f33-b91f-3ff285818fd2",
      "8dd0a113-8c2f-45c5-94ef-60f615a7ca73",
      "c4fa2544-e22a-4a1d-bbc5-0ffc2888ee9e",
      "22d4eb4b-b217-48a0-a7de-5df5972e8cc2",
      "c62d5f3c-1f54-4199-95d9-b5e58a5c44bd",
      "65ac588d-3646-40f1-afc1-7ee09c70f7b7",
      "fa4e853a-1306-40af-a8b7-d4159ccf639c",
      "4245f5e1-65ea-431d-8a11-951aa5044192",
      "13a7a0f0-b435-4837-bd24-486e7530bb4d",
      "ec0c67f2-8109-4c57-a4dd-c0f7a353d225",
      "186f8190-0844-4f51-bbed-e94b2e373e19",
      "82a6a453-46d4-4e62-bfec-c367cc0f08ab",
      "b612f8b4-6913-4606-9b44-7534bac78737",
      "2d67dd35-6cc7-4a75-8cf7-e37f444ae9f8",
      "bfe81dce-c4d8-4f7f-8c4f-b3f3d302357b",
      "6f4330a7-9eaa-4632-8596-44cd31bf532b",
      "3592ddbd-c785-45c9-9343-45fb58a0fe00",
      "e31c1a5a-5972-47f7-a3b5-96bd9250d097",
      "cc030b02-a6de-40e5-a783-fed80c6d92a3",
      "730389c7-8701-4761-9dc1-0e160d96db49",
      "4e5b8229-3c1f-436c-beea-cf7b6cbab79c",
      "932493ed-58e2-4691-912d-91952dcb641b",
      "f7b07680-f3d4-4782-8ed0-18eda9a137d2",
      "afdafd75-5126-4b82-81a7-16cda4d5fa5a",
      "d4428ecd-a109-411a-9afd-767c5eb08f04",
      "0303d843-e744-457d-8a67-1ba711bca6ec",
      "1394ffe3-f0d3-4b82-b74a-4d46e19736e3",
      "a2e996e8-f997-4a56-8558-13385197dbaa",
      "27e67b99-30c5-4552-bb59-1059f79a700e",
      "7da7c1b2-2585-42de-8f2a-e0ae0500d0af",
      "ed681f21-71a8-4ce6-a49d-d49e1be0bc13",
      "eaf662f9-d8ce-473d-a71c-96b2dae7e434",
      "6866325f-1692-400e-badf-0ecce28bb377",
      "c8ae0a10-54d0-40cf-8145-b9547b549c00",
      "32893fbb-e34f-4d4a-80f6-28627dfe65d5",
      "e95a9083-4af6-4791-9ce2-03c9e4cc3dcd",
      "7459458b-fe89-41a8-aebd-b3a274d06ecb",
      "3949e7b9-ac0a-4017-ad0f-27e168ecb0bb",
      "6804b904-bfca-4584-b0ce-4865fc7953dc",
      "adcd7366-5d69-417f-b1fc-d52c28ef0ba2",
      "07368004-bd54-45d8-9804-960ccc45281b",
      "0bda6059-fb9c-491b-893b-2159c28902f5",
      "b07e5adf-5223-4fb4-9630-6021c5a60b76",
      "5493f448-3015-4910-9c8b-ab31cebb0a15",
      "7f0724aa-4a8f-4f97-8a49-28463102cd5f",
      "967634fa-2e34-45bb-a91e-70d2ddbb52fa",
      "595c35dd-60ec-4ebd-8f2d-b72e623efe2b",
      "5182b76f-2a50-4779-952d-2252da0e704d",
      "8cc37547-c473-48ae-be28-a652340108ec",
      "0bae9049-aee9-4b5b-a269-b12cf925d7ac",
      "5876e8c9-6865-4610-8950-723767596b50",
      "7e69f64b-c641-4975-95ed-39d3b64cb0a9",
      "3596c2e2-22f4-4847-85fb-78488674ea54",
      "1af10b80-3ede-46dd-b984-01e2791424e0",
      "2d7c6ca6-9286-44b4-8e6c-daee9a2e7451",
      "b9c3e5e3-ba94-4cf0-b30b-4670ebd3d92f",
      "b3410ce6-14c2-4608-a585-8855ac9efdbb",
      "3572a59d-d41d-4971-a70e-4a74a927071a",
      "74b61dc4-df30-482c-af56-40ac34182e01",
      "b6382416-a4ce-4fca-ade9-f779454ce919",
      "e8ed8aeb-2411-4a7a-89a2-516a1e169cec",
      "99fc6484-5631-4a9c-a481-4c1a5c032756",
      "def89ea5-b220-4e4f-a855-00770ff878e8",
      "1c441672-6270-45fb-b998-02c7206754ee",
      "5429093c-bbeb-415d-94bc-96289b8f639d",
      "199b3c19-5b54-4e5e-94e9-14f402e72e65",
      "b5b39f4f-d112-4756-a544-1b0f0c813d62",
      "dfd98fde-d890-41be-b005-fac14721ef7b",
      "2b08f9f0-711d-47b5-90fd-7d048237f327",
      "7268118f-1c33-485b-9ba1-9ac96d86b5a9",
      "174000f6-a42a-4140-a336-ff35f715bbb4",
      "fb125950-ee2f-431f-8f12-3986d2122eca",
      "66684c45-4cc7-4276-a446-ed5cd82a5038",
      "9d4c5f0f-925e-417e-940a-e46d8799c890",
      "ac4a9c08-24b0-4d18-98c8-5fa3ed5d6ace",
      "c88b246f-d655-4397-9223-f5db848f7c76",
      "d2f01de8-3051-4387-804a-ec087a994aaa",
      "02dedf59-a58c-41a0-809a-fdec68c0a620",
      "579799ef-d342-4988-9c78-3c73ba9227c7",
      "b135ae83-b661-46ac-a765-55c159d151e2",
      "79cedc98-f321-4ad0-a225-1303aaa6bdd1",
      "0c6a2d14-395e-445d-bef1-00060db98138",
      "f753bd37-c8e6-41ce-bbc6-a3209fc37bc1",
      "ae8d3e32-4d1a-4ceb-94d1-09953cbda803",
      "35945e8f-f716-4473-895e-27386210bc7f",
      "53ada779-5080-43bf-a15b-2a2a50f3cbb4",
      "460d4d7b-305b-496f-9b62-1a5344f27c61",
      "8c74d6dd-4332-4d72-9b13-12c2ac58dfe8",
      "88cff9cb-f232-498f-b463-d00f92bcccc6",
      "de2bc7df-7e7b-45a6-8d6c-5c568a214de5",
      "1ac474f6-61a0-420b-8d71-357ce40bd233",
      "a0d65467-3a4d-49e8-888f-46da0e438f93",
      "ffe15da6-2f47-4fe0-a36b-9764fd4b5b47",
      "3e0c8ad6-f9e7-4b93-9c21-b33e2608d802",
      "8cabb26b-4b2d-4e1b-9d2b-8ff2a1aff595",
      "0a482d90-4c7b-4b3f-bd0e-27fe62a7dc7a",
      "86e29d66-5d29-49bf-8e05-b77ed4e5bbcc",
      "8273d462-c92f-4152-9c5d-ac40cf21f1c2",
      "6585916b-97ad-4bd2-8943-419685dc6aec",
      "ab7b1490-6846-4267-a431-4c59d993c178",
      "dd285a20-0cda-4853-abdc-bf32dce1d13e",
      "d6f8df07-487b-4620-ab45-ba915b79f233",
      "79638a91-b69f-4e08-8d31-27fc4bcba343",
      "7fe40ba0-3980-43de-a1b8-cad94059c882",
      "916dffa2-301b-4b82-95e1-bf68755f78ff",
      "7f9bca31-23b6-437b-bbff-504c83de9f1a",
      "191187a8-cffa-45bf-84ba-3fa8e641a4fc",
      "b4b7b042-1082-4180-b3e0-705a83890cfc",
      "762c2542-8b5e-4d7d-864b-f55c374cb4e3",
      "a3955a25-b68c-4490-adf5-c17eec2ff9e0",
      "18a53cb3-d56c-4f8c-afc8-936bb15c84a4",
      "e7030b66-d2e6-4190-8d76-c9a02893219b",
      "0a92778d-3f7a-4d6f-8437-c14b3fd30568",
      "364ae947-9034-46ee-aa18-da142df3ea6d",
      "9f1c81dd-a65a-448b-b55d-c448a20661da",
      "2ff26f15-6bc5-4378-8901-8209747ef5b7",
      "845bca9d-1a01-445c-a833-aec6719add83",
      "7f5536c3-c8f7-4b47-aa71-a9f26033a88b",
      "67dddfed-b56d-4a26-b204-579e1d237c22",
      "8c8e5233-2215-42b7-8ba7-adc3ad49be1a",
      "e4dcda4e-28ee-4789-80ea-cf8c46730fc3",
      "93bc21af-25f8-4c69-bb19-b0677c83ec14",
      "b2b1b549-466a-4ccf-8458-f0a8bc62cae9",
      "d4087ef7-f4ec-41aa-bba4-d17e6545f51d",
      "ad90ba95-dedb-422b-8de8-56b2a033c507",
      "d695b964-48ea-43ce-b065-c8ba3d7d745a",
      "601994ab-cd23-4a3d-af04-283ee7f45e0b",
      "64a6a6f0-bbeb-4c5d-8a92-c0a5a0d56eb9",
      "c0a6b001-1e9e-4e1e-ac5b-f4030e00247c",
      "b4766abd-911b-4349-a7b9-bbc93bc49698",
      "7e544f7b-5d9f-4b1c-8839-aa972c54de3a",
      "b45c46fd-2151-4cfe-95c7-75c053afced6",
      "1990eed0-53fd-4cb6-91f2-add1a237e4d9",
      "bcca8cc6-174c-4d05-adae-5b8a77303f55",
      "1bafabad-beb9-4c70-9f13-3d7ff44178eb",
      "85a769d3-b956-4951-98c1-435e0aff3d71",
      "48ca29dc-a1f3-4bb8-8361-ca9e19f29a24",
      "3a91d973-11e8-4336-a05c-4d65676daed7",
      "592c1648-bde2-4320-b848-b2cd186b0243",
      "85375c51-2eb0-4d82-b20f-6cb6acf62f3e",
      "7170c8a4-4ed5-41f6-90e8-d6bd7f49f36f",
      "9844beff-d39e-4b7a-bedd-61957203c57c",
      "7e8d2397-b4f7-43d8-ad42-471197703ef9",
      "d4e08fc4-ff54-403f-a3b8-075fb61d0be0",
      "ab101510-0d5d-4736-98e7-64e35dc17e41",
      "be18be83-1ec7-4018-bda0-3fbfb07f3819",
      "e678b7b0-9e77-4de9-a91b-9449efa3a203",
      "d6c2c189-ab52-42c6-ad60-4d598d492cd8",
      "07a58680-4c1d-4a0a-8483-939a90fd5ef5",
      "5e6c2c4a-7794-4aa1-8118-67e191f83493",
      "c33efed2-b7c8-45a6-89c9-59cd8d458844",
      "d7c92aef-baf2-4f0a-8556-c235d60ccd19",
      "66ce849b-74a5-4383-aa13-d5b69475fac1",
      "030ef8ae-23b0-4129-939c-b6677346debb",
      "9c4f8eb2-8110-4fca-b7a8-59130d44f628",
      "9317abfc-1334-4533-99c6-2a10cabdafa7",
      "f34a3870-03ca-4539-beea-7cc3671abc32",
      "07fdd493-9416-4acf-b817-54ea006eec3d",
      "5efd9d58-bb5c-4e0f-8c35-d0055db24b81",
      "8506b76b-e9b6-4742-af49-be033779ebb1",
      "d6c7f111-0796-4385-8ddf-b309fd6a8a47",
      "a1fb682c-a389-4a71-8b74-1d810275b9b5",
      "ca88b9f3-ccf3-4462-a504-9b4ec9ec0c71",
      "e5efd9b8-4550-4c4e-8c74-1cc6c679a1d4",
      "2bf0c9ab-9483-4c05-a025-7df659d9ecd9",
      "a95f93cf-bad4-4078-b1b4-61beb2893522",
      "3bd249c8-80c9-4368-96ae-481095190b40",
      "07539192-a4dc-4f1b-bb0a-d84daea04102",
      "e972c50d-e55b-4d13-8415-510c0d979f6e",
      "2e147561-d5f3-41b6-adad-162256cae9ed",
      "5d4de2f4-c1eb-4051-958a-0655b3e24ba5",
      "e73ff149-f0be-4f61-8b67-1e8562b2c31e",
      "02f84592-361e-492f-a24e-bfbd9ae63dfd",
      "4464d8b1-e032-4d7c-abd6-ec42e45fb1b7",
      "d427e953-8307-4c83-a89d-d548c8f19cb0",
      "38e37779-2462-498c-9f54-79eac5a6ba0e",
      "7bdd8dd5-635d-4dc8-af90-3bb16772f64e",
      "243bb2a6-4a1c-4f05-9e56-62226019965f",
      "7bdaeea9-4dde-49b0-a12f-207577f0ca20",
      "3b7208e9-916d-43ea-a1c0-ff26cd5937ee",
      "de36cae8-2cc1-462a-963d-aff48e648d3d",
      "f0c49aa5-f6a8-4da2-a830-1dbafdf68a28",
      "b8dd09dc-2a7b-4822-83e6-4c61f23ea635",
      "b62d961d-a7a0-42b8-9934-240a001b38c4",
      "2ae19afd-501e-4c9c-8f5a-e198e924cd6c",
      "8d1f864e-6d7f-4390-8443-d5cb40e8a98b",
      "3a894539-d16c-4585-a8c3-01ff0b5fec8a",
      "d0bcd771-0c4e-433f-993d-7fad42f56421",
      "df0c6ac5-85ac-48ea-8d8e-ae860b3f6e8c",
      "e73f1c22-3d8f-4b60-977c-e94673f9aa9c",
      "64f1e02b-a681-4d5b-8a80-7dce502feaaa"
    ]
  },
  "url_pool:items:c2f99e78-5126-451f-8d75-e4b5c5f1d452": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.747206+00:00",
      "id": "c2f99e78-5126-451f-8d75-e4b5c5f1d452",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 445, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"68f73a9fdc0f64893621d96fb3f09096\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3264699798-2-hoja-fep-28x20cm-halot-sky-lite-ld006-mono-x-saturn-_JM?pdp_filters=seller_id%3A1220423861#polycard_client=search-nordic&position=4&search_layout=grid&type=item&tracking_id=f1675599-e65a-456f-8444-a9771d519178&wid=MLM3264699798&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:a95f93cf-bad4-4078-b1b4-61beb2893522": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:2a84f1d3-a846-4762-aea2-d8ae92945129": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:81324f5d-33c4-4b58-9966-914f1cacc837": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:18668208-d8f9-42b6-b920-992d873e3f25": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.715719+00:00",
      "id": "18668208-d8f9-42b6-b920-992d873e3f25",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 379, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"bfb4a173ee3090691e58e0ca417bd301\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3038578138-2pc-estuche-organizador-de-lentes-de-sol-32-ranuras-de-polvo-_JM?searchVariation=182611757037#polycard_client=search-nordic&searchVariation=182611757037&position=3&search_layout=grid&type=item&tracking_id=eb245321-0b26-403f-a39d-bfe40ee15bb5",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:5394e668-e39b-45e7-918d-3d39959fe2f8": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:db5ccca2-7490-4edd-83de-c46654a1b49f": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.560679+00:00",
      "id": "db5ccca2-7490-4edd-83de-c46654a1b49f",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 58, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"dd3fb4cf58ac706f25efded46ae8a26b\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1589917982-10pcs-perchas-para-pantalones-en-z-ganchos-antideslizante-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=48&search_layout=grid&type=item&tracking_id=35d9b8ad-fa42-4ee2-9613-12b724bfce90",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:2e147561-d5f3-41b6-adad-162256cae9ed": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:source:all_task_item.xlsx": {
    "type": "set",
    "data": [
      "e833c635-0c93-4fe9-a449-bf62303a6853",
      "672d77c7-7dc8-4c1d-8710-437bd4a6f250",
      "dc13ccf3-9492-479d-b3cd-b4304d07303f",
      "4ab20e64-b3df-43f7-9470-eb3da2634b2f",
      "9f36776c-c551-4de3-ba42-9a87ad921ea3",
      "7f914cf4-e837-44ca-afb8-1d00dc75aa3e",
      "92f2bf3e-a68f-4a3f-b8c3-a70d5eb650f5",
      "6954c748-5393-402c-946e-ada4b90fa6ee",
      "62bcf9b3-7c32-4b47-9f12-31528c6467f7",
      "7e219924-e4ce-40dc-b731-c0410d3d516c",
      "cc9ade3d-af1a-447a-8c59-89b09cae4066",
      "7366b5ba-e2e0-42d8-8ded-8cc8abe62903",
      "bb7b9c9f-ff42-4dc1-a314-18c857a7e6b2",
      "a66f75f4-7fe4-401a-9162-cdebca513f1b",
      "d0973e89-135a-4616-8077-5f4ae3a74cca",
      "9c1fdfe9-6ff1-4576-856c-7cec5f547761",
      "f3c4b350-aaa9-4aab-8718-7caa3a87b172",
      "2a4e82c0-7d1e-4fd8-853a-1f778fc29df3",
      "946a4899-0500-41c9-894e-97bf28f7556d",
      "dd1cec1e-66e0-47e0-b01c-155cd21d67a9",
      "587bb8f9-1aa5-436d-9147-5a76bde6a287",
      "e54b6763-6827-4ee8-bddb-387e14891811",
      "baefd8e7-d2f1-47bf-abc9-ad39b634a1c3",
      "747f5827-15bb-467c-9d99-2f2d584f3d94",
      "94669a74-8a10-4ae5-b71e-a67878e58cc4",
      "f8ca87a3-b281-4bfb-a00a-383c11af844c",
      "d133972a-69bb-4191-b66b-4714b241e775",
      "78da0a7a-280d-4b28-b3da-7e4afe71c3b2",
      "9f8b4fbc-cd5b-4444-ac41-6a20ec08c486",
      "31a7dd40-9cf8-4100-9352-12c5a37b8049",
      "6e8a7df6-037b-4d13-824e-1924e7bdf855",
      "83b6d665-d6ad-44d5-9d9d-328c176960e0",
      "a0fc875e-7e7d-4678-af41-2073f288977b",
      "614cc2a4-70c3-448e-8827-a715aba0feee",
      "3a86beb5-c897-4bb0-8aa4-9c60b2019726",
      "1d534d57-dfeb-422d-950d-76c6dc54d3a2",
      "69f0cdd1-79df-406e-9051-67d80b1c817e",
      "c3b9c5a3-6ba3-4331-b7e2-c0f9bda675f0",
      "5124a881-0d79-459b-805c-9fa63b2e9f45",
      "9f81d14a-8d63-414d-8ddb-8940ea835449",
      "fee13e6f-2529-45eb-9b40-baf1a68f49d2",
      "13e579e9-2882-4a9a-ba0d-cb8b228488d4",
      "c0c184cc-e57f-49b4-9182-897062359fc3",
      "6050121f-9e34-4e27-bbf6-9dde6c437daf",
      "d5200ba0-a406-4332-9cae-8c794cd513a0",
      "eac7d796-f853-44d5-9ee2-8bb9c0d36669",
      "90f89afe-2921-4e63-831a-916ff68cbae2",
      "f3463c5d-f0ce-43ba-9411-ab60062b9e56",
      "3319d7b1-596e-4f13-9fde-b44ee9100a6a",
      "4b7f2c68-2c20-4df8-90c9-2bdacbbdddb1",
      "b2c1c8a9-f064-46cf-8563-98ac9afdd030",
      "85b202cc-f91f-452e-9982-5a6f2605f08d",
      "4bbd9d9f-e158-496f-a50f-d07abb1a42d6",
      "76010da4-3531-44d8-b4f6-23fd620a29b8",
      "27d67ad0-f4df-4b34-bfbd-b226fd36c402",
      "175a0357-0bc6-4f76-b036-d3ffe74f7efc",
      "fc45789c-49a4-4885-8678-c743a3a18125",
      "b4252436-5740-406f-ae78-68b37c9fd72b",
      "2ba3e535-39ff-41d6-85b4-94bad37f8039",
      "0b514166-5712-47ee-9a66-f2d001b0d211",
      "491227af-c24d-4903-a2f7-0ff7fe26d2d6",
      "07b73ebb-e96e-431b-a1f7-bab5e0b670b0",
      "da033aaa-4618-4606-8db2-25587d585095",
      "e35cd4ef-25fa-4359-9ae9-d962bfc584e0",
      "fb7f9c23-ff00-4538-a43b-3ba89d3ccaca",
      "7f86fd85-bdaf-4d1f-a846-758fd58729cb",
      "817df345-6baf-4af9-9342-698994f0f4a2",
      "ddb4c850-75b8-4131-bd8c-db27b174fc62",
      "78071445-fcf6-4a15-9d2b-5d25b52b5901",
      "99d6725c-773e-4446-9455-f2afdd3870d5",
      "67f2dce2-b299-40da-9b31-8e8b1198e275",
      "91722c88-b708-4e0a-8287-99c336d1fc6a",
      "808b5360-371d-4a2f-a9af-fd6b124bb5fb",
      "396614ca-77b3-4e63-8e68-5aac4c162797",
      "75720eed-a972-400b-82b1-f9e78be247b0",
      "be94cc69-9b22-4d27-bb30-92790c93b091",
      "c2e56158-bb88-4dc8-b908-1f6e32cfe97e",
      "003fd43e-aa38-4caf-abd2-05fd7982738b",
      "c094352a-fdc9-4931-bc13-c7ee0fbeba2d",
      "7a1c129a-673b-4789-bad7-985afe7c6c6b",
      "7f23bdda-644d-4060-b16a-7a2b0a0d25e4",
      "e718534f-e9ec-4afc-8366-2ea16ce314c1",
      "db5ccca2-7490-4edd-83de-c46654a1b49f",
      "150d5b16-4c68-4647-a170-443ce4275af6",
      "e34b84ce-163d-446d-aff4-c5b5411f2133",
      "67cb07d6-e281-4a70-93f5-3179137f8f0d",
      "4deedd1a-352d-4c04-9250-9e79096a7d63",
      "4bfbb5d9-b000-4f0e-aace-39775f648f5a",
      "cfc9d3df-2578-44bd-97ce-1e64033b7436",
      "92d475e7-ca9e-4fc2-83da-b390a35f7d76",
      "2f389a03-1d8f-437f-8d24-f4fd6c0202ca",
      "d51abf9d-7c50-4141-8a46-5809a3203ac3",
      "8dd4e411-6aa0-46c9-af64-d1ea09622044",
      "e48c6232-115e-481f-82d2-5225eab0326a",
      "0358c823-9779-4246-b040-4a8165998e47",
      "2e883a20-6765-4b4f-a662-9ccf12101b57",
      "021f523d-783c-4fbc-a96b-986625c81ad1",
      "cb39ec6c-c5b4-4082-820f-9cf96415fb81",
      "fc54d526-a6c3-48c6-ba99-78ebc147a27e",
      "85fd341d-6dd1-41a5-96a2-429fbb9d1458",
      "bcaa757c-50cb-47e9-8b5c-8d85188c10eb",
      "83a913d0-9c84-4112-989e-4e0bc38fa8ca",
      "2e3722ac-8e78-4887-a6ba-106696cae09d",
      "e1d03d48-1569-49d5-9590-9922371f3654",
      "5abd798a-ac98-4bf0-be8d-3927a8c1bd15",
      "bfc53740-e3c7-4c4b-b272-8ff4bfe2429a",
      "e48fabc0-ed8a-43a7-9d0b-08260b7acaff",
      "b49c8d27-2535-4066-a310-0c27bb9cacd1",
      "23640fa5-2b88-4b48-a87f-9911e09e643c",
      "b5b5a1c4-ed12-4fe7-b68c-72491d2f4bef",
      "49a162c6-bf56-4a77-8e2b-9df98da8703d",
      "2b41ba71-bfcc-43ab-9225-9bcb7bbd0e0e",
      "2bae22bf-dc5d-4690-9ca9-1ead28491117",
      "dc64784f-2b91-4d23-a9c4-0ed97b1a10f2",
      "e7ab2145-12f7-4948-a152-f9b8417fdf11",
      "7c38233f-bee8-4634-b523-66c82ed75418",
      "4578872b-83c8-40bd-b181-d8c598a8862f",
      "593dac49-9fc8-4857-af4e-fab2d7eadf79",
      "37e31b0f-2ebb-4b1c-8b20-ff6a610d1b59",
      "c25f0f31-4e0c-4f75-a952-f1ede03d776e",
      "1725793c-0ade-4693-a282-6f1f6713cb6c",
      "a12a878c-5b19-4b79-b238-0e14f158660c",
      "50e72435-a3a4-4e1e-8ae0-0edc2f4f206a",
      "0efe6597-0854-41a8-a84c-9247b74343ac",
      "85c33226-d782-4dd8-bad8-807f97dc48b1",
      "ea39299c-6294-44dc-aa3e-09f64a8fcd34",
      "7d40ab7d-f820-4c23-a0a6-a06ece6919c9",
      "362c3a06-5c79-48a4-92a9-000f5ab018a1",
      "f582343e-377b-413c-a376-14e03e7003e0",
      "2a43aeac-316b-4d0c-a401-80f667f1d7fe",
      "beccf5c5-7bf3-4987-8f3d-eb83abcc77c4",
      "d770c68b-edea-44f3-842a-2d032979efaa",
      "655ad75b-b4dd-44a8-8700-fc46b71bbefc",
      "f8dba5a2-4dae-432a-bbbf-f7de183c8809",
      "b03b6052-3fec-4092-84fc-80d8c042b568",
      "27d0da44-6ebf-4b69-b7ec-14dc11789ad0",
      "cc60737d-4050-46c1-ad07-4d5d77b5df48",
      "dee0c6a6-2da1-4d1d-b8e5-f239bbb54736",
      "c43c7297-a92d-4063-9ed4-37dda9bdbdf4",
      "ec4b624d-5fcf-4ab1-b0d5-e573594590bc",
      "ea2201c4-08ee-402d-a733-715985d1d216",
      "db35b2ba-318e-4d2f-af2e-7bd96caefda9",
      "23c02e6c-ce66-465c-92c8-9caa45ac3acd",
      "563abb7f-a686-406b-9a99-e743bf814462",
      "e13cdb35-b3d9-400b-90e6-50c8594f8f96",
      "32066db5-aca2-4ed5-9d5e-f955de0d0919",
      "2a9bec61-0fa0-41ea-ac02-be4f71825ecf",
      "fda847dc-1a65-4848-9490-e493e2b85f8d",
      "441852f7-4b34-4b98-9fca-e930515128d4",
      "34920b9a-f9fb-445a-9fc6-90f0a36f539d",
      "a41b2f12-a0c5-4f0b-8329-ecb854f6ac59",
      "447812a5-7bd5-4d39-ad62-e179b4967cbd",
      "fd7f93e3-ff43-4593-968a-46df66503ecd",
      "1ef50d6a-71b1-47d1-af07-2488429cf6dc",
      "cc62addf-670b-4967-b8de-0c110cc2e846",
      "656ff6f1-e286-4e12-a1a6-4397b49b238f",
      "48bcbd5a-9f6f-4f8c-a543-b58c060a4100",
      "4a84791d-b7f7-4525-8fe8-d5c184786aa8",
      "37412ee5-7329-44aa-b805-facfb0462b04",
      "d011ffb6-9cc2-4e11-9c0a-da2cfa9c00e5",
      "bed76989-3b5f-4a36-9278-66be1056518b",
      "1c2dd143-fd4d-438e-8136-ce878702ace0",
      "dc8c4819-aae8-458b-99f6-105788f7b753",
      "c6f2e0a1-aae4-4c73-b800-866d44de657b",
      "77c94d25-cbec-4bb4-ae0c-b9accd1d7d32",
      "5bc3974d-77ab-407f-8b20-799cfb13a2cc",
      "b306c0b0-6b7e-4d0b-a6e8-e0ebc6d626bf",
      "4e9d3d17-f40d-44a0-972d-45edd4473260",
      "21ae10a6-c196-4185-9f0e-2e5af7bedc1d",
      "5058c048-4260-43d6-ba17-8c6e7dc7e482",
      "18668208-d8f9-42b6-b920-992d873e3f25",
      "76f87a84-f67f-48e7-a19d-dfb174039b46",
      "86b1d7f6-1fa1-4f3a-a6e2-7e0e3623f227",
      "32c9fe79-d001-4b79-a9c6-b76bac673615",
      "3262a673-920f-4a74-ac37-0b1cd4a7a7c0",
      "df6688cb-0dec-43ca-a9c0-5388ff0b02e7",
      "84e206f4-c0d2-4115-8c20-c9a1a6244e59",
      "8a8ffa9e-1edc-4eeb-8138-183640b579fe",
      "d30ca988-38c3-46f4-b70d-8966e83db91e",
      "fb70f5b8-a6b8-4e3d-8a3e-0ade719ff2fa",
      "4a5643f0-594c-4276-80e3-d33202b61c3e",
      "87e3a2b5-e6c7-4e0c-b178-d6fd67a27e61",
      "18808850-89b6-4803-975a-df16bfc277f9",
      "eed66063-7c2c-452c-9b26-6113bbd29d8c",
      "b256d4dc-37c4-4073-a039-02c86de3569f",
      "bd7b21b4-9110-4a6f-86eb-b280256387d2",
      "b284c2fa-02e2-45e9-9001-2be1b69f4329",
      "494b953d-8586-4665-b4a0-003ae66f9207",
      "53241595-0209-4dde-8d5a-7b4bdba1d327",
      "450ba497-c73a-4eb9-a598-f14de84cb3bb",
      "4ae4f60d-51ac-44d1-a2e3-e806dc5370fb",
      "9c27539a-d518-44a2-bb61-cf8724959e2f",
      "ca52f635-68e8-4123-bdc1-8b23e446971d",
      "a85dcdd2-6449-4b57-8f5c-c437f85920b7",
      "58ba2d6b-918d-4d7b-bfb3-3ffaf76b23b8",
      "e3d1e561-9a0f-4638-bd23-997dedb96f14",
      "8150c675-9fad-4442-904b-0a3e51d82473",
      "b8b60e9d-5101-478a-9ad6-445f19a787a4",
      "8833da2a-5fa3-4a60-94c4-0abd3e0129c5",
      "7500d8c9-ab3c-4918-83a9-d65c77721c5f",
      "b43150e6-0fd9-4872-b5e6-c681a589adb9",
      "ad15455c-9dbb-4363-be0f-958b4c8a08f5",
      "8bbd3487-fd1a-4701-a58f-97e2f97c6d3a",
      "8546a6c8-6111-47dc-9b00-d5024b6093a4",
      "b3ee5359-64c1-40a5-b4e7-43f17556fcd5",
      "b11f1718-63c6-4636-9063-db66e39ac11c",
      "112215ab-5783-48e8-b414-204d73d2b96b",
      "a0ddeb23-4148-4f4e-8610-4b9a9acaaa5b",
      "ada6d385-7a7f-4be6-a699-91663854a6f9",
      "097b5f06-649c-412a-ba74-02611b7ca521",
      "78aa59cb-824f-4f35-8ac7-310b82b487f2",
      "2f5f9ed1-4f9a-4614-8e41-5c026f2e680b",
      "41a1cb5e-c276-4015-883b-54c46218ae51",
      "1fe9f8bc-a3d4-4eac-8dca-018d90d7b2e1",
      "f073574b-e1aa-4cd0-892f-17edd275dbd9",
      "4d5376d7-a468-46f0-b06a-c381ad499e34",
      "f1e27709-e2ab-4861-b221-a16f1837f6ae",
      "2a84f1d3-a846-4762-aea2-d8ae92945129",
      "250db918-993a-46a0-9ae9-84f3402a470b",
      "8477a250-9ac7-4e92-8ebf-60f8694c5648",
      "a6c516f2-4701-4f56-b2f8-617c8823ed6a",
      "99a907a6-f305-457a-9dc8-375f504ee9fd",
      "1fe2c05e-ab41-41d4-a6cb-795215a777b5",
      "0c8508ec-3ff2-4ecd-b156-1900f78b28d0",
      "d3412f83-615d-48d2-bcce-bef57e36bedd",
      "713579a0-0b89-4e1a-bee7-faf3d055de92",
      "80de1e41-0bb8-4d10-8c21-849e75725309",
      "6c380a90-94a1-4774-90e2-9339f35f98db",
      "92ee341d-cf1c-4eec-ac61-579715c11396",
      "fe1dec72-facc-4367-9069-5691a7f2c231",
      "e276b13a-c6b1-4bb8-94aa-3a6645344bcb",
      "2b0af047-c5a4-48b9-91ec-16ff789d57bf",
      "b3a2425a-2d04-4764-9fbc-9d554cc10e42",
      "4fe6af19-72be-4b47-88b7-a5e9c28d6993",
      "14ac291d-162c-4f98-bfcf-6ee2c59965b1",
      "64941fe1-5ea8-413d-bf77-56518c0e7a0c",
      "ae6db036-8cd1-4e1f-9fc3-119cc0b64dc6",
      "a056ba7c-3e6a-4be9-971f-388480113059",
      "76033dcb-af11-4e3c-9086-88b4e73c8221",
      "5f656686-7f35-489b-bfc3-8fa1a725eaa6",
      "970e8c15-4d52-439d-a3ed-8a2941a98c0a",
      "2a7a1908-572a-48cf-b3e2-2447b8657f96",
      "e89a23e3-0eb0-4970-a308-aff11fb86939",
      "93c5df07-c650-4dc6-850a-47b04fc8762c",
      "5d6c1a6a-7b21-4765-b359-288fb8bf2dba",
      "485d9bdb-2208-49f3-a6c7-3c6dba55b155",
      "6425594d-edb6-42b8-9979-a8689a216a15",
      "ea3013ee-9975-4283-b16f-f12515861678",
      "3304ceb2-51c6-4afa-b80e-f3af3f1bb25f",
      "beb4587f-3fcd-4320-abb9-698e4f9af7eb",
      "48a42c61-9ea6-4989-8b7c-5d41f4d347a9",
      "d4986f4b-5629-474f-8a0f-79270de19844",
      "6172b2c1-9295-4748-aec4-032ebeaa74dc",
      "4352328a-72e6-4cc6-837b-92f11c9ab822",
      "43e01a7f-04dd-413c-a215-73d7958fc50a",
      "e1fb7c40-da23-4e0f-8fb1-80f399f0b819",
      "4ac3e4f6-e01e-4f6a-aebc-32e86cae556a",
      "245810d3-47ac-46d1-b274-9199f4eae7ee",
      "c53311ef-32fc-41d6-9f79-7352515045f8",
      "92b89e38-c393-4ba7-9e21-b865c91c4694",
      "ed499675-ed86-4ec1-9317-d208eaa4cc8c",
      "eeef7e56-ef16-4e6b-841d-ece03435d0e3",
      "76f04c9f-9994-44f3-8d37-1985a6bb51f8",
      "74ee89e7-52f4-45af-9bb5-a7a4dec8af5f",
      "1bb39cb3-444b-4a07-9fc3-b9a06ded1e19",
      "887c95f9-8155-4f58-ae6f-48aa35073082",
      "b64a58ff-f2b4-4a64-84d7-6919342d3719",
      "4a99f47e-30ac-4b64-b347-7172857d4b4e",
      "e04ed9ff-d9c3-43c5-8d6c-413d47df6d86",
      "47411789-0322-4677-bbf5-56d3e2795fa0",
      "1dce8086-458d-4bdd-8216-1f72422c551e",
      "b1525cf1-41bd-4003-aa01-81fbd40d2906",
      "80ed09af-fdeb-4fc3-b5f9-e06a6306341e",
      "6c499066-62d3-4938-b5bd-6777598cd51b",
      "b3859bce-bc79-41ac-9ee4-1ce2b60bdc53",
      "c5b2d1a3-dc30-4299-8a1d-4cfe084d4173",
      "53e79342-c8e3-4923-ad51-a7c327e3d23c",
      "ccda614e-6e00-4283-9856-c5fe22b42be9",
      "7d9bdebc-7bb2-4916-ae7e-abeca44992e5",
      "769f0075-fe62-4e40-ab22-556d28766622",
      "d3a0cf80-a058-4eb9-abb8-a978b90514c6",
      "d21561ab-0886-4867-a642-b3c09651667b",
      "8959a23b-079b-4238-97cd-b271b8096f60",
      "379c0f47-ce7c-4fd8-a7d1-719f2184d172",
      "37c9ac1e-4dda-4103-bb32-93645dc08254",
      "6aa101f6-7219-4fc2-a1c1-06464cdb73f8",
      "8c53710b-1779-409a-b64f-c69e5c5e6382",
      "5a626b7d-838d-4773-8dee-46134bb2f815",
      "2f88d18d-29a4-41eb-9ed2-565d3e7d18e0",
      "df67a925-ba6b-4737-bdba-6e6622a33547",
      "91505854-9c5c-4623-b5e2-a0dae89f386b",
      "55de7260-6716-4cc0-9e0d-5a1da99b4c39",
      "d99a2f66-47dd-42d7-8f2a-fbb6c6c73032",
      "1a147a1c-9e2a-4bb7-8c94-8e2ec4f5e6d7",
      "3bb7bcb7-0a95-4558-9510-f652a93bb0e5",
      "b7675573-37da-48ce-9e26-92fd94e73b8d",
      "3bd70939-1fbd-45fe-b690-abaea829890b",
      "d4a46db9-4bce-42ff-9dd1-cb9aee62788e",
      "690b4c42-1d0b-4c47-9cba-6abed88e2c9b",
      "2603397b-4c6c-4503-b41e-3d3eb5c8beb7",
      "1d10e5a4-e461-48b4-a55f-9309ea53d707",
      "0874b960-b46a-4352-b195-cda7ea6ff4ba",
      "b99a711c-aae5-4084-b6d6-051f08c22fc6",
      "30eb62b8-3ebc-4081-a081-35b4badbc234",
      "87e04b31-0d42-469e-bd2f-c342f6b8a8df",
      "f36a5bfc-d71a-46ee-bfa8-9d8b4525afaf",
      "40973603-486b-44a6-9864-b5a28dc3e59f",
      "c2f05330-e0e6-4972-9254-ffcd7182748e",
      "9d1cca60-59c3-46e2-bf7e-105d467f2ef9",
      "266cdacc-5b7e-4a61-b954-c7b7a54a2be8",
      "11b00297-d9c3-45d0-87f5-0e3e3bc497fa",
      "6c0a8f26-0ba0-4a4a-92c2-697ea04cc113",
      "9cead1f4-cf23-4b8a-9827-b0eb469bd39a",
      "566fbb06-def2-4437-b208-c9508612d609",
      "bec0ce6b-acfa-4457-83e0-3711968d4937",
      "de49f594-00a7-437c-9582-f2f1dbf1cae5",
      "de03c240-3663-4d8d-af41-0547fd53b505",
      "521cd3c2-129c-46ed-b357-1f705273beeb",
      "9dcf6980-3315-4a8b-9cec-c82b07109094",
      "186d3279-581d-40e6-9f3c-2be337b967d9",
      "acac48d5-2a55-438a-ab87-3d184a1b1de1",
      "13debc83-447d-4529-a8e9-fd5a3120989e",
      "d98fb04a-3cad-4fd8-ba19-196f588747bd",
      "10adf58a-af02-4b59-9c73-f20d24db5f08",
      "d4c7ec19-8921-454d-a4f7-a69fefafb033",
      "b1d8fa13-198d-4048-ab95-e954e5883b9f",
      "b351aa92-6895-4a37-b35c-fb19698254f2",
      "4d491d36-0f5e-4f8a-86b8-9d2b3f56962a",
      "6686e587-0b5f-40af-94d8-9bd1cfe70bfa",
      "64b7b83f-4a30-4de9-8c75-b2b7d892f747",
      "59c975ae-ab92-40a0-a38e-8ed1dd4e844b",
      "5394e668-e39b-45e7-918d-3d39959fe2f8",
      "a630ea28-5582-4d27-b0b5-262d3b754ee6",
      "bb2d7309-05c2-4fb1-aa36-e5aefeb52a86",
      "b880dc5d-6b4b-4f4a-bbe6-1b5108254e67",
      "37a2df8a-9da5-4d76-a860-b831578275d0",
      "cb84b643-30cc-4d58-ac14-64d87f2626c5",
      "3a43bdbd-b07d-4377-a5db-f6976011226e",
      "dbb71df3-acac-4626-96ce-8aea475f926d",
      "b3abb05c-29c5-474d-bac1-b7e9b91374bb",
      "0ac34e4f-01f4-4f94-882d-e27acb6ca5f5",
      "aa7a2304-3507-4663-af0d-80914cded17d",
      "e77560a1-ba73-4b0a-876b-43e941a768a4",
      "999e2e5c-329a-4dcd-818a-24679f0d6fca",
      "84d2c3b4-1f55-402a-9603-2603fb3471d0",
      "0cb80f5d-e7ae-440c-b19f-b046f5c6db90",
      "b6a1a08a-ad8e-4879-a7c0-c3a66d168eea",
      "906f9445-d1ff-4600-a101-f3c3c72fe902",
      "81645980-b570-4649-a6f2-db38ab0a4135",
      "62c6823a-e5e4-458f-b332-ca0f23c0f3ba",
      "20815335-6862-418c-ae9d-2d8f13704038",
      "27bef139-f150-419a-b656-a98d35f5317c",
      "4b1312e5-5e02-415b-b5c4-9cf1eb439e95",
      "3e2ca227-015a-4919-bbb2-7be5de9af33a",
      "054c7612-fb88-421f-a74e-a38babb9d0d5",
      "6c62724a-144e-4bd6-ba40-d35b5a0a2fdb",
      "0c1e60e1-d226-4421-887d-d1a0cea38688",
      "926a5f46-2725-4f96-b6c9-adf6d67faed8",
      "d6311889-591c-458f-8cf4-614868ea5329",
      "ff9db766-bdd9-48e7-ae04-cf41fa6838ad",
      "19e8cba8-0be1-473a-9263-ab9bc3f3c619",
      "c2f99e78-5126-451f-8d75-e4b5c5f1d452",
      "61990b09-c35e-4235-a899-c8bb5c32ab30",
      "741808f5-5592-4f92-b446-fe2752f3f785",
      "367a5c9d-4b6e-43e5-8f78-92f89d7fc9b6",
      "f883c391-5518-4fe1-94e2-519ae8fc6917",
      "afac7a96-8aed-474f-b14b-1e0bcff9f95d",
      "894258ef-52bf-457f-9619-99efeef09d2c",
      "6cecd239-a69b-48a0-914d-ed592612b491",
      "fc306814-7492-4335-82a2-0957b15a954c",
      "ae5eb396-b269-4de2-97bc-cea9d9151f05",
      "8bf71b6e-eb93-430f-ba97-1b544d2df840",
      "3c9a21a1-b035-437a-a020-5124944e8e0e",
      "60694279-f463-414d-b531-43810e3f3b8c",
      "205a4e4f-4d57-4b9f-b624-cf6ad1876200",
      "9a4c37e2-3a4c-40e5-8e61-21aee8641fc6",
      "0553f5f3-f9aa-4097-8911-db0b5fc12db9",
      "e2833013-b77d-451b-8791-bf31b08dfd5e",
      "f189c157-1e08-4b88-a2c7-3cf3fcc83425",
      "f09f8124-434c-458c-870e-57d2a96e0a01",
      "17a9fc88-04f0-44bd-a313-f7be8cc8dd7f",
      "197b7b57-2187-4f77-a3f5-937eaabd2756",
      "ddd62b84-9287-4d06-bd6f-1bf48743a37b",
      "10f0da4b-59f7-4fa3-84cf-95a1bddab45c",
      "10217c3a-db21-488e-9990-4e4b75628aa6",
      "b33b52db-e54a-4997-893e-4362cff627dc",
      "6a91e5f4-f0d8-4bbf-8ef3-7600c76e1b46",
      "fdf158ee-e6fe-4024-bae5-f0bc3760c48a",
      "66adc338-3f6d-4561-be7b-5bcf6eb63d1b",
      "f903b686-b63b-4cde-9858-ff25b4117850",
      "229c65fb-86d2-4e63-b561-2c52695a963a",
      "2922099f-02d4-43da-b1c1-9f69bcdff571",
      "0ae18e29-2a73-4378-b354-04f8bc50abe1",
      "17c16c8d-2b84-494a-8d16-398d952d9ec3",
      "39ca2ee2-0ff8-4caa-863f-8249605e5a90",
      "5a323552-0e07-410a-9177-2440c184c79a",
      "cd2b15f0-3984-4669-8bdc-7f959fbf1186",
      "6a0ceb3b-f1e8-4006-9dc1-17f62bfb68b9",
      "81324f5d-33c4-4b58-9966-914f1cacc837",
      "bfb45f43-4d41-4ac1-9737-873168f9761e",
      "1ca2ad8a-badb-487b-a004-e40dcef2a13b",
      "7312d47c-f611-4603-a439-3aa63b991cc6",
      "b48ce372-a417-4d0c-8cba-a097cc4b44ca",
      "d8299dbd-a682-4d07-9f9a-33aefc607ed4",
      "6111fd34-89aa-4e8e-a1db-ab5378f33f66",
      "6ee742e9-4b78-44d7-936d-f63b4da0da94",
      "cec34841-3e73-4371-bd4d-9970a8f28296",
      "11997ab7-e4b6-429d-8840-f9be9403fbf9",
      "b8d6d723-dca5-4564-b7ce-d81a6d6d1986",
      "9a7e6eb3-3d8a-44d5-900d-351eed599a97",
      "7b4bc7e4-c426-437d-b86e-7c8be6e1369f",
      "3f312e20-3a5d-4f24-9430-b12e0ba91a27",
      "a608e491-ffbb-42c1-96c4-7a97b9e5633f",
      "2da3cb48-cac7-42bb-bb94-4cff5fb135e8",
      "d19784e2-1f91-4aa5-8c7d-e2a1f81292e6",
      "a6543a36-1fd1-4849-83db-ca77622989cd",
      "cf48d2de-881a-497d-bfe9-455cf06be2d8",
      "247cf279-d2a6-4110-8398-079037ef9397",
      "dbc40a8a-5ee1-4465-b8d3-c77d3b9d323f",
      "f3de0e73-fc82-4cad-99c1-003d396c61f7",
      "09dc52ee-9d7f-41a5-8e5f-55d579b58cf1",
      "d6495207-006e-4b6f-9e9d-e129c03416fa",
      "6847635f-a87f-43d8-92d1-a5a5fee65fbf",
      "4297cf69-6e4a-4f10-88d9-f808824a05d3",
      "a2d8f258-b68a-41b7-a779-8dc15d9d454c",
      "ce61e32c-d5a0-4511-9582-bc281ac47214",
      "d89606d2-0c3e-4296-aa77-7f24184128f3",
      "79a32971-dffe-4db0-a8e2-6bc13525ef09",
      "a757097f-950a-427f-b4ec-b691f9d3fe8a",
      "b3ffac6a-6e48-4577-acca-aca6b0f30fcc",
      "3c88372d-0a83-4bc8-9590-25dc9db11d59",
      "5c08ad59-73d3-4e6c-b36a-ab815220dfcc",
      "5efe0d7a-99eb-43e9-85e1-e8c4927dc095",
      "07aa7a51-902d-4f33-b91f-3ff285818fd2",
      "8dd0a113-8c2f-45c5-94ef-60f615a7ca73",
      "c4fa2544-e22a-4a1d-bbc5-0ffc2888ee9e",
      "22d4eb4b-b217-48a0-a7de-5df5972e8cc2",
      "c62d5f3c-1f54-4199-95d9-b5e58a5c44bd",
      "65ac588d-3646-40f1-afc1-7ee09c70f7b7",
      "fa4e853a-1306-40af-a8b7-d4159ccf639c",
      "4245f5e1-65ea-431d-8a11-951aa5044192",
      "13a7a0f0-b435-4837-bd24-486e7530bb4d",
      "ec0c67f2-8109-4c57-a4dd-c0f7a353d225",
      "186f8190-0844-4f51-bbed-e94b2e373e19",
      "82a6a453-46d4-4e62-bfec-c367cc0f08ab",
      "b612f8b4-6913-4606-9b44-7534bac78737",
      "2d67dd35-6cc7-4a75-8cf7-e37f444ae9f8",
      "bfe81dce-c4d8-4f7f-8c4f-b3f3d302357b",
      "6f4330a7-9eaa-4632-8596-44cd31bf532b",
      "3592ddbd-c785-45c9-9343-45fb58a0fe00",
      "e31c1a5a-5972-47f7-a3b5-96bd9250d097",
      "cc030b02-a6de-40e5-a783-fed80c6d92a3",
      "730389c7-8701-4761-9dc1-0e160d96db49",
      "4e5b8229-3c1f-436c-beea-cf7b6cbab79c",
      "932493ed-58e2-4691-912d-91952dcb641b",
      "f7b07680-f3d4-4782-8ed0-18eda9a137d2",
      "afdafd75-5126-4b82-81a7-16cda4d5fa5a",
      "d4428ecd-a109-411a-9afd-767c5eb08f04",
      "0303d843-e744-457d-8a67-1ba711bca6ec",
      "1394ffe3-f0d3-4b82-b74a-4d46e19736e3",
      "a2e996e8-f997-4a56-8558-13385197dbaa",
      "27e67b99-30c5-4552-bb59-1059f79a700e",
      "7da7c1b2-2585-42de-8f2a-e0ae0500d0af",
      "ed681f21-71a8-4ce6-a49d-d49e1be0bc13",
      "eaf662f9-d8ce-473d-a71c-96b2dae7e434",
      "6866325f-1692-400e-badf-0ecce28bb377",
      "c8ae0a10-54d0-40cf-8145-b9547b549c00",
      "32893fbb-e34f-4d4a-80f6-28627dfe65d5",
      "e95a9083-4af6-4791-9ce2-03c9e4cc3dcd",
      "7459458b-fe89-41a8-aebd-b3a274d06ecb",
      "3949e7b9-ac0a-4017-ad0f-27e168ecb0bb",
      "6804b904-bfca-4584-b0ce-4865fc7953dc",
      "adcd7366-5d69-417f-b1fc-d52c28ef0ba2",
      "07368004-bd54-45d8-9804-960ccc45281b",
      "0bda6059-fb9c-491b-893b-2159c28902f5",
      "b07e5adf-5223-4fb4-9630-6021c5a60b76",
      "5493f448-3015-4910-9c8b-ab31cebb0a15",
      "7f0724aa-4a8f-4f97-8a49-28463102cd5f",
      "967634fa-2e34-45bb-a91e-70d2ddbb52fa",
      "595c35dd-60ec-4ebd-8f2d-b72e623efe2b",
      "5182b76f-2a50-4779-952d-2252da0e704d",
      "8cc37547-c473-48ae-be28-a652340108ec",
      "0bae9049-aee9-4b5b-a269-b12cf925d7ac",
      "5876e8c9-6865-4610-8950-723767596b50",
      "7e69f64b-c641-4975-95ed-39d3b64cb0a9",
      "3596c2e2-22f4-4847-85fb-78488674ea54",
      "1af10b80-3ede-46dd-b984-01e2791424e0",
      "2d7c6ca6-9286-44b4-8e6c-daee9a2e7451",
      "b9c3e5e3-ba94-4cf0-b30b-4670ebd3d92f",
      "b3410ce6-14c2-4608-a585-8855ac9efdbb",
      "3572a59d-d41d-4971-a70e-4a74a927071a",
      "74b61dc4-df30-482c-af56-40ac34182e01",
      "b6382416-a4ce-4fca-ade9-f779454ce919",
      "e8ed8aeb-2411-4a7a-89a2-516a1e169cec",
      "99fc6484-5631-4a9c-a481-4c1a5c032756",
      "def89ea5-b220-4e4f-a855-00770ff878e8",
      "1c441672-6270-45fb-b998-02c7206754ee",
      "5429093c-bbeb-415d-94bc-96289b8f639d",
      "199b3c19-5b54-4e5e-94e9-14f402e72e65",
      "b5b39f4f-d112-4756-a544-1b0f0c813d62",
      "dfd98fde-d890-41be-b005-fac14721ef7b",
      "2b08f9f0-711d-47b5-90fd-7d048237f327",
      "7268118f-1c33-485b-9ba1-9ac96d86b5a9",
      "174000f6-a42a-4140-a336-ff35f715bbb4",
      "fb125950-ee2f-431f-8f12-3986d2122eca",
      "66684c45-4cc7-4276-a446-ed5cd82a5038",
      "9d4c5f0f-925e-417e-940a-e46d8799c890",
      "ac4a9c08-24b0-4d18-98c8-5fa3ed5d6ace",
      "c88b246f-d655-4397-9223-f5db848f7c76",
      "d2f01de8-3051-4387-804a-ec087a994aaa",
      "02dedf59-a58c-41a0-809a-fdec68c0a620",
      "579799ef-d342-4988-9c78-3c73ba9227c7",
      "b135ae83-b661-46ac-a765-55c159d151e2",
      "79cedc98-f321-4ad0-a225-1303aaa6bdd1",
      "0c6a2d14-395e-445d-bef1-00060db98138",
      "f753bd37-c8e6-41ce-bbc6-a3209fc37bc1",
      "ae8d3e32-4d1a-4ceb-94d1-09953cbda803",
      "35945e8f-f716-4473-895e-27386210bc7f",
      "53ada779-5080-43bf-a15b-2a2a50f3cbb4",
      "460d4d7b-305b-496f-9b62-1a5344f27c61",
      "8c74d6dd-4332-4d72-9b13-12c2ac58dfe8",
      "88cff9cb-f232-498f-b463-d00f92bcccc6",
      "de2bc7df-7e7b-45a6-8d6c-5c568a214de5",
      "1ac474f6-61a0-420b-8d71-357ce40bd233",
      "a0d65467-3a4d-49e8-888f-46da0e438f93",
      "ffe15da6-2f47-4fe0-a36b-9764fd4b5b47",
      "3e0c8ad6-f9e7-4b93-9c21-b33e2608d802",
      "8cabb26b-4b2d-4e1b-9d2b-8ff2a1aff595",
      "0a482d90-4c7b-4b3f-bd0e-27fe62a7dc7a",
      "86e29d66-5d29-49bf-8e05-b77ed4e5bbcc",
      "8273d462-c92f-4152-9c5d-ac40cf21f1c2",
      "6585916b-97ad-4bd2-8943-419685dc6aec",
      "ab7b1490-6846-4267-a431-4c59d993c178",
      "dd285a20-0cda-4853-abdc-bf32dce1d13e",
      "d6f8df07-487b-4620-ab45-ba915b79f233",
      "79638a91-b69f-4e08-8d31-27fc4bcba343",
      "7fe40ba0-3980-43de-a1b8-cad94059c882",
      "916dffa2-301b-4b82-95e1-bf68755f78ff",
      "7f9bca31-23b6-437b-bbff-504c83de9f1a",
      "191187a8-cffa-45bf-84ba-3fa8e641a4fc",
      "b4b7b042-1082-4180-b3e0-705a83890cfc",
      "762c2542-8b5e-4d7d-864b-f55c374cb4e3",
      "a3955a25-b68c-4490-adf5-c17eec2ff9e0",
      "18a53cb3-d56c-4f8c-afc8-936bb15c84a4",
      "e7030b66-d2e6-4190-8d76-c9a02893219b",
      "0a92778d-3f7a-4d6f-8437-c14b3fd30568",
      "364ae947-9034-46ee-aa18-da142df3ea6d",
      "9f1c81dd-a65a-448b-b55d-c448a20661da",
      "2ff26f15-6bc5-4378-8901-8209747ef5b7",
      "845bca9d-1a01-445c-a833-aec6719add83",
      "7f5536c3-c8f7-4b47-aa71-a9f26033a88b",
      "67dddfed-b56d-4a26-b204-579e1d237c22",
      "8c8e5233-2215-42b7-8ba7-adc3ad49be1a",
      "e4dcda4e-28ee-4789-80ea-cf8c46730fc3",
      "93bc21af-25f8-4c69-bb19-b0677c83ec14",
      "b2b1b549-466a-4ccf-8458-f0a8bc62cae9",
      "d4087ef7-f4ec-41aa-bba4-d17e6545f51d",
      "ad90ba95-dedb-422b-8de8-56b2a033c507",
      "d695b964-48ea-43ce-b065-c8ba3d7d745a",
      "601994ab-cd23-4a3d-af04-283ee7f45e0b",
      "64a6a6f0-bbeb-4c5d-8a92-c0a5a0d56eb9",
      "c0a6b001-1e9e-4e1e-ac5b-f4030e00247c",
      "b4766abd-911b-4349-a7b9-bbc93bc49698",
      "7e544f7b-5d9f-4b1c-8839-aa972c54de3a",
      "b45c46fd-2151-4cfe-95c7-75c053afced6",
      "1990eed0-53fd-4cb6-91f2-add1a237e4d9",
      "bcca8cc6-174c-4d05-adae-5b8a77303f55",
      "1bafabad-beb9-4c70-9f13-3d7ff44178eb",
      "85a769d3-b956-4951-98c1-435e0aff3d71",
      "48ca29dc-a1f3-4bb8-8361-ca9e19f29a24",
      "3a91d973-11e8-4336-a05c-4d65676daed7",
      "592c1648-bde2-4320-b848-b2cd186b0243",
      "85375c51-2eb0-4d82-b20f-6cb6acf62f3e",
      "7170c8a4-4ed5-41f6-90e8-d6bd7f49f36f",
      "9844beff-d39e-4b7a-bedd-61957203c57c",
      "7e8d2397-b4f7-43d8-ad42-471197703ef9",
      "d4e08fc4-ff54-403f-a3b8-075fb61d0be0",
      "ab101510-0d5d-4736-98e7-64e35dc17e41",
      "be18be83-1ec7-4018-bda0-3fbfb07f3819",
      "e678b7b0-9e77-4de9-a91b-9449efa3a203",
      "d6c2c189-ab52-42c6-ad60-4d598d492cd8",
      "07a58680-4c1d-4a0a-8483-939a90fd5ef5",
      "5e6c2c4a-7794-4aa1-8118-67e191f83493",
      "c33efed2-b7c8-45a6-89c9-59cd8d458844",
      "d7c92aef-baf2-4f0a-8556-c235d60ccd19",
      "66ce849b-74a5-4383-aa13-d5b69475fac1",
      "030ef8ae-23b0-4129-939c-b6677346debb",
      "9c4f8eb2-8110-4fca-b7a8-59130d44f628",
      "9317abfc-1334-4533-99c6-2a10cabdafa7",
      "f34a3870-03ca-4539-beea-7cc3671abc32",
      "07fdd493-9416-4acf-b817-54ea006eec3d",
      "5efd9d58-bb5c-4e0f-8c35-d0055db24b81",
      "8506b76b-e9b6-4742-af49-be033779ebb1",
      "d6c7f111-0796-4385-8ddf-b309fd6a8a47",
      "a1fb682c-a389-4a71-8b74-1d810275b9b5",
      "ca88b9f3-ccf3-4462-a504-9b4ec9ec0c71",
      "e5efd9b8-4550-4c4e-8c74-1cc6c679a1d4",
      "2bf0c9ab-9483-4c05-a025-7df659d9ecd9",
      "a95f93cf-bad4-4078-b1b4-61beb2893522",
      "3bd249c8-80c9-4368-96ae-481095190b40",
      "07539192-a4dc-4f1b-bb0a-d84daea04102",
      "e972c50d-e55b-4d13-8415-510c0d979f6e",
      "2e147561-d5f3-41b6-adad-162256cae9ed",
      "5d4de2f4-c1eb-4051-958a-0655b3e24ba5",
      "e73ff149-f0be-4f61-8b67-1e8562b2c31e",
      "02f84592-361e-492f-a24e-bfbd9ae63dfd",
      "4464d8b1-e032-4d7c-abd6-ec42e45fb1b7",
      "d427e953-8307-4c83-a89d-d548c8f19cb0",
      "38e37779-2462-498c-9f54-79eac5a6ba0e",
      "7bdd8dd5-635d-4dc8-af90-3bb16772f64e",
      "243bb2a6-4a1c-4f05-9e56-62226019965f",
      "7bdaeea9-4dde-49b0-a12f-207577f0ca20",
      "3b7208e9-916d-43ea-a1c0-ff26cd5937ee",
      "de36cae8-2cc1-462a-963d-aff48e648d3d",
      "f0c49aa5-f6a8-4da2-a830-1dbafdf68a28",
      "b8dd09dc-2a7b-4822-83e6-4c61f23ea635",
      "b62d961d-a7a0-42b8-9934-240a001b38c4",
      "2ae19afd-501e-4c9c-8f5a-e198e924cd6c",
      "8d1f864e-6d7f-4390-8443-d5cb40e8a98b",
      "3a894539-d16c-4585-a8c3-01ff0b5fec8a",
      "d0bcd771-0c4e-433f-993d-7fad42f56421",
      "df0c6ac5-85ac-48ea-8d8e-ae860b3f6e8c",
      "e73f1c22-3d8f-4b60-977c-e94673f9aa9c",
      "64f1e02b-a681-4d5b-8a80-7dce502feaaa"
    ]
  },
  "url_pool:items:e678b7b0-9e77-4de9-a91b-9449efa3a203": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.727138+00:00",
      "id": "e678b7b0-9e77-4de9-a91b-9449efa3a203",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 404, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"77d484bcde9d19a5cb4b9f7280bdf73a\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/memo-cx07-ventilador-cooler-para-telefono-semiconductores-cooler-para-celular/p/MLM42430790#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:f36a5bfc-d71a-46ee-bfa8-9d8b4525afaf": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.535552+00:00",
      "id": "f36a5bfc-d71a-46ee-bfa8-9d8b4525afaf",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 10, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"6d9bd62f1cc9b2844c83beeeb1d46acd\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3361224798-cuadro-de-firmas-libro-invitados-xv-anos-para-boda-bautismo-_JM#polycard_client=search-nordic&position=25&search_layout=grid&type=item&tracking_id=18c86909-b23f-4026-995f-37c4c644baca",
      "check_count": "0"
    }
  },
  "url_pool:items:27d67ad0-f4df-4b34-bfbd-b226fd36c402": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.666030+00:00",
      "id": "27d67ad0-f4df-4b34-bfbd-b226fd36c402",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 273, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"29bbff4e58105ad0b4db3ae22c045f83\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3467028110-lampara-de-wood-uv-facial-y-corporalmuebles-para-estetica-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=b13c146f-749b-42af-bd5d-30158c2281ab",
      "check_count": "0"
    }
  },
  "url_pool:items:c094352a-fdc9-4931-bc13-c7ee0fbeba2d": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.796738+00:00",
      "id": "c094352a-fdc9-4931-bc13-c7ee0fbeba2d",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 549, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"6c33306e03f0d0ed5efc3f5744638d9a\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3445525002-diy-artesanal-laminas-de-madera-de-balsa-placa-de-madera-_JM?highlight=false&pdp_filters=seller_id%3A730605346&headerTopBrand=false#polycard_client=search-nordic&position=18&search_layout=grid&type=item&tracking_id=644c49c6-a7f5-45cd-b97f-8a6096f6a1cf",
      "check_count": "0"
    }
  },
  "url_pool:items:c8ae0a10-54d0-40cf-8145-b9547b549c00": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.621366+00:00",
      "id": "c8ae0a10-54d0-40cf-8145-b9547b549c00",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 179, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"5cc08d581a286df9383997f8a3fbbf65\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3361223870-ventosas-de-silicona-para-masajes-8-piezasrosa-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=1&reco_backend_type=low_level&reco_id=2b9cf79c-6a92-49ef-a9a9-ba0640410ae9",
      "check_count": "0"
    }
  },
  "url_pool:items:817df345-6baf-4af9-9342-698994f0f4a2": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.813672+00:00",
      "id": "817df345-6baf-4af9-9342-698994f0f4a2",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 584, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c0ca8cba56cbccb7d335e1ea92284757\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3581536866-control-remoto-para-portones-con-selector-de-9-posiciones-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_low_exposition%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=15&reco_backend_type=low_level&reco_id=dafa3026-7aa3-4d37-9446-cb9526278024",
      "check_count": "0"
    }
  },
  "url_pool:items:e4dcda4e-28ee-4789-80ea-cf8c46730fc3": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.679852+00:00",
      "id": "e4dcda4e-28ee-4789-80ea-cf8c46730fc3",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 302, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"2b327cfe352ec3015ffc92eef1d37dd5\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1424487116-kit-para-aplicar-silicon-acabados-de-silicon-herramientas-_JM#is_advertising=true&position=1&search_layout=grid&type=pad&tracking_id=f4a4281c-5d23-4caa-9488-1b79f858bd82&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=1&ad_click_id=MjY3MDdmMDYtOGUwYS00NjYzLThiMzktNDRkZDAxMjZmNTM1",
      "check_count": "0"
    }
  },
  "url_pool:items:7e8d2397-b4f7-43d8-ad42-471197703ef9": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.549671+00:00",
      "id": "7e8d2397-b4f7-43d8-ad42-471197703ef9",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 37, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"a51f992d319583afd5b1ff0c6593515a\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2194809665-caspol-con-sistema-antiolor-y-pulsador-automatico-de-fregado-_JM?searchVariation=182345621136#polycard_client=search-nordic&searchVariation=182345621136&position=43&search_layout=grid&type=item&tracking_id=b81d15ef-086a-4dc9-bf69-686e4a59eb1a",
      "check_count": "0"
    }
  },
  "url_pool:items:84e206f4-c0d2-4115-8c20-c9a1a6244e59": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.663279+00:00",
      "id": "84e206f4-c0d2-4115-8c20-c9a1a6244e59",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 267, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0e754c2a6fe7183a419f3cc3f1b527ca\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/4-ruedas-llantas-de-2-360-grados-de-giratoria-rodajas/p/MLM43906326?pdp_filters=seller_id%3A639046839#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:4bfbb5d9-b000-4f0e-aace-39775f648f5a": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.714369+00:00",
      "id": "4bfbb5d9-b000-4f0e-aace-39775f648f5a",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 376, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"b968cb7199634eb4c38d0a563248ac88\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3527804676-funda-protectora-de-cargador-cable-de-datos-para-iphone-kit-_JM#is_advertising=true&backend_model=search-backend&position=1&search_layout=stack&type=pad&tracking_id=80e5dd8f-3c1e-427a-bc93-b6160288569e&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=1&ad_click_id=YzZlY2EwZjQtZjIwNi00NDhjLWFhYTgtZTg3YWZiMTBmN2Y0",
      "check_count": "0"
    }
  },
  "url_pool:items:e73f1c22-3d8f-4b60-977c-e94673f9aa9c": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.684042+00:00",
      "id": "e73f1c22-3d8f-4b60-977c-e94673f9aa9c",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 311, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"44658ce60acb89b88646933e88609012\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3605247188-juego-del-calamar-2-set-de-juego-interactivo-completo-_JM#polycard_client=search_best-seller",
      "check_count": "0"
    }
  },
  "url_pool:items:47411789-0322-4677-bbf5-56d3e2795fa0": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.672115+00:00",
      "id": "47411789-0322-4677-bbf5-56d3e2795fa0",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 286, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"adc3fa345852f1562595631c67c59368\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2198645389-organizador-escobas-estante-colgador-4pcs-soporte-multiusos-_JM#polycard_client=search-nordic&position=33&search_layout=grid&type=item&tracking_id=dc42e1cc-6d2d-46f5-bba3-39a31c8ddfee&wid=MLM2198645389&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:23c02e6c-ce66-465c-92c8-9caa45ac3acd": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:b62d961d-a7a0-42b8-9934-240a001b38c4": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.752633+00:00",
      "id": "b62d961d-a7a0-42b8-9934-240a001b38c4",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 456, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"f0f780fae9494e41d938f1e9defd669b\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/domoko-filtro-y-conector-hl-145-v-rapido-para-hidrolavadora-koblenz-completo-naranja-claro/p/MLM42797831?pdp_filters=seller_id%3A812926121%7Cadult_content%3Ayes&searchVariation=MLM42797831#polycard_client=search-nordic&searchVariation=MLM42797831&wid=MLM3357863050&position=20&search_layout=stack&type=product&tracking_id=f2960941-4465-443a-9c25-43786a0edb8e&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:b1525cf1-41bd-4003-aa01-81fbd40d2906": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:a0fc875e-7e7d-4678-af41-2073f288977b": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.636156+00:00",
      "id": "a0fc875e-7e7d-4678-af41-2073f288977b",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 209, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"26d749bd8a345b93a7a79eed14ea44be\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/soporte-de-carga-para-bicicletas-con-capacidad-de-70-kg/p/MLM37418696?pdp_filters=seller_id%3A1423618982#reviews",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:66684c45-4cc7-4276-a446-ed5cd82a5038": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:3a894539-d16c-4585-a8c3-01ff0b5fec8a": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.626727+00:00",
      "id": "3a894539-d16c-4585-a8c3-01ff0b5fec8a",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 190, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c845a6b31c1bbfb27dd86676655f25a4\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3585284086-2pcs-controles-remoto-universal-puertas-automaticas-433mhz-_JM?pdp_filters=item_id:MLM3585284086#polycard_client=recommendations_vip-pads-up&reco_backend=vip-pads-up-experimental-a_marketplace&reco_model=rk_ent_v2_retsys_ads&reco_client=vip-pads-up&reco_item_pos=3&reco_backend_type=low_level&reco_id=2985040d-7e80-4192-8dd0-adab24346186&is_advertising=true&ad_domain=VIPDESKTOP_UP&ad_position=4&ad_click_id=NTgwZTI4M2MtMjUyMS00ZDIyLTkyZjUtNDg4NGE2MmZmOGI3",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:1bafabad-beb9-4c70-9f13-3d7ff44178eb": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:d21561ab-0886-4867-a642-b3c09651667b": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "a78a6a29-fcde-4def-b18e-9c2a300bf64f",
      "3db0e2ae-6cbf-4ca0-ba72-39342742ac2c",
      "0efccc4b-8ce7-44fa-8431-c31ae81fd81f",
      "b37baf75-b0d2-46fb-b2e3-789804025ac1",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe",
      "41a7eb96-648c-4b09-b49e-61718afb4d14"
    ]
  },
  "url_pool:items:1d10e5a4-e461-48b4-a55f-9309ea53d707": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.690430+00:00",
      "id": "1d10e5a4-e461-48b4-a55f-9309ea53d707",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 325, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"ba80628986ad674675a0e53eb6fc3c22\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2209059899-tesla-cybertruck-remolque-caravana-kit-miniatura-metal-coche-_JM?searchVariation=182584361428&vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&searchVariation=182584361428&position=1&search_layout=grid&type=item&tracking_id=839f6e53-1281-4d55-abb3-334c7ea64fc8",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:8d1f864e-6d7f-4390-8443-d5cb40e8a98b": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:b2c1c8a9-f064-46cf-8563-98ac9afdd030": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "a78a6a29-fcde-4def-b18e-9c2a300bf64f",
      "3db0e2ae-6cbf-4ca0-ba72-39342742ac2c",
      "0efccc4b-8ce7-44fa-8431-c31ae81fd81f",
      "b37baf75-b0d2-46fb-b2e3-789804025ac1",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe",
      "41a7eb96-648c-4b09-b49e-61718afb4d14"
    ]
  },
  "url_pool:items:5c08ad59-73d3-4e6c-b36a-ab815220dfcc": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.625728+00:00",
      "id": "5c08ad59-73d3-4e6c-b36a-ab815220dfcc",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 188, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"565d6c4086f11c329febae96f747787c\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/control-para-porton-electrico-puerta-automatica-433mhz-2pcs/p/MLM44961855?pdp_filters=item_id:MLM2241247001#polycard_client=recommendations_pdp-pads-up&reco_backend=pdp_pads_up_merge_rars_v2_with_default&reco_client=pdp-pads-up&reco_item_pos=3&reco_backend_type=low_level&reco_id=bd5ee9f8-45fc-4cb2-b85d-fe2263c42303&wid=MLM2241247001&sid=recos&is_advertising=true&ad_domain=PDPDESKTOP_UP&ad_position=4&ad_click_id=YTZjMTUwODEtZmVjNy00YjExLTllMWItN2ZjMGEyMTc3Y2Uy",
      "check_count": "0"
    }
  },
  "url_pool:items:769f0075-fe62-4e40-ab22-556d28766622": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.795861+00:00",
      "id": "769f0075-fe62-4e40-ab22-556d28766622",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 547, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0069827505257e4fefdf7b7930cda01e\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1601968758-4-pares-de-almohadillas-para-airpods-pro-2-gen-ear-tips-_JM?searchVariation=175723758740&vip_filters=shipping%3Afulfillment&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=175723758740&position=11&search_layout=grid&type=item&tracking_id=e663e4ba-2f22-428f-8aa2-f72e0caf3bf5",
      "check_count": "0"
    }
  },
  "url_pool:items:ec0c67f2-8109-4c57-a4dd-c0f7a353d225": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.710663+00:00",
      "id": "ec0c67f2-8109-4c57-a4dd-c0f7a353d225",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 368, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"2ba7a507c5c32a63fb4c0341e5a3cdae\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3498097464-2-rieles-guia-del-piso-inferior-la-puerta-corrediza-granero-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=56&search_layout=grid&type=item&tracking_id=0907757a-9cd0-468b-bbf5-2b6bd26c503b",
      "check_count": "0"
    }
  },
  "url_pool:items:b3859bce-bc79-41ac-9ee4-1ce2b60bdc53": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.691771+00:00",
      "id": "b3859bce-bc79-41ac-9ee4-1ce2b60bdc53",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 328, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"1d57aae342862c38b04ec19becd92578\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3338498152-espatula-de-laboratorio-de-acero-inoxidable-12-unidades-_JM#polycard_client=recommendations_vip-pads-up&reco_backend=vip-pads-up-experimental-a_marketplace&reco_model=rk_ent_v2_retsys_ads&reco_client=vip-pads-up&reco_item_pos=0&reco_backend_type=low_level&reco_id=d903cf6f-00f0-4e37-9ef5-e5848afdf90f&is_advertising=true&ad_domain=VIPDESKTOP_UP&ad_position=1&ad_click_id=N2NkZjYzMGYtN2Q2OS00NDhmLTk4ZTQtNTdiMzJkYzZlYWJk",
      "check_count": "0"
    }
  },
  "url_pool:items:ad15455c-9dbb-4363-be0f-958b4c8a08f5": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.601114+00:00",
      "id": "ad15455c-9dbb-4363-be0f-958b4c8a08f5",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 138, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"74b45448a3d014b556c0f95d1127593e\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2144267485-tornillo-y-pijas-portaplacas-vehiculo-antirrobo-acero-inox-_JM?searchVariation=184916506615&vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&searchVariation=184916506615&position=8&search_layout=stack&type=item&tracking_id=64c47d8f-090d-4ce2-95c9-7b4fa1bd1a10",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:097b5f06-649c-412a-ba74-02611b7ca521": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:e2833013-b77d-451b-8791-bf31b08dfd5e": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.783872+00:00",
      "id": "e2833013-b77d-451b-8791-bf31b08dfd5e",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 522, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"86bd38ec5770f0fa1a46339eeb4dd048\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3280614920-100-piezas-sello-seguridad-perno-botella-clavo-para-trailer-_JM?searchVariation=181236492984&highlight=true&headerTopBrand=false#polycard_client=search-nordic&searchVariation=181236492984&position=30&search_layout=grid&type=item&tracking_id=3772aff3-53d1-4206-92c3-e39f5d83fa9e",
      "check_count": "0"
    }
  },
  "url_pool:items:8959a23b-079b-4238-97cd-b271b8096f60": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.727683+00:00",
      "id": "8959a23b-079b-4238-97cd-b271b8096f60",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 405, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"39e63f4365ff6d30d38984c106e693d2\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/ventilador-enfriador-de-celular-ventilador-rgb-cooler-equipo/up/MLMU3078693062#polycard_client=recommendations_pdp-v2p&reco_backend=ranker_retrieval_system_vpp_v2p_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=pdp-v2p&reco_item_pos=1&reco_backend_type=low_level&reco_id=e69e3792-7ca5-4b31-a0c1-9956e40df3ba&wid=MLM3600555862&sid=recos",
      "check_count": "0"
    }
  },
  "url_pool:items:112215ab-5783-48e8-b414-204d73d2b96b": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.737341+00:00",
      "id": "112215ab-5783-48e8-b414-204d73d2b96b",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 426, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"910da0533d052ab8169dae088087a008\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1349257178-caja-de-distribucion-ftth-para-6-fibras-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=2&reco_backend_type=low_level&reco_id=396f016a-3b9c-46ae-ace7-c5841da3001e",
      "check_count": "0"
    }
  },
  "url_pool:items:75720eed-a972-400b-82b1-f9e78be247b0": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.715270+00:00",
      "id": "75720eed-a972-400b-82b1-f9e78be247b0",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 378, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c2a1a05a92fcf800931a9027120f1a58\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3439875328-5-pcs-funda-protectora-de-cargador-de-cable-para-celular-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=8&search_layout=stack&type=item&tracking_id=80e5dd8f-3c1e-427a-bc93-b6160288569e&wid=MLM3439875328&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:ac4a9c08-24b0-4d18-98c8-5fa3ed5d6ace": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.584332+00:00",
      "id": "ac4a9c08-24b0-4d18-98c8-5fa3ed5d6ace",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 104, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"77b9fb8e6b6ca01b022eb5f1f60ac0a9\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1584227658-gancho-sujetador-de-bolsas-de-acero-inoxidable-_JM#reviews",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:7500d8c9-ab3c-4918-83a9-d65c77721c5f": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:f0c49aa5-f6a8-4da2-a830-1dbafdf68a28": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:dc8c4819-aae8-458b-99f6-105788f7b753": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:4ac3e4f6-e01e-4f6a-aebc-32e86cae556a": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:9d1cca60-59c3-46e2-bf7e-105d467f2ef9": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.746778+00:00",
      "id": "9d1cca60-59c3-46e2-bf7e-105d467f2ef9",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 444, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0554024bd2c21b6b41091dcfefd75f6b\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2855204834-hoja-fep-con-marco-para-anycubic-photon-mono-2-impresora-3d-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=3&reco_backend_type=low_level&reco_id=2d78f67e-273f-447f-adc6-5e4613a32757&wid=MLM2855204834&sid=recos",
      "check_count": "0"
    }
  },
  "url_pool:items:77c94d25-cbec-4bb4-ae0c-b9accd1d7d32": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.650904+00:00",
      "id": "77c94d25-cbec-4bb4-ae0c-b9accd1d7d32",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 240, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"73e0fc6a446ccf1d6728b61e1b58dfd3\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/alarma-motocicle-bloqueo-impermeable-freno-disco-de-candado-color-amarillo/p/MLM47336962?pdp_filters=shipping:fulfillment&pdp_filters=item_id:MLM3604087560#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:3592ddbd-c785-45c9-9343-45fb58a0fe00": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.690848+00:00",
      "id": "3592ddbd-c785-45c9-9343-45fb58a0fe00",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 326, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"89588c0431a06885b9b5aa561a9cbd20\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3318966118-cuchara-y-espatula-de-laboratorio-de-acero-inoxidable-10-un-_JM?searchVariation=181416289752&vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&searchVariation=181416289752&position=8&search_layout=grid&type=item&tracking_id=85be3b11-7494-42e3-a9dc-db9ed585d24d",
      "check_count": "0"
    }
  },
  "url_pool:items:713579a0-0b89-4e1a-bee7-faf3d055de92": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.682557+00:00",
      "id": "713579a0-0b89-4e1a-bee7-faf3d055de92",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 308, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"fea9286fd7471d3108630f422ef40c9b\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1376805949-12-tacos-futbol-plastico-8x11mm-4x14mm-llave-profesional-_JM#is_advertising=true&position=2&search_layout=grid&type=pad&tracking_id=75e7ca50-589e-4a24-9d73-daad4b2eb51d&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=2&ad_click_id=OTNhZmQ5N2EtMDE2ZS00ODZlLWJiNjUtOWM1MDU5ODU2MWVh",
      "check_count": "0"
    }
  },
  "url_pool:items:3f312e20-3a5d-4f24-9430-b12e0ba91a27": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.567748+00:00",
      "id": "3f312e20-3a5d-4f24-9430-b12e0ba91a27",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 71, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"7b4df388294489389d42105618327fff\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3390917718-collar-isabelino-ajustable-a-prueba-de-mordidas-pperro-gato-_JM?searchVariation=185357887681&vip_filters=shipping:fulfillment#searchVariation=185357887681&position=12&search_layout=stack&type=item&tracking_id=555db5db-8507-4e2c-907e-294e3d8bffe8",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:2f5f9ed1-4f9a-4614-8e41-5c026f2e680b": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:cc030b02-a6de-40e5-a783-fed80c6d92a3": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.817473+00:00",
      "id": "cc030b02-a6de-40e5-a783-fed80c6d92a3",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 592, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"a5b4c0595781ae02e1be770f6237355b\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2924036218-cubierta-antipolvo-para-cpu-para-computadora-impermeable-_JM?searchVariation=179986869024&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=179986869024&position=1&search_layout=grid&type=item&tracking_id=8cc73ee6-48d6-49b5-af2c-13e309f52c19",
      "check_count": "0"
    }
  },
  "url_pool:items:0ae18e29-2a73-4378-b354-04f8bc50abe1": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.789048+00:00",
      "id": "0ae18e29-2a73-4378-b354-04f8bc50abe1",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 533, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"edcc76d7ea03a74d751ca7641093e761\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3101260872-200-tira-ph-papel-medidor-medicion-reactivo-0-14-calibracion-_JM?vip_filters=shipping:fulfillment#position=9&search_layout=grid&type=item&tracking_id=77e53f3b-feaa-40f2-abfc-d192fff4d180",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:5182b76f-2a50-4779-952d-2252da0e704d": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:174000f6-a42a-4140-a336-ff35f715bbb4": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:2a7a1908-572a-48cf-b3e2-2447b8657f96": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.546111+00:00",
      "id": "2a7a1908-572a-48cf-b3e2-2447b8657f96",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 30, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"e09676931cfa88710241069c6286c3a3\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2080009919-60-pz-mini-cajas-con-ventana-para-pasteles-donas-regalos-pan-_JM?searchVariation=185064335371#polycard_client=search-nordic&searchVariation=185064335371&position=4&search_layout=stack&type=item&tracking_id=3540ae84-9b55-4e18-819c-cc018ddddde5",
      "check_count": "0"
    }
  },
  "url_pool:items:2d7c6ca6-9286-44b4-8e6c-daee9a2e7451": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.582102+00:00",
      "id": "2d7c6ca6-9286-44b4-8e6c-daee9a2e7451",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 100, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0c64570cdf5c93dba25e315252137c89\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3362858448-100-piezas-argollas-anillos-para-cortina-aros-de-barral-38mm-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=2&reco_backend_type=low_level&reco_id=bd12db3e-a9ab-409b-b880-34a17024b22d",
      "check_count": "0"
    }
  },
  "url_pool:items:b1525cf1-41bd-4003-aa01-81fbd40d2906": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.629441+00:00",
      "id": "b1525cf1-41bd-4003-aa01-81fbd40d2906",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 196, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"089e1d95b9d148afe008972843c1e2e6\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/control-remoto-clonador-de-puerta-de-garage-433-mhz/up/MLMU459910884#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=1&reco_backend_type=low_level&reco_id=d4dd4599-27a6-4433-9500-240e3bd52a91&wid=MLM1468350593&sid=recos",
      "check_count": "0"
    }
  },
  "url_pool:items:fb125950-ee2f-431f-8f12-3986d2122eca": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.823389+00:00",
      "id": "fb125950-ee2f-431f-8f12-3986d2122eca",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 605, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"ed5c0488530b674c63efe5fe0bd73ab6\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3278919734-polainas-viboreras-de-uso-rudo-contra-mordedura-de-serpiente-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_low_exposition%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=1&reco_backend_type=low_level&reco_id=48e1efed-0faa-44fb-9bd0-7e9f22e1fbd4",
      "check_count": "0"
    }
  },
  "url_pool:items:def89ea5-b220-4e4f-a855-00770ff878e8": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.706006+00:00",
      "id": "def89ea5-b220-4e4f-a855-00770ff878e8",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 359, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"d161c9e979a7bbb2ec8104c1f43bbd79\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2246077421-transportadora-funda-protectora-porta-case-labubu-con-cierre-_JM?searchVariation=187052230923#is_advertising=true&searchVariation=187052230923&position=2&search_layout=grid&type=pad&tracking_id=602f520e-4893-4255-a5fd-2f40519acb04&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=2&ad_click_id=M2RjYjQ1ZDUtNzg3My00NTZjLTkwMjUtZGMyMDEwNjliM2Iy",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:74b61dc4-df30-482c-af56-40ac34182e01": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:a12a878c-5b19-4b79-b238-0e14f158660c": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.733793+00:00",
      "id": "a12a878c-5b19-4b79-b238-0e14f158660c",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 418, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"2817e8de878fd2380bc6e5414faeb5cb\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3552495292-soporte-para-cepillo-de-dientes-con-dispensador-y-4-tazas-_JM#reviews",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:31a7dd40-9cf8-4100-9352-12c5a37b8049": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:99fc6484-5631-4a9c-a481-4c1a5c032756": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.694033+00:00",
      "id": "99fc6484-5631-4a9c-a481-4c1a5c032756",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 333, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"819e4dacafd1f3670de735dd5feab03d\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2627358120-espatula-de-laboratorio-de-acero-inoxidable-12-unidades-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=3&search_layout=grid&type=item&tracking_id=ffcacffc-4bf0-41ff-bb78-46f46ed634a6",
      "check_count": "0"
    }
  },
  "url_pool:items:5abd798a-ac98-4bf0-be8d-3927a8c1bd15": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.765778+00:00",
      "id": "5abd798a-ac98-4bf0-be8d-3927a8c1bd15",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 484, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"863de40fc2e224802ea29bda6dca1c00\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3512038064-100pcs-hisopos-de-esponja-limpieza-para-plotterimpresora-_JM?vip_filters=shipping%3Afulfillment&highlight=false&pdp_filters=shipping%3Afulfillment&headerTopBrand=false#polycard_client=search-nordic&position=2&search_layout=grid&type=item&tracking_id=9bba3049-565f-4dfe-b3c9-7195a525f4bc&wid=MLM3512038064&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:6172b2c1-9295-4748-aec4-032ebeaa74dc": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:6c62724a-144e-4bd6-ba40-d35b5a0a2fdb": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:9844beff-d39e-4b7a-bedd-61957203c57c": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.776642+00:00",
      "id": "9844beff-d39e-4b7a-bedd-61957203c57c",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 507, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"88a266151f2767215fabd1e5322446b9\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3362536774-interruptor-cuchillo-de-desconexion-doble-tiro-2polos-100a-_JM?variation=#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:1fe2c05e-ab41-41d4-a6cb-795215a777b5": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.748369+00:00",
      "id": "1fe2c05e-ab41-41d4-a6cb-795215a777b5",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 447, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"e491c156a49142c99ea969b6629918b0\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/almohadillas-esponjas-para-diadema-h600-h3405par/up/MLMU571395233?pdp_filters=shipping%3Afulfillment#polycard_client=search-nordic&searchVariation=MLMU571395233&wid=MLM3175917064&position=1&search_layout=grid&type=product&tracking_id=d358a234-4df3-4007-b090-e2cfa914804c&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:10adf58a-af02-4b59-9c73-f20d24db5f08": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.633168+00:00",
      "id": "10adf58a-af02-4b59-9c73-f20d24db5f08",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 203, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c58f7c520bcb499cd9cdc6e3f21c8a2b\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2000917483-suspension-mtb-2627529-pulgadas-horquilla-bloqueo-100mm-_JM?searchVariation=181272401717&vip_filters=shipping%3Afulfillment#reviews",
      "check_count": "0"
    }
  },
  "url_pool:items:48ca29dc-a1f3-4bb8-8361-ca9e19f29a24": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.698305+00:00",
      "id": "48ca29dc-a1f3-4bb8-8361-ca9e19f29a24",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 342, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"bace4fe963254439eb04f8d2834ab721\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-1919828993-termometro-digital-cocina-para-refrigerador-con-alarma-_JM?searchVariation=179060672293#reviews",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:a3955a25-b68c-4490-adf5-c17eec2ff9e0": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:0cb80f5d-e7ae-440c-b19f-b046f5c6db90": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.757496+00:00",
      "id": "0cb80f5d-e7ae-440c-b19f-b046f5c6db90",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 466, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"b36cc8d2fb47f9cd4de7aa995a1ca4fa\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3502342970-cubierta-funda-para-teclado-de-piano-electrico-de-88-teclas-_JM#polycard_client=search-nordic&position=37&search_layout=stack&type=item&tracking_id=e96ea788-d0a9-420e-9787-57c1d7f83500&wid=MLM3502342970&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:10217c3a-db21-488e-9990-4e4b75628aa6": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.762987+00:00",
      "id": "10217c3a-db21-488e-9990-4e4b75628aa6",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 478, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0be38567442df06524bef71c741e110c\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3085085074-set-de-4-piezas-sujetadores-magneticos-para-cortinas-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=1&reco_backend_type=low_level&reco_id=391bb964-0762-48e0-9783-0c4d09f541e2",
      "check_count": "0"
    }
  },
  "url_pool:items:27e67b99-30c5-4552-bb59-1059f79a700e": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.619972+00:00",
      "id": "27e67b99-30c5-4552-bb59-1059f79a700e",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 176, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"f2dcd51391eb5f9cc9db4d31a92f5852\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3504659788-cortineros-de-metal-ajustable-barra-de-cortina-a-maximo-42m-_JM?searchVariation=186472421169#polycard_client=search-nordic&searchVariation=186472421169&position=38&search_layout=grid&type=item&tracking_id=dadeb027-acf9-41d2-88ab-e03789b13a35",
      "check_count": "0"
    }
  },
  "url_pool:items:229c65fb-86d2-4e63-b561-2c52695a963a": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.543832+00:00",
      "id": "229c65fb-86d2-4e63-b561-2c52695a963a",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 26, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"732a075c945f9d76979c52a6c63cde1e\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3361336010-correas-moviles-correas-para-levantar-mover-muebles-_JM?searchVariation=181645397896#polycard_client=search-nordic&searchVariation=181645397896&position=16&search_layout=grid&type=item&tracking_id=7c43d25f-8dfc-4e91-8d9f-932ca7e8fc81",
      "check_count": "0"
    }
  },
  "url_pool:items:d011ffb6-9cc2-4e11-9c0a-da2cfa9c00e5": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.823863+00:00",
      "id": "d011ffb6-9cc2-4e11-9c0a-da2cfa9c00e5",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 606, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"d0cfdd4bdf8ce70e6c7b8abf13ec49b8\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3348207468-polainas-de-uso-rudo-exterior-contra-mordedura-de-serpiente-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_low_exposition%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=48e1efed-0faa-44fb-9bd0-7e9f22e1fbd4",
      "check_count": "0"
    }
  },
  "url_pool:items:3c88372d-0a83-4bc8-9590-25dc9db11d59": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.610684+00:00",
      "id": "3c88372d-0a83-4bc8-9590-25dc9db11d59",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 157, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"ccb35613199bad19fc80fee9baa13808\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3374844242-planchas-a-prueba-de-insonorizante-para-auto-camion-2mm-6pcs-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=12&search_layout=stack&type=item&tracking_id=cf31ce72-40c3-4960-98e0-c71abc49f87f",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:53e79342-c8e3-4923-ad51-a7c327e3d23c": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:cc9ade3d-af1a-447a-8c59-89b09cae4066": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:d695b964-48ea-43ce-b065-c8ba3d7d745a": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.803608+00:00",
      "id": "d695b964-48ea-43ce-b065-c8ba3d7d745a",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 563, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"79336e605b9b3c71956ab7a3774d247f\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2222208343-soportes-stand-para-teclado-segundo-nivel-ajustable-de-metal-_JM?vip_filters=shipping%3Afulfillment&highlight=false&pdp_filters=shipping%3Afulfillment&headerTopBrand=false#polycard_client=search-nordic&position=7&search_layout=grid&type=item&tracking_id=859ec86f-42d0-48be-aed3-dfb7e0942a2b",
      "check_count": "0"
    }
  },
  "url_pool:items:61990b09-c35e-4235-a899-c8bb5c32ab30": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.617592+00:00",
      "id": "61990b09-c35e-4235-a899-c8bb5c32ab30",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 171, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"2230b0973f8437b86fe7e77bb5ad27a3\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2213609999-1-pz-persiana-enrollable-translucida-para-ventana-60x200cm-_JM?searchVariation=182642830052&vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&searchVariation=182642830052&position=18&search_layout=grid&type=item&tracking_id=eb4e6dcf-c229-4ba2-acf2-8b1b4e208bc1",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:d4087ef7-f4ec-41aa-bba4-d17e6545f51d": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:6c0a8f26-0ba0-4a4a-92c2-697ea04cc113": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.803170+00:00",
      "id": "6c0a8f26-0ba0-4a4a-92c2-697ea04cc113",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 562, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"fcd64efcea67c8ce9c0c8831332a7d95\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/cuerdas-guitarra-acustica-aw432-12-53-cobre-para-alice/p/MLM45630036?pdp_filters=seller_id%3A1329852069&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=MLM45630036&wid=MLM3563166214&position=1&search_layout=stack&type=product&tracking_id=9098b498-9cb7-4662-8e2a-a7185e65ef22&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:49a162c6-bf56-4a77-8e2b-9df98da8703d": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:1a147a1c-9e2a-4bb7-8c94-8e2ec4f5e6d7": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.831568+00:00",
      "id": "1a147a1c-9e2a-4bb7-8c94-8e2ec4f5e6d7",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 624, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"ee4a1edc08d2d3a6f57e6c5c24d82e01\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/herramienta-para-inserto-de-olivas-frenos-hidraulicos-bici/up/MLMU452015993?pdp_filters=shipping%3Afulfillment&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=MLMU452015993&wid=MLM3309122414&position=1&search_layout=grid&type=product&tracking_id=da2176e7-88e8-4d2e-8572-fbe75436f514&sid=search",
      "check_count": "0"
    }
  },
  "url_pool:items:8546a6c8-6111-47dc-9b00-d5024b6093a4": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.597342+00:00",
      "id": "8546a6c8-6111-47dc-9b00-d5024b6093a4",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 130, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"a4cd8c25a275c90cbaa1b5305f1ead62\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2207780075-fotocelda-110v-240v-1300w-para-exterior-incluye-base-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=16&search_layout=grid&type=item&tracking_id=800e145d-9db7-425d-b280-9c4681808d90&wid=MLM2207780075&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:d4c7ec19-8921-454d-a4f7-a69fefafb033": {
    "type": "set",
    "data": [
      "fe2ae36c-cffa-460e-942a-4c95a78122fe",
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "3db0e2ae-6cbf-4ca0-ba72-39342742ac2c"
    ]
  },
  "monitoring_tasks:url_tasks:7d40ab7d-f820-4c23-a0a6-a06ece6919c9": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:7170c8a4-4ed5-41f6-90e8-d6bd7f49f36f": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.791854+00:00",
      "id": "7170c8a4-4ed5-41f6-90e8-d6bd7f49f36f",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 539, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"f384490309396bf96aceb209c47f8f97\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/soporte-rack-portabicicletas-para-auto-allen-sports/p/MLM27500548?pdp_filters=seller_id%3A89983693&highlight=false&headerTopBrand=false#reviews",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:b48ce372-a417-4d0c-8cba-a097cc4b44ca": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:e3d1e561-9a0f-4638-bd23-997dedb96f14": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:bb7b9c9f-ff42-4dc1-a314-18c857a7e6b2": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:6585916b-97ad-4bd2-8943-419685dc6aec": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.793735+00:00",
      "id": "6585916b-97ad-4bd2-8943-419685dc6aec",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 543, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"5ac4d949a99116180c0c035e15974436\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2283187751-lampara-medica-portatil-recargable-para-diagnostico-3colores-_JM#polycard_client=recommendations_vip-pads-up&reco_backend=vip-pads-up-experimental-a_marketplace&reco_model=rk_ent_v2_retsys_ads&reco_client=vip-pads-up&reco_item_pos=20&reco_backend_type=low_level&reco_id=6bff8995-4427-4974-8c72-e876a857e9d4&is_advertising=true&ad_domain=VIPDESKTOP_UP&ad_position=21&ad_click_id=ZmUxZjVmOGEtYWI5MC00Nzc0LWE3NzQtNjcyOWNjYjJhN2Uy",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:eaf662f9-d8ce-473d-a71c-96b2dae7e434": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:a2d8f258-b68a-41b7-a779-8dc15d9d454c": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:status:active": {
    "type": "set",
    "data": [
      "e833c635-0c93-4fe9-a449-bf62303a6853",
      "672d77c7-7dc8-4c1d-8710-437bd4a6f250",
      "dc13ccf3-9492-479d-b3cd-b4304d07303f",
      "4ab20e64-b3df-43f7-9470-eb3da2634b2f",
      "9f36776c-c551-4de3-ba42-9a87ad921ea3",
      "7f914cf4-e837-44ca-afb8-1d00dc75aa3e",
      "92f2bf3e-a68f-4a3f-b8c3-a70d5eb650f5",
      "6954c748-5393-402c-946e-ada4b90fa6ee",
      "62bcf9b3-7c32-4b47-9f12-31528c6467f7",
      "7e219924-e4ce-40dc-b731-c0410d3d516c",
      "cc9ade3d-af1a-447a-8c59-89b09cae4066",
      "7366b5ba-e2e0-42d8-8ded-8cc8abe62903",
      "bb7b9c9f-ff42-4dc1-a314-18c857a7e6b2",
      "a66f75f4-7fe4-401a-9162-cdebca513f1b",
      "d0973e89-135a-4616-8077-5f4ae3a74cca",
      "9c1fdfe9-6ff1-4576-856c-7cec5f547761",
      "f3c4b350-aaa9-4aab-8718-7caa3a87b172",
      "2a4e82c0-7d1e-4fd8-853a-1f778fc29df3",
      "946a4899-0500-41c9-894e-97bf28f7556d",
      "dd1cec1e-66e0-47e0-b01c-155cd21d67a9",
      "587bb8f9-1aa5-436d-9147-5a76bde6a287",
      "e54b6763-6827-4ee8-bddb-387e14891811",
      "baefd8e7-d2f1-47bf-abc9-ad39b634a1c3",
      "747f5827-15bb-467c-9d99-2f2d584f3d94",
      "94669a74-8a10-4ae5-b71e-a67878e58cc4",
      "f8ca87a3-b281-4bfb-a00a-383c11af844c",
      "d133972a-69bb-4191-b66b-4714b241e775",
      "78da0a7a-280d-4b28-b3da-7e4afe71c3b2",
      "9f8b4fbc-cd5b-4444-ac41-6a20ec08c486",
      "31a7dd40-9cf8-4100-9352-12c5a37b8049",
      "6e8a7df6-037b-4d13-824e-1924e7bdf855",
      "83b6d665-d6ad-44d5-9d9d-328c176960e0",
      "a0fc875e-7e7d-4678-af41-2073f288977b",
      "614cc2a4-70c3-448e-8827-a715aba0feee",
      "3a86beb5-c897-4bb0-8aa4-9c60b2019726",
      "1d534d57-dfeb-422d-950d-76c6dc54d3a2",
      "69f0cdd1-79df-406e-9051-67d80b1c817e",
      "c3b9c5a3-6ba3-4331-b7e2-c0f9bda675f0",
      "5124a881-0d79-459b-805c-9fa63b2e9f45",
      "9f81d14a-8d63-414d-8ddb-8940ea835449",
      "fee13e6f-2529-45eb-9b40-baf1a68f49d2",
      "13e579e9-2882-4a9a-ba0d-cb8b228488d4",
      "c0c184cc-e57f-49b4-9182-897062359fc3",
      "6050121f-9e34-4e27-bbf6-9dde6c437daf",
      "d5200ba0-a406-4332-9cae-8c794cd513a0",
      "eac7d796-f853-44d5-9ee2-8bb9c0d36669",
      "90f89afe-2921-4e63-831a-916ff68cbae2",
      "f3463c5d-f0ce-43ba-9411-ab60062b9e56",
      "3319d7b1-596e-4f13-9fde-b44ee9100a6a",
      "4b7f2c68-2c20-4df8-90c9-2bdacbbdddb1",
      "b2c1c8a9-f064-46cf-8563-98ac9afdd030",
      "85b202cc-f91f-452e-9982-5a6f2605f08d",
      "4bbd9d9f-e158-496f-a50f-d07abb1a42d6",
      "76010da4-3531-44d8-b4f6-23fd620a29b8",
      "27d67ad0-f4df-4b34-bfbd-b226fd36c402",
      "175a0357-0bc6-4f76-b036-d3ffe74f7efc",
      "fc45789c-49a4-4885-8678-c743a3a18125",
      "b4252436-5740-406f-ae78-68b37c9fd72b",
      "2ba3e535-39ff-41d6-85b4-94bad37f8039",
      "0b514166-5712-47ee-9a66-f2d001b0d211",
      "491227af-c24d-4903-a2f7-0ff7fe26d2d6",
      "07b73ebb-e96e-431b-a1f7-bab5e0b670b0",
      "da033aaa-4618-4606-8db2-25587d585095",
      "e35cd4ef-25fa-4359-9ae9-d962bfc584e0",
      "fb7f9c23-ff00-4538-a43b-3ba89d3ccaca",
      "7f86fd85-bdaf-4d1f-a846-758fd58729cb",
      "817df345-6baf-4af9-9342-698994f0f4a2",
      "ddb4c850-75b8-4131-bd8c-db27b174fc62",
      "78071445-fcf6-4a15-9d2b-5d25b52b5901",
      "99d6725c-773e-4446-9455-f2afdd3870d5",
      "67f2dce2-b299-40da-9b31-8e8b1198e275",
      "91722c88-b708-4e0a-8287-99c336d1fc6a",
      "808b5360-371d-4a2f-a9af-fd6b124bb5fb",
      "2184140b-1263-4060-95f3-c4516113f316",
      "396614ca-77b3-4e63-8e68-5aac4c162797",
      "75720eed-a972-400b-82b1-f9e78be247b0",
      "be94cc69-9b22-4d27-bb30-92790c93b091",
      "c2e56158-bb88-4dc8-b908-1f6e32cfe97e",
      "003fd43e-aa38-4caf-abd2-05fd7982738b",
      "c094352a-fdc9-4931-bc13-c7ee0fbeba2d",
      "7a1c129a-673b-4789-bad7-985afe7c6c6b",
      "7f23bdda-644d-4060-b16a-7a2b0a0d25e4",
      "e718534f-e9ec-4afc-8366-2ea16ce314c1",
      "db5ccca2-7490-4edd-83de-c46654a1b49f",
      "150d5b16-4c68-4647-a170-443ce4275af6",
      "e34b84ce-163d-446d-aff4-c5b5411f2133",
      "67cb07d6-e281-4a70-93f5-3179137f8f0d",
      "4deedd1a-352d-4c04-9250-9e79096a7d63",
      "4bfbb5d9-b000-4f0e-aace-39775f648f5a",
      "cfc9d3df-2578-44bd-97ce-1e64033b7436",
      "92d475e7-ca9e-4fc2-83da-b390a35f7d76",
      "2f389a03-1d8f-437f-8d24-f4fd6c0202ca",
      "d51abf9d-7c50-4141-8a46-5809a3203ac3",
      "8dd4e411-6aa0-46c9-af64-d1ea09622044",
      "e48c6232-115e-481f-82d2-5225eab0326a",
      "0358c823-9779-4246-b040-4a8165998e47",
      "2e883a20-6765-4b4f-a662-9ccf12101b57",
      "021f523d-783c-4fbc-a96b-986625c81ad1",
      "cb39ec6c-c5b4-4082-820f-9cf96415fb81",
      "fc54d526-a6c3-48c6-ba99-78ebc147a27e",
      "85fd341d-6dd1-41a5-96a2-429fbb9d1458",
      "bcaa757c-50cb-47e9-8b5c-8d85188c10eb",
      "83a913d0-9c84-4112-989e-4e0bc38fa8ca",
      "2e3722ac-8e78-4887-a6ba-106696cae09d",
      "e1d03d48-1569-49d5-9590-9922371f3654",
      "5abd798a-ac98-4bf0-be8d-3927a8c1bd15",
      "bfc53740-e3c7-4c4b-b272-8ff4bfe2429a",
      "e48fabc0-ed8a-43a7-9d0b-08260b7acaff",
      "b49c8d27-2535-4066-a310-0c27bb9cacd1",
      "23640fa5-2b88-4b48-a87f-9911e09e643c",
      "b5b5a1c4-ed12-4fe7-b68c-72491d2f4bef",
      "49a162c6-bf56-4a77-8e2b-9df98da8703d",
      "2b41ba71-bfcc-43ab-9225-9bcb7bbd0e0e",
      "2bae22bf-dc5d-4690-9ca9-1ead28491117",
      "dc64784f-2b91-4d23-a9c4-0ed97b1a10f2",
      "e7ab2145-12f7-4948-a152-f9b8417fdf11",
      "7c38233f-bee8-4634-b523-66c82ed75418",
      "4578872b-83c8-40bd-b181-d8c598a8862f",
      "593dac49-9fc8-4857-af4e-fab2d7eadf79",
      "95a50c5a-386a-4221-ba59-91dc9c583dce",
      "37e31b0f-2ebb-4b1c-8b20-ff6a610d1b59",
      "c25f0f31-4e0c-4f75-a952-f1ede03d776e",
      "1725793c-0ade-4693-a282-6f1f6713cb6c",
      "a12a878c-5b19-4b79-b238-0e14f158660c",
      "50e72435-a3a4-4e1e-8ae0-0edc2f4f206a",
      "0efe6597-0854-41a8-a84c-9247b74343ac",
      "85c33226-d782-4dd8-bad8-807f97dc48b1",
      "ea39299c-6294-44dc-aa3e-09f64a8fcd34",
      "7d40ab7d-f820-4c23-a0a6-a06ece6919c9",
      "362c3a06-5c79-48a4-92a9-000f5ab018a1",
      "f582343e-377b-413c-a376-14e03e7003e0",
      "2a43aeac-316b-4d0c-a401-80f667f1d7fe",
      "beccf5c5-7bf3-4987-8f3d-eb83abcc77c4",
      "d770c68b-edea-44f3-842a-2d032979efaa",
      "655ad75b-b4dd-44a8-8700-fc46b71bbefc",
      "f8dba5a2-4dae-432a-bbbf-f7de183c8809",
      "b03b6052-3fec-4092-84fc-80d8c042b568",
      "27d0da44-6ebf-4b69-b7ec-14dc11789ad0",
      "cc60737d-4050-46c1-ad07-4d5d77b5df48",
      "dee0c6a6-2da1-4d1d-b8e5-f239bbb54736",
      "c43c7297-a92d-4063-9ed4-37dda9bdbdf4",
      "ec4b624d-5fcf-4ab1-b0d5-e573594590bc",
      "ea2201c4-08ee-402d-a733-715985d1d216",
      "db35b2ba-318e-4d2f-af2e-7bd96caefda9",
      "23c02e6c-ce66-465c-92c8-9caa45ac3acd",
      "563abb7f-a686-406b-9a99-e743bf814462",
      "e13cdb35-b3d9-400b-90e6-50c8594f8f96",
      "32066db5-aca2-4ed5-9d5e-f955de0d0919",
      "2a9bec61-0fa0-41ea-ac02-be4f71825ecf",
      "fda847dc-1a65-4848-9490-e493e2b85f8d",
      "441852f7-4b34-4b98-9fca-e930515128d4",
      "34920b9a-f9fb-445a-9fc6-90f0a36f539d",
      "a41b2f12-a0c5-4f0b-8329-ecb854f6ac59",
      "447812a5-7bd5-4d39-ad62-e179b4967cbd",
      "fd7f93e3-ff43-4593-968a-46df66503ecd",
      "1ef50d6a-71b1-47d1-af07-2488429cf6dc",
      "cc62addf-670b-4967-b8de-0c110cc2e846",
      "656ff6f1-e286-4e12-a1a6-4397b49b238f",
      "48bcbd5a-9f6f-4f8c-a543-b58c060a4100",
      "4a84791d-b7f7-4525-8fe8-d5c184786aa8",
      "37412ee5-7329-44aa-b805-facfb0462b04",
      "d011ffb6-9cc2-4e11-9c0a-da2cfa9c00e5",
      "bed76989-3b5f-4a36-9278-66be1056518b",
      "1c2dd143-fd4d-438e-8136-ce878702ace0",
      "dc8c4819-aae8-458b-99f6-105788f7b753",
      "c6f2e0a1-aae4-4c73-b800-866d44de657b",
      "77c94d25-cbec-4bb4-ae0c-b9accd1d7d32",
      "5bc3974d-77ab-407f-8b20-799cfb13a2cc",
      "b306c0b0-6b7e-4d0b-a6e8-e0ebc6d626bf",
      "4e9d3d17-f40d-44a0-972d-45edd4473260",
      "21ae10a6-c196-4185-9f0e-2e5af7bedc1d",
      "5058c048-4260-43d6-ba17-8c6e7dc7e482",
      "18668208-d8f9-42b6-b920-992d873e3f25",
      "76f87a84-f67f-48e7-a19d-dfb174039b46",
      "86b1d7f6-1fa1-4f3a-a6e2-7e0e3623f227",
      "32c9fe79-d001-4b79-a9c6-b76bac673615",
      "3262a673-920f-4a74-ac37-0b1cd4a7a7c0",
      "df6688cb-0dec-43ca-a9c0-5388ff0b02e7",
      "84e206f4-c0d2-4115-8c20-c9a1a6244e59",
      "8a8ffa9e-1edc-4eeb-8138-183640b579fe",
      "d30ca988-38c3-46f4-b70d-8966e83db91e",
      "fb70f5b8-a6b8-4e3d-8a3e-0ade719ff2fa",
      "4a5643f0-594c-4276-80e3-d33202b61c3e",
      "87e3a2b5-e6c7-4e0c-b178-d6fd67a27e61",
      "18808850-89b6-4803-975a-df16bfc277f9",
      "eed66063-7c2c-452c-9b26-6113bbd29d8c",
      "b256d4dc-37c4-4073-a039-02c86de3569f",
      "bd7b21b4-9110-4a6f-86eb-b280256387d2",
      "b284c2fa-02e2-45e9-9001-2be1b69f4329",
      "494b953d-8586-4665-b4a0-003ae66f9207",
      "53241595-0209-4dde-8d5a-7b4bdba1d327",
      "450ba497-c73a-4eb9-a598-f14de84cb3bb",
      "4ae4f60d-51ac-44d1-a2e3-e806dc5370fb",
      "9c27539a-d518-44a2-bb61-cf8724959e2f",
      "ca52f635-68e8-4123-bdc1-8b23e446971d",
      "a85dcdd2-6449-4b57-8f5c-c437f85920b7",
      "58ba2d6b-918d-4d7b-bfb3-3ffaf76b23b8",
      "e3d1e561-9a0f-4638-bd23-997dedb96f14",
      "8150c675-9fad-4442-904b-0a3e51d82473",
      "b8b60e9d-5101-478a-9ad6-445f19a787a4",
      "8833da2a-5fa3-4a60-94c4-0abd3e0129c5",
      "7500d8c9-ab3c-4918-83a9-d65c77721c5f",
      "b43150e6-0fd9-4872-b5e6-c681a589adb9",
      "ad15455c-9dbb-4363-be0f-958b4c8a08f5",
      "8bbd3487-fd1a-4701-a58f-97e2f97c6d3a",
      "8546a6c8-6111-47dc-9b00-d5024b6093a4",
      "b3ee5359-64c1-40a5-b4e7-43f17556fcd5",
      "b11f1718-63c6-4636-9063-db66e39ac11c",
      "112215ab-5783-48e8-b414-204d73d2b96b",
      "a0ddeb23-4148-4f4e-8610-4b9a9acaaa5b",
      "ada6d385-7a7f-4be6-a699-91663854a6f9",
      "097b5f06-649c-412a-ba74-02611b7ca521",
      "78aa59cb-824f-4f35-8ac7-310b82b487f2",
      "2f5f9ed1-4f9a-4614-8e41-5c026f2e680b",
      "41a1cb5e-c276-4015-883b-54c46218ae51",
      "1fe9f8bc-a3d4-4eac-8dca-018d90d7b2e1",
      "f073574b-e1aa-4cd0-892f-17edd275dbd9",
      "4d5376d7-a468-46f0-b06a-c381ad499e34",
      "f1e27709-e2ab-4861-b221-a16f1837f6ae",
      "2a84f1d3-a846-4762-aea2-d8ae92945129",
      "250db918-993a-46a0-9ae9-84f3402a470b",
      "8477a250-9ac7-4e92-8ebf-60f8694c5648",
      "a6c516f2-4701-4f56-b2f8-617c8823ed6a",
      "99a907a6-f305-457a-9dc8-375f504ee9fd",
      "1fe2c05e-ab41-41d4-a6cb-795215a777b5",
      "0c8508ec-3ff2-4ecd-b156-1900f78b28d0",
      "d3412f83-615d-48d2-bcce-bef57e36bedd",
      "713579a0-0b89-4e1a-bee7-faf3d055de92",
      "80de1e41-0bb8-4d10-8c21-849e75725309",
      "6c380a90-94a1-4774-90e2-9339f35f98db",
      "92ee341d-cf1c-4eec-ac61-579715c11396",
      "fe1dec72-facc-4367-9069-5691a7f2c231",
      "e276b13a-c6b1-4bb8-94aa-3a6645344bcb",
      "2b0af047-c5a4-48b9-91ec-16ff789d57bf",
      "b3a2425a-2d04-4764-9fbc-9d554cc10e42",
      "4fe6af19-72be-4b47-88b7-a5e9c28d6993",
      "14ac291d-162c-4f98-bfcf-6ee2c59965b1",
      "64941fe1-5ea8-413d-bf77-56518c0e7a0c",
      "ae6db036-8cd1-4e1f-9fc3-119cc0b64dc6",
      "a056ba7c-3e6a-4be9-971f-388480113059",
      "76033dcb-af11-4e3c-9086-88b4e73c8221",
      "5f656686-7f35-489b-bfc3-8fa1a725eaa6",
      "970e8c15-4d52-439d-a3ed-8a2941a98c0a",
      "2a7a1908-572a-48cf-b3e2-2447b8657f96",
      "e89a23e3-0eb0-4970-a308-aff11fb86939",
      "93c5df07-c650-4dc6-850a-47b04fc8762c",
      "5d6c1a6a-7b21-4765-b359-288fb8bf2dba",
      "485d9bdb-2208-49f3-a6c7-3c6dba55b155",
      "6425594d-edb6-42b8-9979-a8689a216a15",
      "ea3013ee-9975-4283-b16f-f12515861678",
      "3304ceb2-51c6-4afa-b80e-f3af3f1bb25f",
      "beb4587f-3fcd-4320-abb9-698e4f9af7eb",
      "48a42c61-9ea6-4989-8b7c-5d41f4d347a9",
      "d4986f4b-5629-474f-8a0f-79270de19844",
      "6172b2c1-9295-4748-aec4-032ebeaa74dc",
      "4352328a-72e6-4cc6-837b-92f11c9ab822",
      "43e01a7f-04dd-413c-a215-73d7958fc50a",
      "e1fb7c40-da23-4e0f-8fb1-80f399f0b819",
      "4ac3e4f6-e01e-4f6a-aebc-32e86cae556a",
      "245810d3-47ac-46d1-b274-9199f4eae7ee",
      "c53311ef-32fc-41d6-9f79-7352515045f8",
      "92b89e38-c393-4ba7-9e21-b865c91c4694",
      "ed499675-ed86-4ec1-9317-d208eaa4cc8c",
      "eeef7e56-ef16-4e6b-841d-ece03435d0e3",
      "76f04c9f-9994-44f3-8d37-1985a6bb51f8",
      "74ee89e7-52f4-45af-9bb5-a7a4dec8af5f",
      "1bb39cb3-444b-4a07-9fc3-b9a06ded1e19",
      "887c95f9-8155-4f58-ae6f-48aa35073082",
      "b64a58ff-f2b4-4a64-84d7-6919342d3719",
      "4a99f47e-30ac-4b64-b347-7172857d4b4e",
      "e04ed9ff-d9c3-43c5-8d6c-413d47df6d86",
      "47411789-0322-4677-bbf5-56d3e2795fa0",
      "1dce8086-458d-4bdd-8216-1f72422c551e",
      "b1525cf1-41bd-4003-aa01-81fbd40d2906",
      "80ed09af-fdeb-4fc3-b5f9-e06a6306341e",
      "6c499066-62d3-4938-b5bd-6777598cd51b",
      "b3859bce-bc79-41ac-9ee4-1ce2b60bdc53",
      "c5b2d1a3-dc30-4299-8a1d-4cfe084d4173",
      "53e79342-c8e3-4923-ad51-a7c327e3d23c",
      "ccda614e-6e00-4283-9856-c5fe22b42be9",
      "7d9bdebc-7bb2-4916-ae7e-abeca44992e5",
      "769f0075-fe62-4e40-ab22-556d28766622",
      "d3a0cf80-a058-4eb9-abb8-a978b90514c6",
      "d21561ab-0886-4867-a642-b3c09651667b",
      "8959a23b-079b-4238-97cd-b271b8096f60",
      "379c0f47-ce7c-4fd8-a7d1-719f2184d172",
      "37c9ac1e-4dda-4103-bb32-93645dc08254",
      "6aa101f6-7219-4fc2-a1c1-06464cdb73f8",
      "8c53710b-1779-409a-b64f-c69e5c5e6382",
      "5a626b7d-838d-4773-8dee-46134bb2f815",
      "2f88d18d-29a4-41eb-9ed2-565d3e7d18e0",
      "df67a925-ba6b-4737-bdba-6e6622a33547",
      "91505854-9c5c-4623-b5e2-a0dae89f386b",
      "55de7260-6716-4cc0-9e0d-5a1da99b4c39",
      "d99a2f66-47dd-42d7-8f2a-fbb6c6c73032",
      "1a147a1c-9e2a-4bb7-8c94-8e2ec4f5e6d7",
      "3bb7bcb7-0a95-4558-9510-f652a93bb0e5",
      "b7675573-37da-48ce-9e26-92fd94e73b8d",
      "3bd70939-1fbd-45fe-b690-abaea829890b",
      "d4a46db9-4bce-42ff-9dd1-cb9aee62788e",
      "690b4c42-1d0b-4c47-9cba-6abed88e2c9b",
      "2603397b-4c6c-4503-b41e-3d3eb5c8beb7",
      "1d10e5a4-e461-48b4-a55f-9309ea53d707",
      "0874b960-b46a-4352-b195-cda7ea6ff4ba",
      "b99a711c-aae5-4084-b6d6-051f08c22fc6",
      "30eb62b8-3ebc-4081-a081-35b4badbc234",
      "87e04b31-0d42-469e-bd2f-c342f6b8a8df",
      "f36a5bfc-d71a-46ee-bfa8-9d8b4525afaf",
      "40973603-486b-44a6-9864-b5a28dc3e59f",
      "c2f05330-e0e6-4972-9254-ffcd7182748e",
      "9d1cca60-59c3-46e2-bf7e-105d467f2ef9",
      "266cdacc-5b7e-4a61-b954-c7b7a54a2be8",
      "11b00297-d9c3-45d0-87f5-0e3e3bc497fa",
      "6c0a8f26-0ba0-4a4a-92c2-697ea04cc113",
      "9cead1f4-cf23-4b8a-9827-b0eb469bd39a",
      "566fbb06-def2-4437-b208-c9508612d609",
      "bec0ce6b-acfa-4457-83e0-3711968d4937",
      "de49f594-00a7-437c-9582-f2f1dbf1cae5",
      "de03c240-3663-4d8d-af41-0547fd53b505",
      "521cd3c2-129c-46ed-b357-1f705273beeb",
      "9dcf6980-3315-4a8b-9cec-c82b07109094",
      "186d3279-581d-40e6-9f3c-2be337b967d9",
      "acac48d5-2a55-438a-ab87-3d184a1b1de1",
      "13debc83-447d-4529-a8e9-fd5a3120989e",
      "d98fb04a-3cad-4fd8-ba19-196f588747bd",
      "10adf58a-af02-4b59-9c73-f20d24db5f08",
      "d4c7ec19-8921-454d-a4f7-a69fefafb033",
      "b1d8fa13-198d-4048-ab95-e954e5883b9f",
      "b351aa92-6895-4a37-b35c-fb19698254f2",
      "4d491d36-0f5e-4f8a-86b8-9d2b3f56962a",
      "6686e587-0b5f-40af-94d8-9bd1cfe70bfa",
      "64b7b83f-4a30-4de9-8c75-b2b7d892f747",
      "59c975ae-ab92-40a0-a38e-8ed1dd4e844b",
      "5394e668-e39b-45e7-918d-3d39959fe2f8",
      "a630ea28-5582-4d27-b0b5-262d3b754ee6",
      "bb2d7309-05c2-4fb1-aa36-e5aefeb52a86",
      "b880dc5d-6b4b-4f4a-bbe6-1b5108254e67",
      "37a2df8a-9da5-4d76-a860-b831578275d0",
      "cb84b643-30cc-4d58-ac14-64d87f2626c5",
      "3a43bdbd-b07d-4377-a5db-f6976011226e",
      "dbb71df3-acac-4626-96ce-8aea475f926d",
      "b3abb05c-29c5-474d-bac1-b7e9b91374bb",
      "0ac34e4f-01f4-4f94-882d-e27acb6ca5f5",
      "aa7a2304-3507-4663-af0d-80914cded17d",
      "e77560a1-ba73-4b0a-876b-43e941a768a4",
      "999e2e5c-329a-4dcd-818a-24679f0d6fca",
      "84d2c3b4-1f55-402a-9603-2603fb3471d0",
      "0cb80f5d-e7ae-440c-b19f-b046f5c6db90",
      "b6a1a08a-ad8e-4879-a7c0-c3a66d168eea",
      "906f9445-d1ff-4600-a101-f3c3c72fe902",
      "81645980-b570-4649-a6f2-db38ab0a4135",
      "62c6823a-e5e4-458f-b332-ca0f23c0f3ba",
      "20815335-6862-418c-ae9d-2d8f13704038",
      "27bef139-f150-419a-b656-a98d35f5317c",
      "4b1312e5-5e02-415b-b5c4-9cf1eb439e95",
      "3e2ca227-015a-4919-bbb2-7be5de9af33a",
      "054c7612-fb88-421f-a74e-a38babb9d0d5",
      "6c62724a-144e-4bd6-ba40-d35b5a0a2fdb",
      "0c1e60e1-d226-4421-887d-d1a0cea38688",
      "926a5f46-2725-4f96-b6c9-adf6d67faed8",
      "d6311889-591c-458f-8cf4-614868ea5329",
      "ff9db766-bdd9-48e7-ae04-cf41fa6838ad",
      "19e8cba8-0be1-473a-9263-ab9bc3f3c619",
      "c2f99e78-5126-451f-8d75-e4b5c5f1d452",
      "61990b09-c35e-4235-a899-c8bb5c32ab30",
      "741808f5-5592-4f92-b446-fe2752f3f785",
      "367a5c9d-4b6e-43e5-8f78-92f89d7fc9b6",
      "f883c391-5518-4fe1-94e2-519ae8fc6917",
      "afac7a96-8aed-474f-b14b-1e0bcff9f95d",
      "894258ef-52bf-457f-9619-99efeef09d2c",
      "6cecd239-a69b-48a0-914d-ed592612b491",
      "fc306814-7492-4335-82a2-0957b15a954c",
      "ae5eb396-b269-4de2-97bc-cea9d9151f05",
      "8bf71b6e-eb93-430f-ba97-1b544d2df840",
      "3c9a21a1-b035-437a-a020-5124944e8e0e",
      "60694279-f463-414d-b531-43810e3f3b8c",
      "205a4e4f-4d57-4b9f-b624-cf6ad1876200",
      "9a4c37e2-3a4c-40e5-8e61-21aee8641fc6",
      "142dff07-4f0b-4004-a8ce-e6031dd80556",
      "0553f5f3-f9aa-4097-8911-db0b5fc12db9",
      "e2833013-b77d-451b-8791-bf31b08dfd5e",
      "f189c157-1e08-4b88-a2c7-3cf3fcc83425",
      "f09f8124-434c-458c-870e-57d2a96e0a01",
      "17a9fc88-04f0-44bd-a313-f7be8cc8dd7f",
      "197b7b57-2187-4f77-a3f5-937eaabd2756",
      "ddd62b84-9287-4d06-bd6f-1bf48743a37b",
      "10f0da4b-59f7-4fa3-84cf-95a1bddab45c",
      "10217c3a-db21-488e-9990-4e4b75628aa6",
      "b33b52db-e54a-4997-893e-4362cff627dc",
      "6a91e5f4-f0d8-4bbf-8ef3-7600c76e1b46",
      "fdf158ee-e6fe-4024-bae5-f0bc3760c48a",
      "66adc338-3f6d-4561-be7b-5bcf6eb63d1b",
      "f903b686-b63b-4cde-9858-ff25b4117850",
      "229c65fb-86d2-4e63-b561-2c52695a963a",
      "2922099f-02d4-43da-b1c1-9f69bcdff571",
      "0ae18e29-2a73-4378-b354-04f8bc50abe1",
      "17c16c8d-2b84-494a-8d16-398d952d9ec3",
      "39ca2ee2-0ff8-4caa-863f-8249605e5a90",
      "5a323552-0e07-410a-9177-2440c184c79a",
      "cd2b15f0-3984-4669-8bdc-7f959fbf1186",
      "6a0ceb3b-f1e8-4006-9dc1-17f62bfb68b9",
      "81324f5d-33c4-4b58-9966-914f1cacc837",
      "bfb45f43-4d41-4ac1-9737-873168f9761e",
      "1ca2ad8a-badb-487b-a004-e40dcef2a13b",
      "7312d47c-f611-4603-a439-3aa63b991cc6",
      "b48ce372-a417-4d0c-8cba-a097cc4b44ca",
      "d8299dbd-a682-4d07-9f9a-33aefc607ed4",
      "6111fd34-89aa-4e8e-a1db-ab5378f33f66",
      "6ee742e9-4b78-44d7-936d-f63b4da0da94",
      "cec34841-3e73-4371-bd4d-9970a8f28296",
      "11997ab7-e4b6-429d-8840-f9be9403fbf9",
      "b8d6d723-dca5-4564-b7ce-d81a6d6d1986",
      "9a7e6eb3-3d8a-44d5-900d-351eed599a97",
      "7b4bc7e4-c426-437d-b86e-7c8be6e1369f",
      "3f312e20-3a5d-4f24-9430-b12e0ba91a27",
      "a608e491-ffbb-42c1-96c4-7a97b9e5633f",
      "2da3cb48-cac7-42bb-bb94-4cff5fb135e8",
      "d19784e2-1f91-4aa5-8c7d-e2a1f81292e6",
      "a6543a36-1fd1-4849-83db-ca77622989cd",
      "cf48d2de-881a-497d-bfe9-455cf06be2d8",
      "247cf279-d2a6-4110-8398-079037ef9397",
      "dbc40a8a-5ee1-4465-b8d3-c77d3b9d323f",
      "f3de0e73-fc82-4cad-99c1-003d396c61f7",
      "09dc52ee-9d7f-41a5-8e5f-55d579b58cf1",
      "d6495207-006e-4b6f-9e9d-e129c03416fa",
      "6847635f-a87f-43d8-92d1-a5a5fee65fbf",
      "4297cf69-6e4a-4f10-88d9-f808824a05d3",
      "a2d8f258-b68a-41b7-a779-8dc15d9d454c",
      "ce61e32c-d5a0-4511-9582-bc281ac47214",
      "d89606d2-0c3e-4296-aa77-7f24184128f3",
      "79a32971-dffe-4db0-a8e2-6bc13525ef09",
      "a757097f-950a-427f-b4ec-b691f9d3fe8a",
      "b3ffac6a-6e48-4577-acca-aca6b0f30fcc",
      "3c88372d-0a83-4bc8-9590-25dc9db11d59",
      "5c08ad59-73d3-4e6c-b36a-ab815220dfcc",
      "5efe0d7a-99eb-43e9-85e1-e8c4927dc095",
      "07aa7a51-902d-4f33-b91f-3ff285818fd2",
      "8dd0a113-8c2f-45c5-94ef-60f615a7ca73",
      "c4fa2544-e22a-4a1d-bbc5-0ffc2888ee9e",
      "22d4eb4b-b217-48a0-a7de-5df5972e8cc2",
      "c62d5f3c-1f54-4199-95d9-b5e58a5c44bd",
      "65ac588d-3646-40f1-afc1-7ee09c70f7b7",
      "fa4e853a-1306-40af-a8b7-d4159ccf639c",
      "4245f5e1-65ea-431d-8a11-951aa5044192",
      "13a7a0f0-b435-4837-bd24-486e7530bb4d",
      "ec0c67f2-8109-4c57-a4dd-c0f7a353d225",
      "186f8190-0844-4f51-bbed-e94b2e373e19",
      "82a6a453-46d4-4e62-bfec-c367cc0f08ab",
      "b612f8b4-6913-4606-9b44-7534bac78737",
      "2d67dd35-6cc7-4a75-8cf7-e37f444ae9f8",
      "bfe81dce-c4d8-4f7f-8c4f-b3f3d302357b",
      "6f4330a7-9eaa-4632-8596-44cd31bf532b",
      "3592ddbd-c785-45c9-9343-45fb58a0fe00",
      "e31c1a5a-5972-47f7-a3b5-96bd9250d097",
      "cc030b02-a6de-40e5-a783-fed80c6d92a3",
      "730389c7-8701-4761-9dc1-0e160d96db49",
      "4e5b8229-3c1f-436c-beea-cf7b6cbab79c",
      "932493ed-58e2-4691-912d-91952dcb641b",
      "f7b07680-f3d4-4782-8ed0-18eda9a137d2",
      "afdafd75-5126-4b82-81a7-16cda4d5fa5a",
      "d4428ecd-a109-411a-9afd-767c5eb08f04",
      "0303d843-e744-457d-8a67-1ba711bca6ec",
      "1394ffe3-f0d3-4b82-b74a-4d46e19736e3",
      "a2e996e8-f997-4a56-8558-13385197dbaa",
      "27e67b99-30c5-4552-bb59-1059f79a700e",
      "7da7c1b2-2585-42de-8f2a-e0ae0500d0af",
      "ed681f21-71a8-4ce6-a49d-d49e1be0bc13",
      "eaf662f9-d8ce-473d-a71c-96b2dae7e434",
      "6866325f-1692-400e-badf-0ecce28bb377",
      "c8ae0a10-54d0-40cf-8145-b9547b549c00",
      "32893fbb-e34f-4d4a-80f6-28627dfe65d5",
      "e95a9083-4af6-4791-9ce2-03c9e4cc3dcd",
      "7459458b-fe89-41a8-aebd-b3a274d06ecb",
      "3949e7b9-ac0a-4017-ad0f-27e168ecb0bb",
      "6804b904-bfca-4584-b0ce-4865fc7953dc",
      "adcd7366-5d69-417f-b1fc-d52c28ef0ba2",
      "07368004-bd54-45d8-9804-960ccc45281b",
      "0bda6059-fb9c-491b-893b-2159c28902f5",
      "b07e5adf-5223-4fb4-9630-6021c5a60b76",
      "5493f448-3015-4910-9c8b-ab31cebb0a15",
      "7f0724aa-4a8f-4f97-8a49-28463102cd5f",
      "967634fa-2e34-45bb-a91e-70d2ddbb52fa",
      "595c35dd-60ec-4ebd-8f2d-b72e623efe2b",
      "5182b76f-2a50-4779-952d-2252da0e704d",
      "8cc37547-c473-48ae-be28-a652340108ec",
      "0bae9049-aee9-4b5b-a269-b12cf925d7ac",
      "5876e8c9-6865-4610-8950-723767596b50",
      "7e69f64b-c641-4975-95ed-39d3b64cb0a9",
      "3596c2e2-22f4-4847-85fb-78488674ea54",
      "1af10b80-3ede-46dd-b984-01e2791424e0",
      "2d7c6ca6-9286-44b4-8e6c-daee9a2e7451",
      "b9c3e5e3-ba94-4cf0-b30b-4670ebd3d92f",
      "b3410ce6-14c2-4608-a585-8855ac9efdbb",
      "3572a59d-d41d-4971-a70e-4a74a927071a",
      "74b61dc4-df30-482c-af56-40ac34182e01",
      "b6382416-a4ce-4fca-ade9-f779454ce919",
      "e8ed8aeb-2411-4a7a-89a2-516a1e169cec",
      "99fc6484-5631-4a9c-a481-4c1a5c032756",
      "def89ea5-b220-4e4f-a855-00770ff878e8",
      "1c441672-6270-45fb-b998-02c7206754ee",
      "5429093c-bbeb-415d-94bc-96289b8f639d",
      "199b3c19-5b54-4e5e-94e9-14f402e72e65",
      "b5b39f4f-d112-4756-a544-1b0f0c813d62",
      "dfd98fde-d890-41be-b005-fac14721ef7b",
      "2b08f9f0-711d-47b5-90fd-7d048237f327",
      "7268118f-1c33-485b-9ba1-9ac96d86b5a9",
      "174000f6-a42a-4140-a336-ff35f715bbb4",
      "fb125950-ee2f-431f-8f12-3986d2122eca",
      "66684c45-4cc7-4276-a446-ed5cd82a5038",
      "9d4c5f0f-925e-417e-940a-e46d8799c890",
      "ac4a9c08-24b0-4d18-98c8-5fa3ed5d6ace",
      "c88b246f-d655-4397-9223-f5db848f7c76",
      "d2f01de8-3051-4387-804a-ec087a994aaa",
      "02dedf59-a58c-41a0-809a-fdec68c0a620",
      "579799ef-d342-4988-9c78-3c73ba9227c7",
      "b135ae83-b661-46ac-a765-55c159d151e2",
      "79cedc98-f321-4ad0-a225-1303aaa6bdd1",
      "0c6a2d14-395e-445d-bef1-00060db98138",
      "f753bd37-c8e6-41ce-bbc6-a3209fc37bc1",
      "ae8d3e32-4d1a-4ceb-94d1-09953cbda803",
      "35945e8f-f716-4473-895e-27386210bc7f",
      "53ada779-5080-43bf-a15b-2a2a50f3cbb4",
      "460d4d7b-305b-496f-9b62-1a5344f27c61",
      "8c74d6dd-4332-4d72-9b13-12c2ac58dfe8",
      "88cff9cb-f232-498f-b463-d00f92bcccc6",
      "de2bc7df-7e7b-45a6-8d6c-5c568a214de5",
      "1ac474f6-61a0-420b-8d71-357ce40bd233",
      "a0d65467-3a4d-49e8-888f-46da0e438f93",
      "ffe15da6-2f47-4fe0-a36b-9764fd4b5b47",
      "3e0c8ad6-f9e7-4b93-9c21-b33e2608d802",
      "8cabb26b-4b2d-4e1b-9d2b-8ff2a1aff595",
      "0a482d90-4c7b-4b3f-bd0e-27fe62a7dc7a",
      "86e29d66-5d29-49bf-8e05-b77ed4e5bbcc",
      "8273d462-c92f-4152-9c5d-ac40cf21f1c2",
      "6585916b-97ad-4bd2-8943-419685dc6aec",
      "ab7b1490-6846-4267-a431-4c59d993c178",
      "dd285a20-0cda-4853-abdc-bf32dce1d13e",
      "d6f8df07-487b-4620-ab45-ba915b79f233",
      "79638a91-b69f-4e08-8d31-27fc4bcba343",
      "7fe40ba0-3980-43de-a1b8-cad94059c882",
      "916dffa2-301b-4b82-95e1-bf68755f78ff",
      "7f9bca31-23b6-437b-bbff-504c83de9f1a",
      "191187a8-cffa-45bf-84ba-3fa8e641a4fc",
      "b4b7b042-1082-4180-b3e0-705a83890cfc",
      "762c2542-8b5e-4d7d-864b-f55c374cb4e3",
      "a3955a25-b68c-4490-adf5-c17eec2ff9e0",
      "18a53cb3-d56c-4f8c-afc8-936bb15c84a4",
      "e7030b66-d2e6-4190-8d76-c9a02893219b",
      "0a92778d-3f7a-4d6f-8437-c14b3fd30568",
      "364ae947-9034-46ee-aa18-da142df3ea6d",
      "9f1c81dd-a65a-448b-b55d-c448a20661da",
      "2ff26f15-6bc5-4378-8901-8209747ef5b7",
      "845bca9d-1a01-445c-a833-aec6719add83",
      "7f5536c3-c8f7-4b47-aa71-a9f26033a88b",
      "67dddfed-b56d-4a26-b204-579e1d237c22",
      "8c8e5233-2215-42b7-8ba7-adc3ad49be1a",
      "e4dcda4e-28ee-4789-80ea-cf8c46730fc3",
      "93bc21af-25f8-4c69-bb19-b0677c83ec14",
      "b2b1b549-466a-4ccf-8458-f0a8bc62cae9",
      "d4087ef7-f4ec-41aa-bba4-d17e6545f51d",
      "ad90ba95-dedb-422b-8de8-56b2a033c507",
      "d695b964-48ea-43ce-b065-c8ba3d7d745a",
      "601994ab-cd23-4a3d-af04-283ee7f45e0b",
      "64a6a6f0-bbeb-4c5d-8a92-c0a5a0d56eb9",
      "c0a6b001-1e9e-4e1e-ac5b-f4030e00247c",
      "b4766abd-911b-4349-a7b9-bbc93bc49698",
      "7e544f7b-5d9f-4b1c-8839-aa972c54de3a",
      "b45c46fd-2151-4cfe-95c7-75c053afced6",
      "1990eed0-53fd-4cb6-91f2-add1a237e4d9",
      "bcca8cc6-174c-4d05-adae-5b8a77303f55",
      "1bafabad-beb9-4c70-9f13-3d7ff44178eb",
      "85a769d3-b956-4951-98c1-435e0aff3d71",
      "48ca29dc-a1f3-4bb8-8361-ca9e19f29a24",
      "3a91d973-11e8-4336-a05c-4d65676daed7",
      "592c1648-bde2-4320-b848-b2cd186b0243",
      "85375c51-2eb0-4d82-b20f-6cb6acf62f3e",
      "7170c8a4-4ed5-41f6-90e8-d6bd7f49f36f",
      "9844beff-d39e-4b7a-bedd-61957203c57c",
      "7e8d2397-b4f7-43d8-ad42-471197703ef9",
      "d4e08fc4-ff54-403f-a3b8-075fb61d0be0",
      "ab101510-0d5d-4736-98e7-64e35dc17e41",
      "be18be83-1ec7-4018-bda0-3fbfb07f3819",
      "e678b7b0-9e77-4de9-a91b-9449efa3a203",
      "d6c2c189-ab52-42c6-ad60-4d598d492cd8",
      "07a58680-4c1d-4a0a-8483-939a90fd5ef5",
      "5e6c2c4a-7794-4aa1-8118-67e191f83493",
      "c33efed2-b7c8-45a6-89c9-59cd8d458844",
      "d7c92aef-baf2-4f0a-8556-c235d60ccd19",
      "66ce849b-74a5-4383-aa13-d5b69475fac1",
      "030ef8ae-23b0-4129-939c-b6677346debb",
      "9c4f8eb2-8110-4fca-b7a8-59130d44f628",
      "9317abfc-1334-4533-99c6-2a10cabdafa7",
      "f34a3870-03ca-4539-beea-7cc3671abc32",
      "07fdd493-9416-4acf-b817-54ea006eec3d",
      "5efd9d58-bb5c-4e0f-8c35-d0055db24b81",
      "8506b76b-e9b6-4742-af49-be033779ebb1",
      "d6c7f111-0796-4385-8ddf-b309fd6a8a47",
      "a1fb682c-a389-4a71-8b74-1d810275b9b5",
      "ca88b9f3-ccf3-4462-a504-9b4ec9ec0c71",
      "e5efd9b8-4550-4c4e-8c74-1cc6c679a1d4",
      "2bf0c9ab-9483-4c05-a025-7df659d9ecd9",
      "a95f93cf-bad4-4078-b1b4-61beb2893522",
      "3bd249c8-80c9-4368-96ae-481095190b40",
      "07539192-a4dc-4f1b-bb0a-d84daea04102",
      "e972c50d-e55b-4d13-8415-510c0d979f6e",
      "2e147561-d5f3-41b6-adad-162256cae9ed",
      "5d4de2f4-c1eb-4051-958a-0655b3e24ba5",
      "e73ff149-f0be-4f61-8b67-1e8562b2c31e",
      "02f84592-361e-492f-a24e-bfbd9ae63dfd",
      "4464d8b1-e032-4d7c-abd6-ec42e45fb1b7",
      "d427e953-8307-4c83-a89d-d548c8f19cb0",
      "38e37779-2462-498c-9f54-79eac5a6ba0e",
      "7bdd8dd5-635d-4dc8-af90-3bb16772f64e",
      "243bb2a6-4a1c-4f05-9e56-62226019965f",
      "7bdaeea9-4dde-49b0-a12f-207577f0ca20",
      "3b7208e9-916d-43ea-a1c0-ff26cd5937ee",
      "de36cae8-2cc1-462a-963d-aff48e648d3d",
      "f0c49aa5-f6a8-4da2-a830-1dbafdf68a28",
      "b8dd09dc-2a7b-4822-83e6-4c61f23ea635",
      "b62d961d-a7a0-42b8-9934-240a001b38c4",
      "2ae19afd-501e-4c9c-8f5a-e198e924cd6c",
      "8d1f864e-6d7f-4390-8443-d5cb40e8a98b",
      "3a894539-d16c-4585-a8c3-01ff0b5fec8a",
      "d0bcd771-0c4e-433f-993d-7fad42f56421",
      "df0c6ac5-85ac-48ea-8d8e-ae860b3f6e8c",
      "e73f1c22-3d8f-4b60-977c-e94673f9aa9c",
      "64f1e02b-a681-4d5b-8a80-7dce502feaaa"
    ]
  },
  "monitoring_tasks:url_tasks:970e8c15-4d52-439d-a3ed-8a2941a98c0a": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:df0c6ac5-85ac-48ea-8d8e-ae860b3f6e8c": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:f3c4b350-aaa9-4aab-8718-7caa3a87b172": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:7a1c129a-673b-4789-bad7-985afe7c6c6b": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.771513+00:00",
      "id": "7a1c129a-673b-4789-bad7-985afe7c6c6b",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 496, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"0f74210a0348d3bca44c7fcc1fe4ec97\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3582794742-12-juego-de-mini-compacto-plegable-espejo-colocar-de-bolsill-_JM?searchVariation=187109906419&highlight=false&headerTopBrand=false#polycard_client=search-nordic&searchVariation=187109906419&position=48&search_layout=grid&type=item&tracking_id=5dc07505-b25c-4806-b14a-229f9de34709",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:91505854-9c5c-4623-b5e2-a0dae89f386b": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:2d67dd35-6cc7-4a75-8cf7-e37f444ae9f8": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.815055+00:00",
      "id": "2d67dd35-6cc7-4a75-8cf7-e37f444ae9f8",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 587, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"de472eb78c06a4061c844a66ac506edf\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3317981538-2pz-kit-de-interruptor-de-luz-inalambrico-inteligente-10a-_JM?searchVariation=187070845915#polycard_client=search-nordic&searchVariation=187070845915&position=37&search_layout=grid&type=item&tracking_id=7f2b91c3-32eb-4e8c-be73-6d7e4de51888",
      "check_count": "0"
    }
  },
  "url_pool:items:94669a74-8a10-4ae5-b71e-a67878e58cc4": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.712946+00:00",
      "id": "94669a74-8a10-4ae5-b71e-a67878e58cc4",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 373, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c6c21338d308e1306722a2ad8d4eb441\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2437632184-fundas-de-tela-para-lentes-pack-100-pcs-mayoreo--_JM?searchVariation=178540391364&vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&searchVariation=178540391364&position=4&search_layout=grid&type=item&tracking_id=f7f6e6c3-0b8e-4aa0-9761-3544f493575d",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:c2f99e78-5126-451f-8d75-e4b5c5f1d452": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:07fdd493-9416-4acf-b817-54ea006eec3d": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:0bae9049-aee9-4b5b-a269-b12cf925d7ac": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.763950+00:00",
      "id": "0bae9049-aee9-4b5b-a269-b12cf925d7ac",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 480, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"612312c0685e96f07eff53fc5079fc57\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2528870762-funda-de-guitarra-de-tela-oxford-impermeable-de-41-in-_JM?highlight=false&pdp_filters=seller_id%3A693161561&headerTopBrand=false#polycard_client=search-nordic&position=41&search_layout=grid&type=item&tracking_id=5f63e5e5-7f43-410a-a16e-f2a6f62492f9",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:baefd8e7-d2f1-47bf-abc9-ad39b634a1c3": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:acac48d5-2a55-438a-ab87-3d184a1b1de1": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:5124a881-0d79-459b-805c-9fa63b2e9f45": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:66adc338-3f6d-4561-be7b-5bcf6eb63d1b": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.697350+00:00",
      "id": "66adc338-3f6d-4561-be7b-5bcf6eb63d1b",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 340, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c4be5602ed8ebf85cc682701c46930c9\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-**********-2-cerraduras-con-contrasena-para-refrigerador-infantil-_JM?vip_filters=shipping%3Afulfillment#polycard_client=search-nordic&position=2&search_layout=grid&type=item&tracking_id=70392692-b8c9-47d0-a12d-bb0099c84666",
      "check_count": "0"
    }
  },
  "monitoring_tasks:active_tasks": {
    "type": "set",
    "data": [
      "health_check_task_20250713_001427"
    ]
  },
  "monitoring_tasks:url_tasks:713579a0-0b89-4e1a-bee7-faf3d055de92": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:d4428ecd-a109-411a-9afd-767c5eb08f04": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.594519+00:00",
      "id": "d4428ecd-a109-411a-9afd-767c5eb08f04",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 124, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"2f18470c5fad0cf10eb804c7fe8d47dc\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-**********-moyac-inteligente-enchufe-pared-wifi-con-cargador-2-usb-_JM?searchVariation=182613307302#is_advertising=true&searchVariation=182613307302&backend_model=search-backend&position=11&search_layout=grid&type=pad&tracking_id=12ddc6d2-19ba-4cb7-afd2-edcd54f0767e&is_advertising=true&ad_domain=VQCATCORE_LST&ad_position=11&ad_click_id=MmM4MjZhZTYtNTQxMi00NGI4LTgzZTYtZWFmNWRjNjljZDE1",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:91722c88-b708-4e0a-8287-99c336d1fc6a": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:8dd4e411-6aa0-46c9-af64-d1ea09622044": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:db35b2ba-318e-4d2f-af2e-7bd96caefda9": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:d4986f4b-5629-474f-8a0f-79270de19844": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:2b08f9f0-711d-47b5-90fd-7d048237f327": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:7da7c1b2-2585-42de-8f2a-e0ae0500d0af": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:592c1648-bde2-4320-b848-b2cd186b0243": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.822048+00:00",
      "id": "592c1648-bde2-4320-b848-b2cd186b0243",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 602, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"c1a557f1190aebba3c1e60ae81d3e5cd\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2122523391-polainas-viboreras-contra-mordedura-de-serpiente-caza-pesca-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_low_exposition%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=4&reco_backend_type=low_level&reco_id=48e1efed-0faa-44fb-9bd0-7e9f22e1fbd4",
      "check_count": "0"
    }
  },
  "url_pool:items:dbb71df3-acac-4626-96ce-8aea475f926d": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.699594+00:00",
      "id": "dbb71df3-acac-4626-96ce-8aea475f926d",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 345, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"2436b52cf983345e15e53c5bf6fee9fd\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2735283434-extraccion-de-bomba-de-succion-de-aceite-de-jeringa-multipro-_JM#polycard_client=recommendations_vip-v2p&reco_backend=vpp-v2p-exploration_odin_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=vip-v2p&reco_item_pos=1&reco_backend_type=low_level&reco_id=b18eaa49-0267-40e5-b640-2ba4ee87a742",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:f3de0e73-fc82-4cad-99c1-003d396c61f7": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:8959a23b-079b-4238-97cd-b271b8096f60": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:65ac588d-3646-40f1-afc1-7ee09c70f7b7": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.816128+00:00",
      "id": "65ac588d-3646-40f1-afc1-7ee09c70f7b7",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 589, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"65f2946e5a216d0c551eb42cdd14f489\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2136059519-receptor-rf-relevador-remoto-433mhz-garage-chapas-porton-12v-_JM#polycard_client=recommendations_vip-pads-up&reco_backend=vip-pads-up-experimental-a_marketplace&reco_model=rk_ent_v2_retsys_ads&reco_client=vip-pads-up&reco_item_pos=1&reco_backend_type=low_level&reco_id=826c2930-5832-426e-a010-ee6231b6606c&wid=MLM2136059519&sid=recos&is_advertising=true&ad_domain=VIPDESKTOP_UP&ad_position=2&ad_click_id=YzMxYTQ0ZmQtOTVhNi00ZWEwLTg2ZjEtNTY2MzZlNzM1MzYy",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:1ac474f6-61a0-420b-8d71-357ce40bd233": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:1990eed0-53fd-4cb6-91f2-add1a237e4d9": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.698744+00:00",
      "id": "1990eed0-53fd-4cb6-91f2-add1a237e4d9",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 343, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"d9d749c38685fa6efdf054b3be8bd4fd\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3512317964-extraccion-de-bomba-succion-de-aceite-de-jeringa-multipro-_JM#polycard_client=search-nordic&position=21&search_layout=stack&type=item&tracking_id=7bfdc6d3-384f-46b6-9d21-44f572daa7ce&wid=MLM3512317964&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:url_tasks:1fe9f8bc-a3d4-4eac-8dca-018d90d7b2e1": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:84d2c3b4-1f55-402a-9603-2603fb3471d0": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.744857+00:00",
      "id": "84d2c3b4-1f55-402a-9603-2603fb3471d0",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 440, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"d36f760a29c736bac622c6bf7d6898cd\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2333508024-cubierta-antipolvo-compatible-con-ps5-protector-antiaranazos-_JM#polycard_client=recommendations_pdp-pads-up&reco_backend=pdp_pads_up_merge_rars_v2_with_default&reco_client=pdp-pads-up&reco_item_pos=6&reco_backend_type=low_level&reco_id=091bd3e5-148d-4c8e-bddd-0d09a16c1e40&is_advertising=true&ad_domain=PDPDESKTOP_UP&ad_position=7&ad_click_id=YTMzY2VhNTQtZWVkOS00MTkxLThlMzAtYTNhYzExYjkzNGU4",
      "check_count": "0"
    }
  },
  "url_pool:items:9a4c37e2-3a4c-40e5-8e61-21aee8641fc6": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.595886+00:00",
      "id": "9a4c37e2-3a4c-40e5-8e61-21aee8641fc6",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 127, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"46113dc9af46b98876c09681abed7f72\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-2031611773-contacto-doble-toma-corriente-con-puertos-usb-y-tipo-c-_JM#polycard_client=search_best-seller",
      "check_count": "0"
    }
  },
  "url_pool:items:4a5643f0-594c-4276-80e3-d33202b61c3e": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.607127+00:00",
      "id": "4a5643f0-594c-4276-80e3-d33202b61c3e",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 150, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"30e4819fbf64c6478f89ca347d39aa37\"}",
      "error_count": "0",
      "url": "https://www.mercadolibre.com.mx/qm-frasco-con-versiculos-biblicos-frasco-de-esperanza/p/MLM2003192907#polycard_client=search-nordic&searchVariation=MLM2003192907&wid=MLM2254490327&position=4&search_layout=grid&type=product&tracking_id=8e37d13b-d70f-43ad-b867-dfea0b222f88&sid=search",
      "check_count": "0"
    }
  },
  "monitoring_tasks:scheduled": {
    "type": "set",
    "data": [
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:817df345-6baf-4af9-9342-698994f0f4a2": {
    "type": "set",
    "data": [
      "3d0007bc-2e50-4afb-8d57-a08673b7ccff",
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:5429093c-bbeb-415d-94bc-96289b8f639d": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "monitoring_tasks:url_tasks:2a43aeac-316b-4d0c-a401-80f667f1d7fe": {
    "type": "set",
    "data": [
      "aed073ae-1bcd-454e-924e-876bb4c1dc34",
      "41a7eb96-648c-4b09-b49e-61718afb4d14",
      "fe2ae36c-cffa-460e-942a-4c95a78122fe"
    ]
  },
  "url_pool:items:5394e668-e39b-45e7-918d-3d39959fe2f8": {
    "type": "hash",
    "data": {
      "status": "active",
      "added_at": "2025-07-05T09:24:44.664688+00:00",
      "id": "5394e668-e39b-45e7-918d-3d39959fe2f8",
      "last_check": "",
      "source_file": "all_task_item.xlsx",
      "platform": "mercadolibre",
      "success_count": "0",
      "metadata": "{\"original_row\": 270, \"original_column\": \"product_url\", \"detected_platform\": \"mercadolibre\", \"url_hash\": \"8a3cc528f63fe2f6ebf1bb4a2106a4c5\"}",
      "error_count": "0",
      "url": "https://articulo.mercadolibre.com.mx/MLM-3510892848-reloj-proyector-ninos-dibujos-24-imagenes-capybar-_JM?searchVariation=182603489454#polycard_client=search-nordic&searchVariation=182603489454&position=34&search_layout=grid&type=item&tracking_id=d1a91889-b17d-4b7e-a4bb-e095ff375aff",
      "check_count": "0"
    }
  },
  "monitor_queue": {
    "type": "list",
    "data": [
      