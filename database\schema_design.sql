-- MonIt 电商爬虫系统数据库表结构设计
-- 基于 TimescaleDB 的时间序列数据库

-- ============================================
-- 1. 商品基础信息表
-- ============================================
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name_cn VARCHAR(500) NOT NULL,           -- 中文品名
    name_original VARCHAR(500) NOT NULL,     -- 原始西班牙语品名  
    url TEXT NOT NULL UNIQUE,                -- 商品链接
    domain VARCHAR(100) NOT NULL,            -- 所属域名
    category_id INTEGER,                     -- 商品分类ID
    status VARCHAR(20) DEFAULT 'active',     -- 状态: active, inactive, deleted
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT products_status_check CHECK (status IN ('active', 'inactive', 'deleted'))
);

-- ============================================
-- 2. 商品快照时间序列表 (TimescaleDB hypertable)
-- ============================================
CREATE TABLE product_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,         -- 抓取时间戳
    product_id INTEGER NOT NULL,            -- 商品ID (外键)
    task_id INTEGER NOT NULL,               -- 任务ID (外键)
    
    -- 基础价格信息
    current_price DECIMAL(12,2),            -- 商品当前售价
    original_price DECIMAL(12,2),           -- 商品原价  
    discount_rate DECIMAL(5,2),             -- 折扣率 (百分比)
    
    -- 库存和销售信息
    stock_quantity INTEGER,                 -- 库存数量
    sold_quantity INTEGER,                  -- 销售数量
    
    -- 评价信息
    rating DECIMAL(3,2),                    -- 商品评分 (0-5)
    review_count INTEGER,                   -- 评分数量
    
    -- 媒体信息
    main_image_url TEXT,                    -- 商品首图URL
    images JSONB,                           -- 其他图片URL数组
    
    -- 扩展字段 (JSON格式存储其他爬取字段)
    extra_data JSONB,                       -- 扩展数据
    
    -- 元数据
    crawl_duration_ms INTEGER,              -- 爬取耗时(毫秒)
    success BOOLEAN DEFAULT true,           -- 是否成功抓取
    error_message TEXT,                     -- 错误信息
    
    PRIMARY KEY (timestamp, product_id)
);

-- 将表转换为TimescaleDB hypertable (按时间分区)
SELECT create_hypertable('product_snapshots', 'timestamp');

-- ============================================
-- 3. 爬取任务表
-- ============================================
CREATE TABLE crawl_tasks (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,             -- 任务名称
    description TEXT,                       -- 任务描述
    config_id INTEGER NOT NULL,             -- 爬取配置ID
    
    -- 任务时间信息
    scheduled_at TIMESTAMPTZ,               -- 计划执行时间
    started_at TIMESTAMPTZ,                 -- 实际开始时间
    completed_at TIMESTAMPTZ,               -- 完成时间
    
    -- 任务状态
    status VARCHAR(20) DEFAULT 'pending',   -- pending, running, completed, failed, cancelled
    progress INTEGER DEFAULT 0,             -- 进度百分比 (0-100)
    
    -- 统计信息
    total_products INTEGER DEFAULT 0,       -- 总商品数
    success_count INTEGER DEFAULT 0,        -- 成功数量
    failed_count INTEGER DEFAULT 0,         -- 失败数量
    
    -- 元数据
    created_by VARCHAR(100),                -- 创建者
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT crawl_tasks_status_check CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT crawl_tasks_progress_check CHECK (progress >= 0 AND progress <= 100)
);

-- ============================================
-- 4. 爬取配置表
-- ============================================
CREATE TABLE crawl_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL UNIQUE,      -- 配置名称
    domain VARCHAR(100) NOT NULL,           -- 目标域名
    
    -- 爬取规则 (JSON格式存储)
    selectors JSONB NOT NULL,               -- CSS选择器配置
    headers JSONB,                          -- HTTP请求头配置
    
    -- 反爬策略配置
    user_agents JSONB,                      -- 用户代理列表
    proxy_enabled BOOLEAN DEFAULT false,    -- 是否启用代理
    delay_min INTEGER DEFAULT 1000,         -- 最小延迟(毫秒)
    delay_max INTEGER DEFAULT 3000,         -- 最大延迟(毫秒)
    
    -- 重试配置
    max_retries INTEGER DEFAULT 3,          -- 最大重试次数
    retry_delay INTEGER DEFAULT 5000,       -- 重试延迟(毫秒)
    timeout_ms INTEGER DEFAULT 30000,       -- 超时时间(毫秒)
    
    -- 状态和时间
    enabled BOOLEAN DEFAULT true,           -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- 5. 商品分类表
-- ============================================
CREATE TABLE product_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,             -- 分类名称
    parent_id INTEGER,                      -- 父分类ID
    level INTEGER DEFAULT 1,                -- 分类层级
    path VARCHAR(500),                      -- 分类路径 (如: /电子产品/手机)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_parent_category FOREIGN KEY (parent_id) REFERENCES product_categories(id)
);

-- ============================================
-- 6. 爬取规则表 (细粒度规则管理)
-- ============================================
CREATE TABLE scraping_rules (
    id SERIAL PRIMARY KEY,
    config_id INTEGER NOT NULL,             -- 关联配置ID
    field_name VARCHAR(100) NOT NULL,       -- 字段名称
    selector VARCHAR(500) NOT NULL,         -- CSS选择器
    attribute VARCHAR(100),                 -- 提取属性 (text, href, src等)
    regex_pattern VARCHAR(500),             -- 正则表达式 (可选)
    data_type VARCHAR(50) DEFAULT 'text',   -- 数据类型
    required BOOLEAN DEFAULT false,         -- 是否必填
    default_value TEXT,                     -- 默认值
    enabled BOOLEAN DEFAULT true,           -- 是否启用
    priority INTEGER DEFAULT 1,             -- 优先级
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_scraping_config FOREIGN KEY (config_id) REFERENCES crawl_configs(id) ON DELETE CASCADE,
    CONSTRAINT scraping_rules_data_type_check CHECK (data_type IN ('text', 'number', 'decimal', 'boolean', 'url', 'json'))
);

-- ============================================
-- 7. 代理池表
-- ============================================
CREATE TABLE proxy_pools (
    id SERIAL PRIMARY KEY,
    host VARCHAR(255) NOT NULL,             -- 代理主机
    port INTEGER NOT NULL,                  -- 代理端口
    username VARCHAR(100),                  -- 用户名
    password VARCHAR(100),                  -- 密码
    proxy_type VARCHAR(20) DEFAULT 'http',  -- 代理类型: http, https, socks4, socks5
    
    -- 状态信息
    status VARCHAR(20) DEFAULT 'active',    -- active, inactive, banned
    success_count INTEGER DEFAULT 0,        -- 成功次数
    failed_count INTEGER DEFAULT 0,         -- 失败次数
    last_used_at TIMESTAMPTZ,               -- 最后使用时间
    last_checked_at TIMESTAMPTZ,            -- 最后检查时间
    response_time_ms INTEGER,               -- 响应时间(毫秒)
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT proxy_pools_status_check CHECK (status IN ('active', 'inactive', 'banned')),
    CONSTRAINT proxy_pools_type_check CHECK (proxy_type IN ('http', 'https', 'socks4', 'socks5'))
);

-- ============================================
-- 8. User Agent 池表
-- ============================================
CREATE TABLE user_agents (
    id SERIAL PRIMARY KEY,
    user_agent TEXT NOT NULL UNIQUE,        -- User Agent字符串
    browser VARCHAR(50),                    -- 浏览器类型
    version VARCHAR(20),                    -- 版本号
    platform VARCHAR(50),                  -- 平台类型
    mobile BOOLEAN DEFAULT false,           -- 是否移动端
    weight INTEGER DEFAULT 1,               -- 权重 (使用频率)
    enabled BOOLEAN DEFAULT true,           -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- 9. 系统日志表
-- ============================================
CREATE TABLE system_logs (
    id INTEGER NOT NULL,
    logged_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    level VARCHAR(20) NOT NULL,             -- DEBUG, INFO, WARNING, ERROR, CRITICAL
    logger VARCHAR(100) NOT NULL,           -- 日志记录器名称
    message TEXT NOT NULL,                  -- 日志消息
    module VARCHAR(100),                    -- 模块名称
    function VARCHAR(100),                  -- 函数名称
    line_number INTEGER,                    -- 行号
    
    -- 关联信息
    task_id INTEGER,                        -- 关联任务ID
    product_id INTEGER,                     -- 关联商品ID
    
    -- 额外数据
    extra_data JSONB,                       -- 额外的结构化数据
    
    -- 时间戳字段 (继承自TimestampMixin)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 复合主键，包含分区列
    PRIMARY KEY (id, logged_at),
    CONSTRAINT system_logs_level_check CHECK (level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'))
);

-- 将系统日志表也转换为时间序列表
SELECT create_hypertable('system_logs', 'logged_at');

-- ============================================
-- 10. 外键约束
-- ============================================
ALTER TABLE products ADD CONSTRAINT fk_products_category 
    FOREIGN KEY (category_id) REFERENCES product_categories(id);

ALTER TABLE product_snapshots ADD CONSTRAINT fk_snapshots_product 
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;

ALTER TABLE product_snapshots ADD CONSTRAINT fk_snapshots_task 
    FOREIGN KEY (task_id) REFERENCES crawl_tasks(id) ON DELETE CASCADE;

ALTER TABLE crawl_tasks ADD CONSTRAINT fk_tasks_config 
    FOREIGN KEY (config_id) REFERENCES crawl_configs(id);

-- ============================================
-- 11. 索引优化
-- ============================================

-- 商品表索引
CREATE INDEX idx_products_url ON products(url);
CREATE INDEX idx_products_domain ON products(domain);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_category ON products(category_id);

-- 商品快照表索引 (TimescaleDB会自动创建时间索引)
CREATE INDEX idx_snapshots_product_time ON product_snapshots(product_id, timestamp DESC);
CREATE INDEX idx_snapshots_task ON product_snapshots(task_id);
CREATE INDEX idx_snapshots_success ON product_snapshots(success);

-- 任务表索引
CREATE INDEX idx_tasks_status ON crawl_tasks(status);
CREATE INDEX idx_tasks_scheduled ON crawl_tasks(scheduled_at);
CREATE INDEX idx_tasks_created ON crawl_tasks(created_at);

-- 配置表索引
CREATE INDEX idx_configs_domain ON crawl_configs(domain);
CREATE INDEX idx_configs_enabled ON crawl_configs(enabled);

-- 代理池索引
CREATE INDEX idx_proxy_status ON proxy_pools(status);
CREATE INDEX idx_proxy_last_used ON proxy_pools(last_used_at);

-- 系统日志索引
CREATE INDEX idx_logs_level ON system_logs(level);
CREATE INDEX idx_logs_task ON system_logs(task_id);
CREATE INDEX idx_logs_timestamp ON system_logs(logged_at DESC);

-- ============================================
-- 12. TimescaleDB 压缩和保留策略
-- ============================================

-- 启用压缩 (7天后压缩数据)
ALTER TABLE product_snapshots SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'product_id'
);

SELECT add_compression_policy('product_snapshots', INTERVAL '7 days');

-- 数据保留策略 (1年后删除数据)
SELECT add_retention_policy('product_snapshots', INTERVAL '365 days');

-- 系统日志压缩和保留
ALTER TABLE system_logs SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'level'
);

SELECT add_compression_policy('system_logs', INTERVAL '3 days');
SELECT add_retention_policy('system_logs', INTERVAL '90 days'); 