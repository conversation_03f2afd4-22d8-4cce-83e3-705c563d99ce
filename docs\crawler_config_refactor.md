# 爬虫配置重构：系统级配置分离

## 概述

本次重构将爬虫配置中的系统级参数从用户配置界面中分离出来，移至后端环境变量管理，实现更清晰的配置层次结构。

## 重构目标

1. **配置职责分离**：区分用户级配置和系统级配置
2. **简化用户界面**：移除用户不应该配置的系统参数
3. **统一系统管理**：通过环境变量统一管理系统级配置
4. **提高安全性**：避免用户误配置影响系统性能

## 变更内容

### 1. 移除的前端配置字段

从用户配置表单中移除以下字段：
- `max_concurrent_tasks` (最大并发任务数)
- `batch_timeout` (批次超时时间)
- `callback_url` (回调URL)

**影响的文件：**
- `frontend/src/pages/CrawlerManagement/components/CrawlerConfigForm.tsx`
- `frontend/src/pages/CrawlerManagement/components/CrawlerExecutionModal.tsx`
- `frontend/src/types/crawlerConfig.ts`

### 2. 新增的环境变量配置

在 `docker-compose.dev.yml` 中添加：
```yaml
# 爬虫系统级配置
- CRAWLER_MAX_CONCURRENT_TASKS=2
- CRAWLER_BATCH_TIMEOUT=300
- CRAWLER_DEFAULT_CALLBACK_URL=http://backend:8000/api/v1/crawler/callback
```

### 3. 后端配置更新

**新增文件：**
- `backend/app/services/system_config_service.py` - 系统配置服务

**更新的配置类：**
- `backend/config/settings.py` - 添加系统级配置字段
- `backend/app/api/crawl_config_routes.py` - 移除用户级配置字段
- `backend/app/core/task_manager.py` - 使用系统配置

### 4. 新增系统配置页面

**新增文件：**
- `frontend/src/pages/SystemConfig/index.tsx` - 系统配置查看页面

**新增API端点：**
- `GET /api/v1/crawl-configs/system-config` - 获取系统配置信息

## 配置层次结构

### 用户级配置（前端可配置）
- 爬取查询参数 (LLM Query)
- 数据提取Schema
- 缓存设置
- 任务优先级
- 认证配置
- 用户代理设置

### 系统级配置（环境变量管理）
- 最大并发任务数
- 批次超时时间
- 默认回调URL
- API连接配置
- 速率限制配置

## 使用方式

### 1. 查看系统配置
访问新的系统配置页面查看当前系统级配置：
```
/system-config
```

### 2. 修改系统配置
通过修改环境变量或 `docker-compose.dev.yml` 文件：
```yaml
environment:
  - CRAWLER_MAX_CONCURRENT_TASKS=4  # 修改最大并发数
  - CRAWLER_BATCH_TIMEOUT=600       # 修改超时时间
```

### 3. 任务创建
用户创建任务时，系统会自动应用系统级配置：
```python
# 系统会自动合并用户配置和系统配置
enhanced_config = SystemConfigService.get_system_config_for_task(user_config)
```

## 兼容性说明

### 向后兼容
- 现有的任务配置数据结构保持不变
- API接口保持兼容，只是移除了部分用户输入字段
- 现有任务继续正常运行

### 迁移建议
1. 检查现有任务的配置，确保系统级配置符合预期
2. 如有特殊需求，可通过环境变量调整系统配置
3. 重新部署服务以应用新的配置结构

## 优势

1. **更清晰的职责分离**：用户只需关注业务相关配置
2. **更好的系统控制**：管理员可以统一控制系统性能参数
3. **减少配置错误**：避免用户误配置导致的系统问题
4. **简化用户界面**：减少不必要的配置选项

## 后续计划

1. 添加系统配置的动态更新功能
2. 实现配置变更的审计日志
3. 添加配置模板和预设方案
4. 实现配置的热重载机制
