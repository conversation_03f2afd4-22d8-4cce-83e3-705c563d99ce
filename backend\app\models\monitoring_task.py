"""
监控任务数据模型

定义监控任务的数据结构和数据库模型
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON, Float
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class TaskStatus(str, Enum):
    """任务状态枚举"""
    DRAFT = "draft"          # 草稿
    ACTIVE = "active"        # 活跃
    PAUSED = "paused"        # 暂停
    STOPPED = "stopped"      # 停止


class ScheduleType(str, Enum):
    """调度类型枚举"""
    DAILY = "daily"          # 每天
    WEEKLY = "weekly"        # 每周
    HOURLY = "hourly"        # 每小时
    CUSTOM = "custom"        # 自定义
    MANUAL = "manual"        # 手动执行（向后兼容）


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"              # 低
    NORMAL = "normal"        # 普通（原medium）
    HIGH = "high"            # 高
    URGENT = "urgent"        # 紧急


class UrlStatus(str, Enum):
    """URL状态枚举"""
    ACTIVE = "active"        # 活跃
    INACTIVE = "inactive"    # 非活跃
    ERROR = "error"          # 错误


# Pydantic模型（用于API）
class ScheduleConfig(BaseModel):
    """调度配置"""
    type: ScheduleType
    enabled: bool = True
    time: Optional[str] = Field(None, description="执行时间，格式：HH:MM")
    start_time: Optional[str] = Field(None, description="开始时间，可以是ISO格式或时间格式")
    days: Optional[List[int]] = Field(None, description="周几执行，1-7表示周一到周日")
    interval: Optional[int] = Field(None, description="自定义间隔（小时）")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    timezone: str = Field("Asia/Shanghai", description="时区")
    end_time: Optional[str] = Field(None, description="结束时间，格式：HH:MM")
    max_runs: Optional[int] = Field(None, description="最大执行次数")

    # 随机启动范围配置
    enable_random_delay: bool = Field(False, description="是否启用随机延迟")
    random_delay_min: Optional[int] = Field(0, description="最小随机延迟（分钟）")
    random_delay_max: Optional[int] = Field(180, description="最大随机延迟（分钟）")


class TaskConfig(BaseModel):
    """任务配置 - 仅包含任务业务相关配置，Worker性能配置已移至Worker管理"""
    platform: str
    priority: TaskPriority = TaskPriority.NORMAL
    enable_notifications: bool = True
    notification_config: Optional[Dict[str, Any]] = None


class TaskStats(BaseModel):
    """任务统计信息"""
    total_runs: int = 0
    success_count: int = 0
    failed_count: int = 0
    success_rate: float = 0.0
    avg_duration: float = 0.0
    last_success_time: Optional[datetime] = None

    @field_validator('last_success_time', mode='before')
    @classmethod
    def validate_last_success_time(cls, v):
        """验证last_success_time字段，处理空字符串"""
        if v == "" or v is None:
            return None
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except:
                return None
        return v


class MonitoringUrl(BaseModel):
    """监控URL"""
    id: str
    url: str
    status: UrlStatus = UrlStatus.ACTIVE
    added_at: datetime
    last_check: Optional[datetime] = None
    last_price: Optional[str] = None
    check_count: int = 0
    error_count: int = 0
    url_metadata: Optional[Dict[str, Any]] = None


class MonitoringTaskCreate(BaseModel):
    """创建监控任务请求"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    schedule: ScheduleConfig
    config: TaskConfig
    urls: Optional[List[str]] = None


class MonitoringTaskUpdate(BaseModel):
    """更新监控任务请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    schedule: Optional[ScheduleConfig] = None
    config: Optional[TaskConfig] = None
    status: Optional[TaskStatus] = None


class MonitoringTaskResponse(BaseModel):
    """监控任务响应"""
    id: str
    name: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None  # 任务标签
    schedule: ScheduleConfig
    config: TaskConfig
    total_urls: int
    active_urls: int
    status: TaskStatus
    is_running: bool = False
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    stats: TaskStats
    created_at: datetime
    updated_at: datetime
    source: Optional[str] = None  # 任务来源
    source_info: Optional[Dict[str, Any]] = None  # 来源信息


class UrlBatchAddRequest(BaseModel):
    """批量添加URL请求"""
    urls: List[str]
    replace_existing: bool = False  # 是否替换现有URL


class UrlBatchAddResponse(BaseModel):
    """批量添加URL响应"""
    added_count: int
    duplicate_count: int
    error_count: int
    total_urls: int


class MonitoringTaskListResponse(BaseModel):
    """监控任务列表响应模型"""
    success: bool = True
    data: List[Union[MonitoringTaskResponse, dict]] = []
    total: int = 0
    page: Optional[int] = None
    page_size: Optional[int] = None
    errors: Optional[List[str]] = None


# SQLAlchemy数据库模型
class MonitoringTaskDB(Base):
    """监控任务数据库模型"""
    __tablename__ = "monitoring_tasks"

    id = Column(String(36), primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # 调度配置（JSON存储）
    schedule_config = Column(JSON, nullable=False)
    
    # 任务配置（JSON存储）
    task_config = Column(JSON, nullable=False)
    
    # 状态信息
    status = Column(String(20), nullable=False, default=TaskStatus.DRAFT.value)
    is_running = Column(Boolean, default=False)
    
    # 时间信息
    last_run = Column(DateTime(timezone=True), nullable=True)
    next_run = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 统计信息（JSON存储）
    stats = Column(JSON, nullable=True, default=lambda: {
        "total_runs": 0,
        "success_rate": 0.0,
        "avg_response_time": 0.0,
        "last_success_time": None
    })


class MonitoringUrlDB(Base):
    """监控URL数据库模型"""
    __tablename__ = "monitoring_urls"

    id = Column(String(36), primary_key=True)
    task_id = Column(String(36), nullable=False, index=True)
    url = Column(Text, nullable=False)
    status = Column(String(20), nullable=False, default=UrlStatus.ACTIVE.value)
    
    # 时间信息
    added_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    last_check = Column(DateTime(timezone=True), nullable=True)
    
    # 监控数据
    last_price = Column(String(50), nullable=True)
    check_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    
    # 元数据（JSON存储）
    url_metadata = Column(JSON, nullable=True)


class TaskExecutionLogDB(Base):
    """任务执行日志数据库模型"""
    __tablename__ = "task_execution_logs"

    id = Column(String(36), primary_key=True)
    task_id = Column(String(36), nullable=False, index=True)
    
    # 执行信息
    started_at = Column(DateTime(timezone=True), nullable=False)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), nullable=False)  # success, failed, running
    
    # 统计信息
    total_urls = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    avg_response_time = Column(Float, nullable=True)
    
    # 错误信息
    error_message = Column(Text, nullable=True)
    
    # 详细日志（JSON存储）
    execution_details = Column(JSON, nullable=True)


# 工具函数
def calculate_next_run(schedule: ScheduleConfig, last_run: Optional[datetime] = None) -> Optional[datetime]:
    """计算下次执行时间"""
    from datetime import timedelta
    import pytz
    
    now = datetime.now(pytz.timezone(schedule.timezone))
    
    if schedule.type == ScheduleType.DAILY:
        if schedule.time:
            hour, minute = map(int, schedule.time.split(':'))
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
            return next_run
    
    elif schedule.type == ScheduleType.WEEKLY:
        if schedule.time and schedule.days:
            hour, minute = map(int, schedule.time.split(':'))
            # 找到下一个执行日
            current_weekday = now.weekday() + 1  # 转换为1-7格式
            next_days = [d for d in schedule.days if d > current_weekday]
            if not next_days:
                next_days = schedule.days
                days_to_add = 7 - current_weekday + min(next_days)
            else:
                days_to_add = min(next_days) - current_weekday
            
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            next_run += timedelta(days=days_to_add)
            return next_run
    
    elif schedule.type == ScheduleType.HOURLY:
        if schedule.interval:
            if last_run:
                return last_run + timedelta(hours=schedule.interval)
            else:
                return now + timedelta(hours=schedule.interval)
    
    return None


def validate_schedule_config(schedule: ScheduleConfig) -> bool:
    """验证调度配置"""
    if schedule.type == ScheduleType.DAILY:
        return schedule.time is not None
    elif schedule.type == ScheduleType.WEEKLY:
        return schedule.time is not None and schedule.days is not None and len(schedule.days) > 0
    elif schedule.type == ScheduleType.HOURLY:
        return schedule.interval is not None and schedule.interval > 0
    return False


def generate_task_id() -> str:
    """生成任务ID"""
    import uuid
    return str(uuid.uuid4())


def generate_url_id() -> str:
    """生成URL ID"""
    import uuid
    return str(uuid.uuid4())
