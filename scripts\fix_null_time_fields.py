#!/usr/bin/env python3
"""
修复time字段为null的任务

目的：
1. 找到所有time字段为null的任务
2. 根据任务名称或其他信息推断正确的时间
3. 更新Redis中的调度配置
4. 重新计算下次运行时间
"""

import redis
import json
import pytz
from datetime import datetime, timezone, timedelta
import random
import re


class NullTimeFieldFixer:
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """初始化Redis连接"""
        self.redis = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
    
    def find_null_time_tasks(self):
        """找到所有time字段为null的任务"""
        print("🔍 查找time字段为null的任务...")
        
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        null_time_tasks = []
        
        for task_id in task_ids:
            task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
            task_name = task_data.get('name', '未知')
            
            schedule_raw = task_data.get('schedule', '{}')
            try:
                schedule = json.loads(schedule_raw)
                time_field = schedule.get('time')
                
                if time_field is None:
                    null_time_tasks.append({
                        'task_id': task_id,
                        'task_name': task_name,
                        'schedule': schedule
                    })
            except Exception as e:
                print(f"   ⚠️  任务 {task_name} 调度配置解析失败: {e}")
        
        print(f"   发现 {len(null_time_tasks)} 个需要修复的任务")
        return null_time_tasks
    
    def infer_time_from_task_name(self, task_name):
        """从任务名称推断时间"""
        # 尝试从任务名称中提取时间信息
        # 例如: "0100" -> "01:00", "0200" -> "02:00"
        
        # 匹配4位数字格式 (如 0100, 0200)
        match = re.match(r'^(\d{2})(\d{2})$', task_name.strip())
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2))
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                return f"{hour:02d}:{minute:02d}"
        
        # 匹配其他可能的时间格式
        time_patterns = [
            r'(\d{1,2}):(\d{2})',  # HH:MM 或 H:MM
            r'(\d{1,2})点(\d{2})',  # X点XX
            r'(\d{1,2})时(\d{2})',  # X时XX
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, task_name)
            if match:
                hour = int(match.group(1))
                minute = int(match.group(2))
                if 0 <= hour <= 23 and 0 <= minute <= 59:
                    return f"{hour:02d}:{minute:02d}"
        
        # 如果无法推断，返回默认时间
        return "09:00"
    
    def calculate_next_run_time(self, schedule_config):
        """计算下次运行时间"""
        user_timezone = pytz.timezone(schedule_config.get('timezone', 'Asia/Shanghai'))
        now_utc = datetime.now(timezone.utc)
        now_local = now_utc.astimezone(user_timezone)
        
        time_str = schedule_config.get('time', '09:00')
        try:
            time_parts = time_str.split(':')
            hour = int(time_parts[0])
            minute = int(time_parts[1]) if len(time_parts) > 1 else 0
        except (ValueError, IndexError):
            hour, minute = 9, 0
        
        # 计算下次执行时间
        next_run_local = now_local.replace(hour=hour, minute=minute, second=0, microsecond=0)
        
        # 如果今天的执行时间已过，安排到明天
        if next_run_local <= now_local:
            next_run_local = next_run_local + timedelta(days=1)
        
        # 应用随机延迟
        if schedule_config.get('enable_random_delay', False):
            delay_min = schedule_config.get('random_delay_min', 0)
            delay_max = schedule_config.get('random_delay_max', 180)
            random_delay = random.randint(delay_min, delay_max)
            next_run_local = next_run_local + timedelta(minutes=random_delay)
        
        # 转换回UTC时间
        next_run = next_run_local.astimezone(timezone.utc)
        return next_run
    
    def fix_task_time(self, task_info):
        """修复单个任务的时间配置"""
        task_id = task_info['task_id']
        task_name = task_info['task_name']
        schedule = task_info['schedule']
        
        print(f"   🔧 修复任务: {task_name} ({task_id[:8]}...)")
        
        # 推断正确的时间
        inferred_time = self.infer_time_from_task_name(task_name)
        print(f"      推断时间: {inferred_time}")
        
        # 更新调度配置
        schedule['time'] = inferred_time
        
        # 确保所有必需字段都存在
        if 'enabled' not in schedule:
            schedule['enabled'] = True
        if 'days' not in schedule:
            schedule['days'] = None
        
        # 计算下次运行时间
        next_run = self.calculate_next_run_time(schedule)
        
        # 更新Redis
        task_key = f'monitoring_tasks:{task_id}'
        self.redis.hset(task_key, 'schedule', json.dumps(schedule))
        self.redis.hset(task_key, 'next_run', next_run.isoformat())
        
        # 获取用户时区用于显示
        user_timezone = pytz.timezone(schedule.get('timezone', 'Asia/Shanghai'))
        next_run_local = next_run.astimezone(user_timezone)
        
        print(f"      ✅ 修复完成")
        print(f"         设置时间: {inferred_time}")
        print(f"         下次运行: {next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        return {
            'success': True,
            'task_name': task_name,
            'inferred_time': inferred_time,
            'next_run': next_run.isoformat()
        }
    
    def run_fix(self):
        """执行修复流程"""
        print("🚀 开始修复time字段为null的任务...")
        print("=" * 60)
        
        # 1. 找到需要修复的任务
        null_time_tasks = self.find_null_time_tasks()
        
        if not null_time_tasks:
            print("✅ 没有发现需要修复的任务！")
            return
        
        print(f"\n📋 需要修复的任务:")
        for task in null_time_tasks:
            print(f"   - {task['task_name']} (ID: {task['task_id'][:8]}...)")
        
        print(f"\n🔧 开始修复...")
        
        # 2. 修复每个任务
        results = []
        for task_info in null_time_tasks:
            try:
                result = self.fix_task_time(task_info)
                results.append(result)
            except Exception as e:
                print(f"      ❌ 修复失败: {e}")
                results.append({
                    'success': False,
                    'task_name': task_info['task_name'],
                    'error': str(e)
                })
        
        # 3. 总结结果
        print(f"\n" + "=" * 60)
        successful_fixes = [r for r in results if r['success']]
        failed_fixes = [r for r in results if not r['success']]
        
        print(f"📊 修复结果:")
        print(f"   - 总任务数: {len(null_time_tasks)}")
        print(f"   - 成功修复: {len(successful_fixes)}")
        print(f"   - 修复失败: {len(failed_fixes)}")
        
        if successful_fixes:
            print(f"\n✅ 成功修复的任务:")
            for result in successful_fixes:
                print(f"   - {result['task_name']}: 设置时间为 {result['inferred_time']}")
        
        if failed_fixes:
            print(f"\n❌ 修复失败的任务:")
            for result in failed_fixes:
                print(f"   - {result['task_name']}: {result['error']}")
        
        if len(successful_fixes) == len(null_time_tasks):
            print(f"\n🎉 所有任务修复完成！")
        else:
            print(f"\n⚠️  部分任务修复失败，需要手动检查。")
        
        return results


if __name__ == "__main__":
    fixer = NullTimeFieldFixer()
    results = fixer.run_fix()
