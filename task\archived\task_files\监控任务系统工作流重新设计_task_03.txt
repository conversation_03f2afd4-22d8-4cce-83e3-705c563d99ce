# 任务03：任务创建流程重构

## 📋 任务概述

**任务目标**: 重构任务创建流程，从URL池选择链接创建监控任务，支持定时调度配置
**预计工期**: 3-4天
**优先级**: 高
**状态**: 准备开始

## 🎯 具体需求

### 功能需求
1. **任务创建向导界面**
   - 分步骤的任务创建流程
   - 从URL池选择链接
   - 任务基本信息配置
   - 定时调度设置
   - 创建确认和预览

2. **URL选择功能**
   - 从URL池中选择要监控的链接
   - 支持筛选和搜索
   - 批量选择和预览
   - 选择结果统计

3. **任务配置表单**
   - 任务名称和描述
   - 监控平台选择
   - 优先级设置
   - 执行参数配置

4. **定时调度配置**
   - 执行频率选择（一次性、每日、每周、自定义）
   - 执行时间设置
   - 高级调度选项
   - 调度规则验证

### 技术需求
- 前端：React + Ant Design Steps组件
- 后端：扩展监控任务API
- 数据：任务与URL的关联关系
- 验证：表单验证和数据校验

## 🏗️ 技术实现方案

### 前端实现
```typescript
// 任务创建向导组件
interface TaskCreateWizardProps {
  onSuccess: (task: MonitoringTask) => void;
  onCancel: () => void;
}

// 创建步骤
enum CreateStep {
  SELECT_URLS = 0,    // 选择URL
  BASIC_CONFIG = 1,   // 基础配置
  SCHEDULE_CONFIG = 2, // 调度配置
  CONFIRM = 3         // 确认创建
}

// 任务创建数据
interface TaskCreateData {
  selectedUrls: UrlPoolItem[];
  basicConfig: TaskBasicConfig;
  scheduleConfig: ScheduleConfig;
}
```

### 后端API设计
```python
# 任务创建API
@router.post("/monitoring-tasks/create-from-urls")
async def create_task_from_urls(
    request: TaskCreateFromUrlsRequest
):
    """从URL池创建监控任务"""
    
    # 1. 验证URL存在性
    # 2. 创建监控任务
    # 3. 建立URL关联关系
    # 4. 配置定时调度
    # 5. 返回创建结果

# URL选择预览API
@router.post("/monitoring-tasks/preview-urls")
async def preview_selected_urls(
    url_ids: List[str]
):
    """预览选中的URL信息"""
```

### 数据模型设计
```python
class TaskCreateFromUrlsRequest(BaseModel):
    name: str
    description: Optional[str]
    url_ids: List[str]
    platform: str
    priority: TaskPriority
    schedule: ScheduleConfig
    config: TaskConfig

class ScheduleConfig(BaseModel):
    type: ScheduleType  # once, daily, weekly, custom
    enabled: bool
    start_time: Optional[datetime]
    interval: Optional[int]
    cron_expression: Optional[str]
    timezone: str = "Asia/Shanghai"
```

## 📝 实施步骤

### 步骤1：后端任务创建API扩展
1. **扩展监控任务模型**
   ```python
   # backend/app/models/monitoring_task.py
   class MonitoringTaskCreate(BaseModel):
       name: str
       description: Optional[str]
       url_ids: List[str]
       schedule: ScheduleConfig
       config: TaskConfig
   ```

2. **实现任务创建服务**
   ```python
   # backend/app/services/task_service.py
   class TaskService:
       async def create_task_from_urls(self, request: TaskCreateFromUrlsRequest)
       async def validate_urls(self, url_ids: List[str])
       async def create_url_associations(self, task_id: str, url_ids: List[str])
   ```

3. **创建任务创建API**
   ```python
   # backend/app/api/task_create_routes.py
   @router.post("/create-from-urls")
   async def create_task_from_urls(...)
   ```

### 步骤2：前端任务创建向导开发
1. **创建向导主组件**
   ```typescript
   // frontend/src/components/TaskCreateWizard/index.tsx
   const TaskCreateWizard: React.FC<TaskCreateWizardProps> = ({ onSuccess, onCancel }) => {
     // 向导逻辑实现
   };
   ```

2. **URL选择步骤组件**
   ```typescript
   // frontend/src/components/TaskCreateWizard/UrlSelectionStep.tsx
   const UrlSelectionStep: React.FC<UrlSelectionStepProps> = ({ onNext, onPrev }) => {
     // URL选择逻辑
   };
   ```

3. **基础配置步骤组件**
   ```typescript
   // frontend/src/components/TaskCreateWizard/BasicConfigStep.tsx
   const BasicConfigStep: React.FC<BasicConfigStepProps> = ({ onNext, onPrev }) => {
     // 基础配置表单
   };
   ```

4. **调度配置步骤组件**
   ```typescript
   // frontend/src/components/TaskCreateWizard/ScheduleConfigStep.tsx
   const ScheduleConfigStep: React.FC<ScheduleConfigStepProps> = ({ onNext, onPrev }) => {
     // 调度配置表单
   };
   ```

### 步骤3：任务创建页面集成
1. **创建任务创建页面**
   ```typescript
   // frontend/src/pages/TaskCreate/index.tsx
   const TaskCreatePage: React.FC = () => {
     // 页面逻辑实现
   };
   ```

2. **集成到路由系统**
   ```typescript
   // frontend/src/App.tsx
   <Route path="/tasks/create" element={<TaskCreatePage />} />
   ```

3. **添加导航菜单项**
   ```typescript
   // 在URL池管理页面添加"创建任务"按钮
   ```

### 步骤4：定时调度集成
1. **Celery Beat配置**
   ```python
   # backend/app/services/scheduler_service.py
   class SchedulerService:
       async def create_periodic_task(self, task_id: str, schedule: ScheduleConfig)
       async def update_periodic_task(self, task_id: str, schedule: ScheduleConfig)
       async def delete_periodic_task(self, task_id: str)
   ```

2. **调度任务管理**
   ```python
   # 动态创建和管理Celery Beat任务
   ```

### 步骤5：测试和验证
1. **单元测试**
   - 任务创建API测试
   - URL关联功能测试
   - 调度配置测试

2. **集成测试**
   - 端到端任务创建流程测试
   - 定时调度功能测试
   - 错误处理测试

3. **用户体验测试**
   - 向导流程易用性测试
   - 表单验证测试
   - 响应性能测试

## 📊 验收标准

### 功能验收
- [ ] 支持从URL池选择链接创建任务
- [ ] 分步骤的向导式创建流程
- [ ] 完整的任务配置选项
- [ ] 灵活的定时调度设置
- [ ] 创建前的预览和确认
- [ ] 创建成功后的结果展示

### 性能验收
- [ ] 向导步骤切换响应时间<200ms
- [ ] URL选择支持1000+URL流畅操作
- [ ] 任务创建响应时间<2秒
- [ ] 表单验证实时响应

### 用户体验验收
- [ ] 向导流程直观易懂
- [ ] 表单验证友好明确
- [ ] 错误信息清晰有用
- [ ] 操作反馈及时准确

## 🔧 开发环境准备

### 后端依赖
```python
# requirements.txt 可能需要的新依赖
celery[redis]>=5.3.0
django-celery-beat>=2.5.0  # 如果使用Django
croniter>=1.4.0  # 用于cron表达式解析
```

### 前端依赖
```json
// package.json 可能需要的新依赖
{
  "dependencies": {
    "react-cron-generator": "^1.4.0",  // cron表达式生成器
    "moment": "^2.29.0",  // 时间处理
    "react-beautiful-dnd": "^13.1.0"  // 拖拽排序（可选）
  }
}
```

## 🚀 部署注意事项

1. **数据库迁移**
   - 任务与URL关联表的创建
   - 调度配置字段的添加

2. **Celery配置**
   - Beat调度器的配置
   - 任务队列的设置

3. **时区处理**
   - 服务器时区配置
   - 用户时区转换

4. **权限控制**
   - 任务创建权限验证
   - URL访问权限检查

## 📈 后续优化方向

1. **功能扩展**
   - 任务模板功能
   - 批量任务创建
   - 任务复制和克隆

2. **用户体验**
   - 拖拽式URL选择
   - 智能调度建议
   - 创建历史记录

3. **高级功能**
   - 条件触发任务
   - 任务依赖关系
   - 动态参数配置

---

**负责人**: AI Assistant  
**创建时间**: 2025-07-04  
**预计开始**: 2025-07-04  
**预计完成**: 2025-07-07  
**当前状态**: 准备开始
