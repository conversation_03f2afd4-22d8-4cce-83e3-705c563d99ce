@echo off
echo ========================================
echo    MonIt 混合开发环境启动脚本
echo ========================================
echo.

echo [1/4] 检查Docker服务状态...
docker ps --format "table {{.Names}}\t{{.Status}}" | findstr "monit-"
if %ERRORLEVEL% neq 0 (
    echo ❌ Docker服务未运行，请先启动Docker服务
    echo 运行: docker-compose -f docker-compose.dev.yml up -d timescaledb redis
    pause
    exit /b 1
)

echo.
echo [2/4] 检查conda环境...
call conda activate monit
if %ERRORLEVEL% neq 0 (
    echo ❌ conda环境 'monit' 不存在
    echo 请先创建环境: conda create -n monit python=3.11
    pause
    exit /b 1
)

echo ✅ conda环境已激活

echo.
echo [3/4] 启动后端服务 (端口8001)...
start "MonIt Backend" cmd /k "conda activate monit && cd /d %~dp0.. && python simple_backend.py"

echo.
echo [4/4] 启动前端服务 (端口3000)...
start "MonIt Frontend" cmd /k "cd /d %~dp0..\frontend && npm start"

echo.
echo ========================================
echo ✅ 开发环境启动完成！
echo ========================================
echo.
echo 🌐 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:8001
echo 📊 Grafana:  http://localhost:3001
echo 🔍 Kibana:   http://localhost:5601
echo.
echo 按任意键退出...
pause > nul
