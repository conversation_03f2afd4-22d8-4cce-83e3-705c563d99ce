<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl4AI API 测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            min-height: 80vh;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .server-config {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .server-config h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }

        .api-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .api-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }

        .api-header:hover {
            background: #e9ecef;
        }

        .api-header h4 {
            color: #2c3e50;
            font-size: 1em;
        }

        .api-header .badge {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .api-content {
            padding: 20px;
            display: none;
        }

        .api-content.active {
            display: block;
        }

        .config-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .config-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .config-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .config-panel {
            display: none;
        }

        .config-panel.active {
            display: block;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .result-area {
            margin-top: 20px;
        }

        .result-tabs {
            display: flex;
            margin-bottom: 10px;
        }

        .result-tab {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            cursor: pointer;
            font-size: 13px;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }

        .result-tab.active {
            background: white;
            border-bottom: 1px solid white;
        }

        .result-content {
            border: 1px solid #e9ecef;
            border-radius: 0 4px 4px 4px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-panel {
            display: none;
            padding: 15px;
        }

        .result-panel.active {
            display: block;
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        .status-pending {
            background: #ffc107;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        /* 任务类型选择器样式 */
        .task-type-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .task-type-option {
            position: relative;
        }

        .task-type-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        .task-type-label {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;
        }

        .task-type-label:hover {
            border-color: #1976d2;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
        }

        .task-type-option input[type="radio"]:checked + .task-type-label {
            border-color: #1976d2;
            background: #f3f8ff;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        }

        .task-type-icon {
            font-size: 24px;
            margin-right: 15px;
            min-width: 40px;
            text-align: center;
        }

        .task-type-content h6 {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .task-type-content p {
            margin: 0;
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .task-config-panel {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 5px;
        }

        .advanced-config {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }

        .advanced-config h5 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 0.9em;
        }

        .row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .col {
            flex: 1;
        }

        .col-2 {
            flex: 0 0 50%;
        }

        .col-3 {
            flex: 0 0 33.333%;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            border: 1px solid #c3e6cb;
        }

        .monitor-controls {
            display: flex;
            gap: 10px;
        }

        .monitor-controls .btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .stat-card {
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .progress-container {
            position: relative;
        }

        .monitor-log {
            font-family: 'Courier New', monospace;
            line-height: 1.4;
        }

        .monitor-log div {
            margin-bottom: 2px;
            word-wrap: break-word;
        }

        .queue-stats {
            border-left: 4px solid #667eea;
        }

        .task-list table {
            font-size: 13px;
        }

        .task-list th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .task-list tr:nth-child(even) {
            background: #f8f9fa;
        }

        .task-list tr:hover {
            background: #e3f2fd;
        }

        .advanced-config {
            border-left: 3px solid #667eea;
        }

        .advanced-config h5 {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group small {
            display: block;
            margin-top: 4px;
            font-style: italic;
        }

        /* 监控显示模式控制样式 */
        /* 进度条模式：只显示统计和进度，隐藏详细信息 */
        .monitor-display-mode-progress .display-detailed {
            display: none !important;
        }

        /* 静默模式：隐藏统计、进度和详细信息 */
        .monitor-display-mode-quiet .display-stats,
        .monitor-display-mode-quiet .display-progress,
        .monitor-display-mode-quiet .display-detailed {
            display: none !important;
        }

        /* 详细信息模式：显示所有内容（默认行为）*/
        .monitor-display-mode-detailed .display-stats,
        .monitor-display-mode-detailed .display-progress,
        .monitor-display-mode-detailed .display-detailed {
            display: block !important;
        }

        /* 统计信息网格布局 */
        .monitor-stats.display-stats {
            display: grid !important;
        }

        /* 进度条容器布局 */
        .progress-container.display-progress {
            display: block !important;
        }

        /* 详细信息列表布局 */
        .display-detailed {
            display: block !important;
        }

        /* 当取消勾选"显示进度"时，隐藏进度相关内容 */
        .progress-disabled .display-progress {
            display: none !important;
        }

        .progress-disabled .monitor-stats .stat-value,
        .progress-disabled .monitor-stats .stat-label {
            opacity: 0.5;
        }

        /* 配置管理导航栏样式 */
        .config-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 12px 0;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .config-navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .config-navbar-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-navbar-actions {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .config-navbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 12px;
            border-right: 1px solid rgba(255,255,255,0.2);
        }

        .config-navbar-group:last-child {
            border-right: none;
        }

        .config-navbar .btn {
            padding: 6px 12px;
            font-size: 13px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 4px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .config-navbar .btn:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-1px);
            color: white;
        }

        .config-navbar .btn-primary {
            background: rgba(40, 167, 69, 0.8);
            border-color: rgba(40, 167, 69, 0.8);
        }

        .config-navbar .btn-primary:hover {
            background: rgba(40, 167, 69, 1);
            border-color: rgba(40, 167, 69, 1);
        }

        .config-navbar .btn-warning {
            background: rgba(255, 193, 7, 0.8);
            border-color: rgba(255, 193, 7, 0.8);
            color: #212529;
        }

        .config-navbar .btn-warning:hover {
            background: rgba(255, 193, 7, 1);
            border-color: rgba(255, 193, 7, 1);
            color: #212529;
        }

        .config-status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            color: rgba(255,255,255,0.9);
            font-size: 12px;
            padding: 4px 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .config-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 滑动开关样式 */
        .clear-protection {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255,255,255,0.3);
            transition: 0.3s;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.5);
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .toggle-switch input:checked + .toggle-slider {
            background-color: rgba(220, 53, 69, 0.8);
            border-color: rgba(220, 53, 69, 1);
        }

        .toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(20px);
            background-color: #fff;
        }

        .toggle-switch input:focus + .toggle-slider {
            box-shadow: 0 0 1px rgba(220, 53, 69, 0.8);
        }

        .clear-btn-disabled {
            opacity: 0.5;
            cursor: not-allowed !important;
            pointer-events: none;
        }

        .clear-protection-label {
            font-size: 11px;
            color: rgba(255,255,255,0.8);
            user-select: none;
        }



        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }

            .row {
                flex-direction: column;
            }

            .monitor-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .monitor-controls {
                flex-direction: column;
            }

            .config-navbar-content {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .config-navbar-actions {
                justify-content: center;
            }

            .config-navbar-group {
                border-right: none;
                border-bottom: 1px solid rgba(255,255,255,0.2);
                padding: 8px 0;
                justify-content: center;
            }

            .config-navbar-group:last-child {
                border-bottom: none;
            }

            .config-navbar .btn {
                font-size: 12px;
                padding: 5px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Crawl4AI API 测试工具</h1>
            <p>可视化测试 Crawl4AI 的所有 API 功能</p>
        </div>

        <!-- 配置管理导航栏 -->
        <div class="config-navbar">
            <div class="config-navbar-content">
                <div class="config-navbar-title">
                    <span>💾</span>
                    <span>配置管理</span>
                </div>

                <div class="config-navbar-actions">
                    <!-- 基础操作组 -->
                    <div class="config-navbar-group">
                        <button class="btn btn-primary" onclick="autoSaveAllConfigs()" title="保存当前所有配置">
                            💾 保存配置
                        </button>
                        <button class="btn" onclick="loadSavedConfig()" title="恢复已保存的配置">
                            🔄 恢复配置
                        </button>
                    </div>

                    <!-- 导入导出组 -->
                    <div class="config-navbar-group">
                        <button class="btn" onclick="exportConfig()" title="导出配置到文件">
                            📤 导出
                        </button>
                        <button class="btn" onclick="importConfig()" title="从文件导入配置">
                            📥 导入
                        </button>
                    </div>

                    <!-- 清空操作组 -->
                    <div class="config-navbar-group">
                        <div class="clear-protection">
                            <label class="toggle-switch">
                                <input type="checkbox" id="clearProtectionSwitch" onchange="toggleClearButton()">
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="clear-protection-label">解锁</span>
                            <button class="btn btn-warning clear-btn-disabled" id="clearConfigBtn" onclick="clearAllConfigs()" title="先打开开关才能清空配置">
                                🗑️ 清空
                            </button>
                        </div>
                    </div>

                    <!-- 状态指示器 -->
                    <div class="config-navbar-group">
                        <div class="config-status-indicator">
                            <div class="config-status-dot"></div>
                            <span id="configStatusNavbar">自动保存</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <!-- 服务器配置 -->
                <div class="server-config">
                    <h3>🔧 服务器配置</h3>
                    <div class="form-group">
                        <label for="serverUrl">服务器地址</label>
                        <input type="text" id="serverUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                    </div>
                    <div class="form-group">
                        <label for="apiKey">API Key (可选)</label>
                        <input type="password" id="apiKey" placeholder="输入API密钥">
                    </div>
                    <div class="form-group">
                        <label for="timeout">请求超时 (秒)</label>
                        <input type="number" id="timeout" value="30" min="5" max="300">
                    </div>
                    <button class="btn btn-success" onclick="testConnection()">测试连接</button>
                    <div id="connectionStatus"></div>


                </div>

                <!-- 配置模块列表 -->
                <div class="api-section">
                    <div class="api-header" onclick="showSection('crawler-config')">
                        <h4>🤖 爬虫配置</h4>
                        <span class="badge">crawler</span>
                    </div>
                    <div class="api-content" id="crawler-config-info">
                        <p>配置浏览器行为、反爬虫、页面导航等底层设置</p>
                    </div>
                </div>

                <div class="api-section">
                    <div class="api-header" onclick="showSection('extraction-config')">
                        <h4>🎯 数据提取配置</h4>
                        <span class="badge">extraction</span>
                    </div>
                    <div class="api-content" id="extraction-info">
                        <p>配置LLM提取、CSS选择器、结构化提取等数据处理方式</p>
                    </div>
                </div>

                <div class="api-section">
                    <div class="api-header" onclick="showSection('task-config')">
                        <h4>⚙️ 任务配置</h4>
                        <span class="badge">task</span>
                    </div>
                    <div class="api-content" id="task-info">
                        <p>选择任务执行方式：单次爬取、批量爬取、深度爬取</p>
                    </div>
                </div>

                <div class="api-section">
                    <div class="api-header" onclick="showSection('html-processing-config')">
                        <h4>📄 HTML处理</h4>
                        <span class="badge">independent</span>
                    </div>
                    <div class="api-content" id="html-processing-info">
                        <p>独立的HTML文本处理模块，与爬虫无关</p>
                    </div>
                </div>


            </div>

            <div class="content-area">
                <!-- 爬虫配置 -->
                <div id="crawler-config" class="config-section" style="display: none;">
                    <h2>🤖 爬虫配置</h2>
                    <p style="color: #6c757d; margin-bottom: 20px;">配置浏览器行为、反爬虫、页面导航等底层爬虫设置</p>

                    <div class="config-tabs">
                        <button class="config-tab active" onclick="switchTab('crawler', 'basic')">基础设置</button>
                        <button class="config-tab" onclick="switchTab('crawler', 'browser')">浏览器环境</button>
                        <button class="config-tab" onclick="switchTab('crawler', 'antibot')">反反爬</button>
                        <button class="config-tab" onclick="switchTab('crawler', 'navigation')">页面导航</button>
                        <button class="config-tab" onclick="switchTab('crawler', 'cache')">缓存策略</button>
                        <button class="config-tab" onclick="switchTab('crawler', 'media')">媒体处理</button>
                        <button class="config-tab" onclick="switchTab('crawler', 'debug')">调试监控</button>
                    </div>

                    <!-- 基础设置面板 -->
                    <div id="crawler-basic-panel" class="config-panel active">
                        <div class="form-group">
                            <label for="crawlerMethod">HTTP方法</label>
                            <select id="crawlerMethod">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                            </select>
                        </div>

                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="crawlerVerbose" checked>
                                <label for="crawlerVerbose">详细输出</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="crawlerCheckRobots">
                                <label for="crawlerCheckRobots">检查robots.txt</label>
                            </div>
                        </div>
                    </div>

                    <!-- 浏览器环境面板 -->
                    <div id="crawler-browser-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>🌐 用户代理配置</h5>
                            <div class="form-group">
                                <label for="crawlerUserAgent">用户代理</label>
                                <select id="crawlerUserAgent" onchange="toggleCustomUserAgent()">
                                    <option value="">默认</option>
                                    <option value="random">随机</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>

                            <div class="form-group" id="customUserAgentGroup" style="display: none;">
                                <label for="crawlerCustomUserAgent">自定义用户代理</label>
                                <input type="text" id="crawlerCustomUserAgent" placeholder="Mozilla/5.0...">
                            </div>

                            <div class="form-group">
                                <label for="crawlerUserAgentMode">用户代理模式</label>
                                <select id="crawlerUserAgentMode">
                                    <option value="">默认</option>
                                    <option value="random">随机模式</option>
                                    <option value="rotate">轮换模式</option>
                                </select>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🌍 地理位置配置</h5>
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerLocale">语言环境</label>
                                        <input type="text" id="crawlerLocale" placeholder="en-US">
                                        <small>如: en-US, zh-CN</small>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerTimezone">时区ID</label>
                                        <input type="text" id="crawlerTimezone" placeholder="America/New_York">
                                        <small>如: Asia/Shanghai</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🔒 代理配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerEnableProxy" onchange="toggleProxyConfig()">
                                    <label for="crawlerEnableProxy">启用代理</label>
                                </div>
                            </div>

                            <div id="proxyConfig" style="display: none; margin-top: 15px;">
                                <div class="form-group">
                                    <label for="crawlerProxyServer">代理服务器</label>
                                    <input type="text" id="crawlerProxyServer" placeholder="http://proxy.example.com:8080">
                                </div>
                                <div class="row">
                                    <div class="col-2">
                                        <div class="form-group">
                                            <label for="crawlerProxyUsername">用户名</label>
                                            <input type="text" id="crawlerProxyUsername" placeholder="可选">
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="form-group">
                                            <label for="crawlerProxyPassword">密码</label>
                                            <input type="password" id="crawlerProxyPassword" placeholder="可选">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="crawlerFetchSSL">
                                <label for="crawlerFetchSSL">获取SSL证书信息</label>
                            </div>
                        </div>
                    </div>

                    <!-- 反反爬面板 -->
                    <div id="crawler-antibot-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>🤖 用户行为模拟</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerSimulateUser">
                                    <label for="crawlerSimulateUser">模拟用户行为</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerMagic">
                                    <label for="crawlerMagic">启用Magic模式</label>
                                </div>
                            </div>
                            <small style="color: #6c757d;">模拟真实用户的鼠标移动、点击等行为，Magic模式自动处理弹窗和覆盖层</small>
                        </div>

                        <div class="advanced-config">
                            <h5>🎭 浏览器伪装</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerOverrideNavigator">
                                    <label for="crawlerOverrideNavigator">覆盖Navigator属性</label>
                                </div>
                            </div>
                            <small style="color: #6c757d;">修改浏览器的navigator对象，隐藏自动化特征</small>
                        </div>

                        <div class="advanced-config">
                            <h5>🚫 页面元素处理</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerRemoveOverlay">
                                    <label for="crawlerRemoveOverlay">移除覆盖元素</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerIgnoreBodyVisibility">
                                    <label for="crawlerIgnoreBodyVisibility">忽略body可见性</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerAdjustViewport">
                                    <label for="crawlerAdjustViewport">自适应视口</label>
                                </div>
                            </div>
                            <small style="color: #6c757d;">自动处理弹窗、广告覆盖层等干扰元素</small>
                        </div>
                    </div>

                    <!-- 页面导航面板 -->
                    <div id="crawler-navigation-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>⏱️ 等待条件</h5>
                            <div class="form-group">
                                <label for="crawlerWaitUntil">导航等待条件</label>
                                <select id="crawlerWaitUntil">
                                    <option value="domcontentloaded">DOM内容加载完成</option>
                                    <option value="load">页面完全加载</option>
                                    <option value="networkidle">网络空闲</option>
                                    <option value="commit">导航提交</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="crawlerWaitFor">等待元素/条件</label>
                                <input type="text" id="crawlerWaitFor" placeholder="css:.content-loaded 或 js:() => document.readyState === 'complete'">
                                <small style="color: #6c757d;">CSS选择器(css:)或JavaScript函数(js:)</small>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerWaitForImages">
                                    <label for="crawlerWaitForImages">等待图片加载</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>⏰ 时间控制</h5>
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerPageTimeout">页面超时 (毫秒)</label>
                                        <input type="number" id="crawlerPageTimeout" value="60000" min="5000" max="300000">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerDelayBeforeReturn">返回前延迟 (秒)</label>
                                        <input type="number" id="crawlerDelayBeforeReturn" value="0.1" min="0" max="10" step="0.1">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📜 JavaScript执行</h5>
                            <div class="form-group">
                                <label for="crawlerJsCode">JavaScript代码</label>
                                <textarea id="crawlerJsCode" rows="4" placeholder="window.scrollTo(0, document.body.scrollHeight);"></textarea>
                                <small style="color: #6c757d;">在页面加载后执行的JavaScript代码</small>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerJsOnly">
                                    <label for="crawlerJsOnly">仅JS驱动更新</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📄 页面滚动</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerScanFullPage">
                                    <label for="crawlerScanFullPage">扫描整页</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerProcessIframes">
                                    <label for="crawlerProcessIframes">处理iframe</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="crawlerScrollDelay">滚动延迟 (秒)</label>
                                <input type="number" id="crawlerScrollDelay" value="0.2" min="0.1" max="2" step="0.1">
                                <small style="color: #6c757d;">滚动步骤间的延迟时间</small>
                            </div>
                        </div>
                    </div>

                    <!-- 缓存策略面板 -->
                    <div id="crawler-cache-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>💾 缓存模式</h5>
                            <div class="form-group">
                                <label for="crawlerCacheMode">缓存模式</label>
                                <select id="crawlerCacheMode">
                                    <option value="BYPASS">绕过缓存</option>
                                    <option value="ENABLED">启用缓存</option>
                                    <option value="DISABLED">禁用缓存</option>
                                    <option value="READ_ONLY">只读缓存</option>
                                    <option value="WRITE_ONLY">只写缓存</option>
                                </select>
                                <small style="color: #6c757d;">控制爬虫的缓存行为</small>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🔗 会话管理</h5>
                            <div class="form-group">
                                <label for="crawlerSessionId">会话ID</label>
                                <input type="text" id="crawlerSessionId" placeholder="persistent_session">
                                <small style="color: #6c757d;">用于保持浏览器会话状态，多次爬取间共享cookies等</small>
                            </div>
                        </div>
                    </div>

                    <!-- 媒体处理面板 -->
                    <div id="crawler-media-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>📸 截图配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerScreenshot">
                                    <label for="crawlerScreenshot">生成截图</label>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerScreenshotWait">截图前等待 (秒)</label>
                                        <input type="number" id="crawlerScreenshotWait" value="0" min="0" max="10" step="0.1">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerScreenshotHeight">高度阈值 (像素)</label>
                                        <input type="number" id="crawlerScreenshotHeight" value="20000" min="1000" max="50000">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📄 文档生成</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerPdf">
                                    <label for="crawlerPdf">生成PDF</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerMhtml">
                                    <label for="crawlerMhtml">捕获MHTML</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🖼️ 图片处理</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerExcludeExternalImages">
                                    <label for="crawlerExcludeExternalImages">排除外部图片</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerExcludeAllImages">
                                    <label for="crawlerExcludeAllImages">排除所有图片</label>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerImageScore">图片分数阈值</label>
                                        <input type="number" id="crawlerImageScore" value="3" min="1" max="10">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="crawlerImageDescWords">图片描述最小词数</label>
                                        <input type="number" id="crawlerImageDescWords" value="50" min="10" max="200">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📊 表格处理</h5>
                            <div class="form-group">
                                <label for="crawlerTableScore">表格分数阈值</label>
                                <input type="number" id="crawlerTableScore" value="7" min="1" max="10">
                                <small style="color: #6c757d;">表格处理的质量阈值</small>
                            </div>
                        </div>
                    </div>

                    <!-- 调试监控面板 -->
                    <div id="crawler-debug-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>🔍 网络监控</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerCaptureNetwork">
                                    <label for="crawlerCaptureNetwork">捕获网络请求</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerCaptureConsole">
                                    <label for="crawlerCaptureConsole">捕获控制台消息</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📝 日志配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="crawlerLogConsole">
                                    <label for="crawlerLogConsole">记录浏览器控制台</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="generateCrawlerConfig()">📋 生成配置</button>
                        <button class="btn btn-danger" onclick="clearCrawlerConfig()">🗑️ 清空配置</button>
                    </div>

                    <!-- 配置预览区域 -->
                    <div class="result-area">
                        <div class="result-tabs">
                            <div class="result-tab active" onclick="switchResultTab('crawler', 'config')">配置预览</div>
                            <div class="result-tab" onclick="switchResultTab('crawler', 'help')">配置说明</div>
                        </div>

                        <div class="result-content">
                            <div id="crawler-config-panel" class="result-panel active">
                                <div class="json-viewer" id="crawlerConfigViewer">点击"生成配置"查看爬虫配置</div>
                            </div>
                            <div id="crawler-help-panel" class="result-panel">
                                <div class="json-viewer">
                                    <h4>🤖 爬虫配置说明</h4>
                                    <p style="color: #e67e22; margin-bottom: 15px;"><strong>🎯 配置范围</strong>：爬虫配置专注于"如何爬取"，不包含URL设置（URL在任务配置中指定）</p>
                                    <p><strong>基础设置</strong>：配置HTTP方法、详细输出等基本选项</p>
                                    <p><strong>浏览器环境</strong>：配置用户代理、地理位置、代理等浏览器身份</p>
                                    <p><strong>反反爬</strong>：配置用户行为模拟、浏览器伪装等反检测功能</p>
                                    <p><strong>页面导航</strong>：配置页面等待、JavaScript执行、滚动等导航行为</p>
                                    <p><strong>缓存策略</strong>：配置缓存模式和会话管理</p>
                                    <p><strong>媒体处理</strong>：配置截图、PDF、图片等媒体处理选项</p>
                                    <p><strong>调试监控</strong>：配置网络监控、日志记录等调试功能</p>
                                    <br>
                                    <p style="color: #1976d2;"><strong>💡 提示</strong>：爬虫配置是底层设置，会被所有任务类型使用。URL在任务配置中指定。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务配置 -->
                <div id="task-config" class="config-section" style="display: none;">
                    <h2>⚙️ 任务配置</h2>
                    <p style="color: #6c757d; margin-bottom: 20px;">选择任务执行方式并配置相关参数，结合爬虫配置和数据提取配置执行任务</p>

                    <!-- 任务类型选择 -->
                    <div class="advanced-config" style="margin-bottom: 30px;">
                        <h5>🎯 任务类型选择</h5>
                        <div class="task-type-selector">
                            <div class="task-type-option">
                                <input type="radio" id="taskTypeSingle" name="taskType" value="single" checked onchange="switchTaskType()">
                                <label for="taskTypeSingle" class="task-type-label">
                                    <div class="task-type-icon">🎯</div>
                                    <div class="task-type-content">
                                        <h6>单次爬取</h6>
                                        <p>爬取单个URL，适用于测试和简单场景</p>
                                    </div>
                                </label>
                            </div>
                            <div class="task-type-option">
                                <input type="radio" id="taskTypeBatch" name="taskType" value="batch" onchange="switchTaskType()">
                                <label for="taskTypeBatch" class="task-type-label">
                                    <div class="task-type-icon">📦</div>
                                    <div class="task-type-content">
                                        <h6>批量爬取</h6>
                                        <p>并发爬取多个URL，支持调度器和监控</p>
                                    </div>
                                </label>
                            </div>
                            <div class="task-type-option">
                                <input type="radio" id="taskTypeDeep" name="taskType" value="deep" onchange="switchTaskType()">
                                <label for="taskTypeDeep" class="task-type-label">
                                    <div class="task-type-icon">🔍</div>
                                    <div class="task-type-content">
                                        <h6>深度爬取</h6>
                                        <p>从起始URL开始深度爬取整个网站</p>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 单次爬取配置 -->
                    <div id="task-single-config" class="task-config-panel">
                        <div class="advanced-config">
                            <h5>🎯 单次爬取配置</h5>
                            <div class="form-group">
                                <label for="singleTaskUrl">目标URL</label>
                                <input type="url" id="singleTaskUrl" value="https://example.com" placeholder="https://example.com">
                                <small style="color: #6c757d;">要爬取的单个URL地址</small>
                            </div>

                            <div style="background: #f5f5f5; padding: 15px; border-radius: 4px; margin-top: 15px;">
                                <p style="margin: 0; color: #666; font-size: 14px;">
                                    <strong>💡 说明</strong>：单次爬取将使用您配置的爬虫设置和数据提取设置来处理指定的URL。
                                    这是最简单的任务类型，适用于测试配置和处理单个页面。
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 批量爬取配置 -->
                    <div id="task-batch-config" class="task-config-panel" style="display: none;">
                        <div class="config-tabs">
                            <button class="config-tab active" onclick="switchTab('task-batch', 'urls')">URL列表</button>
                            <button class="config-tab" onclick="switchTab('task-batch', 'concurrency')">并发控制</button>
                            <button class="config-tab" onclick="switchTab('task-batch', 'scheduler')">调度器</button>
                            <button class="config-tab" onclick="switchTab('task-batch', 'monitor')">监控</button>
                        </div>

                        <!-- URL列表面板 -->
                        <div id="task-batch-urls-panel" class="config-panel active">
                            <div class="form-group">
                                <label for="batchTaskUrls">URL列表</label>
                                <textarea id="batchTaskUrls" rows="8" placeholder="每行一个URL">https://example.com/page1
https://example.com/page2
https://example.com/page3
https://example.com/page4
https://example.com/page5</textarea>
                                <small style="color: #6c757d;">每行输入一个URL，支持批量爬取</small>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="batchTaskStream">
                                    <label for="batchTaskStream">启用流式处理</label>
                                </div>
                            </div>
                        </div>

                        <!-- 并发控制面板 -->
                        <div id="task-batch-concurrency-panel" class="config-panel" style="display: none;">
                            <div class="advanced-config">
                                <h5>⚡ 并发控制</h5>
                                <div class="row">
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="batchSemaphoreCount">并发数量</label>
                                            <input type="number" id="batchSemaphoreCount" value="5" min="1" max="20">
                                            <small>同时处理的URL数量</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="batchMeanDelay">平均延迟(秒)</label>
                                            <input type="number" id="batchMeanDelay" value="0.1" min="0" max="10" step="0.1">
                                            <small>请求间的平均延迟</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="batchMaxRange">随机延迟范围(秒)</label>
                                            <input type="number" id="batchMaxRange" value="0.3" min="0" max="5" step="0.1">
                                            <small>随机延迟的最大范围</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 调度器面板 -->
                        <div id="task-batch-scheduler-panel" class="config-panel" style="display: none;">
                            <div class="advanced-config">
                                <h5>📋 调度器配置</h5>
                                <div class="checkbox-group" style="margin-bottom: 15px;">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="batchEnableScheduler" onchange="toggleBatchSchedulerConfig()">
                                        <label for="batchEnableScheduler"><strong>启用高级调度器</strong></label>
                                    </div>
                                </div>

                                <div id="batchSchedulerConfig" style="display: none;">
                                    <div class="form-group">
                                        <label for="batchSchedulerType">调度器类型</label>
                                        <select id="batchSchedulerType">
                                            <option value="AsyncQueueManager">异步队列管理器</option>
                                            <option value="MemoryAdaptiveScheduler">内存自适应调度器</option>
                                        </select>
                                    </div>

                                    <div class="row">
                                        <div class="col-2">
                                            <div class="form-group">
                                                <label for="batchPoolSize">线程池大小</label>
                                                <input type="number" id="batchPoolSize" value="10" min="1" max="50">
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="form-group">
                                                <label for="batchMemoryThreshold">内存阈值(MB)</label>
                                                <input type="number" id="batchMemoryThreshold" value="1024" min="512" max="8192">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 监控面板 -->
                        <div id="task-batch-monitor-panel" class="config-panel" style="display: none;">
                            <div class="advanced-config">
                                <h5>📊 监控配置</h5>
                                <div class="checkbox-group" style="margin-bottom: 15px;">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="batchEnableMonitor" onchange="toggleBatchMonitorConfig()">
                                        <label for="batchEnableMonitor"><strong>启用监控</strong></label>
                                    </div>
                                </div>

                                <div id="batchMonitorConfig" style="display: none;">
                                    <div class="form-group">
                                        <label for="batchDisplayMode">显示模式</label>
                                        <select id="batchDisplayMode" onchange="updateMonitorDisplayMode()">
                                            <option value="progress">进度条</option>
                                            <option value="detailed">详细信息</option>
                                            <option value="quiet">静默模式</option>
                                        </select>
                                    </div>

                                    <div class="checkbox-group">
                                        <div class="checkbox-item">
                                            <input type="checkbox" id="batchShowProgress" checked onchange="updateMonitorDisplayMode()">
                                            <label for="batchShowProgress">显示进度</label>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" id="batchLogErrors" checked>
                                            <label for="batchLogErrors">记录错误</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 深度爬取配置 -->
                    <div id="task-deep-config" class="task-config-panel" style="display: none;">
                        <div class="config-tabs">
                            <button class="config-tab active" onclick="switchTab('task-deep', 'basic')">基础配置</button>
                            <button class="config-tab" onclick="switchTab('task-deep', 'strategy')">爬取策略</button>
                            <button class="config-tab" onclick="switchTab('task-deep', 'filters')">过滤规则</button>
                        </div>

                        <!-- 基础配置面板 -->
                        <div id="task-deep-basic-panel" class="config-panel active">
                            <div class="form-group">
                                <label for="deepTaskStartUrl">起始URL</label>
                                <input type="url" id="deepTaskStartUrl" value="https://example.com" placeholder="https://example.com">
                                <small style="color: #6c757d;">深度爬取的起始页面</small>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepTaskMaxDepth">最大深度</label>
                                        <input type="number" id="deepTaskMaxDepth" value="3" min="1" max="10">
                                        <small>爬取的最大层级深度</small>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepTaskMaxPages">最大页面数</label>
                                        <input type="number" id="deepTaskMaxPages" value="50" min="1" max="1000">
                                        <small>总共爬取的最大页面数</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepTaskConcurrency">并发数</label>
                                        <input type="number" id="deepTaskConcurrency" value="3" min="1" max="10">
                                        <small>同时爬取的页面数</small>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepTaskDelay">页面间延迟(秒)</label>
                                        <input type="number" id="deepTaskDelay" value="1" min="0" max="10" step="0.1">
                                        <small>请求间的延迟时间</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 爬取策略面板 -->
                        <div id="task-deep-strategy-panel" class="config-panel" style="display: none;">
                            <div class="form-group">
                                <label for="deepTaskStrategy">爬取策略</label>
                                <select id="deepTaskStrategy">
                                    <option value="BreadthFirstSearchStrategy">广度优先搜索</option>
                                    <option value="DepthFirstSearchStrategy">深度优先搜索</option>
                                    <option value="BestFirstSearchStrategy">最佳优先搜索</option>
                                </select>
                                <small style="color: #6c757d;">选择深度爬取的遍历策略</small>
                            </div>
                        </div>

                        <!-- 过滤规则面板 -->
                        <div id="task-deep-filters-panel" class="config-panel" style="display: none;">
                            <div class="advanced-config">
                                <h5>🔗 URL过滤规则</h5>
                                <div class="form-group">
                                    <label for="deepTaskUrlPattern">URL模式</label>
                                    <input type="text" id="deepTaskUrlPattern" placeholder="https://example.com/articles/*">
                                    <small>只爬取匹配此模式的URL，支持通配符*</small>
                                </div>

                                <div class="form-group">
                                    <label for="deepTaskExcludePatterns">排除模式</label>
                                    <textarea id="deepTaskExcludePatterns" rows="3" placeholder="每行一个模式">*/admin/*
*/login/*
*/logout/*</textarea>
                                    <small>排除匹配这些模式的URL</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务执行按钮 -->
                    <div class="form-group" style="margin-top: 30px;">
                        <button class="btn btn-primary" onclick="executeTask()">🚀 执行任务</button>
                        <button class="btn btn-secondary" onclick="generateTaskConfig()">📋 生成配置</button>
                        <button class="btn btn-danger" onclick="clearTaskConfig()">🗑️ 清空配置</button>
                    </div>

                    <!-- 配置预览区域 -->
                    <div class="result-area">
                        <div class="result-tabs">
                            <div class="result-tab active" onclick="switchResultTab('task', 'config')">任务配置</div>
                            <div class="result-tab" onclick="switchResultTab('task', 'status')">执行状态</div>
                            <div class="result-tab" onclick="switchResultTab('task', 'monitor')">实时监控</div>
                            <div class="result-tab" onclick="downloadTaskResults()">执行结果</div>
                        </div>

                        <div class="result-content">
                            <div id="task-config-panel" class="result-panel active">
                                <div class="json-viewer" id="taskConfigViewer">点击"生成配置"查看任务配置</div>
                            </div>
                            <div id="task-status-panel" class="result-panel">
                                <div class="json-viewer" id="taskStatusViewer">任务执行状态将在这里显示</div>
                            </div>
                            <div id="task-monitor-panel" class="result-panel">
                                <!-- 任务监控面板 -->
                                <div id="taskMonitorContainer">
                                    <div class="monitor-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                        <h4 style="margin: 0; color: #2c3e50;">📊 任务执行监控</h4>
                                        <div class="monitor-controls">
                                            <button class="btn btn-secondary" onclick="pauseTaskQueue()" id="taskPauseBtn" style="display: none;">⏸️ 暂停</button>
                                            <button class="btn btn-success" onclick="resumeTaskQueue()" id="taskResumeBtn" style="display: none;">▶️ 恢复</button>
                                            <button class="btn btn-danger" onclick="stopTaskQueue()" id="taskStopBtn" style="display: none;">⏹️ 停止</button>
                                            <button class="btn btn-primary" onclick="downloadTaskResults()" id="taskDownloadBtn" style="display: none;">💾 下载结果</button>
                                        </div>
                                    </div>

                                    <!-- 统计信息 -->
                                    <div class="monitor-stats display-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
                                        <div class="stat-card" style="background: #f8f9fa; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div class="stat-value" id="taskTotalTasks" style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">0</div>
                                            <div class="stat-label" style="font-size: 0.8em; color: #6c757d;">总任务数</div>
                                        </div>
                                        <div class="stat-card" style="background: #e3f2fd; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div class="stat-value" id="taskActiveTasks" style="font-size: 1.5em; font-weight: bold; color: #1976d2;">0</div>
                                            <div class="stat-label" style="font-size: 0.8em; color: #6c757d;">进行中</div>
                                        </div>
                                        <div class="stat-card" style="background: #fff3e0; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div class="stat-value" id="taskQueuedTasks" style="font-size: 1.5em; font-weight: bold; color: #f57c00;">0</div>
                                            <div class="stat-label" style="font-size: 0.8em; color: #6c757d;">队列中</div>
                                        </div>
                                        <div class="stat-card" style="background: #e8f5e8; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div class="stat-value" id="taskCompletedTasks" style="font-size: 1.5em; font-weight: bold; color: #2e7d32;">0</div>
                                            <div class="stat-label" style="font-size: 0.8em; color: #6c757d;">已完成</div>
                                        </div>
                                        <div class="stat-card" style="background: #ffebee; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div class="stat-value" id="taskFailedTasks" style="font-size: 1.5em; font-weight: bold; color: #c62828;">0</div>
                                            <div class="stat-label" style="font-size: 0.8em; color: #6c757d;">失败</div>
                                        </div>
                                        <div class="stat-card" style="background: #f3e5f5; padding: 10px; border-radius: 4px; text-align: center;">
                                            <div class="stat-value" id="taskMemoryUsage" style="font-size: 1.5em; font-weight: bold; color: #7b1fa2;">0%</div>
                                            <div class="stat-label" style="font-size: 0.8em; color: #6c757d;">内存使用</div>
                                        </div>
                                    </div>

                                    <!-- 进度条 -->
                                    <div class="progress-container display-progress" style="margin-bottom: 20px;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                            <span style="font-size: 0.9em; color: #495057;">整体进度</span>
                                            <span id="taskProgressText" style="font-size: 0.9em; color: #495057;">0/0 (0%)</span>
                                        </div>
                                        <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                                            <div id="taskProgressBar" style="background: linear-gradient(90deg, #28a745, #20c997); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>

                                    <!-- 实时日志 -->
                                    <div class="monitor-log display-detailed" style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; height: 200px; overflow-y: auto; padding: 10px; font-family: 'Courier New', monospace; font-size: 12px; margin-bottom: 20px;">
                                        <div id="taskMonitorLog">等待开始任务执行...</div>
                                    </div>

                                    <!-- 详细任务状态列表 (仅在详细信息模式显示) -->
                                    <div class="detailed-task-list display-detailed">
                                        <h5 style="color: #495057; margin-bottom: 10px;">📋 任务状态列表</h5>
                                        <div id="taskDetailedContainer" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px;">
                                            <table style="width: 100%; border-collapse: collapse; font-size: 13px;">
                                                <thead>
                                                    <tr style="background: #e9ecef;">
                                                        <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">状态</th>
                                                        <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">URL</th>
                                                        <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">进度</th>
                                                        <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">耗时</th>
                                                        <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">结果</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="taskDetailedBody">
                                                    <tr>
                                                        <td colspan="5" style="padding: 20px; text-align: center; color: #6c757d;">暂无任务</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>



                <!-- 数据提取配置 -->
                <div id="extraction-config" class="config-section" style="display: none;">
                    <h2>🎯 数据提取配置</h2>
                    <p style="color: #6c757d; margin-bottom: 20px;">配置如何从页面中提取和处理数据，与爬虫配置组合使用。URL和任务执行在任务配置中指定。</p>

                    <div class="config-tabs">
                        <button class="config-tab active" onclick="switchTab('extraction', 'basic')">基础配置</button>
                        <button class="config-tab" onclick="switchTab('extraction', 'llm')">LLM提取</button>
                        <button class="config-tab" onclick="switchTab('extraction', 'css')">CSS选择器</button>
                        <button class="config-tab" onclick="switchTab('extraction', 'schema')">结构化提取</button>
                        <button class="config-tab" onclick="switchTab('extraction', 'content')">内容处理</button>
                        <button class="config-tab" onclick="switchTab('extraction', 'links')">链接过滤</button>
                    </div>

                    <!-- 基础配置面板 -->
                    <div id="extraction-basic-panel" class="config-panel active">
                        <div class="form-group">
                            <label for="extractionStrategy">数据提取策略</label>
                            <select id="extractionStrategy">
                                <option value="auto">自动选择（推荐）</option>
                                <option value="LLMExtractionStrategy">LLM提取策略</option>
                                <option value="CosineStrategy">余弦相似度策略</option>
                                <option value="JsonCssExtractionStrategy">JSON CSS提取策略</option>
                            </select>
                            <small style="color: #6c757d;">自动选择会根据启用的配置智能选择最佳策略</small>
                        </div>

                        <div class="form-group">
                            <label for="extractionChunkingStrategy">内容分块策略</label>
                            <select id="extractionChunkingStrategy">
                                <option value="IdentityChunking">无分块（整体处理）</option>
                                <option value="RegexChunking">正则表达式分块</option>
                                <option value="NlpSentenceChunking">NLP句子分块</option>
                                <option value="TopicSegmentationChunking">主题分段分块</option>
                                <option value="FixedLengthWordChunking">固定长度分块</option>
                                <option value="SlidingWindowChunking">滑动窗口分块</option>
                                <option value="OverlappingWindowChunking">重叠窗口分块</option>
                            </select>
                            <small style="color: #6c757d;">控制如何将页面内容分割成块进行处理。选择"无分块"将整个页面作为单个块处理</small>
                        </div>

                        <div class="form-group">
                            <label for="extractionMarkdownGenerator">Markdown生成策略</label>
                            <select id="extractionMarkdownGenerator">
                                <option value="DefaultMarkdownGenerator">默认Markdown生成器</option>
                                <option value="CustomMarkdownGenerator">自定义Markdown生成器</option>
                            </select>
                            <small style="color: #6c757d;">控制如何将HTML内容转换为Markdown格式</small>
                        </div>

                        <div class="advanced-config">
                            <h5>🔧 执行选项</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionVerbose" checked>
                                    <label for="extractionVerbose">详细输出</label>
                                    <small style="color: #6c757d; display: block;">输出详细的提取过程信息，便于调试</small>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionBypassCache">
                                    <label for="extractionBypassCache">绕过缓存</label>
                                    <small style="color: #6c757d; display: block;">强制重新提取，不使用缓存的结果</small>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config" style="margin-bottom: 20px;">
                            <h5>📋 配置说明</h5>
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; font-size: 14px; line-height: 1.6;">
                                <p style="color: #e67e22; margin-bottom: 15px;"><strong>🎯 配置范围</strong>：数据提取配置专注于"如何提取数据"，不包含URL和任务执行（在任务配置中指定）</p>
                                <p><strong>模块职责分工：</strong></p>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li><strong>爬虫配置</strong>：控制如何获取页面（浏览器、反爬虫、导航等）</li>
                                    <li><strong>数据提取配置</strong>：控制如何从页面中提取和处理数据</li>
                                    <li><strong>任务配置</strong>：控制执行方式和目标URL（单次、批量、深度爬取）</li>
                                </ul>
                                <p><strong>推荐配置组合：</strong></p>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li><strong>基础提取</strong>：仅基础配置 + 内容处理</li>
                                    <li><strong>智能提取</strong>：LLM提取 + 结构化验证</li>
                                    <li><strong>精确提取</strong>：CSS选择器 + 内容处理</li>
                                    <li><strong>混合提取</strong>：LLM + CSS + 结构化</li>
                                </ul>
                                <p style="margin: 10px 0 0 0; color: #1976d2;"><strong>💡 提示</strong>：先配置爬虫设置，再配置数据提取，最后在任务配置中指定URL并执行</p>
                            </div>
                        </div>
                    </div>

                    <!-- LLM提取面板 -->
                    <div id="extraction-llm-panel" class="config-panel" style="display: none;">
                        <div class="checkbox-group" style="margin-bottom: 15px;">
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableLLMExtraction" onchange="toggleLLMExtractionConfig()">
                                <label for="enableLLMExtraction"><strong>启用LLM提取</strong></label>
                            </div>
                        </div>

                        <div id="llmExtractionConfig" style="display: none;">
                            <div class="form-group">
                                <label for="extractionQuery">提取查询</label>
                                <textarea id="extractionQuery" rows="3" placeholder="简洁描述要提取的数据类型和字段">提取页面中所有文章的标题、作者和发布时间</textarea>
                                <small style="color: #6c757d;">💡 简洁描述你想要提取什么数据，例如："提取所有产品的名称、价格和描述"</small>
                            </div>

                            <div class="form-group">
                                <label for="extractionProvider">LLM提供商</label>
                                <select id="extractionProvider">
                                    <option value="openai">OpenAI</option>
                                    <option value="anthropic">Anthropic</option>
                                    <option value="google">Google</option>
                                    <option value="ollama">Ollama (本地)</option>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="extractionModel">模型名称</label>
                                        <input type="text" id="extractionModel" value="gpt-4" placeholder="gpt-4">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="extractionApiKey">API密钥</label>
                                        <input type="password" id="extractionApiKey" placeholder="输入LLM API密钥">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="extractionBaseUrl">Base URL (可选)</label>
                                <input type="url" id="extractionBaseUrl" placeholder="https://api.openai.com/v1 (留空使用默认)">
                                <small style="color: #6c757d;">💡 自定义API端点，适用于代理服务或私有部署</small>
                            </div>

                            <div class="advanced-config">
                                <h5>🔧 高级LLM配置</h5>
                                <div class="row">
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="extractionTemperature">温度</label>
                                            <input type="number" id="extractionTemperature" value="0.1" min="0" max="2" step="0.1">
                                            <small>控制输出的随机性</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="extractionMaxTokens">最大令牌数</label>
                                            <input type="number" id="extractionMaxTokens" value="4000" min="100" max="8000">
                                            <small>生成内容的最大长度</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="extractionTopP">Top P</label>
                                            <input type="number" id="extractionTopP" value="0.9" min="0" max="1" step="0.1">
                                            <small>核采样参数</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CSS选择器面板 -->
                    <div id="extraction-css-panel" class="config-panel" style="display: none;">
                        <div class="checkbox-group" style="margin-bottom: 15px;">
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableCSSExtraction" onchange="toggleCSSExtractionConfig()">
                                <label for="enableCSSExtraction"><strong>启用CSS选择器提取</strong></label>
                            </div>
                        </div>

                        <div id="cssExtractionConfig" style="display: none;">
                            <div class="form-group">
                                <label for="extractionCssSelectors">CSS选择器配置</label>
                                <textarea id="extractionCssSelectors" rows="8" placeholder="输入JSON格式的CSS选择器配置">{
  "title": "h1, .title, .headline",
  "content": ".content, .article-body, main p",
  "author": ".author, .byline, [data-author]",
  "date": ".date, .publish-date, time",
  "links": "a[href]",
  "images": "img[src]"
}</textarea>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionIncludeText" checked>
                                    <label for="extractionIncludeText">包含文本内容</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionIncludeAttributes">
                                    <label for="extractionIncludeAttributes">包含元素属性</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionCleanText" checked>
                                    <label for="extractionCleanText">清理文本</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 结构化提取面板 -->
                    <div id="extraction-schema-panel" class="config-panel" style="display: none;">
                        <div class="checkbox-group" style="margin-bottom: 15px;">
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableSchemaExtraction" onchange="toggleSchemaExtractionConfig()">
                                <label for="enableSchemaExtraction"><strong>启用结构化提取</strong></label>
                            </div>
                        </div>

                        <div id="schemaExtractionConfig" style="display: none;">
                            <div class="form-group">
                                <label for="extractionSchema">数据结构定义 (JSON Schema)</label>
                                <textarea id="extractionSchema" rows="12" placeholder="定义要提取的数据结构">{
  "type": "object",
  "properties": {
    "articles": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "title": {"type": "string"},
          "author": {"type": "string"},
          "date": {"type": "string"},
          "content": {"type": "string"},
          "url": {"type": "string"},
          "tags": {
            "type": "array",
            "items": {"type": "string"}
          }
        },
        "required": ["title", "content"]
      }
    }
  }
}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="extractionInstructions">提取指令</label>
                                <textarea id="extractionInstructions" rows="4" placeholder="详细说明如何提取和格式化数据">请从网页中提取所有文章信息，包括标题、作者、发布日期、内容摘要和相关标签。

具体要求：
1. 确保数据格式严格符合上述JSON Schema定义
2. 日期统一转换为YYYY-MM-DD格式
3. 如果字段缺失，请用null表示
4. 内容摘要限制在200字以内
5. 标签以数组形式存储，去除重复项</textarea>
                                <small style="color: #6c757d;">💡 详细说明数据提取的具体规则、格式要求和异常处理方式</small>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionValidateSchema" checked>
                                    <label for="extractionValidateSchema">验证Schema</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="extractionReturnRaw">
                                    <label for="extractionReturnRaw">返回原始数据</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容处理面板 -->
                    <div id="extraction-content-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>📄 内容过滤配置</h5>
                            <div class="row">
                                <div class="col-3">
                                    <div class="form-group">
                                        <label for="contentWordThreshold">词数阈值</label>
                                        <input type="number" id="contentWordThreshold" value="200" min="1">
                                        <small>忽略少于此词数的文本块</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="form-group">
                                        <label for="contentCssSelector">CSS选择器过滤</label>
                                        <input type="text" id="contentCssSelector" placeholder="main, .content, article">
                                        <small>只提取匹配的元素</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contentTargetElements">目标元素选择器</label>
                                <input type="text" id="contentTargetElements" placeholder="h1, h2, p, .article-content">
                                <small>逗号分隔的目标元素CSS选择器列表</small>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🏷️ 标签处理配置</h5>
                            <div class="form-group">
                                <label for="contentExcludedTags">排除标签</label>
                                <input type="text" id="contentExcludedTags" placeholder="nav,footer,aside,script,style" value="nav,footer,aside">
                                <small>逗号分隔的要排除的HTML标签</small>
                            </div>

                            <div class="form-group">
                                <label for="contentExcludedSelector">排除选择器</label>
                                <input type="text" id="contentExcludedSelector" placeholder=".ads, .sidebar, #comments">
                                <small>要排除的CSS选择器</small>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="contentRemoveForms">
                                    <label for="contentRemoveForms">移除表单元素</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📝 文本处理配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="contentOnlyText">
                                    <label for="contentOnlyText">仅提取文本内容</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="contentPrettify">
                                    <label for="contentPrettify">美化HTML输出</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contentParserType">HTML解析器类型</label>
                                <select id="contentParserType">
                                    <option value="lxml">lxml (推荐)</option>
                                    <option value="html.parser">html.parser</option>
                                    <option value="html5lib">html5lib</option>
                                </select>
                                <small>选择HTML解析器，lxml速度最快</small>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🔗 属性处理配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="contentKeepDataAttributes">
                                    <label for="contentKeepDataAttributes">保留data-*属性</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contentKeepAttrs">保留的HTML属性</label>
                                <input type="text" id="contentKeepAttrs" placeholder="href,src,alt,title">
                                <small>逗号分隔的要保留的HTML属性列表</small>
                            </div>
                        </div>
                    </div>

                    <!-- 链接过滤面板 -->
                    <div id="extraction-links-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>🔗 链接过滤配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="linksExcludeExternal">
                                    <label for="linksExcludeExternal">排除外部链接</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="linksExcludeInternal">
                                    <label for="linksExcludeInternal">排除内部链接</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="linksExcludeSocialMedia">
                                    <label for="linksExcludeSocialMedia">排除社交媒体链接</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🌐 域名控制</h5>
                            <div class="form-group">
                                <label for="linksExcludeDomains">排除域名列表</label>
                                <textarea id="linksExcludeDomains" rows="4" placeholder="每行一个域名">example.com
ads.google.com
facebook.com
twitter.com</textarea>
                                <small>要排除的域名列表，每行一个</small>
                            </div>

                            <div class="form-group">
                                <label for="linksSocialMediaDomains">社交媒体域名</label>
                                <textarea id="linksSocialMediaDomains" rows="4" placeholder="每行一个域名">facebook.com
twitter.com
instagram.com
linkedin.com
youtube.com
tiktok.com
pinterest.com
reddit.com</textarea>
                                <small>社交媒体域名列表，每行一个</small>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🖼️ 图片处理</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="linksExcludeExternalImages">
                                    <label for="linksExcludeExternalImages">排除外部图片</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="linksExcludeAllImages">
                                    <label for="linksExcludeAllImages">排除所有图片</label>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="linksImageScore">图片分数阈值</label>
                                        <input type="number" id="linksImageScore" value="3" min="1" max="10">
                                        <small>图片处理的质量阈值</small>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="linksImageDescWords">图片描述最小词数</label>
                                        <input type="number" id="linksImageDescWords" value="50" min="10" max="200">
                                        <small>图片描述的最小词数要求</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📊 表格处理</h5>
                            <div class="form-group">
                                <label for="linksTableScore">表格分数阈值</label>
                                <input type="number" id="linksTableScore" value="7" min="1" max="10">
                                <small>表格处理的质量阈值</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="generateExtractionConfig()">📋 生成配置</button>
                        <button class="btn btn-danger" onclick="clearExtractionConfig()">🗑️ 清空配置</button>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="result-area">
                        <div class="loading" id="extractionLoading">
                            <div class="spinner"></div>
                            <p>正在提取数据...</p>
                        </div>

                        <div class="result-tabs">
                            <div class="result-tab active" onclick="switchResultTab('extraction', 'request')">配置预览</div>
                        </div>

                        <div class="result-content">
                            <div id="extraction-request-panel" class="result-panel active">
                                <div class="json-viewer" id="extractionRequestViewer">点击"生成配置"查看完整的数据提取配置</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 深度爬取配置 -->
                <div id="deep-crawl-config" class="config-section" style="display: none;">
                    <h2>🔍 深度爬取配置</h2>

                    <div class="config-tabs">
                        <button class="config-tab active" onclick="switchTab('deep', 'basic')">基础配置</button>
                        <button class="config-tab" onclick="switchTab('deep', 'strategy')">爬取策略</button>
                        <button class="config-tab" onclick="switchTab('deep', 'filters')">过滤规则</button>
                        <button class="config-tab" onclick="switchTab('deep', 'advanced')">高级配置</button>
                    </div>

                    <!-- 基础配置面板 -->
                    <div id="deep-basic-panel" class="config-panel active">
                        <div class="form-group">
                            <label for="deepStartUrl">起始URL</label>
                            <input type="url" id="deepStartUrl" value="https://example.com" placeholder="https://example.com">
                        </div>

                        <div class="row">
                            <div class="col-2">
                                <div class="form-group">
                                    <label for="deepMaxDepth">最大深度</label>
                                    <input type="number" id="deepMaxDepth" value="3" min="1" max="10">
                                    <small>爬取的最大层级深度</small>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label for="deepMaxPages">最大页面数</label>
                                    <input type="number" id="deepMaxPages" value="50" min="1" max="1000">
                                    <small>总共爬取的最大页面数</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-2">
                                <div class="form-group">
                                    <label for="deepConcurrency">并发数</label>
                                    <input type="number" id="deepConcurrency" value="3" min="1" max="10">
                                    <small>同时爬取的页面数</small>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label for="deepDelay">页面间延迟 (秒)</label>
                                    <input type="number" id="deepDelay" value="1" min="0" max="10" step="0.1">
                                    <small>请求间的延迟时间</small>
                                </div>
                            </div>
                        </div>

                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="deepVerbose" checked>
                                <label for="deepVerbose">详细输出</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="deepBypassCache">
                                <label for="deepBypassCache">绕过缓存</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="deepFollowRedirects" checked>
                                <label for="deepFollowRedirects">跟随重定向</label>
                            </div>
                        </div>
                    </div>

                    <!-- 爬取策略面板 -->
                    <div id="deep-strategy-panel" class="config-panel">
                        <div class="form-group">
                            <label for="deepCrawlStrategy">爬取策略</label>
                            <select id="deepCrawlStrategy">
                                <option value="BreadthFirstSearchStrategy">广度优先搜索</option>
                                <option value="DepthFirstSearchStrategy">深度优先搜索</option>
                                <option value="BestFirstSearchStrategy">最佳优先搜索</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="deepLinkSelector">链接选择器</label>
                            <input type="text" id="deepLinkSelector" value="a[href]" placeholder="a[href], .nav-link, .pagination a">
                            <small>用于查找下一级链接的CSS选择器</small>
                        </div>

                        <div class="form-group">
                            <label for="deepContentSelector">内容选择器</label>
                            <input type="text" id="deepContentSelector" placeholder="article, .content, main">
                            <small>用于提取页面主要内容的CSS选择器</small>
                        </div>

                        <div class="advanced-config">
                            <h5>🎯 最佳优先搜索配置</h5>
                            <div class="form-group">
                                <label for="deepScoringCriteria">评分标准</label>
                                <textarea id="deepScoringCriteria" rows="4" placeholder="描述页面评分标准">优先爬取包含以下关键词的页面：产品、价格、详情、规格。
评分权重：
- 标题匹配：权重 3
- URL路径匹配：权重 2
- 内容匹配：权重 1</textarea>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepMinScore">最小评分</label>
                                        <input type="number" id="deepMinScore" value="0.3" min="0" max="1" step="0.1">
                                        <small>页面的最小评分阈值</small>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepMaxRetries">最大重试次数</label>
                                        <input type="number" id="deepMaxRetries" value="3" min="0" max="10">
                                        <small>失败页面的重试次数</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 过滤规则面板 -->
                    <div id="deep-filters-panel" class="config-panel">
                        <div class="form-group">
                            <label for="deepIncludePatterns">包含模式 (每行一个)</label>
                            <textarea id="deepIncludePatterns" rows="4" placeholder="输入要包含的URL模式，支持正则表达式">.*\/product\/.*
.*\/article\/.*
.*\/news\/.*</textarea>
                            <small>只爬取匹配这些模式的URL</small>
                        </div>

                        <div class="form-group">
                            <label for="deepExcludePatterns">排除模式 (每行一个)</label>
                            <textarea id="deepExcludePatterns" rows="4" placeholder="输入要排除的URL模式，支持正则表达式">.*\.(jpg|jpeg|png|gif|pdf|zip)$
.*\/admin\/.*
.*\/login.*
.*\/logout.*</textarea>
                            <small>排除匹配这些模式的URL</small>
                        </div>

                        <div class="form-group">
                            <label for="deepDomainFilter">域名过滤</label>
                            <select id="deepDomainFilter">
                                <option value="same_domain">仅限同域名</option>
                                <option value="same_subdomain">仅限同子域名</option>
                                <option value="whitelist">白名单域名</option>
                                <option value="no_filter">不限制域名</option>
                            </select>
                        </div>

                        <div class="form-group" id="domainWhitelistGroup" style="display: none;">
                            <label for="deepDomainWhitelist">域名白名单 (每行一个)</label>
                            <textarea id="deepDomainWhitelist" rows="3" placeholder="example.com&#10;subdomain.example.com&#10;partner.com"></textarea>
                        </div>

                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="deepIgnoreRobots">
                                <label for="deepIgnoreRobots">忽略robots.txt</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="deepRespectNoFollow">
                                <label for="deepRespectNoFollow">遵守nofollow标签</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="deepAvoidDuplicates" checked>
                                <label for="deepAvoidDuplicates">避免重复页面</label>
                            </div>
                        </div>
                    </div>

                    <!-- 高级配置面板 -->
                    <div id="deep-advanced-panel" class="config-panel">
                        <div class="advanced-config">
                            <h5>🔧 会话配置</h5>
                            <div class="form-group">
                                <label for="deepSessionId">会话ID</label>
                                <input type="text" id="deepSessionId" placeholder="可选，用于会话管理">
                                <small>用于跨请求保持状态</small>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepMaintainSession">
                                    <label for="deepMaintainSession">维持会话状态</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepHandleCookies" checked>
                                    <label for="deepHandleCookies">处理Cookie</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📊 数据处理</h5>
                            <div class="form-group">
                                <label for="deepDataExtraction">数据提取配置</label>
                                <textarea id="deepDataExtraction" rows="4" placeholder="JSON格式的数据提取配置">{
  "extract_title": true,
  "extract_content": true,
  "extract_links": true,
  "extract_images": false,
  "extract_metadata": true
}</textarea>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepGenerateScreenshots">
                                    <label for="deepGenerateScreenshots">生成截图</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepSaveRawHtml">
                                    <label for="deepSaveRawHtml">保存原始HTML</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepGenerateSitemap">
                                    <label for="deepGenerateSitemap">生成站点地图</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>⚡ 性能优化</h5>
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepTimeout">页面超时 (秒)</label>
                                        <input type="number" id="deepTimeout" value="30" min="5" max="120">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="deepMemoryLimit">内存限制 (MB)</label>
                                        <input type="number" id="deepMemoryLimit" value="1024" min="256" max="4096">
                                    </div>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepOptimizeImages">
                                    <label for="deepOptimizeImages">优化图片加载</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepDisableJavaScript">
                                    <label for="deepDisableJavaScript">禁用JavaScript</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="deepEnableCompression" checked>
                                    <label for="deepEnableCompression">启用压缩</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="executeDeepCrawl()">🔍 开始深度爬取</button>
                        <button class="btn btn-secondary" onclick="generateDeepConfig()">📋 生成配置</button>
                        <button class="btn btn-danger" onclick="clearDeepConfig()">🗑️ 清空配置</button>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="result-area">
                        <div class="loading" id="deepLoading">
                            <div class="spinner"></div>
                            <p>正在深度爬取中...</p>
                        </div>

                        <div class="result-tabs">
                            <div class="result-tab active" onclick="switchResultTab('deep', 'response')">爬取结果</div>
                            <div class="result-tab" onclick="switchResultTab('deep', 'request')">请求结构</div>
                            <div class="result-tab" onclick="switchResultTab('deep', 'sitemap')">站点地图</div>
                            <div class="result-tab" onclick="switchResultTab('deep', 'statistics')">统计信息</div>
                        </div>

                        <div class="result-content">
                            <div id="deep-response-panel" class="result-panel active">
                                <div class="json-viewer" id="deepResponseViewer">点击"开始深度爬取"查看结果</div>
                            </div>
                            <div id="deep-request-panel" class="result-panel">
                                <div class="json-viewer" id="deepRequestViewer">点击"生成配置"查看请求结构</div>
                            </div>
                            <div id="deep-sitemap-panel" class="result-panel">
                                <div class="json-viewer" id="deepSitemapViewer">站点地图将在这里显示</div>
                            </div>
                            <div id="deep-statistics-panel" class="result-panel">
                                <div class="json-viewer" id="deepStatisticsViewer">爬取统计信息将在这里显示</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- HTML处理配置 -->
                <div id="html-processing-config" class="config-section" style="display: none;">
                    <h2>📄 HTML处理</h2>
                    <p style="color: #6c757d; margin-bottom: 20px;">独立的HTML文本处理模块，与爬虫无关</p>

                    <!-- 基础配置 - 始终可见 -->
                    <div class="advanced-config" style="margin-bottom: 20px;">
                        <h5>📝 基础配置</h5>

                        <div class="form-group">
                            <label for="htmlInputMethod">HTML输入方式</label>
                            <select id="htmlInputMethod" onchange="toggleHtmlInputMethod()">
                                <option value="direct">直接输入HTML</option>
                                <option value="file">上传HTML文件</option>
                                <option value="url">从URL获取</option>
                            </select>
                        </div>

                        <!-- 直接输入HTML -->
                        <div id="htmlDirectInput" class="form-group">
                            <label for="htmlContent">HTML内容</label>
                            <textarea id="htmlContent" rows="12" placeholder="粘贴或输入HTML内容..."><!DOCTYPE html>
<html>
<head>
    <title>示例页面</title>
</head>
<body>
    <header>
        <h1>网站标题</h1>
        <nav>
            <a href="/home">首页</a>
            <a href="/about">关于</a>
        </nav>
    </header>
    <main>
        <article>
            <h2>文章标题</h2>
            <p class="author">作者：张三</p>
            <p class="date">发布时间：2024-01-01</p>
            <div class="content">
                <p>这是文章的主要内容...</p>
                <img src="image.jpg" alt="示例图片">
            </div>
        </article>
    </main>
    <footer>
        <p>&copy; 2024 示例网站</p>
    </footer>
</body>
</html></textarea>
                        </div>

                        <!-- 文件上传 -->
                        <div id="htmlFileInput" class="form-group" style="display: none;">
                            <label for="htmlFile">选择HTML文件</label>
                            <input type="file" id="htmlFile" accept=".html,.htm" onchange="handleHtmlFileUpload()">
                            <small>支持.html和.htm文件</small>
                        </div>

                        <!-- URL输入 -->
                        <div id="htmlUrlInput" class="form-group" style="display: none;">
                            <label for="htmlUrl">HTML页面URL</label>
                            <input type="url" id="htmlUrl" placeholder="https://example.com/page.html">
                            <small>将从指定URL获取HTML内容进行处理</small>
                        </div>

                        <div class="form-group">
                            <label for="htmlBaseUrl">基础URL (可选)</label>
                            <input type="url" id="htmlBaseUrl" placeholder="https://example.com">
                            <small>用于解析相对链接和资源路径</small>
                        </div>

                        <div class="form-group">
                            <label for="htmlOutputFormat">输出格式</label>
                            <select id="htmlOutputFormat">
                                <option value="markdown">Markdown</option>
                                <option value="json">JSON</option>
                                <option value="text">纯文本</option>
                                <option value="html">清理后的HTML</option>
                                <option value="structured">结构化数据</option>
                            </select>
                        </div>
                    </div>

                    <!-- 可选功能标签 -->
                    <div class="config-tabs">
                        <button class="config-tab" onclick="toggleHtmlFeaturePanel('processing')">HTML处理</button>
                        <button class="config-tab" onclick="toggleHtmlFeaturePanel('extraction')">内容提取</button>
                        <button class="config-tab" onclick="toggleHtmlFeaturePanel('output')">输出选项</button>
                    </div>

                    <!-- HTML处理功能面板 -->
                    <div id="html-processing-feature-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>🧹 HTML清理</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlRemoveScripts" checked>
                                    <label for="htmlRemoveScripts">移除JavaScript</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlRemoveStyles">
                                    <label for="htmlRemoveStyles">移除CSS样式</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlRemoveComments" checked>
                                    <label for="htmlRemoveComments">移除HTML注释</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlRemoveEmptyTags">
                                    <label for="htmlRemoveEmptyTags">移除空标签</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="htmlExcludeTags">排除标签</label>
                                <input type="text" id="htmlExcludeTags" value="script,style,nav,footer,aside" placeholder="script,style,nav,footer">
                                <small>要完全移除的HTML标签，用逗号分隔</small>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>🔧 内容处理</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlNormalizeWhitespace" checked>
                                    <label for="htmlNormalizeWhitespace">规范化空白字符</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlDecodeEntities" checked>
                                    <label for="htmlDecodeEntities">解码HTML实体</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlFixBrokenTags">
                                    <label for="htmlFixBrokenTags">修复损坏的标签</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlConvertRelativeUrls">
                                    <label for="htmlConvertRelativeUrls">转换相对URL为绝对URL</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📝 文本处理</h5>
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="htmlMinWordCount">最小词数</label>
                                        <input type="number" id="htmlMinWordCount" value="10" min="0" max="1000">
                                        <small>内容块的最小词数</small>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="htmlMaxWordCount">最大词数</label>
                                        <input type="number" id="htmlMaxWordCount" value="10000" min="100" max="50000">
                                        <small>内容块的最大词数</small>
                                    </div>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlPreserveFormatting">
                                    <label for="htmlPreserveFormatting">保留格式</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlExtractTables" checked>
                                    <label for="htmlExtractTables">提取表格</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlExtractLists" checked>
                                    <label for="htmlExtractLists">提取列表</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容提取功能面板 -->
                    <div id="html-extraction-feature-panel" class="config-panel" style="display: none;">
                        <div class="form-group">
                            <label for="htmlContentSelector">主要内容选择器</label>
                            <input type="text" id="htmlContentSelector" placeholder="article, .content, main, #content">
                            <small>用于提取主要内容的CSS选择器</small>
                        </div>

                        <div class="form-group">
                            <label for="htmlTitleSelector">标题选择器</label>
                            <input type="text" id="htmlTitleSelector" value="h1, .title, .headline" placeholder="h1, .title, .headline">
                            <small>用于提取页面标题的CSS选择器</small>
                        </div>

                        <div class="advanced-config">
                            <h5>🎯 特定内容提取</h5>
                            <div class="form-group">
                                <label for="htmlExtractionRules">提取规则 (JSON格式)</label>
                                <textarea id="htmlExtractionRules" rows="8" placeholder="定义要提取的内容规则">{
  "title": {
    "selector": "h1, .title",
    "attribute": "text",
    "required": true
  },
  "author": {
    "selector": ".author, .byline",
    "attribute": "text",
    "required": false
  },
  "date": {
    "selector": ".date, time",
    "attribute": "datetime",
    "fallback_attribute": "text",
    "required": false
  },
  "content": {
    "selector": ".content, article p",
    "attribute": "text",
    "join": "\n",
    "required": true
  },
  "images": {
    "selector": "img",
    "attribute": "src",
    "multiple": true,
    "required": false
  },
  "links": {
    "selector": "a[href]",
    "attribute": "href",
    "multiple": true,
    "required": false
  }
}</textarea>
                            </div>

                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlValidateExtraction">
                                    <label for="htmlValidateExtraction">验证提取结果</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlIncludeMetadata" checked>
                                    <label for="htmlIncludeMetadata">包含元数据</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlExtractStructure">
                                    <label for="htmlExtractStructure">提取页面结构</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 输出选项功能面板 -->
                    <div id="html-output-feature-panel" class="config-panel" style="display: none;">
                        <div class="advanced-config">
                            <h5>📋 Markdown配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlMarkdownIncludeLinks" checked>
                                    <label for="htmlMarkdownIncludeLinks">包含链接</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlMarkdownIncludeImages" checked>
                                    <label for="htmlMarkdownIncludeImages">包含图片</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlMarkdownIncludeTables" checked>
                                    <label for="htmlMarkdownIncludeTables">包含表格</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlMarkdownPreserveFormatting">
                                    <label for="htmlMarkdownPreserveFormatting">保留格式</label>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-config">
                            <h5>📊 JSON配置</h5>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlJsonPrettyPrint" checked>
                                    <label for="htmlJsonPrettyPrint">格式化输出</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlJsonIncludeRaw">
                                    <label for="htmlJsonIncludeRaw">包含原始HTML</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="htmlJsonIncludeStats" checked>
                                    <label for="htmlJsonIncludeStats">包含统计信息</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="htmlCustomTemplate">自定义模板 (可选)</label>
                            <textarea id="htmlCustomTemplate" rows="4" placeholder="自定义输出模板，使用 {{变量名}} 语法">标题: {{title}}
作者: {{author}}
日期: {{date}}

内容:
{{content}}

链接数量: {{links.length}}
图片数量: {{images.length}}</textarea>
                            <small>使用模板语法自定义输出格式</small>
                        </div>
                    </div>

                    <!-- 操作按钮 - 始终可见 -->
                    <div class="form-group" style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="executeHtmlProcess()">📄 开始处理</button>
                        <button class="btn btn-secondary" onclick="generateHtmlConfig()">📋 生成配置</button>
                        <button class="btn btn-danger" onclick="clearHtmlConfig()">🗑️ 清空配置</button>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="result-area">
                        <div class="loading" id="htmlLoading">
                            <div class="spinner"></div>
                            <p>正在处理HTML...</p>
                        </div>

                        <div class="result-tabs">
                            <div class="result-tab active" onclick="switchResultTab('html', 'response')">处理结果</div>
                            <div class="result-tab" onclick="switchResultTab('html', 'request')">请求结构</div>
                            <div class="result-tab" onclick="switchResultTab('html', 'markdown')">Markdown</div>
                            <div class="result-tab" onclick="switchResultTab('html', 'extracted')">提取数据</div>
                            <div class="result-tab" onclick="switchResultTab('html', 'cleaned')">清理后HTML</div>
                        </div>

                        <div class="result-content">
                            <div id="html-response-panel" class="result-panel active">
                                <div class="json-viewer" id="htmlResponseViewer">点击"开始处理"查看结果</div>
                            </div>
                            <div id="html-request-panel" class="result-panel">
                                <div class="json-viewer" id="htmlRequestViewer">点击"生成配置"查看请求结构</div>
                            </div>
                            <div id="html-markdown-panel" class="result-panel">
                                <div class="json-viewer" id="htmlMarkdownViewer">Markdown内容将在这里显示</div>
                            </div>
                            <div id="html-extracted-panel" class="result-panel">
                                <div class="json-viewer" id="htmlExtractedViewer">提取的数据将在这里显示</div>
                            </div>
                            <div id="html-cleaned-panel" class="result-panel">
                                <div class="json-viewer" id="htmlCleanedViewer">清理后的HTML将在这里显示</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 欢迎页面 -->
                <div id="welcome-page" class="config-section">
                    <div style="text-align: center; padding: 50px 20px;">
                        <h2 style="color: #2c3e50; margin-bottom: 20px;">🎉 欢迎使用 Crawl4AI API 测试工具</h2>
                        <p style="font-size: 1.1em; color: #6c757d; margin-bottom: 30px;">
                            这是一个可视化的API测试工具，帮助您轻松测试和调试Crawl4AI的所有功能
                        </p>

                        <div style="background: #f8f9fa; border-radius: 8px; padding: 30px; margin: 30px 0; text-align: left;">
                            <h3 style="color: #495057; margin-bottom: 15px;">🚀 快速开始</h3>
                            <ol style="color: #6c757d; line-height: 1.8;">
                                <li>在左侧配置服务器地址并测试连接</li>
                                <li>选择要测试的API功能</li>
                                <li>配置相关参数</li>
                                <li>点击执行按钮查看结果</li>
                            </ol>
                        </div>

                        <div style="background: #e3f2fd; border-radius: 8px; padding: 20px; margin: 20px 0;">
                            <h4 style="color: #1976d2; margin-bottom: 10px;">💡 功能特性</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: left;">
                                <div>✅ 基础爬取测试</div>
                                <div>✅ 批量爬取测试</div>
                                <div>✅ 数据提取测试</div>
                                <div>✅ 深度爬取测试</div>
                                <div>✅ HTML处理测试</div>
                                <div>✅ 实时结果查看</div>
                            </div>
                        </div>

                        <button class="btn btn-primary" onclick="showSection('crawler-config')" style="margin-top: 20px;">
                            开始配置 →
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'welcome-page';
        let connectionStatus = false;
        let lastTaskResult = null; // 存储最后一次任务执行结果
        let queueMonitorInterval = null;
        let currentBatchJob = null;
        let queueStats = {
            total: 0,
            active: 0,
            queued: 0,
            completed: 0,
            failed: 0,
            memoryUsage: 0
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 确保只显示欢迎页面
            showSection('welcome-page');

            // 初始化用户代理选择器事件（延迟初始化，避免访问不存在的元素）
            setTimeout(() => {
                const basicUserAgent = document.getElementById('basicUserAgent');
                if (basicUserAgent) {
                    basicUserAgent.addEventListener('change', function() {
                        const customGroup = document.getElementById('customUserAgentGroup');
                        if (customGroup) {
                            if (this.value === 'custom') {
                                customGroup.style.display = 'block';
                            } else {
                                customGroup.style.display = 'none';
                            }
                        }
                    });
                }

                // 初始化深度爬取域名过滤配置
                const deepDomainFilter = document.getElementById('deepDomainFilter');
                if (deepDomainFilter) {
                    deepDomainFilter.addEventListener('change', function() {
                        const domainWhitelistGroup = document.getElementById('domainWhitelistGroup');
                        if (domainWhitelistGroup) {
                            if (this.value === 'whitelist') {
                                domainWhitelistGroup.style.display = 'block';
                            } else {
                                domainWhitelistGroup.style.display = 'none';
                            }
                        }
                    });
                }

                // 加载保存的配置
                loadSavedConfig();
            }, 100);
        });

        // 切换侧边栏API部分
        function toggleSection(sectionId) {
            // 隐藏所有配置部分
            const sections = document.querySelectorAll('.config-section');
            sections.forEach(section => section.style.display = 'none');

            // 移除所有API内容的active类
            const apiContents = document.querySelectorAll('.api-content');
            apiContents.forEach(content => content.classList.remove('active'));

            // 显示选中的API内容
            const targetContent = document.getElementById(sectionId);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 显示对应的配置部分
            const configSectionId = sectionId + '-config';
            const configSection = document.getElementById(configSectionId);
            if (configSection) {
                configSection.style.display = 'block';
                currentSection = configSectionId;
            }
        }

        // 显示指定部分
        function showSection(sectionId) {
            // 隐藏所有配置部分
            const sections = document.querySelectorAll('.config-section');
            sections.forEach(section => section.style.display = 'none');

            // 移除所有API内容的active类
            const apiContents = document.querySelectorAll('.api-content');
            apiContents.forEach(content => content.classList.remove('active'));

            // 显示目标部分
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
                currentSection = sectionId;
            }

            // 如果不是欢迎页面，需要初始化相应的配置切换函数
            if (sectionId !== 'welcome-page') {
                setTimeout(() => {
                    initializeSectionToggles(sectionId);
                }, 50);
            }
        }

        // 初始化特定部分的切换函数
        function initializeSectionToggles(sectionId) {
            switch (sectionId) {
                case 'extraction-config':
                    if (typeof toggleLLMExtractionConfig === 'function') toggleLLMExtractionConfig();
                    if (typeof toggleCSSExtractionConfig === 'function') toggleCSSExtractionConfig();
                    if (typeof toggleSchemaExtractionConfig === 'function') toggleSchemaExtractionConfig();
                    break;
                case 'task-config':
                    if (typeof switchTaskType === 'function') switchTaskType();
                    if (typeof toggleBatchSchedulerConfig === 'function') toggleBatchSchedulerConfig();
                    if (typeof toggleBatchMonitorConfig === 'function') toggleBatchMonitorConfig();
                    break;
                case 'html-processing-config':
                    if (typeof toggleHtmlInputMethod === 'function') toggleHtmlInputMethod();
                    break;
            }
        }

        // 切换配置标签
        function switchTab(prefix, tabName) {
            const clickedTab = event.target;
            const targetPanel = document.getElementById(`${prefix}-${tabName}-panel`);

            // 如果是基础配置标签，不做任何操作（始终保持激活）
            if (tabName === 'basic' || tabName === 'url' || tabName === 'urls' || tabName === 'input') {
                return;
            }

            // 检查是否点击的是已经激活的标签
            if (clickedTab.classList.contains('active')) {
                // 取消激活当前标签和面板
                clickedTab.classList.remove('active');
                if (targetPanel) {
                    targetPanel.classList.remove('active');
                    targetPanel.style.display = 'none';
                }
                return;
            }

            // 激活选中的标签和面板
            clickedTab.classList.add('active');
            if (targetPanel) {
                targetPanel.classList.add('active');
                targetPanel.style.display = 'block';
            }

            // 确保基础配置始终激活和显示
            const basicTabName = getBasicTabName(prefix);
            const basicTab = document.querySelector(`#${prefix}-crawl-config .config-tab[onclick*="${basicTabName}"], #${prefix}-config .config-tab[onclick*="${basicTabName}"]`);
            const basicPanel = document.getElementById(`${prefix}-${basicTabName}-panel`);

            if (basicTab && basicPanel) {
                basicTab.classList.add('active');
                basicPanel.classList.add('active');
                basicPanel.style.display = 'block';
            }
        }

        // 获取基础标签名称
        function getBasicTabName(prefix) {
            switch (prefix) {
                case 'basic': return 'url';
                case 'batch': return 'urls';
                case 'extraction': return 'basic';
                case 'deep': return 'basic';
                case 'html': return 'input';
                default: return 'basic';
            }
        }

        // 切换结果标签
        function switchResultTab(prefix, tabName) {
            // 根据prefix确定正确的容器选择器
            let containerSelector;
            if (prefix === 'extraction') {
                containerSelector = '#extraction-config';
            } else if (prefix === 'task') {
                containerSelector = '#task-config';
            } else {
                containerSelector = `#${prefix}-crawl-config`;
            }

            // 移除所有结果标签的active类
            const tabs = document.querySelectorAll(`${containerSelector} .result-tab`);
            tabs.forEach(tab => tab.classList.remove('active'));

            // 隐藏所有结果面板
            const panels = document.querySelectorAll(`${containerSelector} .result-panel`);
            panels.forEach(panel => panel.classList.remove('active'));

            // 激活选中的标签和面板
            if (event && event.target) {
                event.target.classList.add('active');
            }
            const panel = document.getElementById(`${prefix}-${tabName}-panel`);
            if (panel) {
                panel.classList.add('active');
                
                // 如果切换到监控面板，确保任务列表是最新的
                if (prefix === 'task' && tabName === 'monitor') {
                    console.log('🔍 切换到监控面板，检查任务列表状态');
                    const tbody = document.getElementById('taskDetailedBody');
                    if (tbody) {
                        console.log(`🔍 当前任务列表有 ${tbody.children.length} 行`);
                        
                        // 如果有流式任务在进行中，强制刷新任务列表
                        if (window.currentStreamingTaskList && window.currentStreamingTaskList.length > 0) {
                            console.log('🔍 检测到流式任务，强制刷新任务列表');
                            updateTaskDetailedList([...window.currentStreamingTaskList]);
                        }
                    }
                }
            }
        }

        // 测试服务器连接
        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            const statusDiv = document.getElementById('connectionStatus');

            statusDiv.innerHTML = '<div class="loading show"><div class="spinner"></div><p>正在测试连接...</p></div>';

            try {
                // 尝试连接到服务器的健康检查端点
                const response = await fetch(`${serverUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 5000
                });

                if (response.ok) {
                    connectionStatus = true;
                    statusDiv.innerHTML = `
                        <div class="success-message">
                            <span class="status-indicator status-success"></span>
                            连接成功！服务器响应正常
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                connectionStatus = false;
                statusDiv.innerHTML = `
                    <div class="error-message">
                        <span class="status-indicator status-error"></span>
                        连接失败: ${error.message}
                    </div>
                `;
            }
        }

        // 旧的基础爬取配置函数已移除，现在使用新的爬虫配置模块

        // 旧的执行基础爬取函数已移除，现在使用新的爬虫配置模块

        // 旧的清空基础配置函数已移除，现在使用新的爬虫配置模块



        // 数据提取功能相关函数
        // 切换LLM提取配置显示
        function toggleLLMExtractionConfig() {
            const enableLLM = document.getElementById('enableLLMExtraction').checked;
            const llmConfig = document.getElementById('llmExtractionConfig');

            if (enableLLM) {
                llmConfig.style.display = 'block';
            } else {
                llmConfig.style.display = 'none';
            }
        }

        // 切换CSS提取配置显示
        function toggleCSSExtractionConfig() {
            const enableCSS = document.getElementById('enableCSSExtraction').checked;
            const cssConfig = document.getElementById('cssExtractionConfig');

            if (enableCSS) {
                cssConfig.style.display = 'block';
            } else {
                cssConfig.style.display = 'none';
            }
        }

        // 切换Schema提取配置显示
        function toggleSchemaExtractionConfig() {
            const enableSchema = document.getElementById('enableSchemaExtraction').checked;
            const schemaConfig = document.getElementById('schemaExtractionConfig');

            if (enableSchema) {
                schemaConfig.style.display = 'block';
            } else {
                schemaConfig.style.display = 'none';
            }
        }

        // 旧的toggleAdvancedConfig函数已移除，现在使用独立的内容处理和链接过滤面板

        // 任务配置相关函数
        // 切换任务类型
        function switchTaskType() {
            const taskType = document.querySelector('input[name="taskType"]:checked').value;

            // 隐藏所有任务配置面板
            document.getElementById('task-single-config').style.display = 'none';
            document.getElementById('task-batch-config').style.display = 'none';
            document.getElementById('task-deep-config').style.display = 'none';

            // 显示选中的任务配置面板
            document.getElementById(`task-${taskType}-config`).style.display = 'block';
        }

        // 切换批量任务调度器配置
        function toggleBatchSchedulerConfig() {
            const enableScheduler = document.getElementById('batchEnableScheduler').checked;
            const schedulerConfig = document.getElementById('batchSchedulerConfig');

            if (enableScheduler) {
                schedulerConfig.style.display = 'block';
            } else {
                schedulerConfig.style.display = 'none';
            }
        }

        // 切换批量任务监控配置
        function toggleBatchMonitorConfig() {
            const enableMonitor = document.getElementById('batchEnableMonitor').checked;
            const monitorConfig = document.getElementById('batchMonitorConfig');

            if (enableMonitor) {
                monitorConfig.style.display = 'block';
                // 初始化显示模式
                updateMonitorDisplayMode();
            } else {
                monitorConfig.style.display = 'none';
            }
        }

        // 更新监控显示模式 (任务配置版本)
        function updateMonitorDisplayMode() {
            const displayMode = document.getElementById('batchDisplayMode').value;
            const showProgress = document.getElementById('batchShowProgress').checked;
            const monitorContainer = document.getElementById('queueMonitorContainer');
            const taskMonitorContainer = document.getElementById('taskMonitorContainer');
            
            // 更新批量爬取配置页面的监控容器
            if (monitorContainer) {
                // 移除所有模式类
                monitorContainer.classList.remove('monitor-display-mode-progress', 'monitor-display-mode-detailed', 'monitor-display-mode-quiet');
                monitorContainer.classList.remove('progress-disabled');

                // 添加当前模式类
                monitorContainer.classList.add(`monitor-display-mode-${displayMode}`);

                // 处理显示进度选项
                if (!showProgress) {
                    monitorContainer.classList.add('progress-disabled');
                }
            }

            // 更新任务配置页面的监控容器
            if (taskMonitorContainer) {
                updateTaskMonitorDisplay(displayMode, showProgress);
            }

            // 同步到批量配置的监控模式
            const batchMonitorMode = document.getElementById('batchMonitorMode');
            if (batchMonitorMode && !window.syncInProgress) {
                window.syncInProgress = true;
                // 映射任务配置模式到批量配置模式
                const modeMapping = {
                    'progress': 'SIMPLE',
                    'detailed': 'DETAILED', 
                    'quiet': 'MINIMAL'
                };
                batchMonitorMode.value = modeMapping[displayMode] || 'DETAILED';
                window.syncInProgress = false;
            }

            console.log('显示模式已更新:', displayMode, '显示进度:', showProgress);
        }

        // 更新任务监控显示模式
        function updateTaskMonitorDisplay(displayMode, showProgress) {
            const container = document.getElementById('taskMonitorContainer');
            if (!container) return;

            // 获取各个显示元素
            const statsElements = container.querySelectorAll('.display-stats');
            const progressElements = container.querySelectorAll('.display-progress');
            const detailedElements = container.querySelectorAll('.display-detailed');

            // 根据显示模式控制元素显示/隐藏
            if (displayMode === 'detailed') {
                // 详细信息模式：显示所有元素
                statsElements.forEach(el => el.style.display = 'grid');
                progressElements.forEach(el => el.style.display = showProgress ? 'block' : 'none');
                detailedElements.forEach(el => el.style.display = 'block');
            } else if (displayMode === 'progress') {
                // 进度条模式：只显示统计和进度条
                statsElements.forEach(el => el.style.display = 'grid');
                progressElements.forEach(el => el.style.display = 'block');
                detailedElements.forEach(el => el.style.display = 'none');
            } else if (displayMode === 'quiet') {
                // 静默模式：最小化显示
                statsElements.forEach(el => el.style.display = 'none');
                progressElements.forEach(el => el.style.display = 'none');
                detailedElements.forEach(el => el.style.display = 'none');
            }
        }

        // 更新批量爬取监控显示模式（已简化，仅用于向后兼容）
        function updateBatchMonitorDisplayMode() {
            // 这个函数现在主要用于向后兼容，实际监控由updateMonitorDisplayMode处理
            console.log('updateBatchMonitorDisplayMode called - delegating to updateMonitorDisplayMode');
            if (typeof updateMonitorDisplayMode === 'function') {
                updateMonitorDisplayMode();
            }
        }

        // 生成任务配置
        function generateTaskConfig() {
            const taskType = document.querySelector('input[name="taskType"]:checked').value;
            let config = {
                task_type: taskType
            };

            if (taskType === 'single') {
                // 单次爬取配置
                config.url = document.getElementById('singleTaskUrl').value;
                config.method = 'arun';
            } else if (taskType === 'batch') {
                // 批量爬取配置
                const urls = document.getElementById('batchTaskUrls').value
                    .split('\n')
                    .map(url => url.trim())
                    .filter(url => url);

                config.urls = urls;
                config.method = 'arun_many';
                config.semaphore_count = parseInt(document.getElementById('batchSemaphoreCount').value);
                config.stream = document.getElementById('batchTaskStream').checked;
                config.mean_delay = parseFloat(document.getElementById('batchMeanDelay').value);
                config.max_range = parseFloat(document.getElementById('batchMaxRange').value);

                // 调度器配置
                const enableScheduler = document.getElementById('batchEnableScheduler').checked;
                if (enableScheduler) {
                    config.scheduler = {
                        type: document.getElementById('batchSchedulerType').value,
                        pool_size: parseInt(document.getElementById('batchPoolSize').value),
                        memory_threshold: parseInt(document.getElementById('batchMemoryThreshold').value)
                    };
                }

                // 监控配置
                const enableMonitor = document.getElementById('batchEnableMonitor').checked;
                if (enableMonitor) {
                    config.monitor = {
                        display_mode: document.getElementById('batchDisplayMode').value,
                        show_progress: document.getElementById('batchShowProgress').checked,
                        log_errors: document.getElementById('batchLogErrors').checked
                    };
                }
            } else if (taskType === 'deep') {
                // 深度爬取配置
                config.start_url = document.getElementById('deepTaskStartUrl').value;
                config.method = 'deep_crawl';
                config.max_depth = parseInt(document.getElementById('deepTaskMaxDepth').value);
                config.max_pages = parseInt(document.getElementById('deepTaskMaxPages').value);
                config.concurrency = parseInt(document.getElementById('deepTaskConcurrency').value);
                config.delay = parseFloat(document.getElementById('deepTaskDelay').value);
                config.strategy = document.getElementById('deepTaskStrategy').value;

                // URL过滤配置
                const urlPattern = document.getElementById('deepTaskUrlPattern').value.trim();
                if (urlPattern) {
                    config.url_pattern = urlPattern;
                }

                const excludePatterns = document.getElementById('deepTaskExcludePatterns').value.trim();
                if (excludePatterns) {
                    config.exclude_patterns = excludePatterns.split('\n').map(pattern => pattern.trim()).filter(pattern => pattern);
                }
            }

            // 显示配置
            document.getElementById('taskConfigViewer').textContent = JSON.stringify(config, null, 2);

            // 保存配置到localStorage
            localStorage.setItem('crawl4ai_task_config', JSON.stringify(config));

            showMessage('success', '任务配置已生成');
        }

        // 执行任务
        async function executeTask() {
            if (!connectionStatus) {
                alert('请先测试服务器连接！');
                return;
            }

            // 生成任务配置
            generateTaskConfig();

            const taskConfig = JSON.parse(document.getElementById('taskConfigViewer').textContent);
            const taskType = taskConfig.task_type;

            // 获取爬虫配置
            const crawlerConfig = JSON.parse(localStorage.getItem('crawl4ai_crawler_config') || '{}');

            // 获取数据提取配置
            const extractionConfig = JSON.parse(localStorage.getItem('crawl4ai_extraction_config') || '{}');

            // 构建符合后端CrawlRequest格式的请求体
            let crawlRequest;

            if (taskType === 'single') {
                crawlRequest = {
                    urls: [taskConfig.url],
                    browser_config: crawlerConfig.browser_config || {},
                    crawler_config: {
                        ...crawlerConfig,
                        ...extractionConfig,
                        browser_config: undefined  // 移除，因为它在顶层
                    }
                };
            } else if (taskType === 'batch') {
                crawlRequest = {
                    urls: taskConfig.urls,
                    browser_config: crawlerConfig.browser_config || {},
                    crawler_config: {
                        ...crawlerConfig,
                        ...extractionConfig,
                        ...taskConfig,
                        urls: undefined,  // 移除，因为它在顶层
                        browser_config: undefined  // 移除，因为它在顶层
                    }
                };
            } else if (taskType === 'deep') {
                // 深度爬取暂时使用单个URL，后续可以扩展
                crawlRequest = {
                    urls: [taskConfig.start_url],
                    browser_config: crawlerConfig.browser_config || {},
                    crawler_config: {
                        ...crawlerConfig,
                        ...extractionConfig,
                        ...taskConfig,
                        browser_config: undefined  // 移除，因为它在顶层
                    }
                };
            }

            // 显示执行状态
            document.getElementById('taskStatusViewer').textContent = `正在执行${taskType === 'single' ? '单次' : taskType === 'batch' ? '批量' : '深度'}爬取任务...`;

            // 初始化监控面板
            if (taskType === 'batch') {
                // 批量任务监控初始化
                const urlCount = Array.isArray(taskConfig.urls) ? taskConfig.urls.length : 0;
                addTaskLog(`🚀 开始执行批量爬取任务，共 ${urlCount} 个URL`);
                updateTaskStats({
                    total: urlCount,
                    active: 0,
                    queued: urlCount,
                    completed: 0,
                    failed: 0,
                    memoryUsage: 0
                });
                
                // 创建初始任务列表
                const initialTasks = Array.isArray(taskConfig.urls) ? taskConfig.urls.map((url, index) => ({
                    id: index,
                    url: url,
                    status: 'pending',
                    progress: '0%',
                    duration: '-',
                    result: false
                })) : [];
                updateTaskDetailedList(initialTasks);
            } else {
                // 单次任务监控初始化
                addTaskLog(`🚀 开始执行${taskType === 'single' ? '单次' : '深度'}爬取任务`);
                updateTaskStats({
                    total: 1,
                    active: 1,
                    queued: 0,
                    completed: 0,
                    failed: 0,
                    memoryUsage: 0
                });
                
                const initialTask = [{
                    id: 0,
                    url: taskType === 'single' ? taskConfig.url : taskConfig.start_url,
                    status: 'running',
                    progress: '0%',
                    duration: '-',
                    result: false
                }];
                updateTaskDetailedList(initialTask);
            }

            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const apiKey = document.getElementById('apiKey').value;

                const headers = {
                    'Content-Type': 'application/json',
                };

                if (apiKey) {
                    headers['Authorization'] = `Bearer ${apiKey}`;
                }

                // 根据流式处理配置选择端点
                const isStreaming = taskType === 'batch' && document.getElementById('batchTaskStream').checked;
                const endpoint = isStreaming ? '/crawl/stream' : '/crawl';

                addTaskLog(`📡 向服务器发送请求... (${isStreaming ? '流式处理模式' : '标准模式'})`);
                
                const response = await fetch(`${serverUrl}${endpoint}`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(crawlRequest)
                });

                let result;
                if (isStreaming) {
                    // 流式处理：使用ReadableStream处理NDJSON响应
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }
                    
                    // 🔧 修复：初始化流式任务列表
                    if (taskType === 'batch' && Array.isArray(taskConfig.urls)) {
                        // 初始化任务列表为pending状态
                        const initialTaskList = taskConfig.urls.map((url, index) => ({
                            id: index,
                            url: url,
                            status: 'pending',
                            progress: '0%',
                            duration: '等待中...',
                            result: null
                        }));
                        
                        // 存储到全局变量
                        window.streamingTaskList = initialTaskList;
                        window.currentStreamingTaskList = [...initialTaskList];
                        
                        // 立即显示初始任务列表
                        updateTaskDetailedList(initialTaskList);
                        
                        console.log('🔍 流式处理初始化任务列表:', {
                            totalTasks: initialTaskList.length,
                            urls: taskConfig.urls
                        });
                    }
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    const results = [];
                    
                    addTaskLog('🌊 开始处理流式响应...');
                    
                    try {
                        while (true) {
                            const { value, done } = await reader.read();
                            if (done) break;
                            
                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');
                            buffer = lines.pop(); // 保留不完整的行
                            
                            for (const line of lines) {
                                if (line.trim()) {
                                    try {
                                        const data = JSON.parse(line);
                                        if (data.status === 'completed') {
                                            addTaskLog('✅ 流式响应处理完成');
                                            break;
                                        } else if (data.url) {
                                            results.push(data);
                                            addTaskLog(`✅ 收到结果: ${data.url}`);
                                            
                                            // 🔧 修复：添加实时UI更新调用
                                            if (taskType === 'batch' && Array.isArray(taskConfig.urls)) {
                                                // 强制切换到详细显示模式以确保任务列表可见
                                                const displayModeSelect = document.getElementById('batchDisplayMode');
                                                if (displayModeSelect && displayModeSelect.value !== 'detailed') {
                                                    displayModeSelect.value = 'detailed';
                                                    updateMonitorDisplayMode();
                                                }
                                                
                                                console.log(`🔍 流式处理调用 updateStreamingProgress:`, {
                                                    url: data.url,
                                                    totalUrls: taskConfig.urls.length,
                                                    completedResults: results.length
                                                });
                                                
                                                // 调用流式进度更新函数
                                                const progressViewer = document.getElementById('taskProgressViewer');
                                                updateStreamingProgress(data, taskConfig.urls, results, progressViewer, window.streamingTaskList || []);
                                                
                                                // 🔧 修复：实时更新全局变量，支持单个任务下载
                                                window.lastBatchResults = [...results];
                                                window.currentTaskList = [...(window.streamingTaskList || [])];
                                                console.log('🔍 实时更新全局变量:', {
                                                    lastBatchResultsLength: window.lastBatchResults.length,
                                                    currentTaskListLength: window.currentTaskList.length,
                                                    completedTasksCount: window.currentTaskList.filter(t => t.status === 'completed').length
                                                });
                                            }
                                        }
                                    } catch (e) {
                                        // 🔧 修复：改进NDJSON解析错误处理
                                        console.warn('解析NDJSON行失败 - 可能是大数据行:', {
                                            lineLength: line.length,
                                            lineStart: line.substring(0, 100),
                                            error: e.message
                                        });
                                        
                                        // 尝试检查是否是简单的结果对象（没有完整HTML）
                                        if (line.includes('"url":') && line.includes('"html":')) {
                                            try {
                                                // 尝试提取URL并创建简化的结果对象
                                                const urlMatch = line.match(/"url":\s*"([^"]+)"/);
                                                if (urlMatch) {
                                                    const simpleResult = {
                                                        url: urlMatch[1],
                                                        html: '(大数据已省略)',
                                                        success: true,
                                                        timestamp: new Date().toISOString()
                                                    };
                                                    results.push(simpleResult);
                                                    addTaskLog(`✅ 收到结果 (简化): ${simpleResult.url}`);
                                                    
                                                    // 更新UI
                                                    if (taskType === 'batch' && Array.isArray(taskConfig.urls)) {
                                                        const displayModeSelect = document.getElementById('batchDisplayMode');
                                                        if (displayModeSelect && displayModeSelect.value !== 'detailed') {
                                                            displayModeSelect.value = 'detailed';
                                                            updateMonitorDisplayMode();
                                                        }
                                                        
                                                                                                                 const progressViewer = document.getElementById('taskProgressViewer');
                                                         updateStreamingProgress(simpleResult, taskConfig.urls, results, progressViewer, window.streamingTaskList || []);
                                                         
                                                         // 🔧 修复：实时更新全局变量，支持单个任务下载
                                                         window.lastBatchResults = [...results];
                                                         window.currentTaskList = [...(window.streamingTaskList || [])];
                                                     }
                                                     return; // 成功处理，不继续错误处理
                                                }
                                            } catch (e2) {
                                                console.warn('简化处理也失败:', e2);
                                            }
                                        }
                                        
                                        addTaskLog(`⚠️ 解析响应行失败: ${line.substring(0, 50)}...`);
                                    }
                                }
                            }
                        }
                    } finally {
                        reader.releaseLock();
                    }
                    
                    result = taskType === 'batch' ? results : (results.length > 0 ? results[0] : null);
                    addTaskLog(`🔍 调试: 流式处理收到 ${results.length} 个结果`);
                    
                    // 🔧 修复：确保流式处理的结果被正确保存
                    console.log('🔍 流式处理结果:', {
                        taskType,
                        resultsLength: results.length,
                        finalResult: result,
                        resultType: typeof result
                    });
                } else {
                    // 标准处理：JSON响应
                    result = await response.json();
                }

                if (response.ok) {
                    document.getElementById('taskStatusViewer').textContent = '任务执行完成';
                    lastTaskResult = result; // 存储结果到全局变量
                    
                    // 同时更新 window.lastBatchResults 以保持兼容性
                    if (taskType === 'batch' && Array.isArray(result)) {
                        window.lastBatchResults = result;
                    } else {
                        window.lastBatchResults = [result];
                    }
                    
                    addTaskLog(`🔍 调试: lastTaskResult已设置，类型: ${typeof result}, 长度: ${Array.isArray(result) ? result.length : '非数组'}`);
                    
                    // 更新监控面板为完成状态
                    addTaskLog('✅ 任务执行完成！');
                    
                    if (taskType === 'batch') {
                        const urlCount = Array.isArray(taskConfig.urls) ? taskConfig.urls.length : 0;
                        updateTaskStats({
                            total: urlCount,
                            active: 0,
                            queued: 0,
                            completed: urlCount,
                            failed: 0,
                            memoryUsage: 0
                        });
                        
                        // 更新任务列表为完成状态
                        const completedTasks = Array.isArray(taskConfig.urls) ? taskConfig.urls.map((url, index) => ({
                            id: index,
                            url: url,
                            status: 'completed',
                            progress: '100%',
                            duration: '完成',
                            result: Array.isArray(result) && result[index] ? result[index] : (result || true)
                        })) : [];
                        updateTaskDetailedList(completedTasks);
                        
                        // 保存任务列表到全局变量用于下载
                        window.currentTaskList = completedTasks;
                    } else {
                        updateTaskStats({
                            total: 1,
                            active: 0,
                            queued: 0,
                            completed: 1,
                            failed: 0,
                            memoryUsage: 0
                        });
                        
                        const completedTask = [{
                            id: 0,
                            url: taskType === 'single' ? taskConfig.url : taskConfig.start_url,
                            status: 'completed',
                            progress: '100%',
                            duration: '完成',
                            result: result
                        }];
                        updateTaskDetailedList(completedTask);
                        
                        // 保存任务列表到全局变量用于下载
                        window.currentTaskList = completedTask;
                    }
                    
                    showMessage('success', '任务执行成功！点击"执行结果"下载JSON文件');
                } else {
                    throw new Error(result.error || `HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('taskStatusViewer').textContent = `任务执行失败: ${error.message}`;
                lastTaskResult = null; // 清空结果
                
                // 更新监控面板为失败状态
                addTaskLog(`❌ 任务执行失败: ${error.message}`);
                
                if (taskType === 'batch') {
                    const urlCount = Array.isArray(taskConfig.urls) ? taskConfig.urls.length : 0;
                    updateTaskStats({
                        total: urlCount,
                        active: 0,
                        queued: 0,
                        completed: 0,
                        failed: urlCount,
                        memoryUsage: 0
                    });
                    
                    // 更新任务列表为失败状态
                    const failedTasks = Array.isArray(taskConfig.urls) ? taskConfig.urls.map((url, index) => ({
                        id: index,
                        url: url,
                        status: 'failed',
                        progress: '0%',
                        duration: '失败',
                        result: false
                    })) : [];
                    updateTaskDetailedList(failedTasks);
                    
                    // 保存空的任务列表
                    window.currentTaskList = failedTasks;
                    window.lastBatchResults = [];
                } else {
                    updateTaskStats({
                        total: 1,
                        active: 0,
                        queued: 0,
                        completed: 0,
                        failed: 1,
                        memoryUsage: 0
                    });
                    
                    const failedTask = [{
                        id: 0,
                        url: taskType === 'single' ? taskConfig.url : taskConfig.start_url,
                        status: 'failed',
                        progress: '0%',
                        duration: '失败',
                        result: false
                    }];
                    updateTaskDetailedList(failedTask);
                    
                    // 保存空的任务列表
                    window.currentTaskList = failedTask;
                    window.lastBatchResults = [];
                }
                
                showMessage('error', `任务执行失败: ${error.message}`);
            }
        }

        // 清空任务配置
        function clearTaskConfig() {
            if (confirm('确定要清空所有任务配置吗？')) {
                // 重置任务类型选择
                document.getElementById('taskTypeSingle').checked = true;
                switchTaskType();

                // 重置单次爬取配置
                document.getElementById('singleTaskUrl').value = 'https://example.com';

                // 重置批量爬取配置
                document.getElementById('batchTaskUrls').value = 'https://example.com/page1\nhttps://example.com/page2\nhttps://example.com/page3\nhttps://example.com/page4\nhttps://example.com/page5';
                document.getElementById('batchTaskStream').checked = false;
                document.getElementById('batchSemaphoreCount').value = '5';
                document.getElementById('batchMeanDelay').value = '0.1';
                document.getElementById('batchMaxRange').value = '0.3';
                document.getElementById('batchEnableScheduler').checked = false;
                document.getElementById('batchSchedulerType').value = 'AsyncQueueManager';
                document.getElementById('batchPoolSize').value = '10';
                document.getElementById('batchMemoryThreshold').value = '1024';
                document.getElementById('batchEnableMonitor').checked = false;
                document.getElementById('batchDisplayMode').value = 'progress';
                document.getElementById('batchShowProgress').checked = true;
                document.getElementById('batchLogErrors').checked = true;

                // 重置深度爬取配置
                document.getElementById('deepTaskStartUrl').value = 'https://example.com';
                document.getElementById('deepTaskMaxDepth').value = '3';
                document.getElementById('deepTaskMaxPages').value = '50';
                document.getElementById('deepTaskConcurrency').value = '3';
                document.getElementById('deepTaskDelay').value = '1';
                document.getElementById('deepTaskStrategy').value = 'BreadthFirstSearchStrategy';
                document.getElementById('deepTaskUrlPattern').value = '';
                document.getElementById('deepTaskExcludePatterns').value = '*/admin/*\n*/login/*\n*/logout/*';

                // 隐藏条件显示的面板
                document.getElementById('batchSchedulerConfig').style.display = 'none';
                document.getElementById('batchMonitorConfig').style.display = 'none';

                // 清空结果显示
                document.getElementById('taskConfigViewer').textContent = '点击"生成配置"查看任务配置';
                document.getElementById('taskStatusViewer').textContent = '任务执行状态将在这里显示';
                lastTaskResult = null; // 清空执行结果

                // 清除localStorage
                localStorage.removeItem('crawl4ai_task_config');

                showMessage('success', '任务配置已清空');
            }
        }

        // 下载任务执行结果
        function downloadTaskResults() {
            console.log('🔍 下载调试: lastTaskResult =', lastTaskResult);
            console.log('🔍 下载调试: lastTaskResult类型 =', typeof lastTaskResult);
            console.log('🔍 下载调试: lastTaskResult是否为数组 =', Array.isArray(lastTaskResult));
            if (!lastTaskResult) {
                showMessage('warning', '暂无执行结果可下载，请先执行任务');
                return;
            }

            try {
                // 创建JSON文件内容
                const jsonContent = JSON.stringify(lastTaskResult, null, 2);

                // 创建Blob对象
                const blob = new Blob([jsonContent], { type: 'application/json' });

                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;

                // 生成文件名（使用任务的start_timestamp）
                let timestamp;
                try {
                    // 优先从result.extracted_content[0].task_info.start_timestamp获取时间戳
                    if (lastTaskResult && 
                        lastTaskResult.result && 
                        lastTaskResult.result.extracted_content && 
                        lastTaskResult.result.extracted_content[0] &&
                        lastTaskResult.result.extracted_content[0].task_info &&
                        lastTaskResult.result.extracted_content[0].task_info.start_timestamp) {
                        
                        const startTimestamp = lastTaskResult.result.extracted_content[0].task_info.start_timestamp;
                        timestamp = new Date(startTimestamp).toISOString().replace(/[:.]/g, '-').slice(0, 19);
                    } else {
                        // 备用方案：使用当前时间
                        timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                    }
                } catch (error) {
                    // 如果时间戳解析失败，使用当前时间
                    timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                }
                a.download = `crawl4ai_task_result_${timestamp}.json`;

                // 触发下载
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                // 释放URL对象
                URL.revokeObjectURL(url);

                showMessage('success', '执行结果已下载');
            } catch (error) {
                showMessage('error', `下载失败: ${error.message}`);
            }
        }




        // 清空数据提取配置
        function clearExtractionConfig() {
            if (confirm('确定要清空所有配置吗？')) {
                // 重置基础配置
                document.getElementById('extractionStrategy').value = 'auto';
                document.getElementById('extractionChunkingStrategy').value = 'IdentityChunking';
                document.getElementById('extractionMarkdownGenerator').value = 'DefaultMarkdownGenerator';

                // 取消所有功能启用状态
                document.getElementById('enableLLMExtraction').checked = false;
                document.getElementById('enableCSSExtraction').checked = false;
                document.getElementById('enableSchemaExtraction').checked = false;

                // 隐藏所有可选配置面板
                document.getElementById('llmExtractionConfig').style.display = 'none';
                document.getElementById('cssExtractionConfig').style.display = 'none';
                document.getElementById('schemaExtractionConfig').style.display = 'none';

                // 重置LLM配置
                document.getElementById('extractionQuery').value = '提取页面中所有文章的标题、作者和发布时间';
                document.getElementById('extractionProvider').value = 'openai';
                document.getElementById('extractionModel').value = 'gpt-4';
                document.getElementById('extractionApiKey').value = '';
                document.getElementById('extractionBaseUrl').value = '';
                document.getElementById('extractionTemperature').value = '0.1';
                document.getElementById('extractionMaxTokens').value = '4000';
                document.getElementById('extractionTopP').value = '0.9';

                // 重置CSS配置
                document.getElementById('extractionCssSelectors').value = `{
  "title": "h1, .title, .headline",
  "content": ".content, .article-body, main p",
  "author": ".author, .byline, [data-author]",
  "date": ".date, .publish-date, time",
  "links": "a[href]",
  "images": "img[src]"
}`;
                document.getElementById('extractionIncludeText').checked = true;
                document.getElementById('extractionIncludeAttributes').checked = false;
                document.getElementById('extractionCleanText').checked = true;

                // 重置Schema配置
                document.getElementById('extractionSchema').value = `{
  "type": "object",
  "properties": {
    "articles": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "title": {"type": "string"},
          "author": {"type": "string"},
          "date": {"type": "string"},
          "content": {"type": "string"},
          "url": {"type": "string"},
          "tags": {
            "type": "array",
            "items": {"type": "string"}
          }
        },
        "required": ["title", "content"]
      }
    }
  }
}`;
                document.getElementById('extractionInstructions').value = `请从网页中提取所有文章信息，包括标题、作者、发布日期、内容摘要和相关标签。

具体要求：
1. 确保数据格式严格符合上述JSON Schema定义
2. 日期统一转换为YYYY-MM-DD格式
3. 如果字段缺失，请用null表示
4. 内容摘要限制在200字以内
5. 标签以数组形式存储，去除重复项`;
                document.getElementById('extractionValidateSchema').checked = true;
                document.getElementById('extractionReturnRaw').checked = false;

                // 重置内容处理配置
                document.getElementById('contentWordThreshold').value = '200';
                document.getElementById('contentCssSelector').value = '';
                document.getElementById('contentTargetElements').value = '';
                document.getElementById('contentExcludedTags').value = 'nav,footer,aside';
                document.getElementById('contentExcludedSelector').value = '';
                document.getElementById('contentRemoveForms').checked = false;
                document.getElementById('contentOnlyText').checked = false;
                document.getElementById('contentPrettify').checked = false;
                document.getElementById('contentParserType').value = 'lxml';
                document.getElementById('contentKeepDataAttributes').checked = false;
                document.getElementById('contentKeepAttrs').value = '';

                // 重置链接过滤配置
                document.getElementById('linksExcludeExternal').checked = false;
                document.getElementById('linksExcludeInternal').checked = false;
                document.getElementById('linksExcludeSocialMedia').checked = false;
                document.getElementById('linksExcludeDomains').value = 'example.com\nads.google.com\nfacebook.com\ntwitter.com';
                document.getElementById('linksSocialMediaDomains').value = 'facebook.com\ntwitter.com\ninstagram.com\nlinkedin.com\nyoutube.com\ntiktok.com\npinterest.com\nreddit.com';
                document.getElementById('linksExcludeExternalImages').checked = false;
                document.getElementById('linksExcludeAllImages').checked = false;
                document.getElementById('linksImageScore').value = '3';
                document.getElementById('linksImageDescWords').value = '50';
                document.getElementById('linksTableScore').value = '7';

                // 清空结果显示
                document.getElementById('extractionRequestViewer').textContent = '点击"生成配置"查看请求结构';
                document.getElementById('extractionResponseViewer').textContent = '点击"开始提取"查看结果';
                document.getElementById('extractionStructuredViewer').textContent = '结构化提取数据将在这里显示';
                document.getElementById('extractionRawViewer').textContent = '原始提取数据将在这里显示';

                showMessage('success', '配置已清空');
            }
        }

        // 队列控制函数
        function pauseQueue() {
            if (currentBatchJob) {
                // 这里可以添加暂停队列的逻辑
                addMonitorLog('⏸️ 队列已暂停');
                document.getElementById('pauseBtn').style.display = 'none';
                document.getElementById('resumeBtn').style.display = 'inline-block';
            }
        }

        function resumeQueue() {
            if (currentBatchJob) {
                // 这里可以添加恢复队列的逻辑
                addMonitorLog('▶️ 队列已恢复');
                document.getElementById('pauseBtn').style.display = 'inline-block';
                document.getElementById('resumeBtn').style.display = 'none';
            }
        }

        function stopQueue() {
            if (currentBatchJob) {
                // 这里可以添加停止队列的逻辑
                addMonitorLog('⏹️ 队列已停止');
                document.getElementById('pauseBtn').style.display = 'none';
                document.getElementById('resumeBtn').style.display = 'none';
                document.getElementById('stopBtn').style.display = 'none';
                currentBatchJob = null;
                if (queueMonitorInterval) {
                    clearInterval(queueMonitorInterval);
                    queueMonitorInterval = null;
                }
            }
        }

        // 添加监控日志
        function addMonitorLog(message) {
            const logContainer = document.getElementById('taskMonitorLog');
            if (logContainer) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `<span style="color: #6c757d;">[${timestamp}]</span> ${message}`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }

        // 更新队列统计
        function updateQueueStats(stats) {
            queueStats = { ...queueStats, ...stats };

            document.getElementById('totalTasks').textContent = queueStats.total;
            document.getElementById('activeTasks').textContent = queueStats.active;
            document.getElementById('queuedTasks').textContent = queueStats.queued;
            document.getElementById('completedTasks').textContent = queueStats.completed;
            document.getElementById('failedTasks').textContent = queueStats.failed;
            document.getElementById('memoryUsage').textContent = queueStats.memoryUsage + '%';

            // 更新进度条
            const progress = queueStats.total > 0 ? ((queueStats.completed + queueStats.failed) / queueStats.total * 100) : 0;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressText').textContent =
                `${queueStats.completed + queueStats.failed}/${queueStats.total} (${Math.round(progress)}%)`;
        }

        // 更新任务列表 (向后兼容，但元素不存在时安全跳过)
        function updateTaskList(tasks) {
            const tbody = document.getElementById('taskListBody');
            if (!tbody) {
                // 元素不存在时，使用详细任务列表作为备选
                console.log('🔍 taskListBody不存在，使用taskDetailedBody作为备选');
                updateTaskDetailedList(tasks.map(task => ({
                    id: task.id || 0,
                    url: task.url,
                    status: task.status === 'QUEUED' ? 'pending' : 
                           task.status === 'IN_PROGRESS' ? 'running' :
                           task.status === 'COMPLETED' ? 'completed' : 
                           task.status === 'FAILED' ? 'failed' : task.status,
                    progress: task.status === 'COMPLETED' ? '100%' : 
                             task.status === 'IN_PROGRESS' ? '处理中...' : '-',
                    duration: task.waitTime ? Math.round(task.waitTime) + 's' : '-',
                    result: task.status === 'COMPLETED' ? {} : null
                })));
                return;
            }

            tbody.innerHTML = '';

            if (tasks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="padding: 20px; text-align: center; color: #6c757d;">暂无任务</td></tr>';
                return;
            }

            tasks.forEach(task => {
                const row = document.createElement('tr');
                const statusIcon = getStatusIcon(task.status);
                const waitTime = task.waitTime ? Math.round(task.waitTime) + 's' : '0s';

                row.innerHTML = `
                    <td style="padding: 8px; border: 1px solid #dee2e6;">${statusIcon} ${task.status}</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${task.url}">${task.url}</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">${waitTime}</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">${task.retryCount || 0}</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">${task.priority || 0}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取状态图标
        function getStatusIcon(status) {
            switch (status) {
                case 'QUEUED': return '⏳';
                case 'IN_PROGRESS': return '🔄';
                case 'COMPLETED': return '✅';
                case 'FAILED': return '❌';
                case 'CANCELLED': return '⏹️';
                default: return '❓';
            }
        }

        // 删除错误的updateDetailedTaskList函数 - 已合并到updateTaskDetailedList中

        // 获取任务进度
        function getTaskProgress(task) {
            switch (task.status) {
                case 'QUEUED': return '等待中';
                case 'IN_PROGRESS': return '处理中...';
                case 'COMPLETED': return '100%';
                case 'FAILED': return '失败';
                default: return '-';
            }
        }

        // 获取任务持续时间
        function getTaskDuration(task) {
            if (!task.startTime) return '-';
            const now = Date.now();
            const duration = Math.round((now - task.startTime) / 1000);
            return duration + 's';
        }

        // 获取任务结果
        function getTaskResult(task) {
            switch (task.status) {
                case 'COMPLETED': return '✅ 成功';
                case 'FAILED': return '❌ 失败';
                case 'IN_PROGRESS': return '⏳ 处理中';
                case 'QUEUED': return '⏳ 等待中';
                default: return '-';
            }
        }





        // 流式处理响应函数
        async function handleStreamingResponse(response, config, progressViewer, responseViewer) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            const results = [];
            let streamingTaskList = [];
            
            // 初始化任务列表
            config.urls.forEach((url, index) => {
                streamingTaskList.push({
                    id: index,
                    url: url,
                    status: 'pending',
                    progress: '0%',
                    duration: '-',
                    result: null
                });
            });
            
            // 设置全局变量以便其他函数访问
            window.currentStreamingTaskList = streamingTaskList;
            
            // 禁用模拟监控，使用真实流式数据
            if (queueMonitorInterval) {
                clearInterval(queueMonitorInterval);
                queueMonitorInterval = null;
            }
            
            addMonitorLog('🌊 开始处理流式响应...');
            updateTaskDetailedList(streamingTaskList);
            
            try {
                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行
                    
                    for (const line of lines) {
                        if (line.trim()) {
                            try {
                                const data = JSON.parse(line);
                                console.log(`🔍 流式数据解析:`, {
                                    hasStatus: !!data.status,
                                    status: data.status,
                                    hasUrl: !!data.url,
                                    url: data.url ? data.url.substring(0, 50) + '...' : 'undefined',
                                    dataKeys: Object.keys(data)
                                });
                                
                                if (data.status === 'completed') {
                                    addMonitorLog('✅ 流式响应处理完成');
                                    break;
                                } else if (data.url) {
                                    // 单个结果
                                    results.push(data);
                                    console.log(`🔍 流式数据: 接收到结果 ${results.length}/${config.urls.length}`, data.url.substring(0, 50));
                                    updateStreamingProgress(data, config.urls, results, progressViewer, streamingTaskList);
                                } else {
                                    console.log(`❓ 流式数据: 不匹配已知条件`, data);
                                }
                            } catch (e) {
                                console.warn('解析NDJSON行失败:', line, e);
                                addMonitorLog(`⚠️ 解析响应行失败: ${line.substring(0, 50)}...`);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
            
            // 显示最终结果
            responseViewer.textContent = JSON.stringify(results, null, 2);
            window.lastBatchResults = results;
            
            // 最终统计
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;
            addMonitorLog(`🎉 流式处理完成! 总计: ${results.length}, 成功: ${successCount}, 失败: ${failCount}`);
            
            return results;
        }

        // 更新流式处理进度
        function updateStreamingProgress(result, allUrls, allResults, progressViewer, taskList) {
            const url = result.url;
            const success = result.success;
            const index = allUrls.indexOf(url);
            
            // 调试信息
            console.log(`🔍 updateStreamingProgress 被调用:`, {
                url: url ? url.substring(0, 50) + '...' : 'undefined',
                success: success,
                index: index,
                allUrlsLength: allUrls.length,
                taskListLength: taskList.length,
                allUrls: allUrls.map(u => u.substring(0, 30) + '...')
            });
            
            // 更新监控日志
            const icon = success ? '✅' : '❌';
            const status = success ? '成功' : '失败';
            addMonitorLog(`${icon} ${status}: ${url.substring(0, 50)}...`);
            
            // 更新任务列表中的对应项
            if (index >= 0 && taskList[index]) {
                console.log(`🔍 匹配到任务索引 ${index}，开始更新任务状态`);
                taskList[index].status = success ? 'completed' : 'failed';
                taskList[index].progress = '100%';
                taskList[index].duration = '完成';
                taskList[index].result = result;
                
                // 实时更新任务详细列表
                console.log(`🔍 实时更新任务 ${index + 1}: ${url.substring(0, 30)}... - 状态: ${taskList[index].status}`);
                
                // 更新全局变量
                window.currentStreamingTaskList = [...taskList];
                
                // 强制更新任务列表
                updateTaskDetailedList([...taskList]);
                
                // 额外的强制刷新，确保在任何情况下都能看到更新
                setTimeout(() => {
                    const tbody = document.getElementById('taskDetailedBody');
                    if (tbody && tbody.children.length > 0) {
                        const completedCount = taskList.filter(t => t.status === 'completed' || t.status === 'failed').length;
                        console.log(`🔍 验证更新结果: 表格有 ${tbody.children.length} 行，已完成任务 ${completedCount} 个`);
                    }
                }, 100);
            } else {
                console.log(`❌ 无法找到匹配的任务: index=${index}, taskList.length=${taskList.length}`);
                console.log(`❌ 查找的URL: ${url}`);
                console.log(`❌ allUrls:`, allUrls);
                
                // 尝试模糊匹配
                const fuzzyIndex = allUrls.findIndex(u => u.includes(url) || url.includes(u));
                if (fuzzyIndex >= 0) {
                    console.log(`🔍 模糊匹配找到索引 ${fuzzyIndex}，使用该索引更新`);
                    taskList[fuzzyIndex].status = success ? 'completed' : 'failed';
                    taskList[fuzzyIndex].progress = '100%';
                    taskList[fuzzyIndex].duration = '完成';
                    taskList[fuzzyIndex].result = result;
                    
                    window.currentStreamingTaskList = [...taskList];
                    updateTaskDetailedList([...taskList]);
                }
            }
            
            // 统计当前进度
            const completedResults = allResults.filter(r => r.url); // 有效结果
            const successCount = completedResults.filter(r => r.success).length;
            const failCount = completedResults.length - successCount;
            
            // 🔧 修复：检查progressViewer是否存在再更新
            if (progressViewer) {
                progressViewer.textContent = `🌊 实时流式进度: ${completedResults.length}/${allUrls.length}\n✅ 成功: ${successCount}\n❌ 失败: ${failCount}\n\n📄 最新完成:\n${url}`;
            } else {
                console.warn('⚠️ progressViewer 元素不存在，跳过进度显示更新');
            }
            
            // 更新统计面板
            updateTaskStats({
                total: allUrls.length,
                active: allUrls.length - completedResults.length,
                queued: 0,
                completed: successCount,
                failed: failCount,
                memoryUsage: result.server_memory_mb || 0
            });
        }

        // 初始化队列监控
        function initializeQueueMonitor(urls) {
            // 重置统计
            queueStats = {
                total: urls.length,
                active: 0,
                queued: urls.length,
                completed: 0,
                failed: 0,
                memoryUsage: 0
            };

            updateQueueStats(queueStats);

            // 清空监控日志
            const monitorLogElement = document.getElementById('taskMonitorLog');
            if (monitorLogElement) {
                monitorLogElement.innerHTML = '等待开始任务执行...';
            }
            addMonitorLog(`🚀 开始批量爬取 ${urls.length} 个URL`);
            addMonitorLog(`📋 调度器类型: ${document.getElementById('batchDispatcherType').value}`);
            addMonitorLog(`⚙️ 最大并发数: ${document.getElementById('batchMaxConcurrent').value}`);

            // 初始化任务列表
            const tasks = urls.map((url, index) => ({
                id: index,
                url: url,
                status: 'QUEUED',
                waitTime: 0,
                retryCount: 0,
                priority: 0
            }));
            updateTaskList(tasks);

            // 检查是否为流式处理模式
            const isStreaming = document.getElementById('batchStream').checked;
            if (isStreaming) {
                addMonitorLog(`🌊 流式处理模式：将使用实时数据更新`);
                
                // 流式处理模式下强制设置为详细显示模式，确保任务列表可见
                const displayModeSelect = document.getElementById('batchDisplayMode');
                if (displayModeSelect && displayModeSelect.value === 'progress') {
                    console.log('🔍 流式处理模式：自动切换到详细显示模式');
                    displayModeSelect.value = 'detailed';
                    updateMonitorDisplayMode(); // 立即应用显示模式变更
                    addMonitorLog(`📋 已自动切换到详细显示模式以显示任务列表`);
                }
            } else {
                addMonitorLog(`🔄 标准模式：使用模拟监控`);
                // 开始模拟监控（仅在非流式模式下）
                startQueueMonitorSimulation(urls);
            }
        }

        // 开始队列监控模拟
        function startQueueMonitorSimulation(urls) {
            let currentIndex = 0;
            const maxConcurrent = parseInt(document.getElementById('batchMaxConcurrent').value);
            const tasks = urls.map((url, index) => ({
                id: index,
                url: url,
                status: 'QUEUED',
                waitTime: 0,
                retryCount: 0,
                priority: 0,
                startTime: null
            }));

            queueMonitorInterval = setInterval(() => {
                if (!currentBatchJob) return;

                // 模拟内存使用变化
                queueStats.memoryUsage = Math.min(95, 30 + Math.random() * 40);

                // 模拟任务状态变化
                const activeTasks = tasks.filter(t => t.status === 'IN_PROGRESS');
                const queuedTasks = tasks.filter(t => t.status === 'QUEUED');
                const completedTasks = tasks.filter(t => t.status === 'COMPLETED' || t.status === 'FAILED');

                // 启动新任务
                if (activeTasks.length < maxConcurrent && queuedTasks.length > 0) {
                    const nextTask = queuedTasks[0];
                    nextTask.status = 'IN_PROGRESS';
                    nextTask.startTime = Date.now();
                    addMonitorLog(`🔄 开始处理: ${nextTask.url.substring(0, 50)}...`);
                }

                // 完成一些任务
                activeTasks.forEach(task => {
                    if (task.startTime && Date.now() - task.startTime > 2000 + Math.random() * 3000) {
                        const success = Math.random() > 0.1; // 90% 成功率
                        task.status = success ? 'COMPLETED' : 'FAILED';
                        const icon = success ? '✅' : '❌';
                        const status = success ? '成功' : '失败';
                        addMonitorLog(`${icon} ${status}: ${task.url.substring(0, 50)}...`);
                    }
                });

                // 更新等待时间
                tasks.forEach(task => {
                    if (task.status === 'QUEUED') {
                        task.waitTime = (Date.now() - currentBatchJob.startTime) / 1000;
                    }
                });

                // 更新统计
                queueStats.active = tasks.filter(t => t.status === 'IN_PROGRESS').length;
                queueStats.queued = tasks.filter(t => t.status === 'QUEUED').length;
                queueStats.completed = tasks.filter(t => t.status === 'COMPLETED').length;
                queueStats.failed = tasks.filter(t => t.status === 'FAILED').length;

                updateQueueStats(queueStats);
                updateTaskList(tasks.slice(0, 20)); // 只显示前20个任务
                updateTaskDetailedList(tasks); // 修复：使用正确的函数名和正确的元素ID

                // 更新队列统计
                const queueLength = queueStats.queued;
                const avgWaitTime = queueLength > 0 ?
                    tasks.filter(t => t.status === 'QUEUED').reduce((sum, t) => sum + t.waitTime, 0) / queueLength : 0;
                const maxWaitTime = queueLength > 0 ?
                    Math.max(...tasks.filter(t => t.status === 'QUEUED').map(t => t.waitTime)) : 0;

                document.getElementById('queueLength').textContent = queueLength;
                document.getElementById('avgWaitTime').textContent = Math.round(avgWaitTime) + 's';
                document.getElementById('maxWaitTime').textContent = Math.round(maxWaitTime) + 's';

                // 检查是否完成
                if (completedTasks.length === urls.length) {
                    addMonitorLog(`🎉 批量爬取完成! 成功: ${queueStats.completed}, 失败: ${queueStats.failed}`);
                    clearInterval(queueMonitorInterval);
                    queueMonitorInterval = null;
                }
            }, 1000);
        }

        // 下载批量爬取结果
        function downloadBatchResults() {
            if (!window.lastBatchResults) {
                alert('暂无可下载的结果');
                return;
            }

            const results = window.lastBatchResults;
            
            // 尝试从第一个结果中获取时间戳，如果没有则使用当前时间
            let timestamp;
            try {
                // 寻找第一个有start_timestamp的结果
                let firstTimestamp = null;
                for (const result of results) {
                    if (result && 
                        result.extracted_content && 
                        result.extracted_content[0] &&
                        result.extracted_content[0].task_info &&
                        result.extracted_content[0].task_info.start_timestamp) {
                        firstTimestamp = result.extracted_content[0].task_info.start_timestamp;
                        break;
                    }
                }
                
                if (firstTimestamp) {
                    const batchTime = new Date(firstTimestamp);
                    timestamp = batchTime.getFullYear() + '-' + 
                              String(batchTime.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(batchTime.getDate()).padStart(2, '0') + '_' + 
                              String(batchTime.getHours()).padStart(2, '0') + '-' + 
                              String(batchTime.getMinutes()).padStart(2, '0') + '-' + 
                              String(batchTime.getSeconds()).padStart(2, '0');
                } else {
                    // 备用方案：使用当前时间
                    const now = new Date();
                    timestamp = now.getFullYear() + '-' + 
                              String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(now.getDate()).padStart(2, '0') + '_' + 
                              String(now.getHours()).padStart(2, '0') + '-' + 
                              String(now.getMinutes()).padStart(2, '0') + '-' + 
                              String(now.getSeconds()).padStart(2, '0');
                }
            } catch (error) {
                // 如果时间戳解析失败，使用当前时间
                const now = new Date();
                timestamp = now.getFullYear() + '-' + 
                          String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                          String(now.getDate()).padStart(2, '0') + '_' + 
                          String(now.getHours()).padStart(2, '0') + '-' + 
                          String(now.getMinutes()).padStart(2, '0') + '-' + 
                          String(now.getSeconds()).padStart(2, '0');
            }
            
            const filename = `crawl4ai_batch_results_${timestamp}.json`;

            // 创建下载链接
            const dataStr = JSON.stringify(results, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showMessage('success', `结果已下载为 ${filename}`);
        }

        // 下载单个任务结果
        function downloadSingleTaskResult(taskId) {
            console.log('🔍 下载单个任务调试: taskId =', taskId);
            console.log('🔍 下载单个任务调试: window.lastBatchResults =', window.lastBatchResults);
            console.log('🔍 下载单个任务调试: window.currentTaskList =', window.currentTaskList);
            console.log('🔍 下载单个任务调试: lastTaskResult =', lastTaskResult);
            
            // 优先从当前任务列表获取结果，然后尝试lastBatchResults
            let taskResult = null;
            let taskInfo = null;
            
            // 从任务列表中获取任务信息
            if (window.currentTaskList && Array.isArray(window.currentTaskList)) {
                taskInfo = window.currentTaskList.find(task => task.id === taskId);
                console.log('🔍 从currentTaskList找到任务信息:', taskInfo);
            }
            
            // 从结果数据中获取实际结果
            if (window.lastBatchResults && Array.isArray(window.lastBatchResults)) {
                // 🔧 修复：使用URL匹配而不是索引匹配
                if (taskInfo && taskInfo.url) {
                    taskResult = window.lastBatchResults.find(result => {
                        return result.url === taskInfo.url || 
                               result.id === taskId || 
                               result.task_id === taskId;
                    });
                    console.log('🔍 从lastBatchResults通过URL匹配找到任务结果:', {
                        searchUrl: taskInfo.url,
                        foundResult: !!taskResult,
                        resultUrl: taskResult?.url
                    });
                } else {
                    // 回退到原始索引查找
                    taskResult = window.lastBatchResults[taskId] || window.lastBatchResults.find((result, index) => {
                        return index === taskId || result.id === taskId || result.task_id === taskId;
                    });
                    console.log('🔍 从lastBatchResults通过索引找到任务结果:', taskResult);
                }
            } else if (lastTaskResult) {
                // 如果是单个任务结果
                if (Array.isArray(lastTaskResult)) {
                    taskResult = lastTaskResult[taskId];
                } else if (taskId === 0) {
                    taskResult = lastTaskResult;
                }
                console.log('🔍 从lastTaskResult找到任务结果:', taskResult);
            }

            if (!taskResult) {
                alert(`未找到任务 ID ${taskId} 的结果`);
                return;
            }

            // 创建包含任务详细信息的数据结构
            let taskTimestamp;
            try {
                // 尝试从taskResult中获取start_timestamp
                if (taskResult && 
                    taskResult.extracted_content && 
                    taskResult.extracted_content[0] &&
                    taskResult.extracted_content[0].task_info &&
                    taskResult.extracted_content[0].task_info.start_timestamp) {
                    taskTimestamp = taskResult.extracted_content[0].task_info.start_timestamp;
                } else {
                    taskTimestamp = new Date().toISOString();
                }
            } catch (error) {
                taskTimestamp = new Date().toISOString();
            }
            
            const taskData = {
                task_info: {
                    id: taskId,
                    url: taskInfo ? taskInfo.url : (taskResult.url || 'unknown'),
                    timestamp: taskTimestamp,
                    status: taskResult.success !== false ? 'SUCCESS' : 'FAILED'
                },
                result: taskResult
            };

            // 生成文件名 - 使用任务的start_timestamp而不是当前时间
            let timestamp;
            try {
                // 优先从taskResult的extracted_content中获取start_timestamp
                if (taskResult && 
                    taskResult.extracted_content && 
                    taskResult.extracted_content[0] &&
                    taskResult.extracted_content[0].task_info &&
                    taskResult.extracted_content[0].task_info.start_timestamp) {
                    
                    const startTimestamp = taskResult.extracted_content[0].task_info.start_timestamp;
                    let taskTime;
                    
                    // 检测并处理不同的时间戳格式
                    if (typeof startTimestamp === 'string' && /^\d{14}$/.test(startTimestamp)) {
                        // 处理 "YYYYMMDDHHMMSS" 格式
                        const year = startTimestamp.substring(0, 4);
                        const month = startTimestamp.substring(4, 6);
                        const day = startTimestamp.substring(6, 8);
                        const hour = startTimestamp.substring(8, 10);
                        const minute = startTimestamp.substring(10, 12);
                        const second = startTimestamp.substring(12, 14);
                        const isoString = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
                        taskTime = new Date(isoString);
                    } else {
                        // 尝试直接解析（适用于ISO格式）
                        taskTime = new Date(startTimestamp);
                    }
                    
                    // 验证Date对象是否有效
                    if (isNaN(taskTime.getTime())) {
                        // 如果解析失败，使用当前时间
                        taskTime = new Date();
                    }
                    timestamp = taskTime.getFullYear() + '-' + 
                              String(taskTime.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(taskTime.getDate()).padStart(2, '0') + '_' + 
                              String(taskTime.getHours()).padStart(2, '0') + '-' + 
                              String(taskTime.getMinutes()).padStart(2, '0') + '-' + 
                              String(taskTime.getSeconds()).padStart(2, '0');
                } else {
                    // 备用方案：如果没有start_timestamp，使用当前时间
                    const now = new Date();
                    timestamp = now.getFullYear() + '-' + 
                              String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(now.getDate()).padStart(2, '0') + '_' + 
                              String(now.getHours()).padStart(2, '0') + '-' + 
                              String(now.getMinutes()).padStart(2, '0') + '-' + 
                              String(now.getSeconds()).padStart(2, '0');
                }
            } catch (error) {
                // 如果时间戳解析失败，使用当前时间作为备用
                const now = new Date();
                timestamp = now.getFullYear() + '-' + 
                          String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                          String(now.getDate()).padStart(2, '0') + '_' + 
                          String(now.getHours()).padStart(2, '0') + '-' + 
                          String(now.getMinutes()).padStart(2, '0') + '-' + 
                          String(now.getSeconds()).padStart(2, '0');
            }
            
            const urlPart = taskInfo && taskInfo.url ? 
                new URL(taskInfo.url).hostname.replace(/\./g, '_') : 
                (taskResult.url ? new URL(taskResult.url).hostname.replace(/\./g, '_') : 'task');
            const filename = `crawl4ai_task_${taskId}_${urlPart}_${timestamp}.json`;

            // 创建下载链接
            try {
                const dataStr = JSON.stringify(taskData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                showMessage('success', `任务 ${taskId} 的结果已下载为 ${filename}`);
            } catch (error) {
                console.error('下载任务结果时出错:', error);
                showMessage('error', `下载失败: ${error.message}`);
            }
        }

        // 任务监控控制函数
        function pauseTaskQueue() {
            window.taskQueuePaused = true;
            document.getElementById('taskPauseBtn').style.display = 'none';
            document.getElementById('taskResumeBtn').style.display = 'inline-block';
            addTaskLog('⏸️ 任务队列已暂停');
            showMessage('info', '任务队列已暂停');
        }

        function resumeTaskQueue() {
            window.taskQueuePaused = false;
            document.getElementById('taskPauseBtn').style.display = 'inline-block';
            document.getElementById('taskResumeBtn').style.display = 'none';
            addTaskLog('▶️ 任务队列已恢复');
            showMessage('info', '任务队列已恢复');
        }

        function stopTaskQueue() {
            if (confirm('确定要停止所有任务吗？')) {
                window.taskQueueStopped = true;
                document.getElementById('taskPauseBtn').style.display = 'none';
                document.getElementById('taskResumeBtn').style.display = 'none';
                document.getElementById('taskStopBtn').style.display = 'none';
                addTaskLog('⏹️ 任务队列已停止');
                showMessage('warning', '任务队列已停止');
            }
        }



        // 任务监控辅助函数
        function addTaskLog(message, clear = false) {
            const logElement = document.getElementById('taskMonitorLog');
            if (logElement) {
                if (clear) {
                    logElement.innerHTML = '';
                }
                const timestamp = new Date().toLocaleTimeString();
                logElement.innerHTML += `<div style="margin-bottom: 2px;">[${timestamp}] ${message}</div>`;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        function updateTaskStats(stats) {
            const elements = {
                total: document.getElementById('taskTotalTasks'),
                active: document.getElementById('taskActiveTasks'),
                queued: document.getElementById('taskQueuedTasks'),
                completed: document.getElementById('taskCompletedTasks'),
                failed: document.getElementById('taskFailedTasks'),
                memory: document.getElementById('taskMemoryUsage')
            };

            if (elements.total) elements.total.textContent = stats.total || 0;
            if (elements.active) elements.active.textContent = stats.active || 0;
            if (elements.queued) elements.queued.textContent = stats.queued || 0;
            if (elements.completed) elements.completed.textContent = stats.completed || 0;
            if (elements.failed) elements.failed.textContent = stats.failed || 0;
            if (elements.memory) elements.memory.textContent = (stats.memoryUsage || 0) + '%';

            // 更新进度条
            const total = stats.total || 0;
            const completed = stats.completed || 0;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            const progressBar = document.getElementById('taskProgressBar');
            const progressText = document.getElementById('taskProgressText');
            if (progressBar) progressBar.style.width = percentage + '%';
            if (progressText) progressText.textContent = `${completed}/${total} (${percentage}%)`;
        }

        function updateTaskDetailedList(tasks) {
            const tbody = document.getElementById('taskDetailedBody');
            if (!tbody) {
                console.log('❌ updateTaskDetailedList: taskDetailedBody元素未找到');
                return;
            }

            if (!tasks || tasks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="padding: 20px; text-align: center; color: #6c757d;">暂无任务</td></tr>';
                return;
            }

            console.log(`🔍 updateTaskDetailedList: 更新 ${tasks.length} 个任务，状态分布:`, 
                       tasks.reduce((acc, task) => { 
                           acc[task.status] = (acc[task.status] || 0) + 1; 
                           return acc; 
                       }, {}));

            // 确保父容器可见
            const container = document.getElementById('taskDetailedContainer');
            if (container) {
                container.style.display = 'block';
                console.log('🔍 taskDetailedContainer 确保可见');
            }
            
            // 确保 .display-detailed 容器可见 (流式处理关键修复)
            const detailedTaskList = document.querySelector('.detailed-task-list.display-detailed');
            if (detailedTaskList) {
                detailedTaskList.style.display = 'block';
                console.log('🔍 detailed-task-list.display-detailed 确保可见');
                
                // 检查父容器的CSS类，确保没有被隐藏
                const monitorContainer = document.getElementById('taskMonitorContainer');
                if (monitorContainer) {
                    const hasProgressMode = monitorContainer.classList.contains('monitor-display-mode-progress');
                    if (hasProgressMode) {
                        console.log('🔍 检测到进度模式，移除以显示详细任务列表');
                        monitorContainer.classList.remove('monitor-display-mode-progress');
                        monitorContainer.classList.add('monitor-display-mode-detailed');
                        addMonitorLog(`📋 已自动切换显示模式以确保任务列表可见`);
                    }
                }
            }

            // 强制清空并重新构建表格内容
            tbody.innerHTML = '';
            const newHTML = tasks.map(task => {
                const statusIcons = {
                    'pending': '⏳',
                    'running': '🔄',
                    'completed': '✅',
                    'failed': '❌',
                    'paused': '⏸️'
                };

                const statusColors = {
                    'pending': '#ffc107',
                    'running': '#17a2b8',
                    'completed': '#28a745',
                    'failed': '#dc3545',
                    'paused': '#6c757d'
                };

                const icon = statusIcons[task.status] || '❓';
                const color = statusColors[task.status] || '#6c757d';

                // 生成结果列内容：如果任务成功完成且有结果，显示下载按钮
                let resultContent = '-';
                if (task.result && task.status === 'completed') {
                    resultContent = `
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <span>✅</span>
                            <button onclick="downloadSingleTaskResult(${task.id})" 
                                    style="padding: 2px 6px; font-size: 11px; background: #17a2b8; color: white; border: none; border-radius: 3px; cursor: pointer;"
                                    title="下载此任务的结果">
                                📥
                            </button>
                        </div>
                    `;
                } else if (task.result) {
                    resultContent = '✅';
                }

                return `
                    <tr>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            <span style="color: ${color};">${icon} ${task.status}</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #dee2e6; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${task.url}">
                            ${task.url}
                        </td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            ${task.progress || '-'}
                        </td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            ${task.duration || '-'}
                        </td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            ${resultContent}
                        </td>
                    </tr>
                `;
            }).join('');
            
            // 强制设置新内容并触发重绘
            tbody.innerHTML = newHTML;
            
            // 强制触发重绘
            tbody.offsetHeight;
            
            console.log(`🔍 updateTaskDetailedList: DOM已更新，tbody有 ${tbody.children.length} 行`);
        }



        // 爬虫配置相关函数
        // 切换自定义用户代理
        function toggleCustomUserAgent() {
            const userAgent = document.getElementById('crawlerUserAgent').value;
            const customGroup = document.getElementById('customUserAgentGroup');

            if (userAgent === 'custom') {
                customGroup.style.display = 'block';
            } else {
                customGroup.style.display = 'none';
            }
        }

        // 切换代理配置
        function toggleProxyConfig() {
            const enableProxy = document.getElementById('crawlerEnableProxy').checked;
            const proxyConfig = document.getElementById('proxyConfig');

            if (enableProxy) {
                proxyConfig.style.display = 'block';
            } else {
                proxyConfig.style.display = 'none';
            }
        }

        // 生成爬虫配置
        function generateCrawlerConfig() {
            const config = {
                // 基础设置
                method: document.getElementById('crawlerMethod').value,
                verbose: document.getElementById('crawlerVerbose').checked,
                check_robots_txt: document.getElementById('crawlerCheckRobots').checked,
            };

            // 浏览器配置对象
            const browser_config = {
                headless: true,  // 默认headless模式
                verbose: false,  // 浏览器verbose默认关闭
            };

            // 浏览器环境配置
            const userAgent = document.getElementById('crawlerUserAgent').value;
            if (userAgent === 'custom') {
                const customUA = document.getElementById('crawlerCustomUserAgent').value.trim();
                if (customUA) {
                    browser_config.user_agent = customUA;
                }
            } else if (userAgent) {
                browser_config.user_agent_mode = userAgent;
            }

            const userAgentMode = document.getElementById('crawlerUserAgentMode').value;
            if (userAgentMode) {
                browser_config.user_agent_mode = userAgentMode;
            }

            const locale = document.getElementById('crawlerLocale').value.trim();
            if (locale) {
                browser_config.locale = locale;
            }

            const timezone = document.getElementById('crawlerTimezone').value.trim();
            if (timezone) {
                browser_config.timezone_id = timezone;
            }

            // 添加常用的浏览器配置
            browser_config.viewport_width = 1920;
            browser_config.viewport_height = 1080;
            browser_config.wait_for = 2.0;
            browser_config.timeout = 30;
            browser_config.ignore_https_errors = true;
            browser_config.extra_args = [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation"
            ];

            // 代理配置
            const enableProxy = document.getElementById('crawlerEnableProxy').checked;
            if (enableProxy) {
                const proxyServer = document.getElementById('crawlerProxyServer').value.trim();
                if (proxyServer) {
                    config.proxy_config = {
                        server: proxyServer
                    };

                    const proxyUsername = document.getElementById('crawlerProxyUsername').value.trim();
                    const proxyPassword = document.getElementById('crawlerProxyPassword').value.trim();
                    if (proxyUsername && proxyPassword) {
                        config.proxy_config.username = proxyUsername;
                        config.proxy_config.password = proxyPassword;
                    }
                }
            }

            config.fetch_ssl_certificate = document.getElementById('crawlerFetchSSL').checked;

            // 反反爬配置
            config.simulate_user = document.getElementById('crawlerSimulateUser').checked;
            config.magic = document.getElementById('crawlerMagic').checked;
            config.override_navigator = document.getElementById('crawlerOverrideNavigator').checked;
            config.remove_overlay_elements = document.getElementById('crawlerRemoveOverlay').checked;
            config.ignore_body_visibility = document.getElementById('crawlerIgnoreBodyVisibility').checked;
            config.adjust_viewport_to_content = document.getElementById('crawlerAdjustViewport').checked;

            // 页面导航配置
            config.wait_until = document.getElementById('crawlerWaitUntil').value;

            const waitFor = document.getElementById('crawlerWaitFor').value.trim();
            if (waitFor) {
                config.wait_for = waitFor;
            }

            config.wait_for_images = document.getElementById('crawlerWaitForImages').checked;
            config.page_timeout = parseInt(document.getElementById('crawlerPageTimeout').value);
            config.delay_before_return_html = parseFloat(document.getElementById('crawlerDelayBeforeReturn').value);

            const jsCode = document.getElementById('crawlerJsCode').value.trim();
            if (jsCode) {
                config.js_code = jsCode;
            }

            config.js_only = document.getElementById('crawlerJsOnly').checked;
            config.scan_full_page = document.getElementById('crawlerScanFullPage').checked;
            config.process_iframes = document.getElementById('crawlerProcessIframes').checked;
            config.scroll_delay = parseFloat(document.getElementById('crawlerScrollDelay').value);

            // 缓存策略
            config.cache_mode = document.getElementById('crawlerCacheMode').value;

            const sessionId = document.getElementById('crawlerSessionId').value.trim();
            if (sessionId) {
                config.session_id = sessionId;
            }

            // 媒体处理配置
            config.screenshot = document.getElementById('crawlerScreenshot').checked;
            if (config.screenshot) {
                const screenshotWait = parseFloat(document.getElementById('crawlerScreenshotWait').value);
                if (screenshotWait > 0) {
                    config.screenshot_wait_for = screenshotWait;
                }
                config.screenshot_height_threshold = parseInt(document.getElementById('crawlerScreenshotHeight').value);
            }

            config.pdf = document.getElementById('crawlerPdf').checked;
            config.capture_mhtml = document.getElementById('crawlerMhtml').checked;
            config.exclude_external_images = document.getElementById('crawlerExcludeExternalImages').checked;
            config.exclude_all_images = document.getElementById('crawlerExcludeAllImages').checked;
            config.image_score_threshold = parseInt(document.getElementById('crawlerImageScore').value);
            config.image_description_min_word_threshold = parseInt(document.getElementById('crawlerImageDescWords').value);
            config.table_score_threshold = parseInt(document.getElementById('crawlerTableScore').value);

            // 调试监控配置
            config.capture_network_requests = document.getElementById('crawlerCaptureNetwork').checked;
            config.capture_console_messages = document.getElementById('crawlerCaptureConsole').checked;
            config.log_console = document.getElementById('crawlerLogConsole').checked;

            // 添加browser_config到最终配置
            config.browser_config = browser_config;

            // 显示配置
            document.getElementById('crawlerConfigViewer').textContent = JSON.stringify(config, null, 2);

            // 保存配置到localStorage
            localStorage.setItem('crawl4ai_crawler_config', JSON.stringify(config));

            showMessage('success', '爬虫配置已生成');
        }



        // 清空爬虫配置
        function clearCrawlerConfig() {
            if (confirm('确定要清空所有爬虫配置吗？')) {
                // 重置所有表单字段
                document.getElementById('crawlerMethod').value = 'GET';
                document.getElementById('crawlerVerbose').checked = true;
                document.getElementById('crawlerCheckRobots').checked = false;

                // 浏览器环境
                document.getElementById('crawlerUserAgent').value = '';
                document.getElementById('crawlerCustomUserAgent').value = '';
                document.getElementById('crawlerUserAgentMode').value = '';
                document.getElementById('crawlerLocale').value = '';
                document.getElementById('crawlerTimezone').value = '';
                document.getElementById('crawlerEnableProxy').checked = false;
                document.getElementById('crawlerProxyServer').value = '';
                document.getElementById('crawlerProxyUsername').value = '';
                document.getElementById('crawlerProxyPassword').value = '';
                document.getElementById('crawlerFetchSSL').checked = false;

                // 反反爬
                document.getElementById('crawlerSimulateUser').checked = false;
                document.getElementById('crawlerMagic').checked = false;
                document.getElementById('crawlerOverrideNavigator').checked = false;
                document.getElementById('crawlerRemoveOverlay').checked = false;
                document.getElementById('crawlerIgnoreBodyVisibility').checked = true;
                document.getElementById('crawlerAdjustViewport').checked = false;

                // 页面导航
                document.getElementById('crawlerWaitUntil').value = 'domcontentloaded';
                document.getElementById('crawlerWaitFor').value = '';
                document.getElementById('crawlerWaitForImages').checked = false;
                document.getElementById('crawlerPageTimeout').value = '60000';
                document.getElementById('crawlerDelayBeforeReturn').value = '0.1';
                document.getElementById('crawlerJsCode').value = '';
                document.getElementById('crawlerJsOnly').checked = false;
                document.getElementById('crawlerScanFullPage').checked = false;
                document.getElementById('crawlerProcessIframes').checked = false;
                document.getElementById('crawlerScrollDelay').value = '0.2';

                // 缓存策略
                document.getElementById('crawlerCacheMode').value = 'BYPASS';
                document.getElementById('crawlerSessionId').value = '';

                // 媒体处理
                document.getElementById('crawlerScreenshot').checked = false;
                document.getElementById('crawlerScreenshotWait').value = '0';
                document.getElementById('crawlerScreenshotHeight').value = '20000';
                document.getElementById('crawlerPdf').checked = false;
                document.getElementById('crawlerMhtml').checked = false;
                document.getElementById('crawlerExcludeExternalImages').checked = false;
                document.getElementById('crawlerExcludeAllImages').checked = false;
                document.getElementById('crawlerImageScore').value = '3';
                document.getElementById('crawlerImageDescWords').value = '50';
                document.getElementById('crawlerTableScore').value = '7';

                // 调试监控
                document.getElementById('crawlerCaptureNetwork').checked = false;
                document.getElementById('crawlerCaptureConsole').checked = false;
                document.getElementById('crawlerLogConsole').checked = false;

                // 隐藏条件显示的面板
                document.getElementById('customUserAgentGroup').style.display = 'none';
                document.getElementById('proxyConfig').style.display = 'none';

                // 清空配置显示
                document.getElementById('crawlerConfigViewer').textContent = '点击"生成配置"查看爬虫配置';

                // 清除localStorage
                localStorage.removeItem('crawl4ai_crawler_config');

                showMessage('success', '爬虫配置已清空');
            }
        }

        // 数据提取配置切换函数
        function toggleLLMExtractionConfig() {
            const enableLLM = document.getElementById('enableLLMExtraction').checked;
            const llmConfig = document.getElementById('llmExtractionConfig');

            if (enableLLM) {
                llmConfig.style.display = 'block';
            } else {
                llmConfig.style.display = 'none';
            }
        }

        function toggleCSSExtractionConfig() {
            const enableCSS = document.getElementById('enableCSSExtraction').checked;
            const cssConfig = document.getElementById('cssExtractionConfig');

            if (enableCSS) {
                cssConfig.style.display = 'block';
            } else {
                cssConfig.style.display = 'none';
            }
        }

        function toggleSchemaExtractionConfig() {
            const enableSchema = document.getElementById('enableSchemaExtraction').checked;
            const schemaConfig = document.getElementById('schemaExtractionConfig');

            if (enableSchema) {
                schemaConfig.style.display = 'block';
            } else {
                schemaConfig.style.display = 'none';
            }
        }

        // 实时保存数据提取配置
        function autoSaveExtractionConfig() {
            try {
                const config = {
                    // 基础配置
                    extraction_strategy: document.getElementById('extractionStrategy').value,
                    chunking_strategy: document.getElementById('extractionChunkingStrategy').value,
                    markdown_generator: document.getElementById('extractionMarkdownGenerator').value,
                    verbose: document.getElementById('extractionVerbose').checked,
                    bypass_cache: document.getElementById('extractionBypassCache').checked
                };

                // 检查启用的配置
                const enableLLM = document.getElementById('enableLLMExtraction').checked;
                const enableCSS = document.getElementById('enableCSSExtraction').checked;
                const enableSchema = document.getElementById('enableSchemaExtraction').checked;

                // LLM配置
                if (enableLLM) {
                    config.llm_extraction = {
                        query: document.getElementById('extractionQuery').value,
                        provider: document.getElementById('extractionProvider').value,
                        model: document.getElementById('extractionModel').value,
                        api_key: document.getElementById('extractionApiKey').value,
                        temperature: parseFloat(document.getElementById('extractionTemperature').value) ?? 0.1,
                        max_tokens: parseInt(document.getElementById('extractionMaxTokens').value) || 4000,
                        top_p: parseFloat(document.getElementById('extractionTopP').value) ?? 0.9
                    };

                    // 添加base URL（如果有值）
                    const baseUrl = document.getElementById('extractionBaseUrl').value.trim();
                    if (baseUrl) {
                        config.llm_extraction.base_url = baseUrl;
                    }
                }

                // CSS选择器配置
                if (enableCSS) {
                    try {
                        const cssSelectorsText = document.getElementById('extractionCssSelectors').value;
                        if (cssSelectorsText.trim()) {
                            config.css_extraction = {
                                selectors: JSON.parse(cssSelectorsText),
                                include_text: document.getElementById('extractionIncludeText').checked,
                                include_attributes: document.getElementById('extractionIncludeAttributes').checked,
                                clean_text: document.getElementById('extractionCleanText').checked
                            };
                        }
                    } catch (e) {
                        // 如果JSON格式错误，跳过CSS配置保存
                        console.warn('CSS选择器JSON格式错误，跳过保存:', e);
                    }
                }

                // 结构化提取配置
                if (enableSchema) {
                    try {
                        const schemaText = document.getElementById('extractionSchema').value;
                        if (schemaText.trim()) {
                            config.schema_extraction = {
                                schema: JSON.parse(schemaText),
                                instructions: document.getElementById('extractionInstructions').value,
                                validate_schema: document.getElementById('extractionValidateSchema').checked,
                                return_raw: document.getElementById('extractionReturnRaw').checked
                            };
                        }
                    } catch (e) {
                        // 如果JSON格式错误，跳过Schema配置保存
                        console.warn('Schema JSON格式错误，跳过保存:', e);
                    }
                }

                // 内容处理配置
                const contentWordThreshold = document.getElementById('contentWordThreshold').value;
                const contentCssSelector = document.getElementById('contentCssSelector').value;
                const contentTargetElements = document.getElementById('contentTargetElements').value;
                const contentExcludedTags = document.getElementById('contentExcludedTags').value;
                const contentExcludedSelector = document.getElementById('contentExcludedSelector').value;
                const contentRemoveForms = document.getElementById('contentRemoveForms').checked;
                const contentOnlyText = document.getElementById('contentOnlyText').checked;
                const contentPrettify = document.getElementById('contentPrettify').checked;
                const contentParserType = document.getElementById('contentParserType').value;
                const contentKeepDataAttributes = document.getElementById('contentKeepDataAttributes').checked;
                const contentKeepAttrs = document.getElementById('contentKeepAttrs').value;

                if (contentWordThreshold || contentCssSelector || contentTargetElements || contentExcludedTags ||
                    contentExcludedSelector || contentRemoveForms || contentOnlyText || contentPrettify ||
                    contentKeepDataAttributes || contentKeepAttrs) {
                    config.content_processing = {
                        word_count_threshold: parseInt(contentWordThreshold) || 200,
                        css_selector: contentCssSelector,
                        target_elements: contentTargetElements ? contentTargetElements.split(',').map(s => s.trim()) : [],
                        excluded_tags: contentExcludedTags ? contentExcludedTags.split(',').map(s => s.trim()) : [],
                        excluded_selector: contentExcludedSelector,
                        remove_forms: contentRemoveForms,
                        only_text: contentOnlyText,
                        prettify: contentPrettify,
                        parser_type: contentParserType,
                        keep_data_attributes: contentKeepDataAttributes,
                        keep_attrs: contentKeepAttrs ? contentKeepAttrs.split(',').map(s => s.trim()) : []
                    };
                }

                // 链接过滤配置
                const linksExcludeExternal = document.getElementById('linksExcludeExternal').checked;
                const linksExcludeInternal = document.getElementById('linksExcludeInternal').checked;
                const linksExcludeSocialMedia = document.getElementById('linksExcludeSocialMedia').checked;
                const linksExcludeDomains = document.getElementById('linksExcludeDomains').value;
                const linksSocialMediaDomains = document.getElementById('linksSocialMediaDomains').value;
                const linksExcludeExternalImages = document.getElementById('linksExcludeExternalImages').checked;
                const linksExcludeAllImages = document.getElementById('linksExcludeAllImages').checked;
                const linksImageScore = document.getElementById('linksImageScore').value;
                const linksImageDescWords = document.getElementById('linksImageDescWords').value;
                const linksTableScore = document.getElementById('linksTableScore').value;

                if (linksExcludeExternal || linksExcludeInternal || linksExcludeSocialMedia || linksExcludeDomains ||
                    linksSocialMediaDomains || linksExcludeExternalImages || linksExcludeAllImages ||
                    linksImageScore || linksImageDescWords || linksTableScore) {
                    config.link_filtering = {
                        exclude_external_links: linksExcludeExternal,
                        exclude_internal_links: linksExcludeInternal,
                        exclude_social_media_links: linksExcludeSocialMedia,
                        exclude_domains: linksExcludeDomains ? linksExcludeDomains.split('\n').filter(d => d.trim()).map(d => d.trim()) : [],
                        social_media_domains: linksSocialMediaDomains ? linksSocialMediaDomains.split('\n').filter(d => d.trim()).map(d => d.trim()) : [],
                        exclude_external_images: linksExcludeExternalImages,
                        exclude_all_images: linksExcludeAllImages,
                        image_score_threshold: parseInt(linksImageScore) || 3,
                        image_description_min_word_threshold: parseInt(linksImageDescWords) || 50,
                        table_score_threshold: parseInt(linksTableScore) || 7
                    };
                }

                // 保存配置
                saveConfig('extraction', config);
                console.log('数据提取配置已自动保存');
            } catch (e) {
                console.warn('自动保存数据提取配置时出错:', e);
            }
        }

        // 添加任务配置监听器
        function addTaskConfigListeners() {
            // 任务类型监听器
            const taskTypeRadios = document.querySelectorAll('input[name="taskType"]');
            taskTypeRadios.forEach(radio => {
                radio.addEventListener('change', autoSaveTaskConfig);
            });

            // 单次爬取配置监听器
            const singleTaskFields = ['singleTaskUrl'];
            singleTaskFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.addEventListener('input', autoSaveTaskConfig);
                }
            });

            // 批量爬取配置监听器
            const batchTaskFields = ['batchTaskUrls', 'batchSemaphoreCount', 'batchMeanDelay', 'batchMaxRange',
                                   'batchSchedulerType', 'batchPoolSize', 'batchMemoryThreshold',
                                   'batchDisplayMode'];
            batchTaskFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.addEventListener('input', autoSaveTaskConfig);
                }
            });

            // 批量爬取复选框监听器
            const batchTaskCheckboxes = ['batchTaskStream', 'batchEnableScheduler', 'batchEnableMonitor',
                                       'batchShowProgress', 'batchLogErrors'];
            batchTaskCheckboxes.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.addEventListener('change', autoSaveTaskConfig);
                }
            });

            // 深度爬取配置监听器
            const deepTaskFields = ['deepTaskStartUrl', 'deepTaskMaxDepth', 'deepTaskMaxPages',
                                   'deepTaskConcurrency', 'deepTaskDelay', 'deepTaskStrategy',
                                   'deepTaskUrlPattern', 'deepTaskExcludePatterns'];
            deepTaskFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.addEventListener('input', autoSaveTaskConfig);
                }
            });
        }

        // 自动保存任务配置
        function autoSaveTaskConfig() {
            try {
                const taskType = document.querySelector('input[name="taskType"]:checked').value;
                let config = {
                    task_type: taskType
                };

                if (taskType === 'single') {
                    // 单次爬取配置
                    config.url = document.getElementById('singleTaskUrl').value;
                    config.method = 'arun';
                } else if (taskType === 'batch') {
                    // 批量爬取配置
                    const urls = document.getElementById('batchTaskUrls').value
                        .split('\n')
                        .map(url => url.trim())
                        .filter(url => url);

                    config.urls = urls;
                    config.method = 'arun_many';
                    config.semaphore_count = parseInt(document.getElementById('batchSemaphoreCount').value) || 5;
                    config.stream = document.getElementById('batchTaskStream').checked;
                    config.mean_delay = parseFloat(document.getElementById('batchMeanDelay').value) || 0.1;
                    config.max_range = parseFloat(document.getElementById('batchMaxRange').value) || 0.3;

                    // 调度器配置
                    const enableScheduler = document.getElementById('batchEnableScheduler').checked;
                    if (enableScheduler) {
                        config.scheduler = {
                            type: document.getElementById('batchSchedulerType').value,
                            pool_size: parseInt(document.getElementById('batchPoolSize').value) || 10,
                            memory_threshold: parseInt(document.getElementById('batchMemoryThreshold').value) || 1024
                        };
                    }

                    // 监控配置
                    const enableMonitor = document.getElementById('batchEnableMonitor').checked;
                    if (enableMonitor) {
                        config.monitor = {
                            display_mode: document.getElementById('batchDisplayMode').value,
                            show_progress: document.getElementById('batchShowProgress').checked,
                            log_errors: document.getElementById('batchLogErrors').checked
                        };
                    }
                } else if (taskType === 'deep') {
                    // 深度爬取配置
                    config.start_url = document.getElementById('deepTaskStartUrl').value;
                    config.method = 'deep_crawl';
                    config.max_depth = parseInt(document.getElementById('deepTaskMaxDepth').value) || 3;
                    config.max_pages = parseInt(document.getElementById('deepTaskMaxPages').value) || 50;
                    config.concurrency = parseInt(document.getElementById('deepTaskConcurrency').value) || 3;
                    config.delay = parseFloat(document.getElementById('deepTaskDelay').value) || 1;
                    config.strategy = document.getElementById('deepTaskStrategy').value;

                    // URL过滤配置
                    const urlPattern = document.getElementById('deepTaskUrlPattern').value.trim();
                    if (urlPattern) {
                        config.url_pattern = urlPattern;
                    }

                    const excludePatterns = document.getElementById('deepTaskExcludePatterns').value.trim();
                    if (excludePatterns) {
                        config.exclude_patterns = excludePatterns.split('\n').map(pattern => pattern.trim()).filter(pattern => pattern);
                    }
                }

                // 保存配置到localStorage
                localStorage.setItem('crawl4ai_task_config', JSON.stringify(config));

                console.log('任务配置已自动保存');
            } catch (e) {
                console.warn('自动保存任务配置时出错:', e);
            }
        }

        // 数据提取相关函数
        function generateExtractionConfig() {
            const config = {
                // 基础配置
                extraction_strategy: document.getElementById('extractionStrategy').value,
                chunking_strategy: document.getElementById('extractionChunkingStrategy').value,
                markdown_generator: document.getElementById('extractionMarkdownGenerator').value,
                verbose: document.getElementById('extractionVerbose').checked,
                bypass_cache: document.getElementById('extractionBypassCache').checked
            };

            // 检查启用的配置
            const enableLLM = document.getElementById('enableLLMExtraction').checked;
            const enableCSS = document.getElementById('enableCSSExtraction').checked;
            const enableSchema = document.getElementById('enableSchemaExtraction').checked;

            // 根据启用的配置自动选择策略
            if (config.extraction_strategy === 'auto') {
                if (enableLLM && enableSchema) {
                    config.extraction_strategy = 'LLMExtractionStrategy';
                } else if (enableCSS) {
                    config.extraction_strategy = 'JsonCssExtractionStrategy';
                } else if (enableLLM) {
                    config.extraction_strategy = 'LLMExtractionStrategy';
                } else {
                    config.extraction_strategy = 'LLMExtractionStrategy'; // 默认策略
                }
            }

            // LLM配置
            if (enableLLM) {
                config.llm_extraction = {
                    query: document.getElementById('extractionQuery').value,
                    provider: document.getElementById('extractionProvider').value,
                    model: document.getElementById('extractionModel').value,
                    api_key: document.getElementById('extractionApiKey').value,
                    temperature: parseFloat(document.getElementById('extractionTemperature').value) ?? 0.1,
                    max_tokens: parseInt(document.getElementById('extractionMaxTokens').value) || 4000,
                    top_p: parseFloat(document.getElementById('extractionTopP').value) ?? 0.9
                };

                // 添加base URL（如果有值）
                const baseUrl = document.getElementById('extractionBaseUrl').value.trim();
                if (baseUrl) {
                    config.llm_extraction.base_url = baseUrl;
                }
            }

            // CSS选择器配置
            if (enableCSS) {
                try {
                    config.css_extraction = {
                        selectors: JSON.parse(document.getElementById('extractionCssSelectors').value),
                        include_text: document.getElementById('extractionIncludeText').checked,
                        include_attributes: document.getElementById('extractionIncludeAttributes').checked,
                        clean_text: document.getElementById('extractionCleanText').checked
                    };
                } catch (e) {
                    alert('CSS选择器配置格式错误，请检查JSON格式');
                    return;
                }
            }

            // 结构化提取配置
            if (enableSchema) {
                const schemaText = document.getElementById('extractionSchema').value;
                if (schemaText.trim()) {
                    try {
                        config.schema_extraction = {
                            schema: JSON.parse(schemaText),
                            instructions: document.getElementById('extractionInstructions').value,
                            validate_schema: document.getElementById('extractionValidateSchema').checked,
                            return_raw: document.getElementById('extractionReturnRaw').checked
                        };
                    } catch (e) {
                        alert('Schema格式错误，请检查JSON格式');
                        return;
                    }
                }
            }

            // 内容处理配置
            const contentWordThreshold = document.getElementById('contentWordThreshold').value;
            const contentCssSelector = document.getElementById('contentCssSelector').value;
            const contentTargetElements = document.getElementById('contentTargetElements').value;
            const contentExcludedTags = document.getElementById('contentExcludedTags').value;
            const contentExcludedSelector = document.getElementById('contentExcludedSelector').value;
            const contentRemoveForms = document.getElementById('contentRemoveForms').checked;
            const contentOnlyText = document.getElementById('contentOnlyText').checked;
            const contentPrettify = document.getElementById('contentPrettify').checked;
            const contentParserType = document.getElementById('contentParserType').value;
            const contentKeepDataAttributes = document.getElementById('contentKeepDataAttributes').checked;
            const contentKeepAttrs = document.getElementById('contentKeepAttrs').value;

            if (contentWordThreshold || contentCssSelector || contentTargetElements || contentExcludedTags ||
                contentExcludedSelector || contentRemoveForms || contentOnlyText || contentPrettify ||
                contentKeepDataAttributes || contentKeepAttrs) {
                config.content_processing = {
                    word_count_threshold: parseInt(contentWordThreshold) || 200,
                    css_selector: contentCssSelector,
                    target_elements: contentTargetElements ? contentTargetElements.split(',').map(s => s.trim()) : [],
                    excluded_tags: contentExcludedTags ? contentExcludedTags.split(',').map(s => s.trim()) : [],
                    excluded_selector: contentExcludedSelector,
                    remove_forms: contentRemoveForms,
                    only_text: contentOnlyText,
                    prettify: contentPrettify,
                    parser_type: contentParserType,
                    keep_data_attributes: contentKeepDataAttributes,
                    keep_attrs: contentKeepAttrs ? contentKeepAttrs.split(',').map(s => s.trim()) : []
                };
            }

            // 链接过滤配置
            const linksExcludeExternal = document.getElementById('linksExcludeExternal').checked;
            const linksExcludeInternal = document.getElementById('linksExcludeInternal').checked;
            const linksExcludeSocialMedia = document.getElementById('linksExcludeSocialMedia').checked;
            const linksExcludeDomains = document.getElementById('linksExcludeDomains').value;
            const linksSocialMediaDomains = document.getElementById('linksSocialMediaDomains').value;
            const linksExcludeExternalImages = document.getElementById('linksExcludeExternalImages').checked;
            const linksExcludeAllImages = document.getElementById('linksExcludeAllImages').checked;
            const linksImageScore = document.getElementById('linksImageScore').value;
            const linksImageDescWords = document.getElementById('linksImageDescWords').value;
            const linksTableScore = document.getElementById('linksTableScore').value;

            if (linksExcludeExternal || linksExcludeInternal || linksExcludeSocialMedia || linksExcludeDomains ||
                linksSocialMediaDomains || linksExcludeExternalImages || linksExcludeAllImages ||
                linksImageScore || linksImageDescWords || linksTableScore) {
                config.link_filtering = {
                    exclude_external_links: linksExcludeExternal,
                    exclude_internal_links: linksExcludeInternal,
                    exclude_social_media_links: linksExcludeSocialMedia,
                    exclude_domains: linksExcludeDomains ? linksExcludeDomains.split('\n').filter(d => d.trim()).map(d => d.trim()) : [],
                    social_media_domains: linksSocialMediaDomains ? linksSocialMediaDomains.split('\n').filter(d => d.trim()).map(d => d.trim()) : [],
                    exclude_external_images: linksExcludeExternalImages,
                    exclude_all_images: linksExcludeAllImages,
                    image_score_threshold: parseInt(linksImageScore) || 3,
                    image_description_min_word_threshold: parseInt(linksImageDescWords) || 50,
                    table_score_threshold: parseInt(linksTableScore) || 7
                };
            }

            // 添加配置摘要
            config._config_summary = {
                enabled_features: {
                    llm_extraction: enableLLM,
                    css_extraction: enableCSS,
                    schema_extraction: enableSchema,
                    content_processing: !!config.content_processing,
                    link_filtering: !!config.link_filtering
                },
                strategy_used: config.extraction_strategy
            };

            document.getElementById('extractionRequestViewer').textContent = JSON.stringify(config, null, 2);
            saveConfig('extraction', config);
            showMessage('success', '数据提取配置已生成');
        }



        function clearExtractionConfig() {
            if (confirm('确定要清空所有配置吗？')) {
                // 基础配置
                document.getElementById('extractionStrategy').value = 'auto';
                document.getElementById('extractionChunkingStrategy').value = 'IdentityChunking';
                document.getElementById('extractionMarkdownGenerator').value = 'DefaultMarkdownGenerator';
                document.getElementById('extractionVerbose').checked = true;
                document.getElementById('extractionBypassCache').checked = false;

                // 禁用所有高级配置
                document.getElementById('enableLLMExtraction').checked = false;
                document.getElementById('enableCSSExtraction').checked = false;
                document.getElementById('enableSchemaExtraction').checked = false;

                // 重置LLM配置
                document.getElementById('extractionQuery').value = '提取页面中所有文章的标题、作者和发布时间';
                document.getElementById('extractionProvider').value = 'openai';
                document.getElementById('extractionModel').value = 'gpt-4';
                document.getElementById('extractionApiKey').value = '';
                document.getElementById('extractionBaseUrl').value = '';
                document.getElementById('extractionTemperature').value = '0.1';
                document.getElementById('extractionMaxTokens').value = '4000';
                document.getElementById('extractionTopP').value = '0.9';

                // 重置CSS配置
                document.getElementById('extractionCssSelectors').value = `{
  "title": "h1, .title, .headline",
  "content": ".content, .article-body, main p",
  "author": ".author, .byline, [data-author]",
  "date": ".date, .publish-date, time",
  "links": "a[href]",
  "images": "img[src]"
}`;
                document.getElementById('extractionIncludeText').checked = true;
                document.getElementById('extractionIncludeAttributes').checked = false;
                document.getElementById('extractionCleanText').checked = true;

                // 重置Schema配置
                document.getElementById('extractionSchema').value = `{
  "type": "object",
  "properties": {
    "articles": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "title": {"type": "string"},
          "author": {"type": "string"},
          "date": {"type": "string"},
          "content": {"type": "string"},
          "url": {"type": "string"},
          "tags": {
            "type": "array",
            "items": {"type": "string"}
          }
        },
        "required": ["title", "content"]
      }
    }
  }
}`;
                document.getElementById('extractionInstructions').value = `请从网页中提取所有文章信息，包括标题、作者、发布日期、内容摘要和相关标签。

具体要求：
1. 确保数据格式严格符合上述JSON Schema定义
2. 日期统一转换为YYYY-MM-DD格式
3. 如果字段缺失，请用null表示
4. 内容摘要限制在200字以内
5. 标签以数组形式存储，去除重复项`;
                document.getElementById('extractionValidateSchema').checked = true;
                document.getElementById('extractionReturnRaw').checked = false;

                // 重置内容处理配置
                document.getElementById('contentWordThreshold').value = '200';
                document.getElementById('contentCssSelector').value = '';
                document.getElementById('contentTargetElements').value = '';
                document.getElementById('contentExcludedTags').value = 'nav,footer,aside';
                document.getElementById('contentExcludedSelector').value = '';
                document.getElementById('contentRemoveForms').checked = false;
                document.getElementById('contentOnlyText').checked = false;
                document.getElementById('contentPrettify').checked = false;
                document.getElementById('contentParserType').value = 'lxml';
                document.getElementById('contentKeepDataAttributes').checked = false;
                document.getElementById('contentKeepAttrs').value = '';

                // 重置链接过滤配置
                document.getElementById('linksExcludeExternal').checked = false;
                document.getElementById('linksExcludeInternal').checked = false;
                document.getElementById('linksExcludeSocialMedia').checked = false;
                document.getElementById('linksExcludeDomains').value = 'example.com\nads.google.com\nfacebook.com\ntwitter.com';
                document.getElementById('linksSocialMediaDomains').value = 'facebook.com\ntwitter.com\ninstagram.com\nlinkedin.com\nyoutube.com\ntiktok.com\npinterest.com\nreddit.com';
                document.getElementById('linksExcludeExternalImages').checked = false;
                document.getElementById('linksExcludeAllImages').checked = false;
                document.getElementById('linksImageScore').value = '3';
                document.getElementById('linksImageDescWords').value = '50';
                document.getElementById('linksTableScore').value = '7';

                // 隐藏配置面板
                toggleLLMExtractionConfig();
                toggleCSSExtractionConfig();
                toggleSchemaExtractionConfig();

                // 清空结果显示
                document.getElementById('extractionRequestViewer').textContent = '点击"生成配置"查看请求结构';
                document.getElementById('extractionResponseViewer').textContent = '数据提取配置预览，在任务配置中执行提取任务';
                document.getElementById('extractionStructuredViewer').textContent = '结构化提取数据将在这里显示';
                document.getElementById('extractionRawViewer').textContent = '原始提取数据将在这里显示';

                showMessage('success', '配置已清空');
            }
        }

        // 深度爬取相关函数
        function generateDeepConfig() {
            const config = {
                start_url: document.getElementById('deepStartUrl').value,
                max_depth: parseInt(document.getElementById('deepMaxDepth').value),
                max_pages: parseInt(document.getElementById('deepMaxPages').value),
                concurrency: parseInt(document.getElementById('deepConcurrency').value),
                delay: parseFloat(document.getElementById('deepDelay').value),
                verbose: document.getElementById('deepVerbose').checked,
                bypass_cache: document.getElementById('deepBypassCache').checked,
                follow_redirects: document.getElementById('deepFollowRedirects').checked,
                crawl_strategy: document.getElementById('deepCrawlStrategy').value,
                link_selector: document.getElementById('deepLinkSelector').value,
                content_selector: document.getElementById('deepContentSelector').value
            };

            // 过滤规则
            const includePatterns = document.getElementById('deepIncludePatterns').value
                .split('\n').filter(p => p.trim()).map(p => p.trim());
            const excludePatterns = document.getElementById('deepExcludePatterns').value
                .split('\n').filter(p => p.trim()).map(p => p.trim());

            if (includePatterns.length > 0) config.include_patterns = includePatterns;
            if (excludePatterns.length > 0) config.exclude_patterns = excludePatterns;

            config.domain_filter = document.getElementById('deepDomainFilter').value;

            if (config.domain_filter === 'whitelist') {
                const whitelist = document.getElementById('deepDomainWhitelist').value
                    .split('\n').filter(d => d.trim()).map(d => d.trim());
                config.domain_whitelist = whitelist;
            }

            // 高级配置
            config.ignore_robots = document.getElementById('deepIgnoreRobots').checked;
            config.respect_nofollow = document.getElementById('deepRespectNoFollow').checked;
            config.avoid_duplicates = document.getElementById('deepAvoidDuplicates').checked;

            document.getElementById('deepRequestViewer').textContent = JSON.stringify(config, null, 2);
            saveConfig('deep', config);
        }

        async function executeDeepCrawl() {
            if (!connectionStatus) {
                alert('请先测试服务器连接！');
                return;
            }

            const serverUrl = document.getElementById('serverUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const loading = document.getElementById('deepLoading');
            const responseViewer = document.getElementById('deepResponseViewer');

            generateDeepConfig();
            const configText = document.getElementById('deepRequestViewer').textContent;
            const config = JSON.parse(configText);

            loading.classList.add('show');
            responseViewer.textContent = '正在深度爬取中...';

            try {
                const headers = {
                    'Content-Type': 'application/json',
                };

                if (apiKey) {
                    headers['Authorization'] = `Bearer ${apiKey}`;
                }

                // 构建符合后端CrawlRequest格式的请求体
                const crawlRequest = {
                    urls: config.urls,
                    browser_config: config.browser_config || {},
                    crawler_config: {
                        ...config,
                        urls: undefined,  // 移除urls，因为它在顶层
                        browser_config: undefined  // 移除browser_config，因为它在顶层
                    }
                };

                const response = await fetch(`${serverUrl}/crawl`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(crawlRequest)
                });

                const result = await response.json();

                if (response.ok) {
                    responseViewer.textContent = JSON.stringify(result, null, 2);

                    if (result.sitemap) {
                        document.getElementById('deepSitemapViewer').textContent = JSON.stringify(result.sitemap, null, 2);
                    }

                    if (result.statistics) {
                        document.getElementById('deepStatisticsViewer').textContent = JSON.stringify(result.statistics, null, 2);
                    }

                    showMessage('success', '深度爬取完成！');
                } else {
                    throw new Error(result.error || `HTTP ${response.status}`);
                }
            } catch (error) {
                responseViewer.textContent = `错误: ${error.message}`;
                showMessage('error', `深度爬取失败: ${error.message}`);
            } finally {
                loading.classList.remove('show');
            }
        }

        function clearDeepConfig() {
            if (confirm('确定要清空所有配置吗？')) {
                document.getElementById('deepStartUrl').value = 'https://example.com';
                document.getElementById('deepMaxDepth').value = '3';
                document.getElementById('deepMaxPages').value = '50';
                document.getElementById('deepConcurrency').value = '3';
                document.getElementById('deepDelay').value = '1';
                // 重置其他字段...
                showMessage('success', '配置已清空');
            }
        }



        // HTML处理相关函数
        function toggleHtmlInputMethod() {
            const method = document.getElementById('htmlInputMethod').value;
            document.getElementById('htmlDirectInput').style.display = method === 'direct' ? 'block' : 'none';
            document.getElementById('htmlFileInput').style.display = method === 'file' ? 'block' : 'none';
            document.getElementById('htmlUrlInput').style.display = method === 'url' ? 'block' : 'none';
        }

        // 切换HTML处理功能面板
        function toggleHtmlFeaturePanel(panelName) {
            const panelId = `html-${panelName}-feature-panel`;
            const panel = document.getElementById(panelId);
            const clickedTab = event.target;

            if (!panel) return;

            // 检查面板当前是否显示
            const isCurrentlyVisible = panel.style.display === 'block';

            if (isCurrentlyVisible) {
                // 隐藏面板并取消激活标签
                panel.style.display = 'none';
                clickedTab.classList.remove('active');
            } else {
                // 显示面板并激活标签
                panel.style.display = 'block';
                clickedTab.classList.add('active');
            }
        }

        function handleHtmlFileUpload() {
            const fileInput = document.getElementById('htmlFile');
            const file = fileInput.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('htmlContent').value = e.target.result;
                    showMessage('success', '文件上传成功');
                };
                reader.readAsText(file);
            }
        }

        function generateHtmlConfig() {
            const inputMethod = document.getElementById('htmlInputMethod').value;
            const config = {
                input_method: inputMethod,
                base_url: document.getElementById('htmlBaseUrl').value,
                output_format: document.getElementById('htmlOutputFormat').value
            };

            // 检查是否显示了HTML处理面板
            const processingPanel = document.getElementById('html-processing-feature-panel');
            const extractionPanel = document.getElementById('html-extraction-feature-panel');
            const outputPanel = document.getElementById('html-output-feature-panel');

            // HTML处理配置
            if (processingPanel && processingPanel.style.display === 'block') {
                config.processing = {
                    remove_scripts: document.getElementById('htmlRemoveScripts').checked,
                    remove_styles: document.getElementById('htmlRemoveStyles').checked,
                    remove_comments: document.getElementById('htmlRemoveComments').checked,
                    remove_empty_tags: document.getElementById('htmlRemoveEmptyTags').checked,
                    normalize_whitespace: document.getElementById('htmlNormalizeWhitespace').checked,
                    decode_entities: document.getElementById('htmlDecodeEntities').checked,
                    fix_broken_tags: document.getElementById('htmlFixBrokenTags').checked,
                    convert_relative_urls: document.getElementById('htmlConvertRelativeUrls').checked,
                    min_word_count: parseInt(document.getElementById('htmlMinWordCount').value),
                    max_word_count: parseInt(document.getElementById('htmlMaxWordCount').value),
                    preserve_formatting: document.getElementById('htmlPreserveFormatting').checked,
                    extract_tables: document.getElementById('htmlExtractTables').checked,
                    extract_lists: document.getElementById('htmlExtractLists').checked
                };

                // 排除标签
                const excludeTags = document.getElementById('htmlExcludeTags').value;
                if (excludeTags) {
                    config.processing.exclude_tags = excludeTags.split(',').map(tag => tag.trim());
                }
            }

            // 内容提取配置
            if (extractionPanel && extractionPanel.style.display === 'block') {
                config.extraction = {
                    content_selector: document.getElementById('htmlContentSelector').value,
                    title_selector: document.getElementById('htmlTitleSelector').value,
                    validate_extraction: document.getElementById('htmlValidateExtraction').checked,
                    include_metadata: document.getElementById('htmlIncludeMetadata').checked,
                    extract_structure: document.getElementById('htmlExtractStructure').checked
                };

                // 提取规则
                const extractionRules = document.getElementById('htmlExtractionRules').value;
                if (extractionRules.trim()) {
                    try {
                        config.extraction.rules = JSON.parse(extractionRules);
                    } catch (e) {
                        alert('提取规则格式错误，请检查JSON格式');
                        return;
                    }
                }
            }

            // 输出选项配置
            if (outputPanel && outputPanel.style.display === 'block') {
                config.output_options = {
                    markdown: {
                        include_links: document.getElementById('htmlMarkdownIncludeLinks').checked,
                        include_images: document.getElementById('htmlMarkdownIncludeImages').checked,
                        include_tables: document.getElementById('htmlMarkdownIncludeTables').checked,
                        preserve_formatting: document.getElementById('htmlMarkdownPreserveFormatting').checked
                    },
                    json: {
                        pretty_print: document.getElementById('htmlJsonPrettyPrint').checked,
                        include_raw: document.getElementById('htmlJsonIncludeRaw').checked,
                        include_stats: document.getElementById('htmlJsonIncludeStats').checked
                    }
                };
            }

            // 添加HTML内容
            if (inputMethod === 'direct') {
                config.html_content = document.getElementById('htmlContent').value;
            } else if (inputMethod === 'url') {
                config.url = document.getElementById('htmlUrl').value;
            }

            // 自定义模板
            const customTemplate = document.getElementById('htmlCustomTemplate').value;
            if (customTemplate.trim() && outputPanel && outputPanel.style.display === 'block') {
                if (!config.output_options) config.output_options = {};
                config.output_options.custom_template = customTemplate;
            }

            // 添加配置摘要
            config._config_summary = {
                enabled_features: {
                    html_processing: processingPanel && processingPanel.style.display === 'block',
                    content_extraction: extractionPanel && extractionPanel.style.display === 'block',
                    output_options: outputPanel && outputPanel.style.display === 'block'
                },
                input_method: inputMethod,
                output_format: config.output_format
            };

            document.getElementById('htmlRequestViewer').textContent = JSON.stringify(config, null, 2);
            saveConfig('html', config);
        }

        async function executeHtmlProcess() {
            if (!connectionStatus) {
                alert('请先测试服务器连接！');
                return;
            }

            const serverUrl = document.getElementById('serverUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const loading = document.getElementById('htmlLoading');
            const responseViewer = document.getElementById('htmlResponseViewer');
            const markdownViewer = document.getElementById('htmlMarkdownViewer');
            const extractedViewer = document.getElementById('htmlExtractedViewer');
            const cleanedViewer = document.getElementById('htmlCleanedViewer');

            generateHtmlConfig();
            const configText = document.getElementById('htmlRequestViewer').textContent;
            const config = JSON.parse(configText);

            loading.classList.add('show');
            responseViewer.textContent = '正在处理HTML...';

            try {
                const headers = {
                    'Content-Type': 'application/json',
                };

                if (apiKey) {
                    headers['Authorization'] = `Bearer ${apiKey}`;
                }

                const response = await fetch(`${serverUrl}/process_html`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(config)
                });

                const result = await response.json();

                if (response.ok) {
                    responseViewer.textContent = JSON.stringify(result, null, 2);

                    if (result.markdown) {
                        markdownViewer.textContent = result.markdown;
                    }

                    if (result.extracted_data) {
                        extractedViewer.textContent = JSON.stringify(result.extracted_data, null, 2);
                    }

                    if (result.cleaned_html) {
                        cleanedViewer.textContent = result.cleaned_html;
                    }

                    showMessage('success', 'HTML处理完成！');
                } else {
                    throw new Error(result.error || `HTTP ${response.status}`);
                }
            } catch (error) {
                responseViewer.textContent = `错误: ${error.message}`;
                markdownViewer.textContent = '处理失败';
                extractedViewer.textContent = '处理失败';
                cleanedViewer.textContent = '处理失败';
                showMessage('error', `HTML处理失败: ${error.message}`);
            } finally {
                loading.classList.remove('show');
            }
        }

        function clearHtmlConfig() {
            if (confirm('确定要清空所有配置吗？')) {
                // 基础配置
                document.getElementById('htmlInputMethod').value = 'direct';
                document.getElementById('htmlContent').value = `<!DOCTYPE html>
<html>
<head>
    <title>示例页面</title>
</head>
<body>
    <header>
        <h1>网站标题</h1>
        <nav>
            <a href="/home">首页</a>
            <a href="/about">关于</a>
        </nav>
    </header>
    <main>
        <article>
            <h2>文章标题</h2>
            <p class="author">作者：张三</p>
            <p class="date">发布时间：2024-01-01</p>
            <div class="content">
                <p>这是文章的主要内容...</p>
                <img src="image.jpg" alt="示例图片">
            </div>
        </article>
    </main>
    <footer>
        <p>&copy; 2024 示例网站</p>
    </footer>
</body>
</html>`;
                document.getElementById('htmlBaseUrl').value = '';
                document.getElementById('htmlUrl').value = '';

                // 隐藏所有功能面板
                const processingPanel = document.getElementById('html-processing-feature-panel');
                const extractionPanel = document.getElementById('html-extraction-feature-panel');
                const outputPanel = document.getElementById('html-output-feature-panel');

                if (processingPanel) processingPanel.style.display = 'none';
                if (extractionPanel) extractionPanel.style.display = 'none';
                if (outputPanel) outputPanel.style.display = 'none';

                // 取消激活所有标签
                const tabs = document.querySelectorAll('#html-processing-config .config-tab');
                tabs.forEach(tab => tab.classList.remove('active'));

                // 重置处理选项
                document.getElementById('htmlRemoveScripts').checked = true;
                document.getElementById('htmlRemoveStyles').checked = false;
                document.getElementById('htmlRemoveComments').checked = true;
                document.getElementById('htmlRemoveEmptyTags').checked = false;
                document.getElementById('htmlNormalizeWhitespace').checked = true;
                document.getElementById('htmlDecodeEntities').checked = true;
                document.getElementById('htmlFixBrokenTags').checked = false;
                document.getElementById('htmlConvertRelativeUrls').checked = false;
                document.getElementById('htmlMinWordCount').value = '10';
                document.getElementById('htmlMaxWordCount').value = '10000';
                document.getElementById('htmlPreserveFormatting').checked = false;
                document.getElementById('htmlExtractTables').checked = true;
                document.getElementById('htmlExtractLists').checked = true;
                document.getElementById('htmlExcludeTags').value = 'script,style,nav,footer,aside';

                // 重置提取配置
                document.getElementById('htmlContentSelector').value = '';
                document.getElementById('htmlTitleSelector').value = 'h1, .title, .headline';
                document.getElementById('htmlExtractionRules').value = `{
  "title": {
    "selector": "h1, .title",
    "attribute": "text",
    "required": true
  },
  "author": {
    "selector": ".author, .byline",
    "attribute": "text",
    "required": false
  },
  "date": {
    "selector": ".date, time",
    "attribute": "datetime",
    "fallback_attribute": "text",
    "required": false
  },
  "content": {
    "selector": ".content, article p",
    "attribute": "text",
    "join": "\\n",
    "required": true
  },
  "images": {
    "selector": "img",
    "attribute": "src",
    "multiple": true,
    "required": false
  },
  "links": {
    "selector": "a[href]",
    "attribute": "href",
    "multiple": true,
    "required": false
  }
}`;
                document.getElementById('htmlValidateExtraction').checked = false;
                document.getElementById('htmlIncludeMetadata').checked = true;
                document.getElementById('htmlExtractStructure').checked = false;

                // 重置输出配置
                document.getElementById('htmlOutputFormat').value = 'markdown';
                document.getElementById('htmlMarkdownIncludeLinks').checked = true;
                document.getElementById('htmlMarkdownIncludeImages').checked = true;
                document.getElementById('htmlMarkdownIncludeTables').checked = true;
                document.getElementById('htmlMarkdownPreserveFormatting').checked = false;
                document.getElementById('htmlJsonPrettyPrint').checked = true;
                document.getElementById('htmlJsonIncludeRaw').checked = false;
                document.getElementById('htmlJsonIncludeStats').checked = true;
                document.getElementById('htmlCustomTemplate').value = `标题: {{title}}
作者: {{author}}
日期: {{date}}

内容:
{{content}}

链接数量: {{links.length}}
图片数量: {{images.length}}`;

                // 更新配置面板显示
                toggleHtmlInputMethod();

                // 清空结果显示
                document.getElementById('htmlRequestViewer').textContent = '点击"生成配置"查看请求结构';
                document.getElementById('htmlResponseViewer').textContent = '点击"开始处理"查看结果';
                document.getElementById('htmlMarkdownViewer').textContent = 'Markdown内容将在这里显示';
                document.getElementById('htmlExtractedViewer').textContent = '提取的数据将在这里显示';
                document.getElementById('htmlCleanedViewer').textContent = '清理后的HTML将在这里显示';

                showMessage('success', '配置已清空');
            }
        }

        // 显示消息
        function showMessage(type, message) {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'success' ? 'success-message' : 'error-message';
            messageDiv.textContent = message;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.right = '20px';
            messageDiv.style.zIndex = '9999';
            messageDiv.style.minWidth = '300px';
            messageDiv.style.animation = 'slideIn 0.3s ease-out';

            // 添加到页面
            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 保存配置到本地存储
        function saveConfig(type, config) {
            try {
                localStorage.setItem(`crawl4ai_${type}_config`, JSON.stringify(config));
            } catch (e) {
                console.warn('无法保存配置到本地存储:', e);
            }
        }

        // 加载保存的配置
        function loadSavedConfig() {
            try {
                // 加载服务器配置
                loadServerConfig();

                // 加载爬虫配置
                loadCrawlerConfig();

                // 加载数据提取配置
                loadExtractionConfig();

                // 加载任务配置
                loadTaskConfig();

                // 加载HTML处理配置
                loadHtmlConfig();

                console.log('所有保存的配置已加载');
            } catch (e) {
                console.warn('无法加载保存的配置:', e);
            }
        }

        // 加载服务器配置
        function loadServerConfig() {
            const serverConfig = localStorage.getItem('crawl4ai_server_config');
            if (serverConfig) {
                const config = JSON.parse(serverConfig);
                if (config.serverUrl) document.getElementById('serverUrl').value = config.serverUrl;
                if (config.timeout) document.getElementById('timeout').value = config.timeout;
                if (config.apiKey) document.getElementById('apiKey').value = config.apiKey;
            }
        }

        // 加载爬虫配置
        function loadCrawlerConfig() {
            const crawlerConfig = localStorage.getItem('crawl4ai_crawler_config');
            if (crawlerConfig) {
                const config = JSON.parse(crawlerConfig);

                // 基础设置
                if (config.method) document.getElementById('crawlerMethod').value = config.method;
                if (config.verbose !== undefined) document.getElementById('crawlerVerbose').checked = config.verbose;
                if (config.check_robots_txt !== undefined) document.getElementById('crawlerCheckRobots').checked = config.check_robots_txt;

                // 浏览器环境
                if (config.user_agent) document.getElementById('crawlerUserAgent').value = config.user_agent;
                if (config.custom_user_agent) document.getElementById('crawlerCustomUserAgent').value = config.custom_user_agent;
                if (config.user_agent_mode) document.getElementById('crawlerUserAgentMode').value = config.user_agent_mode;
                if (config.locale) document.getElementById('crawlerLocale').value = config.locale;
                if (config.timezone) document.getElementById('crawlerTimezone').value = config.timezone;

                // 代理设置
                if (config.proxy_config) {
                    document.getElementById('crawlerEnableProxy').checked = true;
                    if (config.proxy_config.server) document.getElementById('crawlerProxyServer').value = config.proxy_config.server;
                    if (config.proxy_config.username) document.getElementById('crawlerProxyUsername').value = config.proxy_config.username;
                    if (config.proxy_config.password) document.getElementById('crawlerProxyPassword').value = config.proxy_config.password;
                    toggleProxyConfig(); // 显示代理配置面板
                }

                // 反反爬设置
                if (config.simulate_user !== undefined) document.getElementById('crawlerSimulateUser').checked = config.simulate_user;
                if (config.magic !== undefined) document.getElementById('crawlerMagic').checked = config.magic;
                if (config.override_navigator !== undefined) document.getElementById('crawlerOverrideNavigator').checked = config.override_navigator;
                if (config.remove_overlay_elements !== undefined) document.getElementById('crawlerRemoveOverlay').checked = config.remove_overlay_elements;
                if (config.ignore_body_visibility !== undefined) document.getElementById('crawlerIgnoreBodyVisibility').checked = config.ignore_body_visibility;
                if (config.adjust_viewport_to_content !== undefined) document.getElementById('crawlerAdjustViewport').checked = config.adjust_viewport_to_content;

                // 页面导航设置
                if (config.wait_until) document.getElementById('crawlerWaitUntil').value = config.wait_until;
                if (config.wait_for) document.getElementById('crawlerWaitFor').value = config.wait_for;
                if (config.wait_for_images !== undefined) document.getElementById('crawlerWaitForImages').checked = config.wait_for_images;
                if (config.page_timeout) document.getElementById('crawlerPageTimeout').value = config.page_timeout;
                if (config.delay_before_return_html) document.getElementById('crawlerDelayBeforeReturn').value = config.delay_before_return_html;
                if (config.js_code) document.getElementById('crawlerJsCode').value = config.js_code;
                if (config.js_only !== undefined) document.getElementById('crawlerJsOnly').checked = config.js_only;
                if (config.scan_full_page !== undefined) document.getElementById('crawlerScanFullPage').checked = config.scan_full_page;
                if (config.process_iframes !== undefined) document.getElementById('crawlerProcessIframes').checked = config.process_iframes;
                if (config.scroll_delay) document.getElementById('crawlerScrollDelay').value = config.scroll_delay;

                // 缓存策略
                if (config.cache_mode) document.getElementById('crawlerCacheMode').value = config.cache_mode;
                if (config.session_id) document.getElementById('crawlerSessionId').value = config.session_id;

                // 媒体处理
                if (config.screenshot !== undefined) document.getElementById('crawlerScreenshot').checked = config.screenshot;
                if (config.screenshot_wait_for) document.getElementById('crawlerScreenshotWait').value = config.screenshot_wait_for;
                if (config.screenshot_height_threshold) document.getElementById('crawlerScreenshotHeight').value = config.screenshot_height_threshold;
                if (config.pdf !== undefined) document.getElementById('crawlerPdf').checked = config.pdf;
                if (config.mhtml !== undefined) document.getElementById('crawlerMhtml').checked = config.mhtml;
                if (config.exclude_external_images !== undefined) document.getElementById('crawlerExcludeExternalImages').checked = config.exclude_external_images;
                if (config.exclude_all_images !== undefined) document.getElementById('crawlerExcludeAllImages').checked = config.exclude_all_images;
                if (config.image_description_min_word_threshold) document.getElementById('crawlerImageScore').value = config.image_description_min_word_threshold;
                if (config.image_description_max_word_threshold) document.getElementById('crawlerImageDescWords').value = config.image_description_max_word_threshold;
                if (config.table_min_word_threshold) document.getElementById('crawlerTableScore').value = config.table_min_word_threshold;

                // 调试监控
                if (config.capture_network !== undefined) document.getElementById('crawlerCaptureNetwork').checked = config.capture_network;
                if (config.capture_console !== undefined) document.getElementById('crawlerCaptureConsole').checked = config.capture_console;
                if (config.log_console !== undefined) document.getElementById('crawlerLogConsole').checked = config.log_console;

                // 更新条件显示的面板
                if (config.user_agent === 'custom') {
                    document.getElementById('customUserAgentGroup').style.display = 'block';
                }

                console.log('爬虫配置已恢复');
            }
        }

        // 加载数据提取配置
        function loadExtractionConfig() {
            const extractionConfig = localStorage.getItem('crawl4ai_extraction_config');
            if (extractionConfig) {
                const config = JSON.parse(extractionConfig);

                // 基础配置
                if (config.extraction_strategy) document.getElementById('extractionStrategy').value = config.extraction_strategy;
                if (config.chunking_strategy) document.getElementById('extractionChunkingStrategy').value = config.chunking_strategy;
                if (config.markdown_generator) document.getElementById('extractionMarkdownGenerator').value = config.markdown_generator;
                if (config.verbose !== undefined) document.getElementById('extractionVerbose').checked = config.verbose;
                if (config.bypass_cache !== undefined) document.getElementById('extractionBypassCache').checked = config.bypass_cache;

                // LLM提取配置
                if (config.llm_extraction) {
                    document.getElementById('enableLLMExtraction').checked = true;
                    if (config.llm_extraction.query) document.getElementById('extractionQuery').value = config.llm_extraction.query;
                    if (config.llm_extraction.provider) document.getElementById('extractionProvider').value = config.llm_extraction.provider;
                    if (config.llm_extraction.model) document.getElementById('extractionModel').value = config.llm_extraction.model;
                    if (config.llm_extraction.api_key) document.getElementById('extractionApiKey').value = config.llm_extraction.api_key;
                    if (config.llm_extraction.base_url) document.getElementById('extractionBaseUrl').value = config.llm_extraction.base_url;
                    if (config.llm_extraction.temperature !== undefined) document.getElementById('extractionTemperature').value = config.llm_extraction.temperature;
                    if (config.llm_extraction.max_tokens) document.getElementById('extractionMaxTokens').value = config.llm_extraction.max_tokens;
                    if (config.llm_extraction.top_p) document.getElementById('extractionTopP').value = config.llm_extraction.top_p;
                    toggleLLMExtractionConfig(); // 显示LLM配置面板
                }

                // CSS选择器配置
                if (config.css_extraction) {
                    document.getElementById('enableCSSExtraction').checked = true;
                    if (config.css_extraction.selectors) document.getElementById('extractionCssSelectors').value = JSON.stringify(config.css_extraction.selectors, null, 2);
                    if (config.css_extraction.include_text !== undefined) document.getElementById('extractionIncludeText').checked = config.css_extraction.include_text;
                    if (config.css_extraction.include_attributes !== undefined) document.getElementById('extractionIncludeAttributes').checked = config.css_extraction.include_attributes;
                    if (config.css_extraction.clean_text !== undefined) document.getElementById('extractionCleanText').checked = config.css_extraction.clean_text;
                    toggleCSSExtractionConfig(); // 显示CSS配置面板
                }

                // 结构化提取配置
                if (config.schema_extraction) {
                    document.getElementById('enableSchemaExtraction').checked = true;
                    if (config.schema_extraction.schema) document.getElementById('extractionSchema').value = JSON.stringify(config.schema_extraction.schema, null, 2);
                    if (config.schema_extraction.instructions) document.getElementById('extractionInstructions').value = config.schema_extraction.instructions;
                    if (config.schema_extraction.validate_schema !== undefined) document.getElementById('extractionValidateSchema').checked = config.schema_extraction.validate_schema;
                    if (config.schema_extraction.return_raw !== undefined) document.getElementById('extractionReturnRaw').checked = config.schema_extraction.return_raw;
                    toggleSchemaExtractionConfig(); // 显示Schema配置面板
                }

                // 内容处理配置
                if (config.content_processing) {
                    if (config.content_processing.word_count_threshold) document.getElementById('contentWordThreshold').value = config.content_processing.word_count_threshold;
                    if (config.content_processing.css_selector) document.getElementById('contentCssSelector').value = config.content_processing.css_selector;
                    if (config.content_processing.target_elements) document.getElementById('contentTargetElements').value = config.content_processing.target_elements.join(',');
                    if (config.content_processing.excluded_tags) document.getElementById('contentExcludedTags').value = config.content_processing.excluded_tags.join(',');
                    if (config.content_processing.excluded_selector) document.getElementById('contentExcludedSelector').value = config.content_processing.excluded_selector;
                    if (config.content_processing.remove_forms !== undefined) document.getElementById('contentRemoveForms').checked = config.content_processing.remove_forms;
                    if (config.content_processing.only_text !== undefined) document.getElementById('contentOnlyText').checked = config.content_processing.only_text;
                    if (config.content_processing.prettify !== undefined) document.getElementById('contentPrettify').checked = config.content_processing.prettify;
                    if (config.content_processing.parser_type) document.getElementById('contentParserType').value = config.content_processing.parser_type;
                    if (config.content_processing.keep_data_attributes !== undefined) document.getElementById('contentKeepDataAttributes').checked = config.content_processing.keep_data_attributes;
                    if (config.content_processing.keep_attrs) document.getElementById('contentKeepAttrs').value = config.content_processing.keep_attrs.join(',');
                }

                // 链接过滤配置
                if (config.link_filtering) {
                    if (config.link_filtering.exclude_external_links !== undefined) document.getElementById('linksExcludeExternal').checked = config.link_filtering.exclude_external_links;
                    if (config.link_filtering.exclude_internal_links !== undefined) document.getElementById('linksExcludeInternal').checked = config.link_filtering.exclude_internal_links;
                    if (config.link_filtering.exclude_social_media_links !== undefined) document.getElementById('linksExcludeSocialMedia').checked = config.link_filtering.exclude_social_media_links;
                    if (config.link_filtering.exclude_domains) document.getElementById('linksExcludeDomains').value = config.link_filtering.exclude_domains.join('\n');
                    if (config.link_filtering.social_media_domains) document.getElementById('linksSocialMediaDomains').value = config.link_filtering.social_media_domains.join('\n');
                    if (config.link_filtering.exclude_external_images !== undefined) document.getElementById('linksExcludeExternalImages').checked = config.link_filtering.exclude_external_images;
                    if (config.link_filtering.exclude_all_images !== undefined) document.getElementById('linksExcludeAllImages').checked = config.link_filtering.exclude_all_images;
                    if (config.link_filtering.image_score_threshold) document.getElementById('linksImageScore').value = config.link_filtering.image_score_threshold;
                    if (config.link_filtering.image_description_min_word_threshold) document.getElementById('linksImageDescWords').value = config.link_filtering.image_description_min_word_threshold;
                    if (config.link_filtering.table_score_threshold) document.getElementById('linksTableScore').value = config.link_filtering.table_score_threshold;
                }

                console.log('数据提取配置已恢复');
            }
        }

        // 加载任务配置
        function loadTaskConfig() {
            const taskConfig = localStorage.getItem('crawl4ai_task_config');
            if (taskConfig) {
                const config = JSON.parse(taskConfig);

                // 任务类型
                if (config.task_type) {
                    document.querySelector(`input[name="taskType"][value="${config.task_type}"]`).checked = true;
                    switchTaskType(); // 切换任务类型显示
                }

                // 单次爬取配置
                if (config.task_type === 'single' && config.url) {
                    document.getElementById('singleTaskUrl').value = config.url;
                    if (config.execution_mode) document.getElementById('singleTaskMode').value = config.execution_mode;
                    if (config.verbose !== undefined) document.getElementById('singleTaskVerbose').checked = config.verbose;
                    if (config.bypass_cache !== undefined) document.getElementById('singleTaskBypassCache').checked = config.bypass_cache;
                }

                // 批量爬取配置
                if (config.task_type === 'batch' && config.urls) {
                    document.getElementById('batchTaskUrls').value = config.urls.join('\n');
                    if (config.semaphore_count) document.getElementById('batchSemaphoreCount').value = config.semaphore_count;
                    if (config.stream !== undefined) document.getElementById('batchTaskStream').checked = config.stream;
                    if (config.mean_delay) document.getElementById('batchMeanDelay').value = config.mean_delay;
                    if (config.max_range) document.getElementById('batchMaxRange').value = config.max_range;

                    // 调度器配置
                    if (config.scheduler) {
                        document.getElementById('batchEnableScheduler').checked = true;
                        if (config.scheduler.type) document.getElementById('batchSchedulerType').value = config.scheduler.type;
                        if (config.scheduler.pool_size) document.getElementById('batchPoolSize').value = config.scheduler.pool_size;
                        if (config.scheduler.memory_threshold) document.getElementById('batchMemoryThreshold').value = config.scheduler.memory_threshold;
                        toggleBatchSchedulerConfig(); // 显示调度器配置面板
                    }

                    // 监控配置
                    if (config.monitor) {
                        document.getElementById('batchEnableMonitor').checked = true;
                        if (config.monitor.display_mode) document.getElementById('batchDisplayMode').value = config.monitor.display_mode;
                        if (config.monitor.show_progress !== undefined) document.getElementById('batchShowProgress').checked = config.monitor.show_progress;
                        if (config.monitor.log_errors !== undefined) document.getElementById('batchLogErrors').checked = config.monitor.log_errors;
                        toggleBatchMonitorConfig(); // 显示监控配置面板
                    }
                }

                // 深度爬取配置
                if (config.task_type === 'deep' && config.start_url) {
                    document.getElementById('deepTaskStartUrl').value = config.start_url;
                    if (config.max_depth) document.getElementById('deepTaskMaxDepth').value = config.max_depth;
                    if (config.max_pages) document.getElementById('deepTaskMaxPages').value = config.max_pages;
                    if (config.concurrency) document.getElementById('deepTaskConcurrency').value = config.concurrency;
                    if (config.delay) document.getElementById('deepTaskDelay').value = config.delay;
                    if (config.strategy) document.getElementById('deepTaskStrategy').value = config.strategy;
                    if (config.url_pattern) document.getElementById('deepTaskUrlPattern').value = config.url_pattern;
                    if (config.exclude_patterns) document.getElementById('deepTaskExcludePatterns').value = config.exclude_patterns.join('\n');
                }

                console.log('任务配置已恢复');
            }
        }

        // 加载HTML处理配置
        function loadHtmlConfig() {
            const htmlConfig = localStorage.getItem('crawl4ai_html_config');
            if (htmlConfig) {
                const config = JSON.parse(htmlConfig);

                // 基础配置
                if (config.input_method) {
                    document.getElementById('htmlInputMethod').value = config.input_method;
                    toggleHtmlInputMethod(); // 切换输入方式显示
                }
                if (config.html_content) document.getElementById('htmlContent').value = config.html_content;
                if (config.url) document.getElementById('htmlUrl').value = config.url;
                if (config.base_url) document.getElementById('htmlBaseUrl').value = config.base_url;
                if (config.output_format) document.getElementById('htmlOutputFormat').value = config.output_format;

                // HTML处理配置
                if (config.processing) {
                    // 显示HTML处理面板
                    const processingPanel = document.getElementById('html-processing-feature-panel');
                    if (processingPanel) {
                        processingPanel.style.display = 'block';
                        // 激活对应标签
                        const processingTab = document.querySelector('button[onclick="toggleHtmlFeaturePanel(\'processing\')"]');
                        if (processingTab) processingTab.classList.add('active');
                    }

                    if (config.processing.remove_scripts !== undefined) document.getElementById('htmlRemoveScripts').checked = config.processing.remove_scripts;
                    if (config.processing.remove_styles !== undefined) document.getElementById('htmlRemoveStyles').checked = config.processing.remove_styles;
                    if (config.processing.remove_comments !== undefined) document.getElementById('htmlRemoveComments').checked = config.processing.remove_comments;
                    if (config.processing.remove_empty_tags !== undefined) document.getElementById('htmlRemoveEmptyTags').checked = config.processing.remove_empty_tags;
                    if (config.processing.normalize_whitespace !== undefined) document.getElementById('htmlNormalizeWhitespace').checked = config.processing.normalize_whitespace;
                    if (config.processing.decode_entities !== undefined) document.getElementById('htmlDecodeEntities').checked = config.processing.decode_entities;
                    if (config.processing.fix_broken_tags !== undefined) document.getElementById('htmlFixBrokenTags').checked = config.processing.fix_broken_tags;
                    if (config.processing.convert_relative_urls !== undefined) document.getElementById('htmlConvertRelativeUrls').checked = config.processing.convert_relative_urls;
                    if (config.processing.min_word_count) document.getElementById('htmlMinWordCount').value = config.processing.min_word_count;
                    if (config.processing.max_word_count) document.getElementById('htmlMaxWordCount').value = config.processing.max_word_count;
                    if (config.processing.preserve_formatting !== undefined) document.getElementById('htmlPreserveFormatting').checked = config.processing.preserve_formatting;
                    if (config.processing.extract_tables !== undefined) document.getElementById('htmlExtractTables').checked = config.processing.extract_tables;
                    if (config.processing.extract_lists !== undefined) document.getElementById('htmlExtractLists').checked = config.processing.extract_lists;
                    if (config.processing.exclude_tags) document.getElementById('htmlExcludeTags').value = config.processing.exclude_tags.join(',');
                }

                // 内容提取配置
                if (config.extraction) {
                    // 显示内容提取面板
                    const extractionPanel = document.getElementById('html-extraction-feature-panel');
                    if (extractionPanel) {
                        extractionPanel.style.display = 'block';
                        // 激活对应标签
                        const extractionTab = document.querySelector('button[onclick="toggleHtmlFeaturePanel(\'extraction\')"]');
                        if (extractionTab) extractionTab.classList.add('active');
                    }

                    if (config.extraction.content_selector) document.getElementById('htmlContentSelector').value = config.extraction.content_selector;
                    if (config.extraction.title_selector) document.getElementById('htmlTitleSelector').value = config.extraction.title_selector;
                    if (config.extraction.validate_extraction !== undefined) document.getElementById('htmlValidateExtraction').checked = config.extraction.validate_extraction;
                    if (config.extraction.include_metadata !== undefined) document.getElementById('htmlIncludeMetadata').checked = config.extraction.include_metadata;
                    if (config.extraction.extract_structure !== undefined) document.getElementById('htmlExtractStructure').checked = config.extraction.extract_structure;
                    if (config.extraction.rules) document.getElementById('htmlExtractionRules').value = JSON.stringify(config.extraction.rules, null, 2);
                }

                // 输出选项配置
                if (config.output_options) {
                    // 显示输出选项面板
                    const outputPanel = document.getElementById('html-output-feature-panel');
                    if (outputPanel) {
                        outputPanel.style.display = 'block';
                        // 激活对应标签
                        const outputTab = document.querySelector('button[onclick="toggleHtmlFeaturePanel(\'output\')"]');
                        if (outputTab) outputTab.classList.add('active');
                    }

                    if (config.output_options.markdown) {
                        if (config.output_options.markdown.include_links !== undefined) document.getElementById('htmlMarkdownIncludeLinks').checked = config.output_options.markdown.include_links;
                        if (config.output_options.markdown.include_images !== undefined) document.getElementById('htmlMarkdownIncludeImages').checked = config.output_options.markdown.include_images;
                        if (config.output_options.markdown.include_tables !== undefined) document.getElementById('htmlMarkdownIncludeTables').checked = config.output_options.markdown.include_tables;
                        if (config.output_options.markdown.preserve_formatting !== undefined) document.getElementById('htmlMarkdownPreserveFormatting').checked = config.output_options.markdown.preserve_formatting;
                    }
                    if (config.output_options.json) {
                        if (config.output_options.json.pretty_print !== undefined) document.getElementById('htmlJsonPrettyPrint').checked = config.output_options.json.pretty_print;
                        if (config.output_options.json.include_raw !== undefined) document.getElementById('htmlJsonIncludeRaw').checked = config.output_options.json.include_raw;
                        if (config.output_options.json.include_stats !== undefined) document.getElementById('htmlJsonIncludeStats').checked = config.output_options.json.include_stats;
                    }
                    if (config.output_options.custom_template) document.getElementById('htmlCustomTemplate').value = config.output_options.custom_template;
                }

                console.log('HTML处理配置已恢复');
            }
        }



        // 切换清空按钮状态
        function toggleClearButton() {
            const switchElement = document.getElementById('clearProtectionSwitch');
            const clearBtn = document.getElementById('clearConfigBtn');
            const label = document.querySelector('.clear-protection-label');

            if (switchElement.checked) {
                // 开关打开，启用清空按钮
                clearBtn.classList.remove('clear-btn-disabled');
                clearBtn.title = '清空所有配置（不可恢复）';
                label.textContent = '已解锁';
                label.style.color = 'rgba(220, 53, 69, 0.9)';
            } else {
                // 开关关闭，禁用清空按钮
                clearBtn.classList.add('clear-btn-disabled');
                clearBtn.title = '先打开开关才能清空配置';
                label.textContent = '解锁';
                label.style.color = 'rgba(255,255,255,0.8)';
            }
        }

        // 增强的清空配置函数
        function clearAllConfigs() {
            const switchElement = document.getElementById('clearProtectionSwitch');

            // 检查保护开关是否打开
            if (!switchElement.checked) {
                showMessage('warning', '请先打开保护开关才能清空配置');
                return;
            }

            // 二次确认
            if (!confirm('⚠️ 确定要清空所有配置吗？\n\n这将删除：\n• 服务器配置\n• 爬虫配置\n• 数据提取配置\n• 任务配置\n• HTML处理配置\n\n此操作不可恢复！')) {
                return;
            }

            try {
                // 清空所有localStorage配置
                const configKeys = [
                    'crawl4ai_server_config',
                    'crawl4ai_crawler_config',
                    'crawl4ai_extraction_config',
                    'crawl4ai_task_config',
                    'crawl4ai_html_config'
                ];

                configKeys.forEach(key => {
                    localStorage.removeItem(key);
                });

                // 清空所有配置预览区域
                const viewers = [
                    'crawlerConfigViewer',
                    'extractionRequestViewer',
                    'taskConfigViewer',
                    'htmlRequestViewer'
                ];

                viewers.forEach(viewerId => {
                    const viewer = document.getElementById(viewerId);
                    if (viewer) {
                        viewer.textContent = '点击"生成配置"查看配置';
                    }
                });

                // 重置表单到默认状态
                resetAllForms();

                // 关闭保护开关
                switchElement.checked = false;
                toggleClearButton();

                // 更新状态显示
                updateConfigStatus();

                showMessage('success', '所有配置已清空');
                console.log('所有配置已清空');

            } catch (error) {
                console.error('清空配置时出错:', error);
                showMessage('error', `清空配置失败: ${error.message}`);
            }
        }

        // 重置所有表单到默认状态
        function resetAllForms() {
            try {
                // 重置服务器配置
                document.getElementById('serverUrl').value = 'http://localhost:8000';
                document.getElementById('timeout').value = '30';
                document.getElementById('apiKey').value = '';

                // 重置所有复选框
                const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    if (checkbox.id !== 'clearProtectionSwitch') {
                        checkbox.checked = false;
                    }
                });

                // 重置所有选择框到第一个选项
                const selects = document.querySelectorAll('select');
                selects.forEach(select => {
                    select.selectedIndex = 0;
                });

                // 重置所有文本输入框
                const textInputs = document.querySelectorAll('input[type="text"], input[type="number"], input[type="password"], textarea');
                textInputs.forEach(input => {
                    if (input.id !== 'serverUrl' && input.id !== 'timeout') {
                        input.value = '';
                    }
                });

                // 隐藏所有条件显示的面板
                const conditionalPanels = [
                    'proxyConfig',
                    'customUserAgentGroup',
                    'batchSchedulerConfig',
                    'batchMonitorConfig'
                ];
                conditionalPanels.forEach(panelId => {
                    const panel = document.getElementById(panelId);
                    if (panel) {
                        panel.style.display = 'none';
                    }
                });

                console.log('所有表单已重置到默认状态');
            } catch (error) {
                console.warn('重置表单时出错:', error);
            }
        }

        // 更新配置状态显示
        function updateConfigStatus() {
            try {
                const statusDiv = document.getElementById('configStatus');
                if (!statusDiv) return;

                const configs = {
                    '服务器配置': localStorage.getItem('crawl4ai_server_config'),
                    '爬虫配置': localStorage.getItem('crawl4ai_crawler_config'),
                    '数据提取配置': localStorage.getItem('crawl4ai_extraction_config'),
                    '任务配置': localStorage.getItem('crawl4ai_task_config'),
                    'HTML处理配置': localStorage.getItem('crawl4ai_html_config')
                };

                const statusItems = [];
                let totalConfigs = 0;
                let savedConfigs = 0;

                for (const [name, config] of Object.entries(configs)) {
                    totalConfigs++;
                    if (config && config !== '{}') {
                        savedConfigs++;
                        statusItems.push(`✅ ${name}`);
                    } else {
                        statusItems.push(`❌ ${name}`);
                    }
                }

                const statusText = `已保存 ${savedConfigs}/${totalConfigs} 个配置模块\n${statusItems.join('\n')}`;
                statusDiv.innerHTML = statusText.replace(/\n/g, '<br>');

                // 计算存储使用情况
                let totalSize = 0;
                for (const config of Object.values(configs)) {
                    if (config) totalSize += config.length;
                }

                const sizeText = `存储使用: ${(totalSize / 1024).toFixed(2)} KB`;
                statusDiv.innerHTML += `<br><small style="color: #999;">${sizeText}</small>`;

                // 更新导航栏状态指示器
                const navbarStatus = document.getElementById('configStatusNavbar');
                if (navbarStatus) {
                    if (savedConfigs === totalConfigs) {
                        navbarStatus.textContent = '全部已保存';
                        navbarStatus.parentElement.querySelector('.config-status-dot').style.background = '#28a745';
                    } else if (savedConfigs > 0) {
                        navbarStatus.textContent = `${savedConfigs}/${totalConfigs} 已保存`;
                        navbarStatus.parentElement.querySelector('.config-status-dot').style.background = '#ffc107';
                    } else {
                        navbarStatus.textContent = '未保存';
                        navbarStatus.parentElement.querySelector('.config-status-dot').style.background = '#dc3545';
                    }
                }

            } catch (e) {
                console.warn('更新配置状态时出错:', e);
            }
        }

        // 导出配置
        function exportConfig() {
            try {
                const config = {
                    server: {
                        url: document.getElementById('serverUrl').value,
                        timeout: document.getElementById('timeout').value,
                        api_key: document.getElementById('apiKey').value
                    }
                };

                // 安全地解析各种配置
                // 从localStorage获取保存的配置，而不是从预览区域解析
                const crawlerConfig = localStorage.getItem('crawl4ai_crawler_config');
                if (crawlerConfig) {
                    try {
                        config.crawler = JSON.parse(crawlerConfig);
                    } catch (e) {
                        console.warn('爬虫配置解析失败:', e);
                        config.crawler = {};
                    }
                } else {
                    config.crawler = {};
                }

                const extractionConfig = localStorage.getItem('crawl4ai_extraction_config');
                if (extractionConfig) {
                    try {
                        config.extraction = JSON.parse(extractionConfig);
                    } catch (e) {
                        console.warn('数据提取配置解析失败:', e);
                        config.extraction = {};
                    }
                } else {
                    config.extraction = {};
                }

                const taskConfig = localStorage.getItem('crawl4ai_task_config');
                if (taskConfig) {
                    try {
                        config.task = JSON.parse(taskConfig);
                    } catch (e) {
                        console.warn('任务配置解析失败:', e);
                        config.task = {};
                    }
                } else {
                    config.task = {};
                }

                const htmlConfig = localStorage.getItem('crawl4ai_html_config');
                if (htmlConfig) {
                    try {
                        config.html = JSON.parse(htmlConfig);
                    } catch (e) {
                        console.warn('HTML处理配置解析失败:', e);
                        config.html = {};
                    }
                } else {
                    config.html = {};
                }

                // 添加导出信息
                config._export_info = {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    description: 'Crawl4AI配置文件'
                };

                const dataStr = JSON.stringify(config, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `crawl4ai_config_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                link.click();

                URL.revokeObjectURL(url);
                showMessage('success', '配置已导出');
            } catch (error) {
                console.error('导出配置时出错:', error);
                showMessage('error', `导出配置失败: ${error.message}`);
            }
        }

        // 导入配置
        function importConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const config = JSON.parse(e.target.result);

                            // 恢复服务器配置
                            if (config.server) {
                                if (config.server.url) document.getElementById('serverUrl').value = config.server.url;
                                if (config.server.timeout) document.getElementById('timeout').value = config.server.timeout;
                                if (config.server.api_key) document.getElementById('apiKey').value = config.server.api_key;

                                // 保存服务器配置到localStorage
                                const serverConfig = {
                                    serverUrl: config.server.url || '',
                                    timeout: config.server.timeout || '30',
                                    apiKey: config.server.api_key || ''
                                };
                                localStorage.setItem('crawl4ai_server_config', JSON.stringify(serverConfig));
                            }

                            // 恢复爬虫配置
                            if (config.crawler) {
                                localStorage.setItem('crawl4ai_crawler_config', JSON.stringify(config.crawler));
                            }

                            // 恢复数据提取配置
                            if (config.extraction) {
                                localStorage.setItem('crawl4ai_extraction_config', JSON.stringify(config.extraction));
                            }

                            // 恢复任务配置
                            if (config.task) {
                                localStorage.setItem('crawl4ai_task_config', JSON.stringify(config.task));
                            }

                            // 恢复HTML处理配置
                            if (config.html) {
                                localStorage.setItem('crawl4ai_html_config', JSON.stringify(config.html));
                            }

                            // 重新加载所有配置到界面
                            loadSavedConfig();
                            updateConfigStatus();

                            showMessage('success', `配置已导入${config._export_info ? ` (导出时间: ${new Date(config._export_info.timestamp).toLocaleString()})` : ''}`);
                        } catch (error) {
                            console.error('导入配置时出错:', error);
                            showMessage('error', `配置文件格式错误: ${error.message}`);
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter 执行当前API
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                if (currentSection === 'basic-crawl-config') {
                    executeBasicCrawl();
                }
            }

            // Ctrl+S 保存配置
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                exportConfig();
            }
        });

        // 添加数据提取配置的实时保存监听器
        function addExtractionConfigListeners() {
            // 基础配置监听器
            const basicFields = ['extractionStrategy', 'extractionChunkingStrategy', 'extractionMarkdownGenerator',
                                'extractionVerbose', 'extractionBypassCache'];
            basicFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.addEventListener('change', autoSaveExtractionConfig);
                }
            });

            // LLM配置监听器
            const llmFields = ['enableLLMExtraction', 'extractionQuery', 'extractionProvider', 'extractionModel',
                              'extractionApiKey', 'extractionBaseUrl', 'extractionTemperature', 'extractionMaxTokens', 'extractionTopP'];
            llmFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.addEventListener('change', autoSaveExtractionConfig);
                    } else {
                        element.addEventListener('input', debounce(autoSaveExtractionConfig, 1000));
                    }
                }
            });

            // CSS配置监听器
            const cssFields = ['enableCSSExtraction', 'extractionCssSelectors', 'extractionIncludeText',
                              'extractionIncludeAttributes', 'extractionCleanText'];
            cssFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.addEventListener('change', autoSaveExtractionConfig);
                    } else {
                        element.addEventListener('input', debounce(autoSaveExtractionConfig, 1000));
                    }
                }
            });

            // Schema配置监听器
            const schemaFields = ['enableSchemaExtraction', 'extractionSchema', 'extractionInstructions',
                                 'extractionValidateSchema', 'extractionReturnRaw'];
            schemaFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.addEventListener('change', autoSaveExtractionConfig);
                    } else {
                        element.addEventListener('input', debounce(autoSaveExtractionConfig, 1000));
                    }
                }
            });

            // 内容处理配置监听器
            const contentFields = ['contentWordThreshold', 'contentCssSelector', 'contentTargetElements',
                                  'contentExcludedTags', 'contentExcludedSelector', 'contentRemoveForms',
                                  'contentOnlyText', 'contentPrettify', 'contentParserType',
                                  'contentKeepDataAttributes', 'contentKeepAttrs'];
            contentFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.addEventListener('change', autoSaveExtractionConfig);
                    } else {
                        element.addEventListener('input', debounce(autoSaveExtractionConfig, 1000));
                    }
                }
            });

            // 链接过滤配置监听器
            const linkFields = ['linksExcludeExternal', 'linksExcludeInternal', 'linksExcludeSocialMedia',
                               'linksExcludeDomains', 'linksSocialMediaDomains', 'linksExcludeExternalImages',
                               'linksExcludeAllImages', 'linksImageScore', 'linksImageDescWords', 'linksTableScore'];
            linkFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.addEventListener('change', autoSaveExtractionConfig);
                    } else {
                        element.addEventListener('input', debounce(autoSaveExtractionConfig, 1000));
                    }
                }
            });
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 页面加载完成后恢复配置
        window.addEventListener('DOMContentLoaded', function() {
            // 延迟加载配置，确保所有元素都已渲染
            setTimeout(function() {
                loadSavedConfig();
                updateConfigStatus();

                // 添加实时保存监听器
                addExtractionConfigListeners();
                addTaskConfigListeners();

                // 初始化监控显示模式
                updateMonitorDisplayMode();
                updateBatchMonitorDisplayMode();

                // 检查是否有保存的配置
                const hasConfigs = localStorage.getItem('crawl4ai_server_config') ||
                                 localStorage.getItem('crawl4ai_crawler_config') ||
                                 localStorage.getItem('crawl4ai_extraction_config') ||
                                 localStorage.getItem('crawl4ai_task_config') ||
                                 localStorage.getItem('crawl4ai_html_config');

                if (hasConfigs) {
                    showMessage('success', '历史配置已恢复');
                } else {
                    showMessage('info', '欢迎使用 Crawl4AI 测试工具');
                }
            }, 500);
        });

        // 页面卸载前保存当前配置
        window.addEventListener('beforeunload', function() {
            // 保存服务器配置
            const serverConfig = {
                serverUrl: document.getElementById('serverUrl').value,
                timeout: document.getElementById('timeout').value,
                apiKey: document.getElementById('apiKey').value
            };
            saveConfig('server', serverConfig);

            // 自动保存当前所有配置
            autoSaveAllConfigs();
        });

        // 自动保存所有配置
        function autoSaveAllConfigs() {
            try {
                // 如果当前配置预览区域有内容，说明用户已经生成了配置，需要保存
                const crawlerConfigViewer = document.getElementById('crawlerConfigViewer');
                if (crawlerConfigViewer && crawlerConfigViewer.textContent.trim() && crawlerConfigViewer.textContent !== '点击"生成配置"查看爬虫配置') {
                    // 爬虫配置已生成，保存当前表单状态
                    if (typeof generateCrawlerConfig === 'function') {
                        generateCrawlerConfig();
                    }
                }

                const extractionRequestViewer = document.getElementById('extractionRequestViewer');
                if (extractionRequestViewer && extractionRequestViewer.textContent.trim() && extractionRequestViewer.textContent !== '点击"生成配置"查看请求结构') {
                    // 数据提取配置已生成，保存当前表单状态
                    if (typeof generateExtractionConfig === 'function') {
                        generateExtractionConfig();
                    }
                }

                const taskConfigViewer = document.getElementById('taskConfigViewer');
                if (taskConfigViewer && taskConfigViewer.textContent.trim() && taskConfigViewer.textContent !== '点击"生成配置"查看任务配置') {
                    // 任务配置已生成，保存当前表单状态
                    if (typeof generateTaskConfig === 'function') {
                        generateTaskConfig();
                    }
                }

                const htmlRequestViewer = document.getElementById('htmlRequestViewer');
                if (htmlRequestViewer && htmlRequestViewer.textContent.trim() && htmlRequestViewer.textContent !== '点击"生成配置"查看请求结构') {
                    // HTML处理配置已生成，保存当前表单状态
                    if (typeof generateHtmlConfig === 'function') {
                        generateHtmlConfig();
                    }
                }

                // 更新配置状态显示
                updateConfigStatus();

                console.log('所有配置已自动保存');
                showMessage('success', '配置已保存');
            } catch (e) {
                console.warn('自动保存配置时出错:', e);
                showMessage('error', '保存配置时出错');
            }
        }
    </script>
</body>
</html>