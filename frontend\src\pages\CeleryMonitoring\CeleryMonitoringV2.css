.celery-monitoring-v2 {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.monitoring-header {
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.overview-stat {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  font-size: 24px;
  margin-right: 12px;
  width: 40px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 系统状态面板样式 */
.system-status-panel {
  padding: 16px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.status-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.status-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.status-card-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.status-indicator.healthy {
  background-color: #52c41a;
}

.status-indicator.unhealthy {
  background-color: #ff4d4f;
}

.status-indicator.warning {
  background-color: #faad14;
}

.status-details {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

/* 日志流面板样式 */
.log-stream-panel {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.log-container {
  flex: 1;
  background: #1f1f1f;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.log-content {
  height: 100%;
  overflow-y: auto;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #f0f0f0;
}

.log-entry {
  margin-bottom: 2px;
  padding: 2px 0;
  word-break: break-all;
}

.log-entry.error {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  padding: 4px;
  border-radius: 3px;
}

.log-entry.warning {
  color: #ffd93d;
  background-color: rgba(255, 217, 61, 0.1);
  padding: 4px;
  border-radius: 3px;
}

.log-entry.info {
  color: #74c0fc;
}

.log-entry.debug {
  color: #b197fc;
}

.log-timestamp {
  color: #868e96;
  margin-right: 8px;
}

.log-level {
  display: inline-block;
  width: 60px;
  text-align: center;
  margin-right: 8px;
  font-weight: bold;
}

.log-message {
  word-wrap: break-word;
}

/* 任务管理面板样式 */
.task-management-panel {
  padding: 16px;
}

.task-filters {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.task-table {
  background: white;
  border-radius: 6px;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.task-state-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
}

/* Beat调度面板样式 */
.beat-schedule-panel {
  padding: 16px;
}

.schedule-item {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  transition: all 0.3s ease;
}

.schedule-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.schedule-item.disabled {
  opacity: 0.6;
  background: #f5f5f5;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.schedule-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.schedule-controls {
  display: flex;
  gap: 8px;
}

.schedule-details {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.schedule-timing {
  margin-top: 8px;
  padding: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  font-family: monospace;
  font-size: 11px;
}

/* 性能监控面板样式 */
.performance-panel {
  padding: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background: white;
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-trend {
  font-size: 11px;
  margin-top: 4px;
}

.metric-trend.up {
  color: #52c41a;
}

.metric-trend.down {
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .celery-monitoring-v2 {
    padding: 12px;
  }
  
  .monitoring-header {
    padding: 12px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .log-stream-panel {
    height: 400px;
  }
  
  .log-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
  width: 8px;
}

.log-content::-webkit-scrollbar-track {
  background: #2f2f2f;
}

.log-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 动画效果 */
.status-card {
  transition: all 0.3s ease;
}

.status-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.log-entry {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
