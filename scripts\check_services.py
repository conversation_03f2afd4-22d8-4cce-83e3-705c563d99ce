#!/usr/bin/env python3
"""
MonIt 服务状态检查脚本

检查所有必需服务的运行状态
"""

import asyncio
import logging
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Tuple

import requests
import redis

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class ServiceChecker:
    """服务状态检查器"""
    
    def __init__(self):
        self.services = {
            'docker': {'name': 'Docker', 'required': True},
            'conda': {'name': 'Conda', 'required': True},
            'redis': {'name': 'Redis', 'required': True, 'port': 6379},
            'timescaledb': {'name': 'TimescaleDB', 'required': False, 'port': 5432},
            'backend': {'name': 'FastAPI Backend', 'required': False, 'port': 8000},
            'frontend': {'name': 'React Frontend', 'required': False, 'port': 3000},
        }
        
        self.results = {}
    
    def check_command_available(self, command: str) -> bool:
        """检查命令是否可用"""
        try:
            subprocess.run([command, '--version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def check_port_open(self, port: int, host: str = 'localhost') -> bool:
        """检查端口是否开放"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(3)
                result = sock.connect_ex((host, port))
                return result == 0
        except Exception:
            return False
    
    def check_docker_container(self, container_name: str) -> Tuple[bool, str]:
        """检查Docker容器状态"""
        try:
            result = subprocess.run(
                ['docker', 'ps', '--filter', f'name={container_name}', '--format', '{{.Status}}'],
                capture_output=True, text=True, check=True
            )
            if result.stdout.strip():
                return True, result.stdout.strip()
            else:
                return False, "Not running"
        except Exception as e:
            return False, str(e)
    
    def check_redis_connection(self) -> Tuple[bool, str]:
        """检查Redis连接"""
        try:
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=3)
            response = r.ping()
            if response:
                info = r.info()
                version = info.get('redis_version', 'unknown')
                return True, f"Connected (v{version})"
            else:
                return False, "Ping failed"
        except Exception as e:
            return False, str(e)
    
    def check_http_service(self, url: str) -> Tuple[bool, str]:
        """检查HTTP服务"""
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return True, f"HTTP {response.status_code}"
            else:
                return False, f"HTTP {response.status_code}"
        except Exception as e:
            return False, str(e)
    
    def check_conda_environment(self) -> Tuple[bool, str]:
        """检查conda环境"""
        try:
            result = subprocess.run(
                ['conda', 'env', 'list'],
                capture_output=True, text=True, check=True
            )
            if 'monit' in result.stdout:
                return True, "Environment exists"
            else:
                return False, "Environment not found"
        except Exception as e:
            return False, str(e)
    
    def run_checks(self) -> Dict[str, Dict]:
        """运行所有检查"""
        print("🔍 MonIt 服务状态检查")
        print("=" * 50)
        
        # 检查基础命令
        print("\n📦 基础环境检查:")
        
        # Docker
        docker_available = self.check_command_available('docker')
        self.results['docker'] = {
            'status': docker_available,
            'message': "Available" if docker_available else "Not installed",
            'required': True
        }
        print(f"  {'✅' if docker_available else '❌'} Docker: {self.results['docker']['message']}")
        
        # Conda
        conda_available = self.check_command_available('conda')
        if conda_available:
            conda_env_ok, conda_msg = self.check_conda_environment()
            self.results['conda'] = {
                'status': conda_env_ok,
                'message': conda_msg,
                'required': True
            }
        else:
            self.results['conda'] = {
                'status': False,
                'message': "Not installed",
                'required': True
            }
        print(f"  {'✅' if self.results['conda']['status'] else '❌'} Conda (monit env): {self.results['conda']['message']}")
        
        # 检查Docker容器
        print("\n🐳 Docker容器检查:")
        
        # Redis容器
        redis_running, redis_status = self.check_docker_container('monit-redis')
        if not redis_running:
            redis_running, redis_status = self.check_docker_container('monit-redis-test')
        
        self.results['redis_container'] = {
            'status': redis_running,
            'message': redis_status,
            'required': False
        }
        print(f"  {'✅' if redis_running else '⚠️'} Redis Container: {redis_status}")
        
        # TimescaleDB容器
        db_running, db_status = self.check_docker_container('monit-timescaledb')
        self.results['timescaledb_container'] = {
            'status': db_running,
            'message': db_status,
            'required': False
        }
        print(f"  {'✅' if db_running else '⚠️'} TimescaleDB Container: {db_status}")
        
        # 检查服务连接
        print("\n🌐 服务连接检查:")
        
        # Redis连接
        redis_conn_ok, redis_conn_msg = self.check_redis_connection()
        self.results['redis_connection'] = {
            'status': redis_conn_ok,
            'message': redis_conn_msg,
            'required': True
        }
        print(f"  {'✅' if redis_conn_ok else '❌'} Redis Connection: {redis_conn_msg}")
        
        # 检查HTTP服务
        print("\n🚀 应用服务检查:")
        
        # Backend API
        backend_ok, backend_msg = self.check_http_service('http://localhost:8000/health')
        self.results['backend'] = {
            'status': backend_ok,
            'message': backend_msg,
            'required': False
        }
        print(f"  {'✅' if backend_ok else '⚠️'} Backend API: {backend_msg}")
        
        # Frontend
        frontend_ok, frontend_msg = self.check_http_service('http://localhost:3000')
        self.results['frontend'] = {
            'status': frontend_ok,
            'message': frontend_msg,
            'required': False
        }
        print(f"  {'✅' if frontend_ok else '⚠️'} Frontend: {frontend_msg}")
        
        return self.results
    
    def print_summary(self):
        """打印检查结果摘要"""
        print("\n" + "=" * 50)
        print("📊 检查结果摘要")
        print("=" * 50)
        
        required_services = [k for k, v in self.results.items() if v.get('required', False)]
        optional_services = [k for k, v in self.results.items() if not v.get('required', False)]
        
        required_ok = all(self.results[svc]['status'] for svc in required_services)
        optional_count = sum(1 for svc in optional_services if self.results[svc]['status'])
        
        print(f"\n🔧 必需服务: {'✅ 全部正常' if required_ok else '❌ 存在问题'}")
        for svc in required_services:
            status = '✅' if self.results[svc]['status'] else '❌'
            print(f"   {status} {svc}: {self.results[svc]['message']}")
        
        print(f"\n🚀 可选服务: {optional_count}/{len(optional_services)} 运行中")
        for svc in optional_services:
            status = '✅' if self.results[svc]['status'] else '⚠️'
            print(f"   {status} {svc}: {self.results[svc]['message']}")
        
        print(f"\n🎯 系统状态: ", end="")
        if required_ok:
            if optional_count >= 2:
                print("🟢 完全就绪")
            elif optional_count >= 1:
                print("🟡 基本就绪")
            else:
                print("🟡 最小就绪")
        else:
            print("🔴 需要修复")
        
        print("\n💡 建议操作:")
        if not required_ok:
            print("   1. 修复必需服务问题")
            if not self.results.get('docker', {}).get('status'):
                print("      - 安装Docker: https://www.docker.com/products/docker-desktop")
            if not self.results.get('conda', {}).get('status'):
                print("      - 运行环境设置: scripts/setup_environment.bat")
            if not self.results.get('redis_connection', {}).get('status'):
                print("      - 启动Redis: scripts/start_task_system.bat")
        else:
            print("   ✅ 可以运行任务调度系统测试")
            if optional_count < 2:
                print("   💡 启动更多服务: scripts/start_dev.bat")


def main():
    """主函数"""
    checker = ServiceChecker()
    results = checker.run_checks()
    checker.print_summary()
    
    # 返回适当的退出码
    required_ok = all(
        results[svc]['status'] 
        for svc, info in results.items() 
        if info.get('required', False)
    )
    
    sys.exit(0 if required_ok else 1)


if __name__ == "__main__":
    main()
