#!/usr/bin/env python3
"""
清空Celery Worker当前执行的任务
包括真实的Celery任务和统一执行服务中的任务
"""

import asyncio
import requests
import redis
import json
from datetime import datetime

# 配置
BACKEND_URL = "http://localhost:8000"
REDIS_URL = "redis://localhost:6379/0"

def clear_celery_native_tasks():
    """清空Celery原生任务"""
    print("🔍 清空Celery原生任务...")
    
    try:
        # 1. 获取当前活跃任务
        response = requests.get(f"{BACKEND_URL}/api/v1/celery/tasks/active")
        if response.status_code == 200:
            active_tasks = response.json()
            celery_tasks = [task for task in active_tasks if 'celery@' in task.get('worker', '')]
            
            print(f"   找到 {len(celery_tasks)} 个Celery原生任务")
            
            # 2. 撤销活跃任务
            for task in celery_tasks:
                task_id = task.get('id')
                if task_id:
                    try:
                        # 调用撤销任务API
                        revoke_response = requests.post(f"{BACKEND_URL}/api/v1/celery/tasks/{task_id}/revoke")
                        if revoke_response.status_code == 200:
                            print(f"   ✅ 撤销任务: {task_id}")
                        else:
                            print(f"   ❌ 撤销任务失败: {task_id} - {revoke_response.status_code}")
                    except Exception as e:
                        print(f"   ❌ 撤销任务异常: {task_id} - {e}")
            
            return len(celery_tasks)
        else:
            print(f"   ❌ 获取活跃任务失败: {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"   ❌ 清空Celery原生任务失败: {e}")
        return 0

async def clear_unified_execution_tasks():
    """清空统一执行服务中的任务"""
    print("🔍 清空统一执行服务任务...")
    
    try:
        # 连接Redis
        redis_client = redis.Redis.from_url(REDIS_URL, decode_responses=True)
        
        # 1. 获取所有状态的执行记录
        statuses = ['running', 'pending']
        total_cleared = 0
        
        for status in statuses:
            execution_ids = redis_client.smembers(f"url_executions:by_status:{status}")
            print(f"   找到 {len(execution_ids)} 个 {status} 状态的任务")
            
            for execution_id in execution_ids:
                try:
                    # 获取执行记录详情
                    execution_data = redis_client.hgetall(f"url_executions:{execution_id}")
                    
                    if execution_data:
                        # 更新状态为cancelled
                        redis_client.hset(f"url_executions:{execution_id}", "status", "cancelled")
                        redis_client.hset(f"url_executions:{execution_id}", "completed_at", datetime.now().isoformat())
                        redis_client.hset(f"url_executions:{execution_id}", "error_message", "Manually cancelled")
                        
                        # 从状态集合中移除
                        redis_client.srem(f"url_executions:by_status:{status}", execution_id)
                        
                        # 添加到cancelled状态集合
                        redis_client.sadd("url_executions:by_status:cancelled", execution_id)
                        
                        total_cleared += 1
                        print(f"   ✅ 取消任务: {execution_id}")
                        
                except Exception as e:
                    print(f"   ❌ 取消任务失败: {execution_id} - {e}")
        
        # 2. 清空Worker当前执行任务记录
        worker_keys = redis_client.keys("current_executions:by_worker:*")
        for key in worker_keys:
            redis_client.delete(key)
            print(f"   ✅ 清空Worker执行记录: {key}")
        
        # 3. 清空会话状态
        session_keys = redis_client.keys("execution_sessions:*")
        for key in session_keys:
            session_data = redis_client.hgetall(key)
            if session_data.get("status") in ["running", "pending"]:
                redis_client.hset(key, "status", "cancelled")
                redis_client.hset(key, "completed_at", datetime.now().isoformat())
                print(f"   ✅ 取消执行会话: {key}")
        
        return total_cleared
        
    except Exception as e:
        print(f"   ❌ 清空统一执行服务任务失败: {e}")
        return 0

def clear_redis_task_queues():
    """清空Redis中的任务队列"""
    print("🔍 清空Redis任务队列...")
    
    try:
        redis_client = redis.Redis.from_url(REDIS_URL, decode_responses=True)
        
        # 常见的Celery队列名称
        queue_names = [
            "celery",
            "default", 
            "crawl_queue",
            "monitoring_queue"
        ]
        
        total_cleared = 0
        
        for queue_name in queue_names:
            queue_length = redis_client.llen(queue_name)
            if queue_length > 0:
                redis_client.delete(queue_name)
                total_cleared += queue_length
                print(f"   ✅ 清空队列 {queue_name}: {queue_length} 个任务")
        
        return total_cleared
        
    except Exception as e:
        print(f"   ❌ 清空Redis队列失败: {e}")
        return 0

def restart_celery_worker():
    """重启Celery Worker"""
    print("🔍 重启Celery Worker...")
    
    try:
        # 通过Docker重启Celery Worker
        import subprocess
        result = subprocess.run(
            ["docker", "compose", "-f", "docker-compose.dev.yml", "restart", "celery"],
            cwd="c:\\Users\\<USER>\\Desktop\\MonIt",
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("   ✅ Celery Worker重启成功")
            return True
        else:
            print(f"   ❌ Celery Worker重启失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 重启Celery Worker异常: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始清空Celery Worker当前执行的任务\n")
    
    # 1. 获取清空前的状态
    print("📊 清空前状态:")
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/celery/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"   活跃任务: {stats.get('active_tasks', 0)}")
            print(f"   等待任务: {stats.get('pending_tasks', 0)}")
            print(f"   在线Worker: {stats.get('workers_online', 0)}")
        else:
            print("   ❌ 无法获取状态")
    except:
        print("   ❌ 无法连接到API")
    
    print()
    
    # 2. 清空Celery原生任务
    celery_cleared = clear_celery_native_tasks()
    print()
    
    # 3. 清空统一执行服务任务
    unified_cleared = await clear_unified_execution_tasks()
    print()
    
    # 4. 清空Redis队列
    queue_cleared = clear_redis_task_queues()
    print()
    
    # 5. 重启Celery Worker
    worker_restarted = restart_celery_worker()
    print()
    
    # 6. 等待一段时间后检查清空后的状态
    print("⏳ 等待5秒后检查清空效果...")
    await asyncio.sleep(5)
    
    print("📊 清空后状态:")
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/celery/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"   活跃任务: {stats.get('active_tasks', 0)}")
            print(f"   等待任务: {stats.get('pending_tasks', 0)}")
            print(f"   在线Worker: {stats.get('workers_online', 0)}")
        else:
            print("   ❌ 无法获取状态")
    except:
        print("   ❌ 无法连接到API")
    
    # 7. 汇总结果
    print("\n" + "="*50)
    print("📋 清空结果汇总:")
    print("="*50)
    print(f"Celery原生任务: {celery_cleared} 个")
    print(f"统一执行服务任务: {unified_cleared} 个")
    print(f"Redis队列任务: {queue_cleared} 个")
    print(f"Worker重启: {'✅ 成功' if worker_restarted else '❌ 失败'}")
    
    total_cleared = celery_cleared + unified_cleared + queue_cleared
    print(f"\n总计清空: {total_cleared} 个任务")
    
    if total_cleared > 0 or worker_restarted:
        print("\n🎉 任务清空完成！")
        print("\n💡 建议:")
        print("   - 刷新Celery监控页面查看效果")
        print("   - 检查是否还有残留任务")
        print("   - 如需要可以重新创建测试任务")
    else:
        print("\n⚠️  没有找到需要清空的任务")

if __name__ == "__main__":
    asyncio.run(main())
