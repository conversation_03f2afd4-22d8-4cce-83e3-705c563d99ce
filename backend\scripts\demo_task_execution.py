#!/usr/bin/env python3
"""
任务执行系统演示脚本
展示如何使用新的任务执行服务记录和跟踪任务执行
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.task_execution_service import task_execution_service
from app.services.crawler_worker_service import crawler_worker_service
from app.schemas.crawler_worker import TaskExecutionRecord


async def demo_task_execution():
    """演示任务执行流程"""
    print("🚀 任务执行系统演示")
    print("=" * 50)
    
    # 假设的Worker ID和任务信息
    worker_id = "demo_worker_001"
    task_id = "demo_task_001"
    task_name = "演示监控任务"
    total_urls = 100
    
    try:
        # 1. 开始任务执行
        print(f"📋 开始任务执行: {task_name}")
        execution_record = await task_execution_service.start_task_execution(
            task_id=task_id,
            worker_id=worker_id,
            task_name=task_name,
            total_urls=total_urls,
            priority="high"
        )
        print(f"✅ 任务执行已开始: {execution_record.execution_id}")
        
        # 2. 模拟任务执行过程
        print("\n🔄 模拟任务执行过程...")
        
        # 第一次更新：处理了30个URL
        await task_execution_service.update_task_execution(
            execution_id=execution_record.execution_id,
            processed_urls=30,
            success_urls=28,
            failed_urls=2,
            memory_usage_mb=256.5,
            cpu_usage_percent=45.2
        )
        print("📊 进度更新: 30/100 URLs 处理完成")
        
        # 第二次更新：处理了60个URL
        await task_execution_service.update_task_execution(
            execution_id=execution_record.execution_id,
            processed_urls=60,
            success_urls=55,
            failed_urls=5,
            memory_usage_mb=312.8,
            cpu_usage_percent=52.1
        )
        print("📊 进度更新: 60/100 URLs 处理完成")
        
        # 第三次更新：处理了100个URL（完成）
        await task_execution_service.update_task_execution(
            execution_id=execution_record.execution_id,
            processed_urls=100,
            success_urls=92,
            failed_urls=8,
            memory_usage_mb=298.3,
            cpu_usage_percent=38.7
        )
        print("📊 进度更新: 100/100 URLs 处理完成")
        
        # 3. 完成任务执行
        final_stats = {
            "avg_response_time_ms": 1250.5,
            "throughput_per_minute": 8.5
        }
        
        await task_execution_service.complete_task_execution(
            execution_id=execution_record.execution_id,
            status="completed",
            final_stats=final_stats
        )
        print("✅ 任务执行已完成")
        
        # 4. 查看执行记录
        print("\n📋 查看执行记录:")
        final_record = await task_execution_service.get_execution_record(execution_record.execution_id)
        if final_record:
            print(f"  执行ID: {final_record.execution_id}")
            print(f"  任务名称: {final_record.task_name}")
            print(f"  Worker: {final_record.worker_name}")
            print(f"  状态: {final_record.status}")
            print(f"  总URL数: {final_record.total_urls}")
            print(f"  成功URL数: {final_record.success_urls}")
            print(f"  失败URL数: {final_record.failed_urls}")
            print(f"  执行时间: {final_record.execution_time_seconds:.2f}秒")
            print(f"  平均响应时间: {final_record.avg_response_time_ms}ms")
            print(f"  吞吐量: {final_record.throughput_per_minute}/分钟")
        
        # 5. 查看Worker的任务分配记录
        print(f"\n📋 查看Worker {worker_id} 的任务分配记录:")
        assignments = await crawler_worker_service.get_worker_assignments(worker_id, limit=5)
        
        if assignments:
            for i, assignment in enumerate(assignments, 1):
                print(f"  {i}. {assignment.task_name}")
                print(f"     状态: {assignment.status}")
                print(f"     分配时间: {assignment.assigned_at}")
                print(f"     成功率: {assignment.success_urls}/{assignment.total_urls}")
                if assignment.execution_time_seconds:
                    print(f"     执行时间: {assignment.execution_time_seconds:.2f}秒")
                print()
        else:
            print("  暂无任务分配记录")
        
        # 6. 查看活跃执行
        print("\n🔄 查看活跃执行:")
        active_executions = await task_execution_service.get_active_executions()
        if active_executions:
            for execution in active_executions:
                print(f"  - {execution.task_name} (Worker: {execution.worker_name})")
                print(f"    状态: {execution.status}")
                print(f"    进度: {execution.processed_urls}/{execution.total_urls}")
        else:
            print("  当前没有活跃的任务执行")
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def create_demo_worker():
    """创建演示用的Worker"""
    try:
        from app.schemas.crawler_worker import CrawlerWorkerCreate
        
        worker_data = CrawlerWorkerCreate(
            worker_name="演示Worker",
            description="用于演示任务执行系统的Worker",
            crawler_config_id="demo_crawler_config",
            backend_config_id="demo_backend_config",
            max_concurrent_tasks=5,
            allocated_concurrent=3,
            priority="normal",
            tags=["demo", "test"]
        )
        
        # 注意：这里需要先有对应的配置才能创建Worker
        # 在实际使用中，需要先创建爬取配置和后端配置
        print("💡 提示: 请先创建对应的爬取配置和后端配置，然后创建Worker")
        
    except Exception as e:
        print(f"创建演示Worker时出错: {e}")


async def main():
    """主函数"""
    print("任务执行系统演示")
    print("请选择操作:")
    print("1. 演示任务执行流程")
    print("2. 创建演示Worker")
    print("3. 退出")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        await demo_task_execution()
    elif choice == "2":
        await create_demo_worker()
    elif choice == "3":
        print("再见！")
    else:
        print("无效选择")


if __name__ == "__main__":
    asyncio.run(main())
