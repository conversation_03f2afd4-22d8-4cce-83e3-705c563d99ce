#!/usr/bin/env python3
"""
调试Worker任务分配问题的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def debug_worker_assignments():
    """调试Worker任务分配问题"""
    
    print("🔍 调试Worker任务分配问题...")
    
    worker_id = "46ac2f77-1e6a-401c-8853-e22f03ccaa94"  # 11237 Worker
    
    print(f"\n📋 Worker ID: {worker_id}")
    
    # 1. 检查Worker基本信息
    print(f"\n1️⃣ 检查Worker基本信息...")
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}")
        if response.status_code == 200:
            worker = response.json()
            print(f"✅ Worker名称: {worker['worker_name']}")
            print(f"✅ Worker状态: {worker['status']}")
        else:
            print(f"❌ 获取Worker信息失败: {response.text}")
            return
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 2. 检查Worker任务分配API
    print(f"\n2️⃣ 检查Worker任务分配API...")
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}/assignments")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"返回的分配数量: {len(assignments)}")
            
            if assignments:
                for i, assignment in enumerate(assignments, 1):
                    print(f"\n  分配 {i}:")
                    print(f"    分配ID: {assignment['assignment_id']}")
                    print(f"    任务名称: {assignment['task_name']}")
                    print(f"    分配状态: {assignment['assignment_status']}")
                    print(f"    创建时间: {assignment['created_at']}")
            else:
                print(f"❌ 返回空数组")
        else:
            print(f"❌ API调用失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 3. 检查任务分配服务的list_assignments
    print(f"\n3️⃣ 检查任务分配列表...")
    try:
        response = requests.get(f"{BASE_URL}/task-assignments/?worker_id={worker_id}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"任务分配服务返回的分配数量: {len(assignments)}")
            
            if assignments:
                for i, assignment in enumerate(assignments, 1):
                    print(f"\n  分配 {i}:")
                    print(f"    分配ID: {assignment['assignment_id']}")
                    print(f"    任务ID: {assignment['task_id']}")
                    print(f"    分配名称: {assignment['assignment_name']}")
                    print(f"    状态: {assignment['status']}")
                    print(f"    Worker数量: {assignment['worker_count']}")
                    print(f"    Worker IDs: {assignment['worker_ids']}")
            else:
                print(f"❌ 任务分配服务也返回空数组")
        else:
            print(f"❌ 任务分配服务调用失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 4. 直接检查具体分配
    print(f"\n4️⃣ 检查具体分配...")
    assignment_ids = [
        "8c7f092f-fe99-456a-9dfe-86f02058a855",
        "e56dfed4-f3c1-4fad-8983-f2e0fbbe1f09",
        "f2aeb29f-791d-4f2d-92f3-298c20cde238",
        "758b0f2d-c8cb-441c-bb7b-4c9c805fff72"
    ]
    
    for assignment_id in assignment_ids:
        try:
            response = requests.get(f"{BASE_URL}/task-assignments/{assignment_id}")
            if response.status_code == 200:
                assignment = response.json()
                print(f"\n  分配 {assignment_id}:")
                print(f"    状态: {assignment['status']}")
                print(f"    Worker IDs: {assignment['worker_ids']}")
                print(f"    包含目标Worker: {worker_id in assignment['worker_ids']}")
                
                if worker_id in assignment['worker_ids']:
                    print(f"    ✅ 这个分配包含目标Worker")
                    print(f"    任务ID: {assignment['task_id']}")
                    print(f"    分配名称: {assignment['assignment_name']}")
                    print(f"    创建时间: {assignment['created_at']}")
            else:
                print(f"  ❌ 分配 {assignment_id} 获取失败: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 分配 {assignment_id} 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始调试Worker任务分配问题\n")
    debug_worker_assignments()

if __name__ == "__main__":
    main()
