#!/usr/bin/env python3
"""
测试任务详情API

验证修复后的任务详情API是否正常工作
"""

import requests
import json

def test_task_detail_api():
    """测试任务详情API"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试任务详情API")
    print("=" * 50)
    
    try:
        # 首先获取任务列表，找到一个任务ID
        print("\n📋 步骤1: 获取任务列表")
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response.status_code}")
        
        if list_response.status_code != 200:
            print("  ❌ 获取任务列表失败")
            print(f"  错误: {list_response.text}")
            return
        
        list_data = list_response.json()
        tasks = list_data.get('data', [])
        
        if not tasks:
            print("  ℹ️ 没有任务可供测试，创建一个测试任务")
            
            # 创建测试任务
            create_data = {
                "name": "详情API测试任务",
                "description": "用于测试任务详情API的任务",
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai"
                },
                "config": {
                    "platform": "mercadolibre",
                    "batch_size": 10,
                    "max_retries": 3,
                    "timeout": 300,
                    "priority": "medium"
                },
                "urls": [
                    "https://example.com/product/1",
                    "https://example.com/product/2"
                ]
            }
            
            create_response = requests.post(f"{base_url}/api/v1/monitoring-tasks/", json=create_data, timeout=10)
            if create_response.status_code == 200:
                created_task = create_response.json()
                task_id = created_task.get('data', {}).get('id')
                print(f"  ✅ 创建测试任务成功: {task_id}")
            else:
                print("  ❌ 创建测试任务失败")
                print(f"  错误: {create_response.text}")
                return
        else:
            task_id = tasks[0]['id']
            print(f"  ✅ 找到任务: {task_id}")
        
        # 测试任务详情API
        print(f"\n📋 步骤2: 获取任务详情")
        print(f"  任务ID: {task_id}")
        
        detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
        print(f"  状态码: {detail_response.status_code}")
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            print("  ✅ 任务详情获取成功")
            
            # 检查响应结构
            task_data = detail_data.get('data', {})
            required_fields = ['id', 'name', 'total_urls', 'active_urls', 'stats']
            
            print(f"\n📋 步骤3: 验证响应结构")
            for field in required_fields:
                if field in task_data:
                    print(f"  ✅ {field}: {task_data[field]}")
                else:
                    print(f"  ❌ 缺少字段: {field}")
            
            # 显示完整的任务信息
            print(f"\n📋 完整任务信息:")
            print(json.dumps(detail_data, indent=2, ensure_ascii=False))
            
        else:
            print("  ❌ 任务详情获取失败")
            print(f"  错误: {detail_response.text}")
        
        # 如果是我们创建的测试任务，清理它
        if 'created_task' in locals():
            print(f"\n📋 步骤4: 清理测试任务")
            delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if delete_response.status_code in [200, 204]:
                print("  ✅ 测试任务已清理")
            else:
                print("  ⚠️ 测试任务清理失败")
        
        print("\n🎉 任务详情API测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_task_detail_api()
