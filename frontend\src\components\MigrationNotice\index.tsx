import React, { useState } from 'react';
import { Alert, Button, Modal, Typography, Space, Divider } from 'antd';
import { InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

interface MigrationNoticeProps {
  onMigrationComplete?: () => void;
}

const MigrationNotice: React.FC<MigrationNoticeProps> = ({ onMigrationComplete }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [dismissed, setDismissed] = useState(
    localStorage.getItem('migration_notice_dismissed') === 'true'
  );

  const handleDismiss = () => {
    setDismissed(true);
    localStorage.setItem('migration_notice_dismissed', 'true');
  };

  const handleShowDetails = () => {
    setShowDetails(true);
  };

  if (dismissed) {
    return null;
  }

  return (
    <>
      <Alert
        message="配置系统升级通知"
        description={
          <Space direction="vertical" size="small">
            <Text>
              传统爬虫配置已升级为新的实例配置系统，提供更好的性能和管理体验。
            </Text>
            <Space>
              <Button type="link" size="small" onClick={handleShowDetails}>
                了解详情
              </Button>
              <Button type="link" size="small" onClick={handleDismiss}>
                不再显示
              </Button>
            </Space>
          </Space>
        }
        type="info"
        icon={<InfoCircleOutlined />}
        showIcon
        closable
        onClose={handleDismiss}
        style={{ marginBottom: 16 }}
      />

      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#1890ff' }} />
            配置系统升级说明
          </Space>
        }
        open={showDetails}
        onCancel={() => setShowDetails(false)}
        footer={[
          <Button key="close" onClick={() => setShowDetails(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        <Typography>
          <Title level={4}>升级内容</Title>
          <Paragraph>
            为了提供更好的性能和管理体验，我们已将传统的单一爬虫配置升级为新的实例配置系统：
          </Paragraph>

          <Title level={5}>主要改进</Title>
          <ul>
            <li><strong>多实例支持</strong>：可以创建多个爬虫实例，每个实例独立配置</li>
            <li><strong>更好的性能</strong>：优化的配置管理和任务调度</li>
            <li><strong>增强的监控</strong>：更详细的实例状态监控和日志</li>
            <li><strong>灵活的配置</strong>：支持不同场景的专用配置</li>
          </ul>

          <Divider />

          <Title level={5}>兼容性说明</Title>
          <Paragraph>
            <Text type="success">✅ 向后兼容</Text>：现有的配置页面将继续正常工作
          </Paragraph>
          <Paragraph>
            <Text type="success">✅ 自动迁移</Text>：您的配置将自动转换为新的实例配置
          </Paragraph>
          <Paragraph>
            <Text type="success">✅ 无缝切换</Text>：所有功能保持不变，无需重新配置
          </Paragraph>

          <Divider />

          <Title level={5}>建议操作</Title>
          <Paragraph>
            <ol>
              <li>继续使用当前配置页面，系统会自动处理迁移</li>
              <li>如需更高级的配置管理，可访问新的"爬虫实例管理"页面</li>
              <li>有任何问题请联系技术支持</li>
            </ol>
          </Paragraph>

          <Alert
            message="重要提醒"
            description="此次升级不会影响您现有的监控任务和数据，所有功能将正常运行。"
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        </Typography>
      </Modal>
    </>
  );
};

export default MigrationNotice;
