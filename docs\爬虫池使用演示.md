# 爬虫池使用演示指南

## 🎯 快速开始

### 1. 访问爬虫池管理页面

1. 打开浏览器访问：`http://localhost:3000`
2. 在左侧导航栏中点击 **"爬虫池管理"** 📊
3. 您将看到当前的爬虫池列表（包括自动创建的默认池）

### 2. 查看默认爬虫池

系统已经自动为您创建了一个默认爬虫池：
- **池名称**: 默认爬虫池
- **后端数量**: 1个
- **状态**: 可以看到健康状态和统计信息

### 3. 创建新的爬虫池

点击右上角的 **"创建爬虫池"** 按钮，按照以下步骤：

#### 第一步：基本信息配置
```
池名称: 测试爬虫池
描述: 用于测试的多后端爬虫池
负载均衡策略: 加权轮询
健康检查间隔: 60秒
故障阈值: 3
恢复阈值: 2
```

#### 第二步：添加多个爬虫后端

**后端1 - 主要服务器**
```
后端名称: 主要爬虫服务器
API地址: http://localhost:11234
描述: 主要的爬虫处理服务器
超时时间: 30000ms
最大重试次数: 3
最大并发数: 5
权重: 3
优先级: 1
认证类型: 无认证
```

**后端2 - 备用服务器**
```
后端名称: 备用爬虫服务器
API地址: http://localhost:11235
描述: 备用的爬虫处理服务器
超时时间: 30000ms
最大重试次数: 3
最大并发数: 3
权重: 2
优先级: 2
认证类型: 无认证
```

**后端3 - 高性能服务器**
```
后端名称: 高性能爬虫服务器
API地址: http://localhost:11236
描述: 高性能专用爬虫服务器
超时时间: 20000ms
最大重试次数: 2
最大并发数: 10
权重: 5
优先级: 1
认证类型: API密钥
API密钥: your-api-key-here
```

### 4. 管理爬虫池

创建完成后，您可以：

1. **查看详情**: 点击眼睛图标查看池的详细信息
2. **健康检查**: 点击心形图标手动触发健康检查
3. **编辑配置**: 点击设置图标编辑池配置
4. **删除池**: 点击删除图标删除池（默认池不能删除）

### 5. 监控健康状态

在爬虫池列表中，您可以看到：
- **健康状态进度条**: 显示健康后端的百分比
- **后端统计**: 显示健康后端数量/总后端数量
- **整体健康评分**: 显示池的整体健康度

## 🔧 高级配置

### 负载均衡策略详解

1. **轮询**: 适合后端性能相近的情况
2. **加权轮询**: 适合后端性能不同的情况
3. **最少连接**: 适合任务执行时间差异较大的情况
4. **基于健康评分**: 适合需要高可用性的情况
5. **基于响应时间**: 适合对响应速度要求高的情况

### 认证配置

支持多种认证方式：
- **无认证**: 适合内网环境
- **API密钥**: 在请求头中添加 `X-API-Key`
- **Bearer Token**: 在请求头中添加 `Authorization: Bearer <token>`
- **基础认证**: 使用用户名和密码的HTTP基础认证

### 性能调优

1. **权重设置**: 性能更好的服务器设置更高权重
2. **并发控制**: 根据服务器性能设置合适的并发数
3. **超时配置**: 根据网络环境调整超时时间
4. **重试策略**: 根据任务重要性设置重试次数

## 📊 使用场景示例

### 场景1：开发测试环境
```
池名称: 开发测试池
后端配置:
- 本地开发服务器 (localhost:11234, 权重:1)
- 测试服务器 (test-server:11234, 权重:1)
负载均衡: 轮询
```

### 场景2：生产环境高可用
```
池名称: 生产环境池
后端配置:
- 主服务器 (prod-1:11234, 权重:5, 优先级:1)
- 备服务器1 (prod-2:11234, 权重:3, 优先级:2)
- 备服务器2 (prod-3:11234, 权重:3, 优先级:2)
- 应急服务器 (emergency:11234, 权重:1, 优先级:3)
负载均衡: 基于健康评分
```

### 场景3：多地域部署
```
池名称: 全球分布池
后端配置:
- 北京服务器 (beijing:11234, 权重:3)
- 上海服务器 (shanghai:11234, 权重:3)
- 深圳服务器 (shenzhen:11234, 权重:2)
- 海外服务器 (overseas:11234, 权重:1)
负载均衡: 加权轮询
```

## 🚀 任务执行

配置好爬虫池后，系统会自动：

1. **智能选择后端**: 根据负载均衡策略选择最佳后端
2. **故障转移**: 自动检测故障并转移任务
3. **性能监控**: 实时监控各后端的性能指标
4. **自动恢复**: 故障后端恢复后自动重新加入

## 🔍 故障排查

### 常见问题

1. **后端连接失败**
   - 检查API地址是否正确
   - 确认后端服务是否运行
   - 验证网络连通性

2. **认证失败**
   - 检查API密钥是否正确
   - 确认认证类型设置
   - 验证用户名密码

3. **性能问题**
   - 调整并发数设置
   - 优化超时时间
   - 检查服务器资源使用情况

### 监控指标

关注以下关键指标：
- **健康评分**: 应保持在80%以上
- **响应时间**: 应在合理范围内
- **成功率**: 应保持在95%以上
- **并发数**: 不应超过设置的最大值

## 💡 最佳实践

1. **渐进式部署**: 先在测试环境验证配置
2. **监控告警**: 设置健康状态告警
3. **定期检查**: 定期检查和优化配置
4. **备份配置**: 保存重要的配置信息
5. **文档记录**: 记录配置变更和故障处理

## 🎉 开始使用

现在您已经了解了爬虫池的基本使用方法，可以：

1. 访问 `http://localhost:3000/crawler-pool` 开始配置
2. 创建您的第一个多后端爬虫池
3. 监控和管理您的爬虫资源
4. 享受高可用、高性能的爬虫服务！

有任何问题，请查看系统日志或联系技术支持。
