#!/usr/bin/env python3
"""
检查Redis中的调度数据

分析当前存储的调度配置，找出不兼容的数据
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis


async def check_schedule_data():
    """检查调度数据"""
    try:
        # 连接Redis
        redis_client = redis.from_url("redis://localhost:6379/0")
        await redis_client.ping()
        
        print("🔍 检查Redis中的调度数据")
        print("=" * 50)
        
        # 获取所有任务ID
        task_ids = await redis_client.smembers('monitoring_tasks:indexes:all_task_ids')
        print(f"📊 总任务数: {len(task_ids)}")
        
        if not task_ids:
            print("ℹ️ 没有找到任务数据")
            await redis_client.aclose()
            return
        
        # 检查每个任务的调度配置
        schedule_issues = []
        
        for task_id in task_ids:
            task_id_str = task_id.decode() if isinstance(task_id, bytes) else task_id
            
            # 获取调度数据
            schedule_key = f"monitoring_tasks:tasks:{task_id_str}:schedule"
            schedule_data = await redis_client.hgetall(schedule_key)
            
            if schedule_data:
                print(f"\n📝 任务 {task_id_str} 的调度配置:")
                
                schedule_dict = {}
                for field, value in schedule_data.items():
                    field_str = field.decode() if isinstance(field, bytes) else field
                    value_str = value.decode() if isinstance(value, bytes) else value
                    schedule_dict[field_str] = value_str
                    print(f"  {field_str}: {value_str}")
                
                # 检查type字段
                schedule_type = schedule_dict.get('type')
                if schedule_type:
                    valid_types = ['daily', 'weekly', 'hourly', 'custom']
                    if schedule_type not in valid_types:
                        issue = {
                            'task_id': task_id_str,
                            'field': 'type',
                            'current_value': schedule_type,
                            'valid_values': valid_types
                        }
                        schedule_issues.append(issue)
                        print(f"  ❌ 无效的调度类型: {schedule_type}")
                
                # 检查enabled字段
                enabled = schedule_dict.get('enabled')
                if enabled and enabled not in ['true', 'false', 'True', 'False']:
                    issue = {
                        'task_id': task_id_str,
                        'field': 'enabled',
                        'current_value': enabled,
                        'valid_values': ['true', 'false']
                    }
                    schedule_issues.append(issue)
                    print(f"  ❌ 无效的enabled值: {enabled}")
        
        # 总结问题
        print(f"\n📊 数据检查总结:")
        print(f"  检查的任务数: {len(task_ids)}")
        print(f"  发现的问题数: {len(schedule_issues)}")
        
        if schedule_issues:
            print(f"\n❌ 发现的问题:")
            for i, issue in enumerate(schedule_issues, 1):
                print(f"  {i}. 任务 {issue['task_id']}")
                print(f"     字段: {issue['field']}")
                print(f"     当前值: {issue['current_value']}")
                print(f"     有效值: {issue['valid_values']}")
        else:
            print(f"\n✅ 所有调度配置都正常")
        
        await redis_client.aclose()
        return schedule_issues
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []


async def fix_schedule_data(issues):
    """修复调度数据问题"""
    if not issues:
        print("\n✅ 没有需要修复的问题")
        return
    
    print(f"\n🔧 开始修复 {len(issues)} 个问题...")
    
    try:
        redis_client = redis.from_url("redis://localhost:6379/0")
        await redis_client.ping()
        
        for issue in issues:
            task_id = issue['task_id']
            field = issue['field']
            current_value = issue['current_value']
            
            schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
            
            if field == 'type':
                # 修复调度类型
                if current_value == 'manual':
                    new_value = 'custom'  # 将manual映射为custom
                    await redis_client.hset(schedule_key, field, new_value)
                    print(f"  ✅ 修复任务 {task_id}: {field} {current_value} → {new_value}")
                else:
                    print(f"  ⚠️ 未知的调度类型: {current_value}，需要手动处理")
            
            elif field == 'enabled':
                # 修复enabled字段
                if current_value.lower() in ['true', '1', 'yes']:
                    new_value = 'true'
                else:
                    new_value = 'false'
                await redis_client.hset(schedule_key, field, new_value)
                print(f"  ✅ 修复任务 {task_id}: {field} {current_value} → {new_value}")
        
        await redis_client.aclose()
        print(f"\n🎉 修复完成!")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")


async def main():
    """主函数"""
    print("🔍 Redis调度数据检查和修复工具")
    print("=" * 50)
    
    # 检查数据
    issues = await check_schedule_data()
    
    # 如果有问题，询问是否修复
    if issues:
        print(f"\n❓ 发现 {len(issues)} 个问题，是否自动修复？")
        print("   输入 'y' 或 'yes' 确认修复，其他任何输入取消")
        
        # 在脚本中自动修复
        print("🔧 自动修复模式，开始修复...")
        await fix_schedule_data(issues)
        
        # 重新检查
        print(f"\n🔍 重新检查数据...")
        new_issues = await check_schedule_data()
        
        if not new_issues:
            print(f"\n🎉 所有问题已修复!")
        else:
            print(f"\n⚠️ 仍有 {len(new_issues)} 个问题需要手动处理")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
