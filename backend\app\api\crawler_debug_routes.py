"""
爬虫请求调试API路由
提供爬虫Worker请求的监控、调试和重发功能
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import logging
import asyncio
from ..core.redis_client import get_redis_client

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/crawler-debug", tags=["crawler-debug"])


class CrawlerRequest(BaseModel):
    id: str
    task_id: str
    url: str
    worker_id: str
    celery_worker: Optional[str] = None  # Celery Worker名称
    timestamp: str
    method: str
    headers: Dict[str, str]
    payload: Any
    response: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status: str  # pending, sent, success, failed


class CrawlerWorkerInfo(BaseModel):
    worker_id: str
    version: str
    endpoint: str
    status: str  # online, offline, busy
    capabilities: List[str]
    last_seen: str


class ResendRequestModel(BaseModel):
    url: str
    method: str
    headers: Dict[str, str]
    payload: Any


async def log_crawler_request(
    task_id: str,
    url: str,
    worker_id: str,
    method: str,
    headers: Dict[str, str],
    payload: Any,
    request_id: str = None,
    celery_worker: str = None
) -> str:
    """记录爬虫请求到Redis"""
    try:
        redis_client = await get_redis_client()
        
        # 生成请求ID
        if not request_id:
            request_id = f"req_{task_id}_{int(datetime.now().timestamp() * 1000)}"
        
        # 构建请求记录
        request_data = {
            "id": request_id,
            "task_id": task_id,
            "url": url,
            "worker_id": worker_id,
            "celery_worker": celery_worker,
            "timestamp": datetime.now().isoformat(),
            "method": method,
            "headers": headers,
            "payload": payload,
            "status": "pending"
        }
        
        # 存储请求记录
        request_key = f"crawler_request:{request_id}"
        await redis_client.setex(
            request_key,
            86400,  # 24小时过期
            json.dumps(request_data, default=str, ensure_ascii=False)
        )
        
        # 添加到请求索引
        requests_index_key = f"crawler_requests_index:{task_id}"
        await redis_client.lpush(requests_index_key, request_id)
        await redis_client.expire(requests_index_key, 86400)
        
        # 添加到全局请求索引
        global_requests_key = "crawler_requests_global"
        await redis_client.lpush(global_requests_key, request_id)
        await redis_client.ltrim(global_requests_key, 0, 999)  # 保留最近1000条
        
        logger.debug(f"Logged crawler request: {request_id}")
        return request_id
        
    except Exception as e:
        logger.error(f"Failed to log crawler request: {e}")
        raise


async def update_crawler_request_response(
    request_id: str,
    response_data: Dict[str, Any] = None,
    error: str = None
):
    """更新爬虫请求的响应信息"""
    try:
        redis_client = await get_redis_client()
        
        request_key = f"crawler_request:{request_id}"
        request_data_str = await redis_client.get(request_key)
        
        if not request_data_str:
            logger.warning(f"Request {request_id} not found for response update")
            return
        
        request_data = json.loads(request_data_str)
        
        # 更新状态和响应信息
        if error:
            request_data["status"] = "failed"
            request_data["error"] = error
        elif response_data:
            request_data["status"] = "success"
            request_data["response"] = response_data
        else:
            request_data["status"] = "sent"
        
        request_data["updated_at"] = datetime.now().isoformat()
        
        # 保存更新后的数据
        await redis_client.setex(
            request_key,
            86400,
            json.dumps(request_data, default=str, ensure_ascii=False)
        )
        
        logger.debug(f"Updated crawler request response: {request_id}")
        
    except Exception as e:
        logger.error(f"Failed to update crawler request response: {e}")


@router.get("/requests", response_model=List[CrawlerRequest])
async def get_crawler_requests(
    task_id: Optional[str] = Query(None, description="任务ID过滤"),
    worker_id: Optional[str] = Query(None, description="Worker ID过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取爬虫请求历史"""
    try:
        redis_client = await get_redis_client()
        
        # 确定要查询的请求ID列表
        if task_id:
            # 从任务索引获取
            requests_index_key = f"crawler_requests_index:{task_id}"
            request_ids = await redis_client.lrange(requests_index_key, 0, limit - 1)
        else:
            # 从全局索引获取
            global_requests_key = "crawler_requests_global"
            request_ids = await redis_client.lrange(global_requests_key, 0, limit - 1)
        
        # 获取请求详情
        requests = []
        for request_id_bytes in request_ids:
            request_id = request_id_bytes.decode() if isinstance(request_id_bytes, bytes) else request_id_bytes
            
            request_key = f"crawler_request:{request_id}"
            request_data_str = await redis_client.get(request_key)
            
            if request_data_str:
                try:
                    request_data = json.loads(request_data_str)
                    
                    # 应用过滤条件
                    if worker_id and request_data.get("worker_id") != worker_id:
                        continue
                    
                    if status and request_data.get("status") != status:
                        continue
                    
                    if start_time or end_time:
                        request_time = datetime.fromisoformat(request_data.get("timestamp", ""))
                        if start_time and request_time < start_time:
                            continue
                        if end_time and request_time > end_time:
                            continue
                    
                    requests.append(CrawlerRequest(**request_data))
                    
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse request data for {request_id}")
                    continue
        
        return requests
        
    except Exception as e:
        logger.error(f"Failed to get crawler requests: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workers", response_model=List[CrawlerWorkerInfo])
async def get_crawler_workers():
    """获取爬虫Worker信息"""
    try:
        redis_client = await get_redis_client()
        
        # 获取Worker信息
        workers_key = "crawler_workers"
        worker_ids = await redis_client.smembers(workers_key)
        
        workers = []
        for worker_id_bytes in worker_ids:
            worker_id = worker_id_bytes.decode() if isinstance(worker_id_bytes, bytes) else worker_id_bytes
            
            worker_info_key = f"crawler_worker_info:{worker_id}"
            worker_data_str = await redis_client.get(worker_info_key)
            
            if worker_data_str:
                try:
                    worker_data = json.loads(worker_data_str)
                    
                    # 检查Worker状态（基于最后活跃时间）
                    last_seen = datetime.fromisoformat(worker_data.get("last_seen", ""))
                    if datetime.now() - last_seen > timedelta(minutes=5):
                        worker_data["status"] = "offline"
                    
                    workers.append(CrawlerWorkerInfo(**worker_data))
                    
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse worker data for {worker_id}")
                    continue
        
        return workers
        
    except Exception as e:
        logger.error(f"Failed to get crawler workers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/resend")
async def resend_crawler_request(request_data: ResendRequestModel):
    """重发爬虫请求"""
    try:
        # 这里可以集成实际的爬虫API调用逻辑
        # 目前返回模拟响应
        
        # 记录重发请求
        request_id = await log_crawler_request(
            task_id="debug_resend",
            url=request_data.url,
            worker_id="debug_worker",
            method=request_data.method,
            headers=request_data.headers,
            payload=request_data.payload
        )
        
        # 模拟发送请求
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        # 更新请求状态
        await update_crawler_request_response(
            request_id,
            response_data={
                "status": 200,
                "headers": {"Content-Type": "application/json"},
                "data": {"message": "Debug request sent successfully"},
                "response_time": 100
            }
        )
        
        return {
            "success": True,
            "request_id": request_id,
            "message": "Request resent successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to resend crawler request: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/requests/{request_id}")
async def get_crawler_request_detail(request_id: str):
    """获取单个请求的详细信息"""
    try:
        redis_client = await get_redis_client()
        
        request_key = f"crawler_request:{request_id}"
        request_data_str = await redis_client.get(request_key)
        
        if not request_data_str:
            raise HTTPException(status_code=404, detail="Request not found")
        
        request_data = json.loads(request_data_str)
        return CrawlerRequest(**request_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler request detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/requests/{request_id}")
async def delete_crawler_request(request_id: str):
    """删除请求记录"""
    try:
        redis_client = await get_redis_client()
        
        request_key = f"crawler_request:{request_id}"
        deleted = await redis_client.delete(request_key)
        
        if deleted:
            return {"success": True, "message": "Request deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Request not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete crawler request: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 辅助函数：在爬虫任务中调用以记录请求
async def log_crawler_api_call(task_id: str, url: str, worker_id: str, api_request: Dict[str, Any], celery_worker: str = None) -> str:
    """在爬虫任务中调用此函数来记录API请求"""
    return await log_crawler_request(
        task_id=task_id,
        url=url,
        worker_id=worker_id,
        celery_worker=celery_worker,
        method=api_request.get("method", "POST"),
        headers=api_request.get("headers", {}),
        payload=api_request.get("payload", {})
    )


async def log_crawler_api_response(request_id: str, response: Dict[str, Any] = None, error: str = None):
    """在爬虫任务中调用此函数来记录API响应"""
    await update_crawler_request_response(request_id, response, error)


# ==================== 任务关联查询API ====================

class TaskRelationInfo(BaseModel):
    """任务关联信息"""
    monitoring_task_id: str
    celery_task_id: str
    worker_id: str
    url: str
    batch_id: str
    created_at: str
    status: str


class MonitoringTaskRelationsInfo(BaseModel):
    """监控任务完整关联信息"""
    monitoring_task_id: str
    celery_task_ids: List[str]
    worker_ids: List[str]
    urls: List[str]
    created_at: str
    updated_at: str


@router.get("/task-relations/by-celery-task/{celery_task_id}", response_model=Optional[TaskRelationInfo])
async def get_task_relation_by_celery_id(celery_task_id: str):
    """通过Celery任务ID获取任务关联信息"""
    try:
        from ..services.task_relation_service import task_relation_service
        relation = await task_relation_service.get_task_relation_by_celery_id(celery_task_id)

        if not relation:
            raise HTTPException(status_code=404, detail="Task relation not found")

        return TaskRelationInfo(
            monitoring_task_id=relation.monitoring_task_id,
            celery_task_id=relation.celery_task_id,
            worker_id=relation.worker_id,
            url=relation.url,
            batch_id=relation.batch_id,
            created_at=relation.created_at,
            status=relation.status
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task relation by celery ID {celery_task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task-relations/by-monitoring-task/{monitoring_task_id}", response_model=Optional[MonitoringTaskRelationsInfo])
async def get_monitoring_task_relations(monitoring_task_id: str):
    """通过监控任务ID获取完整关联信息"""
    try:
        from ..services.task_relation_service import task_relation_service
        relations = await task_relation_service.get_monitoring_task_relations(monitoring_task_id)

        if not relations:
            raise HTTPException(status_code=404, detail="Monitoring task relations not found")

        return MonitoringTaskRelationsInfo(
            monitoring_task_id=relations.monitoring_task_id,
            celery_task_ids=relations.celery_task_ids,
            worker_ids=relations.worker_ids,
            urls=relations.urls,
            created_at=relations.created_at,
            updated_at=relations.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get monitoring task relations {monitoring_task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task-relations/by-worker/{worker_id}", response_model=List[str])
async def get_worker_tasks(worker_id: str):
    """通过Worker ID获取关联的所有Celery任务ID"""
    try:
        from ..services.task_relation_service import task_relation_service
        task_ids = await task_relation_service.get_worker_tasks(worker_id)
        return task_ids

    except Exception as e:
        logger.error(f"Failed to get worker tasks for {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task-relations/by-url", response_model=List[str])
async def get_url_tasks(url: str = Query(..., description="URL to search for")):
    """通过URL获取关联的所有Celery任务ID"""
    try:
        from ..services.task_relation_service import task_relation_service
        task_ids = await task_relation_service.get_url_tasks(url)
        return task_ids

    except Exception as e:
        logger.error(f"Failed to get URL tasks for {url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/task-relations/status/{celery_task_id}")
async def update_task_relation_status(celery_task_id: str, status: str = Query(..., description="New status")):
    """更新任务关联状态"""
    try:
        from ..services.task_relation_service import task_relation_service
        await task_relation_service.update_task_status(celery_task_id, status)
        return {"success": True, "message": f"Task {celery_task_id} status updated to {status}"}

    except Exception as e:
        logger.error(f"Failed to update task status for {celery_task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 统一执行查询API ====================

@router.get("/execution/url-status")
async def get_url_execution_status(url: str = Query(..., description="URL to check")):
    """查询URL当前执行状态"""
    try:
        from ..services.unified_execution_service import unified_execution_service
        status = await unified_execution_service.get_current_url_execution_status(url)

        if not status:
            raise HTTPException(status_code=404, detail="URL execution status not found")

        return status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get URL execution status for {url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/execution/monitoring-task/{task_uuid}")
async def get_monitoring_task_execution_chain(task_uuid: str):
    """获取监控任务完整执行链路"""
    try:
        from ..services.unified_execution_service import unified_execution_service
        chain = await unified_execution_service.get_monitoring_task_execution_chain(task_uuid)
        return chain

    except Exception as e:
        logger.error(f"Failed to get monitoring task execution chain for {task_uuid}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/execution/worker-load/{worker_id}")
async def get_worker_current_load(worker_id: str):
    """获取Worker当前负载"""
    try:
        from ..services.unified_execution_service import unified_execution_service
        load = await unified_execution_service.get_worker_current_load(worker_id)
        return load

    except Exception as e:
        logger.error(f"Failed to get worker current load for {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/execution/celery-task/{celery_task_id}")
async def get_celery_task_execution_info(celery_task_id: str):
    """通过Celery任务ID获取执行信息"""
    try:
        from ..services.unified_execution_service import unified_execution_service
        info = await unified_execution_service.get_celery_task_execution_info(celery_task_id)
        return info

    except Exception as e:
        logger.error(f"Failed to get Celery task execution info for {celery_task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
