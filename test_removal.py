#!/usr/bin/env python3
"""
测试Worker移除功能的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def create_test_assignment():
    """创建测试分配"""
    
    print("🔧 创建测试分配...")
    
    assignment_data = {
        "task_id": "20baf174-bb0d-4d45-b931-d1580bac02da",
        "worker_ids": ["46ac2f77-1e6a-401c-8853-e22f03ccaa94", "81d48722-8467-492f-aa45-9bf946da8bd8"],
        "assignment_name": "测试移除功能",
        "description": "用于测试单个Worker移除功能",
        "assignment_strategy": "health_based",
        "auto_failover": True,
        "total_urls": 49
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/task-assignments/",
            json=assignment_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignment = response.json()
            print("✅ 分配创建成功!")
            print(f"  分配ID: {assignment['assignment_id']}")
            print(f"  Worker IDs: {assignment['worker_ids']}")
            return assignment['assignment_id']
        else:
            print(f"❌ 分配创建失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_worker_removal():
    """测试Worker移除"""
    
    print(f"\n🧪 测试Worker移除...")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    # 1. 获取当前Worker列表
    print(f"\n📋 获取当前Worker列表...")
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/task/{task_id}/assigned-workers")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 当前有 {len(workers)} 个Worker:")
            for worker in workers:
                print(f"  - {worker['worker_name']} ({worker['worker_id']})")
            
            if len(workers) >= 2:
                # 选择第一个Worker进行移除
                target_worker = workers[0]
                worker_id = target_worker['worker_id']
                worker_name = target_worker['worker_name']
                
                print(f"\n🎯 移除Worker: {worker_name}")
                
                # 2. 移除Worker
                remove_response = requests.delete(f"{BASE_URL}/task-assignments/task/{task_id}/worker/{worker_id}")
                
                print(f"移除响应状态码: {remove_response.status_code}")
                
                if remove_response.status_code == 200:
                    result = remove_response.json()
                    print(f"✅ 移除成功: {result['message']}")
                    
                    # 3. 验证移除结果
                    print(f"\n📊 验证移除结果...")
                    verify_response = requests.get(f"{BASE_URL}/crawler-workers/task/{task_id}/assigned-workers")
                    
                    if verify_response.status_code == 200:
                        remaining_workers = verify_response.json()
                        print(f"✅ 移除后剩余 {len(remaining_workers)} 个Worker:")
                        
                        for worker in remaining_workers:
                            print(f"  - {worker['worker_name']} ({worker['worker_id']})")
                        
                        # 检查目标Worker是否被移除
                        removed_worker_exists = any(w['worker_id'] == worker_id for w in remaining_workers)
                        
                        if not removed_worker_exists and len(remaining_workers) == len(workers) - 1:
                            print(f"✅ 目标Worker {worker_name} 已成功移除")
                            print(f"✅ 其他Worker保持不变")
                            return True
                        else:
                            print(f"❌ Worker移除验证失败")
                            return False
                    else:
                        print(f"❌ 验证失败: {verify_response.text}")
                        return False
                else:
                    print(f"❌ 移除失败: {remove_response.text}")
                    return False
            else:
                print("❌ Worker数量不足，需要至少2个Worker进行测试")
                return False
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试Worker移除功能\n")
    
    # 1. 创建测试分配
    assignment_id = create_test_assignment()
    
    if assignment_id:
        # 2. 测试Worker移除
        removal_result = test_worker_removal()
        
        print(f"\n🎯 测试结果:")
        print(f"  分配创建: ✅ 成功")
        print(f"  Worker移除: {'✅ 成功' if removal_result else '❌ 失败'}")
        
        if removal_result:
            print(f"\n🎉 Worker移除功能测试通过！")
        else:
            print(f"\n❌ Worker移除功能测试失败")
    else:
        print(f"\n❌ 无法创建测试分配")

if __name__ == "__main__":
    main()
