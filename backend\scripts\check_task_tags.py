#!/usr/bin/env python3
"""
检查特定任务在Redis中的tags存储格式
"""

import redis
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_task_tags(task_id):
    """检查指定任务的tags存储格式"""
    
    # 连接Redis
    redis_client = redis.from_url("redis://redis:6379/0")
    
    try:
        # 构建Redis键
        basic_key = f"monitoring_tasks:{task_id}:basic"
        
        logger.info(f"检查任务 {task_id} 的tags存储格式")
        logger.info(f"Redis键: {basic_key}")
        
        # 检查键是否存在
        if not redis_client.exists(basic_key):
            logger.error(f"任务 {task_id} 不存在于Redis中")
            return
        
        # 获取所有基本信息字段
        all_basic_data = redis_client.hgetall(basic_key)
        
        logger.info("=== 任务基本信息 ===")
        for field, value in all_basic_data.items():
            field_str = field.decode() if isinstance(field, bytes) else field
            value_str = value.decode() if isinstance(value, bytes) else value
            logger.info(f"{field_str}: {repr(value_str)}")
        
        # 专门检查tags字段
        tags_value = redis_client.hget(basic_key, 'tags')
        
        if tags_value is None:
            logger.warning("tags字段不存在")
            return
        
        tags_str = tags_value.decode() if isinstance(tags_value, bytes) else tags_value
        
        logger.info("\n=== Tags字段详细分析 ===")
        logger.info(f"原始值: {repr(tags_str)}")
        logger.info(f"类型: {type(tags_str)}")
        logger.info(f"长度: {len(tags_str)}")
        
        # 尝试解析
        if tags_str.startswith('["') and tags_str.endswith('"]'):
            logger.info("✅ 格式正确: JSON数组格式")
            try:
                parsed = json.loads(tags_str)
                logger.info(f"解析结果: {parsed}")
                logger.info(f"解析类型: {type(parsed)}")
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析失败: {e}")
        
        elif tags_str.startswith("['") and tags_str.endswith("']"):
            logger.warning("⚠️ 格式错误: Python字符串表示格式")
            try:
                import ast
                parsed = ast.literal_eval(tags_str)
                logger.info(f"ast解析结果: {parsed}")
                logger.info(f"应该修复为: {json.dumps(parsed)}")
            except (ValueError, SyntaxError) as e:
                logger.error(f"❌ ast解析失败: {e}")
        
        elif tags_str == "[]":
            logger.info("✅ 空数组格式正确")
        
        else:
            logger.warning(f"⚠️ 未知格式: {repr(tags_str)}")
        
    except Exception as e:
        logger.error(f"检查过程中出错: {e}")
    
    finally:
        redis_client.close()

if __name__ == "__main__":
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    check_task_tags(task_id)
