#!/usr/bin/env python3
"""
检查Redis中的任务数据

查看Redis中存储的任务数据格式
"""

import redis
import json

def check_redis_data():
    """检查Redis中的任务数据"""
    print("🔍 检查Redis中的任务数据")
    print("=" * 50)
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 检查所有任务ID
        all_task_ids_key = "monitoring_tasks:indexes:all_task_ids"
        task_ids = r.smembers(all_task_ids_key)
        
        print(f"\n📋 任务索引:")
        print(f"  all_task_ids: {len(task_ids)} 个任务")
        print(f"  任务ID列表: {list(task_ids)}")
        
        # 检查每个任务的数据
        for task_id in task_ids:
            print(f"\n📋 任务 {task_id}:")
            
            # 检查分层存储
            basic_key = f"monitoring_tasks:tasks:{task_id}:basic"
            schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
            config_key = f"monitoring_tasks:tasks:{task_id}:config"
            urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            
            print(f"  分层存储:")
            print(f"    basic存在: {r.exists(basic_key)}")
            print(f"    schedule存在: {r.exists(schedule_key)}")
            print(f"    config存在: {r.exists(config_key)}")
            print(f"    urls存在: {r.exists(urls_key)}")
            
            # 检查basic数据
            if r.exists(basic_key):
                basic_data = r.hgetall(basic_key)
                print(f"    basic数据: {basic_data}")
            
            # 检查schedule数据
            if r.exists(schedule_key):
                schedule_data = r.hgetall(schedule_key)
                print(f"    schedule数据: {schedule_data}")
                
                # 检查schedule数据的类型
                for key, value in schedule_data.items():
                    print(f"      {key}: {type(value)} = {value}")
            
            # 检查config数据
            if r.exists(config_key):
                config_data = r.hgetall(config_key)
                print(f"    config数据: {config_data}")
            
            # 检查urls数据
            if r.exists(urls_key):
                urls_data = r.smembers(urls_key)
                print(f"    urls数据: {urls_data}")
            
            # 检查旧的扁平存储
            old_task_key = f"monitoring_tasks:{task_id}"
            if r.exists(old_task_key):
                old_data = r.hgetall(old_task_key)
                print(f"  旧的扁平存储:")
                print(f"    数据: {old_data}")
        
        # 检查其他相关键
        print(f"\n📋 其他相关键:")
        
        # 检查活跃任务
        active_key = "monitoring_tasks:indexes:active_task_ids"
        active_ids = r.smembers(active_key)
        print(f"  active_task_ids: {active_ids}")
        
        # 检查URL关联
        for task_id in task_ids:
            url_task_key = f"monitoring_tasks:task_urls:{task_id}"
            if r.exists(url_task_key):
                url_ids = r.smembers(url_task_key)
                print(f"  task_urls:{task_id}: {url_ids}")
        
        print(f"\n🎉 Redis数据检查完成!")
        
    except Exception as e:
        print(f"❌ 检查Redis数据时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    check_redis_data()
