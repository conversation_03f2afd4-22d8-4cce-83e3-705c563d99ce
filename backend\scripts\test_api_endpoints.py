#!/usr/bin/env python3
"""
测试API端点是否正常工作
"""

import asyncio
import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.services.backend_config_service import backend_config_service
from app.schemas.backend_config import BackendConfigCreate

async def test_api_endpoints():
    """测试API端点"""
    
    print("🧪 测试后端配置API端点...")
    
    try:
        # 1. 测试获取配置列表
        print("\n1. 测试获取配置列表...")
        configs = await backend_config_service.list_configs()
        print(f"   ✅ 获取到 {len(configs)} 个配置")
        
        # 2. 测试创建配置
        print("\n2. 测试创建配置...")
        config_data = {
            "backend_name": "API测试后端",
            "description": "测试API端点的后端配置",
            "api_endpoint": "http://api-test:8080",
            "auth_config": {
                "auth_type": "none"
            },
            "performance_config": {
                "max_concurrent_tasks": 3,
                "batch_timeout": 300,
                "api_timeout": 30000,
                "api_max_retries": 3,
                "api_retry_delay": 1000,
                "memory_limit_mb": 1024,
                "cpu_limit_percent": 80,
                "rate_limit_requests": 100,
                "rate_limit_period": 60
            },
            "system_auth_config": {
                "auth_config": {
                    "auth_type": "none"
                },
                "default_permissions": [],
                "default_role": "user",
                "include_client_ip": True,
                "include_user_agent": True
            },
            "callback_config": {
                "callback_timeout": 30000,
                "callback_retries": 3,
                "callback_verify_ssl": True
            },
            "monitoring_config": {
                "health_check_enabled": True,
                "health_check_interval": 60,
                "health_check_timeout": 10000,
                "response_time_threshold": 30000,
                "error_rate_threshold": 0.1,
                "alert_on_system_failure": True,
                "failure_threshold": 3,
                "recovery_threshold": 2
            },
            "weight": 1,
            "priority": 1,
            "performance_level": "medium"
        }
        
        config = BackendConfigCreate(**config_data)
        result = await backend_config_service.create_config(config)
        print(f"   ✅ 创建配置成功: {result.backend_name}")
        
        # 3. 测试获取单个配置
        print("\n3. 测试获取单个配置...")
        single_config = await backend_config_service.get_config(result.backend_id)
        if single_config:
            print(f"   ✅ 获取配置成功: {single_config.backend_name}")
        else:
            print(f"   ❌ 获取配置失败")
        
        # 4. 测试更新配置
        print("\n4. 测试更新配置...")
        from app.schemas.backend_config import BackendConfigUpdate
        update_data = BackendConfigUpdate(
            description="更新后的API测试配置"
        )
        updated_config = await backend_config_service.update_config(result.backend_id, update_data)
        if updated_config:
            print(f"   ✅ 更新配置成功: {updated_config.description}")
        else:
            print(f"   ❌ 更新配置失败")
        
        # 5. 测试删除配置
        print("\n5. 测试删除配置...")
        success = await backend_config_service.delete_config(result.backend_id)
        if success:
            print(f"   ✅ 删除配置成功")
        else:
            print(f"   ❌ 删除配置失败")
        
        print(f"\n✅ 所有API端点测试完成！")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_api_endpoints()

if __name__ == "__main__":
    asyncio.run(main())
