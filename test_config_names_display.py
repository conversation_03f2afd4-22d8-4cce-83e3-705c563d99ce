#!/usr/bin/env python3
"""
测试配置名称显示功能的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_task_worker_config_names():
    """测试任务Worker列表的配置名称显示"""
    
    print("🧪 测试任务Worker列表的配置名称显示...")
    
    task_id = "success"
    
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/task/{task_id}/assigned-workers")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 获取到 {len(workers)} 个Worker:")
            
            for i, worker in enumerate(workers, 1):
                print(f"\n  {i}. Worker: {worker['worker_name']}")
                print(f"     Worker ID: {worker['worker_id']}")
                print(f"     前端配置ID: {worker['crawler_config_id']}")
                print(f"     前端配置名: {worker['crawler_config_name']}")
                print(f"     后端配置ID: {worker['backend_config_id']}")
                print(f"     后端配置名: {worker['backend_config_name']}")
                print(f"     分配状态: {worker['assignment_status']}")
                
                # 验证配置名称不是ID
                if worker['crawler_config_name'] != worker['crawler_config_id']:
                    print(f"     ✅ 前端配置名称正确显示")
                else:
                    print(f"     ❌ 前端配置名称仍显示ID")
                
                if worker['backend_config_name'] != worker['backend_config_id']:
                    print(f"     ✅ 后端配置名称正确显示")
                else:
                    print(f"     ❌ 后端配置名称仍显示ID")
            
            return True
        else:
            print(f"❌ 获取任务Worker列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_worker_management_config_names():
    """测试Worker管理页面的配置名称显示"""
    
    print(f"\n🧪 测试Worker管理页面的配置名称显示...")
    
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 获取到 {len(workers)} 个Worker:")
            
            for i, worker in enumerate(workers, 1):
                print(f"\n  {i}. Worker: {worker['worker_name']}")
                print(f"     前端配置名: {worker.get('crawler_config_name', '未知')}")
                print(f"     后端配置名: {worker.get('backend_config_name', '未知')}")
                print(f"     状态: {worker['status']}")
                print(f"     优先级: {worker['priority']}")
                
                # 验证配置名称字段存在
                if 'crawler_config_name' in worker and 'backend_config_name' in worker:
                    print(f"     ✅ 配置名称字段存在")
                else:
                    print(f"     ❌ 配置名称字段缺失")
            
            return True
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_worker_detail_config_names():
    """测试Worker详情的配置名称显示"""
    
    print(f"\n🧪 测试Worker详情的配置名称显示...")
    
    # 先获取一个Worker ID
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/")
        if response.status_code == 200:
            workers = response.json()
            if workers:
                worker_id = workers[0]['worker_id']
                worker_name = workers[0]['worker_name']
                
                print(f"测试Worker: {worker_name} ({worker_id})")
                
                # 获取Worker详情
                detail_response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}")
                if detail_response.status_code == 200:
                    worker_detail = detail_response.json()
                    print(f"✅ 获取Worker详情成功:")
                    print(f"     Worker名称: {worker_detail['worker_name']}")
                    print(f"     前端配置ID: {worker_detail.get('crawler_config_id', '未知')}")
                    print(f"     后端配置ID: {worker_detail.get('backend_config_id', '未知')}")
                    
                    # 检查是否有配置名称字段（在前端会通过API获取）
                    if 'crawler_config_name' in worker_detail:
                        print(f"     前端配置名: {worker_detail['crawler_config_name']}")
                    else:
                        print(f"     前端配置名: 需要前端通过配置API获取")
                    
                    if 'backend_config_name' in worker_detail:
                        print(f"     后端配置名: {worker_detail['backend_config_name']}")
                    else:
                        print(f"     后端配置名: 需要前端通过配置API获取")
                    
                    return True
                else:
                    print(f"❌ 获取Worker详情失败: {detail_response.text}")
                    return False
            else:
                print(f"❌ 没有可用的Worker进行测试")
                return False
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试配置名称显示功能\n")
    
    # 1. 测试任务Worker列表的配置名称显示
    print("=" * 60)
    task_worker_result = test_task_worker_config_names()
    
    # 2. 测试Worker管理页面的配置名称显示
    print("=" * 60)
    worker_management_result = test_worker_management_config_names()
    
    # 3. 测试Worker详情的配置名称显示
    print("=" * 60)
    worker_detail_result = test_worker_detail_config_names()
    
    # 4. 总结测试结果
    print("=" * 60)
    print(f"\n🎯 测试结果总结:")
    print(f"  任务Worker列表配置名称: {'✅ 通过' if task_worker_result else '❌ 失败'}")
    print(f"  Worker管理页面配置名称: {'✅ 通过' if worker_management_result else '❌ 失败'}")
    print(f"  Worker详情配置名称: {'✅ 通过' if worker_detail_result else '❌ 失败'}")
    
    if task_worker_result and worker_management_result and worker_detail_result:
        print(f"\n🎉 所有测试通过！配置名称显示功能正常！")
        print(f"\n📋 功能验证:")
        print(f"  ✅ 任务详情页面Worker分配列表显示配置名称")
        print(f"  ✅ Worker管理页面显示配置名称")
        print(f"  ✅ Worker详情页面显示配置信息")
        print(f"  ✅ 配置名称可读性良好，不再显示难懂的ID")
    else:
        print(f"\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
