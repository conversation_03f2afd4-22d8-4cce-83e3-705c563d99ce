# Schema显示问题修复文档

## 问题描述

用户反馈爬虫系统配置页面中的"提取schema"字段显示为`[object Object]`，无法正确显示和编辑Schema内容。

## 问题分析

### 根本原因

1. **数据类型不匹配**：
   - 后端配置中的`extraction_schema`字段是一个JavaScript对象（dict）
   - 前端TextArea组件期望的是字符串类型
   - 当对象直接传递给TextArea时，会被转换为`[object Object]`字符串

2. **缺少序列化处理**：
   - 配置加载时没有将JSON对象序列化为字符串
   - 配置保存时没有将JSON字符串反序列化为对象
   - 重置配置时也没有进行相应的转换

### 数据流问题

```
后端 (JSON对象) → 前端加载 → TextArea显示 → 用户编辑 → 前端保存 → 后端存储
     ↑                                                                    ↓
   dict类型                                                           需要dict类型
     ↑                                                                    ↓
   缺少序列化 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←← 缺少反序列化
```

## 解决方案

### 1. 配置加载时的序列化处理

**修改前**：
```typescript
form.setFieldsValue(loadedConfig);
```

**修改后**：
```typescript
const formValues = {
  ...loadedConfig,
  schema_extraction: {
    ...loadedConfig.schema_extraction,
    extraction_schema: JSON.stringify(loadedConfig.schema_extraction.extraction_schema, null, 2)
  }
};
form.setFieldsValue(formValues);
```

### 2. 配置保存时的反序列化处理

**修改前**：
```typescript
const values = await form.validateFields();
await crawlerConfigService.updateConfig(values);
```

**修改后**：
```typescript
const values = await form.validateFields();
const configToSave = {
  ...values,
  schema_extraction: {
    ...values.schema_extraction,
    extraction_schema: JSON.parse(values.schema_extraction.extraction_schema || '{}')
  }
};
await crawlerConfigService.updateConfig(configToSave);
```

### 3. 配置重置时的处理

**修改前**：
```typescript
const defaultConfig = crawlerConfigService.getDefaultConfig();
form.setFieldsValue(defaultConfig);
```

**修改后**：
```typescript
const defaultConfig = crawlerConfigService.getDefaultConfig();
const defaultFormValues = {
  ...defaultConfig,
  schema_extraction: {
    ...defaultConfig.schema_extraction,
    extraction_schema: JSON.stringify(defaultConfig.schema_extraction.extraction_schema, null, 2)
  }
};
form.setFieldsValue(defaultFormValues);
```

### 4. JSON格式验证

添加了实时的JSON格式验证：

```typescript
rules={[
  {
    validator: (_, value) => {
      if (!value || value.trim() === '') {
        return Promise.resolve();
      }
      try {
        JSON.parse(value);
        return Promise.resolve();
      } catch (e) {
        return Promise.reject(new Error('请输入有效的JSON格式'));
      }
    }
  }
]}
```

### 5. 用户体验改进

#### JSON格式化按钮

添加了一键格式化功能：

```typescript
<Button 
  size="small" 
  type="link" 
  onClick={() => {
    const currentValue = form.getFieldValue(['schema_extraction', 'extraction_schema']);
    if (currentValue) {
      try {
        const parsed = JSON.parse(currentValue);
        const formatted = JSON.stringify(parsed, null, 2);
        form.setFieldValue(['schema_extraction', 'extraction_schema'], formatted);
        message.success('JSON格式化成功');
      } catch (e) {
        message.error('JSON格式错误，无法格式化');
      }
    }
  }}
>
  格式化
</Button>
```

#### 编辑器样式优化

```typescript
<TextArea 
  rows={12} 
  style={{ fontFamily: 'Monaco, Consolas, "Courier New", monospace' }}
/>
```

- 增加了行数到12行，提供更大的编辑空间
- 使用等宽字体，提高JSON代码的可读性

## 测试验证

### 测试用例

1. **序列化测试**：
   - ✅ JSON对象正确序列化为格式化的字符串
   - ✅ 序列化后的字符串长度为6746字符（完整的电商Schema）
   - ✅ 不会显示为`[object Object]`

2. **反序列化测试**：
   - ✅ JSON字符串正确反序列化为对象
   - ✅ 反序列化后的对象与原对象完全相等
   - ✅ 保存时不会出现格式错误

3. **格式验证测试**：
   - ✅ 有效JSON通过验证
   - ✅ 无效JSON显示错误提示
   - ✅ 空值被允许（可选字段）

4. **用户交互测试**：
   - ✅ 格式化按钮正常工作
   - ✅ 错误提示友好明确
   - ✅ 等宽字体提高可读性

## 默认Schema内容

修复后，用户可以看到完整的电商商品信息提取Schema，包含：

### 主要数据结构

- **task_info**: 任务执行信息
- **product_basic_info**: 商品基础信息（URL、名称、ID）
- **pricing_info**: 价格和库存信息
- **category_info**: 商品分类信息
- **seller_info**: 销售商信息
- **media_info**: 商品媒体信息
- **qa_section**: 问答区域信息
- **rating_info**: 评分信息
- **reviews_section**: 评论区域信息

### Schema特点

- **完整性**: 覆盖电商商品的所有重要信息
- **结构化**: 使用标准JSON Schema格式
- **可扩展**: 支持嵌套对象和数组结构
- **验证**: 包含必填字段和数据类型验证

## 错误处理

### 1. JSON解析错误

```typescript
try {
  JSON.parse(values.schema_extraction.extraction_schema || '{}');
} catch (e) {
  message.error('Schema JSON格式错误，请检查语法');
  throw new Error('Invalid JSON format in extraction_schema');
}
```

### 2. 格式化错误

```typescript
try {
  const parsed = JSON.parse(currentValue);
  const formatted = JSON.stringify(parsed, null, 2);
  // 成功处理
} catch (e) {
  message.error('JSON格式错误，无法格式化');
}
```

### 3. 配置验证错误

- 前端验证：实时JSON语法检查
- 后端验证：Pydantic模型验证
- 用户友好的错误提示

## 使用说明

### 用户操作流程

1. **查看Schema**: 打开配置页面，切换到"Schema提取"选项卡
2. **编辑Schema**: 在文本框中直接编辑JSON内容
3. **格式化**: 点击"格式化"按钮美化JSON格式
4. **验证**: 系统自动验证JSON语法
5. **保存**: 点击保存按钮应用配置

### 最佳实践

1. **使用格式化**: 编辑完成后使用格式化按钮提高可读性
2. **逐步验证**: 大幅修改时分步骤进行，及时验证
3. **备份重要配置**: 重大修改前先备份当前配置
4. **参考默认Schema**: 可以基于默认Schema进行修改

## 总结

通过这次修复：

- ✅ **解决了显示问题**: Schema不再显示为`[object Object]`
- ✅ **提供了完整功能**: 用户可以查看、编辑、格式化Schema
- ✅ **增强了用户体验**: 添加了格式化按钮和等宽字体
- ✅ **保证了数据完整性**: 正确的序列化/反序列化处理
- ✅ **提供了错误处理**: 友好的错误提示和验证

现在用户可以完全通过UI界面管理复杂的数据提取Schema，无需手动编辑配置文件。
