#!/usr/bin/env python3
"""
验证后端重启是否成功
"""

import asyncio
import aiohttp
import time

API_BASE_URL = "http://localhost:8000"

async def verify_backend_restart():
    """验证后端重启是否成功"""
    
    print("🔄 验证后端重启状态...")
    
    # 等待后端启动
    max_retries = 30
    retry_count = 0
    
    async with aiohttp.ClientSession() as session:
        while retry_count < max_retries:
            try:
                print(f"尝试连接后端... ({retry_count + 1}/{max_retries})")
                
                # 测试健康检查端点
                async with session.get(f"{API_BASE_URL}/health", timeout=5) as response:
                    if response.status == 200:
                        print("✅ 后端健康检查通过")
                        break
                    else:
                        print(f"❌ 健康检查失败: {response.status}")
                        
            except Exception as e:
                print(f"⏳ 连接失败: {e}")
                
            retry_count += 1
            if retry_count < max_retries:
                await asyncio.sleep(2)
        
        if retry_count >= max_retries:
            print("❌ 后端启动超时，请检查Docker容器状态")
            return False
        
        # 测试API接口
        print("\n🧪 测试关键API接口...")
        
        # 1. 测试任务列表API
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    print(f"✅ 任务列表API正常: {len(tasks)} 个任务")
                else:
                    print(f"❌ 任务列表API失败: {response.status}")
        except Exception as e:
            print(f"❌ 任务列表API异常: {e}")
        
        # 2. 测试URL池API
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/url-pool/") as response:
                if response.status == 200:
                    data = await response.json()
                    urls = data.get('data', [])
                    print(f"✅ URL池API正常: {len(urls)} 个URL")
                else:
                    print(f"❌ URL池API失败: {response.status}")
        except Exception as e:
            print(f"❌ URL池API异常: {e}")
        
        # 3. 测试新修复的API（如果有任务的话）
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        
                        # 测试任务详情API
                        async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as detail_response:
                            if detail_response.status == 200:
                                detail_data = await detail_response.json()
                                task_detail = detail_data.get('data', {})
                                print(f"✅ 任务详情API正常: {task_detail.get('name')}")
                                print(f"   URL数量: {task_detail.get('total_urls')}")
                            else:
                                print(f"❌ 任务详情API失败: {detail_response.status}")
                        
                        # 测试统计API
                        async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as stats_response:
                            if stats_response.status == 200:
                                stats_data = await stats_response.json()
                                stats = stats_data.get('data', {})
                                url_stats = stats.get('url_stats', {})
                                print(f"✅ 统计API正常: {url_stats.get('total_urls')} 个URL")
                            else:
                                print(f"❌ 统计API失败: {stats_response.status}")
                    else:
                        print("ℹ️ 没有任务，跳过任务相关API测试")
        except Exception as e:
            print(f"❌ 任务相关API测试异常: {e}")
        
        print("\n🎉 后端重启验证完成!")
        return True

if __name__ == "__main__":
    asyncio.run(verify_backend_restart())
