#!/usr/bin/env python3
"""
新架构API测试脚本
验证所有新架构API接口的功能
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime
from typing import Dict, Any, List


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
        # 测试数据存储
        self.test_data = {
            "crawler_config_id": None,
            "backend_config_id": None,
            "worker_id": None,
            "assignment_id": None
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_api(self, method: str, endpoint: str, data: Dict[str, Any] = None, expected_status: int = 200) -> Dict[str, Any]:
        """测试API接口"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    result = await self._process_response(response, expected_status, endpoint)
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    result = await self._process_response(response, expected_status, endpoint)
            elif method.upper() == "PUT":
                async with self.session.put(url, json=data) as response:
                    result = await self._process_response(response, expected_status, endpoint)
            elif method.upper() == "DELETE":
                async with self.session.delete(url) as response:
                    result = await self._process_response(response, expected_status, endpoint)
            else:
                result = {"success": False, "error": f"Unsupported method: {method}"}
            
            return result
            
        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "endpoint": endpoint,
                "method": method
            }
            self.test_results.append(result)
            return result
    
    async def _process_response(self, response, expected_status: int, endpoint: str) -> Dict[str, Any]:
        """处理响应"""
        try:
            response_data = await response.json()
        except:
            response_data = await response.text()
        
        success = response.status == expected_status
        result = {
            "success": success,
            "status": response.status,
            "expected_status": expected_status,
            "endpoint": endpoint,
            "data": response_data if success else None,
            "error": response_data if not success else None
        }
        
        self.test_results.append(result)
        return result
    
    async def test_crawler_config_apis(self):
        """测试爬取配置API"""
        print("🧪 测试爬取配置API...")
        
        # 1. 获取配置列表（应该为空）
        result = await self.test_api("GET", "/api/v1/crawler-configs/")
        print(f"  ✅ 获取配置列表: {result['success']}")
        
        # 2. 创建配置（使用默认配置模板）
        config_data = {
            "config_name": "测试爬取配置",
            "description": "API测试用的爬取配置",
            "browser": {
                "headless": True,
                "verbose": False,
                "viewport_width": 1920,
                "viewport_height": 1080,
                "wait_for": 2.0,
                "timeout": 30,  # 修复：使用30秒而不是30000毫秒
                "ignore_https_errors": True,
                "extra_args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation"
                ]
            },
            "crawler": {
                "method": "arun_many",
                "verbose": True,
                "check_robots_txt": False,
                "fetch_ssl_certificate": False,
                "simulate_user": True,
                "magic": True,
                "override_navigator": True,
                "remove_overlay_elements": True,
                "ignore_body_visibility": True,
                "adjust_viewport_to_content": True,
                "wait_until": "domcontentloaded",
                "wait_for_images": False,
                "page_timeout": 60000,
                "delay_before_return_html": 0.1,
                "js_only": False,
                "scan_full_page": True,
                "process_iframes": True,
                "scroll_delay": 0.2,
                "cache_mode": "BYPASS",
                "screenshot": False,
                "pdf": False,
                "capture_mhtml": False,
                "exclude_external_images": False,
                "exclude_all_images": False,
                "image_score_threshold": 50,
                "image_description_min_word_threshold": 50,
                "table_score_threshold": 7,
                "capture_network_requests": False,
                "capture_console_messages": False,
                "log_console": False,
                "extraction_strategy": "LLMExtractionStrategy",
                "chunking_strategy": "IdentityChunking",
                "markdown_generator": "DefaultMarkdownGenerator",
                "bypass_cache": True
            },
            "llm": {
                "query": "提取页面中所有文章的标题、作者和发布时间",
                "provider": "openai",
                "model": "deepseek-v3-0324",
                "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi",  # 修复：添加必填的api_key
                "base_url": "https://api.lkeap.cloud.tencent.com/v1",
                "temperature": 0.0,
                "max_tokens": 16384,
                "top_p": 0.9
            },
            "schema_extraction": {
                "enabled": True,
                "schema_type": "auto",
                "validate_schema": True,
                "return_raw": False
            },
            "content_processing": {
                "word_count_threshold": 200,
                "css_selector": "",
                "target_elements": [],
                "excluded_tags": ["nav", "footer", "aside"],
                "excluded_selector": "",
                "remove_forms": False,
                "only_text": False,
                "prettify": False,
                "parser_type": "lxml",
                "keep_data_attributes": False,
                "keep_attrs": []
            },
            "link_filtering": {
                "exclude_external_links": False,
                "exclude_internal_links": False,
                "exclude_social_media_links": False,
                "exclude_domains": ["example.com", "ads.google.com", "facebook.com", "twitter.com"],
                "social_media_domains": ["facebook.com", "twitter.com", "instagram.com", "linkedin.com", "youtube.com", "tiktok.com", "pinterest.com", "reddit.com"],
                "exclude_external_images": False,
                "exclude_all_images": False,
                "image_score_threshold": 3,
                "image_description_min_word_threshold": 50,
                "table_score_threshold": 7
            },
            "monitor": {
                "display_mode": "detailed",
                "show_progress": True,
                "log_errors": True
            }
        }
        
        result = await self.test_api("POST", "/api/v1/crawler-configs/", config_data, 200)
        if result["success"]:
            self.test_data["crawler_config_id"] = result["data"]["config_id"]
            print(f"  ✅ 创建配置: {result['success']} (ID: {self.test_data['crawler_config_id'][:8]}...)")
        else:
            print(f"  ❌ 创建配置失败: {result.get('error', 'Unknown error')}")
            return False
        
        # 3. 获取配置详情
        config_id = self.test_data["crawler_config_id"]
        result = await self.test_api("GET", f"/api/v1/crawler-configs/{config_id}")
        print(f"  ✅ 获取配置详情: {result['success']}")
        
        # 4. 更新配置
        update_data = {
            "description": "更新后的配置描述"
        }
        result = await self.test_api("PUT", f"/api/v1/crawler-configs/{config_id}", update_data)
        print(f"  ✅ 更新配置: {result['success']}")
        
        # 5. 验证配置
        result = await self.test_api("POST", f"/api/v1/crawler-configs/{config_id}/validate")
        print(f"  ✅ 验证配置: {result['success']}")
        
        return True
    
    async def test_backend_config_apis(self):
        """测试后端配置API"""
        print("🧪 测试后端配置API...")
        
        # 1. 获取后端配置列表
        result = await self.test_api("GET", "/api/v1/backend-configs/")
        print(f"  ✅ 获取后端配置列表: {result['success']}")
        
        # 2. 创建后端配置
        backend_data = {
            "backend_name": "测试后端配置",
            "description": "API测试用的后端配置",
            "api_endpoint": "http://localhost:11234",
            "timeout": 30000,
            "max_retries": 3,
            "auth_config": {
                "auth_type": "none"
            },
            "max_concurrent": 2,
            "mean_delay": 1000,
            "max_range": 2000,
            "pool_size": 10,
            "memory_threshold": 512,
            "weight": 1,
            "priority": 1,
            "performance_level": "medium"
        }
        
        result = await self.test_api("POST", "/api/v1/backend-configs/", backend_data, 200)
        if result["success"]:
            self.test_data["backend_config_id"] = result["data"]["backend_id"]
            print(f"  ✅ 创建后端配置: {result['success']} (ID: {self.test_data['backend_config_id'][:8]}...)")
        else:
            print(f"  ❌ 创建后端配置失败: {result.get('error', 'Unknown error')}")
            return False
        
        # 3. 获取后端配置详情
        backend_id = self.test_data["backend_config_id"]
        result = await self.test_api("GET", f"/api/v1/backend-configs/{backend_id}")
        print(f"  ✅ 获取后端配置详情: {result['success']}")
        
        # 4. 测试连接
        result = await self.test_api("POST", f"/api/v1/backend-configs/{backend_id}/test-connection")
        print(f"  ✅ 测试连接: {result['success']}")
        
        # 5. 获取可用后端
        result = await self.test_api("GET", "/api/v1/backend-configs/available")
        print(f"  ✅ 获取可用后端: {result['success']}")
        
        return True
    
    async def test_crawler_worker_apis(self):
        """测试Worker API"""
        print("🧪 测试Worker API...")
        
        if not self.test_data["crawler_config_id"] or not self.test_data["backend_config_id"]:
            print("  ❌ 缺少必要的配置ID，跳过Worker测试")
            return False
        
        # 1. 获取Worker列表
        result = await self.test_api("GET", "/api/v1/crawler-workers/")
        print(f"  ✅ 获取Worker列表: {result['success']}")
        
        # 2. 创建Worker
        worker_data = {
            "worker_name": "测试Worker",
            "description": "API测试用的Worker",
            "crawler_config_id": self.test_data["crawler_config_id"],
            "backend_config_id": self.test_data["backend_config_id"],
            "allocated_concurrent": 1,
            "priority": "normal"
        }
        
        result = await self.test_api("POST", "/api/v1/crawler-workers/", worker_data, 200)
        if result["success"]:
            self.test_data["worker_id"] = result["data"]["worker_id"]
            print(f"  ✅ 创建Worker: {result['success']} (ID: {self.test_data['worker_id'][:8]}...)")
        else:
            print(f"  ❌ 创建Worker失败: {result.get('error', 'Unknown error')}")
            return False
        
        # 3. 获取Worker详情
        worker_id = self.test_data["worker_id"]
        result = await self.test_api("GET", f"/api/v1/crawler-workers/{worker_id}")
        print(f"  ✅ 获取Worker详情: {result['success']}")
        
        # 4. 获取Worker详细信息
        result = await self.test_api("GET", f"/api/v1/crawler-workers/{worker_id}/detail")
        print(f"  ✅ 获取Worker详细信息: {result['success']}")
        
        # 5. 检查兼容性
        result = await self.test_api("POST", f"/api/v1/crawler-workers/{worker_id}/check-compatibility")
        print(f"  ✅ 检查Worker兼容性: {result['success']}")
        
        # 6. 获取可用Worker
        result = await self.test_api("GET", "/api/v1/crawler-workers/available")
        print(f"  ✅ 获取可用Worker: {result['success']}")
        
        return True
    
    async def test_task_assignment_apis(self):
        """测试任务分配API"""
        print("🧪 测试任务分配API...")
        
        if not self.test_data["worker_id"]:
            print("  ❌ 缺少Worker ID，跳过任务分配测试")
            return False
        
        # 1. 获取任务分配列表
        result = await self.test_api("GET", "/api/v1/task-assignments/")
        print(f"  ✅ 获取任务分配列表: {result['success']}")
        
        # 2. 检测Worker冲突
        worker_ids = [self.test_data["worker_id"]]
        result = await self.test_api("POST", "/api/v1/task-assignments/check-conflicts", worker_ids)
        print(f"  ✅ 检测Worker冲突: {result['success']}")
        
        # 3. 创建任务分配
        assignment_data = {
            "task_id": "test_task_001",
            "assignment_name": "测试任务分配",
            "description": "API测试用的任务分配",
            "worker_ids": [self.test_data["worker_id"]],
            "assignment_strategy": "health_based",
            "auto_failover": True,
            "total_urls": 100
        }
        
        result = await self.test_api("POST", "/api/v1/task-assignments/", assignment_data, 200)
        if result["success"]:
            self.test_data["assignment_id"] = result["data"]["assignment_id"]
            print(f"  ✅ 创建任务分配: {result['success']} (ID: {self.test_data['assignment_id'][:8]}...)")
        else:
            print(f"  ❌ 创建任务分配失败: {result.get('error', 'Unknown error')}")
            return False
        
        # 4. 获取任务分配详情
        assignment_id = self.test_data["assignment_id"]
        result = await self.test_api("GET", f"/api/v1/task-assignments/{assignment_id}")
        print(f"  ✅ 获取任务分配详情: {result['success']}")
        
        # 5. 更新任务进度
        result = await self.test_api("POST", f"/api/v1/task-assignments/{assignment_id}/update-progress?processed_urls=10&successful_urls=8&failed_urls=2")
        print(f"  ✅ 更新任务进度: {result['success']}")
        
        # 6. 获取任务进度
        result = await self.test_api("GET", f"/api/v1/task-assignments/{assignment_id}/progress")
        print(f"  ✅ 获取任务进度: {result['success']}")
        
        return True
    
    async def cleanup_test_data(self):
        """清理测试数据"""
        print("🧹 清理测试数据...")
        
        # 删除任务分配
        if self.test_data["assignment_id"]:
            result = await self.test_api("DELETE", f"/api/v1/task-assignments/{self.test_data['assignment_id']}")
            print(f"  ✅ 删除任务分配: {result['success']}")
        
        # 删除Worker
        if self.test_data["worker_id"]:
            result = await self.test_api("DELETE", f"/api/v1/crawler-workers/{self.test_data['worker_id']}")
            print(f"  ✅ 删除Worker: {result['success']}")
        
        # 删除后端配置
        if self.test_data["backend_config_id"]:
            result = await self.test_api("DELETE", f"/api/v1/backend-configs/{self.test_data['backend_config_id']}")
            print(f"  ✅ 删除后端配置: {result['success']}")
        
        # 删除爬取配置
        if self.test_data["crawler_config_id"]:
            result = await self.test_api("DELETE", f"/api/v1/crawler-configs/{self.test_data['crawler_config_id']}")
            print(f"  ✅ 删除爬取配置: {result['success']}")
    
    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["success"]])
        failed_tests = total_tests - successful_tests
        
        print("\n" + "="*50)
        print("📊 API测试报告")
        print("="*50)
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['endpoint']}: {result.get('error', 'Unknown error')}")
        
        return failed_tests == 0


async def main():
    """主测试流程"""
    print("🚀 开始新架构API测试...")
    
    async with APITester() as tester:
        try:
            # 测试各个API模块
            success = True
            
            success &= await tester.test_crawler_config_apis()
            success &= await tester.test_backend_config_apis()
            success &= await tester.test_crawler_worker_apis()
            success &= await tester.test_task_assignment_apis()
            
            # 清理测试数据
            await tester.cleanup_test_data()
            
            # 生成测试报告
            overall_success = tester.generate_test_report()
            
            if overall_success:
                print("\n🎉 所有API测试通过！新架构后端功能正常。")
                return 0
            else:
                print("\n❌ 部分API测试失败，需要检查和修复。")
                return 1
                
        except Exception as e:
            print(f"\n❌ 测试过程发生错误: {e}")
            return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
