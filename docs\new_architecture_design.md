# MonIt 新架构设计文档

## 📋 概述

本文档详细描述了MonIt系统的新架构设计，该架构简化了原有的复杂抽象层，采用更直接和清晰的组件关系。

## 🎯 核心架构

### 架构公式
```
CrawlerConfig (爬取配置) + BackendConfig (后端配置) = CrawlerWorker
TaskAssignment = Task + CrawlerWorker集合 (带互斥检查)
```

### 架构图
```mermaid
graph TD
    A[CrawlerConfig 爬取配置] --> C[CrawlerWorker 爬虫工作器]
    B[BackendConfig 后端配置] --> C
    C --> D[TaskAssignment 任务分配]
    E[Task 任务] --> D
    D --> F[任务执行]
    
    subgraph "配置层"
        A
        B
    end
    
    subgraph "工作器层"
        C
    end
    
    subgraph "任务层"
        E
        D
        F
    end
```

## 🏗️ 组件详细设计

### 1. CrawlerConfig (爬取配置)
定义爬虫的行为参数和配置模板。

**核心属性：**
- 浏览器配置 (Browser Settings)
- LLM配置 (LLM Settings)
- 爬虫行为配置 (Crawler Behavior)
- Schema提取配置 (Schema Extraction)
- 内容处理配置 (Content Processing)
- 链接过滤配置 (Link Filtering)
- 监控配置 (Monitor Settings)

### 2. BackendConfig (后端配置)
定义后端服务的连接和性能参数。

**核心属性：**
- 连接配置 (Connection Settings)
- 性能配置 (Performance Settings)
- 超时配置 (Timeout Settings)
- 重试配置 (Retry Settings)
- 健康检查配置 (Health Check Settings)

### 3. CrawlerWorker (爬虫工作器)
由CrawlerConfig和BackendConfig组合而成的可执行单元。

**核心属性：**
- worker_id: 唯一标识符
- name: 工作器名称
- crawler_config_id: 关联的爬取配置ID
- backend_config_id: 关联的后端配置ID
- status: 工作器状态 (active/inactive/error)
- priority: 优先级 (high/medium/low)
- max_concurrent_tasks: 最大并发任务数
- created_at: 创建时间
- updated_at: 更新时间

### 4. TaskAssignment (任务分配)
将任务分配给CrawlerWorker集合，支持负载均衡和互斥检查。

**核心属性：**
- assignment_id: 分配唯一标识符
- task_id: 关联的任务ID
- worker_ids: 分配的工作器ID列表
- assignment_strategy: 分配策略 (round_robin/weighted/least_connections)
- auto_failover: 自动故障转移
- mutual_exclusion: 互斥检查配置
- status: 分配状态
- created_at: 创建时间

## 🔄 工作流程

### 1. 配置创建流程
```
1. 创建 CrawlerConfig (定义爬取行为)
2. 创建 BackendConfig (定义后端连接)
3. 创建 CrawlerWorker (组合配置)
4. 验证 Worker 兼容性
```

### 2. 任务分配流程
```
1. 创建 Task (定义任务内容)
2. 选择可用的 CrawlerWorker 集合
3. 创建 TaskAssignment (分配策略)
4. 执行互斥检查
5. 开始任务执行
```

### 3. 负载均衡策略
- **轮询 (Round Robin)**: 依次分配给每个Worker
- **加权轮询 (Weighted Round Robin)**: 根据Worker优先级分配
- **最少连接 (Least Connections)**: 分配给当前任务最少的Worker
- **随机 (Random)**: 随机选择可用Worker
- **健康优先 (Health First)**: 优先选择健康状态的Worker

## 📡 API 接口详情

### CrawlerConfig API

#### 获取所有爬取配置
```http
GET /api/v1/crawler-configs/
```

#### 创建爬取配置
```http
POST /api/v1/crawler-configs/
Content-Type: application/json

{
  "name": "配置名称",
  "description": "配置描述",
  "browser_config": { ... },
  "llm_config": { ... },
  "crawler_config": { ... },
  "schema_config": { ... },
  "content_config": { ... },
  "links_config": { ... },
  "monitor_config": { ... }
}
```

#### 获取单个爬取配置
```http
GET /api/v1/crawler-configs/{config_id}/
```

#### 更新爬取配置
```http
PUT /api/v1/crawler-configs/{config_id}/
```

#### 删除爬取配置
```http
DELETE /api/v1/crawler-configs/{config_id}/
```

#### 验证爬取配置
```http
POST /api/v1/crawler-configs/{config_id}/validate/
```

### BackendConfig API

#### 获取所有后端配置
```http
GET /api/v1/backend-configs/
```

#### 创建后端配置
```http
POST /api/v1/backend-configs/
Content-Type: application/json

{
  "name": "后端配置名称",
  "description": "配置描述",
  "host": "localhost",
  "port": 8080,
  "protocol": "http",
  "timeout": 30,
  "max_retries": 3,
  "health_check_interval": 60,
  "performance_level": "medium"
}
```

#### 测试后端连接
```http
POST /api/v1/backend-configs/{backend_id}/test-connection/
```

#### 获取后端统计信息
```http
GET /api/v1/backend-configs/{backend_id}/stats/
```

### CrawlerWorker API

#### 获取所有工作器
```http
GET /api/v1/crawler-workers/
```

#### 创建工作器
```http
POST /api/v1/crawler-workers/
Content-Type: application/json

{
  "name": "工作器名称",
  "description": "工作器描述",
  "crawler_config_id": "crawler_config_uuid",
  "backend_config_id": "backend_config_uuid",
  "priority": "medium",
  "max_concurrent_tasks": 5,
  "tags": ["tag1", "tag2"]
}
```

#### 检查工作器兼容性
```http
POST /api/v1/crawler-workers/check-compatibility/
Content-Type: application/json

{
  "crawler_config_id": "crawler_config_uuid",
  "backend_config_id": "backend_config_uuid"
}
```

#### 启动/停止工作器
```http
POST /api/v1/crawler-workers/{worker_id}/start/
POST /api/v1/crawler-workers/{worker_id}/stop/
```

### TaskAssignment API

#### 获取所有任务分配
```http
GET /api/v1/task-assignments/
```

#### 创建任务分配
```http
POST /api/v1/task-assignments/
Content-Type: application/json

{
  "task_id": "task_uuid",
  "worker_ids": ["worker1_uuid", "worker2_uuid"],
  "assignment_strategy": "round_robin",
  "auto_failover": true,
  "mutual_exclusion": {
    "enabled": true,
    "scope": "url_domain"
  }
}
```

#### 获取分配建议
```http
POST /api/v1/task-assignments/recommend/
Content-Type: application/json

{
  "task_requirements": {
    "estimated_duration": 3600,
    "resource_requirements": "medium",
    "priority": "high"
  },
  "constraints": {
    "exclude_workers": ["worker_uuid"],
    "required_tags": ["tag1"]
  }
}
```

## 🔒 互斥检查机制

### 互斥范围
- **URL级别**: 同一URL不能被多个Worker同时处理
- **域名级别**: 同一域名下的URL有访问频率限制
- **Worker级别**: 单个Worker的并发任务数限制
- **全局级别**: 系统总体并发数限制

### 互斥实现
```python
class MutualExclusionChecker:
    def check_assignment_conflicts(
        self, 
        task_id: str, 
        worker_ids: List[str]
    ) -> ConflictCheckResult:
        # 检查URL冲突
        # 检查域名频率限制
        # 检查Worker容量
        # 检查全局限制
        pass
```

## 📊 监控和统计

### Worker性能指标
- 任务完成率
- 平均响应时间
- 错误率
- 资源使用率

### 任务分配指标
- 分配成功率
- 负载均衡效果
- 故障转移次数
- 互斥冲突次数

## 🚀 部署和配置

### 环境变量
```bash
# Redis配置
REDIS_URL=redis://redis:6379/0

# API配置
API_BASE_URL=http://localhost:8000

# 监控配置
MONITORING_ENABLED=true
METRICS_EXPORT_INTERVAL=60
```

### Docker配置
```yaml
services:
  backend:
    environment:
      - REDIS_URL=redis://redis:6379/0
  
  frontend:
    environment:
      - REACT_APP_API_URL=http://localhost:8000
```

## 📝 最佳实践

### 配置管理
1. 使用模板化的配置减少重复
2. 定期验证配置的有效性
3. 保持配置版本控制

### Worker管理
1. 根据任务类型创建专门的Worker
2. 合理设置并发数避免资源竞争
3. 定期监控Worker健康状态

### 任务分配
1. 选择合适的负载均衡策略
2. 启用自动故障转移提高可靠性
3. 配置合理的互斥规则避免冲突

## 🔄 版本历史

### v2.0.0 (当前版本)
- 实现新的简化架构
- 移除爬虫池抽象层
- 添加TaskAssignment机制
- 支持互斥检查和负载均衡

### v1.x.x (旧版本)
- 使用爬虫池管理
- 复杂的抽象层结构
- 已废弃

## 🛠️ 技术实现细节

### 数据模型定义

#### CrawlerConfig Schema
```python
class CrawlerConfigCreate(BaseModel):
    name: str
    description: Optional[str] = None
    browser_config: Dict[str, Any]
    llm_config: Dict[str, Any]
    crawler_config: Dict[str, Any]
    schema_config: Dict[str, Any]
    content_config: Dict[str, Any]
    links_config: Dict[str, Any]
    monitor_config: Dict[str, Any]
    tags: List[str] = []
    is_active: bool = True

class CrawlerConfigData(CrawlerConfigCreate):
    config_id: str
    created_at: datetime
    updated_at: datetime
    version: int
```

#### BackendConfig Schema
```python
class BackendConfigCreate(BaseModel):
    name: str
    description: Optional[str] = None
    host: str
    port: int
    protocol: str = "http"
    timeout: int = 30
    max_retries: int = 3
    health_check_interval: int = 60
    performance_level: str = "medium"
    auth_config: Optional[Dict[str, Any]] = None

class BackendConfigData(BackendConfigCreate):
    backend_id: str
    created_at: datetime
    updated_at: datetime
    last_health_check: Optional[datetime] = None
    health_status: str = "unknown"
```

#### CrawlerWorker Schema
```python
class CrawlerWorkerCreate(BaseModel):
    name: str
    description: Optional[str] = None
    crawler_config_id: str
    backend_config_id: str
    priority: WorkerPriority = WorkerPriority.MEDIUM
    max_concurrent_tasks: int = 5
    tags: List[str] = []
    auto_start: bool = True

class CrawlerWorker(CrawlerWorkerCreate):
    worker_id: str
    status: WorkerStatus
    current_tasks: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    created_at: datetime
    updated_at: datetime
    last_activity: Optional[datetime] = None
```

#### TaskAssignment Schema
```python
class TaskAssignmentCreate(BaseModel):
    task_id: str
    worker_ids: List[str]
    assignment_strategy: AssignmentStrategy = AssignmentStrategy.ROUND_ROBIN
    auto_failover: bool = True
    max_retries: int = 3
    mutual_exclusion: MutualExclusionConfig
    priority: TaskPriority = TaskPriority.MEDIUM

class TaskAssignment(TaskAssignmentCreate):
    assignment_id: str
    status: AssignmentStatus
    assigned_worker_id: Optional[str] = None
    retry_count: int = 0
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
```

### Redis数据结构

#### 配置存储
```
# 爬取配置
crawler_configs:{config_id} -> CrawlerConfigData (JSON)
crawler_configs:index -> Set[config_id]
crawler_configs:by_name:{name} -> config_id

# 后端配置
backend_configs:{backend_id} -> BackendConfigData (JSON)
backend_configs:index -> Set[backend_id]
backend_configs:health:{backend_id} -> HealthStatus (JSON)

# 工作器
crawler_workers:{worker_id} -> CrawlerWorker (JSON)
crawler_workers:index -> Set[worker_id]
crawler_workers:by_status:{status} -> Set[worker_id]
crawler_workers:by_config:{config_id} -> Set[worker_id]

# 任务分配
task_assignments:{assignment_id} -> TaskAssignment (JSON)
task_assignments:index -> Set[assignment_id]
task_assignments:by_task:{task_id} -> assignment_id
task_assignments:by_worker:{worker_id} -> Set[assignment_id]
```

#### 互斥锁结构
```
# URL级别锁
mutex:url:{url_hash} -> {worker_id, timestamp}

# 域名级别限制
mutex:domain:{domain} -> {current_count, last_reset}

# Worker容量限制
mutex:worker:{worker_id} -> {current_tasks, max_tasks}

# 全局限制
mutex:global -> {total_active_tasks, max_global_tasks}
```

### 服务层实现

#### CrawlerConfigService
```python
class CrawlerConfigService:
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.key_prefix = "crawler_configs"

    async def create_config(self, config_data: CrawlerConfigCreate) -> CrawlerConfigData:
        config_id = str(uuid.uuid4())
        config = CrawlerConfigData(
            config_id=config_id,
            **config_data.dict(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            version=1
        )

        # 存储到Redis
        await self.redis.hset(
            f"{self.key_prefix}:{config_id}",
            mapping=config.dict()
        )
        await self.redis.sadd(f"{self.key_prefix}:index", config_id)

        return config

    async def validate_config(self, config_id: str) -> CrawlerConfigValidationResult:
        # 验证配置的有效性
        # 检查必需字段
        # 验证配置格式
        # 测试配置兼容性
        pass
```

#### TaskAssignmentService
```python
class TaskAssignmentService:
    def __init__(self, redis_client: Redis, mutex_checker: MutualExclusionChecker):
        self.redis = redis_client
        self.mutex_checker = mutex_checker

    async def create_assignment(
        self,
        assignment_data: TaskAssignmentCreate
    ) -> TaskAssignment:
        # 检查互斥冲突
        conflict_result = await self.mutex_checker.check_conflicts(
            assignment_data.task_id,
            assignment_data.worker_ids
        )

        if conflict_result.has_conflicts:
            raise ConflictError(conflict_result.conflicts)

        # 选择最佳Worker
        selected_worker = await self._select_worker(
            assignment_data.worker_ids,
            assignment_data.assignment_strategy
        )

        # 创建分配记录
        assignment = TaskAssignment(
            assignment_id=str(uuid.uuid4()),
            **assignment_data.dict(),
            assigned_worker_id=selected_worker,
            status=AssignmentStatus.ASSIGNED,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # 存储到Redis
        await self._store_assignment(assignment)

        # 获取互斥锁
        await self.mutex_checker.acquire_locks(
            assignment.task_id,
            selected_worker
        )

        return assignment

    async def _select_worker(
        self,
        worker_ids: List[str],
        strategy: AssignmentStrategy
    ) -> str:
        if strategy == AssignmentStrategy.ROUND_ROBIN:
            return await self._round_robin_selection(worker_ids)
        elif strategy == AssignmentStrategy.LEAST_CONNECTIONS:
            return await self._least_connections_selection(worker_ids)
        elif strategy == AssignmentStrategy.WEIGHTED:
            return await self._weighted_selection(worker_ids)
        else:
            return random.choice(worker_ids)
```

### 前端组件架构

#### 配置管理页面结构
```
src/pages/Configuration/
├── NewConfiguration.tsx          # 主页面
├── components/
│   ├── CrawlerConfigForm.tsx     # 爬取配置表单
│   ├── BackendConfigForm.tsx     # 后端配置表单
│   └── ConfigStatsCard.tsx       # 配置统计卡片
└── hooks/
    ├── useCrawlerConfig.ts       # 爬取配置Hook
    └── useBackendConfig.ts       # 后端配置Hook
```

#### Worker管理页面结构
```
src/pages/WorkerManagement/
├── index.tsx                     # 主页面
├── components/
│   ├── WorkerForm.tsx           # Worker表单
│   ├── WorkerDetailModal.tsx    # Worker详情弹窗
│   └── WorkerStatsCard.tsx      # Worker统计卡片
└── hooks/
    └── useWorkerManagement.ts   # Worker管理Hook
```

#### API服务层
```
src/services/
├── crawlerConfigApi.ts          # 爬取配置API
├── backendConfigApi.ts          # 后端配置API
├── workerApi.ts                 # Worker API
└── taskAssignmentApi.ts         # 任务分配API
```

### 错误处理和日志

#### 错误类型定义
```python
class ConfigurationError(Exception):
    """配置相关错误"""
    pass

class WorkerCompatibilityError(Exception):
    """Worker兼容性错误"""
    pass

class TaskAssignmentError(Exception):
    """任务分配错误"""
    pass

class MutualExclusionError(Exception):
    """互斥检查错误"""
    pass
```

#### 日志记录
```python
import logging

logger = logging.getLogger(__name__)

class AuditLogger:
    @staticmethod
    def log_config_change(config_id: str, action: str, user_id: str):
        logger.info(f"Config {action}: {config_id} by user {user_id}")

    @staticmethod
    def log_worker_action(worker_id: str, action: str, details: dict):
        logger.info(f"Worker {action}: {worker_id}, details: {details}")

    @staticmethod
    def log_assignment_event(assignment_id: str, event: str, context: dict):
        logger.info(f"Assignment {event}: {assignment_id}, context: {context}")
```

## 🧪 测试策略

### 单元测试
```python
# 测试配置创建
def test_create_crawler_config():
    service = CrawlerConfigService(redis_client)
    config_data = CrawlerConfigCreate(name="test", ...)
    result = await service.create_config(config_data)
    assert result.config_id is not None

# 测试Worker兼容性
def test_worker_compatibility():
    checker = WorkerCompatibilityChecker()
    result = await checker.check_compatibility(
        crawler_config_id="config1",
        backend_config_id="backend1"
    )
    assert result.is_compatible

# 测试任务分配
def test_task_assignment():
    service = TaskAssignmentService(redis_client, mutex_checker)
    assignment_data = TaskAssignmentCreate(...)
    result = await service.create_assignment(assignment_data)
    assert result.status == AssignmentStatus.ASSIGNED
```

### 集成测试
```python
# 测试完整工作流
async def test_complete_workflow():
    # 1. 创建配置
    crawler_config = await create_crawler_config()
    backend_config = await create_backend_config()

    # 2. 创建Worker
    worker = await create_worker(crawler_config.config_id, backend_config.backend_id)

    # 3. 创建任务分配
    assignment = await create_task_assignment(task_id, [worker.worker_id])

    # 4. 验证分配成功
    assert assignment.status == AssignmentStatus.ASSIGNED
    assert assignment.assigned_worker_id == worker.worker_id
```

### 性能测试
```python
# 测试并发分配
async def test_concurrent_assignments():
    tasks = []
    for i in range(100):
        task = create_task_assignment(f"task_{i}", worker_ids)
        tasks.append(task)

    results = await asyncio.gather(*tasks)

    # 验证没有冲突
    assigned_workers = [r.assigned_worker_id for r in results]
    assert len(set(assigned_workers)) <= len(worker_ids)
```

## 📈 性能优化

### Redis优化
- 使用Pipeline批量操作
- 设置合理的过期时间
- 使用Redis Cluster提高可用性

### API优化
- 实现响应缓存
- 使用异步处理
- 添加请求限流

### 前端优化
- 实现虚拟滚动
- 使用React.memo优化渲染
- 添加数据预加载

## 🔐 安全考虑

### 认证授权
- JWT Token认证
- 基于角色的访问控制
- API密钥管理

### 数据安全
- 敏感配置加密存储
- 审计日志记录
- 输入验证和清理

### 网络安全
- HTTPS强制使用
- CORS配置
- 请求频率限制
