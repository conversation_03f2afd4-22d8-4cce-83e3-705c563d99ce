#!/usr/bin/env python3
"""
测试所有API是否使用统一的新结构

验证所有API端点都使用新的分层存储结构
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_api_unified_structure():
    """测试所有API是否使用统一的新结构"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试所有API使用统一的新结构")
    print("=" * 60)
    
    created_url_ids = []
    task_id = None
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/api-test-url",
            "platform": "mercadolibre",
            "title": "API测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'api_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'api_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:api_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 测试任务创建API (task_create_routes.py)
        print(f"\n📋 步骤2: 测试任务创建API")
        
        create_data = {
            "name": "API结构测试任务",
            "description": "用于测试API统一结构的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "11:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            print(f"  ✅ 任务创建API正常: {task_id}")
        else:
            print(f"  ❌ 任务创建API失败: {create_response.text}")
            return
        
        # 步骤3: 测试监控任务列表API (monitoring_task_routes.py)
        print(f"\n📋 步骤3: 测试监控任务列表API")
        
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        if list_response.status_code == 200:
            list_result = list_response.json()
            tasks = list_result.get('data', [])
            found_task = any(t.get('id') == task_id for t in tasks)
            if found_task:
                print(f"  ✅ 监控任务列表API正常: 找到创建的任务")
            else:
                print(f"  ⚠️ 监控任务列表API: 未找到创建的任务")
        else:
            print(f"  ❌ 监控任务列表API失败: {list_response.text}")
        
        # 步骤4: 测试任务详情API
        print(f"\n📋 步骤4: 测试任务详情API")
        
        detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
        if detail_response.status_code == 200:
            detail_result = detail_response.json()
            task_data = detail_result.get('data', {})
            total_urls = task_data.get('total_urls', 0)
            if total_urls > 0:
                print(f"  ✅ 任务详情API正常: URL数量 {total_urls}")
            else:
                print(f"  ⚠️ 任务详情API: URL数量为0")
        else:
            print(f"  ❌ 任务详情API失败: {detail_response.text}")
        
        # 步骤5: 测试任务URL列表API
        print(f"\n📋 步骤5: 测试任务URL列表API")
        
        urls_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}/urls", timeout=10)
        if urls_response.status_code == 200:
            urls_result = urls_response.json()
            task_urls = urls_result.get('data', [])
            if len(task_urls) > 0:
                print(f"  ✅ 任务URL列表API正常: 找到 {len(task_urls)} 个URL")
            else:
                print(f"  ⚠️ 任务URL列表API: 未找到URL")
        else:
            print(f"  ❌ 任务URL列表API失败: {urls_response.text}")
        
        # 步骤6: 检查Redis存储结构
        print(f"\n📋 步骤6: 检查Redis存储结构")
        
        # 检查新的分层结构
        basic_key = f"monitoring_tasks:tasks:{task_id}:basic"
        config_key = f"monitoring_tasks:tasks:{task_id}:config"
        schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
        stats_key = f"monitoring_tasks:tasks:{task_id}:stats"
        urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
        
        print(f"  新的分层结构:")
        print(f"    basic存在: {'✅' if r.exists(basic_key) else '❌'}")
        print(f"    config存在: {'✅' if r.exists(config_key) else '❌'}")
        print(f"    schedule存在: {'✅' if r.exists(schedule_key) else '❌'}")
        print(f"    stats存在: {'✅' if r.exists(stats_key) else '❌'}")
        print(f"    urls存在: {'✅' if r.exists(urls_key) else '❌'}")
        
        if r.exists(urls_key):
            url_count = r.scard(urls_key)
            print(f"    URL数量: {url_count}")
        
        # 检查URL反向关联
        url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
        print(f"  URL反向关联:")
        print(f"    反向关联存在: {'✅' if r.exists(url_tasks_key) else '❌'}")
        if r.exists(url_tasks_key):
            associated_tasks = r.smembers(url_tasks_key)
            print(f"    关联的任务数: {len(associated_tasks)}")
        
        # 检查索引
        all_task_ids_key = "monitoring_tasks:indexes:all_task_ids"
        task_in_index = r.sismember(all_task_ids_key, task_id)
        print(f"  索引:")
        print(f"    任务在总索引中: {'✅' if task_in_index else '❌'}")
        
        # 检查是否有旧格式的残留
        old_task_key = f"monitoring_tasks:{task_id}"
        old_task_urls_key = f"monitoring_tasks:task_urls:{task_id}"
        print(f"  旧格式检查:")
        print(f"    旧任务键存在: {'❌ (应该不存在)' if r.exists(old_task_key) else '✅'}")
        print(f"    旧URL关联键存在: {'❌ (应该不存在)' if r.exists(old_task_urls_key) else '✅'}")
        
        # 步骤7: 测试任务删除API
        print(f"\n📋 步骤7: 测试任务删除API")
        
        delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
        if delete_response.status_code in [200, 204]:
            print(f"  ✅ 任务删除API正常")
            
            # 检查删除后的清理
            print(f"  删除后清理检查:")
            print(f"    任务基本信息已删除: {'✅' if not r.exists(basic_key) else '❌'}")
            print(f"    URL反向关联已清理: {'✅' if not r.exists(url_tasks_key) else '❌'}")
            print(f"    任务已从索引移除: {'✅' if not r.sismember(all_task_ids_key, task_id) else '❌'}")
        else:
            print(f"  ❌ 任务删除API失败: {delete_response.text}")
        
        print(f"\n🎉 API统一结构测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:api_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_api_unified_structure()
