"""
智能任务分片器

解决外部爬虫API限制问题：
- 外部API限制: 最多2个URL并行处理，单次API调用最多100个URL
- 业务需求: 需要处理1000+商品URL的监控任务
"""

import logging
import math
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    URGENT = 3


@dataclass
class TaskBatch:
    """任务批次"""
    batch_id: str
    urls: List[str]
    priority: TaskPriority
    platform: str
    estimated_duration: float
    created_at: datetime
    metadata: Dict[str, Any]


@dataclass
class SplitterConfig:
    """分片器配置"""
    max_batch_size: int = 100          # 单批次最大URL数量
    max_concurrent_batches: int = 2    # 最大并发批次数
    min_batch_size: int = 1            # 最小批次大小
    priority_boost_factor: float = 1.5 # 优先级提升因子
    load_balance_enabled: bool = True  # 是否启用负载均衡
    adaptive_sizing: bool = True       # 是否启用自适应分片大小


class TaskSplitter:
    """智能任务分片器"""
    
    def __init__(self, config: Optional[SplitterConfig] = None):
        """初始化任务分片器
        
        Args:
            config: 分片器配置
        """
        self.config = config or SplitterConfig()
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self.stats = {
            "total_batches_created": 0,
            "total_urls_processed": 0,
            "avg_batch_size": 0.0,
            "last_split_time": None
        }
        
        # 性能历史记录
        self.performance_history: List[Dict[str, Any]] = []
        
    def split_urls(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        priority: TaskPriority = TaskPriority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[TaskBatch]:
        """将URL列表分片为小批次

        Args:
            urls: URL列表
            platform: 平台类型
            priority: 任务优先级
            metadata: 元数据

        Returns:
            List[TaskBatch]: 任务批次列表
        """
        if not urls:
            return []

        start_time = datetime.now()
        metadata = metadata or {}

        try:
            # 检查是否有用户指定的批次大小
            user_batch_size = None
            if metadata.get("options") and isinstance(metadata["options"], dict):
                user_batch_size = metadata["options"].get("batch_size")

            if user_batch_size and isinstance(user_batch_size, int) and user_batch_size > 0:
                # 使用用户指定的批次大小，但确保在合理范围内
                batch_size = max(
                    self.config.min_batch_size,
                    min(user_batch_size, self.config.max_batch_size)
                )
                self.logger.info(f"Using user-specified batch size: {batch_size} (requested: {user_batch_size})")
            else:
                # 计算最优批次大小
                batch_size = self._calculate_optimal_batch_size(
                    len(urls), platform, priority
                )
            
            # 创建批次
            batches = []
            for i in range(0, len(urls), batch_size):
                batch_urls = urls[i:i + batch_size]
                
                batch = TaskBatch(
                    batch_id=self._generate_batch_id(i // batch_size, platform),
                    urls=batch_urls,
                    priority=priority,
                    platform=platform,
                    estimated_duration=self._estimate_batch_duration(
                        len(batch_urls), platform
                    ),
                    created_at=datetime.now(),
                    metadata={
                        **metadata,
                        "batch_index": i // batch_size,
                        "total_batches": math.ceil(len(urls) / batch_size),
                        "original_task_size": len(urls)
                    }
                )
                
                batches.append(batch)
            
            # 更新统计信息
            self._update_stats(batches, start_time)
            
            self.logger.info(
                f"Split {len(urls)} URLs into {len(batches)} batches "
                f"(avg size: {len(urls) / len(batches):.1f})"
            )
            
            return batches
            
        except Exception as e:
            self.logger.error(f"Failed to split URLs: {e}")
            raise
    
    def _calculate_optimal_batch_size(
        self,
        total_urls: int,
        platform: str,
        priority: TaskPriority
    ) -> int:
        """计算最优批次大小
        
        Args:
            total_urls: 总URL数量
            platform: 平台类型
            priority: 任务优先级
            
        Returns:
            int: 最优批次大小
        """
        base_size = self.config.max_batch_size
        
        # 根据优先级调整
        if priority == TaskPriority.URGENT:
            # 紧急任务使用较小批次以便快速开始
            base_size = min(base_size, 50)
        elif priority == TaskPriority.LOW:
            # 低优先级任务可以使用更大批次
            base_size = self.config.max_batch_size
        
        # 根据平台特性调整
        platform_factors = {
            "mercadolibre": 1.0,
            "amazon": 0.8,      # Amazon可能需要更小批次
            "ebay": 0.9,
        }
        
        platform_factor = platform_factors.get(platform, 1.0)
        adjusted_size = int(base_size * platform_factor)
        
        # 自适应调整
        if self.config.adaptive_sizing and self.performance_history:
            adjusted_size = self._adaptive_size_adjustment(
                adjusted_size, platform
            )
        
        # 确保在合理范围内
        return max(
            self.config.min_batch_size,
            min(adjusted_size, self.config.max_batch_size)
        )
    
    def _adaptive_size_adjustment(
        self,
        base_size: int,
        platform: str
    ) -> int:
        """基于历史性能的自适应大小调整
        
        Args:
            base_size: 基础批次大小
            platform: 平台类型
            
        Returns:
            int: 调整后的批次大小
        """
        # 获取最近的性能数据
        recent_performance = [
            p for p in self.performance_history[-10:]  # 最近10次
            if p.get("platform") == platform
        ]
        
        if not recent_performance:
            return base_size
        
        # 计算平均成功率和处理时间
        avg_success_rate = sum(
            p.get("success_rate", 1.0) for p in recent_performance
        ) / len(recent_performance)
        
        avg_duration = sum(
            p.get("avg_duration", 60.0) for p in recent_performance
        ) / len(recent_performance)
        
        # 根据成功率调整
        if avg_success_rate < 0.8:
            # 成功率低，减小批次
            adjustment_factor = 0.7
        elif avg_success_rate > 0.95:
            # 成功率高，可以增大批次
            adjustment_factor = 1.2
        else:
            adjustment_factor = 1.0
        
        # 根据处理时间调整
        if avg_duration > 300:  # 超过5分钟
            adjustment_factor *= 0.8
        elif avg_duration < 60:  # 少于1分钟
            adjustment_factor *= 1.1
        
        adjusted_size = int(base_size * adjustment_factor)
        return max(self.config.min_batch_size, min(adjusted_size, self.config.max_batch_size))
    
    def _estimate_batch_duration(
        self,
        batch_size: int,
        platform: str
    ) -> float:
        """估算批次处理时间
        
        Args:
            batch_size: 批次大小
            platform: 平台类型
            
        Returns:
            float: 预估处理时间（秒）
        """
        # 基础处理时间（每个URL）
        base_time_per_url = {
            "mercadolibre": 2.0,  # 2秒每URL
            "amazon": 3.0,        # 3秒每URL
            "ebay": 2.5,          # 2.5秒每URL
        }
        
        time_per_url = base_time_per_url.get(platform, 2.5)
        
        # 考虑批次开销
        batch_overhead = 10.0  # 10秒批次开销
        
        # 考虑网络延迟和重试
        network_factor = 1.3
        
        estimated_time = (batch_size * time_per_url + batch_overhead) * network_factor
        
        return estimated_time
    
    def _generate_batch_id(self, batch_index: int, platform: str) -> str:
        """生成批次ID
        
        Args:
            batch_index: 批次索引
            platform: 平台类型
            
        Returns:
            str: 批次ID
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"batch_{platform}_{timestamp}_{batch_index:04d}"
    
    def _update_stats(self, batches: List[TaskBatch], start_time: datetime) -> None:
        """更新统计信息
        
        Args:
            batches: 批次列表
            start_time: 开始时间
        """
        total_urls = sum(len(batch.urls) for batch in batches)
        
        self.stats["total_batches_created"] += len(batches)
        self.stats["total_urls_processed"] += total_urls
        self.stats["avg_batch_size"] = (
            self.stats["total_urls_processed"] / 
            self.stats["total_batches_created"]
        )
        self.stats["last_split_time"] = start_time
    
    def calculate_priority_score(
        self,
        priority: TaskPriority,
        created_at: datetime,
        url_count: int
    ) -> float:
        """计算任务优先级分数
        
        Args:
            priority: 任务优先级
            created_at: 创建时间
            url_count: URL数量
            
        Returns:
            float: 优先级分数（越高越优先）
        """
        # 基础优先级分数
        base_scores = {
            TaskPriority.LOW: 1.0,
            TaskPriority.NORMAL: 2.0,
            TaskPriority.HIGH: 4.0,
            TaskPriority.URGENT: 8.0
        }
        
        base_score = base_scores.get(priority, 2.0)
        
        # 时间因子（等待时间越长，优先级越高）
        wait_time = (datetime.now() - created_at).total_seconds()
        time_factor = 1.0 + (wait_time / 3600)  # 每小时增加1.0
        
        # 大小因子（较小的任务优先级稍高）
        size_factor = max(0.5, 2.0 - (url_count / 100))
        
        final_score = base_score * time_factor * size_factor
        
        return final_score
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.stats,
            "config": {
                "max_batch_size": self.config.max_batch_size,
                "max_concurrent_batches": self.config.max_concurrent_batches,
                "adaptive_sizing": self.config.adaptive_sizing
            }
        }
