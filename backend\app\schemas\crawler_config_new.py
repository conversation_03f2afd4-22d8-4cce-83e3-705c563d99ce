"""
新架构：爬取配置数据模型
分离后的纯爬取配置，不包含后端性能相关配置
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum

# 导入现有的配置模型
from ..api.v1.crawler_config import (
    BrowserConfig, CrawlerConfig as OriginalCrawlerConfig, LLMConfig, 
    SchemaExtractionConfig, ContentProcessingConfig, LinkFilteringConfig, MonitorConfig
)


class ConfigStatus(str, Enum):
    """配置状态枚举"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    DRAFT = "draft"            # 草稿
    ARCHIVED = "archived"      # 已归档


class CrawlerConfig(BaseModel):
    """爬取配置（与后端性能无关的纯爬取配置）"""
    
    # 基本信息
    config_id: str = Field(description="配置唯一标识")
    config_name: str = Field(description="配置名称", max_length=100)
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    
    # 爬取相关配置（从原有配置中分离出来）
    browser: BrowserConfig = Field(description="浏览器配置")
    crawler: OriginalCrawlerConfig = Field(description="爬虫配置")
    llm: LLMConfig = Field(description="LLM配置")
    schema_extraction: SchemaExtractionConfig = Field(description="模式提取配置")
    content_processing: ContentProcessingConfig = Field(description="内容处理配置")
    link_filtering: LinkFilteringConfig = Field(description="链接过滤配置")
    monitor: MonitorConfig = Field(description="监控配置")
    
    # 配置元数据
    status: ConfigStatus = Field(default=ConfigStatus.ACTIVE, description="配置状态")
    version: str = Field(default="1.0", description="配置版本")
    tags: List[str] = Field(default_factory=list, description="配置标签")
    
    # 使用统计
    usage_count: int = Field(default=0, ge=0, description="使用次数")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")
    
    # 时间戳
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者")
    updated_by: Optional[str] = Field(None, description="更新者")
    
    @validator('config_name')
    def validate_config_name(cls, v):
        """验证配置名称"""
        if not v or not v.strip():
            raise ValueError("配置名称不能为空")
        return v.strip()
    
    @validator('tags')
    def validate_tags(cls, v):
        """验证标签"""
        if v:
            # 去重并过滤空标签
            return list(set(tag.strip() for tag in v if tag and tag.strip()))
        return []
    
    @property
    def is_active(self) -> bool:
        """判断配置是否活跃"""
        return self.status == ConfigStatus.ACTIVE
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used = datetime.now()


class CrawlerConfigCreate(BaseModel):
    """创建爬取配置的数据模型"""
    
    config_name: str = Field(description="配置名称", max_length=100)
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    
    # 爬取配置
    browser: BrowserConfig = Field(description="浏览器配置")
    crawler: OriginalCrawlerConfig = Field(description="爬虫配置")
    llm: LLMConfig = Field(description="LLM配置")
    schema_extraction: SchemaExtractionConfig = Field(description="模式提取配置")
    content_processing: ContentProcessingConfig = Field(description="内容处理配置")
    link_filtering: LinkFilteringConfig = Field(description="链接过滤配置")
    monitor: MonitorConfig = Field(description="监控配置")
    
    # 可选元数据
    version: str = Field(default="1.0", description="配置版本")
    tags: List[str] = Field(default_factory=list, description="配置标签")
    created_by: Optional[str] = Field(None, description="创建者")


class CrawlerConfigUpdate(BaseModel):
    """更新爬取配置的数据模型"""
    
    config_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    
    # 爬取配置（可选更新）
    browser: Optional[BrowserConfig] = None
    crawler: Optional[OriginalCrawlerConfig] = None
    llm: Optional[LLMConfig] = None
    schema_extraction: Optional[SchemaExtractionConfig] = None
    content_processing: Optional[ContentProcessingConfig] = None
    link_filtering: Optional[LinkFilteringConfig] = None
    monitor: Optional[MonitorConfig] = None
    
    # 元数据更新
    status: Optional[ConfigStatus] = None
    version: Optional[str] = None
    tags: Optional[List[str]] = None
    updated_by: Optional[str] = None


class CrawlerConfigSummary(BaseModel):
    """爬取配置摘要（用于列表显示）"""
    
    config_id: str
    config_name: str
    description: Optional[str]
    status: ConfigStatus
    version: str
    tags: List[str]
    usage_count: int
    last_used: Optional[datetime]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    
    # 配置特征摘要
    browser_headless: bool
    llm_provider: str
    llm_model: str
    
    @classmethod
    def from_config(cls, config: CrawlerConfig) -> 'CrawlerConfigSummary':
        """从完整配置创建摘要"""
        return cls(
            config_id=config.config_id,
            config_name=config.config_name,
            description=config.description,
            status=config.status,
            version=config.version,
            tags=config.tags,
            usage_count=config.usage_count,
            last_used=config.last_used,
            created_at=config.created_at,
            updated_at=config.updated_at,
            browser_headless=config.browser.headless,
            llm_provider=getattr(config.llm, 'provider', config.llm.model.split('-')[0] if config.llm.model else 'unknown'),
            llm_model=config.llm.model
        )


class CrawlerConfigTemplate(BaseModel):
    """爬取配置模板"""
    
    template_id: str = Field(description="模板ID")
    template_name: str = Field(description="模板名称")
    description: str = Field(description="模板描述")
    category: str = Field(description="模板分类")
    
    # 模板配置
    browser: BrowserConfig
    crawler: OriginalCrawlerConfig
    llm: LLMConfig
    schema_extraction: SchemaExtractionConfig
    content_processing: ContentProcessingConfig
    link_filtering: LinkFilteringConfig
    monitor: MonitorConfig
    
    # 模板元数据
    is_builtin: bool = Field(default=False, description="是否内置模板")
    usage_count: int = Field(default=0, description="使用次数")
    created_at: Optional[datetime] = None
    
    def to_config_create(self, config_name: str, description: Optional[str] = None) -> CrawlerConfigCreate:
        """转换为配置创建数据"""
        return CrawlerConfigCreate(
            config_name=config_name,
            description=description or self.description,
            browser=self.browser,
            crawler=self.crawler,
            llm=self.llm,
            schema_extraction=self.schema_extraction,
            content_processing=self.content_processing,
            link_filtering=self.link_filtering,
            monitor=self.monitor
        )


class CrawlerConfigStats(BaseModel):
    """爬取配置统计信息"""
    
    total_configs: int = Field(description="总配置数")
    active_configs: int = Field(description="活跃配置数")
    inactive_configs: int = Field(description="非活跃配置数")
    draft_configs: int = Field(description="草稿配置数")
    archived_configs: int = Field(description="归档配置数")
    
    total_usage: int = Field(description="总使用次数")
    avg_usage_per_config: float = Field(description="平均每个配置使用次数")
    
    most_used_config: Optional[CrawlerConfigSummary] = Field(None, description="最常用配置")
    recently_created: List[CrawlerConfigSummary] = Field(description="最近创建的配置")
    recently_used: List[CrawlerConfigSummary] = Field(description="最近使用的配置")
    
    # 配置特征统计
    browser_stats: Dict[str, int] = Field(description="浏览器配置统计")
    llm_provider_stats: Dict[str, int] = Field(description="LLM提供商统计")
    llm_model_stats: Dict[str, int] = Field(description="LLM模型统计")


class CrawlerConfigValidationResult(BaseModel):
    """配置验证结果"""
    
    is_valid: bool = Field(description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")
    
    # 详细验证信息
    browser_validation: Dict[str, Any] = Field(default_factory=dict)
    crawler_validation: Dict[str, Any] = Field(default_factory=dict)
    llm_validation: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
