"""
爬取任务相关数据模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import String, Text, Integer, ForeignKey, DateTime, Boolean, CheckConstraint, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, IDMixin, TimestampMixin


class CrawlConfig(Base, IDMixin, TimestampMixin):
    """爬取配置表"""
    
    __tablename__ = "crawl_configs"
    
    # 基础配置
    name: Mapped[str] = mapped_column(String(200), nullable=False, unique=True, comment="配置名称")
    domain: Mapped[str] = mapped_column(String(100), nullable=False, comment="目标域名")
    
    # 爬取规则 (JSON格式存储)
    selectors: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, comment="CSS选择器配置")
    headers: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="HTTP请求头配置")
    
    # 反爬策略配置
    user_agents: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="用户代理列表")
    proxy_enabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否启用代理")
    delay_min: Mapped[int] = mapped_column(Integer, default=1000, comment="最小延迟(毫秒)")
    delay_max: Mapped[int] = mapped_column(Integer, default=3000, comment="最大延迟(毫秒)")
    
    # 重试配置
    max_retries: Mapped[int] = mapped_column(Integer, default=3, comment="最大重试次数")
    retry_delay: Mapped[int] = mapped_column(Integer, default=5000, comment="重试延迟(毫秒)")
    timeout_ms: Mapped[int] = mapped_column(Integer, default=30000, comment="超时时间(毫秒)")
    
    # 状态
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    # 关系
    tasks: Mapped[List["CrawlTask"]] = relationship(
        "CrawlTask",
        back_populates="config"
    )
    rules: Mapped[List["ScrapingRule"]] = relationship(
        "ScrapingRule",
        back_populates="config",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<CrawlConfig(id={self.id}, name='{self.name}', domain='{self.domain}')>"


class CrawlTask(Base, IDMixin, TimestampMixin):
    """爬取任务表"""
    
    __tablename__ = "crawl_tasks"
    __table_args__ = (
        CheckConstraint(
            "status IN ('pending', 'running', 'completed', 'failed', 'cancelled')",
            name="crawl_tasks_status_check"
        ),
        CheckConstraint(
            "progress >= 0 AND progress <= 100",
            name="crawl_tasks_progress_check"
        ),
    )
    
    # 基础信息
    name: Mapped[str] = mapped_column(String(200), nullable=False, comment="任务名称")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="任务描述")
    config_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("crawl_configs.id"),
        nullable=False,
        comment="爬取配置ID"
    )
    
    # 任务时间信息
    scheduled_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="计划执行时间"
    )
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="实际开始时间"
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    # 任务状态
    status: Mapped[str] = mapped_column(
        String(20),
        default="pending",
        comment="任务状态: pending, running, completed, failed, cancelled"
    )
    progress: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="进度百分比(0-100)"
    )
    
    # 统计信息
    total_products: Mapped[int] = mapped_column(Integer, default=0, comment="总商品数")
    success_count: Mapped[int] = mapped_column(Integer, default=0, comment="成功数量")
    failed_count: Mapped[int] = mapped_column(Integer, default=0, comment="失败数量")
    
    # 元数据
    created_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="创建者")
    
    # 关系
    config: Mapped[CrawlConfig] = relationship(
        "CrawlConfig",
        back_populates="tasks"
    )
    snapshots: Mapped[List["ProductSnapshot"]] = relationship(
        "ProductSnapshot",
        back_populates="task",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<CrawlTask(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    @property
    def duration(self) -> Optional[int]:
        """任务执行时长(秒)"""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return None
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self.status == "running"
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in ("completed", "failed", "cancelled")
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_products == 0:
            return 0.0
        return (self.success_count / self.total_products) * 100 