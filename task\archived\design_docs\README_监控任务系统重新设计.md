# 监控任务系统重新设计 - 项目文档索引

**项目名称**: MonIt监控任务系统工作流重构  
**项目状态**: 设计完成，准备实施  
**最后更新**: 2025年7月4日

## 📚 文档结构

### 核心设计文档
- **[监控任务系统工作流重新设计方案](./监控任务系统工作流重新设计方案_20250704.md)**
  - 完整的需求分析和系统设计方案
  - 技术架构和数据模型设计
  - API接口规范和前端页面架构
  - 性能指标和安全考虑

- **[监控任务系统实施计划](./监控任务系统实施计划_20250704.md)**
  - 详细的6阶段实施计划
  - 项目里程碑和交付标准
  - 风险管理和成功指标
  - 时间安排和资源分配

### 任务管理文档
- **[总体任务规划](../task/监控任务系统工作流重新设计_all_task.txt)**
  - 项目背景和目标
  - 系统架构重新设计
  - 数据模型和API设计
  - 实施计划概述

- **[第一阶段详细计划](../task/监控任务系统工作流重新设计_task_01.txt)**
  - Excel上传与URL解析功能实现
  - 技术实现方案和步骤
  - 验收标准和开发环境
  - 部署注意事项

### 历史文档
- **[项目状态报告](../task/监控任务系统重新设计_完成报告_20250703.md)**
  - 项目进展历史记录
  - 已完成和进行中的任务
  - 技术架构演进过程

## 🎯 项目概述

### 用户需求
用户期望的工作流程：
1. **Excel上传解析** → 前端上传，后端解析，返回URL列表
2. **URL池管理** → Redis持久化，支持筛选、多选、反选
3. **任务创建** → 从URL池选择链接创建监控任务
4. **任务管理** → 修改配置、管理链接、手动执行、终止

### 核心改进
- ✅ 解耦数据导入和任务创建流程
- ✅ 增加专门的URL池管理功能
- ✅ 重构任务创建流程，支持从URL池选择
- ✅ 完善定时调度和任务管理功能
- ✅ 优化数据存储和查询性能

## 🏗️ 技术架构

### 系统架构
```
Excel上传 → URL解析 → URL池(Redis) → 任务创建 → 定时调度(Celery) → 执行监控
```

### 技术栈
- **前端**: React 18 + TypeScript + Ant Design 5.x
- **后端**: FastAPI + Pydantic + Redis + Celery
- **数据**: Redis (主存储) + TimescaleDB (时序数据)
- **工具**: pandas + openpyxl (Excel处理)

### 核心组件
1. **Excel解析器** - 文件上传和URL提取
2. **URL池管理器** - URL生命周期管理
3. **任务调度器** - 基于Celery的定时任务
4. **任务执行器** - 监控任务执行引擎
5. **状态管理器** - 实时状态同步

## 📊 数据模型

### URL池数据结构
```typescript
interface UrlPoolItem {
  id: string;                    // 唯一标识
  url: string;                   // URL地址
  platform: string;              // 平台类型
  source_file: string;           // 来源Excel文件
  added_at: string;              // 添加时间
  status: 'active' | 'disabled'; // 状态
  // ... 更多字段
}
```

### 监控任务数据结构
```typescript
interface MonitoringTask {
  id: string;                    // 任务ID
  name: string;                  // 任务名称
  url_ids: string[];            // 关联的URL ID列表
  schedule: ScheduleConfig;      // 调度配置
  config: TaskConfig;           // 任务配置
  status: TaskStatus;           // 任务状态
  // ... 更多字段
}
```

## 🔧 API接口

### URL池管理
- `POST /api/v1/excel/upload-and-parse` - Excel上传解析
- `GET /api/v1/urls/pool` - 获取URL池列表
- `POST /api/v1/urls/pool/batch-update` - 批量更新URL
- `GET /api/v1/urls/pool/stats` - URL池统计

### 任务管理
- `POST /api/v1/monitoring-tasks/` - 创建监控任务
- `PUT /api/v1/monitoring-tasks/{id}/urls` - 更新任务URL
- `POST /api/v1/monitoring-tasks/{id}/execute` - 手动执行
- `POST /api/v1/monitoring-tasks/{id}/terminate` - 终止执行

## 🎯 前端页面

### 页面结构
```
/monitoring
├── /url-pool              # URL池管理页面
├── /tasks                 # 任务列表页面
├── /tasks/create          # 创建任务页面
├── /tasks/:id             # 任务详情页面
└── /tasks/:id/edit        # 编辑任务页面
```

### 核心组件
- **ExcelUpload** - Excel上传组件
- **UrlPool** - URL池管理组件
- **TaskManagement** - 任务管理组件
- **ScheduleConfig** - 调度配置组件

## 📝 实施计划

### 6个实施阶段
1. **Excel上传与URL解析** (7月4日-7月7日) - 基础数据导入
2. **URL池管理页面** (7月8日-7月12日) - 核心管理界面
3. **任务创建流程重构** (7月13日-7月16日) - 符合用户期望
4. **任务管理功能增强** (7月17日-7月21日) - 完整操作功能
5. **定时调度系统集成** (7月22日-7月27日) - Celery集成
6. **数据持久化优化** (7月28日-7月31日) - 性能优化

### 项目里程碑
- **M1** (7月7日): Excel上传解析系统
- **M2** (7月12日): URL池管理界面
- **M3** (7月16日): 任务创建流程
- **M4** (7月21日): 任务管理系统
- **M5** (7月27日): 定时调度系统
- **M6** (7月31日): 系统优化完成

## 📈 预期效果

### 用户体验
- ✅ 完全符合用户期望的工作流程
- ✅ 直观的URL池管理界面
- ✅ 灵活的任务创建和管理
- ✅ 实时的状态反馈

### 系统性能
- ✅ 高效的数据存储和查询
- ✅ 稳定的定时调度机制
- ✅ 可扩展的系统架构
- ✅ 完善的错误处理

### 功能完整性
- ✅ 完整的URL生命周期管理
- ✅ 灵活的任务配置选项
- ✅ 丰富的监控统计功能
- ✅ 便捷的批量操作支持

## 🚀 下一步行动

1. **立即开始**: 第一阶段 - Excel上传与URL解析功能实现
2. **准备工作**: 开发环境配置和依赖安装
3. **并行开发**: 前后端同步开发，API优先
4. **持续测试**: 每个阶段完成后进行集成测试
5. **用户反馈**: 及时收集用户反馈，迭代优化

## 📞 联系信息

**项目负责人**: AI Assistant  
**技术架构师**: AI Assistant  
**文档维护**: AI Assistant  
**最后更新**: 2025年7月4日

---

> 📌 **重要提醒**: 本项目文档会随着实施进展持续更新，请关注最新版本。如有疑问或建议，请及时反馈。
