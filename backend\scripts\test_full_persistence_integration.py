#!/usr/bin/env python3
"""
完整的持久化TaskManager集成测试

模拟真实场景：
1. 创建持久化TaskManager
2. 提交真实任务
3. 检查Redis持久化
4. 模拟重启恢复
5. 验证数据一致性
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class MockTaskManager:
    """模拟的持久化TaskManager
    
    简化版本，专注于测试持久化功能
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self.redis_client = None
        
        # Redis键名
        self.pending_queue_key = "taskmanager:pending_batches"
        self.running_hash_key = "taskmanager:running_batches"
        self.completed_set_key = "taskmanager:completed_batches"
        self.failed_set_key = "taskmanager:failed_batches"
        
        # 内存状态
        self.pending_batches = []
        self.running_batches = {}
        self.completed_batches = []
        self.failed_batches = []
        
        # 状态
        self.is_running = False
        self.start_time = None
    
    async def start(self):
        """启动TaskManager"""
        logger.info("🚀 Starting Mock TaskManager...")
        
        # 连接Redis
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        
        # 恢复状态
        await self._restore_state()
        
        self.is_running = True
        self.start_time = datetime.now()
        
        logger.info("✅ Mock TaskManager started")
    
    async def stop(self):
        """停止TaskManager"""
        logger.info("🛑 Stopping Mock TaskManager...")
        
        # 保存状态
        await self._save_state()
        
        self.is_running = False
        
        if self.redis_client:
            await self.redis_client.aclose()
        
        logger.info("✅ Mock TaskManager stopped")
    
    async def submit_task(self, task_id: int, urls: List[str], platform: str = "mercadolibre", priority: str = "normal"):
        """提交任务"""
        # 创建批次
        batch_id = f"batch_{task_id}_{int(time.time())}"
        
        batch_data = {
            "batch_id": batch_id,
            "urls": urls,
            "platform": platform,
            "priority": priority,
            "estimated_duration": len(urls) * 2.0,
            "created_at": datetime.now().isoformat(),
            "metadata": {
                "task_id": task_id,
                "submission_id": f"submission_{task_id}",
                "test": True
            }
        }
        
        # 添加到内存
        self.pending_batches.append(batch_data)
        
        # 持久化到Redis
        await self._persist_pending_batch(batch_data)
        
        logger.info(f"✅ Submitted task {task_id}: {batch_id} ({len(urls)} URLs)")
        return batch_id
    
    async def start_batch(self, batch_data: Dict[str, Any]):
        """开始执行批次"""
        batch_id = batch_data["batch_id"]
        
        # 从待处理队列移除
        self.pending_batches = [b for b in self.pending_batches if b["batch_id"] != batch_id]
        
        # 创建执行记录
        execution_data = {
            "batch_id": batch_id,
            "celery_task_id": f"celery_{batch_id}",
            "batch": batch_data,
            "started_at": datetime.now().isoformat(),
            "status": "running",
            "result": None,
            "error": None
        }
        
        # 添加到运行状态
        self.running_batches[batch_id] = execution_data
        
        # 持久化
        await self._remove_from_pending_queue(batch_data)
        await self._persist_running_batch(batch_id, execution_data)
        
        logger.info(f"✅ Started batch: {batch_id}")
    
    async def complete_batch(self, batch_id: str, success: bool = True):
        """完成批次"""
        if batch_id in self.running_batches:
            # 从运行状态移除
            del self.running_batches[batch_id]
            
            # 添加到完成或失败列表
            if success:
                self.completed_batches.append(batch_id)
                await self._persist_completed_batch(batch_id)
            else:
                self.failed_batches.append(batch_id)
                await self._persist_failed_batch(batch_id)
            
            # 从Redis运行哈希移除
            await self._remove_from_running_hash(batch_id)
            
            status = "completed" if success else "failed"
            logger.info(f"✅ Batch {status}: {batch_id}")
    
    async def _restore_state(self):
        """恢复状态"""
        logger.info("🔄 Restoring state from Redis...")
        
        try:
            # 恢复待处理队列
            pending_data = await self.redis_client.lrange(self.pending_queue_key, 0, -1)
            for data in pending_data:
                batch = json.loads(data)
                self.pending_batches.append(batch)
            
            # 恢复运行状态
            running_data = await self.redis_client.hgetall(self.running_hash_key)
            for batch_id, execution_data in running_data.items():
                batch_id = batch_id.decode() if isinstance(batch_id, bytes) else batch_id
                execution = json.loads(execution_data)
                self.running_batches[batch_id] = execution
            
            # 恢复完成列表
            completed_data = await self.redis_client.smembers(self.completed_set_key)
            self.completed_batches = [bid.decode() if isinstance(bid, bytes) else bid for bid in completed_data]
            
            # 恢复失败列表
            failed_data = await self.redis_client.smembers(self.failed_set_key)
            self.failed_batches = [bid.decode() if isinstance(bid, bytes) else bid for bid in failed_data]
            
            logger.info(f"✅ Restored: {len(self.pending_batches)} pending, {len(self.running_batches)} running, "
                       f"{len(self.completed_batches)} completed, {len(self.failed_batches)} failed")
        
        except Exception as e:
            logger.error(f"❌ Failed to restore state: {e}")
    
    async def _save_state(self):
        """保存状态"""
        logger.info("💾 Saving state to Redis...")
        
        try:
            # 保存所有当前状态到Redis
            await self._persist_all_pending_batches()
            await self._persist_all_running_batches()
            
            logger.info("✅ State saved successfully")
        
        except Exception as e:
            logger.error(f"❌ Failed to save state: {e}")
    
    async def _persist_pending_batch(self, batch_data: Dict[str, Any]):
        """持久化待处理批次"""
        await self.redis_client.lpush(self.pending_queue_key, json.dumps(batch_data))
    
    async def _persist_running_batch(self, batch_id: str, execution_data: Dict[str, Any]):
        """持久化运行批次"""
        await self.redis_client.hset(self.running_hash_key, batch_id, json.dumps(execution_data))
    
    async def _persist_completed_batch(self, batch_id: str):
        """持久化完成批次"""
        await self.redis_client.sadd(self.completed_set_key, batch_id)
    
    async def _persist_failed_batch(self, batch_id: str):
        """持久化失败批次"""
        await self.redis_client.sadd(self.failed_set_key, batch_id)
    
    async def _remove_from_pending_queue(self, batch_data: Dict[str, Any]):
        """从待处理队列移除"""
        await self.redis_client.lrem(self.pending_queue_key, 1, json.dumps(batch_data))
    
    async def _remove_from_running_hash(self, batch_id: str):
        """从运行哈希移除"""
        await self.redis_client.hdel(self.running_hash_key, batch_id)
    
    async def _persist_all_pending_batches(self):
        """持久化所有待处理批次"""
        # 清空现有队列
        await self.redis_client.delete(self.pending_queue_key)
        
        # 重新添加所有批次
        if self.pending_batches:
            batch_data_list = [json.dumps(batch) for batch in self.pending_batches]
            await self.redis_client.lpush(self.pending_queue_key, *batch_data_list)
    
    async def _persist_all_running_batches(self):
        """持久化所有运行批次"""
        # 清空现有哈希
        await self.redis_client.delete(self.running_hash_key)
        
        # 重新添加所有运行批次
        for batch_id, execution_data in self.running_batches.items():
            await self.redis_client.hset(self.running_hash_key, batch_id, json.dumps(execution_data))
    
    def get_memory_state(self):
        """获取内存状态"""
        return {
            "pending_count": len(self.pending_batches),
            "running_count": len(self.running_batches),
            "completed_count": len(self.completed_batches),
            "failed_count": len(self.failed_batches),
            "pending_ids": [b["batch_id"] for b in self.pending_batches],
            "running_ids": list(self.running_batches.keys()),
            "completed_ids": self.completed_batches,
            "failed_ids": self.failed_batches
        }


async def test_full_persistence_integration():
    """完整的持久化集成测试"""
    
    logger.info("🧪 Full Persistence Integration Test")
    logger.info("=" * 60)
    
    # 清理测试环境
    redis_client = redis.from_url("redis://localhost:6379/0")
    await redis_client.ping()
    
    test_keys = [
        "taskmanager:pending_batches",
        "taskmanager:running_batches",
        "taskmanager:completed_batches",
        "taskmanager:failed_batches"
    ]
    
    for key in test_keys:
        await redis_client.delete(key)
    
    await redis_client.aclose()
    
    logger.info("✅ Test environment cleaned")
    
    # 阶段1: 创建TaskManager并提交任务
    logger.info("\n📤 Phase 1: Task Submission")
    logger.info("-" * 40)
    
    task_manager = MockTaskManager()
    await task_manager.start()
    
    # 提交多个任务
    test_tasks = [
        {"task_id": 1, "urls": [f"https://example.com/product/{i}" for i in range(1, 11)], "priority": "urgent"},
        {"task_id": 2, "urls": [f"https://example.com/product/{i}" for i in range(11, 21)], "priority": "normal"},
        {"task_id": 3, "urls": [f"https://example.com/product/{i}" for i in range(21, 31)], "priority": "low"}
    ]
    
    submitted_batch_ids = []
    for task in test_tasks:
        batch_id = await task_manager.submit_task(**task)
        submitted_batch_ids.append(batch_id)
    
    # 获取提交后的状态
    memory_state_1 = task_manager.get_memory_state()
    logger.info(f"📊 After submission: {memory_state_1['pending_count']} pending")
    
    # 阶段2: 开始执行一些任务
    logger.info("\n⚙️ Phase 2: Task Execution")
    logger.info("-" * 40)
    
    # 开始执行前2个批次
    for i in range(2):
        if task_manager.pending_batches:
            batch_data = task_manager.pending_batches[0]
            await task_manager.start_batch(batch_data)
    
    # 完成第1个批次
    if task_manager.running_batches:
        first_running = list(task_manager.running_batches.keys())[0]
        await task_manager.complete_batch(first_running, success=True)
    
    # 获取执行后的状态
    memory_state_2 = task_manager.get_memory_state()
    logger.info(f"📊 After execution: {memory_state_2['pending_count']} pending, "
               f"{memory_state_2['running_count']} running, {memory_state_2['completed_count']} completed")
    
    # 阶段3: 检查Redis持久化
    logger.info("\n💾 Phase 3: Redis Persistence Check")
    logger.info("-" * 40)
    
    redis_client = redis.from_url("redis://localhost:6379/0")
    await redis_client.ping()
    
    redis_state = {
        "pending_count": await redis_client.llen("taskmanager:pending_batches"),
        "running_count": await redis_client.hlen("taskmanager:running_batches"),
        "completed_count": await redis_client.scard("taskmanager:completed_batches"),
        "failed_count": await redis_client.scard("taskmanager:failed_batches")
    }
    
    logger.info(f"📊 Redis state: {redis_state}")
    
    # 验证内存和Redis一致性
    consistency_check = {
        "pending_match": memory_state_2["pending_count"] == redis_state["pending_count"],
        "running_match": memory_state_2["running_count"] == redis_state["running_count"],
        "completed_match": memory_state_2["completed_count"] == redis_state["completed_count"],
        "failed_match": memory_state_2["failed_count"] == redis_state["failed_count"]
    }
    
    logger.info(f"🔍 Consistency check: {consistency_check}")
    
    await redis_client.aclose()
    
    # 阶段4: 模拟重启
    logger.info("\n🔄 Phase 4: Restart Simulation")
    logger.info("-" * 40)
    
    # 停止当前TaskManager
    await task_manager.stop()
    logger.info("🛑 TaskManager stopped (simulating restart)")
    
    # 等待一段时间
    await asyncio.sleep(2)
    
    # 创建新的TaskManager实例
    new_task_manager = MockTaskManager()
    await new_task_manager.start()
    logger.info("🚀 New TaskManager started")
    
    # 获取恢复后的状态
    memory_state_3 = new_task_manager.get_memory_state()
    logger.info(f"📊 After restart: {memory_state_3['pending_count']} pending, "
               f"{memory_state_3['running_count']} running, {memory_state_3['completed_count']} completed")
    
    # 阶段5: 验证恢复一致性
    logger.info("\n✅ Phase 5: Recovery Verification")
    logger.info("-" * 40)
    
    recovery_check = {
        "pending_recovered": memory_state_2["pending_count"] == memory_state_3["pending_count"],
        "running_recovered": memory_state_2["running_count"] == memory_state_3["running_count"],
        "completed_recovered": memory_state_2["completed_count"] == memory_state_3["completed_count"],
        "failed_recovered": memory_state_2["failed_count"] == memory_state_3["failed_count"]
    }
    
    logger.info(f"🔍 Recovery check: {recovery_check}")
    
    # 详细对比
    logger.info("\n📋 Detailed State Comparison:")
    logger.info(f"Before restart: {memory_state_2}")
    logger.info(f"After restart:  {memory_state_3}")
    
    # 继续处理恢复的任务
    logger.info("\n🔄 Phase 6: Continue Processing")
    logger.info("-" * 40)
    
    # 完成剩余的运行任务
    for batch_id in list(new_task_manager.running_batches.keys()):
        await new_task_manager.complete_batch(batch_id, success=True)
    
    # 开始处理待处理任务
    while new_task_manager.pending_batches:
        batch_data = new_task_manager.pending_batches[0]
        await new_task_manager.start_batch(batch_data)
        
        # 立即完成
        batch_id = batch_data["batch_id"]
        await new_task_manager.complete_batch(batch_id, success=True)
    
    # 最终状态
    final_state = new_task_manager.get_memory_state()
    logger.info(f"📊 Final state: {final_state['pending_count']} pending, "
               f"{final_state['running_count']} running, {final_state['completed_count']} completed")
    
    # 清理
    await new_task_manager.stop()
    
    # 测试结果
    all_consistency_passed = all(consistency_check.values())
    all_recovery_passed = all(recovery_check.values())
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 INTEGRATION TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Consistency Check: {'✅ PASS' if all_consistency_passed else '❌ FAIL'}")
    logger.info(f"Recovery Check: {'✅ PASS' if all_recovery_passed else '❌ FAIL'}")
    
    if all_consistency_passed and all_recovery_passed:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("✅ Persistence works correctly")
        logger.info("✅ Recovery works correctly")
        logger.info("✅ Data consistency maintained")
        logger.info("✅ Ready for production!")
    else:
        logger.info("\n⚠️ Some tests failed")
        logger.info(f"Consistency issues: {[k for k, v in consistency_check.items() if not v]}")
        logger.info(f"Recovery issues: {[k for k, v in recovery_check.items() if not v]}")
    
    return all_consistency_passed and all_recovery_passed


async def main():
    """主函数"""
    print("🧪 Full Persistence Integration Test")
    print("=" * 60)
    
    try:
        success = await test_full_persistence_integration()
        
        if success:
            print("\n🎉 Integration test completed successfully!")
        else:
            print("\n❌ Integration test failed!")
        
        return success
    
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    asyncio.run(main())
