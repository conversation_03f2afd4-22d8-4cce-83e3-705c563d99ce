import React, { useState } from 'react';
import {
  Upload,
  Button,
  Progress,
  Alert,
  Card,
  Space,
  Typography,
  Select,
  Checkbox,
  Divider,
  Statistic,
  Row,
  Col,
  message,
  Modal
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { uploadAndParseExcel, getSupportedPlatforms } from '../../services/urlPoolApi';
import type { ExcelUploadResponse, UrlPoolItem } from '../../types/urlPool';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface ExcelUploadProps {
  onUploadSuccess?: (response: ExcelUploadResponse) => void;
  onUploadError?: (error: string) => void;
}

interface UploadState {
  uploading: boolean;
  progress: number;
  result: ExcelUploadResponse | null;
  error: string | null;
}

const ExcelUpload: React.FC<ExcelUploadProps> = ({
  onUploadSuccess,
  onUploadError
}) => {
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    progress: 0,
    result: null,
    error: null
  });
  
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [autoDetect, setAutoDetect] = useState<boolean>(true);
  const [supportedPlatforms, setSupportedPlatforms] = useState<string[]>([]);
  const [showResultModal, setShowResultModal] = useState<boolean>(false);

  // 获取支持的平台列表
  React.useEffect(() => {
    const fetchPlatforms = async () => {
      try {
        const response = await getSupportedPlatforms();
        setSupportedPlatforms(response.platforms);
      } catch (error) {
        console.error('获取平台列表失败:', error);
      }
    };
    fetchPlatforms();
  }, []);

  const handleUpload = async (file: File) => {
    setUploadState({
      uploading: true,
      progress: 0,
      result: null,
      error: null
    });

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90)
        }));
      }, 200);

      // 创建FormData
      const formData = new FormData();
      formData.append('file', file);

      // 构建查询参数
      const params = new URLSearchParams();
      if (platformFilter !== 'all') {
        params.append('platform_filter', platformFilter);
      }
      params.append('auto_detect', autoDetect.toString());

      // 上传文件
      const response = await uploadAndParseExcel(formData, params.toString());

      clearInterval(progressInterval);
      
      setUploadState({
        uploading: false,
        progress: 100,
        result: response,
        error: null
      });

      message.success(`成功处理 ${response.total_urls} 个URL，新增 ${response.new_urls} 个到URL池`);
      
      // 显示结果详情
      setShowResultModal(true);
      
      // 调用成功回调
      onUploadSuccess?.(response);

    } catch (error: any) {
      setUploadState({
        uploading: false,
        progress: 0,
        result: null,
        error: error.message || '上传失败'
      });

      message.error(`上传失败: ${error.message}`);
      onUploadError?.(error.message);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls',
    beforeUpload: (file) => {
      // 验证文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.endsWith('.xlsx') ||
                     file.name.endsWith('.xls');
      
      if (!isExcel) {
        message.error('只能上传 Excel 文件（.xlsx 或 .xls）');
        return false;
      }

      // 移除文件大小限制
      // 注释：已取消文件大小限制，支持大文件上传

      // 开始上传
      handleUpload(file);
      return false; // 阻止默认上传行为
    },
    showUploadList: false
  };

  const renderUploadArea = () => (
    <Upload.Dragger {...uploadProps} disabled={uploadState.uploading}>
      <p className="ant-upload-drag-icon">
        <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />
      </p>
      <p className="ant-upload-text">
        点击或拖拽 Excel 文件到此区域上传
      </p>
      <p className="ant-upload-hint">
        支持 .xlsx 和 .xls 格式，无文件大小限制
      </p>
    </Upload.Dragger>
  );

  const renderUploadOptions = () => (
    <Card size="small" title="上传选项" style={{ marginBottom: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>平台筛选：</Text>
          <Select
            value={platformFilter}
            onChange={setPlatformFilter}
            style={{ width: 200, marginLeft: 8 }}
            disabled={uploadState.uploading}
          >
            <Option value="all">所有平台</Option>
            {supportedPlatforms.map(platform => (
              <Option key={platform} value={platform}>
                {platform.charAt(0).toUpperCase() + platform.slice(1)}
              </Option>
            ))}
          </Select>
        </div>
        
        <div>
          <Checkbox
            checked={autoDetect}
            onChange={(e) => setAutoDetect(e.target.checked)}
            disabled={uploadState.uploading}
          >
            自动检测平台类型
          </Checkbox>
        </div>
      </Space>
    </Card>
  );

  const renderProgress = () => {
    if (!uploadState.uploading && uploadState.progress === 0) return null;

    return (
      <Card size="small" style={{ marginTop: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>上传进度</Text>
          <Progress
            percent={uploadState.progress}
            status={uploadState.uploading ? 'active' : 'success'}
            showInfo
          />
          {uploadState.uploading && (
            <Text type="secondary">正在解析Excel文件...</Text>
          )}
        </Space>
      </Card>
    );
  };

  const renderResult = () => {
    if (uploadState.error) {
      return (
        <Alert
          message="上传失败"
          description={uploadState.error}
          type="error"
          showIcon
          style={{ marginTop: 16 }}
        />
      );
    }

    if (!uploadState.result) return null;

    const { result } = uploadState;

    return (
      <Card 
        size="small" 
        title={
          <Space>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            上传成功
          </Space>
        }
        style={{ marginTop: 16 }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic title="总URL数" value={result.total_urls} />
          </Col>
          <Col span={6}>
            <Statistic 
              title="新增URL" 
              value={result.new_urls} 
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="重复URL" 
              value={result.duplicate_urls}
              valueStyle={{ color: '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="处理时间" 
              value={result.processing_time.toFixed(2)} 
              suffix="秒"
            />
          </Col>
        </Row>
        
        <Divider />
        
        <Space>
          <Button 
            type="primary" 
            onClick={() => setShowResultModal(true)}
          >
            查看详情
          </Button>
          <Button 
            onClick={() => {
              setUploadState({
                uploading: false,
                progress: 0,
                result: null,
                error: null
              });
            }}
          >
            重新上传
          </Button>
        </Space>
      </Card>
    );
  };

  const renderResultModal = () => (
    <Modal
      title="上传结果详情"
      open={showResultModal}
      onCancel={() => setShowResultModal(false)}
      footer={[
        <Button key="close" onClick={() => setShowResultModal(false)}>
          关闭
        </Button>
      ]}
      width={800}
    >
      {uploadState.result && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message={uploadState.result.message}
            type="success"
            showIcon
          />
          
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small">
                <Statistic 
                  title="总URL数量" 
                  value={uploadState.result.total_urls}
                  prefix={<InfoCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Statistic 
                  title="新增到URL池" 
                  value={uploadState.result.new_urls}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small">
                <Statistic 
                  title="重复URL" 
                  value={uploadState.result.duplicate_urls}
                  prefix={<ExclamationCircleOutlined />}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Statistic 
                  title="处理时间" 
                  value={uploadState.result.processing_time.toFixed(2)}
                  suffix="秒"
                />
              </Card>
            </Col>
          </Row>
          
          {uploadState.result.urls.length > 0 && (
            <Card size="small" title="URL样本预览（前5个）">
              <Space direction="vertical" style={{ width: '100%' }}>
                {uploadState.result.urls.slice(0, 5).map((url, index) => (
                  <div key={index} style={{ padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                    <Text strong>{url.platform.toUpperCase()}</Text>
                    <br />
                    <Text code style={{ fontSize: 12 }}>{url.url}</Text>
                  </div>
                ))}
                {uploadState.result.urls.length > 5 && (
                  <Text type="secondary">
                    ... 还有 {uploadState.result.urls.length - 5} 个URL
                  </Text>
                )}
              </Space>
            </Card>
          )}
        </Space>
      )}
    </Modal>
  );

  return (
    <div>
      <Card>
        <Title level={4}>Excel文件上传</Title>
        <Paragraph type="secondary">
          上传包含URL的Excel文件，系统将自动解析并添加到URL池中。
          支持自动识别URL列和平台类型。
        </Paragraph>
        
        {renderUploadOptions()}
        {renderUploadArea()}
        {renderProgress()}
        {renderResult()}
      </Card>
      
      {renderResultModal()}
    </div>
  );
};

export default ExcelUpload;
