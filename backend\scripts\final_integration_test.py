#!/usr/bin/env python3
"""
最终集成测试

验证整个PersistentTaskManager系统的完整功能
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class FinalIntegrationTest:
    """最终集成测试"""
    
    def __init__(self):
        self.redis_url = "redis://localhost:6379/0"
        self.redis_client = None
        
        # TaskManager队列键
        self.keys = {
            "pending": "taskmanager:pending_batches",
            "running": "taskmanager:running_batches",
            "completed": "taskmanager:completed_batches",
            "failed": "taskmanager:failed_batches",
            "state": "taskmanager:state"
        }
        
        # 测试结果
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up final integration test...")
        
        try:
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info("✅ Connected to Redis")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to setup: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        if self.redis_client:
            await self.redis_client.aclose()
    
    async def test_redis_persistence_structure(self):
        """测试Redis持久化结构"""
        logger.info("🔍 Testing Redis persistence structure...")
        
        try:
            # 检查所有TaskManager键是否存在
            key_status = {}
            for name, key in self.keys.items():
                exists = await self.redis_client.exists(key)
                key_type = await self.redis_client.type(key) if exists else None
                
                if key_type:
                    key_type = key_type.decode() if isinstance(key_type, bytes) else key_type
                
                key_status[name] = {
                    "exists": bool(exists),
                    "type": key_type,
                    "key": key
                }
            
            # 检查数据结构
            structure_valid = True
            
            # 检查待处理队列（应该是list）
            if key_status["pending"]["exists"]:
                if key_status["pending"]["type"] != "list":
                    structure_valid = False
                    logger.error(f"❌ Pending queue should be list, got {key_status['pending']['type']}")
                else:
                    count = await self.redis_client.llen(self.keys["pending"])
                    logger.info(f"✅ Pending queue (list): {count} items")
            
            # 检查运行哈希（应该是hash）
            if key_status["running"]["exists"]:
                if key_status["running"]["type"] != "hash":
                    structure_valid = False
                    logger.error(f"❌ Running hash should be hash, got {key_status['running']['type']}")
                else:
                    count = await self.redis_client.hlen(self.keys["running"])
                    logger.info(f"✅ Running hash (hash): {count} items")
            
            # 检查完成集合（应该是set）
            if key_status["completed"]["exists"]:
                if key_status["completed"]["type"] != "set":
                    structure_valid = False
                    logger.error(f"❌ Completed set should be set, got {key_status['completed']['type']}")
                else:
                    count = await self.redis_client.scard(self.keys["completed"])
                    logger.info(f"✅ Completed set (set): {count} items")
            
            # 检查失败集合（应该是set）
            if key_status["failed"]["exists"]:
                if key_status["failed"]["type"] != "set":
                    structure_valid = False
                    logger.error(f"❌ Failed set should be set, got {key_status['failed']['type']}")
                else:
                    count = await self.redis_client.scard(self.keys["failed"])
                    logger.info(f"✅ Failed set (set): {count} items")
            
            self.test_results["redis_structure"] = {
                "success": structure_valid,
                "key_status": key_status
            }
            
            return structure_valid
            
        except Exception as e:
            logger.error(f"❌ Redis structure test failed: {e}")
            self.test_results["redis_structure"] = {"success": False, "error": str(e)}
            return False
    
    async def test_data_format_validation(self):
        """测试数据格式验证"""
        logger.info("🔍 Testing data format validation...")
        
        try:
            format_valid = True
            
            # 检查待处理队列中的数据格式
            pending_sample = await self.redis_client.lrange(self.keys["pending"], 0, 2)
            if pending_sample:
                for i, sample in enumerate(pending_sample):
                    try:
                        data = json.loads(sample)
                        required_fields = ["batch_id", "urls", "platform", "priority", "created_at", "metadata"]
                        
                        for field in required_fields:
                            if field not in data:
                                format_valid = False
                                logger.error(f"❌ Pending batch {i} missing field: {field}")
                        
                        # 检查数据类型
                        if not isinstance(data.get("urls"), list):
                            format_valid = False
                            logger.error(f"❌ Pending batch {i} urls should be list")
                        
                        if not isinstance(data.get("metadata"), dict):
                            format_valid = False
                            logger.error(f"❌ Pending batch {i} metadata should be dict")
                        
                        if format_valid:
                            logger.info(f"✅ Pending batch {i} format valid: {data['batch_id']}")
                    
                    except json.JSONDecodeError as e:
                        format_valid = False
                        logger.error(f"❌ Pending batch {i} invalid JSON: {e}")
            
            # 检查运行哈希中的数据格式
            running_sample = await self.redis_client.hgetall(self.keys["running"])
            if running_sample:
                for batch_id, execution_data in list(running_sample.items())[:3]:
                    try:
                        batch_id = batch_id.decode() if isinstance(batch_id, bytes) else batch_id
                        data = json.loads(execution_data)
                        
                        required_fields = ["batch_id", "celery_task_id", "batch", "started_at", "status"]
                        
                        for field in required_fields:
                            if field not in data:
                                format_valid = False
                                logger.error(f"❌ Running batch {batch_id} missing field: {field}")
                        
                        # 检查嵌套批次数据
                        if "batch" in data and isinstance(data["batch"], dict):
                            nested_batch = data["batch"]
                            if "batch_id" not in nested_batch:
                                format_valid = False
                                logger.error(f"❌ Running batch {batch_id} nested batch missing batch_id")
                        
                        if format_valid:
                            logger.info(f"✅ Running batch format valid: {batch_id}")
                    
                    except json.JSONDecodeError as e:
                        format_valid = False
                        logger.error(f"❌ Running batch {batch_id} invalid JSON: {e}")
            
            self.test_results["data_format"] = {"success": format_valid}
            return format_valid
            
        except Exception as e:
            logger.error(f"❌ Data format test failed: {e}")
            self.test_results["data_format"] = {"success": False, "error": str(e)}
            return False
    
    async def test_system_performance(self):
        """测试系统性能"""
        logger.info("🚀 Testing system performance...")
        
        try:
            # 收集Redis性能指标
            info = await self.redis_client.info()
            
            performance_metrics = {
                "memory_used": info.get("used_memory", 0),
                "memory_used_human": info.get("used_memory_human", "0B"),
                "connected_clients": info.get("connected_clients", 0),
                "ops_per_sec": info.get("instantaneous_ops_per_sec", 0),
                "total_commands": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "mem_fragmentation_ratio": info.get("mem_fragmentation_ratio", 0)
            }
            
            # 计算命中率
            hits = performance_metrics["keyspace_hits"]
            misses = performance_metrics["keyspace_misses"]
            total_requests = hits + misses
            
            if total_requests > 0:
                hit_rate = hits / total_requests
                performance_metrics["hit_rate"] = hit_rate
            else:
                performance_metrics["hit_rate"] = 0
            
            # 性能评估
            performance_good = True
            
            # 检查内存碎片率
            if performance_metrics["mem_fragmentation_ratio"] > 2.0:
                performance_good = False
                logger.warning(f"⚠️ High memory fragmentation: {performance_metrics['mem_fragmentation_ratio']:.2f}")
            else:
                logger.info(f"✅ Memory fragmentation OK: {performance_metrics['mem_fragmentation_ratio']:.2f}")
            
            # 检查命中率
            if performance_metrics["hit_rate"] < 0.8 and total_requests > 100:
                performance_good = False
                logger.warning(f"⚠️ Low hit rate: {performance_metrics['hit_rate']:.2%}")
            else:
                logger.info(f"✅ Hit rate OK: {performance_metrics['hit_rate']:.2%}")
            
            # 检查连接数
            if performance_metrics["connected_clients"] > 100:
                logger.warning(f"⚠️ High client connections: {performance_metrics['connected_clients']}")
            else:
                logger.info(f"✅ Client connections OK: {performance_metrics['connected_clients']}")
            
            logger.info(f"📊 Performance summary:")
            logger.info(f"   Memory used: {performance_metrics['memory_used_human']}")
            logger.info(f"   Operations/sec: {performance_metrics['ops_per_sec']}")
            logger.info(f"   Total commands: {performance_metrics['total_commands']}")
            logger.info(f"   Hit rate: {performance_metrics['hit_rate']:.2%}")
            
            self.test_results["performance"] = {
                "success": performance_good,
                "metrics": performance_metrics
            }
            
            return performance_good
            
        except Exception as e:
            logger.error(f"❌ Performance test failed: {e}")
            self.test_results["performance"] = {"success": False, "error": str(e)}
            return False
    
    async def test_queue_statistics(self):
        """测试队列统计"""
        logger.info("📊 Testing queue statistics...")
        
        try:
            # 收集队列统计
            stats = {}
            
            # 待处理队列
            stats["pending"] = await self.redis_client.llen(self.keys["pending"])
            
            # 运行哈希
            stats["running"] = await self.redis_client.hlen(self.keys["running"])
            
            # 完成集合
            stats["completed"] = await self.redis_client.scard(self.keys["completed"])
            
            # 失败集合
            stats["failed"] = await self.redis_client.scard(self.keys["failed"])
            
            # 总计
            stats["total"] = stats["pending"] + stats["running"] + stats["completed"] + stats["failed"]
            
            # 计算比例
            if stats["total"] > 0:
                stats["success_rate"] = stats["completed"] / (stats["completed"] + stats["failed"]) if (stats["completed"] + stats["failed"]) > 0 else 0
                stats["completion_rate"] = (stats["completed"] + stats["failed"]) / stats["total"]
            else:
                stats["success_rate"] = 0
                stats["completion_rate"] = 0
            
            logger.info(f"📊 Queue statistics:")
            logger.info(f"   Pending: {stats['pending']}")
            logger.info(f"   Running: {stats['running']}")
            logger.info(f"   Completed: {stats['completed']}")
            logger.info(f"   Failed: {stats['failed']}")
            logger.info(f"   Total: {stats['total']}")
            logger.info(f"   Success rate: {stats['success_rate']:.2%}")
            logger.info(f"   Completion rate: {stats['completion_rate']:.2%}")
            
            self.test_results["queue_stats"] = {
                "success": True,
                "stats": stats
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Queue statistics test failed: {e}")
            self.test_results["queue_stats"] = {"success": False, "error": str(e)}
            return False
    
    def generate_final_report(self):
        """生成最终报告"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 FINAL INTEGRATION TEST REPORT")
        logger.info("=" * 60)
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        logger.info(f"\n📋 Test Summary:")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Passed: {passed_tests}")
        logger.info(f"   Failed: {total_tests - passed_tests}")
        logger.info(f"   Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        # 详细结果
        logger.info(f"\n📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            logger.info(f"   {status} {test_name}")
            
            if not result.get("success", False) and "error" in result:
                logger.info(f"     Error: {result['error']}")
        
        # 系统状态总结
        if "queue_stats" in self.test_results and self.test_results["queue_stats"]["success"]:
            stats = self.test_results["queue_stats"]["stats"]
            logger.info(f"\n📊 System Status:")
            logger.info(f"   Total Tasks: {stats['total']}")
            logger.info(f"   Active Tasks: {stats['pending'] + stats['running']}")
            logger.info(f"   Completed Tasks: {stats['completed']}")
            logger.info(f"   Success Rate: {stats['success_rate']:.2%}")
        
        # 性能总结
        if "performance" in self.test_results and self.test_results["performance"]["success"]:
            metrics = self.test_results["performance"]["metrics"]
            logger.info(f"\n⚡ Performance Status:")
            logger.info(f"   Memory Usage: {metrics['memory_used_human']}")
            logger.info(f"   Operations/sec: {metrics['ops_per_sec']}")
            logger.info(f"   Cache Hit Rate: {metrics['hit_rate']:.2%}")
        
        # 最终结论
        all_passed = passed_tests == total_tests
        
        logger.info(f"\n🎯 Final Conclusion:")
        if all_passed:
            logger.info("   ✅ ALL TESTS PASSED!")
            logger.info("   🎉 PersistentTaskManager is ready for production!")
            logger.info("   💪 System is stable, performant, and reliable!")
        else:
            logger.info("   ⚠️ Some tests failed.")
            logger.info("   🔧 Please review and fix the issues before production deployment.")
        
        logger.info("\n" + "=" * 60)
        
        return all_passed
    
    async def run_final_test(self):
        """运行最终测试"""
        logger.info("🧪 Starting Final Integration Test")
        logger.info("=" * 60)
        
        try:
            # 运行所有测试
            test1 = await self.test_redis_persistence_structure()
            test2 = await self.test_data_format_validation()
            test3 = await self.test_system_performance()
            test4 = await self.test_queue_statistics()
            
            # 生成最终报告
            all_passed = self.generate_final_report()
            
            return all_passed
            
        except Exception as e:
            logger.error(f"❌ Final test failed: {e}")
            return False


async def main():
    """主函数"""
    print("🧪 PersistentTaskManager Final Integration Test")
    print("=" * 60)
    
    test = FinalIntegrationTest()
    
    try:
        if not await test.setup():
            sys.exit(1)
        
        success = await test.run_final_test()
        
        if success:
            logger.info("🎉 Final integration test completed successfully!")
        else:
            logger.error("❌ Final integration test failed!")
            sys.exit(1)
    
    finally:
        await test.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
