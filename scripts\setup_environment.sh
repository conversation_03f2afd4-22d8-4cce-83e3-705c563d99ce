#!/bin/bash

# MonIt项目环境设置脚本 (Linux/Mac)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "MonIt 电商爬虫系统 - 环境设置"
echo "========================================"

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    log_error "未找到conda命令，请先安装Miniconda或Anaconda"
    exit 1
fi

log_info "1. 检查conda环境..."
if conda env list | grep -q "monit"; then
    log_warn "MonIt环境已存在，是否重新创建？ [y/N]"
    read -r recreate
    if [[ $recreate =~ ^[Yy]$ ]]; then
        log_info "删除现有环境..."
        conda env remove -n monit -y
    else
        log_info "使用现有环境"
        conda activate monit
        exit 0
    fi
fi

log_info "2. 创建conda环境 (Python 3.11)..."
conda create -n monit python=3.11 -y

log_info "3. 激活环境..."
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate monit

log_info "4. 升级pip..."
python -m pip install --upgrade pip

log_info "5. 安装项目依赖..."
pip install -r requirements.txt

log_info "6. 创建必要目录..."
mkdir -p logs data temp

log_info "7. 检查安装结果..."
echo "检查Python版本:"
python --version

echo "检查关键包:"
python -c "import pandas; print(f'pandas: {pandas.__version__}')"
python -c "import redis; print(f'redis: {redis.__version__}')"
python -c "import celery; print(f'celery: {celery.__version__}')"
python -c "import fastapi; print(f'fastapi: {fastapi.__version__}')"

echo ""
echo "========================================"
log_info "环境设置完成！"
echo "========================================"
echo ""
echo "使用方法:"
echo "1. 激活环境: conda activate monit"
echo "2. 运行测试: python backend/scripts/quick_test.py"
echo "3. 启动服务: python backend/scripts/start_task_manager.py"
echo ""
echo "注意: 请确保Redis服务正在运行"
echo "启动Redis: redis-server"
echo ""

# 创建激活脚本
cat > activate_monit.sh << 'EOF'
#!/bin/bash
# MonIt环境激活脚本
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate monit
echo "MonIt环境已激活 (Python $(python --version))"
EOF

chmod +x activate_monit.sh
log_info "创建了激活脚本: ./activate_monit.sh"
