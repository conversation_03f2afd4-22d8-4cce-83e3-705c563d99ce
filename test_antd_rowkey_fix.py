#!/usr/bin/env python3
"""
测试Ant Design Table rowKey废弃警告修复
验证新的复合key方案是否正常工作
"""

import requests
import json

def test_live_stream_data_structure():
    """测试实时任务流数据结构和key唯一性"""
    print("🔍 测试实时任务流数据结构...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/celery/tasks/live-stream", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            executing_tasks = data.get('executing_tasks', [])
            
            print(f"✅ API响应正常")
            print(f"   执行中任务数量: {len(executing_tasks)}")
            
            if executing_tasks:
                print("\n📋 新的复合Key方案测试:")
                
                # 生成新的复合key并检查唯一性
                composite_keys = []
                
                for i, task in enumerate(executing_tasks):
                    # 模拟前端的新key生成逻辑
                    task_id = task.get('id', '')
                    current_url = task.get('current_url', 'no-url')
                    worker = task.get('worker', 'no-worker')
                    started_at = task.get('started_at', str(1000000000 + i))  # 模拟时间戳
                    
                    composite_key = f"{task_id}_{current_url}_{worker}_{started_at}"
                    composite_keys.append(composite_key)
                    
                    if i < 3:  # 只显示前3个
                        print(f"   任务 {i+1}:")
                        print(f"     ID: {task_id}")

                        # 安全地显示URL
                        if current_url and current_url != 'no-url':
                            print(f"     URL: {current_url[:50]}...")
                        else:
                            print(f"     URL: {current_url}")

                        print(f"     Worker: {worker}")
                        print(f"     开始时间: {started_at}")
                        print(f"     复合Key: {composite_key[:80]}...")
                        print()
                
                # 检查key唯一性
                unique_keys = set(composite_keys)
                if len(composite_keys) == len(unique_keys):
                    print("✅ 所有复合key都是唯一的")
                    print(f"   总计: {len(composite_keys)} 个任务，{len(unique_keys)} 个唯一key")
                else:
                    print("⚠️  发现重复的复合key:")
                    key_counts = {}
                    for key in composite_keys:
                        key_counts[key] = key_counts.get(key, 0) + 1
                    
                    for key, count in key_counts.items():
                        if count > 1:
                            print(f"   - Key '{key[:60]}...' 出现 {count} 次")
                
                return len(composite_keys) == len(unique_keys)
            else:
                print("⚠️  没有执行中的任务")
                return True
            
        else:
            print(f"❌ API响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_key_generation_logic():
    """测试key生成逻辑的健壮性"""
    print("🔍 测试key生成逻辑的健壮性...")
    
    # 模拟各种边界情况的数据
    test_cases = [
        {
            'id': 'task1',
            'current_url': 'https://example.com/test',
            'worker': 'worker1',
            'started_at': '2025-08-09T10:00:00'
        },
        {
            'id': 'task2',
            'current_url': None,  # 空URL
            'worker': 'worker2',
            'started_at': '2025-08-09T10:01:00'
        },
        {
            'id': 'task3',
            'current_url': 'https://example.com/test',  # 相同URL
            'worker': None,  # 空Worker
            'started_at': '2025-08-09T10:02:00'
        },
        {
            'id': 'task4',
            'current_url': 'https://example.com/test',  # 相同URL
            'worker': 'worker1',  # 相同Worker
            'started_at': None  # 空时间
        }
    ]
    
    print("📝 测试用例:")
    composite_keys = []
    
    for i, task in enumerate(test_cases):
        # 模拟前端的key生成逻辑
        task_id = task.get('id', '')
        current_url = task.get('current_url') or 'no-url'
        worker = task.get('worker') or 'no-worker'
        started_at = task.get('started_at') or str(1000000000 + i)
        
        composite_key = f"{task_id}_{current_url}_{worker}_{started_at}"
        composite_keys.append(composite_key)
        
        print(f"   用例 {i+1}: {composite_key}")
    
    # 检查唯一性
    unique_keys = set(composite_keys)
    if len(composite_keys) == len(unique_keys):
        print("✅ 所有测试用例的key都是唯一的")
        return True
    else:
        print("❌ 发现重复的key")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Ant Design Table rowKey修复\n")
    
    test_results = []
    
    # 1. 测试实际数据的key唯一性
    result1 = test_live_stream_data_structure()
    test_results.append(("实时任务数据key唯一性", result1))
    print()
    
    # 2. 测试key生成逻辑的健壮性
    result2 = test_key_generation_logic()
    test_results.append(("Key生成逻辑健壮性", result2))
    print()
    
    # 汇总结果
    print("="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 Ant Design Table rowKey修复成功！")
        print("\n💡 修复说明:")
        print("   - 问题: 使用了废弃的index参数")
        print("   - 修复: 使用任务的多个属性组合生成唯一key")
        print("   - 新方案: `${record.id}_${record.current_url}_${record.worker}_${record.started_at}`")
        print("\n🔧 修复详情:")
        print("   文件: frontend/src/pages/CeleryMonitoring/CeleryMonitoringV3.tsx")
        print("   行号: 712")
        print("   修改前: rowKey={(record, index) => ...}")
        print("   修改后: rowKey={(record) => ...}")
        print("\n✅ 优势:")
        print("   - 不再使用废弃的index参数")
        print("   - 基于任务实际属性生成key，更稳定")
        print("   - 处理了空值情况，更健壮")
        
        return True
    else:
        print("\n⚠️  部分测试失败，可能还需要进一步调整")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
