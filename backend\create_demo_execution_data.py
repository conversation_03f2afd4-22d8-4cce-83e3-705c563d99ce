#!/usr/bin/env python3
"""
创建演示执行数据
为前端显示创建真实的URL执行状态数据
"""

import asyncio
import time
from datetime import datetime

# 演示配置
DEMO_TASK_ID = "2243"  # 使用图片中显示的任务ID
DEMO_URLS = [
    "https://articulo.mercadolibre.com.mx/MLM-3195229646-7juegos-filtro-y-conector-for-hidrolavadora-koblenz-completo-_JM",
    "https://articulo.mercadolibre.com.mx/MLM-3406662550-test-product-1",
    "https://articulo.mercadolibre.com.mx/MLM-3406662551-test-product-2",
    "https://articulo.mercadolibre.com.mx/MLM-3406662552-test-product-3",
    "https://articulo.mercadolibre.com.mx/MLM-3406662553-test-product-4"
]


async def create_demo_execution_data():
    """创建演示执行数据"""
    print("🔍 创建演示执行数据...")
    
    try:
        from app.services.unified_execution_service import UnifiedExecutionService
        service = UnifiedExecutionService(redis_url="redis://localhost:6379/0")
        
        # 创建执行会话
        session = await service.create_execution_session(
            monitoring_task_id=DEMO_TASK_ID,
            celery_task_id=f"celery_demo_{int(time.time())}",
            execution_type="manual",
            urls=DEMO_URLS
        )
        
        print(f"✅ 创建执行会话: {session.session_id}")
        
        # 创建URL执行记录，模拟不同的执行状态
        workers = ["11235w", "worker_v2", "crawler_001", "backend_worker", "test_worker"]
        statuses = ["completed", "running", "failed", "pending", "completed"]
        response_times = [2.5, None, 1.2, None, 3.8]
        error_messages = [None, None, "Connection timeout", None, None]
        
        for i, url in enumerate(DEMO_URLS):
            worker_id = workers[i % len(workers)]
            status = statuses[i % len(statuses)]
            response_time = response_times[i % len(response_times)]
            error_message = error_messages[i % len(error_messages)]
            
            execution = await service.create_url_execution(
                session_id=session.session_id,
                monitoring_task_id=DEMO_TASK_ID,
                celery_task_id=session.celery_task_id,
                url=url,
                assigned_worker_id=worker_id,
                crawler_endpoint=f"http://localhost:11235/crawl"
            )
            
            # 更新状态
            await service.update_url_execution_status(
                execution.execution_id,
                status,
                response_time=response_time,
                error_message=error_message
            )
            
            print(f"✅ URL执行记录: {url[:50]}... -> {status} (Worker: {worker_id})")
        
        print(f"\n🎉 演示数据创建完成！")
        print(f"📋 监控任务ID: {DEMO_TASK_ID}")
        print(f"📋 Celery任务ID: {session.celery_task_id}")
        print(f"📋 执行会话ID: {session.session_id}")
        print(f"📋 URL数量: {len(DEMO_URLS)}")
        
        # 验证数据
        print(f"\n🔍 验证创建的数据...")
        for url in DEMO_URLS[:2]:  # 只验证前两个URL
            status = await service.get_current_url_execution_status(url)
            if status:
                print(f"✅ {url[:50]}...")
                print(f"   Worker: {status.get('assigned_worker_id')}")
                print(f"   状态: {status.get('status')}")
                print(f"   响应时间: {status.get('response_time', 'N/A')}")
            else:
                print(f"❌ {url[:50]}... - 未找到状态")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建演示数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_endpoints():
    """测试API端点"""
    print(f"\n🔍 测试API端点...")
    
    import requests
    
    try:
        # 测试URL执行状态API
        test_url = DEMO_URLS[0]
        response = requests.get(
            f"http://localhost:8000/api/v1/crawler-debug/execution/url-status",
            params={"url": test_url}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ URL状态API测试成功")
            print(f"   监控任务ID: {data.get('monitoring_task_id')}")
            print(f"   分配Worker: {data.get('assigned_worker_id')}")
            print(f"   状态: {data.get('status')}")
        else:
            print(f"❌ URL状态API测试失败: {response.status_code}")
        
        # 测试监控任务执行链路API
        response = requests.get(
            f"http://localhost:8000/api/v1/crawler-debug/execution/monitoring-task/{DEMO_TASK_ID}"
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 监控任务执行链路API测试成功")
            print(f"   总会话数: {data.get('total_sessions')}")
        else:
            print(f"❌ 监控任务执行链路API测试失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 开始创建演示执行数据\n")
    
    # 1. 创建演示数据
    success = await create_demo_execution_data()
    if not success:
        return False
    
    # 等待数据写入
    await asyncio.sleep(1)
    
    # 2. 测试API端点
    api_success = test_api_endpoints()
    
    if success and api_success:
        print(f"\n🎉 演示数据创建成功！")
        print(f"\n💡 现在可以在前端查看监控任务详情页面:")
        print(f"   - 访问: http://localhost:3000/monitoring-tasks/{DEMO_TASK_ID}")
        print(f"   - 切换到 'Worker分配' 标签页")
        print(f"   - 查看 'URL处理详情' 部分")
        print(f"\n📊 应该能看到:")
        print(f"   - ✅ 爬虫Worker信息 (11235w, worker_v2, 等)")
        print(f"   - ✅ Celery Worker信息 (任务ID前8位)")
        print(f"   - ✅ 最后处理时间 (当前时间)")
        print(f"   - ✅ 响应时间 (2.5s, 1.2s, 3.8s 等)")
        print(f"   - ✅ 执行状态 (已完成、处理中、失败、等待中)")
        return True
    else:
        print(f"\n❌ 演示数据创建失败")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
