#!/usr/bin/env python3
"""
强制清空所有Celery任务
彻底清理系统中的所有任务数据
"""

import subprocess
import time
import requests

def stop_all_services():
    """停止所有相关服务"""
    print("🛑 停止所有服务...")
    
    services = ["celery-worker", "celery-beat", "backend", "task-manager"]
    
    for service in services:
        try:
            result = subprocess.run(
                ["docker", "compose", "-f", "docker-compose.dev.yml", "stop", service],
                cwd="c:\\Users\\<USER>\\Desktop\\MonIt",
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                print(f"   ✅ 停止服务: {service}")
            else:
                print(f"   ⚠️  停止服务失败: {service}")
        except Exception as e:
            print(f"   ❌ 停止服务异常: {service} - {e}")

def clear_redis_completely():
    """完全清空Redis"""
    print("🗑️  完全清空Redis...")
    
    try:
        result = subprocess.run(
            ["docker", "exec", "monit-redis", "redis-cli", "FLUSHALL"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("   ✅ Redis完全清空成功")
            return True
        else:
            print(f"   ❌ Redis清空失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Redis清空异常: {e}")
        return False

def restart_all_services():
    """重启所有服务"""
    print("🔄 重启所有服务...")
    
    services = ["redis", "backend", "celery-worker", "celery-beat", "task-manager"]
    
    for service in services:
        try:
            result = subprocess.run(
                ["docker", "compose", "-f", "docker-compose.dev.yml", "restart", service],
                cwd="c:\\Users\\<USER>\\Desktop\\MonIt",
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                print(f"   ✅ 重启服务: {service}")
            else:
                print(f"   ⚠️  重启服务失败: {service}")
        except Exception as e:
            print(f"   ❌ 重启服务异常: {service} - {e}")
        
        # 等待服务启动
        time.sleep(2)

def verify_cleanup():
    """验证清空效果"""
    print("🔍 验证清空效果...")
    
    # 等待服务完全启动
    print("   等待服务启动...")
    time.sleep(10)
    
    try:
        # 检查统计信息
        response = requests.get("http://localhost:8000/api/v1/celery/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            active_tasks = stats.get('active_tasks', 0)
            pending_tasks = stats.get('pending_tasks', 0)
            workers_online = stats.get('workers_online', 0)
            
            print(f"   活跃任务: {active_tasks}")
            print(f"   等待任务: {pending_tasks}")
            print(f"   在线Worker: {workers_online}")
            
            if active_tasks == 0 and pending_tasks == 0:
                print("   ✅ 所有任务已清空")
                return True
            else:
                print("   ⚠️  仍有任务存在")
                return False
        else:
            print(f"   ❌ 无法获取统计信息: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始强制清空所有Celery任务\n")
    
    # 1. 停止所有服务
    stop_all_services()
    print()
    
    # 2. 完全清空Redis
    redis_cleared = clear_redis_completely()
    print()
    
    # 3. 重启所有服务
    restart_all_services()
    print()
    
    # 4. 验证清空效果
    cleanup_verified = verify_cleanup()
    print()
    
    # 5. 汇总结果
    print("="*50)
    print("📋 强制清空结果:")
    print("="*50)
    print(f"Redis清空: {'✅ 成功' if redis_cleared else '❌ 失败'}")
    print(f"任务清空验证: {'✅ 成功' if cleanup_verified else '❌ 失败'}")
    
    if redis_cleared and cleanup_verified:
        print("\n🎉 所有任务已彻底清空！")
        print("\n💡 现在可以:")
        print("   - 访问 http://localhost:3000/celery-monitoring 查看空白状态")
        print("   - 重新创建测试任务")
        print("   - 开始新的监控任务")
    else:
        print("\n⚠️  清空可能不完整，建议手动检查")

if __name__ == "__main__":
    main()
