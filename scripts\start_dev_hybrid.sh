#!/bin/bash

echo "========================================"
echo "    MonIt 混合开发环境启动脚本"
echo "========================================"
echo

echo "[1/4] 检查Docker服务状态..."
if ! docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "monit-"; then
    echo "❌ Docker服务未运行，请先启动Docker服务"
    echo "运行: docker-compose -f docker-compose.dev.yml up -d timescaledb redis"
    exit 1
fi

echo "✅ Docker服务运行中"

echo
echo "[2/4] 检查conda环境..."
if ! conda info --envs | grep -q "monit"; then
    echo "❌ conda环境 'monit' 不存在"
    echo "请先创建环境: conda create -n monit python=3.11"
    exit 1
fi

echo "✅ conda环境存在"

echo
echo "[3/4] 启动后端服务 (端口8001)..."
# 在后台启动后端
(
    source activate monit
    cd "$(dirname "$0")/.."
    python simple_backend.py
) &
BACKEND_PID=$!

echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"

echo
echo "[4/4] 启动前端服务 (端口3000)..."
# 在后台启动前端
(
    cd "$(dirname "$0")/../frontend"
    npm start
) &
FRONTEND_PID=$!

echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"

echo
echo "========================================"
echo "✅ 开发环境启动完成！"
echo "========================================"
echo
echo "🌐 前端地址: http://localhost:3000"
echo "🔧 后端地址: http://localhost:8001"
echo "📊 Grafana:  http://localhost:3001"
echo "🔍 Kibana:   http://localhost:5601"
echo
echo "进程ID:"
echo "  后端: $BACKEND_PID"
echo "  前端: $FRONTEND_PID"
echo
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT
wait
