#!/usr/bin/env python3
"""
诊断API结构问题

详细分析API使用的存储结构
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def diagnose_api_structure():
    """诊断API结构问题"""
    base_url = "http://localhost:8000"
    
    print("🔍 诊断API结构问题")
    print("=" * 60)
    
    created_url_ids = []
    task_id = None
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 清理所有旧数据
        print("\n📋 步骤1: 清理所有旧数据")
        
        # 清理所有monitoring_tasks相关的键
        all_monitoring_keys = r.keys("monitoring_tasks:*")
        if all_monitoring_keys:
            deleted_count = r.delete(*all_monitoring_keys)
            print(f"  清理了 {deleted_count} 个旧的monitoring_tasks键")
        else:
            print(f"  没有找到需要清理的monitoring_tasks键")
        
        # 步骤2: 创建测试URL
        print(f"\n📋 步骤2: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/diagnose-url",
            "platform": "mercadolibre",
            "title": "诊断URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'diagnose_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'diagnose_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:diagnose_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤3: 创建任务并监控Redis变化
        print(f"\n📋 步骤3: 创建任务并监控Redis变化")
        
        # 记录创建前的Redis状态
        print(f"  创建前Redis状态:")
        all_keys_before = r.keys("monitoring_tasks:*")
        print(f"    monitoring_tasks键数量: {len(all_keys_before)}")
        
        create_data = {
            "name": "诊断结构测试任务",
            "description": "用于诊断API结构的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "12:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            print(f"  ✅ 任务创建成功: {task_id}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
            return
        
        # 记录创建后的Redis状态
        print(f"  创建后Redis状态:")
        all_keys_after = r.keys("monitoring_tasks:*")
        print(f"    monitoring_tasks键数量: {len(all_keys_after)}")
        
        new_keys = set(all_keys_after) - set(all_keys_before)
        print(f"    新增的键: {len(new_keys)} 个")
        for key in sorted(new_keys):
            print(f"      {key}")
        
        # 步骤4: 详细分析Redis结构
        print(f"\n📋 步骤4: 详细分析Redis结构")
        
        # 检查新的分层结构
        expected_keys = [
            f"monitoring_tasks:tasks:{task_id}:basic",
            f"monitoring_tasks:tasks:{task_id}:config", 
            f"monitoring_tasks:tasks:{task_id}:schedule",
            f"monitoring_tasks:tasks:{task_id}:stats",
            f"monitoring_tasks:tasks:{task_id}:urls",
            f"monitoring_tasks:indexes:all_task_ids",
            f"monitoring_tasks:indexes:active_tasks",
            f"monitoring_tasks:url_tasks:{url_id}"
        ]
        
        print(f"  期望的新格式键:")
        for key in expected_keys:
            exists = r.exists(key)
            print(f"    {'✅' if exists else '❌'} {key}")
            if exists and key.endswith(':urls'):
                url_count = r.scard(key)
                print(f"        URL数量: {url_count}")
            elif exists and key.endswith(f':{url_id}'):
                task_count = r.scard(key)
                print(f"        关联任务数: {task_count}")
        
        # 检查不应该存在的旧格式键
        old_format_keys = [
            f"monitoring_tasks:{task_id}",
            f"monitoring_tasks:task_urls:{task_id}",
            f"monitoring_tasks:all_ids",
            f"monitoring_tasks:active",
            f"task_urls:{task_id}",
            f"url_tasks:{url_id}"
        ]
        
        print(f"  不应该存在的旧格式键:")
        old_keys_found = []
        for key in old_format_keys:
            exists = r.exists(key)
            print(f"    {'❌ (不应存在)' if exists else '✅'} {key}")
            if exists:
                old_keys_found.append(key)
        
        # 步骤5: 测试API响应
        print(f"\n📋 步骤5: 测试API响应")
        
        # 测试任务详情API
        detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
        if detail_response.status_code == 200:
            detail_result = detail_response.json()
            task_data = detail_result.get('data', {})
            print(f"  ✅ 任务详情API正常")
            print(f"    任务名称: {task_data.get('name')}")
            print(f"    URL数量: {task_data.get('total_urls', 0)}")
            print(f"    调度状态: {task_data.get('schedule', {}).get('enabled')}")
        else:
            print(f"  ❌ 任务详情API失败: {detail_response.text}")
        
        # 测试任务URL列表API
        urls_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}/urls", timeout=10)
        if urls_response.status_code == 200:
            urls_result = urls_response.json()
            task_urls = urls_result.get('data', [])
            print(f"  ✅ 任务URL列表API正常")
            print(f"    返回URL数量: {len(task_urls)}")
            if task_urls:
                print(f"    第一个URL: {task_urls[0].get('url', 'N/A')}")
        else:
            print(f"  ❌ 任务URL列表API失败: {urls_response.text}")
        
        # 步骤6: 总结分析
        print(f"\n📋 步骤6: 总结分析")
        
        if old_keys_found:
            print(f"  ❌ 发现旧格式键，说明某些API仍在使用旧结构:")
            for key in old_keys_found:
                print(f"    - {key}")
        else:
            print(f"  ✅ 没有发现旧格式键，所有API都使用新结构")
        
        # 检查数据完整性
        basic_exists = r.exists(f"monitoring_tasks:tasks:{task_id}:basic")
        urls_exists = r.exists(f"monitoring_tasks:tasks:{task_id}:urls")
        reverse_exists = r.exists(f"monitoring_tasks:url_tasks:{url_id}")
        
        if basic_exists and urls_exists and reverse_exists:
            print(f"  ✅ 数据完整性检查通过")
        else:
            print(f"  ❌ 数据完整性检查失败:")
            print(f"    基本信息: {'✅' if basic_exists else '❌'}")
            print(f"    URL关联: {'✅' if urls_exists else '❌'}")
            print(f"    反向关联: {'✅' if reverse_exists else '❌'}")
        
        print(f"\n🎉 诊断完成!")
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        if task_id:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:diagnose_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    diagnose_api_structure()
