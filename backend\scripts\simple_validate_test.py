#!/usr/bin/env python3
"""
简单的验证API测试
"""

import requests
import json

def test_validate_api():
    """测试验证API"""
    url = "http://localhost:8000/api/v1/tasks/validate"
    
    # 测试数据
    data = {
        "name": "测试任务",
        "url_ids": ["test1", "test2"],
        "schedule": {
            "type": "daily",
            "enabled": True,
            "time": "09:00",
            "timezone": "Asia/Shanghai"
        }
    }
    
    print("🔍 测试验证API")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 成功")
            print(f"响应: {response.json()}")
        else:
            print("❌ 失败")
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    test_validate_api()
