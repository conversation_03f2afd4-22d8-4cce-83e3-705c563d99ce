#!/usr/bin/env python3
"""
导出当前配置默认值
用于架构重构前的配置备份和迁移准备
"""

import json
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.app.api.v1.crawler_config import config_service
from backend.app.services.crawler_instance_config_service import crawler_instance_config_service


async def export_current_defaults():
    """导出当前配置默认值"""
    print("🔧 开始导出当前配置默认值...")
    
    export_data = {
        "export_time": datetime.now().isoformat(),
        "export_purpose": "架构重构前的配置备份",
        "configs": {}
    }
    
    try:
        # 1. 导出传统单一配置
        print("📋 导出传统单一配置...")
        try:
            traditional_config = await config_service.get_config()
            export_data["configs"]["traditional_single_config"] = {
                "description": "传统的单一爬虫配置",
                "config": traditional_config.dict() if traditional_config else None
            }
            print("✅ 传统配置导出成功")
        except Exception as e:
            print(f"⚠️ 传统配置导出失败: {e}")
            export_data["configs"]["traditional_single_config"] = {
                "description": "传统配置导出失败",
                "error": str(e)
            }
        
        # 2. 导出爬虫实例配置
        print("📋 导出爬虫实例配置...")
        try:
            instance_configs = await crawler_instance_config_service.get_all_configs()
            export_data["configs"]["crawler_instance_configs"] = {
                "description": "爬虫实例配置列表",
                "count": len(instance_configs),
                "configs": [config.dict() for config in instance_configs]
            }
            print(f"✅ 实例配置导出成功: {len(instance_configs)} 个配置")
        except Exception as e:
            print(f"⚠️ 实例配置导出失败: {e}")
            export_data["configs"]["crawler_instance_configs"] = {
                "description": "实例配置导出失败",
                "error": str(e)
            }
        
        # 3. 导出默认配置
        print("📋 导出默认配置...")
        try:
            default_config = await crawler_instance_config_service.get_config_manager()
            default_config_data = default_config.get_default_config()
            export_data["configs"]["default_config"] = {
                "description": "当前默认配置",
                "default_config_id": default_config.default_config_id,
                "config": default_config_data.dict() if default_config_data else None
            }
            print("✅ 默认配置导出成功")
        except Exception as e:
            print(f"⚠️ 默认配置导出失败: {e}")
            export_data["configs"]["default_config"] = {
                "description": "默认配置导出失败",
                "error": str(e)
            }
        
        # 4. 创建配置分离映射
        print("📋 创建配置分离映射...")
        export_data["config_separation_mapping"] = {
            "description": "新架构中的配置分离映射",
            "crawler_config_fields": [
                "browser",
                "crawler", 
                "llm",
                "schema_extraction",
                "content_processing",
                "link_filtering",
                "monitor"
            ],
            "backend_config_fields": [
                "api_endpoint",
                "timeout",
                "max_retries",
                "auth_config",
                "max_concurrent",
                "weight",
                "priority",
                # 新增的调度相关字段
                "mean_delay",
                "max_range",
                "pool_size", 
                "memory_threshold"
            ]
        }
        
        # 5. 创建默认值模板
        print("📋 创建默认值模板...")
        if export_data["configs"]["default_config"]["config"]:
            default_cfg = export_data["configs"]["default_config"]["config"]
            
            export_data["default_templates"] = {
                "crawler_config_template": {
                    "config_name": "默认爬取配置",
                    "description": "从旧架构迁移的默认爬取配置",
                    "browser": default_cfg.get("browser", {}),
                    "crawler": default_cfg.get("crawler", {}),
                    "llm": default_cfg.get("llm", {}),
                    "schema_extraction": default_cfg.get("schema_extraction", {}),
                    "content_processing": default_cfg.get("content_processing", {}),
                    "link_filtering": default_cfg.get("link_filtering", {}),
                    "monitor": default_cfg.get("monitor", {})
                },
                "backend_config_template": {
                    "backend_name": "默认后端配置",
                    "description": "从旧架构迁移的默认后端配置",
                    "api_endpoint": default_cfg.get("api_endpoint", "http://localhost:11234"),
                    "timeout": default_cfg.get("timeout", 30000),
                    "max_retries": default_cfg.get("max_retries", 3),
                    "auth_config": default_cfg.get("auth_config", {"auth_type": "none"}),
                    "max_concurrent": default_cfg.get("max_concurrent", 2),
                    "weight": default_cfg.get("weight", 1),
                    "priority": default_cfg.get("priority", 1),
                    # 新增的调度配置默认值
                    "mean_delay": 1000,  # 1秒平均延迟
                    "max_range": 2000,   # 最大2秒范围
                    "pool_size": 10,     # 线程池大小
                    "memory_threshold": 512  # 512MB内存阈值
                }
            }
        
        # 6. 保存导出数据
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        export_file = config_dir / "exported_config_defaults.json"
        with open(export_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 配置默认值导出完成: {export_file}")
        
        # 7. 生成迁移报告
        print("📊 生成迁移报告...")
        report = generate_migration_report(export_data)
        
        report_file = config_dir / "migration_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 迁移报告生成完成: {report_file}")
        
        return export_data
        
    except Exception as e:
        print(f"❌ 导出过程发生错误: {e}")
        raise


def generate_migration_report(export_data):
    """生成迁移报告"""
    report = f"""# 配置迁移报告

## 导出信息
- **导出时间**: {export_data['export_time']}
- **导出目的**: {export_data['export_purpose']}

## 配置统计

### 传统单一配置
- **状态**: {'✅ 成功' if 'error' not in export_data['configs']['traditional_single_config'] else '❌ 失败'}
- **描述**: {export_data['configs']['traditional_single_config']['description']}

### 爬虫实例配置
- **状态**: {'✅ 成功' if 'error' not in export_data['configs']['crawler_instance_configs'] else '❌ 失败'}
- **数量**: {export_data['configs']['crawler_instance_configs'].get('count', 0)} 个配置
- **描述**: {export_data['configs']['crawler_instance_configs']['description']}

### 默认配置
- **状态**: {'✅ 成功' if 'error' not in export_data['configs']['default_config'] else '❌ 失败'}
- **默认配置ID**: {export_data['configs']['default_config'].get('default_config_id', 'N/A')}
- **描述**: {export_data['configs']['default_config']['description']}

## 配置分离计划

### CrawlerConfig 字段
"""
    
    for field in export_data['config_separation_mapping']['crawler_config_fields']:
        report += f"- {field}\n"
    
    report += "\n### BackendConfig 字段\n"
    
    for field in export_data['config_separation_mapping']['backend_config_fields']:
        report += f"- {field}\n"
    
    report += f"""
## 迁移建议

1. **配置拆分**: 将现有配置按照上述字段分离到 CrawlerConfig 和 BackendConfig
2. **Worker创建**: 为每个现有配置创建对应的 CrawlerWorker
3. **默认值保留**: 使用导出的默认值作为新配置的基础
4. **验证测试**: 迁移后进行完整的功能测试

## 风险评估

- **数据完整性**: {'✅ 低风险' if all('error' not in config for config in export_data['configs'].values()) else '⚠️ 中等风险'}
- **配置复杂度**: 中等 (需要手动映射部分配置)
- **回滚难度**: 低 (保留完整备份)

## 下一步行动

1. 检查导出的配置数据
2. 验证配置分离映射的正确性
3. 开始实施数据模型重构
4. 准备迁移脚本

---
**报告生成时间**: {datetime.now().isoformat()}
"""
    
    return report


async def main():
    """主函数"""
    print("🚀 开始配置默认值导出流程...")
    
    try:
        export_data = await export_current_defaults()
        
        print("\n" + "="*50)
        print("🎉 配置导出完成！")
        print("\n📁 生成的文件:")
        print("   - config/exported_config_defaults.json")
        print("   - config/migration_report.md")
        print("\n📋 导出统计:")
        
        configs = export_data["configs"]
        for config_type, config_data in configs.items():
            status = "✅ 成功" if "error" not in config_data else "❌ 失败"
            print(f"   - {config_type}: {status}")
        
        print("\n🔄 下一步:")
        print("   1. 检查导出的配置文件")
        print("   2. 验证配置分离映射")
        print("   3. 开始数据模型重构")
        
    except Exception as e:
        print(f"\n❌ 导出失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
