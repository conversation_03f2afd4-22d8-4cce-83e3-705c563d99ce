"""
爬虫系统自定义异常类

定义了爬虫系统中各个模块可能抛出的异常类型，
便于错误处理和调试。
"""

from typing import Optional, Dict, Any


class CrawlerException(Exception):
    """爬虫系统基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        base_msg = self.message
        if self.error_code:
            base_msg = f"[{self.error_code}] {base_msg}"
        if self.details:
            base_msg += f" Details: {self.details}"
        return base_msg


class BrowserException(CrawlerException):
    """浏览器相关异常"""
    
    def __init__(
        self, 
        message: str, 
        browser_type: Optional[str] = None,
        page_url: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if browser_type:
            details['browser_type'] = browser_type
        if page_url:
            details['page_url'] = page_url
        
        super().__init__(message, **kwargs, details=details)


class BrowserStartupException(BrowserException):
    """浏览器启动异常"""
    pass


class BrowserTimeoutException(BrowserException):
    """浏览器超时异常"""
    
    def __init__(self, message: str, timeout: Optional[float] = None, **kwargs):
        details = kwargs.get('details', {})
        if timeout:
            details['timeout'] = timeout
        super().__init__(message, **kwargs, details=details)


class PageNavigationException(BrowserException):
    """页面导航异常"""
    pass


class ExtractionException(CrawlerException):
    """数据提取异常"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        selector: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if field_name:
            details['field_name'] = field_name
        if selector:
            details['selector'] = selector
        
        super().__init__(message, **kwargs, details=details)


class SelectorNotFoundException(ExtractionException):
    """选择器未找到异常"""
    pass


class DataValidationException(ExtractionException):
    """数据验证异常"""
    
    def __init__(
        self, 
        message: str, 
        expected_type: Optional[str] = None,
        actual_value: Optional[Any] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if expected_type:
            details['expected_type'] = expected_type
        if actual_value is not None:
            details['actual_value'] = actual_value
        
        super().__init__(message, **kwargs, details=details)


class TaskException(CrawlerException):
    """任务执行异常"""
    
    def __init__(
        self, 
        message: str, 
        task_id: Optional[str] = None,
        task_status: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if task_id:
            details['task_id'] = task_id
        if task_status:
            details['task_status'] = task_status
        
        super().__init__(message, **kwargs, details=details)


class TaskTimeoutException(TaskException):
    """任务超时异常"""
    pass


class TaskRetryExhaustedException(TaskException):
    """任务重试次数耗尽异常"""
    
    def __init__(
        self, 
        message: str, 
        max_retries: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if max_retries:
            details['max_retries'] = max_retries
        
        super().__init__(message, **kwargs, details=details)


class SessionException(CrawlerException):
    """会话管理异常"""
    
    def __init__(
        self, 
        message: str, 
        session_id: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if session_id:
            details['session_id'] = session_id
        
        super().__init__(message, **kwargs, details=details)


class SessionExpiredException(SessionException):
    """会话过期异常"""
    pass


class LoginException(SessionException):
    """登录失败异常"""
    
    def __init__(
        self, 
        message: str, 
        username: Optional[str] = None,
        platform: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if username:
            details['username'] = username
        if platform:
            details['platform'] = platform
        
        super().__init__(message, **kwargs, details=details)


class ProxyException(CrawlerException):
    """代理相关异常"""
    
    def __init__(
        self, 
        message: str, 
        proxy_ip: Optional[str] = None,
        proxy_port: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if proxy_ip:
            details['proxy_ip'] = proxy_ip
        if proxy_port:
            details['proxy_port'] = proxy_port
        
        super().__init__(message, **kwargs, details=details)


class ProxyConnectionException(ProxyException):
    """代理连接异常"""
    pass


class AntiDetectionException(CrawlerException):
    """反检测异常"""
    
    def __init__(
        self, 
        message: str, 
        detection_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if detection_type:
            details['detection_type'] = detection_type
        
        super().__init__(message, **kwargs, details=details)


class CaptchaDetectedException(AntiDetectionException):
    """验证码检测到异常"""
    pass


class RateLimitException(AntiDetectionException):
    """频率限制异常"""
    
    def __init__(
        self, 
        message: str, 
        retry_after: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if retry_after:
            details['retry_after'] = retry_after
        
        super().__init__(message, **kwargs, details=details)


class ConfigurationException(CrawlerException):
    """配置异常"""
    
    def __init__(
        self, 
        message: str, 
        config_key: Optional[str] = None,
        config_file: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        if config_file:
            details['config_file'] = config_file
        
        super().__init__(message, **kwargs, details=details)


# 异常映射字典，便于根据错误代码创建异常
EXCEPTION_MAP = {
    'BROWSER_STARTUP_FAILED': BrowserStartupException,
    'BROWSER_TIMEOUT': BrowserTimeoutException,
    'PAGE_NAVIGATION_FAILED': PageNavigationException,
    'SELECTOR_NOT_FOUND': SelectorNotFoundException,
    'DATA_VALIDATION_FAILED': DataValidationException,
    'TASK_TIMEOUT': TaskTimeoutException,
    'TASK_RETRY_EXHAUSTED': TaskRetryExhaustedException,
    'SESSION_EXPIRED': SessionExpiredException,
    'LOGIN_FAILED': LoginException,
    'PROXY_CONNECTION_FAILED': ProxyConnectionException,
    'CAPTCHA_DETECTED': CaptchaDetectedException,
    'RATE_LIMITED': RateLimitException,
    'CONFIG_ERROR': ConfigurationException,
}


def create_exception(error_code: str, message: str, **kwargs) -> CrawlerException:
    """根据错误代码创建对应的异常实例"""
    exception_class = EXCEPTION_MAP.get(error_code, CrawlerException)
    return exception_class(message, error_code=error_code, **kwargs) 