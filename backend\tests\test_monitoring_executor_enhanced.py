"""
测试增强的监控任务执行器
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from app.tasks.monitoring_executor import MonitoringTaskExecutor


class TestMonitoringExecutorEnhanced:
    """测试增强的监控任务执行器"""
    
    @pytest.mark.asyncio
    async def test_execute_with_request_builder_success(self):
        """测试使用请求构建器的监控任务执行成功场景"""
        
        task_id = "test-monitoring-task-123"
        
        # 模拟CrawlerRequestBuilder返回的请求
        mock_crawler_request = {
            "request": {
                "urls": [
                    "https://example.com/product/1",
                    "https://example.com/product/2"
                ],
                "q": "提取商品标题和价格",
                "priority": "medium",
                "batch_name": "Task_test-monitoring-task-123_20250809_140000",
                "max_concurrent_tasks": 2,
                "batch_timeout": 300
            },
            "auth_context": {
                "user_id": "monit_system",
                "auth_method": "api_key",
                "api_key_prefix": "monitoring_key"
            }
        }
        
        # 模拟爬虫API响应
        mock_api_result = {
            "status": "success",
            "batch_id": "monitoring-batch-456",
            "submitted_urls": 2,
            "estimated_completion": "2025-08-09T14:30:00"
        }
        
        with patch('app.tasks.monitoring_executor.CrawlerRequestBuilder') as mock_builder, \
             patch('app.services.crawler_api_service.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值
            mock_builder.build_request_for_task = AsyncMock(return_value=mock_crawler_request)
            
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.return_value = mock_api_result
            mock_api_service.return_value = mock_api_instance
            
            # 创建执行器
            executor = MonitoringTaskExecutor(task_id)
            
            # 模拟依赖的方法
            executor._update_task_execution_start = AsyncMock()
            executor._update_task_execution_complete = AsyncMock()
            executor._cleanup_resources = AsyncMock()
            
            # 执行测试
            result = await executor.execute_with_request_builder()
            
            # 验证结果
            assert result["task_id"] == task_id
            assert result["urls_count"] == 2
            assert result["priority"] == "medium"
            assert result["batch_name"] == "Task_test-monitoring-task-123_20250809_140000"
            assert result["status"] == "submitted_to_crawler"
            assert result["method"] == "request_builder"
            assert result["crawler_api_result"] == mock_api_result
            assert "execution_id" in result
            assert "execution_context" in result
            
            # 验证调用
            mock_builder.build_request_for_task.assert_called_once()
            call_args = mock_builder.build_request_for_task.call_args
            assert call_args[1]["task_id"] == task_id
            assert "execution_id" in call_args[1]["execution_context"]
            
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)
            executor._update_task_execution_start.assert_called_once()
            executor._update_task_execution_complete.assert_called_once()
            executor._cleanup_resources.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_with_request_builder_validation_error(self):
        """测试监控任务执行验证错误场景"""
        
        task_id = "test-monitoring-task-no-config"
        
        with patch('app.tasks.monitoring_executor.CrawlerRequestBuilder') as mock_builder:
            # 模拟验证错误
            mock_builder.build_request_for_task = AsyncMock(
                side_effect=ValueError("Task has no crawl config")
            )
            
            # 创建执行器
            executor = MonitoringTaskExecutor(task_id)
            
            # 模拟依赖的方法
            executor._update_task_execution_start = AsyncMock()
            executor._update_task_execution_complete = AsyncMock()
            executor._cleanup_resources = AsyncMock()
            
            # 执行测试
            result = await executor.execute_with_request_builder()
            
            # 验证结果
            assert result["task_id"] == task_id
            assert result["status"] == "validation_error"
            assert "Task has no crawl config" in result["error"]
            assert result["method"] == "request_builder"
            assert "execution_id" in result
            
            # 验证调用
            executor._update_task_execution_start.assert_called_once()
            executor._update_task_execution_complete.assert_called_once()
            executor._cleanup_resources.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_with_request_builder_api_error(self):
        """测试监控任务执行API错误场景"""
        
        task_id = "test-monitoring-task-api-error"
        
        # 模拟正常的请求构建
        mock_crawler_request = {
            "request": {
                "urls": ["https://example.com/product/1"],
                "q": "提取商品信息",
                "priority": "low",
                "batch_name": "Task_test-monitoring-task-api-error_20250809_140000"
            },
            "auth_context": {"user_id": "monit_system"}
        }
        
        with patch('app.tasks.monitoring_executor.CrawlerRequestBuilder') as mock_builder, \
             patch('app.services.crawler_api_service.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值
            mock_builder.build_request_for_task = AsyncMock(return_value=mock_crawler_request)
            
            # 模拟API调用失败
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.side_effect = Exception("Crawler API unavailable")
            mock_api_service.return_value = mock_api_instance
            
            # 创建执行器
            executor = MonitoringTaskExecutor(task_id)
            
            # 模拟依赖的方法
            executor._update_task_execution_start = AsyncMock()
            executor._update_task_execution_complete = AsyncMock()
            executor._cleanup_resources = AsyncMock()
            
            # 执行测试，应该抛出异常
            with pytest.raises(Exception, match="Crawler API unavailable"):
                await executor.execute_with_request_builder()
            
            # 验证调用
            mock_builder.build_request_for_task.assert_called_once()
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)
            executor._update_task_execution_start.assert_called_once()
            executor._update_task_execution_complete.assert_called_once()  # 失败状态更新
            executor._cleanup_resources.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
