#!/usr/bin/env python3
"""
调试任务删除过程

检查任务删除时URL反向关联的清理过程
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def debug_task_deletion():
    """调试任务删除过程"""
    base_url = "http://localhost:8000"
    
    print("🔍 调试任务删除过程")
    print("=" * 50)
    
    created_url_ids = []
    task_id = None
    
    try:
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/debug-url",
            "platform": "mercadolibre",
            "title": "调试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'debug_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'debug_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:debug_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 创建任务
        print(f"\n📋 步骤2: 创建任务")
        
        create_data = {
            "name": "调试删除任务",
            "description": "用于调试删除过程的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "10:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            print(f"  ✅ 任务创建成功: {task_id}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
            return
        
        # 步骤3: 检查创建后的关联状态
        print(f"\n📋 步骤3: 检查创建后的关联状态")
        
        # 检查任务的URL
        task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
        task_url_ids = r.smembers(task_urls_key)
        print(f"  任务URL集合: {task_url_ids}")
        
        # 检查URL反向关联
        url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
        url_task_ids = r.smembers(url_tasks_key)
        print(f"  URL反向关联: {url_task_ids}")
        
        # 步骤4: 删除任务并监控过程
        print(f"\n📋 步骤4: 删除任务并监控过程")
        
        print(f"  删除前状态:")
        print(f"    任务URL集合存在: {r.exists(task_urls_key)}")
        print(f"    URL反向关联存在: {r.exists(url_tasks_key)}")
        print(f"    URL反向关联内容: {r.smembers(url_tasks_key)}")
        
        # 执行删除
        delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
        print(f"  删除API状态码: {delete_response.status_code}")
        
        if delete_response.status_code in [200, 204]:
            print(f"  ✅ 任务删除API调用成功")
        else:
            print(f"  ❌ 任务删除API调用失败: {delete_response.text}")
        
        print(f"  删除后状态:")
        print(f"    任务URL集合存在: {r.exists(task_urls_key)}")
        print(f"    URL反向关联存在: {r.exists(url_tasks_key)}")
        print(f"    URL反向关联内容: {r.smembers(url_tasks_key)}")
        
        # 检查任务是否真的被删除
        task_basic_key = f"monitoring_tasks:tasks:{task_id}:basic"
        print(f"    任务基本信息存在: {r.exists(task_basic_key)}")
        
        # 检查索引
        all_task_ids_key = "monitoring_tasks:indexes:all_task_ids"
        task_in_index = r.sismember(all_task_ids_key, task_id)
        print(f"    任务在总索引中: {task_in_index}")
        
        # 步骤5: 验证清理结果
        print(f"\n📋 步骤5: 验证清理结果")
        
        if not r.exists(url_tasks_key):
            print(f"  ✅ URL反向关联已正确清理")
        elif r.scard(url_tasks_key) == 0:
            print(f"  ✅ URL反向关联为空（应该被删除）")
        else:
            remaining_tasks = r.smembers(url_tasks_key)
            print(f"  ❌ URL反向关联未清理，剩余任务: {remaining_tasks}")
        
        if not r.exists(task_urls_key):
            print(f"  ✅ 任务URL集合已正确删除")
        else:
            print(f"  ❌ 任务URL集合未删除")
        
        if not task_in_index:
            print(f"  ✅ 任务已从索引中移除")
        else:
            print(f"  ❌ 任务仍在索引中")
        
        print(f"\n🎉 任务删除调试完成!")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:debug_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    debug_task_deletion()
