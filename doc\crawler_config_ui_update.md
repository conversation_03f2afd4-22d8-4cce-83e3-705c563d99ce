# 爬虫配置UI更新文档

## 问题描述

用户反馈爬虫系统配置页面很多配置项都不可见，特别是LLM配置中的schema部分和其他新增的配置项。

## 问题分析

经过检查发现，前端配置页面存在以下问题：

1. **接口定义过时**：页面中使用了本地定义的旧接口，没有使用更新后的服务接口
2. **缺失配置项**：
   - LLM配置中缺少 `query` 字段
   - 完全缺少 `schema_extraction` 配置部分
   - 完全缺少 `content_processing` 配置部分  
   - 完全缺少 `link_filtering` 配置部分
   - 爬虫配置中缺少新增的字段

## 解决方案

### 1. 更新接口使用

**修改前**：
```typescript
interface CrawlerConfig {
  // 本地重复定义的接口
}
```

**修改后**：
```typescript
// 使用从服务中导入的接口，而不是重复定义
import { CrawlerFullConfig } from '../../services/crawlerConfigService';
```

### 2. 更新LLM配置

**新增字段**：
- `query`: 提取查询指令字段，支持多行文本输入

**UI组件**：
```tsx
<Form.Item
  label="提取查询指令"
  name={['llm', 'query']}
  rules={[{ required: true, message: '请输入提取查询指令' }]}
>
  <TextArea rows={3} placeholder="提取页面中所有文章的标题、作者和发布时间" />
</Form.Item>
```

### 3. 新增Schema提取配置选项卡

**配置项**：
- `extraction_schema`: JSON Schema定义（多行文本）
- `instructions`: 提取指令（多行文本）
- `validate_schema`: 是否验证Schema（开关）
- `return_raw`: 是否返回原始数据（开关）

**UI特性**：
- 使用TextArea组件支持大段文本输入
- 提供详细的工具提示说明
- 合理的表单验证

### 4. 新增内容处理配置选项卡

**配置项**：
- `word_count_threshold`: 词数阈值（数字输入）
- `css_selector`: CSS选择器（文本输入）
- `excluded_tags`: 排除标签（标签选择器）
- `excluded_selector`: 排除选择器（文本输入）
- `parser_type`: 解析器类型（下拉选择）
- `remove_forms`, `only_text`, `prettify`, `keep_data_attributes`: 布尔开关

### 5. 新增链接过滤配置选项卡

**配置项**：
- 链接控制开关：`exclude_external_links`, `exclude_internal_links`, `exclude_social_media_links`
- 域名列表：`exclude_domains`, `social_media_domains`（标签选择器）
- 图片和表格阈值：`image_score_threshold`, `table_score_threshold`（数字输入）

### 6. 更新爬虫配置

**新增字段**：
- `check_robots_txt`: 检查robots.txt
- `fetch_ssl_certificate`: 获取SSL证书
- `extraction_strategy`: 提取策略（下拉选择）
- `chunking_strategy`: 分块策略（下拉选择）
- `markdown_generator`: Markdown生成器（下拉选择）
- `bypass_cache`: 绕过缓存

## 更新后的配置选项卡结构

1. **API配置** - 基础API连接设置
2. **浏览器配置** - 浏览器行为和反检测设置
3. **爬虫配置** - 核心爬取策略和高级设置
4. **LLM配置** - 大语言模型参数和查询指令
5. **Schema提取** - 结构化数据提取配置 ⭐ 新增
6. **内容处理** - 页面内容处理和过滤 ⭐ 新增
7. **链接过滤** - 链接和媒体过滤规则 ⭐ 新增
8. **调度配置** - 任务调度和监控设置

## UI改进

### 1. 表单组件优化

- **TextArea**: 用于多行文本输入（指令、Schema等）
- **Select with tags**: 用于域名列表等多值输入
- **Tooltip**: 为复杂配置项提供详细说明
- **Divider**: 合理分组相关配置项

### 2. 用户体验提升

- **分组显示**: 使用Divider将相关配置分组
- **工具提示**: 为每个配置项提供详细说明
- **表单验证**: 必填项和格式验证
- **默认值**: 所有配置项都有合理的默认值

### 3. 响应式布局

- 使用Ant Design的栅格系统
- 不同配置项根据重要性分配不同的列宽
- 在小屏幕上自动调整布局

## 配置数据流

```
用户界面 → Form表单 → crawlerConfigService → 后端API → Redis存储
```

### 数据验证

1. **前端验证**: 表单字段验证、必填项检查
2. **服务层验证**: crawlerConfigService中的配置验证
3. **后端验证**: Pydantic模型验证
4. **存储验证**: Redis数据完整性检查

## 测试验证

创建了测试页面 `test_crawler_config_ui.html` 来验证：

- ✅ 所有配置项的完整性
- ✅ 新增配置项的正确显示
- ✅ 配置项的分组和组织
- ✅ UI组件的适配性

## 使用说明

### 开发者

1. 所有配置项现在都通过统一的服务接口管理
2. 新增配置项会自动在UI中显示
3. 配置验证在多个层级进行

### 用户

1. **Schema提取**: 可以自定义数据提取结构和指令
2. **内容处理**: 可以精确控制页面内容的处理方式
3. **链接过滤**: 可以设置详细的过滤规则避免无关内容
4. **高级爬虫设置**: 可以调整提取策略和缓存行为

## 后续优化建议

1. **配置预设**: 为不同场景提供预设配置模板
2. **实时预览**: 提供配置效果的实时预览
3. **配置导入导出**: 支持配置的备份和分享
4. **配置历史**: 记录配置变更历史
5. **配置验证**: 增强配置项的实时验证和错误提示

## 总结

通过这次更新，爬虫配置页面现在包含了所有最新的配置项，特别是：

- ✅ LLM配置中的query字段
- ✅ 完整的Schema提取配置
- ✅ 详细的内容处理选项
- ✅ 灵活的链接过滤规则
- ✅ 高级爬虫策略设置

用户现在可以通过UI界面完全控制爬虫系统的所有行为，无需手动编辑配置文件。
