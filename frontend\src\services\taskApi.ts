/**
 * 任务管理API服务
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// URL数据类型
export interface URLData {
  id: string;
  url: string;
  status: 'valid' | 'invalid' | 'pending';
  platform: string;
  row_index: number;
  chinese_name?: string;
  spanish_name?: string;
  error_message?: string;
}

// Excel解析响应类型
export interface ExcelUploadResponse {
  success: boolean;
  submission_id?: string;
  message: string;
  total_urls: number;
  valid_urls: number;
  invalid_urls: number;
  platforms: Record<string, number>;
  filename?: string;
  warnings?: string[];
  urls?: URLData[];
  validation_results?: URLData[]; // 添加验证结果字段
  file_info?: {
    filename: string;
    size: number;
    size_mb: number;
    rows: number;
    columns: number;
    upload_time: string;
  };
  parse_time?: string;
  validation_time?: string; // 添加验证时间字段
}

export interface LinkData {
  id: string;
  url: string;
  title: string;
  status: 'valid' | 'invalid' | 'checking';
  platform: string;
  lastCheck: string;
  chinese_name?: string;
  spanish_name?: string;
  row_index?: number;
}

export interface TaskSubmissionRequest {
  urls: string[];
  platform: string;
  priority: string;
  options?: Record<string, any>;
}

export interface TaskSubmissionResponse {
  success: boolean;
  submission_id: string;
  message: string;
  batches_created: number;
  total_urls: number;
}

export interface BatchData {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalUrls: number;
  completedUrls: number;
  failedUrls: number;
  startTime: string | null;
  endTime: string | null;
  urls: Array<{
    url: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    price: string | null;
    lastCheck: string | null;
  }>;
}

export interface TaskDetailResponse {
  success: boolean;
  task: TaskData;
  batches: BatchData[];
}

export interface TaskData {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  totalUrls: number;
  completedUrls: number;
  failedUrls: number;
  createdAt: string;
  updatedAt: string;
  platform: string;
  priority: string;
}

/**
 * 上传Excel文件到后端进行解析（仅解析，不创建任务）
 */
export const parseExcelFile = async (
  file: File
): Promise<ExcelUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${API_BASE_URL}/api/v1/excel/parse`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Excel验证响应类型（专门用于验证API）
export interface ExcelValidationResponse {
  success: boolean;
  message: string;
  total_urls: number;
  valid_urls: number;
  invalid_urls: number;
  validation_results: URLData[];
  validation_time: string;
}

/**
 * 验证URL列表
 */
export const validateUrls = async (
  urls: string[],
  platform: string = 'mercadolibre'
): Promise<ExcelValidationResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/excel/validate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      urls,
      platform,
      validate_accessibility: false
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取支持的平台列表
 */
export const getSupportedPlatforms = async () => {
  const response = await fetch(`${API_BASE_URL}/api/v1/excel/platforms`);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取任务列表
 */
export const getTaskList = async (): Promise<TaskData[]> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/list`);
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  return data.tasks || [];
};

/**
 * 获取任务详情
 */
export const getTaskDetail = async (taskId: string): Promise<TaskData> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}`);
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 暂停任务
 */
export const pauseTask = async (taskId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/pause`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
};

/**
 * 恢复任务
 */
export const resumeTask = async (taskId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/resume`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
};

/**
 * 取消任务
 */
export const cancelTask = async (taskId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/cancel`, {
    method: 'POST',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
};

/**
 * 删除任务
 */
export const deleteTask = async (taskId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
};

/**
 * 获取任务日志
 */
export const getTaskLogs = async (taskId: string): Promise<any[]> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/logs`);
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  return data.logs || [];
};

/**
 * 验证Excel文件结构
 */
export const validateExcelFile = async (file: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/validate-excel`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 提交任务到后端TaskManager
 */
export const submitTask = async (
  request: TaskSubmissionRequest
): Promise<TaskSubmissionResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/submit`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取任务详情，包括批次信息
 */
export const getTaskDetailWithBatches = async (taskId: string): Promise<TaskDetailResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/detail`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 启动任务
 */
export const startTask = async (taskId: string): Promise<{ success: boolean; message: string }> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 停止任务
 */
export const stopTask = async (taskId: string): Promise<{ success: boolean; message: string }> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/tasks/${taskId}/stop`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};
