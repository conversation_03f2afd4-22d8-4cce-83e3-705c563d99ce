"""
爬虫实例配置管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List
import logging

from ...schemas.crawler_instance_config import (
    CrawlerInstanceConfig, CrawlerInstanceConfigCreate, CrawlerInstanceConfigUpdate,
    CrawlerConfigManager, ConnectionTestResult
)
from ...services.crawler_instance_config_service import crawler_instance_config_service


logger = logging.getLogger(__name__)
router = APIRouter(prefix="/crawler/instances", tags=["爬虫实例配置管理"])


@router.get("/", response_model=List[CrawlerInstanceConfig])
async def get_all_instance_configs():
    """获取所有爬虫实例配置"""
    try:
        configs = await crawler_instance_config_service.get_all_configs()
        return configs
    except Exception as e:
        logger.error(f"获取爬虫实例配置列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置列表失败: {str(e)}")


@router.post("/", response_model=CrawlerInstanceConfig)
async def create_instance_config(config_data: CrawlerInstanceConfigCreate):
    """创建爬虫实例配置"""
    try:
        config = await crawler_instance_config_service.create_config(config_data)
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建爬虫实例配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")


@router.get("/{config_id}", response_model=CrawlerInstanceConfig)
async def get_instance_config(config_id: str):
    """获取特定爬虫实例配置"""
    try:
        config = await crawler_instance_config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="爬虫实例配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取爬虫实例配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.put("/{config_id}", response_model=CrawlerInstanceConfig)
async def update_instance_config(config_id: str, update_data: CrawlerInstanceConfigUpdate):
    """更新爬虫实例配置"""
    try:
        config = await crawler_instance_config_service.update_config(config_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="爬虫实例配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新爬虫实例配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.delete("/{config_id}")
async def delete_instance_config(config_id: str):
    """删除爬虫实例配置"""
    try:
        success = await crawler_instance_config_service.delete_config(config_id)
        if not success:
            raise HTTPException(status_code=404, detail="爬虫实例配置不存在")
        return {"message": "爬虫实例配置删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除爬虫实例配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")


@router.get("/manager/info", response_model=CrawlerConfigManager)
async def get_config_manager():
    """获取配置管理器信息"""
    try:
        manager = await crawler_instance_config_service.get_config_manager()
        return manager
    except Exception as e:
        logger.error(f"获取配置管理器失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置管理器失败: {str(e)}")


@router.post("/{config_id}/set-default")
async def set_default_config(config_id: str):
    """设置默认配置"""
    try:
        success = await crawler_instance_config_service.set_default_config(config_id)
        if not success:
            raise HTTPException(status_code=404, detail="爬虫实例配置不存在")
        return {"message": "默认配置设置成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置默认配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"设置默认配置失败: {str(e)}")


@router.post("/{config_id}/test-connection", response_model=ConnectionTestResult)
async def test_instance_connection(config_id: str):
    """测试爬虫实例连接"""
    try:
        result = await crawler_instance_config_service.test_connection(config_id)
        return result
    except Exception as e:
        logger.error(f"测试连接失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试连接失败: {str(e)}")


@router.get("/default/config", response_model=CrawlerInstanceConfig)
async def get_default_config():
    """获取默认配置"""
    try:
        manager = await crawler_instance_config_service.get_config_manager()
        default_config = manager.get_default_config()
        
        if not default_config:
            raise HTTPException(status_code=404, detail="没有设置默认配置")
        
        return default_config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取默认配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取默认配置失败: {str(e)}")


@router.get("/active/list", response_model=List[CrawlerInstanceConfig])
async def get_active_configs():
    """获取活跃的配置列表"""
    try:
        manager = await crawler_instance_config_service.get_config_manager()
        active_configs = manager.get_active_configs()
        return active_configs
    except Exception as e:
        logger.error(f"获取活跃配置列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃配置列表失败: {str(e)}")


@router.get("/healthy/list", response_model=List[CrawlerInstanceConfig])
async def get_healthy_configs():
    """获取健康的配置列表"""
    try:
        manager = await crawler_instance_config_service.get_config_manager()
        healthy_configs = manager.get_healthy_configs()
        return healthy_configs
    except Exception as e:
        logger.error(f"获取健康配置列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取健康配置列表失败: {str(e)}")


@router.post("/batch/test-connections")
async def batch_test_connections():
    """批量测试所有配置的连接"""
    try:
        configs = await crawler_instance_config_service.get_all_configs()
        results = []
        
        for config in configs:
            result = await crawler_instance_config_service.test_connection(config.config_id)
            results.append(result)
        
        return {
            "total_configs": len(configs),
            "tested_configs": len(results),
            "successful_connections": sum(1 for r in results if r.is_connected),
            "failed_connections": sum(1 for r in results if not r.is_connected),
            "results": results
        }
    except Exception as e:
        logger.error(f"批量测试连接失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量测试连接失败: {str(e)}")


@router.post("/migrate/from-single-config")
async def migrate_from_single_config():
    """从单一配置迁移到实例配置"""
    try:
        # 这个功能在服务初始化时自动执行
        # 这里提供手动触发的接口
        manager = await crawler_instance_config_service._create_default_manager()
        return {
            "message": "配置迁移成功",
            "configs_count": len(manager.configs),
            "default_config_id": manager.default_config_id
        }
    except Exception as e:
        logger.error(f"配置迁移失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置迁移失败: {str(e)}")


@router.get("/stats/summary")
async def get_config_stats():
    """获取配置统计信息"""
    try:
        manager = await crawler_instance_config_service.get_config_manager()
        
        total_configs = len(manager.configs)
        active_configs = len(manager.get_active_configs())
        healthy_configs = len(manager.get_healthy_configs())
        
        # 计算平均健康评分
        if manager.configs:
            avg_health_score = sum(config.health_score for config in manager.configs) / total_configs
        else:
            avg_health_score = 0.0
        
        # 计算平均响应时间
        if manager.configs:
            avg_response_time = sum(config.avg_response_time for config in manager.configs) / total_configs
        else:
            avg_response_time = 0.0
        
        return {
            "total_configs": total_configs,
            "active_configs": active_configs,
            "healthy_configs": healthy_configs,
            "inactive_configs": total_configs - active_configs,
            "unhealthy_configs": total_configs - healthy_configs,
            "default_config_id": manager.default_config_id,
            "avg_health_score": round(avg_health_score, 2),
            "avg_response_time": round(avg_response_time, 2),
            "health_percentage": round((healthy_configs / total_configs * 100) if total_configs > 0 else 0, 1)
        }
    except Exception as e:
        logger.error(f"获取配置统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置统计失败: {str(e)}")
