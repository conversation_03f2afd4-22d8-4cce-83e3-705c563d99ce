# 爬虫API请求生成分析

## 概述

基于爬虫API的请求结构，分析各个字段的数据来源和生成逻辑，确保请求构建的合理性和一致性。

## 爬虫API请求结构

```json
{
  "request": {
    "urls": ["string"],
    "q": "string",
    "schema_str": "string", 
    "cache": false,
    "priority": "medium",
    "batch_name": "string",
    "batch_description": "string",
    "callback_url": "string",
    "max_concurrent_tasks": 0,
    "batch_timeout": 0
  },
  "auth_context": {
    "user_id": "string",
    "role": "user", 
    "permissions": [],
    "auth_method": "none",
    "api_key_prefix": "string",
    "client_ip": "string",
    "user_agent": "string",
    "authenticated_at": "2025-08-09T07:24:24.628236"
  }
}
```

## 字段数据来源分析

### request 部分

| 字段 | 数据来源 | 生成方式 | 说明 |
|------|----------|----------|------|
| `urls` | 监控任务的活跃URL | 从URL池获取 | 通过TaskStorageService获取任务关联的URL ID，再从UrlPoolService获取活跃状态的URL |
| `q` | 爬取配置 | 用户配置 | 来自爬取配置中的llm_query字段 |
| `schema_str` | 爬取配置 | 用户配置 | 来自爬取配置中的llm_schema字段 |
| `cache` | 爬取配置 | 用户配置 | 来自爬取配置中的cache_enabled字段 |
| `priority` | 爬取配置 | 用户配置 | 来自爬取配置中的priority字段 |
| `batch_name` | 自动生成 | 系统生成 | 基于任务名称和时间戳生成 |
| `batch_description` | 自动生成 | 系统生成 | 基于任务描述和执行信息生成 |
| `callback_url` | 系统配置 | 环境变量 | 来自CRAWLER_DEFAULT_CALLBACK_URL |
| `max_concurrent_tasks` | 系统配置 | 环境变量 | 来自CRAWLER_MAX_CONCURRENT_TASKS |
| `batch_timeout` | 系统配置 | 环境变量 | 来自CRAWLER_BATCH_TIMEOUT |

### auth_context 部分

| 字段 | 数据来源 | 生成方式 | 说明 |
|------|----------|----------|------|
| `user_id` | 系统配置 | 固定值 | "monit_system" |
| `role` | 系统配置 | 固定值 | "user" |
| `permissions` | 系统配置 | 固定值 | [] |
| `auth_method` | 爬取配置 | 用户配置 | 来自爬取配置中的auth_method字段 |
| `api_key_prefix` | 爬取配置 | 用户配置 | 来自爬取配置中的api_key_prefix字段 |
| `client_ip` | 系统检测 | 自动获取 | 从请求头获取客户端IP |
| `user_agent` | 爬取配置 | 用户配置 | 来自爬取配置中的user_agent字段 |
| `authenticated_at` | 自动生成 | 系统生成 | 当前时间戳 |

## 数据流程图

```
监控任务执行 → Celery Worker → 爬虫API请求构建

1. 获取任务配置
   ├── 从TaskStorageService获取监控任务配置
   ├── 从UrlPoolService获取活跃URL列表
   └── 从SystemConfigService获取系统级配置

2. 构建请求数据
   ├── request.urls ← 活跃URL列表
   ├── request.q ← 爬取配置.llm_query
   ├── request.schema_str ← 爬取配置.llm_schema
   ├── request.cache ← 爬取配置.cache_enabled
   ├── request.priority ← 爬取配置.priority
   ├── request.batch_name ← 自动生成
   ├── request.batch_description ← 自动生成
   ├── request.callback_url ← 系统配置
   ├── request.max_concurrent_tasks ← 系统配置
   └── request.batch_timeout ← 系统配置

3. 构建认证上下文
   ├── auth_context.user_id ← 固定值
   ├── auth_context.role ← 固定值
   ├── auth_context.permissions ← 固定值
   ├── auth_context.auth_method ← 爬取配置
   ├── auth_context.api_key_prefix ← 爬取配置
   ├── auth_context.client_ip ← 自动获取
   ├── auth_context.user_agent ← 爬取配置
   └── auth_context.authenticated_at ← 当前时间

4. 发送到爬虫API
```

## 实现建议

### 1. 创建请求构建服务

```python
# backend/app/services/crawler_request_builder.py

class CrawlerRequestBuilder:
    """爬虫API请求构建器"""
    
    @staticmethod
    async def build_crawler_request(
        task_id: str,
        urls: List[str],
        crawl_config: Dict[str, Any],
        execution_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """构建爬虫API请求"""
        
        # 获取系统配置
        system_config = SystemConfigService.get_crawler_config()
        
        # 生成批次信息
        batch_name = f"Task_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        batch_description = f"监控任务 {task_id} 的批次执行"
        
        # 构建请求
        request_data = {
            "request": {
                "urls": urls,
                "q": crawl_config.get("llm_query", ""),
                "schema_str": crawl_config.get("llm_schema", ""),
                "cache": crawl_config.get("cache_enabled", False),
                "priority": crawl_config.get("priority", "medium"),
                "batch_name": batch_name,
                "batch_description": batch_description,
                "callback_url": system_config.default_callback_url,
                "max_concurrent_tasks": system_config.max_concurrent_tasks,
                "batch_timeout": system_config.batch_timeout
            },
            "auth_context": {
                "user_id": "monit_system",
                "role": "user",
                "permissions": [],
                "auth_method": crawl_config.get("auth_method", "api_key"),
                "api_key_prefix": crawl_config.get("api_key_prefix", ""),
                "client_ip": execution_context.get("client_ip", "127.0.0.1"),
                "user_agent": crawl_config.get("user_agent", "MonIt-Crawler/1.0"),
                "authenticated_at": datetime.now().isoformat()
            }
        }
        
        return request_data
```

### 2. 更新Celery任务

```python
# backend/app/tasks/crawl_batch.py

@celery_app.task(bind=True)
def crawl_batch_task(self, task_id: str, urls: List[str], platform: str, options: Dict = None):
    """批量爬取任务"""
    
    # 获取爬取配置
    crawl_config = get_crawl_config_for_task(task_id)
    
    # 构建爬虫API请求
    crawler_request = CrawlerRequestBuilder.build_crawler_request(
        task_id=task_id,
        urls=urls,
        crawl_config=crawl_config,
        execution_context={
            "client_ip": "**********",  # Docker内部IP
            "celery_task_id": self.request.id
        }
    )
    
    # 发送到爬虫API
    crawler_api = CrawlerAPIService()
    result = await crawler_api.submit_batch_request(crawler_request)
    
    return result
```

### 3. URL获取优化

```python
# backend/app/services/task_url_service.py

class TaskUrlService:
    """任务URL服务"""
    
    @staticmethod
    async def get_active_urls_for_task(task_id: str) -> List[str]:
        """获取任务的活跃URL列表"""
        
        # 1. 从TaskStorageService获取任务关联的URL ID
        task_storage = TaskStorageService()
        url_ids = await task_storage.get_task_urls(task_id)
        
        # 2. 从UrlPoolService获取活跃URL
        redis_client = redis.from_url("redis://redis:6379/0")
        url_pool_service = UrlPoolService(redis_client)
        
        active_urls = []
        for url_id in url_ids:
            url_item = await url_pool_service.get_url_by_id(url_id)
            if url_item and url_item.status == "active":
                active_urls.append(url_item.url)
        
        await redis_client.close()
        return active_urls
```

## 配置分层总结

### 用户级配置（来自爬取配置）
- `q` (LLM查询指令)
- `schema_str` (数据提取Schema)
- `cache` (缓存设置)
- `priority` (任务优先级)
- `auth_method` (认证方法)
- `api_key_prefix` (API密钥前缀)
- `user_agent` (用户代理)

### 系统级配置（来自环境变量）
- `callback_url` (回调URL)
- `max_concurrent_tasks` (最大并发任务数)
- `batch_timeout` (批次超时时间)

### 自动生成字段
- `urls` (从监控任务的活跃URL获取)
- `batch_name` (基于任务ID和时间戳)
- `batch_description` (基于任务信息)
- `user_id` (固定为"monit_system")
- `role` (固定为"user")
- `permissions` (固定为空数组)
- `client_ip` (从执行环境获取)
- `authenticated_at` (当前时间戳)

## 实现状态

### ✅ 已完成
1. **CrawlerRequestBuilder服务**：已创建并通过测试 (6/6 测试通过)
2. **系统配置分离**：系统级配置已移至环境变量
3. **前端界面优化**：移除重复的优先级选项
4. **API格式统一**：创建和更新配置API返回格式一致
5. **enhanced_crawl_task**：新的Celery任务使用标准化请求构建 (4/4 测试通过)
6. **crawl_batch_task迁移**：更新现有批量爬取任务支持新的请求构建逻辑 (3/3 测试通过)
7. **monitoring_executor集成**：在监控执行器中使用CrawlerRequestBuilder (3/3 测试通过)
8. **完整数据流验证**：从监控任务到爬虫API的完整数据流测试 (4/4 测试通过)

### 📋 可选优化
1. **性能优化**：URL获取缓存和批量处理优化
2. **向后兼容性**：逐步迁移现有任务使用新的请求构建方式
3. **监控和日志**：增强请求构建过程的监控和日志记录

## 验证结果

### 🧪 测试结果
- ✅ **CrawlerRequestBuilder单元测试**：6/6 通过
- ✅ **enhanced_crawl_task测试**：4/4 通过
- ✅ **crawl_batch_enhanced测试**：3/3 通过
- ✅ **monitoring_executor_enhanced测试**：3/3 通过
- ✅ **完整数据流测试**：4/4 通过
- ✅ **系统配置API测试**：3/3 通过
- ✅ **爬虫配置表单测试**：3/3 通过
- ✅ **请求生成流程测试**：完整流程验证成功
- ✅ **综合测试**：20/20 测试全部通过

### 📊 生成的请求示例
```json
{
  "request": {
    "urls": ["https://articulo.mercadolibre.com.mx/MLM-1234567890-test-product-1"],
    "q": "提取商品标题、价格、描述信息",
    "schema_str": "{\"type\": \"object\", \"properties\": {...}}",
    "cache": true,
    "priority": "high",
    "batch_name": "Task_test-task-request-generation_20250809_123456",
    "batch_description": "监控任务 test-task-request-generation 的批次执行 - 3 URLs",
    "callback_url": "http://backend:8000/api/v1/crawler/callback",
    "max_concurrent_tasks": 2,
    "batch_timeout": 300
  },
  "auth_context": {
    "user_id": "monit_system",
    "role": "user",
    "permissions": [],
    "auth_method": "api_key",
    "api_key_prefix": "test_key_123",
    "client_ip": "*************",
    "user_agent": "MonIt-RequestTest/1.0",
    "authenticated_at": "2025-08-09T12:34:56"
  }
}
```

## 关键优势

1. **数据来源清晰**：每个字段的来源明确定义
2. **配置分离**：用户配置与系统配置完全分离
3. **标准化**：统一的请求构建逻辑
4. **可测试性**：完整的单元测试覆盖
5. **可维护性**：清晰的服务边界和职责分工
