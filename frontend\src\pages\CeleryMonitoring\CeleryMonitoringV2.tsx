import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Spin,
  Badge,
  Tooltip
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  FileTextOutlined,
  BarsOutlined,
  TeamOutlined
} from '@ant-design/icons';

// 导入子组件
import SystemStatusPanel from './components/SystemStatusPanel';
import LogStreamPanel from './components/LogStreamPanel';
import TaskManagementPanel from './components/TaskManagementPanel';
import BeatSchedulePanel from './components/BeatSchedulePanel';
import PerformancePanel from './components/PerformancePanel';

// 导入hooks
import { useSystemStatus } from './hooks/useSystemStatus';
import { useAutoRefresh } from './hooks/useAutoRefresh';

import './CeleryMonitoringV2.css';

const { Title, Text } = Typography;

interface SystemOverview {
  containers: {
    worker: { status: string; uptime: string };
    beat: { status: string; uptime: string };
  };
  redis: { connected: boolean; queues: number };
  tasks: { active: number; failed: number; pending: number };
  workers: { online: number; total: number };
}

const CeleryMonitoringV2: React.FC = () => {
  const [activeTab, setActiveTab] = useState('system');
  const [loading, setLoading] = useState(false);
  const [systemOverview, setSystemOverview] = useState<SystemOverview | null>(null);
  
  // 使用自定义hooks
  const { systemStatus, refreshSystemStatus } = useSystemStatus();
  const { autoRefresh, setAutoRefresh, refreshInterval, setRefreshInterval } = useAutoRefresh();

  // 刷新所有数据
  const handleRefresh = async () => {
    setLoading(true);
    try {
      await refreshSystemStatus();
      // 触发子组件刷新
      window.dispatchEvent(new CustomEvent('celery-refresh'));
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取系统概览数据
  const fetchSystemOverview = async () => {
    try {
      // 并行获取数据，但允许部分失败
      const [containersRes, statsRes] = await Promise.allSettled([
        fetch('/api/v1/celery/docker/containers'),
        fetch('/api/v1/celery/stats')
      ]);

      let containers: any[] = [];
      let stats: any = {};

      // 处理容器数据
      if (containersRes.status === 'fulfilled' && containersRes.value.ok) {
        try {
          containers = await containersRes.value.json();
        } catch (error) {
          console.warn('Failed to parse containers response:', error);
        }
      } else {
        console.warn('Failed to fetch containers:', containersRes);
      }

      // 处理统计数据
      if (statsRes.status === 'fulfilled' && statsRes.value.ok) {
        try {
          stats = await statsRes.value.json();
        } catch (error) {
          console.warn('Failed to parse stats response:', error);
        }
      } else {
        console.warn('Failed to fetch stats:', statsRes);
      }

      // 查找容器
      const workerContainer = containers.find((c: any) =>
        c.name && (c.name.includes('worker') || c.name.includes('celery'))
      );
      const beatContainer = containers.find((c: any) =>
        c.name && c.name.includes('beat')
      );

      setSystemOverview({
        containers: {
          worker: {
            status: workerContainer?.state || 'unknown',
            uptime: workerContainer?.uptime || 'unknown'
          },
          beat: {
            status: beatContainer?.state || 'unknown',
            uptime: beatContainer?.uptime || 'unknown'
          }
        },
        redis: {
          connected: statsRes.status === 'fulfilled' && statsRes.value.ok,
          queues: stats.total_queues || 0
        },
        tasks: {
          active: stats.active_tasks || 0,
          failed: stats.failed_tasks || 0,
          pending: stats.pending_tasks || 0
        },
        workers: {
          online: stats.workers_online || 0,
          total: stats.workers_online || 0
        }
      });
    } catch (error) {
      console.error('Failed to fetch system overview:', error);
      // 设置默认值，避免页面崩溃
      setSystemOverview({
        containers: {
          worker: { status: 'error', uptime: 'unknown' },
          beat: { status: 'error', uptime: 'unknown' }
        },
        redis: { connected: false, queues: 0 },
        tasks: { active: 0, failed: 0, pending: 0 },
        workers: { online: 0, total: 0 }
      });
    }
  };

  // 初始化和自动刷新
  useEffect(() => {
    fetchSystemOverview();
    handleRefresh();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchSystemOverview();
        if (activeTab === 'system') {
          refreshSystemStatus();
        }
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, activeTab]);

  // 获取系统状态指示器
  const getSystemStatusIndicator = () => {
    if (!systemOverview) return <Spin size="small" />;

    const isHealthy = 
      systemOverview.containers.worker.status === 'running' &&
      systemOverview.containers.beat.status === 'running' &&
      systemOverview.redis.connected;

    return (
      <Badge 
        status={isHealthy ? 'success' : 'error'} 
        text={isHealthy ? '系统正常' : '系统异常'}
      />
    );
  };

  // 标签页配置
  const tabItems = [
    {
      key: 'system',
      label: (
        <Space>
          <SettingOutlined />
          系统状态
          {systemOverview && (
            <Badge 
              count={systemOverview.tasks.failed} 
              size="small" 
              style={{ backgroundColor: '#ff4d4f' }}
            />
          )}
        </Space>
      ),
      children: <SystemStatusPanel systemOverview={systemOverview} />
    },
    {
      key: 'logs',
      label: (
        <Space>
          <FileTextOutlined />
          实时日志
        </Space>
      ),
      children: <LogStreamPanel />
    },
    {
      key: 'tasks',
      label: (
        <Space>
          <BarsOutlined />
          任务管理
          {systemOverview && (
            <Badge 
              count={systemOverview.tasks.active} 
              size="small" 
              style={{ backgroundColor: '#1890ff' }}
            />
          )}
        </Space>
      ),
      children: <TaskManagementPanel />
    },
    {
      key: 'beat',
      label: (
        <Space>
          <ClockCircleOutlined />
          Beat调度
        </Space>
      ),
      children: <BeatSchedulePanel />
    },
    {
      key: 'performance',
      label: (
        <Space>
          <TeamOutlined />
          性能监控
        </Space>
      ),
      children: <PerformancePanel />
    }
  ];

  return (
    <div className="celery-monitoring-v2">
      {/* 页面头部 */}
      <div className="monitoring-header">
        <Row justify="space-between" align="middle">
          <Col>
            <Space align="center">
              <Title level={2} style={{ margin: 0 }}>
                Celery监控与管理中心
              </Title>
              {getSystemStatusIndicator()}
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
                type="primary"
              >
                刷新
              </Button>
              <Tooltip title={autoRefresh ? '点击关闭自动刷新' : '点击开启自动刷新'}>
                <Button
                  icon={autoRefresh ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  type={autoRefresh ? 'default' : 'dashed'}
                >
                  {autoRefresh ? '自动刷新' : '手动模式'}
                </Button>
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 系统概览卡片 */}
      {systemOverview && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card size="small">
              <div className="overview-stat">
                <div className="stat-icon">
                  <TeamOutlined style={{ color: '#1890ff' }} />
                </div>
                <div className="stat-content">
                  <div className="stat-title">Worker状态</div>
                  <div className="stat-value">
                    {systemOverview.containers.worker.status === 'running' ? (
                      <Text type="success">运行中</Text>
                    ) : (
                      <Text type="danger">异常</Text>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <div className="overview-stat">
                <div className="stat-icon">
                  <ClockCircleOutlined style={{ color: '#52c41a' }} />
                </div>
                <div className="stat-content">
                  <div className="stat-title">Beat调度</div>
                  <div className="stat-value">
                    {systemOverview.containers.beat.status === 'running' ? (
                      <Text type="success">运行中</Text>
                    ) : (
                      <Text type="danger">异常</Text>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <div className="overview-stat">
                <div className="stat-icon">
                  <BarsOutlined style={{ color: '#faad14' }} />
                </div>
                <div className="stat-content">
                  <div className="stat-title">活跃任务</div>
                  <div className="stat-value">{systemOverview.tasks.active}</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <div className="overview-stat">
                <div className="stat-icon">
                  <WarningOutlined style={{ color: '#ff4d4f' }} />
                </div>
                <div className="stat-content">
                  <div className="stat-title">失败任务</div>
                  <div className="stat-value">{systemOverview.tasks.failed}</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </div>
  );
};

export default CeleryMonitoringV2;
