"""
任务执行管理服务
负责任务执行的记录、状态跟踪和与Worker的集成
"""

import json
import uuid
import logging
import redis
import os
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from ..schemas.crawler_worker import TaskExecutionRecord
from ..services.crawler_worker_service import crawler_worker_service

logger = logging.getLogger(__name__)


class TaskExecutionService:
    """任务执行管理服务"""
    
    def __init__(self):
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        
        # Redis键模式
        self.keys = {
            'execution': 'task_execution:{}',
            'worker_executions': 'task_execution:worker:{}',
            'task_executions': 'task_execution:task:{}',
            'active_executions': 'task_execution:active',
            'execution_stats': 'task_execution:stats'
        }
    
    async def start_task_execution(
        self, 
        task_id: str, 
        worker_id: str, 
        task_name: str,
        total_urls: int,
        priority: str = "normal"
    ) -> TaskExecutionRecord:
        """开始任务执行"""
        try:
            execution_id = f"exec_{uuid.uuid4().hex[:8]}"
            assignment_id = f"assign_{task_id}_{worker_id}"
            
            # 获取Worker信息
            worker = await crawler_worker_service.get_worker(worker_id)
            worker_name = worker.worker_name if worker else f"Worker {worker_id}"
            
            # 创建执行记录
            execution_record = TaskExecutionRecord(
                execution_id=execution_id,
                assignment_id=assignment_id,
                task_id=task_id,
                task_name=task_name,
                worker_id=worker_id,
                worker_name=worker_name,
                status="running",
                priority=priority,
                assigned_at=datetime.now(),
                started_at=datetime.now(),
                completed_at=None,
                total_urls=total_urls,
                processed_urls=0,
                success_urls=0,
                failed_urls=0,
                execution_time_seconds=None,
                avg_response_time_ms=None,
                throughput_per_minute=None,
                error_message=None,
                error_count=0,
                retry_count=0,
                memory_usage_mb=None,
                cpu_usage_percent=None
            )
            
            # 保存到Redis
            await self._save_execution_record(execution_record)
            
            # 添加到活跃执行列表
            self.redis_client.sadd(self.keys['active_executions'], execution_id)
            
            logger.info(f"Started task execution: {execution_id}")
            return execution_record
            
        except Exception as e:
            logger.error(f"Failed to start task execution: {e}")
            raise
    
    async def update_task_execution(
        self,
        execution_id: str,
        processed_urls: Optional[int] = None,
        success_urls: Optional[int] = None,
        failed_urls: Optional[int] = None,
        error_message: Optional[str] = None,
        memory_usage_mb: Optional[float] = None,
        cpu_usage_percent: Optional[float] = None
    ) -> bool:
        """更新任务执行状态"""
        try:
            execution_record = await self.get_execution_record(execution_id)
            if not execution_record:
                return False
            
            # 更新字段
            if processed_urls is not None:
                execution_record.processed_urls = processed_urls
            if success_urls is not None:
                execution_record.success_urls = success_urls
            if failed_urls is not None:
                execution_record.failed_urls = failed_urls
            if error_message is not None:
                execution_record.error_message = error_message
                execution_record.error_count += 1
            if memory_usage_mb is not None:
                execution_record.memory_usage_mb = memory_usage_mb
            if cpu_usage_percent is not None:
                execution_record.cpu_usage_percent = cpu_usage_percent
            
            # 计算吞吐量
            if execution_record.started_at and processed_urls is not None:
                elapsed_minutes = (datetime.now() - execution_record.started_at).total_seconds() / 60
                if elapsed_minutes > 0:
                    execution_record.throughput_per_minute = processed_urls / elapsed_minutes
            
            # 保存更新
            await self._save_execution_record(execution_record)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update task execution {execution_id}: {e}")
            return False
    
    async def complete_task_execution(
        self,
        execution_id: str,
        status: str = "completed",
        final_stats: Optional[Dict[str, Any]] = None
    ) -> bool:
        """完成任务执行"""
        try:
            execution_record = await self.get_execution_record(execution_id)
            if not execution_record:
                return False
            
            # 更新完成状态
            execution_record.status = status
            execution_record.completed_at = datetime.now()
            
            # 计算执行时间
            if execution_record.started_at:
                execution_record.execution_time_seconds = (
                    execution_record.completed_at - execution_record.started_at
                ).total_seconds()
            
            # 应用最终统计
            if final_stats:
                for key, value in final_stats.items():
                    if hasattr(execution_record, key):
                        setattr(execution_record, key, value)
            
            # 保存最终记录
            await self._save_execution_record(execution_record)
            
            # 从活跃执行列表移除
            self.redis_client.srem(self.keys['active_executions'], execution_id)
            
            # 记录到Worker服务
            await crawler_worker_service.record_task_execution(execution_record)
            
            logger.info(f"Completed task execution: {execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete task execution {execution_id}: {e}")
            return False
    
    async def get_execution_record(self, execution_id: str) -> Optional[TaskExecutionRecord]:
        """获取执行记录"""
        try:
            execution_key = self.keys['execution'].format(execution_id)
            execution_data = self.redis_client.get(execution_key)
            
            if not execution_data:
                return None
            
            execution_dict = json.loads(execution_data)
            return TaskExecutionRecord(**execution_dict)
            
        except Exception as e:
            logger.error(f"Failed to get execution record {execution_id}: {e}")
            return None
    
    async def get_active_executions(self) -> List[TaskExecutionRecord]:
        """获取所有活跃的执行"""
        try:
            active_ids = self.redis_client.smembers(self.keys['active_executions'])
            executions = []
            
            for execution_id in active_ids:
                execution = await self.get_execution_record(execution_id)
                if execution:
                    executions.append(execution)
            
            return executions
            
        except Exception as e:
            logger.error(f"Failed to get active executions: {e}")
            return []
    
    async def _save_execution_record(self, execution_record: TaskExecutionRecord):
        """保存执行记录到Redis"""
        try:
            execution_key = self.keys['execution'].format(execution_record.execution_id)
            execution_data = execution_record.dict()
            execution_json = json.dumps(execution_data, default=str, ensure_ascii=False)
            
            # 保存执行记录，设置30天过期
            self.redis_client.setex(execution_key, 30 * 24 * 3600, execution_json)
            
            # 添加到Worker执行索引
            worker_key = self.keys['worker_executions'].format(execution_record.worker_id)
            self.redis_client.lpush(worker_key, execution_record.execution_id)
            self.redis_client.expire(worker_key, 30 * 24 * 3600)
            
            # 添加到任务执行索引
            task_key = self.keys['task_executions'].format(execution_record.task_id)
            self.redis_client.lpush(task_key, execution_record.execution_id)
            self.redis_client.expire(task_key, 30 * 24 * 3600)
            
        except Exception as e:
            logger.error(f"Failed to save execution record: {e}")
            raise


# 全局服务实例
task_execution_service = TaskExecutionService()
