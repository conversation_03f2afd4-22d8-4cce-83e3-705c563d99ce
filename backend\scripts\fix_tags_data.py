#!/usr/bin/env python3
"""
修复Redis中错误存储的tags数据格式

将 "['tag']" 格式修复为 '["tag"]' 格式
"""

import asyncio
import redis
import json
import ast
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fix_tags_data():
    """修复Redis中的tags数据格式"""
    
    # 连接Redis
    redis_client = redis.from_url("redis://redis:6379/0")
    
    try:
        # 获取所有任务的基本信息键
        pattern = "monitoring_tasks:*:basic"
        keys = redis_client.keys(pattern)
        
        fixed_count = 0
        total_count = 0
        
        for key in keys:
            total_count += 1
            key_str = key.decode() if isinstance(key, bytes) else key
            
            # 获取tags字段
            tags_value = redis_client.hget(key_str, 'tags')
            
            if tags_value:
                tags_str = tags_value.decode() if isinstance(tags_value, bytes) else tags_value
                logger.info(f"检查任务 {key_str}, tags值: {repr(tags_str)}")
                
                # 检查是否是错误格式（Python字符串表示）
                if tags_str.startswith("['") and tags_str.endswith("']"):
                    try:
                        # 解析Python字符串表示
                        tags_list = ast.literal_eval(tags_str)
                        
                        # 转换为正确的JSON格式
                        correct_json = json.dumps(tags_list)
                        
                        # 更新Redis
                        redis_client.hset(key_str, 'tags', correct_json)
                        
                        logger.info(f"修复 {key_str}: {repr(tags_str)} → {repr(correct_json)}")
                        fixed_count += 1
                        
                    except (ValueError, SyntaxError) as e:
                        logger.error(f"无法解析 {key_str} 的tags值 {repr(tags_str)}: {e}")
                
                elif tags_str.startswith('["') and tags_str.endswith('"]'):
                    logger.info(f"任务 {key_str} 的tags格式已正确: {repr(tags_str)}")
                
                else:
                    logger.warning(f"任务 {key_str} 的tags格式未知: {repr(tags_str)}")
        
        logger.info(f"修复完成: 总共检查 {total_count} 个任务，修复 {fixed_count} 个")
        
    except Exception as e:
        logger.error(f"修复过程中出错: {e}")
    
    finally:
        redis_client.close()

if __name__ == "__main__":
    asyncio.run(fix_tags_data())
