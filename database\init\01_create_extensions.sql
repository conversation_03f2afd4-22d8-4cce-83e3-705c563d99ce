-- 创建必要的PostgreSQL扩展
-- 此脚本在数据库初始化时自动执行

-- 创建TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- 创建UUID扩展 (如果需要UUID类型)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建加密扩展 (用于密码哈希等)
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 设置时区
SET timezone = 'UTC';

-- 显示安装的扩展版本信息
SELECT 
    extname AS "Extension",
    extversion AS "Version"
FROM pg_extension 
WHERE extname IN ('timescaledb', 'uuid-ossp', 'pgcrypto')
ORDER BY extname; 