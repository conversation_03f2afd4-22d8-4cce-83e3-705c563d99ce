# 编辑任务功能设计

**创建时间**: 2025年7月5日  
**状态**: 设计中  

## 🎯 功能需求

### 1. 编辑任务的触发方式
- **任务列表页面**: 点击编辑按钮
- **任务详情页面**: 点击编辑任务按钮

### 2. 编辑任务的实现方式
有两种实现方式可选：

#### 方案A: 弹窗编辑（推荐）
- **优点**: 不离开当前页面，操作流畅
- **缺点**: 弹窗空间有限，复杂配置可能显示不全
- **适用**: 基本信息编辑

#### 方案B: 页面跳转编辑
- **优点**: 空间充足，可以展示完整的编辑界面
- **缺点**: 需要页面跳转，操作步骤较多
- **适用**: 复杂配置编辑

### 3. 可编辑的字段
```typescript
interface EditableTaskFields {
  // 基本信息
  name: string;           // 任务名称
  description?: string;   // 任务描述
  
  // 调度配置
  schedule: {
    type: 'daily' | 'weekly' | 'hourly';
    time?: string;        // 执行时间 (HH:mm)
    interval?: number;    // 间隔（小时）
    timezone: string;     // 时区
  };
  
  // 任务配置
  config: {
    platform: string;    // 平台类型
    priority: 'high' | 'medium' | 'low';
    retry_count: number;  // 重试次数
    timeout: number;      // 超时时间
    batch_size: number;   // 批次大小
  };
}
```

### 4. 编辑限制
- **运行中的任务**: 只能编辑基本信息，不能编辑调度和配置
- **已停止的任务**: 可以编辑所有字段
- **URL管理**: 在任务详情页面的URL管理Tab中处理

## 🏗️ 实现方案

### 方案选择: 弹窗编辑 + 页面跳转编辑
- **简单编辑**: 使用弹窗（任务名称、描述、基本配置）
- **高级编辑**: 跳转到详情页面进行完整编辑

### 技术实现

#### 1. 后端API接口
```python
PUT /api/v1/monitoring-tasks/{id}
```

#### 2. 前端组件结构
```
EditTaskModal.tsx           # 编辑任务弹窗组件
├── BasicInfoForm          # 基本信息表单
├── ScheduleConfigForm     # 调度配置表单
└── TaskConfigForm         # 任务配置表单
```

#### 3. 表单验证
- 任务名称: 必填，长度限制
- 执行时间: 时间格式验证
- 数值字段: 范围验证
- 平台类型: 枚举值验证

## 🔄 用户交互流程

### 编辑流程
1. **点击编辑按钮** → 打开编辑弹窗
2. **加载当前数据** → 预填充表单
3. **修改字段** → 实时验证
4. **提交保存** → 调用API更新
5. **更新成功** → 关闭弹窗，刷新列表

### 错误处理
- **网络错误**: 显示重试按钮
- **验证错误**: 高亮错误字段
- **权限错误**: 显示权限提示
- **冲突错误**: 显示冲突信息

## 📋 开发计划

### 阶段1: 后端API实现
1. 实现PUT接口
2. 添加数据验证
3. 支持部分字段更新
4. 处理运行中任务的限制

### 阶段2: 前端弹窗组件
1. 创建EditTaskModal组件
2. 实现表单布局
3. 添加字段验证
4. 集成API调用

### 阶段3: 集成和测试
1. 集成到任务列表页面
2. 集成到任务详情页面
3. 添加权限控制
4. 完善错误处理

---

**设计者**: Augment Agent  
**创建时间**: 2025年7月5日
