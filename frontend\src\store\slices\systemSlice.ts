import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SystemMetrics } from '../../types';
import { systemApi } from '../../services/api';

// 异步actions
export const fetchSystemMetrics = createAsyncThunk(
  'system/fetchSystemMetrics',
  async (params?: {
    start_time?: string;
    end_time?: string;
    interval?: string;
  }) => {
    const response = await systemApi.getSystemMetrics(params);
    return response;
  }
);

export const fetchSystemStatus = createAsyncThunk(
  'system/fetchSystemStatus',
  async () => {
    const response = await systemApi.getSystemStatus();
    return response;
  }
);

// State类型
interface SystemState {
  metrics: SystemMetrics[];
  currentMetrics: SystemMetrics | null;
  status: {
    status: string;
    uptime: number;
    version: string;
    services: Record<string, boolean>;
  } | null;
  loading: boolean;
  error: string | null;
  isConnected: boolean;
  lastUpdate: string | null;
}

// 初始状态
const initialState: SystemState = {
  metrics: [],
  currentMetrics: null,
  status: null,
  loading: false,
  error: null,
  isConnected: false,
  lastUpdate: null,
};

// Slice
const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    updateCurrentMetrics: (state, action: PayloadAction<SystemMetrics>) => {
      state.currentMetrics = action.payload;
      state.lastUpdate = new Date().toISOString();
      
      // 添加到历史记录，保持最近100条
      state.metrics.unshift(action.payload);
      if (state.metrics.length > 100) {
        state.metrics = state.metrics.slice(0, 100);
      }
    },
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearMetrics: (state) => {
      state.metrics = [];
      state.currentMetrics = null;
    },
  },
  extraReducers: (builder) => {
    // fetchSystemMetrics
    builder
      .addCase(fetchSystemMetrics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSystemMetrics.fulfilled, (state, action) => {
        state.loading = false;
        state.metrics = action.payload.data || [];
        if (state.metrics.length > 0) {
          state.currentMetrics = state.metrics[0];
        }
        state.lastUpdate = new Date().toISOString();
      })
      .addCase(fetchSystemMetrics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch system metrics';
      });

    // fetchSystemStatus
    builder
      .addCase(fetchSystemStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSystemStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.status = action.payload.data || null;
        state.isConnected = true;
      })
      .addCase(fetchSystemStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch system status';
        state.isConnected = false;
      });
  },
});

export const {
  updateCurrentMetrics,
  setConnectionStatus,
  clearError,
  clearMetrics,
} = systemSlice.actions;

export default systemSlice.reducer;
