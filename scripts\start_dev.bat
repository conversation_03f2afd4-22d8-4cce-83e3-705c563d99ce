@echo off
chcp 65001 >nul
echo ====================================================
echo 🚀 MonIt 爬虫系统 - 一键启动开发环境
echo ====================================================
echo.

cd /d "%~dp0\.."

echo 📦 检查环境...
where conda >nul 2>&1
if errorlevel 1 (
    echo ❌ Conda未安装或不在PATH中
    pause
    exit /b 1
)

where docker >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装或未启动
    pause
    exit /b 1
)

echo ✅ 基础环境检查通过

echo.
echo 🐳 启动数据库和Redis容器...
docker-compose -f docker-compose.dev.yml up timescaledb redis -d
if errorlevel 1 (
    echo ❌ Docker容器启动失败
    pause
    exit /b 1
)

echo.
echo ⏳ 等待数据库启动 (10秒)...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 启动前端服务...
cd frontend
start "MonIt Frontend" cmd /k "npm start"
echo ✅ 前端服务启动中...

echo.
echo 🐍 启动后端API服务...
cd ..\backend
start "MonIt Backend" cmd /k "call conda activate monit-crawler && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
echo ✅ 后端服务启动中...

cd ..
echo.
echo ====================================================
echo 🎉 开发环境启动完成！
echo ====================================================
echo 📌 访问地址:
echo    前端界面: http://localhost:3000
echo    后端API:  http://localhost:8000/docs
echo    健康检查: http://localhost:8000/health
echo.
echo 📝 注意: 前端和后端在独立窗口中运行
echo 关闭时请分别关闭各个服务窗口
echo ====================================================

timeout /t 5 /nobreak >nul
echo 按任意键关闭此窗口 (或等待5秒自动关闭)...
timeout /t 5 >nul 