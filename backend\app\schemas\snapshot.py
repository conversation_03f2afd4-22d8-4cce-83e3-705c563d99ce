"""
产品快照数据模式定义
"""
from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict, validator

from .common import BaseSchema


class ProductSnapshotBase(BaseModel):
    """产品快照基础模式"""
    
    # 价格信息
    current_price: Optional[Decimal] = Field(
        None, 
        description="当前价格",
        ge=0
    )
    original_price: Optional[Decimal] = Field(
        None, 
        description="原价",
        ge=0
    )
    discount_percentage: Optional[float] = Field(
        None, 
        description="折扣百分比",
        ge=0, 
        le=100
    )
    
    # 库存和销售信息
    stock_quantity: Optional[int] = Field(
        None, 
        description="库存数量",
        ge=0
    )
    sold_quantity: Optional[int] = Field(
        None, 
        description="已售数量",
        ge=0
    )
    
    # 评价信息
    rating_score: Optional[float] = Field(
        None, 
        description="评分",
        ge=0, 
        le=5
    )
    review_count: Optional[int] = Field(
        None, 
        description="评价数量",
        ge=0
    )
    
    # 商家信息
    seller_id: Optional[str] = Field(
        None, 
        description="卖家ID",
        max_length=100
    )
    seller_name: Optional[str] = Field(
        None, 
        description="卖家名称",
        max_length=200
    )
    seller_reputation: Optional[str] = Field(
        None, 
        description="卖家声誉",
        max_length=50
    )
    
    # 产品状态
    is_available: bool = Field(
        True, 
        description="是否可用"
    )
    is_promoted: bool = Field(
        False, 
        description="是否推广商品"
    )
    
    # 快照信息
    snapshot_time: datetime = Field(
        description="快照时间"
    )
    data_source: str = Field(
        description="数据来源",
        max_length=50
    )
    
    # 额外数据
    extra_data: Optional[Dict[str, Any]] = Field(
        None, 
        description="额外数据（JSON格式）"
    )
    
    @validator('snapshot_time', pre=True)
    def validate_snapshot_time(cls, v):
        """验证快照时间"""
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    @validator('discount_percentage')
    def validate_discount_percentage(cls, v, values):
        """验证折扣百分比"""
        if v is not None:
            current = values.get('current_price')
            original = values.get('original_price')
            if current and original and current > original:
                raise ValueError("当前价格不能高于原价")
        return v

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v)
        }
    )


class ProductSnapshotCreate(ProductSnapshotBase):
    """创建产品快照的数据模式"""
    
    product_id: int = Field(
        description="产品ID",
        gt=0
    )


class ProductSnapshotUpdate(BaseModel):
    """更新产品快照的数据模式"""
    
    # 价格信息
    current_price: Optional[Decimal] = Field(
        None, 
        ge=0
    )
    original_price: Optional[Decimal] = Field(
        None, 
        ge=0
    )
    discount_percentage: Optional[float] = Field(
        None, 
        ge=0, 
        le=100
    )
    
    # 库存和销售信息
    stock_quantity: Optional[int] = Field(
        None, 
        ge=0
    )
    sold_quantity: Optional[int] = Field(
        None, 
        ge=0
    )
    
    # 评价信息
    rating_score: Optional[float] = Field(
        None, 
        ge=0, 
        le=5
    )
    review_count: Optional[int] = Field(
        None, 
        ge=0
    )
    
    # 商家信息
    seller_id: Optional[str] = Field(
        None, 
        max_length=100
    )
    seller_name: Optional[str] = Field(
        None, 
        max_length=200
    )
    seller_reputation: Optional[str] = Field(
        None, 
        max_length=50
    )
    
    # 产品状态
    is_available: Optional[bool] = None
    is_promoted: Optional[bool] = None
    
    # 额外数据
    extra_data: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v)
        }
    )


class ProductSnapshotResponse(BaseSchema):
    """产品快照响应数据模式"""
    
    id: int = Field(description="快照ID")
    product_id: int = Field(description="产品ID")
    
    # 价格信息
    current_price: Optional[Decimal] = Field(description="当前价格")
    original_price: Optional[Decimal] = Field(description="原价")
    discount_percentage: Optional[float] = Field(description="折扣百分比")
    
    # 库存和销售信息
    stock_quantity: Optional[int] = Field(description="库存数量")
    sold_quantity: Optional[int] = Field(description="已售数量")
    
    # 评价信息
    rating_score: Optional[float] = Field(description="评分")
    review_count: Optional[int] = Field(description="评价数量")
    
    # 商家信息
    seller_id: Optional[str] = Field(description="卖家ID")
    seller_name: Optional[str] = Field(description="卖家名称")
    seller_reputation: Optional[str] = Field(description="卖家声誉")
    
    # 产品状态
    is_available: bool = Field(description="是否可用")
    is_promoted: bool = Field(description="是否推广商品")
    
    # 快照信息
    snapshot_time: datetime = Field(description="快照时间")
    data_source: str = Field(description="数据来源")
    
    # 额外数据
    extra_data: Optional[Dict[str, Any]] = Field(description="额外数据")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v)
        }
    )


class ProductSnapshotListResponse(BaseModel):
    """产品快照列表响应"""
    
    snapshots: List[ProductSnapshotResponse]
    total: int
    page: int = 1
    size: int = 10
    
    model_config = ConfigDict(from_attributes=True)


class ProductSnapshotQuery(BaseModel):
    """产品快照查询条件"""
    
    product_id: Optional[int] = Field(None, description="产品ID")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    data_source: Optional[str] = Field(None, description="数据来源")
    is_available: Optional[bool] = Field(None, description="是否可用")
    min_price: Optional[Decimal] = Field(None, description="最低价格", ge=0)
    max_price: Optional[Decimal] = Field(None, description="最高价格", ge=0)
    
    # 分页参数
    page: int = Field(1, description="页码", ge=1)
    size: int = Field(10, description="每页大小", ge=1, le=100)
    
    # 排序参数
    sort_by: str = Field("snapshot_time", description="排序字段")
    sort_order: str = Field("desc", description="排序顺序 (asc/desc)")
    
    @validator('end_time')
    def validate_time_range(cls, v, values):
        """验证时间范围"""
        start_time = values.get('start_time')
        if start_time and v and v <= start_time:
            raise ValueError("结束时间必须晚于开始时间")
        return v
    
    @validator('max_price')
    def validate_price_range(cls, v, values):
        """验证价格范围"""
        min_price = values.get('min_price')
        if min_price and v and v <= min_price:
            raise ValueError("最高价格必须高于最低价格")
        return v

    model_config = ConfigDict(from_attributes=True) 