import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Typography,
  Card,
  Button,
  Upload,
  Table,
  Tag,
  Progress,
  Input,
  Select,
  Space,
  Row,
  Col,
  Form,
  InputNumber,
  Switch,
  Divider,
  Modal,
  message,
  Tooltip,
  Badge,
  Statistic,
  Alert,
  Tabs,
  Dropdown,
  Collapse,
  Spin
} from 'antd';
import {
  UploadOutlined,
  InboxOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined,
  SearchOutlined,
  FileExcelOutlined,
  LinkOutlined,
  SettingOutlined,
  MonitorOutlined,
  MoreOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import type { UploadProps, TableColumnsType } from 'antd';
import './index.css';
import { parseExcelFile, validateUrls, getSupportedPlatforms, getTaskList, submitTask, getTaskDetailWithBatches, deleteTask, startTask, stopTask, type ExcelUploadResponse, type ExcelValidationResponse, type TaskSubmissionRequest, type TaskData, type BatchData } from '../../services/taskApi';
import { formatDate, getLocalTimeString, formatServerTime } from '../../utils';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { Dragger } = Upload;

// 类型定义
interface LinkData {
  id: string;
  url: string;
  title: string;
  status: 'valid' | 'invalid' | 'pending';
  platform: string;
  lastCheck: string;
}

// 使用导入的TaskData接口，不需要本地定义

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  source: string;
}

const TaskManager: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('create');
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [linkData, setLinkData] = useState<LinkData[]>([]);
  const [taskList, setTaskList] = useState<TaskData[]>([]);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [taskFilter, setTaskFilter] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isCreatingTask, setIsCreatingTask] = useState(false);
  const [taskLogs, setTaskLogs] = useState<Record<string, LogEntry[]>>({});
  const [uploadResponse, setUploadResponse] = useState<ExcelUploadResponse | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [selectedTaskDetail, setSelectedTaskDetail] = useState<TaskData | null>(null);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [taskConfig, setTaskConfig] = useState({
    platform: 'mercadolibre',
    priority: 'normal',
    batchSize: 10,
    delay: 2,
    retryCount: 3,
    enableProxy: false,
    enableJs: false
  });
  const [linkTablePageSize, setLinkTablePageSize] = useState(20); // 链接预览表格每页显示条数
  const fileInputRef = useRef<any>(null);

  // 检测屏幕尺寸 - 调整阈值为更大的断点
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024); // 提高到1024px
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);



  // 移动端任务卡片组件
  const MobileTaskCard: React.FC<{ task: TaskData }> = ({ task }) => {
    const getActionButton = () => {
      if (task.status === 'running') {
        return {
          icon: <PauseCircleOutlined />,
          text: '暂停',
          onClick: () => handleTaskAction(task.id, 'stop'),
          type: undefined
        };
      } else if (task.status === 'paused') {
        return {
          icon: <PlayCircleOutlined />,
          text: '恢复',
          onClick: () => handleTaskAction(task.id, 'start'),
          type: 'primary' as const
        };
      } else {
        return {
          icon: <PlayCircleOutlined />,
          text: '启动',
          onClick: () => handleTaskAction(task.id, 'start'),
          type: 'primary' as const
        };
      }
    };

    const actionButton = getActionButton();
    const statusConfig = {
      pending: { color: 'default', text: '待执行' },
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      paused: { color: 'warning', text: '已暂停' }
    };

    return (
      <Card
        className="mobile-task-card"
        size="small"
        style={{ marginBottom: 12 }}
      >
        <div className="mobile-task-header">
          <div className="mobile-task-info">
            <div className="mobile-task-name">{task.name}</div>
            <div className="mobile-task-meta">
              <Tag color={(statusConfig as any)[task.status]?.color}>
                {(statusConfig as any)[task.status]?.text}
              </Tag>
              <span className="mobile-task-platform">{task.platform}</span>
              <span className="mobile-task-time">
                {new Date(task.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        <div className="mobile-task-progress">
          <Progress
            percent={task.progress}
            size="small"
            status={task.status === 'failed' ? 'exception' : 'normal'}
          />
        </div>

        <div className="mobile-task-actions">
          <div className="mobile-action-row">
            <Button
              size="small"
              icon={actionButton.icon}
              type={actionButton.type}
              onClick={actionButton.onClick}
              className="mobile-action-btn"
            >
              {actionButton.text}
            </Button>
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewTaskDetail(task)}
              className="mobile-action-btn"
            >
              详情
            </Button>
          </div>
          <div className="mobile-action-row">
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteTask(task.id, task.name)}
              className="mobile-action-btn mobile-action-delete"
            >
              删除
            </Button>
          </div>
        </div>
      </Card>
    );
  };

  // 数据将从API获取，不再使用mock数据

  // 获取任务列表的函数
  const fetchTaskList = useCallback(async () => {
    try {
      // 调用真实API获取任务列表
      const response = await getTaskList();
      setTaskList(response);
      setTaskLogs({});
    } catch (error) {
      console.error('获取任务列表失败:', error);
      setTaskList([]);
      setTaskLogs({});
    }
  }, []);

  // 初始化数据 - 从API获取
  React.useEffect(() => {
    fetchTaskList();
  }, [fetchTaskList]);

  // 任务操作处理函数
  const handleTaskAction = useCallback(async (taskId: string, action: 'start' | 'stop') => {
    try {
      if (action === 'start') {
        await startTask(taskId);
        message.success('任务启动成功');
      } else {
        await stopTask(taskId);
        message.success('任务暂停成功');
      }

      // 刷新任务列表
      await fetchTaskList();
    } catch (error) {
      console.error(`Task ${action} error:`, error);
      message.error(`任务${action === 'start' ? '启动' : '暂停'}失败：${(error as Error).message}`);
    }
  }, [fetchTaskList]);

  // 重新验证URL
  const handleValidateUrls = useCallback(async () => {
    if (!linkData.length) {
      message.error('没有URL可以验证');
      return;
    }

    try {
      message.loading('正在验证URL...', 0);

      const urls = linkData.map(link => link.url);
      const response = await validateUrls(urls, taskConfig.platform);

      message.destroy();

      if (response.success) {
        // 更新链接状态
        const updatedLinkData = linkData.map(link => {
          const validationResult = response.validation_results?.find(url => url.url === link.url);
          return {
            ...link,
            status: validationResult?.status || link.status,
            lastCheck: new Date().toLocaleString()
          };
        });

        setLinkData(updatedLinkData);
        message.success(`URL验证完成！有效链接 ${response.valid_urls} 个，无效链接 ${response.invalid_urls} 个`);
      }
    } catch (error) {
      message.destroy();
      console.error('URL validation error:', error);
      message.error(`URL验证失败：${(error as Error).message}`);
    }
  }, [linkData, taskConfig.platform]);

  // 创建任务处理函数
  const handleCreateTask = useCallback(async () => {
    if (!uploadResponse || !linkData.length) {
      message.error('请先上传Excel文件并确保有有效链接');
      return;
    }

    try {
      setIsCreatingTask(true);

      // 提取有效的URL
      const validUrls = linkData
        .filter(link => link.status === 'valid')
        .map(link => link.url);

      if (validUrls.length === 0) {
        message.error('没有有效的URL可以创建任务');
        return;
      }

      // 构建任务提交请求
      const taskRequest: TaskSubmissionRequest = {
        urls: validUrls,
        platform: taskConfig.platform,
        priority: taskConfig.priority,
        options: {
          source: 'excel_upload',
          filename: uploadResponse.file_info?.filename,
          batch_size: taskConfig.batchSize,
          delay: taskConfig.delay,
          retry_count: taskConfig.retryCount,
          enable_proxy: taskConfig.enableProxy,
          enable_js: taskConfig.enableJs,
        }
      };

      // 提交任务
      const response = await submitTask(taskRequest);

      if (response.success) {
        // 创建新任务对象添加到任务列表
        const newTask: TaskData = {
          id: response.submission_id,
          name: `${uploadResponse.file_info?.filename || 'Excel任务'} - ${response.total_urls}个链接`,
          status: 'pending',  // 修复：任务创建时应该是pending状态，而不是running
          platform: taskConfig.platform,
          priority: taskConfig.priority,
          progress: 0,
          totalUrls: response.total_urls,
          completedUrls: 0,
          failedUrls: 0,
          createdAt: getLocalTimeString(),
          updatedAt: getLocalTimeString()
        };

        // 添加到任务列表（移除模拟数据）
        setTaskList(prevTasks => {
          // 如果是第一个真实任务，清除模拟数据
          const hasRealTasks = prevTasks.some(task => !task.id.startsWith('task-'));
          if (!hasRealTasks) {
            return [newTask];
          }
          return [newTask, ...prevTasks];
        });

        message.success(
          `任务创建成功！提交ID: ${response.submission_id}，创建了 ${response.batches_created} 个批次`
        );

        // 刷新任务列表
        await fetchTaskList();

        // 切换到任务列表页面
        setActiveTab('list');

        // 清理上传状态
        setUploadedFile(null);
        setUploadResponse(null);
        setLinkData([]);
        setTaskConfig({
          platform: 'mercadolibre',
          priority: 'normal',
          batchSize: 10,
          delay: 2,
          retryCount: 3,
          enableProxy: false,
          enableJs: false
        });
      } else {
        throw new Error(response.message || '任务创建失败');
      }

    } catch (error) {
      console.error('Task creation error:', error);
      message.error(`任务创建失败：${(error as Error).message}`);
    } finally {
      setIsCreatingTask(false);
    }
  }, [uploadResponse, linkData, taskConfig]);

  // 查看任务详情
  const handleViewTaskDetail = useCallback((task: TaskData) => {
    setSelectedTaskDetail(task);
    setTaskDetailVisible(true);
  }, []);

  // 删除任务
  const handleDeleteTask = useCallback((taskId: string, taskName: string) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除任务"${taskName}"吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteTask(taskId);
          message.success('任务删除成功');
          // 刷新任务列表
          await fetchTaskList();
        } catch (error) {
          console.error('Delete task error:', error);
          message.error(`删除任务失败：${(error as Error).message}`);
        }
      }
    });
  }, [fetchTaskList]);

  // 文件上传处理（仅解析，不创建任务）
  const handleFileUpload: UploadProps['customRequest'] = useCallback(async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      // 显示上传中状态
      message.loading('正在上传和解析Excel文件...', 0);

      // 调用新的Excel解析API（仅解析，不创建任务）
      const response: ExcelUploadResponse = await parseExcelFile(file as File);

      // 清除loading消息
      message.destroy();

      if (response.success) {
        setUploadedFile(file);
        setUploadResponse(response);

        // 使用后端返回的实际URL数据
        if (response.urls && response.urls.length > 0) {
          const linkData: LinkData[] = response.urls.map((urlData, index) => ({
            id: urlData.id,
            url: urlData.url,
            title: `商品 ${index + 1}`, // 可以后续从URL中提取标题
            status: urlData.status,
            platform: urlData.platform,
            lastCheck: new Date().toLocaleString(),
            row_index: urlData.row_index
          }));

          setLinkData(linkData);
        } else {
          // 如果没有URL数据，显示空列表
          setLinkData([]);
        }

        onSuccess?.(response);

        // 显示解析结果和警告信息
        let successMessage = `Excel文件解析成功！共解析 ${response.total_urls} 个链接，有效链接 ${response.valid_urls} 个`;

        if (response.warnings && response.warnings.length > 0) {
          successMessage += `\n注意：${response.warnings.join('，')}`;
        }

        message.success(successMessage);
      } else {
        throw new Error(response.message || '文件解析失败');
      }
    } catch (error) {
      message.destroy();
      console.error('Excel parse error:', error);
      onError?.(error as Error);
      message.error(`文件解析失败：${(error as Error).message}`);
    }
  }, []);

  // 定义标签页配置
  const tabItems = [
    {
      key: 'create',
      label: (
        <span>
          <FileExcelOutlined />
          创建任务
        </span>
      ),
      children: (
        <div className="task-creation-section">
          <Card
            title="任务创建向导"
            className="task-creation-card"
            extra={
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={isCreatingTask}
                disabled={!uploadedFile || linkData.length === 0}
                onClick={handleCreateTask}
              >
                创建任务
              </Button>
            }
          >
            {/* 文件上传区域 */}
            <div style={{ marginBottom: 24 }}>
              <Title level={4}>1. 上传Excel文件</Title>
              <Dragger
                name="file"
                multiple={false}
                accept=".xlsx,.xls"
                customRequest={handleFileUpload}
                showUploadList={false}
                className="upload-area"
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined className="upload-icon" />
                </p>
                <p className="upload-text">点击或拖拽文件到此区域上传</p>
                <p className="upload-hint">
                  支持 .xlsx 和 .xls 格式，无文件大小限制
                </p>
              </Dragger>

              {uploadedFile && uploadResponse && (
                <div style={{ marginTop: 16 }}>
                  <Alert
                    message={`已上传文件: ${uploadedFile.name}`}
                    description={
                      <div style={{ marginTop: 8 }}>
                        <Row gutter={16}>
                          <Col span={6}>
                            <Statistic 
                              title="总链接数" 
                              value={uploadResponse.total_urls} 
                              valueStyle={{ fontSize: 16 }}
                            />
                          </Col>
                          <Col span={6}>
                            <Statistic 
                              title="有效链接" 
                              value={uploadResponse.valid_urls} 
                              valueStyle={{ fontSize: 16, color: '#52c41a' }}
                            />
                          </Col>
                          <Col span={6}>
                            <Statistic 
                              title="无效链接" 
                              value={uploadResponse.invalid_urls} 
                              valueStyle={{ fontSize: 16, color: '#ff4d4f' }}
                            />
                          </Col>
                          <Col span={6}>
                            <div>
                              <div style={{ fontSize: 12, color: '#666' }}>平台分布</div>
                              <div style={{ fontSize: 14, marginTop: 4 }}>
                                {Object.entries(uploadResponse.platforms).map(([platform, count]) => (
                                  <Tag key={platform} color="blue" style={{ marginBottom: 4 }}>
                                    {platform}: {count}
                                  </Tag>
                                ))}
                              </div>
                            </div>
                          </Col>
                        </Row>
                      </div>
                    }
                    type="success"
                    showIcon
                    action={
                      <Button
                        size="small"
                        onClick={() => {
                          setUploadedFile(null);
                          setLinkData([]);
                          setUploadResponse(null);
                        }}
                      >
                        重新上传
                      </Button>
                    }
                  />
                </div>
              )}
            </div>

            {/* 链接预览区域 */}
            {linkData.length > 0 && (
              <div style={{ marginBottom: 24 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                  <Title level={4} style={{ margin: 0 }}>2. 链接预览</Title>
                  <Button
                    icon={<CheckCircleOutlined />}
                    onClick={handleValidateUrls}
                    type="default"
                  >
                    重新验证URL
                  </Button>
                </div>
                <div className="link-preview-stats">
                  <Row gutter={16}>
                    <Col span={6}>
                      <Card size="small">
                        <Statistic title="总链接数" value={linkData.length} />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card size="small">
                        <Statistic 
                          title="有效链接" 
                          value={linkData.filter(link => link.status === 'valid').length}
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card size="small">
                        <Statistic 
                          title="无效链接" 
                          value={linkData.filter(link => link.status === 'invalid').length}
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card size="small">
                        <Statistic 
                          title="待检查" 
                          value={linkData.filter(link => link.status === 'pending').length}
                          valueStyle={{ color: '#faad14' }}
                        />
                      </Card>
                    </Col>
                  </Row>
                </div>
                
                <Table
                  dataSource={linkData}
                  rowKey="id"
                  columns={[
                    {
                      title: 'URL',
                      dataIndex: 'url',
                      key: 'url',
                      ellipsis: true,
                      render: (url: string) => (
                        <a href={url} target="_blank" rel="noopener noreferrer">
                          {url}
                        </a>
                      )
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      width: 100,
                      render: (status: string) => {
                        const statusConfig = {
                          valid: { color: 'green', text: '有效' },
                          invalid: { color: 'red', text: '无效' },
                          pending: { color: 'orange', text: '待检查' }
                        };
                        const config = statusConfig[status as keyof typeof statusConfig];
                        return <Tag color={config.color}>{config.text}</Tag>;
                      }
                    },
                    {
                      title: '平台',
                      dataIndex: 'platform',
                      key: 'platform',
                      width: 120
                    }
                  ]}
                  pagination={{
                    pageSize: linkTablePageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50', '100'],
                    onShowSizeChange: (current, size) => setLinkTablePageSize(size)
                  }}
                  size="small"
                />
              </div>
            )}

            {/* 任务配置区域 */}
            {uploadResponse && linkData.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Title level={4}>3. 任务配置</Title>
                <Form layout="vertical">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="目标平台">
                        <Select
                          value={taskConfig.platform}
                          onChange={(value) => setTaskConfig(prev => ({ ...prev, platform: value }))}
                        >
                          <Option value="mercadolibre">MercadoLibre</Option>
                          <Option value="amazon">Amazon</Option>
                          <Option value="ebay">eBay</Option>
                          <Option value="aliexpress">AliExpress</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="任务优先级">
                        <Select
                          value={taskConfig.priority}
                          onChange={(value) => setTaskConfig(prev => ({ ...prev, priority: value }))}
                        >
                          <Option value="low">低优先级</Option>
                          <Option value="normal">普通优先级</Option>
                          <Option value="high">高优先级</Option>
                          <Option value="urgent">紧急优先级</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="批次大小">
                        <InputNumber
                          min={1}
                          max={50}
                          value={taskConfig.batchSize}
                          onChange={(value) => setTaskConfig(prev => ({ ...prev, batchSize: value || 10 }))}
                          addonAfter="个URL/批次"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="请求间隔">
                        <InputNumber
                          min={0.5}
                          max={10}
                          step={0.5}
                          value={taskConfig.delay}
                          onChange={(value) => setTaskConfig(prev => ({ ...prev, delay: value || 2 }))}
                          addonAfter="秒"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="重试次数">
                        <InputNumber
                          min={0}
                          max={5}
                          value={taskConfig.retryCount}
                          onChange={(value) => setTaskConfig(prev => ({ ...prev, retryCount: value || 3 }))}
                          addonAfter="次"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="高级选项">
                        <Space direction="vertical">
                          <Switch
                            checked={taskConfig.enableProxy}
                            onChange={(checked) => setTaskConfig(prev => ({ ...prev, enableProxy: checked }))}
                            checkedChildren="启用代理"
                            unCheckedChildren="禁用代理"
                          />
                          <Switch
                            checked={taskConfig.enableJs}
                            onChange={(checked) => setTaskConfig(prev => ({ ...prev, enableJs: checked }))}
                            checkedChildren="启用JS渲染"
                            unCheckedChildren="禁用JS渲染"
                          />
                        </Space>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </div>
            )}
          </Card>
        </div>
      )
    },
    {
      key: 'list',
      label: (
        <span>
          <SettingOutlined />
          任务列表
          <Badge count={taskList.filter(task => task.status === 'running').length} offset={[10, 0]} />
        </span>
      ),
      children: (
        <div className="task-list-section">
          <Card title="任务管理" className="task-list-card">
            {/* 任务操作栏 */}
            <div className="task-actions" style={{ marginBottom: 16 }}>
              <Row gutter={16} align="middle">
                <Col flex="auto">
                  <Space>
                    <Select
                      value={taskFilter}
                      onChange={setTaskFilter}
                      style={{ width: 120 }}
                    >
                      <Option value="all">全部任务</Option>
                      <Option value="running">运行中</Option>
                      <Option value="completed">已完成</Option>
                      <Option value="failed">失败</Option>
                      <Option value="paused">已暂停</Option>
                    </Select>
                    <Search
                      placeholder="搜索任务名称"
                      value={searchKeyword}
                      onChange={(e) => setSearchKeyword(e.target.value)}
                      style={{ width: 200 }}
                    />
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <Button icon={<ReloadOutlined />} onClick={fetchTaskList}>刷新</Button>
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      disabled={selectedTasks.length === 0}
                    >
                      批量启动
                    </Button>
                    <Button
                      icon={<PauseCircleOutlined />}
                      disabled={selectedTasks.length === 0}
                    >
                      批量暂停
                    </Button>
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      disabled={selectedTasks.length === 0}
                    >
                      批量删除
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            {/* 任务列表 - 响应式布局 */}
            {isMobile ? (
              // 移动端卡片布局
              <div className="mobile-task-list">
                {taskList.filter(task => {
                  const matchesFilter = taskFilter === 'all' || task.status === taskFilter;
                  const matchesSearch = !searchKeyword ||
                    task.name.toLowerCase().includes(searchKeyword.toLowerCase());
                  return matchesFilter && matchesSearch;
                }).map(task => (
                  <MobileTaskCard key={task.id} task={task} />
                ))}
                {taskList.length === 0 && (
                  <div className="empty-state">
                    <Alert
                      message="暂无任务"
                      description="请先上传Excel文件创建监控任务"
                      type="info"
                      showIcon
                    />
                  </div>
                )}
              </div>
            ) : (
              // 桌面端表格布局
              <Table
                dataSource={taskList.filter(task => {
                  const matchesFilter = taskFilter === 'all' || task.status === taskFilter;
                  const matchesSearch = !searchKeyword ||
                    task.name.toLowerCase().includes(searchKeyword.toLowerCase());
                  return matchesFilter && matchesSearch;
                })}
                rowKey="id"
                rowSelection={{
                  selectedRowKeys: selectedTasks,
                  onChange: (selectedRowKeys) => setSelectedTasks(selectedRowKeys as string[])
                }}
              columns={[
                {
                  title: '任务名称',
                  dataIndex: 'name',
                  key: 'name',
                  ellipsis: true
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => {
                    const statusConfig = {
                      pending: { color: 'default', text: '待执行' },
                      running: { color: 'processing', text: '运行中' },
                      completed: { color: 'success', text: '已完成' },
                      failed: { color: 'error', text: '失败' },
                      paused: { color: 'warning', text: '已暂停' }
                    };
                    const config = statusConfig[status as keyof typeof statusConfig];
                    return <Tag color={config.color}>{config.text}</Tag>;
                  }
                },
                {
                  title: '进度',
                  dataIndex: 'progress',
                  key: 'progress',
                  width: 150,
                  render: (progress: number, record: TaskData) => (
                    <div>
                      <Progress
                        percent={progress}
                        size="small"
                        status={record.status === 'failed' ? 'exception' : 'normal'}
                      />
                      <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                        {record.completedUrls}/{record.totalUrls} 完成
                      </div>
                    </div>
                  )
                },
                {
                  title: '平台',
                  dataIndex: 'platform',
                  key: 'platform',
                  width: 100
                },
                {
                  title: '创建时间',
                  dataIndex: 'createdAt',
                  key: 'createdAt',
                  width: 150,
                  render: (time: string) => formatServerTime(time)
                },
                {
                  title: '操作',
                  key: 'actions',
                  width: 160,
                  fixed: 'right' as const,
                  render: (_, record: TaskData) => {
                    const getActionButton = () => {
                      if (record.status === 'running') {
                        return {
                          icon: <PauseCircleOutlined />,
                          text: '暂停',
                          onClick: () => handleTaskAction(record.id, 'stop')
                        };
                      } else if (record.status === 'paused') {
                        return {
                          icon: <PlayCircleOutlined />,
                          text: '恢复',
                          onClick: () => handleTaskAction(record.id, 'start'),
                          type: 'primary' as const
                        };
                      } else {
                        return {
                          icon: <PlayCircleOutlined />,
                          text: '启动',
                          onClick: () => handleTaskAction(record.id, 'start'),
                          type: 'primary' as const
                        };
                      }
                    };

                    const actionButton = getActionButton();

                    const dropdownItems = [
                      {
                        key: 'view',
                        icon: <EyeOutlined />,
                        label: '查看详情'
                      },
                      {
                        key: 'delete',
                        icon: <DeleteOutlined />,
                        label: '删除任务',
                        danger: true
                      }
                    ];

                    return (
                      <div className="task-actions">
                        <Button
                          size="small"
                          icon={actionButton.icon}
                          type={actionButton.type}
                          onClick={actionButton.onClick}
                          title={actionButton.text}
                        >
                          <span>{actionButton.text}</span>
                        </Button>
                        <Dropdown
                          menu={{
                            items: dropdownItems,
                            onClick: ({ key }) => {
                              if (key === 'view') {
                                handleViewTaskDetail(record);
                              } else if (key === 'delete') {
                                handleDeleteTask(record.id, record.name);
                              }
                            }
                          }}
                          trigger={['click']}
                          placement="bottomRight"
                        >
                          <Button
                            size="small"
                            icon={<MoreOutlined />}
                            title="更多操作"
                          />
                        </Dropdown>
                      </div>
                    );
                  }
                }
              ]}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                pageSizeOptions: ['10', '20', '50', '100']
              }}
              />
            )}
          </Card>
        </div>
      )
    },
    {
      key: 'monitor',
      label: (
        <span>
          <MonitorOutlined />
          实时监控
        </span>
      ),
      children: (
        <div className="task-monitor-section">
          <Row gutter={[16, 16]}>
            {/* 任务概览 */}
            <Col span={24}>
              <Card title="任务概览" className="monitor-overview-card">
                <Row gutter={16}>
                  <Col span={6}>
                    <Card size="small">
                      <Statistic
                        title="总任务数"
                        value={taskList.length}
                        prefix={<SettingOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card size="small">
                      <Statistic
                        title="运行中"
                        value={taskList.filter(t => t.status === 'running').length}
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<PlayCircleOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card size="small">
                      <Statistic
                        title="已完成"
                        value={taskList.filter(t => t.status === 'completed').length}
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<SettingOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card size="small">
                      <Statistic
                        title="失败"
                        value={taskList.filter(t => t.status === 'failed').length}
                        valueStyle={{ color: '#ff4d4f' }}
                        prefix={<StopOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>
              </Card>
            </Col>

            {/* 运行中的任务详情 */}
            {taskList.filter(task => task.status === 'running').map(task => (
              <Col span={12} key={task.id}>
                <Card
                  title={task.name}
                  size="small"
                  extra={
                    <Tag color="processing">运行中</Tag>
                  }
                >
                  <div style={{ marginBottom: 16 }}>
                    <Progress
                      percent={task.progress}
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                    />
                    <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                      已完成: {task.completedUrls}/{task.totalUrls} 个链接
                    </div>
                  </div>

                  {taskLogs[task.id] && (
                    <div className="task-logs">
                      <div style={{ fontSize: 12, fontWeight: 600, marginBottom: 8 }}>
                        最新日志:
                      </div>
                      <div className="log-container">
                        {taskLogs[task.id].slice(-5).map((log, index) => (
                          <div key={index} className="log-entry">
                            <span className="log-time">{log.timestamp}</span>
                            <span className={`log-level ${log.level}`}>{log.level}</span>
                            <span className="log-message">{log.message}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      )
    }
  ];

  return (
    <div className="task-manager">
      {/* 页面头部 */}
      <div className="task-manager-header">
        <Title level={2}>任务管理</Title>
        <Paragraph>
          管理电商爬虫任务，支持Excel文件批量导入、实时监控任务进度、查看详细日志信息
        </Paragraph>
      </div>

      {/* 主要内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        items={tabItems}
      />

      {/* 任务详情模态框 */}
      <Modal
        title={`任务详情 - ${selectedTaskDetail?.name}`}
        open={taskDetailVisible}
        onCancel={() => setTaskDetailVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setTaskDetailVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {selectedTaskDetail && <TaskDetailContent task={selectedTaskDetail} />}
      </Modal>
    </div>
  );
};

// 任务详情内容组件
const TaskDetailContent: React.FC<{ task: TaskData }> = ({ task }) => {
  const [batches, setBatches] = useState<BatchData[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取任务详情数据
  useEffect(() => {
    const fetchTaskDetail = async () => {
      try {
        setLoading(true);
        const response = await getTaskDetailWithBatches(task.id);
        if (response.success) {
          setBatches(response.batches);
        }
      } catch (error) {
        console.error('Failed to fetch task detail:', error);
        message.error('获取任务详情失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTaskDetail();
  }, [task.id]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载任务详情中...</div>
      </div>
    );
  }



  return (
    <div className="task-detail-content">
      {/* 任务概览 */}
      <Card title="任务概览" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic title="总URL数量" value={task.totalUrls} />
          </Col>
          <Col span={6}>
            <Statistic title="已完成" value={task.completedUrls} valueStyle={{ color: '#3f8600' }} />
          </Col>
          <Col span={6}>
            <Statistic title="失败数量" value={task.failedUrls} valueStyle={{ color: '#cf1322' }} />
          </Col>
          <Col span={6}>
            <Statistic title="总进度" value={task.progress} suffix="%" />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={6}>
            <div><strong>任务状态:</strong> <Tag color={task.status === 'running' ? 'processing' : 'default'}>{task.status}</Tag></div>
          </Col>
          <Col span={6}>
            <div><strong>目标平台:</strong> {task.platform}</div>
          </Col>
          <Col span={6}>
            <div><strong>优先级:</strong> {task.priority}</div>
          </Col>
          <Col span={6}>
            <div><strong>批次数量:</strong> {batches.length} 个</div>
          </Col>
        </Row>
      </Card>

      {/* 批次详情 */}
      <Card title="批次执行详情" size="small">
        <Collapse
          items={batches.map((batch) => ({
            key: batch.id,
            label: (
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>
                  <Tag color={
                    batch.status === 'completed' ? 'success' :
                    batch.status === 'running' ? 'processing' :
                    batch.status === 'failed' ? 'error' : 'default'
                  }>
                    {batch.status === 'completed' ? '已完成' :
                     batch.status === 'running' ? '运行中' :
                     batch.status === 'failed' ? '失败' : '待执行'}
                  </Tag>
                  {batch.name} ({batch.completedUrls}/{batch.totalUrls})
                </span>
                <Progress percent={batch.progress} size="small" style={{ width: 100 }} />
              </div>
            ),
            children: (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <div><strong>开始时间:</strong> {batch.startTime || '未开始'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>结束时间:</strong> {batch.endTime || '进行中'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>执行状态:</strong> {batch.completedUrls}完成 / {batch.failedUrls}失败</div>
                    </Col>
                  </Row>
                </div>

                {/* URL详情表格 */}
                <Table
                  dataSource={batch.urls}
                  rowKey={(record) => `${batch.id}-${record.url}-${record.status || 'unknown'}`}
                  size="small"
                  pagination={false}
                  columns={[
                    {
                      title: 'URL',
                      dataIndex: 'url',
                      key: 'url',
                      ellipsis: true,
                      render: (url: string) => (
                        <a href={url} target="_blank" rel="noopener noreferrer" style={{ fontSize: '12px' }}>
                          {url}
                        </a>
                      )
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      width: 80,
                      render: (status: string) => (
                        <Tag
                          color={
                            status === 'completed' ? 'success' :
                            status === 'running' ? 'processing' :
                            status === 'failed' ? 'error' : 'default'
                          }
                        >
                          {status === 'completed' ? '完成' :
                           status === 'running' ? '运行' :
                           status === 'failed' ? '失败' : '待处理'}
                        </Tag>
                      )
                    },
                    {
                      title: '价格',
                      dataIndex: 'price',
                      key: 'price',
                      width: 80,
                      render: (price: string | null) => price || '-'
                    },
                    {
                      title: '最后检查',
                      dataIndex: 'lastCheck',
                      key: 'lastCheck',
                      width: 100,
                      render: (time: string | null) => time || '-'
                    }
                  ]}
                />
              </div>
            )
          }))}
        />
      </Card>
    </div>
  );
};

export default TaskManager;
