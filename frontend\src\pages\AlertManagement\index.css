.alert-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.alert-header {
  margin-bottom: 24px;
}

.alert-header .ant-typography-title {
  margin-bottom: 8px;
}

/* 统计卡片样式 */
.alert-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.alert-management .ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.alert-management .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格样式 */
.alert-management .ant-table {
  border-radius: 8px;
}

.alert-management .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.alert-management .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 严重程度标签样式 */
.alert-management .ant-tag {
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
}

/* 告警规则表单样式 */
.alert-rule-form .ant-form-item {
  margin-bottom: 16px;
}

.alert-rule-form .ant-form-item-label > label {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-management {
    padding: 16px;
  }
  
  .alert-management .ant-statistic-content {
    font-size: 20px;
  }
  
  .alert-management .ant-table {
    font-size: 12px;
  }
  
  .alert-management .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 告警状态指示器 */
.alert-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.alert-status-indicator .anticon {
  font-size: 16px;
}

/* 告警消息样式 */
.alert-message {
  max-width: 300px;
  word-break: break-word;
}

/* 告警时间样式 */
.alert-timestamp {
  font-size: 12px;
  color: #999;
}

/* 空状态样式 */
.alert-empty-state {
  text-align: center;
  padding: 40px 20px;
}

.alert-empty-state .anticon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

/* 告警规则开关样式 */
.alert-rule-switch {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 告警规则描述样式 */
.alert-rule-description {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}

/* 告警操作按钮样式 */
.alert-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.alert-actions .ant-btn {
  border-radius: 4px;
}

/* 告警统计卡片动画 */
.alert-management .ant-card {
  transition: all 0.3s ease;
}

.alert-management .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 告警表格行样式 */
.alert-table-row-critical {
  background-color: #fff2f0;
  border-left: 4px solid #ff4d4f;
}

.alert-table-row-error {
  background-color: #fff7e6;
  border-left: 4px solid #ff7a45;
}

.alert-table-row-warning {
  background-color: #fffbe6;
  border-left: 4px solid #fa8c16;
}

.alert-table-row-info {
  background-color: #f6ffed;
  border-left: 4px solid #1890ff;
}
