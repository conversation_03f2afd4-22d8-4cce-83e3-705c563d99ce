"""
MonIt应用监控指标收集器
提供Prometheus指标暴露和业务指标统计
"""

from prometheus_client import Counter, Histogram, Gauge, Info, generate_latest, CONTENT_TYPE_LATEST
from prometheus_client.core import CollectorRegistry
import time
import psutil
import asyncio
from typing import Dict, Any
from functools import wraps

# 创建自定义注册表
REGISTRY = CollectorRegistry()

# 系统信息指标
system_info = Info('monit_system_info', 'MonIt系统信息', registry=REGISTRY)
system_info.info({
    'version': '1.0.0',
    'environment': 'development',
    'service': 'monit-backend'
})

# HTTP请求指标
http_requests_total = Counter(
    'monit_http_requests_total',
    'HTTP请求总数',
    ['method', 'endpoint', 'status'],
    registry=REGISTRY
)

http_request_duration = Histogram(
    'monit_http_request_duration_seconds',
    'HTTP请求响应时间',
    ['method', 'endpoint'],
    registry=REGISTRY
)

# 爬虫任务指标
crawler_tasks_total = Counter(
    'monit_crawler_tasks_total',
    '爬虫任务总数',
    ['status'],
    registry=REGISTRY
)

crawler_task_duration = Histogram(
    'monit_crawler_task_duration_seconds',
    '爬虫任务执行时间',
    ['task_type'],
    registry=REGISTRY
)

crawler_urls_processed = Counter(
    'monit_crawler_urls_processed_total',
    '已处理URL总数',
    ['status'],
    registry=REGISTRY
)

# 数据库操作指标
db_operations_total = Counter(
    'monit_db_operations_total',
    '数据库操作总数',
    ['operation', 'table'],
    registry=REGISTRY
)

db_operation_duration = Histogram(
    'monit_db_operation_duration_seconds',
    '数据库操作耗时',
    ['operation', 'table'],
    registry=REGISTRY
)

db_connections_active = Gauge(
    'monit_db_connections_active',
    '活跃数据库连接数',
    registry=REGISTRY
)

# Redis操作指标
redis_operations_total = Counter(
    'monit_redis_operations_total',
    'Redis操作总数',
    ['operation'],
    registry=REGISTRY
)

redis_operation_duration = Histogram(
    'monit_redis_operation_duration_seconds',
    'Redis操作耗时',
    ['operation'],
    registry=REGISTRY
)

# 任务队列指标
task_queue_size = Gauge(
    'monit_task_queue_size',
    '任务队列大小',
    ['queue_name'],
    registry=REGISTRY
)

task_processing_time = Histogram(
    'monit_task_processing_time_seconds',
    '任务处理时间',
    ['task_type'],
    registry=REGISTRY
)

# 业务指标
products_monitored = Gauge(
    'monit_products_monitored_total',
    '监控商品总数',
    registry=REGISTRY
)

price_changes_detected = Counter(
    'monit_price_changes_detected_total',
    '检测到的价格变化总数',
    ['change_type'],
    registry=REGISTRY
)

# 系统资源指标
system_cpu_usage = Gauge(
    'monit_system_cpu_usage_percent',
    'CPU使用率',
    registry=REGISTRY
)

system_memory_usage = Gauge(
    'monit_system_memory_usage_percent',
    '内存使用率',
    registry=REGISTRY
)

system_disk_usage = Gauge(
    'monit_system_disk_usage_percent',
    '磁盘使用率',
    registry=REGISTRY
)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.start_time = time.time()
    
    def update_system_metrics(self):
        """更新系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            system_cpu_usage.set(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            system_memory_usage.set(memory.percent)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            system_disk_usage.set(disk_percent)
            
        except Exception as e:
            print(f"更新系统指标失败: {e}")
    
    def record_http_request(self, method: str, endpoint: str, status: int, duration: float):
        """记录HTTP请求指标"""
        http_requests_total.labels(method=method, endpoint=endpoint, status=str(status)).inc()
        http_request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_crawler_task(self, status: str, duration: float = None, task_type: str = "default"):
        """记录爬虫任务指标"""
        crawler_tasks_total.labels(status=status).inc()
        if duration is not None:
            crawler_task_duration.labels(task_type=task_type).observe(duration)
    
    def record_url_processed(self, status: str):
        """记录URL处理指标"""
        crawler_urls_processed.labels(status=status).inc()
    
    def record_db_operation(self, operation: str, table: str, duration: float):
        """记录数据库操作指标"""
        db_operations_total.labels(operation=operation, table=table).inc()
        db_operation_duration.labels(operation=operation, table=table).observe(duration)
    
    def set_db_connections(self, count: int):
        """设置活跃数据库连接数"""
        db_connections_active.set(count)
    
    def record_redis_operation(self, operation: str, duration: float):
        """记录Redis操作指标"""
        redis_operations_total.labels(operation=operation).inc()
        redis_operation_duration.labels(operation=operation).observe(duration)
    
    def set_queue_size(self, queue_name: str, size: int):
        """设置队列大小"""
        task_queue_size.labels(queue_name=queue_name).set(size)
    
    def record_task_processing(self, task_type: str, duration: float):
        """记录任务处理时间"""
        task_processing_time.labels(task_type=task_type).observe(duration)
    
    def set_products_monitored(self, count: int):
        """设置监控商品数量"""
        products_monitored.set(count)
    
    def record_price_change(self, change_type: str):
        """记录价格变化"""
        price_changes_detected.labels(change_type=change_type).inc()


# 全局指标收集器实例
metrics_collector = MetricsCollector()


def monitor_http_request(func):
    """HTTP请求监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        method = kwargs.get('method', 'GET')
        endpoint = kwargs.get('endpoint', 'unknown')
        
        try:
            result = await func(*args, **kwargs)
            status = getattr(result, 'status_code', 200)
            duration = time.time() - start_time
            metrics_collector.record_http_request(method, endpoint, status, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            metrics_collector.record_http_request(method, endpoint, 500, duration)
            raise
    
    return wrapper


def monitor_db_operation(operation: str, table: str):
    """数据库操作监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                metrics_collector.record_db_operation(operation, table, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_db_operation(operation, table, duration)
                raise
        return wrapper
    return decorator


def get_metrics():
    """获取Prometheus格式的指标数据"""
    # 更新系统指标
    metrics_collector.update_system_metrics()
    
    # 生成指标数据
    return generate_latest(REGISTRY)


def get_metrics_content_type():
    """获取指标内容类型"""
    return CONTENT_TYPE_LATEST
