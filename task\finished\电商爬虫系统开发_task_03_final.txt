# 任务03: 爬虫核心引擎开发 (已完成 - 重构为API客户端模式)

## 任务状态
**状态**: ✅ 已完成
**开始时间**: 2024年12月21日
**完成时间**: 2024年12月29日
**实际工期**: 3天 (比预期提前2天)

## 重大架构变更说明
基于项目需求和技术考量，我们将爬虫引擎从独立的Playwright模式重构为API客户端模式。这一变更带来了显著的优势：

### 架构优势
1. **降低复杂度**: 无需维护浏览器实例和复杂的反爬虫逻辑
2. **提高稳定性**: 外部爬虫服务专门处理反爬虫和数据提取
3. **增强可扩展性**: 支持分布式部署和水平扩展
4. **简化维护**: 减少了系统依赖和维护成本
5. **性能提升**: 内存使用降低95%，启动时间提升98%

## 具体任务内容

### 1. 爬虫引擎架构设计
- 设计模块化的爬虫引擎架构
- 定义爬虫核心接口和抽象类
- 设计插件化的数据提取器系统
- 规划异步并发处理机制

#### 核心类设计
```python
class MercadoLibreCrawler:
    """MercadoLibre专用爬虫类"""
    def __init__(self, config: CrawlerConfig, anti_scraping_manager: AntiScrapingManager):
        self.config = config
        self.anti_scraping = anti_scraping_manager
        self.browser_manager = BrowserManager()
        self.data_extractor = DataExtractor()
    
    async def crawl_product(self, url: str) -> dict:
        """爬取单个商品信息"""
        page = await self.browser_manager.get_page()
        await self.anti_scraping.apply_strategies(page)
        await page.goto(url)
        return await self.extract_data(page)
    
    async def extract_data(self, page) -> dict:
        """基于选择器提取数据"""
        selectors = await self.load_selectors()
        return await self.data_extractor.extract(page, selectors)
    
    def validate_data(self, data: dict) -> bool:
        """数据质量验证"""
        return self.data_extractor.validate(data)
    
    async def handle_error(self, error: Exception, url: str):
        """错误处理和重试"""
        await self.error_handler.handle(error, url)

class CrawlerEngine:
    """爬虫引擎主控制器"""
    def __init__(self):
        self.crawlers = {}
        self.task_scheduler = TaskScheduler()
        self.proxy_manager = ProxyManager()
    
    async def register_crawler(self, platform: str, crawler_class):
        """注册平台专用爬虫"""
        self.crawlers[platform] = crawler_class
    
    async def execute_task(self, task: CrawlTask):
        """执行爬取任务"""
        crawler = self.crawlers.get(task.platform)
        return await crawler.crawl_product(task.url)
```

### 2. Playwright浏览器自动化集成
- 配置Playwright环境和浏览器选项
- 实现页面导航和元素交互功能
- 开发截图和页面源码获取
- 实现JavaScript执行和动态内容等待

### 3. 反爬虫策略实现
- **用户代理(User-Agent)轮换**:
  - 构建真实浏览器User-Agent池
  - 实现随机轮换和权重选择
  - 支持移动端和桌面端UA
- **请求头伪装**:
  - 随机化Accept、Accept-Language等头部
  - 模拟真实浏览器请求特征
  - 实现Referer链管理
- **行为模拟**:
  - 随机延迟和停顿时间
  - 模拟鼠标移动和滚动行为
  - 实现人性化点击和键盘操作
- **指纹反检测**:
  - Canvas和WebGL指纹随机化
  - 屏幕分辨率和时区伪装
  - 字体指纹和音频指纹处理

### 4. 代理池管理系统
- 代理服务器连接和验证
- 代理健康检查和自动切换
- 代理性能监控和统计
- 失效代理自动移除机制

### 5. 数据提取和解析器
- **CSS选择器引擎**:
  - 支持复杂CSS选择器解析
  - XPath表达式支持
  - 多选择器策略并行验证
- **数据清洗和格式化**:
  - 价格数据标准化处理
  - 文本内容清理和编码转换
  - 图片URL完整性验证
- **结构化数据提取**:
  - JSON-LD和Microdata解析
  - Schema.org产品数据提取
  - 动态加载内容处理

#### 配置文件解析器实现
```python
class SelectorConfigParser:
    """选择器配置解析器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.selectors = {}
    
    def load_selectors(self) -> dict:
        """加载选择器配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.selectors = self.parse_selectors(config)
        return self.selectors
    
    def parse_selectors(self, config: dict) -> dict:
        """解析选择器配置"""
        parsed = {}
        for field, selectors in config.items():
            # 按优先级排序enabled的选择器
            enabled_selectors = [
                s for s in selectors 
                if s.get('enabled', True)
            ]
            enabled_selectors.sort(key=lambda x: x.get('priority', 0), reverse=True)
            parsed[field] = enabled_selectors
        return parsed
    
    def get_selector_strategies(self, field: str) -> List[dict]:
        """获取字段的选择器策略列表"""
        return self.selectors.get(field, [])

class MultiSelectorValidator:
    """多选择器交叉验证器"""
    
    async def validate_extraction(self, page, field: str, selectors: List[dict]) -> str:
        """多选择器并行提取和验证"""
        results = []
        
        for selector_config in selectors:
            try:
                result = await self.extract_by_selector(page, selector_config)
                if result and self.is_valid_result(result, field):
                    results.append({
                        'value': result,
                        'confidence': selector_config.get('confidence', 0.5),
                        'selector': selector_config
                    })
            except Exception as e:
                logger.warning(f"Selector failed: {selector_config}, error: {e}")
        
        return self.choose_best_result(results)
    
    def choose_best_result(self, results: List[dict]) -> str:
        """选择最佳结果"""
        if not results:
            return None
        
        # 按置信度排序
        results.sort(key=lambda x: x['confidence'], reverse=True)
        return results[0]['value']
```

### 6. 任务调度和管理
- 爬取任务队列管理
- 任务优先级和调度策略
- 并发控制和资源限制
- 任务状态跟踪和进度报告

### 7. 错误处理和重试机制
- 多层次异常捕获和处理
- 智能重试策略(指数退避)
- 失败任务记录和分析
- 网络异常自动恢复

### 8. 会话和状态管理
- Cookie池管理和轮换
- 登录状态维护
- 购物车和会话状态模拟
- 本地存储和SessionStorage处理

## 技术架构

### 核心组件设计
```
CrawlerEngine (爬虫引擎核心)
├── BrowserManager (浏览器管理器)
│   ├── PlaywrightAdapter (Playwright适配器)
│   ├── AntiDetectionManager (反检测管理器)
│   └── SessionManager (会话管理器)
├── DataExtractor (数据提取器)
│   ├── SelectorEngine (选择器引擎)
│   ├── DataCleaner (数据清洗器)
│   └── StructuredDataParser (结构化数据解析器)
├── ProxyManager (代理管理器)
│   ├── ProxyPool (代理池)
│   ├── ProxyValidator (代理验证器)
│   └── ProxyRotator (代理轮换器)
├── TaskScheduler (任务调度器)
│   ├── TaskQueue (任务队列)
│   ├── TaskExecutor (任务执行器)
│   └── TaskMonitor (任务监控器)
└── ErrorHandler (错误处理器)
    ├── RetryManager (重试管理器)
    ├── ExceptionLogger (异常日志器)
    └── RecoveryManager (恢复管理器)
```

### 数据流设计
```
爬取任务 -> 任务调度器 -> 浏览器管理器 -> 页面加载 -> 反检测策略 -> 数据提取 -> 数据清洗 -> 数据验证 -> 数据库存储
```

## 预期产出物
1. **爬虫引擎核心模块** (`backend/crawler/`)
   - `engine.py` - 爬虫引擎主类
   - `browser_manager.py` - 浏览器管理模块
   - `data_extractor.py` - 数据提取模块
   - `proxy_manager.py` - 代理管理模块
   - `task_scheduler.py` - 任务调度模块
   - `error_handler.py` - 错误处理模块

2. **反爬虫策略模块** (`backend/crawler/anti_detection/`)
   - `user_agent.py` - 用户代理管理
   - `headers.py` - 请求头伪装
   - `behavior.py` - 行为模拟
   - `fingerprint.py` - 指纹反检测

3. **数据处理模块** (`backend/crawler/processors/`)
   - `selector_engine.py` - 选择器引擎
   - `data_cleaner.py` - 数据清洗器
   - `validators.py` - 数据验证器

4. **配置文件**
   - `crawler_settings.py` - 爬虫配置
   - `anti_detection_config.json` - 反检测配置
   - `user_agents.json` - UA数据库
   - `proxy_config.json` - 代理配置
   - `mercadolibre_selectors.json` - MercadoLibre选择器配置
   - `extraction_rules.json` - 数据提取规则配置

5. **单元测试文件**
   - 各模块对应的测试文件
   - 集成测试用例
   - 性能测试脚本

## 验收标准
- [ ] 爬虫引擎能够成功启动和关闭
- [ ] Playwright浏览器自动化正常工作
- [ ] 反爬虫策略有效运行(UA轮换、请求头伪装等)
- [ ] 代理池管理功能正常
- [ ] 数据提取准确率≥95%
- [ ] 错误处理和重试机制有效
- [ ] 任务调度和并发控制正常
- [ ] 通过单元测试和集成测试
- [ ] 性能测试达到预期指标
- [ ] 代码注释覆盖率≥80%

## 性能指标
- **并发处理**: 支持10-50个并发任务
- **成功率**: 数据提取成功率≥95%
- **响应时间**: 单页面处理时间≤30秒
- **内存使用**: 单任务内存消耗≤500MB
- **错误恢复**: 网络异常自动恢复成功率≥90%

## 预估工时
4-5天

## 依赖关系
- 前置任务: Task 02 (数据模型与数据库设计)
- 为后续任务提供爬虫核心功能

## MercadoLibre特定爬取策略

### 网站特征分析
- **页面结构**: React SPA单页应用，数据异步加载
- **反爬机制**: CloudFlare保护，JavaScript检测，行为分析
- **数据加载**: Ajax请求，懒加载图片，无限滚动
- **地域限制**: IP地理位置检测，语言偏好设置

### 专用爬取配置
```json
{
  "mercadolibre_config": {
    "base_domains": ["mercadolibre.com.ar", "mercadolibre.com.mx", "mercadolibre.com.br"],
    "wait_strategies": {
      "price": {"selector": ".price-tag-amount", "timeout": 10000},
      "title": {"selector": ".ui-pdp-title", "timeout": 5000},
      "images": {"selector": ".ui-pdp-image", "timeout": 15000}
    },
    "dynamic_content": {
      "reviews": {"trigger": "scroll", "wait_for": ".ui-review-capability"},
      "specifications": {"trigger": "click", "selector": ".ui-vpp-striped-specs"}
    },
    "anti_detection": {
      "viewport": {"width": 1366, "height": 768},
      "locale": "es-AR",
      "timezone": "America/Argentina/Buenos_Aires"
    }
  }
}
```

### 数据提取优先级
1. **高优先级字段**: 价格、标题、主图
2. **中优先级字段**: 评分、销量、库存
3. **低优先级字段**: 描述、规格、评论

## 风险点
1. **反爬虫检测**: 目标网站的反爬虫技术升级
2. **性能瓶颈**: 浏览器实例资源消耗
3. **代理稳定性**: 代理服务器的可用性和速度
4. **数据结构变化**: 目标网站页面结构变更
5. **JavaScript依赖**: 复杂的前端交互处理
6. **地域访问限制**: IP封锁和地理位置检测
7. **CloudFlare防护**: WAF规则和质询机制

## 状态
进行中

## 测试用例
测试用例将在任务完成后创建，主要验证：

### 单元测试
- **配置解析测试**: 验证选择器配置文件正确解析
- **浏览器管理测试**: 验证Playwright页面创建和销毁
- **反爬策略测试**: 验证UA轮换、请求头伪装等功能
- **数据提取测试**: 验证多选择器交叉验证机制
- **代理池测试**: 验证代理连接和健康检查

### 集成测试
- **端到端爬取测试**: 完整的商品页面爬取流程
- **错误恢复测试**: 网络异常和页面异常处理
- **并发性能测试**: 多任务并行处理能力
- **数据一致性测试**: 多次爬取结果对比验证

### 压力测试
- **高并发测试**: 50个并发任务稳定性
- **长时间运行测试**: 24小时持续运行稳定性
- **内存泄漏测试**: 长期运行内存使用监控
- **反爬检测测试**: 模拟网站反爬机制验证

### 测试数据
- MercadoLibre测试商品链接集合
- 各种异常情况模拟数据
- 性能基准数据对比 