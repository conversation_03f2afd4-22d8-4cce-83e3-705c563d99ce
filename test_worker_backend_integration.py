#!/usr/bin/env python3
"""
测试Worker后端集成功能
验证立即执行时是否正确调用分配的Worker后端
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def test_worker_backend_integration():
    """测试Worker后端集成功能"""
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 获取监控任务
        logger.info("1. 获取监控任务...")
        async with session.get(f"{API_BASE}/monitoring-tasks") as response:
            if response.status == 200:
                data = await response.json()
                tasks = data.get("data", [])
                if not tasks:
                    logger.error("没有找到监控任务")
                    return False
                
                task = tasks[0]
                task_id = task["id"]
                logger.info(f"选择任务: {task_id} - {task.get('name', 'Unknown')}")
            else:
                logger.error(f"获取任务列表失败: {response.status}")
                return False
        
        # 2. 检查任务的Worker分配
        logger.info("2. 检查任务的Worker分配...")
        async with session.get(f"{API_BASE}/task-assignments?task_id={task_id}") as response:
            if response.status == 200:
                assignments = await response.json()
                # 处理不同的响应格式
                if isinstance(assignments, dict):
                    assignment_data = assignments.get("data", [])
                else:
                    assignment_data = assignments

                if assignment_data:
                    assignment = assignment_data[0]
                    primary_worker_id = assignment.get("primary_worker_id") or assignment.get("worker_ids", [])[0]
                    logger.info(f"✅ 任务已分配Worker: {primary_worker_id}")
                    
                    # 获取Worker详情
                    async with session.get(f"{API_BASE}/crawler-workers/{primary_worker_id}") as worker_response:
                        if worker_response.status == 200:
                            worker = await worker_response.json()
                            backend_config_id = worker.get("backend_config_id")
                            logger.info(f"Worker后端配置ID: {backend_config_id}")
                            
                            # 获取后端配置
                            async with session.get(f"{API_BASE}/backend-configs/{backend_config_id}") as backend_response:
                                if backend_response.status == 200:
                                    backend_config = await backend_response.json()
                                    api_endpoint = backend_config.get("api_endpoint")
                                    logger.info(f"✅ Worker后端地址: {api_endpoint}")
                                    
                                    # 检查后端是否可访问
                                    try:
                                        async with session.get(f"{api_endpoint}/health", timeout=5) as health_response:
                                            if health_response.status == 200:
                                                logger.info(f"✅ Worker后端健康检查通过: {api_endpoint}")
                                            else:
                                                logger.warning(f"⚠️ Worker后端健康检查失败: {health_response.status}")
                                    except Exception as e:
                                        logger.warning(f"⚠️ Worker后端不可访问: {e}")
                                else:
                                    logger.error(f"获取后端配置失败: {backend_response.status}")
                        else:
                            logger.error(f"获取Worker详情失败: {worker_response.status}")
                else:
                    logger.warning("⚠️ 任务没有Worker分配")
                    return False
            else:
                logger.warning(f"获取任务分配失败: {response.status}")
                return False
        
        # 3. 确保任务未运行
        logger.info("3. 检查任务状态...")
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}") as response:
            if response.status == 200:
                task_detail = await response.json()
                if task_detail.get("is_running"):
                    logger.info("任务正在运行，先终止...")
                    async with session.post(f"{API_BASE}/monitoring-tasks/{task_id}/terminate") as term_response:
                        if term_response.status == 200:
                            logger.info("任务已终止")
                            await asyncio.sleep(2)
                        else:
                            logger.warning("终止任务失败")
        
        # 4. 执行任务并监控后端调用
        logger.info("4. 执行任务...")
        
        # 记录执行前的时间，用于检查日志
        execution_start_time = datetime.now()
        
        async with session.post(f"{API_BASE}/monitoring-tasks/{task_id}/execute") as response:
            if response.status == 200:
                result = await response.json()
                logger.info("✅ 任务执行已启动")
                logger.info(f"执行ID: {result.get('execution_id')}")
                logger.info(f"提交ID: {result.get('submission_id')}")
                logger.info(f"URL数量: {result.get('url_count')}")
                
                # 检查是否有Worker分配信息
                worker_assignments = result.get('worker_assignments', {})
                if worker_assignments:
                    logger.info("✅ 执行结果包含Worker分配信息:")
                    for worker_id, urls in worker_assignments.items():
                        logger.info(f"  {worker_id}: {len(urls)} 个URL")
                else:
                    logger.warning("⚠️ 执行结果没有Worker分配信息")
                
            else:
                error_text = await response.text()
                logger.error(f"❌ 任务执行失败: {response.status} - {error_text}")
                return False
        
        # 5. 等待一段时间让任务开始执行
        logger.info("5. 等待任务开始执行...")
        await asyncio.sleep(5)
        
        # 6. 检查后端日志（通过Docker logs）
        logger.info("6. 检查执行状态...")
        
        # 检查任务管理器状态
        async with session.get(f"{API_BASE}/tasks/status") as response:
            if response.status == 200:
                tm_status = await response.json()
                logger.info(f"任务管理器状态: {tm_status.get('status')}")
                logger.info(f"运行中批次: {tm_status.get('running_batches')}")
                
                if tm_status.get('running_batches', 0) > 0:
                    logger.info("✅ 任务管理器中有运行中的批次")
                else:
                    logger.info("ℹ️ 任务管理器中没有运行中的批次")
        
        # 7. 检查执行历史
        logger.info("7. 检查执行历史...")
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}/history") as response:
            if response.status == 200:
                history = await response.json()
                executions = history.get("data", [])
                if executions:
                    latest = executions[0]
                    logger.info(f"最新执行状态: {latest.get('status')}")
                    logger.info(f"触发方式: {latest.get('trigger')}")
                    
                    if latest.get('trigger') == 'manual':
                        logger.info("✅ 找到手动触发的执行记录")
                    else:
                        logger.warning("⚠️ 最新执行记录不是手动触发")
        
        logger.info("🎉 测试完成!")
        return True

async def check_docker_logs():
    """检查Docker日志中的Worker后端调用"""
    import subprocess
    
    logger.info("📋 检查Docker日志...")
    
    try:
        # 检查Celery worker日志
        result = subprocess.run(
            ["docker", "logs", "monit-celery-worker", "--tail", "20"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            logs = result.stdout
            if "Using crawler backend" in logs:
                logger.info("✅ Celery日志中找到Worker后端调用信息")
                # 提取相关日志行
                for line in logs.split('\n'):
                    if "Using crawler backend" in line or "localhost:11235" in line:
                        logger.info(f"  📝 {line.strip()}")
            else:
                logger.warning("⚠️ Celery日志中没有找到Worker后端调用信息")
        else:
            logger.error(f"获取Celery日志失败: {result.stderr}")
            
    except Exception as e:
        logger.error(f"检查Docker日志时出错: {e}")

async def main():
    """主函数"""
    logger.info("🚀 开始测试Worker后端集成功能...")
    logger.info("=" * 60)
    
    try:
        success = await test_worker_backend_integration()
        
        if success:
            logger.info("✅ 基本测试完成!")
            
            # 检查Docker日志
            await check_docker_logs()
            
            logger.info("\n" + "=" * 60)
            logger.info("📋 测试总结:")
            logger.info("1. ✅ 任务执行流程正常")
            logger.info("2. ✅ Worker分配信息获取成功")
            logger.info("3. ✅ 任务提交到TaskManager成功")
            logger.info("4. 📝 请检查Worker后端是否收到请求")
            logger.info("\n💡 如果Worker后端仍未收到请求，请检查:")
            logger.info("   - Worker后端服务是否正在运行")
            logger.info("   - 后端配置中的API端点是否正确")
            logger.info("   - 网络连接是否正常")
        else:
            logger.error("❌ 测试失败!")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
