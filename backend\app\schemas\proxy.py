"""
代理池数据模式定义
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, validator

from .common import BaseSchema


class ProxyType(str, Enum):
    """代理类型枚举"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"


class ProxyStatus(str, Enum):
    """代理状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    FAILED = "failed"


class ProxyPoolBase(BaseModel):
    """代理池基础模式"""
    
    proxy_host: str = Field(
        description="代理主机",
        max_length=255
    )
    proxy_port: int = Field(
        description="代理端口",
        ge=1,
        le=65535
    )
    proxy_type: ProxyType = Field(
        description="代理类型"
    )
    username: Optional[str] = Field(
        None,
        description="用户名",
        max_length=100
    )
    password: Optional[str] = Field(
        None,
        description="密码",
        max_length=100
    )
    country: Optional[str] = Field(
        None,
        description="国家",
        max_length=50
    )
    region: Optional[str] = Field(
        None,
        description="地区",
        max_length=50
    )
    
    @validator('proxy_host')
    def validate_proxy_host(cls, v):
        """验证代理主机"""
        if not v or not v.strip():
            raise ValueError("代理主机不能为空")
        return v.strip()

    model_config = ConfigDict(from_attributes=True)


class ProxyPoolCreate(ProxyPoolBase):
    """创建代理池的数据模式"""
    pass


class ProxyPoolUpdate(BaseModel):
    """更新代理池的数据模式"""
    
    proxy_host: Optional[str] = Field(
        None,
        max_length=255
    )
    proxy_port: Optional[int] = Field(
        None,
        ge=1,
        le=65535
    )
    proxy_type: Optional[ProxyType] = None
    username: Optional[str] = Field(
        None,
        max_length=100
    )
    password: Optional[str] = Field(
        None,
        max_length=100
    )
    country: Optional[str] = Field(
        None,
        max_length=50
    )
    region: Optional[str] = Field(
        None,
        max_length=50
    )
    status: Optional[ProxyStatus] = None
    is_active: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class ProxyPoolResponse(BaseSchema):
    """代理池响应数据模式"""
    
    id: int = Field(description="代理ID")
    proxy_host: str = Field(description="代理主机")
    proxy_port: int = Field(description="代理端口")
    proxy_type: ProxyType = Field(description="代理类型")
    username: Optional[str] = Field(description="用户名")
    # 注意：出于安全考虑，不返回密码
    country: Optional[str] = Field(description="国家")
    region: Optional[str] = Field(description="地区")
    
    # 状态信息
    status: ProxyStatus = Field(description="代理状态")
    is_active: bool = Field(description="是否激活")
    
    # 统计信息
    success_count: int = Field(description="成功次数")
    failed_count: int = Field(description="失败次数")
    avg_response_time: Optional[float] = Field(description="平均响应时间（秒）")
    last_tested_at: Optional[datetime] = Field(description="最后测试时间")
    last_success_at: Optional[datetime] = Field(description="最后成功时间")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class UserAgentBase(BaseModel):
    """用户代理基础模式"""
    
    user_agent: str = Field(
        description="用户代理字符串",
        max_length=500
    )
    browser: Optional[str] = Field(
        None,
        description="浏览器类型",
        max_length=50
    )
    version: Optional[str] = Field(
        None,
        description="版本",
        max_length=50
    )
    platform: Optional[str] = Field(
        None,
        description="平台",
        max_length=50
    )
    device_type: Optional[str] = Field(
        None,
        description="设备类型",
        max_length=50
    )
    
    @validator('user_agent')
    def validate_user_agent(cls, v):
        """验证用户代理"""
        if not v or not v.strip():
            raise ValueError("用户代理不能为空")
        if len(v.strip()) < 10:
            raise ValueError("用户代理字符串过短")
        return v.strip()

    model_config = ConfigDict(from_attributes=True)


class UserAgentCreate(UserAgentBase):
    """创建用户代理的数据模式"""
    pass


class UserAgentUpdate(BaseModel):
    """更新用户代理的数据模式"""
    
    user_agent: Optional[str] = Field(
        None,
        max_length=500
    )
    browser: Optional[str] = Field(
        None,
        max_length=50
    )
    version: Optional[str] = Field(
        None,
        max_length=50
    )
    platform: Optional[str] = Field(
        None,
        max_length=50
    )
    device_type: Optional[str] = Field(
        None,
        max_length=50
    )
    is_active: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class UserAgentResponse(BaseSchema):
    """用户代理响应数据模式"""
    
    id: int = Field(description="用户代理ID")
    user_agent: str = Field(description="用户代理字符串")
    browser: Optional[str] = Field(description="浏览器类型")
    version: Optional[str] = Field(description="版本")
    platform: Optional[str] = Field(description="平台")
    device_type: Optional[str] = Field(description="设备类型")
    
    # 状态信息
    is_active: bool = Field(description="是否激活")
    
    # 统计信息
    usage_count: int = Field(description="使用次数")
    success_rate: float = Field(description="成功率")
    last_used_at: Optional[datetime] = Field(description="最后使用时间")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class ProxyTestRequest(BaseModel):
    """代理测试请求"""
    
    proxy_id: int = Field(
        description="代理ID",
        gt=0
    )
    test_url: str = Field(
        "https://httpbin.org/ip",
        description="测试URL",
        max_length=500
    )
    timeout: int = Field(
        10,
        description="超时时间（秒）",
        ge=1,
        le=60
    )

    model_config = ConfigDict(from_attributes=True)


class ProxyTestResponse(BaseModel):
    """代理测试响应"""
    
    success: bool = Field(description="测试是否成功")
    response_time: Optional[float] = Field(description="响应时间（秒）")
    status_code: Optional[int] = Field(description="HTTP状态码")
    error_message: Optional[str] = Field(description="错误信息")
    ip_address: Optional[str] = Field(description="检测到的IP地址")
    location: Optional[str] = Field(description="IP地理位置")

    model_config = ConfigDict(from_attributes=True)


class ProxyBatchTestRequest(BaseModel):
    """代理批量测试请求"""
    
    proxy_ids: List[int] = Field(
        description="代理ID列表",
        min_items=1,
        max_items=100
    )
    test_url: str = Field(
        "https://httpbin.org/ip",
        description="测试URL",
        max_length=500
    )
    timeout: int = Field(
        10,
        description="超时时间（秒）",
        ge=1,
        le=60
    )
    max_concurrent: int = Field(
        5,
        description="最大并发测试数",
        ge=1,
        le=20
    )

    model_config = ConfigDict(from_attributes=True)


class ProxyBatchTestResponse(BaseModel):
    """代理批量测试响应"""
    
    total_tested: int = Field(description="总测试数量")
    success_count: int = Field(description="成功数量")
    failed_count: int = Field(description="失败数量")
    avg_response_time: float = Field(description="平均响应时间")
    test_results: List[Dict[str, Any]] = Field(description="详细测试结果")

    model_config = ConfigDict(from_attributes=True) 