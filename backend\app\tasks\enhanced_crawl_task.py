"""
增强的爬取任务
使用新的CrawlerRequestBuilder构建标准化的爬虫API请求
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from celery import Task

from app.celery_app import celery_app
from app.services.crawler_request_builder import CrawlerRequestBuilder
from app.services.crawler_api_service import CrawlerAPIService

logger = logging.getLogger(__name__)


def _run_async_in_thread(coro):
    """在线程中运行异步函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True
)
def enhanced_crawl_task(
    self,
    task_id: str,
    execution_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    增强的爬取任务
    
    使用CrawlerRequestBuilder构建标准化请求，然后发送给爬虫API
    
    Args:
        task_id: 监控任务ID
        execution_context: 执行上下文
        
    Returns:
        爬取结果
    """
    return _run_async_in_thread(_enhanced_crawl_async(
        self, task_id, execution_context
    ))


async def _enhanced_crawl_async(
    celery_task_self,
    task_id: str,
    execution_context: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步增强爬取实现"""
    
    start_time = datetime.now()
    
    try:
        logger.info(f"Starting enhanced crawl task for monitoring task: {task_id}")
        
        # 添加Celery任务信息到执行上下文
        if execution_context is None:
            execution_context = {}
        
        execution_context.update({
            "celery_task_id": celery_task_self.request.id,
            "celery_task_name": "enhanced_crawl_task",
            "start_time": start_time.isoformat()
        })
        
        # 1. 使用CrawlerRequestBuilder构建标准化请求
        logger.info("Building crawler request using CrawlerRequestBuilder...")
        crawler_request = await CrawlerRequestBuilder.build_request_for_task(
            task_id=task_id,
            execution_context=execution_context
        )
        
        # 记录请求信息
        urls_count = len(crawler_request["request"]["urls"])
        priority = crawler_request["request"]["priority"]
        batch_name = crawler_request["request"]["batch_name"]
        
        logger.info(f"Built crawler request: {urls_count} URLs, priority: {priority}, batch: {batch_name}")
        
        # 2. 发送请求到爬虫API
        logger.info("Sending request to crawler API...")
        crawler_api = CrawlerAPIService()
        
        try:
            # 发送批量爬取请求
            api_result = await crawler_api.submit_batch_request(crawler_request)
            
            logger.info(f"Crawler API request successful: {api_result}")
            
            # 构建成功结果
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "status": "success",
                "task_id": task_id,
                "celery_task_id": celery_task_self.request.id,
                "batch_name": batch_name,
                "urls_count": urls_count,
                "priority": priority,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration": duration,
                "crawler_api_result": api_result,
                "execution_context": execution_context,
                "method": "enhanced_crawl_task"  # 标识使用了增强任务
            }
            
            logger.info(f"Enhanced crawl task completed successfully: {task_id}, duration: {duration:.2f}s")
            return result
            
        except Exception as api_error:
            logger.error(f"Crawler API request failed: {api_error}")
            raise
            
    except ValueError as ve:
        # 处理预期的错误（如没有活跃URL、没有配置等）
        logger.warning(f"Enhanced crawl task validation error: {ve}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return {
            "status": "validation_error",
            "task_id": task_id,
            "celery_task_id": celery_task_self.request.id,
            "error": str(ve),
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration": duration,
            "execution_context": execution_context,
            "method": "enhanced_crawl_task"
        }
        
    except Exception as e:
        # 处理其他异常
        logger.error(f"Enhanced crawl task failed: {e}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 重新抛出异常以触发Celery重试
        raise Exception(f"Enhanced crawl task failed for {task_id}: {str(e)}")


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30}
)
def enhanced_single_url_crawl(
    self,
    task_id: str,
    url: str,
    execution_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    增强的单URL爬取任务
    
    Args:
        task_id: 监控任务ID
        url: 单个URL
        execution_context: 执行上下文
        
    Returns:
        爬取结果
    """
    return _run_async_in_thread(_enhanced_single_url_async(
        self, task_id, url, execution_context
    ))


async def _enhanced_single_url_async(
    celery_task_self,
    task_id: str,
    url: str,
    execution_context: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步单URL爬取实现"""
    
    start_time = datetime.now()
    
    try:
        logger.info(f"Starting enhanced single URL crawl: {url} for task: {task_id}")
        
        # 添加Celery任务信息到执行上下文
        if execution_context is None:
            execution_context = {}
        
        execution_context.update({
            "celery_task_id": celery_task_self.request.id,
            "celery_task_name": "enhanced_single_url_crawl",
            "start_time": start_time.isoformat(),
            "single_url": url
        })
        
        # 1. 获取任务的爬取配置
        crawl_config = await CrawlerRequestBuilder.get_crawl_config_for_task(task_id)
        if not crawl_config:
            raise ValueError(f"Task {task_id} has no crawl config")
        
        # 2. 构建单URL的爬虫请求
        crawler_request = await CrawlerRequestBuilder.build_crawler_request(
            task_id=task_id,
            urls=[url],  # 单URL列表
            crawl_config=crawl_config,
            execution_context=execution_context
        )
        
        logger.info(f"Built single URL crawler request: {url}")
        
        # 3. 发送请求到爬虫API
        crawler_api = CrawlerAPIService()
        api_result = await crawler_api.submit_batch_request(crawler_request)
        
        # 构建成功结果
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        result = {
            "status": "success",
            "task_id": task_id,
            "celery_task_id": celery_task_self.request.id,
            "url": url,
            "priority": crawler_request["request"]["priority"],
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration": duration,
            "crawler_api_result": api_result,
            "execution_context": execution_context,
            "method": "enhanced_single_url_crawl"
        }
        
        logger.info(f"Enhanced single URL crawl completed: {url}, duration: {duration:.2f}s")
        return result
        
    except Exception as e:
        logger.error(f"Enhanced single URL crawl failed: {url}, error: {e}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 重新抛出异常以触发Celery重试
        raise Exception(f"Enhanced single URL crawl failed for {url}: {str(e)}")
