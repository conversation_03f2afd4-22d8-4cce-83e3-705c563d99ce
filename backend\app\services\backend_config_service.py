"""
新架构：后端配置管理服务
管理后端API连接和性能配置
"""

import json
import uuid
import asyncio
import logging
import redis
import os
import httpx  # 使用现有的httpx而不是aiohttp
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from ..schemas.backend_config import (
    BackendConfig, BackendConfigCreate, BackendConfigUpdate,
    BackendConfigSummary, BackendConfigStats, BackendHealthCheck,
    BackendStatus, PerformanceLevel
)

logger = logging.getLogger(__name__)


class BackendConfigService:
    """后端配置管理服务"""
    
    def __init__(self):
        # 使用现有的Redis连接模式，Docker环境下使用服务名
        redis_url = os.getenv('REDIS_URL', 'redis://redis:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.data_dir = Path("data/backend_configs")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Redis键模式
        self.keys = {
            'config': 'backend_config:{}',
            'config_list': 'backend_config:list',
            'config_by_name': 'backend_config:name:{}',
            'config_by_endpoint': 'backend_config:endpoint:{}',
            'health_check': 'backend_health:{}',
            'stats': 'backend_config:stats'
        }
    
    async def create_config(self, config_data: BackendConfigCreate) -> BackendConfig:
        """创建后端配置"""
        try:
            # 检查名称是否已存在
            existing_config = await self.get_config_by_name(config_data.backend_name)
            if existing_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"后端名称 '{config_data.backend_name}' 已存在"
                )
            
            # 检查端点是否已存在
            existing_endpoint = await self.get_config_by_endpoint(config_data.api_endpoint)
            if existing_endpoint:
                raise HTTPException(
                    status_code=400, 
                    detail=f"API端点 '{config_data.api_endpoint}' 已存在"
                )
            
            # 生成配置ID
            backend_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 创建配置对象
            config = BackendConfig(
                backend_id=backend_id,
                backend_name=config_data.backend_name,
                description=config_data.description,
                api_endpoint=config_data.api_endpoint,
                timeout=config_data.timeout,
                max_retries=config_data.max_retries,
                auth_config=config_data.auth_config,

                # 系统配置字段
                performance_config=config_data.performance_config,
                system_auth_config=config_data.system_auth_config,
                callback_config=config_data.callback_config,
                monitoring_config=config_data.monitoring_config,
                weight=config_data.weight,
                priority=config_data.priority,
                performance_level=config_data.performance_level,
                created_at=now,
                updated_at=now,
                created_by=config_data.created_by
            )
            
            # 保存配置
            await self._save_config(config)
            
            # 添加到配置列表
            self.redis_client.sadd(self.keys['config_list'], backend_id)
            
            # 建立索引
            self.redis_client.set(
                self.keys['config_by_name'].format(config_data.backend_name), 
                backend_id
            )
            self.redis_client.set(
                self.keys['config_by_endpoint'].format(config_data.api_endpoint), 
                backend_id
            )
            
            # 执行初始健康检查
            asyncio.create_task(self.check_backend_health(backend_id))
            
            logger.info(f"Created backend config: {backend_id} ({config_data.backend_name})")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create backend config: {e}")
            raise HTTPException(status_code=500, detail=f"创建后端配置失败: {str(e)}")
    
    async def get_config(self, backend_id: str) -> Optional[BackendConfig]:
        """获取后端配置"""
        try:
            # 从Redis获取
            config_data = self.redis_client.get(self.keys['config'].format(backend_id))
            if config_data:
                config_dict = json.loads(config_data)
                return BackendConfig(**config_dict)
            
            # 从文件获取
            config_file = self.data_dir / f"{backend_id}.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    config = BackendConfig(**config_dict)
                    # 同步到Redis
                    await self._save_config(config)
                    return config
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get backend config {backend_id}: {e}")
            return None
    
    async def get_config_by_name(self, backend_name: str) -> Optional[BackendConfig]:
        """根据名称获取配置"""
        try:
            backend_id = self.redis_client.get(self.keys['config_by_name'].format(backend_name))
            if backend_id:
                backend_id_str = backend_id if isinstance(backend_id, str) else backend_id.decode()
                return await self.get_config(backend_id_str)
            return None
        except Exception as e:
            logger.error(f"Failed to get config by name {backend_name}: {e}")
            return None
    
    async def get_config_by_endpoint(self, api_endpoint: str) -> Optional[BackendConfig]:
        """根据端点获取配置"""
        try:
            backend_id = self.redis_client.get(self.keys['config_by_endpoint'].format(api_endpoint))
            if backend_id:
                backend_id_str = backend_id if isinstance(backend_id, str) else backend_id.decode()
                return await self.get_config(backend_id_str)
            return None
        except Exception as e:
            logger.error(f"Failed to get config by endpoint {api_endpoint}: {e}")
            return None
    
    async def update_config(self, backend_id: str, update_data: BackendConfigUpdate) -> Optional[BackendConfig]:
        """更新后端配置"""
        try:
            # 获取现有配置
            config = await self.get_config(backend_id)
            if not config:
                raise HTTPException(status_code=404, detail="后端配置不存在")
            
            # 检查名称冲突
            if update_data.backend_name and update_data.backend_name != config.backend_name:
                existing_config = await self.get_config_by_name(update_data.backend_name)
                if existing_config and existing_config.backend_id != backend_id:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"后端名称 '{update_data.backend_name}' 已存在"
                    )
            
            # 检查端点冲突
            if update_data.api_endpoint and update_data.api_endpoint != config.api_endpoint:
                existing_endpoint = await self.get_config_by_endpoint(update_data.api_endpoint)
                if existing_endpoint and existing_endpoint.backend_id != backend_id:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"API端点 '{update_data.api_endpoint}' 已存在"
                    )
            
            # 更新配置
            update_dict = update_data.dict(exclude_unset=True)
            old_name = config.backend_name
            old_endpoint = config.api_endpoint
            
            for field, value in update_dict.items():
                if hasattr(config, field):
                    setattr(config, field, value)
            
            config.updated_at = datetime.now()
            
            # 保存更新后的配置
            await self._save_config(config)
            
            # 更新索引
            if update_data.backend_name and update_data.backend_name != old_name:
                self.redis_client.delete(self.keys['config_by_name'].format(old_name))
                self.redis_client.set(
                    self.keys['config_by_name'].format(update_data.backend_name), 
                    backend_id
                )
            
            if update_data.api_endpoint and update_data.api_endpoint != old_endpoint:
                self.redis_client.delete(self.keys['config_by_endpoint'].format(old_endpoint))
                self.redis_client.set(
                    self.keys['config_by_endpoint'].format(update_data.api_endpoint), 
                    backend_id
                )
            
            # 如果端点变更，重新检查健康状态
            if update_data.api_endpoint and update_data.api_endpoint != old_endpoint:
                asyncio.create_task(self.check_backend_health(backend_id))
            
            logger.info(f"Updated backend config: {backend_id}")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update backend config {backend_id}: {e}")
            raise HTTPException(status_code=500, detail=f"更新后端配置失败: {str(e)}")
    
    async def delete_config(self, backend_id: str) -> bool:
        """删除后端配置"""
        try:
            # 获取配置信息
            config = await self.get_config(backend_id)
            if not config:
                raise HTTPException(status_code=404, detail="后端配置不存在")
            
            # 检查是否被Worker使用
            # TODO: 实现Worker依赖检查
            
            # 从Redis删除
            self.redis_client.delete(self.keys['config'].format(backend_id))
            self.redis_client.srem(self.keys['config_list'], backend_id)
            self.redis_client.delete(self.keys['config_by_name'].format(config.backend_name))
            self.redis_client.delete(self.keys['config_by_endpoint'].format(config.api_endpoint))
            self.redis_client.delete(self.keys['health_check'].format(backend_id))
            
            # 删除文件
            config_file = self.data_dir / f"{backend_id}.json"
            if config_file.exists():
                config_file.unlink()
            
            logger.info(f"Deleted backend config: {backend_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to delete backend config {backend_id}: {e}")
            return False
    
    async def list_configs(
        self, 
        status: Optional[BackendStatus] = None,
        performance_level: Optional[PerformanceLevel] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[BackendConfigSummary]:
        """获取后端配置列表"""
        try:
            # 获取所有配置ID
            config_ids = self.redis_client.smembers(self.keys['config_list'])
            if not config_ids:
                return []
            
            # 获取配置详情
            configs = []
            for config_id in config_ids:
                config_id_str = config_id if isinstance(config_id, str) else config_id.decode()
                config = await self.get_config(config_id_str)
                if config:
                    configs.append(config)
            
            # 过滤
            if status:
                configs = [c for c in configs if c.status == status]
            
            if performance_level:
                configs = [c for c in configs if c.performance_level == performance_level]
            
            # 排序（按健康评分和优先级）
            configs.sort(key=lambda x: (x.health_score, x.priority), reverse=True)
            
            # 分页
            configs = configs[offset:offset + limit]
            
            # 转换为摘要
            summaries = [BackendConfigSummary.from_config(config) for config in configs]
            
            return summaries
            
        except Exception as e:
            logger.error(f"Failed to list backend configs: {e}")
            return []
    
    async def _perform_health_check(self, config: BackendConfig) -> Optional[BackendHealthCheck]:
        """执行健康检查的内部方法"""
        try:
            start_time = datetime.now()

            try:
                # 构建健康检查URL，处理Docker环境中的网络访问
                endpoint = config.api_endpoint.rstrip('/')

                # 如果在Docker环境中且端点使用localhost，转换为host.docker.internal
                if os.getenv('DOCKER_ENV') == 'true' and 'localhost' in endpoint:
                    endpoint = endpoint.replace('localhost', 'host.docker.internal')
                    logger.info(f"Docker环境中将localhost转换为host.docker.internal: {endpoint}")

                health_url = f"{endpoint}/health"

                # 发送健康检查请求
                timeout = config.monitoring_config.health_check_timeout / 1000
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(health_url)
                    response_time = (datetime.now() - start_time).total_seconds() * 1000

                    if response.status_code == 200:
                        try:
                            response_data = response.json()
                        except:
                            response_data = {}

                        health_check = BackendHealthCheck(
                            backend_id=config.backend_id,
                            is_healthy=True,
                            response_time=response_time,
                            check_time=datetime.now(),
                            api_version=response_data.get('version'),
                            server_info=response_data.get('server_info'),
                            load_info=response_data.get('load_info')
                        )
                    else:
                        health_check = BackendHealthCheck(
                            backend_id=config.backend_id,
                            is_healthy=False,
                            response_time=response_time,
                            error_message=f"HTTP {response.status_code}",
                            check_time=datetime.now()
                        )

            except Exception as e:
                response_time = (datetime.now() - start_time).total_seconds() * 1000
                health_check = BackendHealthCheck(
                    backend_id=config.backend_id,
                    is_healthy=False,
                    response_time=response_time,
                    error_message=str(e),
                    check_time=datetime.now()
                )
            

            
            return health_check

        except Exception as e:
            logger.error(f"Failed to perform health check: {e}")
            return None

    async def check_backend_health(self, backend_id: str) -> Optional[BackendHealthCheck]:
        """检查后端健康状态"""
        try:
            config = await self.get_config(backend_id)
            if not config:
                return None

            # 使用内部方法执行健康检查
            health_check = await self._perform_health_check(config)

            if health_check:
                # 更新配置的健康状态
                config.health_score = health_check.health_score
                config.last_health_check = health_check.check_time
                if not health_check.is_healthy:
                    config.status = BackendStatus.ERROR
                elif config.status == BackendStatus.ERROR and health_check.is_healthy:
                    config.status = BackendStatus.ACTIVE

                await self._save_config(config)

                # 保存健康检查结果
                health_data = health_check.dict()
                self.redis_client.set(
                    self.keys['health_check'].format(backend_id),
                    json.dumps(health_data, default=str),
                    ex=3600  # 1小时过期
                )

            return health_check

        except Exception as e:
            logger.error(f"Failed to check backend health {backend_id}: {e}")
            return None

    async def get_available_backends(self) -> List[BackendConfig]:
        """获取可用的后端配置"""
        try:
            configs = await self.list_configs(status=BackendStatus.ACTIVE, limit=1000)
            available_configs = []
            
            for summary in configs:
                config = await self.get_config(summary.backend_id)
                if config and config.is_available:
                    available_configs.append(config)
            
            return available_configs
            
        except Exception as e:
            logger.error(f"Failed to get available backends: {e}")
            return []
    
    async def _save_config(self, config: BackendConfig):
        """保存配置到Redis和文件"""
        try:
            config_dict = config.dict()
            config_json = json.dumps(config_dict, default=str, ensure_ascii=False)
            
            # 保存到Redis
            self.redis_client.set(
                self.keys['config'].format(config.backend_id), 
                config_json
            )
            
            # 保存到文件
            config_file = self.data_dir / f"{config.backend_id}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False, default=str)
            
        except Exception as e:
            logger.error(f"Failed to save backend config {config.backend_id}: {e}")
            raise

    async def get_config_stats(self, backend_id: str) -> BackendConfigStats:
        """获取后端配置统计信息"""
        try:
            # 获取所有后端配置摘要
            all_summaries = await self.list_configs()

            # 计算基本统计信息
            total_backends = len(all_summaries)
            active_backends = len([c for c in all_summaries if c.status == BackendStatus.ACTIVE])
            healthy_backends = len([c for c in all_summaries if c.health_score >= 0.8])

            # 获取当前后端的详细信息
            current_config = await self.get_config(backend_id)
            if not current_config:
                raise HTTPException(status_code=404, detail="后端配置不存在")

            # 计算性能级别分布
            performance_distribution = {}
            for config in all_summaries:
                level = config.performance_level.value
                performance_distribution[level] = performance_distribution.get(level, 0) + 1

            # 获取所有详细配置来计算请求统计（仅用于演示，实际应该从数据库聚合）
            total_requests = 0
            total_success = 0
            total_failed = 0
            avg_response_time = 0.0

            # 为了演示，我们使用当前配置的数据
            if current_config:
                total_requests = current_config.total_requests
                total_success = current_config.success_requests
                total_failed = current_config.failed_requests
                avg_response_time = current_config.avg_response_time

            # 计算容量信息
            available_backends = len([c for c in all_summaries if c.status == BackendStatus.ACTIVE and c.health_score >= 0.8])
            total_capacity = sum(c.max_concurrent for c in all_summaries)
            used_capacity = sum(c.current_load for c in all_summaries)
            available_capacity = total_capacity - used_capacity

            # 计算平均健康评分
            avg_health_score = sum(c.health_score for c in all_summaries) / total_backends if total_backends > 0 else 0.0

            # 计算总体成功率
            total_success_rate = total_success / total_requests if total_requests > 0 else 0.0

            # 获取最近创建和失败的后端（简化版本）
            recently_created = all_summaries[:3]  # 取前3个作为示例
            recently_failed = [c for c in all_summaries if c.status == BackendStatus.ERROR][:3]

            stats = BackendConfigStats(
                total_backends=total_backends,
                active_backends=active_backends,
                healthy_backends=healthy_backends,
                available_backends=available_backends,
                total_capacity=total_capacity,
                used_capacity=used_capacity,
                available_capacity=available_capacity,
                avg_health_score=avg_health_score,
                avg_response_time=avg_response_time,
                total_success_rate=total_success_rate,
                performance_distribution=performance_distribution,
                recently_created=recently_created,
                recently_failed=recently_failed
            )

            logger.info(f"Generated stats for backend config: {backend_id}")
            return stats

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get backend config stats {backend_id}: {e}")
            raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


# 全局服务实例
backend_config_service = BackendConfigService()
