# 监控任务系统工作流重新设计方案

**文档版本**: v2.0  
**创建日期**: 2025年7月4日  
**设计师**: AI Assistant  
**状态**: 设计完成，准备实施

## 📋 需求背景

### 用户期望的工作流程
1. **Excel上传与解析**: 前端上传Excel → 后端解析URL → 返回前端显示
2. **URL池管理**: URL列表持久化到Redis，支持筛选、多选、反选、平台筛选
3. **任务创建**: 从URL池选择链接创建监控任务，支持定时调度配置
4. **任务管理**: 修改配置、管理链接、手动执行、终止任务

### 当前系统问题分析
- ❌ 缺少URL池管理功能
- ❌ 任务创建流程不符合用户期望
- ❌ 定时调度功能不完整
- ❌ URL与任务关联关系设计不合理
- ❌ 缺少批量操作和状态管理

## 🏗️ 新架构设计

### 系统架构图
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Excel上传   │───▶│ URL解析     │───▶│ URL池管理   │───▶│ 任务创建    │
│ 前端组件    │    │ 后端服务    │    │ Redis存储   │    │ 配置界面    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                              │                    │
                                              ▼                    ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 监控执行    │◀───│ 定时调度    │◀───│ 任务管理    │◀───│ 任务存储    │
│ 爬虫引擎    │    │ Celery Beat │    │ 状态控制    │    │ Redis/DB    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### 数据流设计
```
Excel文件 → URL解析 → URL池(Redis) → 任务创建 → 定时调度 → 监控执行 → 结果存储
    ↓           ↓           ↓            ↓           ↓           ↓
  验证格式    平台识别    去重存储      配置调度    状态管理    数据分析
```

## 📊 核心数据模型

### URL池数据结构
```typescript
interface UrlPoolItem {
  id: string;                    // 唯一标识符
  url: string;                   // URL地址
  platform: string;              // 平台类型 (mercadolibre, amazon, etc.)
  source_file: string;           // 来源Excel文件名
  added_at: string;              // 添加时间 (ISO格式)
  status: 'active' | 'disabled'; // URL状态
  last_check?: string;           // 最后检查时间
  check_count: number;           // 总检查次数
  success_count: number;         // 成功检查次数
  error_count: number;           // 错误次数
  metadata?: {                   // 扩展元数据
    original_row?: number;       // Excel中的原始行号
    detected_platform?: string; // 自动检测的平台
    validation_status?: string;  // 验证状态
    [key: string]: any;
  };
}
```

### 监控任务数据结构
```typescript
interface MonitoringTask {
  id: string;                    // 任务唯一标识
  name: string;                  // 任务名称
  description?: string;          // 任务描述
  url_ids: string[];            // 关联的URL ID列表
  schedule: {                    // 调度配置
    type: 'once' | 'daily' | 'weekly' | 'hourly' | 'custom';
    interval?: number;           // 间隔时间
    time?: string;              // 执行时间 (HH:mm)
    days?: number[];            // 执行日期 (周几)
    enabled: boolean;           // 是否启用调度
  };
  config: {                     // 任务配置
    platform: string;          // 目标平台
    priority: 'low' | 'normal' | 'high' | 'urgent';
    retry_count: number;        // 重试次数
    timeout: number;            // 超时时间(秒)
    concurrent_limit: number;   // 并发限制
    batch_size: number;         // 批次大小
  };
  status: 'draft' | 'active' | 'paused' | 'stopped' | 'error';
  is_running: boolean;          // 是否正在运行
  created_at: string;           // 创建时间
  updated_at: string;           // 更新时间
  last_run?: string;            // 最后运行时间
  next_run?: string;            // 下次运行时间
  stats: {                      // 统计信息
    total_runs: number;         // 总运行次数
    success_runs: number;       // 成功运行次数
    failed_runs: number;        // 失败运行次数
    avg_duration: number;       // 平均执行时长
    last_success_time?: string; // 最后成功时间
    success_rate: number;       // 成功率 (0-1)
  };
}
```

## 🔧 API接口设计

### URL池管理API
```python
# Excel上传和解析
POST /api/v1/excel/upload-and-parse
Content-Type: multipart/form-data
Body: {
  file: File,                    # Excel文件
  platform_filter?: string,     # 平台筛选
  auto_detect: boolean = true    # 自动检测平台
}
Response: {
  success: boolean,
  total_urls: number,
  new_urls: number,
  duplicate_urls: number,
  invalid_urls: number,
  urls: UrlPoolItem[]
}

# URL池列表查询
GET /api/v1/urls/pool
Query: {
  page?: number = 1,
  page_size?: number = 20,
  platform?: string,
  status?: string,
  source_file?: string,
  search?: string,
  sort_by?: string = 'added_at',
  sort_order?: 'asc' | 'desc' = 'desc'
}
Response: {
  success: boolean,
  data: UrlPoolItem[],
  total: number,
  page: number,
  page_size: number
}

# URL池批量操作
POST /api/v1/urls/pool/batch-update
Body: {
  url_ids: string[],
  action: 'enable' | 'disable' | 'delete',
  status?: string
}

# URL池统计信息
GET /api/v1/urls/pool/stats
Response: {
  total_urls: number,
  active_urls: number,
  disabled_urls: number,
  platforms: { [platform: string]: number },
  recent_uploads: { date: string, count: number }[]
}
```

### 监控任务管理API
```python
# 创建监控任务
POST /api/v1/monitoring-tasks/
Body: MonitoringTaskCreate

# 更新任务关联的URL
PUT /api/v1/monitoring-tasks/{task_id}/urls
Body: {
  url_ids: string[],
  action: 'add' | 'remove' | 'replace'
}

# 手动执行任务
POST /api/v1/monitoring-tasks/{task_id}/execute
Body: {
  force: boolean = false,       # 强制执行
  url_ids?: string[]           # 指定URL执行
}

# 终止任务执行
POST /api/v1/monitoring-tasks/{task_id}/terminate
Body: {
  reason?: string              # 终止原因
}

# 获取任务执行日志
GET /api/v1/monitoring-tasks/{task_id}/logs
Query: {
  page?: number,
  limit?: number,
  level?: 'info' | 'warning' | 'error'
}
```

## 🎯 前端页面架构

### 路由结构
```typescript
/monitoring
├── /url-pool                  # URL池管理主页
│   ├── 上传Excel功能区
│   ├── URL列表展示区
│   ├── 筛选和搜索区
│   └── 批量操作工具栏
├── /tasks                     # 任务列表页面
│   ├── 任务概览卡片
│   ├── 状态监控面板
│   ├── 快速操作按钮
│   └── 任务统计图表
├── /tasks/create              # 创建任务页面
│   ├── 步骤1: 从URL池选择链接
│   ├── 步骤2: 配置任务参数
│   ├── 步骤3: 设置定时调度
│   └── 步骤4: 确认创建
├── /tasks/:id                 # 任务详情页面
│   ├── Tab1: 基本信息
│   ├── Tab2: URL管理
│   ├── Tab3: 执行历史
│   ├── Tab4: 统计图表
│   └── Tab5: 日志查看
└── /tasks/:id/edit            # 编辑任务页面
    ├── 修改基本配置
    ├── 调整调度设置
    ├── 管理关联URL
    └── 高级选项配置
```

### 组件架构
```typescript
// 核心组件
components/
├── ExcelUpload/               # Excel上传组件
│   ├── index.tsx             # 主组件
│   ├── UploadProgress.tsx    # 进度显示
│   └── ResultPreview.tsx     # 结果预览
├── UrlPool/                   # URL池组件
│   ├── UrlTable.tsx          # URL表格
│   ├── UrlFilters.tsx        # 筛选器
│   ├── BatchActions.tsx      # 批量操作
│   └── UrlStats.tsx          # 统计信息
├── TaskManagement/            # 任务管理组件
│   ├── TaskList.tsx          # 任务列表
│   ├── TaskCard.tsx          # 任务卡片
│   ├── TaskForm.tsx          # 任务表单
│   ├── ScheduleConfig.tsx    # 调度配置
│   └── TaskActions.tsx       # 任务操作
└── Common/                    # 通用组件
    ├── StatusTag.tsx         # 状态标签
    ├── PlatformIcon.tsx      # 平台图标
    └── TimeDisplay.tsx       # 时间显示
```

## 🗄️ Redis数据存储设计

### 数据结构设计
```redis
# URL池主数据 - Hash结构
url_pool:items:{url_id} = {
  "id": "uuid-string",
  "url": "https://example.com/product/123",
  "platform": "mercadolibre",
  "source_file": "products_2025.xlsx",
  "added_at": "2025-07-04T10:30:00Z",
  "status": "active",
  "last_check": null,
  "check_count": 0,
  "success_count": 0,
  "error_count": 0,
  "metadata": "{\"original_row\": 5}"
}

# URL索引 - Set结构
url_pool:all_ids = {url_id1, url_id2, url_id3, ...}
url_pool:platform:mercadolibre = {url_id1, url_id3, ...}
url_pool:platform:amazon = {url_id2, url_id4, ...}
url_pool:status:active = {url_id1, url_id2, ...}
url_pool:status:disabled = {url_id3, ...}
url_pool:source:products_2025.xlsx = {url_id1, url_id2, ...}

# URL去重 - Set结构 (存储URL的hash值)
url_pool:url_hashes = {hash1, hash2, hash3, ...}

# 监控任务数据 - Hash结构
monitoring_tasks:{task_id} = {
  "id": "task-uuid",
  "name": "商品价格监控",
  "description": "监控指定商品价格变化",
  "url_ids": "[\"url_id1\", \"url_id2\"]",
  "schedule": "{\"type\": \"daily\", \"time\": \"09:00\"}",
  "config": "{\"platform\": \"mercadolibre\", \"priority\": \"normal\"}",
  "status": "active",
  "is_running": false,
  "created_at": "2025-07-04T10:30:00Z",
  "updated_at": "2025-07-04T10:30:00Z",
  "stats": "{\"total_runs\": 0, \"success_rate\": 0}"
}

# 任务索引
monitoring_tasks:all_ids = {task_id1, task_id2, ...}
monitoring_tasks:status:active = {task_id1, ...}
monitoring_tasks:status:paused = {task_id2, ...}

# 任务-URL关联关系
task_urls:{task_id} = {url_id1, url_id2, url_id3, ...}
url_tasks:{url_id} = {task_id1, task_id2, ...}
```

### 缓存策略
```redis
# 统计信息缓存 (TTL: 5分钟)
stats:url_pool = {
  "total_urls": 1500,
  "active_urls": 1200,
  "platforms": "{\"mercadolibre\": 800, \"amazon\": 700}",
  "updated_at": "2025-07-04T10:30:00Z"
}

# 最近上传记录 (TTL: 1小时)
recent_uploads = [
  "{\"file\": \"products_2025.xlsx\", \"count\": 500, \"time\": \"2025-07-04T10:30:00Z\"}",
  "{\"file\": \"items_batch2.xlsx\", \"count\": 300, \"time\": \"2025-07-04T09:15:00Z\"}"
]
```

## 📝 实施计划

### 第一阶段：Excel上传与URL解析 (3-4天)
- [x] 需求分析和设计完成
- [ ] 后端Excel解析服务开发
- [ ] Redis数据存储实现
- [ ] 前端上传组件开发
- [ ] API接口集成测试

### 第二阶段：URL池管理页面 (4-5天)
- [ ] URL池管理页面UI开发
- [ ] 筛选、搜索、分页功能
- [ ] 批量操作功能实现
- [ ] 状态管理和实时更新

### 第三阶段：任务创建流程重构 (3-4天)
- [ ] 任务创建向导开发
- [ ] URL选择界面实现
- [ ] 调度配置组件开发
- [ ] 任务创建API调整

### 第四阶段：任务管理功能增强 (4-5天)
- [ ] 任务详情页面完善
- [ ] URL关联管理功能
- [ ] 手动执行和终止功能
- [ ] 任务配置修改功能

### 第五阶段：定时调度系统集成 (5-6天)
- [ ] Celery Beat集成
- [ ] 动态任务调度实现
- [ ] 任务状态实时更新
- [ ] 执行日志记录系统

### 第六阶段：数据持久化优化 (3-4天)
- [ ] Redis性能优化
- [ ] 数据一致性保证
- [ ] 备份和恢复机制
- [ ] 监控和告警系统

## 🎯 预期效果

### 用户体验改进
- ✅ 完全符合用户期望的工作流程
- ✅ 直观的URL池管理界面
- ✅ 灵活的任务创建和管理
- ✅ 实时的状态反馈和进度显示

### 系统性能提升
- ✅ 高效的Redis数据存储和查询
- ✅ 可扩展的任务调度架构
- ✅ 稳定的定时执行机制
- ✅ 完善的错误处理和恢复

### 功能完整性
- ✅ 完整的URL生命周期管理
- ✅ 灵活的任务配置选项
- ✅ 丰富的监控和统计功能
- ✅ 便捷的批量操作支持

## 🔍 技术架构对比分析

### 当前架构 vs 新架构

| 方面 | 当前架构 | 新架构 | 改进点 |
|------|----------|--------|--------|
| **数据流** | Excel → 直接创建任务 | Excel → URL池 → 任务创建 | 解耦数据导入和任务创建 |
| **URL管理** | 无独立管理 | 专门的URL池管理 | 支持复用、批量操作 |
| **任务创建** | 上传即创建 | 从URL池选择创建 | 更灵活的任务配置 |
| **定时调度** | 基础实现 | 完整的Celery集成 | 更稳定的调度机制 |
| **状态管理** | 简单状态 | 多维度状态管理 | 更精细的控制 |
| **用户体验** | 一次性流程 | 分步骤管理 | 符合用户期望 |

### 技术选型说明

**前端技术栈**:
- React 18 + TypeScript - 类型安全和组件化
- Ant Design 5.x - 企业级UI组件库
- React Router 6 - 现代路由管理
- Axios - HTTP客户端

**后端技术栈**:
- FastAPI - 高性能异步API框架
- Pydantic - 数据验证和序列化
- Redis - 高性能缓存和数据存储
- Celery + Beat - 分布式任务调度
- pandas + openpyxl - Excel文件处理

**数据存储**:
- Redis - 主要数据存储（URL池、任务配置）
- TimescaleDB - 时序数据存储（执行历史、统计）
- 文件系统 - 临时文件和日志存储

## 📊 性能指标和监控

### 关键性能指标 (KPI)
- **Excel解析性能**: 1000行 < 10秒
- **URL池查询**: 响应时间 < 200ms
- **任务创建**: 响应时间 < 500ms
- **并发支持**: > 100个活跃任务
- **系统可用性**: > 99.5%

### 监控指标
```python
# 业务指标
- url_pool_size: URL池总数量
- active_tasks_count: 活跃任务数量
- daily_upload_count: 每日上传文件数
- task_success_rate: 任务成功率

# 技术指标
- redis_memory_usage: Redis内存使用率
- api_response_time: API响应时间
- celery_queue_length: Celery队列长度
- error_rate: 错误率
```

## 🛡️ 安全和稳定性考虑

### 数据安全
- 文件上传大小限制和格式验证
- URL数据脱敏和隐私保护
- Redis访问控制和数据加密
- API接口权限控制

### 系统稳定性
- Redis数据持久化和备份
- 任务执行异常处理和重试
- 系统资源监控和告警
- 优雅降级和故障恢复

### 扩展性设计
- 微服务架构支持
- 水平扩展能力
- 插件化平台支持
- 配置化业务规则

---

**设计负责人**: AI Assistant
**文档版本**: v2.0
**最后更新**: 2025年7月4日
**审核状态**: 待用户确认
**下一步**: 开始实施第一阶段任务
