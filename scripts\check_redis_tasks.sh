#!/bin/bash
# Redis任务监控快捷脚本

echo "🔍 检查Redis中的任务数据..."
echo "=================================="

# 检查所有键
echo "📋 所有键:"
docker exec monit-redis redis-cli keys "*"

echo ""
echo "📋 任务相关键:"
docker exec monit-redis redis-cli keys "*task*"

echo ""
echo "📋 TaskManager相关键:"
docker exec monit-redis redis-cli keys "*taskmanager*"

echo ""
echo "📋 Celery相关键:"
docker exec monit-redis redis-cli keys "celery-task-meta-*"

echo ""
echo "=================================="
echo "✅ 检查完成"
