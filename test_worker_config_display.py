#!/usr/bin/env python3
"""
测试Worker配置名称显示和任务分配信息的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_worker_config_display():
    """测试Worker配置名称显示"""
    
    print("🧪 测试Worker配置名称显示...")
    
    # 1. 获取Worker列表
    print(f"\n📋 获取Worker列表...")
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 获取到 {len(workers)} 个Worker:")
            
            for worker in workers:
                print(f"\n  Worker: {worker['worker_name']}")
                print(f"    ID: {worker['worker_id']}")
                print(f"    前端配置名: {worker.get('crawler_config_name', '未知')}")
                print(f"    后端配置名: {worker.get('backend_config_name', '未知')}")
                print(f"    状态: {worker['status']}")
            
            return workers
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def test_task_worker_display():
    """测试任务Worker列表显示"""
    
    print(f"\n🧪 测试任务Worker列表显示...")
    
    task_id = "success"
    
    # 1. 获取任务的Worker列表
    print(f"\n📋 获取任务 {task_id} 的Worker列表...")
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/task/{task_id}/assigned-workers")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 获取到 {len(workers)} 个Worker:")
            
            for worker in workers:
                print(f"\n  Worker: {worker['worker_name']}")
                print(f"    ID: {worker['worker_id']}")
                print(f"    前端配置名: {worker.get('crawler_config_name', '未知')}")
                print(f"    后端配置名: {worker.get('backend_config_name', '未知')}")
                print(f"    分配ID: {worker.get('assignment_id', '未知')}")
                print(f"    分配状态: {worker.get('assignment_status', '未知')}")
            
            return workers
        else:
            print(f"❌ 获取任务Worker列表失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def create_test_assignment_and_test():
    """创建测试分配并测试Worker任务分配信息"""
    
    print(f"\n🧪 创建测试分配并测试Worker任务分配信息...")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    worker_id = "81d48722-8467-492f-aa45-9bf946da8bd8"
    
    # 1. 创建测试分配
    print(f"\n🔧 创建测试分配...")
    assignment_data = {
        "task_id": task_id,
        "worker_ids": [worker_id],
        "assignment_name": "测试配置显示",
        "description": "用于测试Worker配置名称显示功能",
        "assignment_strategy": "health_based",
        "auto_failover": True,
        "total_urls": 49
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/task-assignments/",
            json=assignment_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"分配创建响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignment = response.json()
            print("✅ 分配创建成功!")
            print(f"  分配ID: {assignment['assignment_id']}")
            
            # 2. 测试Worker任务分配API
            print(f"\n📋 测试Worker任务分配API...")
            assignments_response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}/assignments")
            
            print(f"任务分配响应状态码: {assignments_response.status_code}")
            
            if assignments_response.status_code == 200:
                assignments = assignments_response.json()
                print(f"✅ 获取到 {len(assignments)} 个任务分配:")
                
                for assignment in assignments:
                    print(f"\n  分配: {assignment['assignment_name']}")
                    print(f"    分配ID: {assignment['assignment_id']}")
                    print(f"    任务名称: {assignment['task_name']}")
                    print(f"    任务ID: {assignment['task_id']}")
                    print(f"    任务状态: {assignment['task_status']}")
                    print(f"    分配状态: {assignment['assignment_status']}")
                    print(f"    是否主要Worker: {assignment['is_primary_worker']}")
                    print(f"    Worker数量: {assignment['worker_count']}")
                
                return True
            else:
                print(f"❌ 获取Worker任务分配失败: {assignments_response.text}")
                return False
        else:
            print(f"❌ 分配创建失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试Worker配置名称显示和任务分配信息\n")
    
    # 1. 测试Worker配置名称显示
    print("=" * 60)
    workers = test_worker_config_display()
    
    # 2. 测试任务Worker列表显示
    print("=" * 60)
    task_workers = test_task_worker_display()
    
    # 3. 创建测试分配并测试Worker任务分配信息
    print("=" * 60)
    assignment_test_result = create_test_assignment_and_test()
    
    # 4. 总结测试结果
    print("=" * 60)
    print(f"\n🎯 测试结果总结:")
    print(f"  Worker列表获取: {'✅ 成功' if len(workers) > 0 else '❌ 失败'}")
    print(f"  任务Worker列表: {'✅ 成功' if len(task_workers) > 0 else '❌ 失败'}")
    print(f"  Worker任务分配: {'✅ 成功' if assignment_test_result else '❌ 失败'}")
    
    if len(workers) > 0 and assignment_test_result:
        print(f"\n🎉 所有测试通过！Worker配置显示和任务分配功能正常！")
    else:
        print(f"\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
