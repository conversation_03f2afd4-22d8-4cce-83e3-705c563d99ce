#!/usr/bin/env python3
"""
测试Redis连接和任务管理器基本功能

验证Docker中的Redis服务是否能被任务管理系统正常使用
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis
import redis as sync_redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_redis_connection():
    """测试Redis连接"""
    
    logger.info("🔍 Testing Redis Connection for Task Manager")
    logger.info("=" * 50)
    
    # 测试同步Redis连接
    logger.info("📡 Testing Sync Redis Connection...")
    try:
        sync_r = sync_redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 基本连接测试
        pong = sync_r.ping()
        logger.info(f"✅ Sync Redis Ping: {pong}")
        
        # 获取Redis信息
        info = sync_r.info()
        logger.info(f"✅ Redis Version: {info.get('redis_version')}")
        logger.info(f"✅ Connected Clients: {info.get('connected_clients')}")
        logger.info(f"✅ Used Memory: {info.get('used_memory_human')}")
        
        # 测试基本操作
        test_key = f"test_key_{int(time.time())}"
        sync_r.set(test_key, "test_value", ex=60)  # 60秒过期
        value = sync_r.get(test_key)
        logger.info(f"✅ Set/Get Test: {value}")
        
        # 清理测试键
        sync_r.delete(test_key)
        
    except Exception as e:
        logger.error(f"❌ Sync Redis connection failed: {e}")
        return False
    
    # 测试异步Redis连接
    logger.info("\n📡 Testing Async Redis Connection...")
    try:
        async_r = redis.from_url("redis://localhost:6379/0")
        
        # 基本连接测试
        pong = await async_r.ping()
        logger.info(f"✅ Async Redis Ping: {pong}")
        
        # 测试队列操作
        queue_name = f"test_queue_{int(time.time())}"
        
        # 推送测试任务
        for i in range(5):
            task_data = f"task_{i}_{int(time.time())}"
            await async_r.lpush(queue_name, task_data)
        
        # 检查队列长度
        queue_length = await async_r.llen(queue_name)
        logger.info(f"✅ Queue Length: {queue_length}")
        
        # 弹出任务
        tasks = []
        for i in range(queue_length):
            task = await async_r.rpop(queue_name)
            if task:
                tasks.append(task.decode() if isinstance(task, bytes) else task)
        
        logger.info(f"✅ Retrieved Tasks: {len(tasks)}")
        
        # 清理测试队列
        await async_r.delete(queue_name)
        
        await async_r.close()
        
    except Exception as e:
        logger.error(f"❌ Async Redis connection failed: {e}")
        return False
    
    return True


async def test_task_queue_simulation():
    """模拟任务队列操作"""
    
    logger.info("\n🔄 Testing Task Queue Simulation...")
    logger.info("=" * 50)
    
    try:
        r = redis.from_url("redis://localhost:6379/0")
        
        # 模拟任务管理器的队列操作
        queue_names = [
            "crawler_queue",
            "crawler_queue_high", 
            "monitor_queue",
            "processor_queue",
            "cleanup_queue"
        ]
        
        # 清理现有队列
        for queue in queue_names:
            await r.delete(queue)
        
        # 模拟任务提交
        logger.info("📤 Simulating Task Submission...")
        
        # 高优先级任务
        for i in range(3):
            task_data = {
                "task_id": f"urgent_task_{i}",
                "urls": [f"https://example.com/product/{i}"],
                "priority": "urgent",
                "created_at": datetime.now().isoformat()
            }
            await r.lpush("crawler_queue_high", str(task_data))
        
        # 普通优先级任务
        for i in range(10):
            task_data = {
                "task_id": f"normal_task_{i}",
                "urls": [f"https://example.com/product/{i+100}"],
                "priority": "normal", 
                "created_at": datetime.now().isoformat()
            }
            await r.lpush("crawler_queue", str(task_data))
        
        # 检查队列状态
        logger.info("📊 Queue Status:")
        for queue in queue_names:
            length = await r.llen(queue)
            logger.info(f"  {queue}: {length} tasks")
        
        # 模拟任务处理
        logger.info("\n⚙️ Simulating Task Processing...")
        
        # 处理高优先级任务
        high_priority_count = await r.llen("crawler_queue_high")
        for i in range(high_priority_count):
            task = await r.rpop("crawler_queue_high")
            if task:
                logger.info(f"  Processed high priority task: {task.decode()[:50]}...")
        
        # 处理部分普通任务
        for i in range(5):
            task = await r.rpop("crawler_queue")
            if task:
                logger.info(f"  Processed normal task: {task.decode()[:50]}...")
        
        # 最终队列状态
        logger.info("\n📊 Final Queue Status:")
        for queue in queue_names:
            length = await r.llen(queue)
            logger.info(f"  {queue}: {length} tasks")
        
        # 清理测试数据
        for queue in queue_names:
            await r.delete(queue)
        
        await r.close()
        
        logger.info("✅ Task queue simulation completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Task queue simulation failed: {e}")
        return False


async def test_rate_limiting():
    """测试限流功能"""
    
    logger.info("\n🚦 Testing Rate Limiting with Redis...")
    logger.info("=" * 50)
    
    try:
        r = redis.from_url("redis://localhost:6379/0")
        
        # 模拟限流键
        rate_limit_key = "rate_limit:test"
        concurrent_key = "concurrent:test"
        
        # 清理现有键
        await r.delete(rate_limit_key, concurrent_key)
        
        # 测试并发限制
        max_concurrent = 2
        current_concurrent = 0
        
        logger.info(f"Testing concurrent limit: {max_concurrent}")
        
        # 模拟请求
        for i in range(5):
            # 检查当前并发数
            current = await r.scard(concurrent_key)
            
            if current < max_concurrent:
                # 添加到并发集合
                request_id = f"req_{i}_{int(time.time())}"
                await r.sadd(concurrent_key, request_id)
                current_concurrent += 1
                logger.info(f"  ✅ Request {i+1} accepted (concurrent: {current+1}/{max_concurrent})")
                
                # 模拟处理时间
                await asyncio.sleep(0.1)
                
                # 处理完成，移除
                await r.srem(concurrent_key, request_id)
                current_concurrent -= 1
                
            else:
                logger.info(f"  ❌ Request {i+1} rejected (concurrent limit reached)")
        
        # 测试速率限制
        logger.info("\nTesting rate limiting...")
        
        rate_limit_window = 60  # 60秒窗口
        max_requests = 10       # 最多10个请求
        
        # 清理速率限制键
        await r.delete(rate_limit_key)
        
        for i in range(15):
            # 检查当前窗口内的请求数
            current_count = await r.zcard(rate_limit_key)
            
            if current_count < max_requests:
                # 添加请求记录
                now = time.time()
                await r.zadd(rate_limit_key, {f"req_{i}": now})
                
                # 清理过期记录
                cutoff = now - rate_limit_window
                await r.zremrangebyscore(rate_limit_key, 0, cutoff)
                
                # 设置过期时间
                await r.expire(rate_limit_key, rate_limit_window)
                
                logger.info(f"  ✅ Rate limit request {i+1} accepted ({current_count+1}/{max_requests})")
            else:
                logger.info(f"  ❌ Rate limit request {i+1} rejected (rate limit exceeded)")
        
        # 清理测试数据
        await r.delete(rate_limit_key, concurrent_key)
        
        await r.close()
        
        logger.info("✅ Rate limiting test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Rate limiting test failed: {e}")
        return False


async def main():
    """主函数"""
    
    print("🧪 MonIt Redis & Task Manager Connection Test")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试Redis连接
    if await test_redis_connection():
        success_count += 1
    
    # 测试任务队列模拟
    if await test_task_queue_simulation():
        success_count += 1
    
    # 测试限流功能
    if await test_rate_limiting():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    print(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Redis is ready for Task Manager!")
        print("\n💡 Next Steps:")
        print("   1. Start Task Manager: python backend/scripts/start_task_manager.py")
        print("   2. Run Real Test: python backend/scripts/run_real_test.py")
        print("   3. Start Celery Worker: celery -A app.celery_app worker")
    else:
        print("❌ Some tests failed. Please check Redis configuration.")
    
    return success_count == total_tests


if __name__ == "__main__":
    asyncio.run(main())
