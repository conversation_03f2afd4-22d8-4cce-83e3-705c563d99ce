#!/usr/bin/env python3
"""
检查Redis中的执行记录
验证任务执行历史数据的存储情况
"""

import asyncio
import redis.asyncio as redis
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def check_redis_execution_records():
    """检查Redis中的执行记录"""
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    try:
        redis_client = redis.from_url("redis://localhost:6379/0")
        
        print(f"🔍 检查Redis中的执行记录")
        print(f"📋 任务ID: {task_id}")
        print("=" * 80)
        
        # 1. 检查任务执行记录索引
        print(f"\n1. 检查任务执行记录索引...")
        task_executions_key = f"task_execution:task:{task_id}:executions"
        execution_ids = await redis_client.lrange(task_executions_key, 0, -1)
        
        print(f"   键名: {task_executions_key}")
        print(f"   执行记录数: {len(execution_ids)}")
        
        if execution_ids:
            print(f"   执行记录ID列表:")
            for i, execution_id in enumerate(execution_ids):
                execution_id_str = execution_id.decode() if isinstance(execution_id, bytes) else execution_id
                print(f"     {i+1}. {execution_id_str}")
        else:
            print(f"   ❌ 没有找到执行记录")
        
        # 2. 检查所有执行历史记录
        print(f"\n2. 检查所有执行历史记录...")
        history_pattern = "task_execution:history:*"
        history_keys = await redis_client.keys(history_pattern)
        
        print(f"   模式: {history_pattern}")
        print(f"   找到记录数: {len(history_keys)}")
        
        if history_keys:
            print(f"   执行历史记录:")
            for i, key in enumerate(history_keys[:10]):  # 只显示前10个
                key_str = key.decode() if isinstance(key, bytes) else key
                execution_data = await redis_client.get(key_str)
                if execution_data:
                    try:
                        record = json.loads(execution_data)
                        print(f"     {i+1}. {key_str}")
                        print(f"        任务ID: {record.get('task_id', 'N/A')}")
                        print(f"        状态: {record.get('status', 'N/A')}")
                        print(f"        开始时间: {record.get('started_at', 'N/A')}")
                    except json.JSONDecodeError:
                        print(f"     {i+1}. {key_str} (解析失败)")
        
        # 3. 检查Worker执行记录
        print(f"\n3. 检查Worker执行记录...")
        worker_pattern = "task_execution:worker:*"
        worker_keys = await redis_client.keys(worker_pattern)
        
        print(f"   模式: {worker_pattern}")
        print(f"   找到记录数: {len(worker_keys)}")
        
        if worker_keys:
            print(f"   Worker执行记录:")
            for i, key in enumerate(worker_keys[:5]):  # 只显示前5个
                key_str = key.decode() if isinstance(key, bytes) else key
                execution_data = await redis_client.get(key_str)
                if execution_data:
                    try:
                        record = json.loads(execution_data)
                        print(f"     {i+1}. {key_str}")
                        print(f"        任务ID: {record.get('task_id', 'N/A')}")
                        print(f"        Worker ID: {record.get('worker_id', 'N/A')}")
                        print(f"        状态: {record.get('status', 'N/A')}")
                    except json.JSONDecodeError:
                        print(f"     {i+1}. {key_str} (解析失败)")
        
        # 4. 检查任务相关的所有键
        print(f"\n4. 检查任务相关的所有键...")
        task_pattern = f"*{task_id}*"
        task_keys = await redis_client.keys(task_pattern)
        
        print(f"   模式: {task_pattern}")
        print(f"   找到键数: {len(task_keys)}")
        
        if task_keys:
            print(f"   任务相关键:")
            for i, key in enumerate(task_keys):
                key_str = key.decode() if isinstance(key, bytes) else key
                print(f"     {i+1}. {key_str}")
        
        # 5. 检查监控任务存储
        print(f"\n5. 检查监控任务存储...")
        monitoring_task_key = f"monitoring_tasks:tasks:{task_id}"
        task_data = await redis_client.hgetall(monitoring_task_key)
        
        if task_data:
            print(f"   任务数据存在: {monitoring_task_key}")
            task_dict = {}
            for key, value in task_data.items():
                key_str = key.decode() if isinstance(key, bytes) else key
                value_str = value.decode() if isinstance(value, bytes) else value
                task_dict[key_str] = value_str
            
            print(f"   任务状态: {task_dict.get('status', 'N/A')}")
            print(f"   是否运行中: {task_dict.get('is_running', 'N/A')}")
            print(f"   最后运行: {task_dict.get('last_run', 'N/A')}")
            print(f"   下次运行: {task_dict.get('next_run', 'N/A')}")
        else:
            print(f"   ❌ 任务数据不存在: {monitoring_task_key}")
        
        await redis_client.close()
        
        print(f"\n" + "=" * 80)
        print(f"🎯 检查结果总结")
        
        if not execution_ids and not history_keys:
            print(f"❌ 该任务没有执行记录，可能从未被执行过")
            print(f"💡 建议: 手动执行一次任务来生成执行记录")
        elif execution_ids:
            print(f"✅ 找到任务执行记录，修复的统计逻辑应该能正常工作")
        else:
            print(f"⚠️  有执行历史记录但没有任务索引，可能存在数据不一致")
        
    except Exception as e:
        print(f"❌ 检查Redis执行记录失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_redis_execution_records())
