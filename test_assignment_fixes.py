#!/usr/bin/env python3
"""
测试Worker指派修复功能的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_duplicate_assignment():
    """测试重复指派检查"""
    
    # 任务ID和Worker ID
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    worker_id = "f1798516-0d34-4acb-b8dd-2118c15690a6"
    
    print("🧪 测试重复指派检查...")
    print(f"任务ID: {task_id}")
    print(f"Worker ID: {worker_id}")
    
    # 尝试创建重复的任务分配
    assignment_data = {
        "task_id": task_id,
        "worker_ids": [worker_id],
        "assignment_name": "重复测试分配",
        "description": "测试重复指派检查功能",
        "assignment_strategy": "health_based",
        "auto_failover": True,
        "total_urls": 49
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/task-assignments/",
            json=assignment_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignment = response.json()
            print("✅ 重复分配创建成功 (这可能表示后端没有重复检查)")
            print(f"分配ID: {assignment.get('assignment_id')}")
            return assignment.get('assignment_id')
        elif response.status_code == 400:
            error_detail = response.json().get('detail', '')
            if 'already assigned' in error_detail.lower() or '已指派' in error_detail:
                print("✅ 重复检查正常工作 - 后端拒绝了重复分配")
            else:
                print(f"❓ 后端返回400错误，但原因不明: {error_detail}")
            return None
        else:
            print(f"❌ 意外的响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def get_current_assignments(task_id):
    """获取当前任务分配"""
    
    print(f"\n📋 获取任务 {task_id} 的当前分配...")
    
    try:
        response = requests.get(f"{BASE_URL}/task-assignments/task/{task_id}/assignments")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ 当前有 {len(assignments)} 个分配")
            
            # 统计Worker分配情况
            worker_count = {}
            for assignment in assignments:
                if assignment.get('status') not in ['cancelled', 'failed']:
                    for worker_id in assignment.get('worker_ids', []):
                        worker_count[worker_id] = worker_count.get(worker_id, 0) + 1
            
            print("\n📊 Worker分配统计:")
            for worker_id, count in worker_count.items():
                status = "⚠️ 重复分配" if count > 1 else "✅ 正常"
                print(f"  Worker {worker_id}: {count} 次分配 {status}")
            
            return assignments, worker_count
        else:
            print(f"❌ 获取失败: {response.text}")
            return [], {}
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return [], {}

def test_worker_compatibility():
    """测试Worker兼容性检查"""
    
    print(f"\n🔍 测试Worker兼容性检查...")
    
    # 获取所有可用Worker
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/")
        if response.status_code == 200:
            workers = response.json()
            worker_ids = [w['worker_id'] for w in workers[:2]]  # 取前两个Worker
            
            print(f"测试Worker组合: {worker_ids}")
            
            # 检查兼容性
            compatibility_response = requests.post(
                f"{BASE_URL}/crawler-workers/check-group-compatibility",
                json={"worker_ids": worker_ids},
                headers={"Content-Type": "application/json"}
            )
            
            if compatibility_response.status_code == 200:
                result = compatibility_response.json()
                print(f"✅ 兼容性检查完成")
                print(f"  兼容性: {'✅ 兼容' if result.get('is_compatible') else '❌ 不兼容'}")
                print(f"  总Worker数: {result.get('total_workers')}")
                print(f"  有效Worker数: {result.get('valid_workers')}")
                print(f"  冲突Worker数: {result.get('conflicted_workers')}")
                return result
            else:
                print(f"❌ 兼容性检查失败: {compatibility_response.text}")
                return None
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 兼容性检查失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始测试Worker指派修复功能\n")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    # 1. 获取当前分配状态
    assignments, worker_count = get_current_assignments(task_id)
    
    # 2. 测试重复指派检查
    duplicate_assignment_id = test_duplicate_assignment()
    
    # 3. 再次检查分配状态
    print(f"\n📋 重复测试后的分配状态:")
    assignments_after, worker_count_after = get_current_assignments(task_id)
    
    # 4. 测试Worker兼容性检查
    compatibility_result = test_worker_compatibility()
    
    # 5. 总结测试结果
    print(f"\n🎯 测试结果总结:")
    print(f"  初始分配数量: {len(assignments)}")
    print(f"  测试后分配数量: {len(assignments_after)}")
    print(f"  重复分配创建: {'成功' if duplicate_assignment_id else '被阻止'}")
    print(f"  兼容性检查: {'正常' if compatibility_result else '失败'}")
    
    # 检查是否有重复分配
    has_duplicates = any(count > 1 for count in worker_count_after.values())
    print(f"  重复分配检测: {'❌ 发现重复' if has_duplicates else '✅ 无重复'}")

if __name__ == "__main__":
    main()
