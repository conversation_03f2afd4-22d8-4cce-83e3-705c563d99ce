"""
新爬取配置服务
用于处理新的爬取配置管理中创建的配置
"""

import json
import redis
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class CrawlConfigService:
    """新爬取配置服务"""
    
    def __init__(self):
        self.redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )
        self.config_key_prefix = "crawl_configs"
    
    async def get_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """获取单个爬取配置"""
        try:
            config_key = f"{self.config_key_prefix}:{config_id}"
            config_data = self.redis_client.hgetall(config_key)
            
            if not config_data:
                return None
            
            # 转换JSON字段
            for key in ['config', 'auth_config', 'tags']:
                if config_data.get(key):
                    try:
                        config_data[key] = json.loads(config_data[key])
                    except:
                        pass
            
            # 转换布尔值
            if 'is_active' in config_data:
                config_data['is_active'] = config_data['is_active'].lower() == 'true'
            if 'cache_enabled' in config_data:
                config_data['cache_enabled'] = config_data['cache_enabled'].lower() == 'true'
            
            # 转换数值
            for key in ['usage_count', 'max_concurrent_tasks', 'batch_timeout']:
                if config_data.get(key):
                    try:
                        config_data[key] = int(config_data[key])
                    except:
                        pass
            
            return config_data
            
        except Exception as e:
            logger.error(f"获取爬取配置失败 {config_id}: {e}")
            return None
    
    async def config_exists(self, config_id: str) -> bool:
        """检查配置是否存在"""
        try:
            config_key = f"{self.config_key_prefix}:{config_id}"
            return self.redis_client.exists(config_key) > 0
        except Exception as e:
            logger.error(f"检查配置存在性失败 {config_id}: {e}")
            return False
    
    async def is_config_active(self, config_id: str) -> bool:
        """检查配置是否活跃"""
        try:
            config = await self.get_config(config_id)
            return config and config.get('is_active', False)
        except Exception as e:
            logger.error(f"检查配置活跃状态失败 {config_id}: {e}")
            return False
    
    def get_config_name(self, config_id: str) -> str:
        """获取配置名称（同步方法，用于快速获取）"""
        try:
            config_key = f"{self.config_key_prefix}:{config_id}"
            name = self.redis_client.hget(config_key, 'name')
            return name or "未知配置"
        except Exception as e:
            logger.error(f"获取配置名称失败 {config_id}: {e}")
            return "未知配置"

# 创建全局实例
crawl_config_service = CrawlConfigService()
