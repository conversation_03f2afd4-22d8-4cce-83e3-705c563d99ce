"""
爬虫引擎实现 - API客户端模式

基于外部爬虫API服务的爬虫引擎实现。
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .base import BaseCrawlerEngine
from .interfaces import TaskStatus, ICrawlerEngine
from ..api_client import CrawlerAPIClient, APIConfig
from ..exceptions import CrawlerException
from ..schedulers.task_scheduler import TaskScheduler


class CrawlerEngine(BaseCrawlerEngine, ICrawlerEngine):
    """MonIt 爬虫引擎 - API客户端模式"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化爬虫引擎

        Args:
            config: 配置字典，包含API客户端和调度器配置
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # API客户端配置
        api_config_dict = self.config.get('api_client', {})
        self.api_config = APIConfig.from_dict(api_config_dict) if api_config_dict else APIConfig.from_env()

        # 初始化API客户端
        self.api_client = CrawlerAPIClient(self.api_config)

        # 任务调度器
        scheduler_config = self.config.get('scheduler', {})
        self.task_scheduler = TaskScheduler(scheduler_config)

        # 并发设置
        self.max_concurrent_tasks = self.config.get('max_concurrent_tasks', 10)
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

        # 状态管理
        self.is_running = False
        self.start_time: Optional[datetime] = None

        # 统计信息
        self.stats = {
            "tasks_processed": 0,
            "tasks_successful": 0,
            "tasks_failed": 0,
            "total_processing_time": 0.0
        }
    
    async def _initialize_components(self) -> None:
        """初始化所有组件"""
        try:
            # 启动API客户端
            await self.api_client.start()

            # 启动任务调度器
            await self.task_scheduler.start()

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise CrawlerException(f"Component initialization failed: {e}")
    
    async def _cleanup_components(self) -> None:
        """清理所有组件"""
        errors = []

        # 停止任务调度器
        if self.task_scheduler:
            try:
                await self.task_scheduler.stop()
            except Exception as e:
                errors.append(f"Task scheduler stop error: {e}")

        # 关闭API客户端
        if self.api_client:
            try:
                await self.api_client.close()
            except Exception as e:
                errors.append(f"API client close error: {e}")

        if errors:
            self.logger.warning(f"Component cleanup errors: {errors}")
    
    async def start(self) -> bool:
        """启动爬虫引擎

        Returns:
            bool: 启动是否成功
        """
        try:
            self.logger.info("Starting crawler engine...")

            # 初始化组件
            await self._initialize_components()

            # 设置运行状态
            self.is_running = True
            self.start_time = datetime.now()

            self.logger.info("Crawler engine started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start crawler engine: {e}")
            return False

    async def stop(self) -> bool:
        """停止爬虫引擎

        Returns:
            bool: 停止是否成功
        """
        try:
            self.logger.info("Stopping crawler engine...")

            # 停止组件
            await self._cleanup_components()

            # 设置停止状态
            self.is_running = False

            self.logger.info("Crawler engine stopped successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to stop crawler engine: {e}")
            return False
    
    async def crawl_url(
        self,
        url: str,
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """爬取单个URL

        Args:
            url: 目标URL
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 爬取结果
        """
        if not self.is_running:
            raise CrawlerException("Crawler engine is not running")

        async with self.semaphore:
            return await self._crawl_single_url(url, platform, options)
    
    async def _crawl_single_url(
        self,
        url: str,
        platform: str,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """爬取单个URL的内部实现"""
        start_time = datetime.now()
        self.stats["tasks_processed"] += 1

        try:
            self.logger.info(f"Starting to crawl {url} (platform: {platform})")

            # 调用外部爬虫API
            result = await self.api_client.crawl_url(url, platform, options)

            # 更新统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            self.stats["total_processing_time"] += processing_time
            self.stats["tasks_successful"] += 1

            # 添加元数据
            result.update({
                "processing_time": processing_time,
                "timestamp": datetime.now().isoformat(),
                "engine_stats": self.get_engine_status()
            })

            self.logger.info(f"Successfully crawled {url} in {processing_time:.2f}s")
            return result

        except Exception as e:
            self.stats["tasks_failed"] += 1
            self.logger.error(f"Failed to crawl {url}: {e}")
            raise CrawlerException(f"Crawl failed for {url}: {e}")

    async def crawl_batch(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """批量爬取URL

        Args:
            urls: URL列表
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 批量任务信息
        """
        if not self.is_running:
            raise CrawlerException("Crawler engine is not running")

        try:
            self.logger.info(f"Starting batch crawl for {len(urls)} URLs")

            # 调用外部爬虫API
            result = await self.api_client.crawl_batch(urls, platform, options)

            self.logger.info(f"Batch crawl initiated for {len(urls)} URLs")
            return result

        except Exception as e:
            self.logger.error(f"Failed to start batch crawl: {e}")
            raise CrawlerException(f"Batch crawl failed: {e}")

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        try:
            return await self.api_client.get_task_status(task_id)
        except Exception as e:
            self.logger.error(f"Failed to get task status for {task_id}: {e}")
            raise CrawlerException(f"Get task status failed: {e}")

    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务结果
        """
        try:
            return await self.api_client.get_task_result(task_id)
        except Exception as e:
            self.logger.error(f"Failed to get task result for {task_id}: {e}")
            raise CrawlerException(f"Get task result failed: {e}")

    async def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态

        Returns:
            Dict[str, Any]: 引擎状态信息
        """
        try:
            # 获取引擎状态信息
            uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0

            # 计算成功率
            total_tasks = self.stats["tasks_successful"] + self.stats["tasks_failed"]
            success_rate = (self.stats["tasks_successful"] / total_tasks) if total_tasks > 0 else 0.0

            # 计算平均处理时间
            avg_processing_time = (
                self.stats["total_processing_time"] / self.stats["tasks_successful"]
                if self.stats["tasks_successful"] > 0 else 0.0
            )

            status = {
                "is_running": self.is_running,
                "uptime": uptime,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "stats": self.stats.copy(),
                "success_rate": success_rate,
                "avg_processing_time": avg_processing_time,
                "api_client_stats": self.api_client.get_stats(),
                "config": {
                    "max_concurrent_tasks": self.max_concurrent_tasks,
                    "api_base_url": self.api_config.base_url,
                    "default_platform": self.api_config.default_platform
                }
            }

            return status

        except Exception as e:
            self.logger.error(f"Failed to get engine status: {e}")
            return {
                "is_running": self.is_running,
                "error": str(e)
            }