#!/usr/bin/env python3
"""
测试爬虫池页面修复
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_crawler_pool_api():
    """测试爬虫池API"""
    print("🔧 测试爬虫池API...")
    
    try:
        # 获取爬虫池列表
        response = requests.get(f"{BASE_URL}/api/v1/crawler/pools")
        if response.status_code == 200:
            pools = response.json()
            print(f"✅ 获取爬虫池列表成功: {len(pools)} 个池")
            
            for pool in pools:
                print(f"   池名称: {pool['pool_name']}")
                print(f"   池ID: {pool['pool_id']}")
                
                # 检查新旧架构字段
                if 'crawler_config_ids' in pool:
                    print(f"   新架构 - 配置数量: {len(pool['crawler_config_ids'])}")
                elif 'backends' in pool:
                    print(f"   旧架构 - 后端数量: {len(pool['backends'])}")
                else:
                    print(f"   未知架构")
                
                print(f"   负载均衡: {pool['load_balance_strategy']}")
                print("   ---")
            
            return pools
        else:
            print(f"❌ 获取爬虫池列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return []

def test_pool_health_check(pools):
    """测试池健康检查"""
    print("\n💓 测试池健康检查...")
    
    for pool in pools:
        pool_id = pool['pool_id']
        try:
            response = requests.get(f"{BASE_URL}/api/v1/crawler/pools/{pool_id}/health")
            if response.status_code == 200:
                health = response.json()
                print(f"✅ 池 {pool['pool_name']} 健康检查:")
                print(f"   整体健康评分: {health.get('overall_health_score', 0)}")
                print(f"   健康后端数: {health.get('healthy_backends', 0)}")
                print(f"   总后端数: {health.get('total_backends', 0)}")
            else:
                print(f"❌ 池 {pool['pool_name']} 健康检查失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 池 {pool['pool_name']} 健康检查异常: {e}")

def test_frontend_compatibility():
    """测试前端兼容性"""
    print("\n🖥️ 测试前端兼容性...")
    
    # 模拟前端可能遇到的数据结构
    test_pools = [
        {
            "pool_id": "test-new-arch",
            "pool_name": "新架构测试池",
            "crawler_config_ids": ["config1", "config2"],
            "load_balance_strategy": "weighted_round_robin"
        },
        {
            "pool_id": "test-old-arch", 
            "pool_name": "旧架构测试池",
            "backends": [
                {"id": "backend1", "name": "后端1"},
                {"id": "backend2", "name": "后端2"}
            ],
            "load_balance_strategy": "round_robin"
        },
        {
            "pool_id": "test-empty",
            "pool_name": "空池测试",
            "load_balance_strategy": "health_based"
        }
    ]
    
    print("测试前端数据处理逻辑:")
    
    for pool in test_pools:
        print(f"\n池: {pool['pool_name']}")
        
        # 模拟前端的配置数量计算
        config_count = pool.get('crawler_config_ids', [])
        backend_count = pool.get('backends', [])
        
        if config_count:
            count = len(config_count)
            print(f"   新架构 - 配置数量: {count}")
        elif backend_count:
            count = len(backend_count)
            print(f"   旧架构 - 后端数量: {count}")
        else:
            count = 0
            print(f"   空池 - 数量: {count}")
        
        # 模拟前端的安全访问
        safe_count = len(pool.get('crawler_config_ids', [])) or len(pool.get('backends', [])) or 0
        print(f"   安全计算结果: {safe_count}")

def main():
    """主测试流程"""
    print("🚀 开始测试爬虫池页面修复...")
    print("=" * 50)
    
    # 1. 测试API
    pools = test_crawler_pool_api()
    
    # 2. 测试健康检查
    if pools:
        test_pool_health_check(pools)
    
    # 3. 测试前端兼容性
    test_frontend_compatibility()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    print("\n📋 修复总结:")
    print("✅ 支持新旧架构兼容")
    print("✅ 添加安全的数组访问")
    print("✅ 更新字段名称和显示")
    print("✅ 防止undefined错误")
    
    print("\n🔗 前端页面:")
    print("   爬虫池管理: http://localhost:3000/crawler-pool")
    print("   新架构创建: http://localhost:3000/crawler-pool/create-new")
    
    print("\n💡 修复内容:")
    print("   - 将 'backends.length' 改为兼容新旧架构的安全访问")
    print("   - 添加 '?.length || 0' 防护")
    print("   - 更新显示文本从'后端'改为'配置'")
    print("   - 支持 crawler_config_ids 和 backends 双重字段")

if __name__ == "__main__":
    main()
