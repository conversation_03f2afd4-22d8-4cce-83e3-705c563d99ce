#!/usr/bin/env python3
"""
测试定时配置显示修复
"""

import asyncio
import aiohttp
import json
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

async def test_schedule_display_fix():
    """测试定时配置显示修复"""
    
    async with aiohttp.ClientSession() as session:
        print("🕐 测试定时配置显示修复...")
        
        # 1. 获取任务列表，查看定时配置数据
        print("\n1. 获取任务列表，检查定时配置数据...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    print(f"✅ 找到 {len(tasks)} 个任务")
                    
                    for i, task in enumerate(tasks):
                        print(f"\n任务 {i+1}: {task.get('name')}")
                        
                        # 检查schedule数据
                        schedule = task.get('schedule', {})
                        print(f"  定时配置:")
                        print(f"    类型: {schedule.get('type')}")
                        print(f"    时间: {schedule.get('time')} (原始值)")
                        print(f"    时区: {schedule.get('timezone')}")
                        
                        # 检查next_run数据
                        next_run = task.get('next_run')
                        print(f"  下次执行:")
                        print(f"    原始值: {next_run}")
                        
                        if next_run:
                            try:
                                # 模拟前端的时间格式化
                                date = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                                formatted = date.strftime('%Y-%m-%d %H:%M:%S')
                                print(f"    格式化后: {formatted}")
                            except Exception as e:
                                print(f"    格式化失败: {e}")
                        
                        # 模拟前端的定时配置文本生成
                        if schedule.get('type') == 'daily':
                            time = schedule.get('time') or '未设置'
                            schedule_text = f"每天 {time}"
                        elif schedule.get('type') == 'weekly':
                            time = schedule.get('time') or '未设置'
                            schedule_text = f"每周 {time}"
                        elif schedule.get('type') == 'hourly':
                            interval = schedule.get('interval') or 1
                            schedule_text = f"每{interval}小时"
                        else:
                            schedule_text = '未配置'
                        
                        print(f"  前端显示: {schedule_text}")
                        
                        if i >= 2:  # 只显示前3个任务
                            break
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 创建一个测试任务来验证修复
        print(f"\n2. 创建测试任务验证定时配置...")
        test_task_data = {
            "name": "定时配置测试任务",
            "description": "用于测试定时配置显示的任务",
            "schedule": {
                "type": "daily",
                "time": "14:30",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "taobao",
                "priority": "medium",
                "retry_count": 3,
                "timeout": 30,
                "batch_size": 10
            },
            "urls": [
                "https://example.com/schedule-test-1"
            ]
        }
        
        test_task_id = None
        try:
            async with session.post(
                f"{API_BASE_URL}/api/v1/monitoring-tasks/",
                json=test_task_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    test_task_id = data['data']['id']
                    print(f"✅ 创建测试任务成功: {test_task_id}")
                    
                    # 获取创建的任务详情
                    async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{test_task_id}") as detail_response:
                        if detail_response.status == 200:
                            detail_data = await detail_response.json()
                            task_detail = detail_data.get('data', {})
                            
                            print(f"\n测试任务详情:")
                            print(f"  名称: {task_detail.get('name')}")
                            
                            schedule = task_detail.get('schedule', {})
                            print(f"  定时配置:")
                            print(f"    类型: {schedule.get('type')}")
                            print(f"    时间: {schedule.get('time')}")
                            print(f"    时区: {schedule.get('timezone')}")
                            
                            next_run = task_detail.get('next_run')
                            print(f"  下次执行: {next_run}")
                            
                            # 验证修复效果
                            if schedule.get('time'):
                                print(f"✅ 时间字段不为null: {schedule.get('time')}")
                            else:
                                print(f"❌ 时间字段仍为null")
                            
                            if next_run:
                                print(f"✅ 下次执行时间已设置")
                            else:
                                print(f"⚠️ 下次执行时间未设置")
                        else:
                            print(f"❌ 获取测试任务详情失败: {detail_response.status}")
                else:
                    error_data = await response.json()
                    print(f"❌ 创建测试任务失败: {response.status} - {error_data}")
        except Exception as e:
            print(f"❌ 创建测试任务异常: {e}")
        
        # 3. 清理测试任务
        if test_task_id:
            print(f"\n3. 清理测试任务...")
            try:
                async with session.delete(f"{API_BASE_URL}/api/v1/monitoring-tasks/{test_task_id}") as response:
                    if response.status == 200:
                        print(f"✅ 测试任务清理成功")
                    else:
                        print(f"⚠️ 测试任务清理失败: {response.status}")
            except Exception as e:
                print(f"⚠️ 测试任务清理异常: {e}")
        
        print("\n🎉 定时配置显示修复测试完成!")
        print("\n📋 修复总结:")
        print("   ✅ 修复了 'schedule.time' 为null的显示问题")
        print("   ✅ 添加了时间格式化处理")
        print("   ✅ 改善了下次执行时间的显示格式")
        print("   ✅ 添加了默认值处理逻辑")

if __name__ == "__main__":
    asyncio.run(test_schedule_display_fix())
