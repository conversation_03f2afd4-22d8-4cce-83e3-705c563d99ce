/**
 * 时间处理工具函数
 * 统一处理不同调度类型的时间格式转换和显示
 */

import dayjs, { Dayjs } from 'dayjs';

export type ScheduleType = 'once' | 'daily' | 'weekly' | 'hourly' | 'custom';

/**
 * 时间格式枚举
 */
export enum TimeFormat {
  TIME_ONLY = 'HH:mm',           // 仅时间：09:30
  DATETIME_ISO = 'ISO',          // ISO格式：2025-01-01T09:30:00.000Z
  DATETIME_DISPLAY = 'YYYY-MM-DD HH:mm'  // 显示格式：2025-01-01 09:30
}

/**
 * 根据调度类型获取应该使用的时间格式
 */
export function getTimeFormatByScheduleType(scheduleType: ScheduleType): TimeFormat {
  switch (scheduleType) {
    case 'daily':
    case 'weekly':
    case 'hourly':
      return TimeFormat.TIME_ONLY;
    case 'once':
      return TimeFormat.DATETIME_ISO;
    case 'custom':
      return TimeFormat.TIME_ONLY; // cron表达式通常不需要额外时间字段
    default:
      return TimeFormat.TIME_ONLY;
  }
}

/**
 * 根据调度类型判断是否应该使用TimePicker
 */
export function shouldUseTimePicker(scheduleType: ScheduleType): boolean {
  return ['daily', 'weekly', 'hourly'].includes(scheduleType);
}

/**
 * 根据调度类型判断是否应该使用DatePicker
 */
export function shouldUseDatePicker(scheduleType: ScheduleType): boolean {
  return scheduleType === 'once';
}

/**
 * 将前端时间值转换为后端存储格式
 */
export function convertTimeForBackend(
  timeValue: string | Dayjs | undefined,
  scheduleType: ScheduleType
): string | undefined {
  console.log('=== convertTimeForBackend 调试信息 ===');
  console.log('输入参数:');
  console.log('  timeValue:', timeValue, '(type:', typeof timeValue, ')');
  console.log('  scheduleType:', scheduleType);

  if (!timeValue) {
    console.log('时间值为空，返回undefined');
    return undefined;
  }

  try {
    let dayjsTime: Dayjs;

    if (typeof timeValue === 'string') {
      console.log('处理字符串类型的时间值');

      // 如果是HH:mm格式的字符串，直接返回（已经是后端需要的格式）
      if (timeValue.match(/^\d{2}:\d{2}$/)) {
        console.log('检测到HH:mm格式');
        const format = getTimeFormatByScheduleType(scheduleType);
        console.log('调度类型对应的格式:', format);

        if (format === TimeFormat.TIME_ONLY) {
          console.log('直接返回HH:mm格式:', timeValue);
          return timeValue; // 直接返回HH:mm格式
        }
      }

      // 尝试解析其他格式的字符串
      console.log('尝试用dayjs解析字符串');
      dayjsTime = dayjs(timeValue);
    } else {
      console.log('处理Dayjs类型的时间值');
      dayjsTime = timeValue;
    }

    console.log('dayjs解析结果:');
    console.log('  isValid:', dayjsTime.isValid());
    console.log('  value:', dayjsTime.format());

    if (!dayjsTime.isValid()) {
      console.warn('Invalid time value:', timeValue);
      return undefined;
    }

    const format = getTimeFormatByScheduleType(scheduleType);
    console.log('最终使用的格式:', format);

    let result: string;
    switch (format) {
      case TimeFormat.TIME_ONLY:
        result = dayjsTime.format('HH:mm');
        break;
      case TimeFormat.DATETIME_ISO:
        result = dayjsTime.toISOString();
        break;
      default:
        result = dayjsTime.format('HH:mm');
        break;
    }

    console.log('转换结果:', result);
    return result;
  } catch (error) {
    console.warn('Time conversion for backend failed:', timeValue, error);
    return undefined;
  }
}

/**
 * 将后端时间值转换为前端显示格式
 */
export function convertTimeForFrontend(
  timeValue: string | undefined,
  scheduleType: ScheduleType
): Dayjs | undefined {
  if (!timeValue) return undefined;

  try {
    const format = getTimeFormatByScheduleType(scheduleType);
    
    switch (format) {
      case TimeFormat.TIME_ONLY:
        // 如果是HH:mm格式，创建今天的时间
        if (timeValue.match(/^\d{2}:\d{2}$/)) {
          const [hours, minutes] = timeValue.split(':');
          return dayjs().hour(parseInt(hours, 10)).minute(parseInt(minutes, 10)).second(0).millisecond(0);
        }
        // 如果是ISO格式，提取时间部分
        return dayjs(timeValue);
        
      case TimeFormat.DATETIME_ISO:
        return dayjs(timeValue);
        
      default:
        return dayjs(timeValue);
    }
  } catch (error) {
    console.warn('Time conversion for frontend failed:', timeValue, error);
    return undefined;
  }
}

/**
 * 将时间值转换为显示文本
 */
export function convertTimeForDisplay(
  timeValue: string | undefined,
  scheduleType: ScheduleType
): string {
  if (!timeValue) return '未设置';

  try {
    const format = getTimeFormatByScheduleType(scheduleType);
    
    switch (format) {
      case TimeFormat.TIME_ONLY:
        // 如果是HH:mm格式，直接返回
        if (timeValue.match(/^\d{2}:\d{2}$/)) {
          return timeValue;
        }
        // 如果是ISO格式，提取时间部分
        if (timeValue.match(/^\d{4}-\d{2}-\d{2}T(\d{2}:\d{2})/)) {
          return timeValue.match(/^\d{4}-\d{2}-\d{2}T(\d{2}:\d{2})/)![1];
        }
        return dayjs(timeValue).format('HH:mm');
        
      case TimeFormat.DATETIME_ISO:
        return dayjs(timeValue).format('YYYY-MM-DD HH:mm');
        
      default:
        return timeValue;
    }
  } catch (error) {
    console.warn('Time conversion for display failed:', timeValue, error);
    return '格式错误';
  }
}

/**
 * 获取统一的时间字段名
 * 为了向后兼容，仍然支持time和start_time字段
 */
export function getTimeFieldValue(schedule: any): string | undefined {
  // 优先使用新的统一字段
  if (schedule.execution_time) {
    return schedule.execution_time;
  }
  
  // 向后兼容：根据调度类型选择合适的字段
  if (schedule.type === 'daily' || schedule.type === 'weekly' || schedule.type === 'hourly') {
    return schedule.time || schedule.start_time;
  } else {
    return schedule.start_time || schedule.time;
  }
}

/**
 * 生成调度描述文本
 */
export function generateScheduleDescription(schedule: any): string {
  if (!schedule || !schedule.type) return '未配置';

  const timeValue = getTimeFieldValue(schedule);
  const displayTime = convertTimeForDisplay(timeValue, schedule.type);

  switch (schedule.type) {
    case 'daily':
      return `每天 ${displayTime}`;
    case 'weekly':
      const days = schedule.days || [];
      const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      const dayText = days.length > 0 
        ? days.map((d: number) => dayNames[d - 1]).join('、')
        : '每周';
      return `${dayText} ${displayTime}`;
    case 'hourly':
      const interval = schedule.interval || 1;
      return `每${interval}小时`;
    case 'once':
      return `单次执行 ${displayTime}`;
    case 'custom':
      return schedule.cron_expression 
        ? `自定义 (${schedule.cron_expression})`
        : '自定义调度';
    default:
      return schedule.type;
  }
}

/**
 * 验证时间格式是否正确
 */
export function validateTimeFormat(
  timeValue: string | undefined,
  scheduleType: ScheduleType
): { valid: boolean; message?: string } {
  if (!timeValue) {
    return { valid: false, message: '时间不能为空' };
  }

  const format = getTimeFormatByScheduleType(scheduleType);
  
  try {
    switch (format) {
      case TimeFormat.TIME_ONLY:
        if (!timeValue.match(/^\d{2}:\d{2}$/)) {
          return { valid: false, message: '时间格式应为HH:mm' };
        }
        const [hours, minutes] = timeValue.split(':');
        const h = parseInt(hours, 10);
        const m = parseInt(minutes, 10);
        if (h < 0 || h > 23 || m < 0 || m > 59) {
          return { valid: false, message: '时间值超出有效范围' };
        }
        break;
        
      case TimeFormat.DATETIME_ISO:
        const parsed = dayjs(timeValue);
        if (!parsed.isValid()) {
          return { valid: false, message: '日期时间格式无效' };
        }
        if (parsed.isBefore(dayjs())) {
          return { valid: false, message: '执行时间不能早于当前时间' };
        }
        break;
    }
    
    return { valid: true };
  } catch (error) {
    return { valid: false, message: '时间格式验证失败' };
  }
}
