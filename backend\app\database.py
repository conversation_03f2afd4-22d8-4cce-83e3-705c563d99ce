"""
数据库连接和初始化
"""

import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.sql import text
from sqlalchemy import event
from sqlalchemy.engine import Engine
import logging

from config.settings import settings
try:
    from app.models.base import Base
except ImportError:
    # 如果模型不存在，创建一个基本的Base
    from sqlalchemy.orm import declarative_base
    Base = declarative_base()

# 配置日志
logger = logging.getLogger(__name__)

# 创建异步引擎
engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    echo=settings.DATABASE_ECHO,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话的依赖注入函数"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

# 别名函数，用于兼容性
get_async_session = get_session


async def init_database():
    """初始化数据库"""
    logger.info("正在初始化数据库...")
    
    try:
        # 创建所有表
        async with engine.begin() as conn:
            # 创建所有表结构
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表结构创建完成")
            
            # 创建TimescaleDB hypertables
            await create_hypertables(conn)
            
            # 设置TimescaleDB压缩和保留策略
            await setup_timescale_policies(conn)
            
            # 创建索引
            await create_indexes(conn)
            
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def create_hypertables(conn):
    """创建TimescaleDB hypertables"""
    logger.info("正在创建TimescaleDB hypertables...")
    
    hypertables = [
        {
            'table': 'product_snapshots',
            'time_column': 'timestamp',
            'partitioning_column': 'product_id',
            'chunk_time_interval': '1 day'
        },
        {
            'table': 'system_logs',
            'time_column': 'logged_at',
            'partitioning_column': 'level',
            'chunk_time_interval': '1 day'
        }
    ]
    
    for table_config in hypertables:
        try:
            # 检查表是否已经是hypertable
            result = await conn.execute(text("""
                SELECT 1 FROM timescaledb_information.hypertables 
                WHERE hypertable_name = :table_name
            """), {"table_name": table_config['table']})
            
            if result.fetchone() is None:
                # 创建hypertable
                create_sql = f"""
                SELECT create_hypertable(
                    '{table_config['table']}', 
                    '{table_config['time_column']}',
                    chunk_time_interval => INTERVAL '{table_config['chunk_time_interval']}'
                );
                """
                await conn.execute(text(create_sql))
                logger.info(f"Hypertable {table_config['table']} 创建成功")
            else:
                logger.info(f"Hypertable {table_config['table']} 已存在")
                
        except Exception as e:
            logger.error(f"创建hypertable {table_config['table']} 失败: {e}")
            # 继续处理其他表，不要因为一个表失败就停止


async def setup_timescale_policies(conn):
    """设置TimescaleDB压缩和保留策略"""
    logger.info("正在设置TimescaleDB策略...")
    
    policies = [
        {
            'table': 'product_snapshots',
            'compress_after': '7 days',
            'retention_after': '365 days',
            'compress_segmentby': 'product_id'
        },
        {
            'table': 'system_logs',
            'compress_after': '3 days',
            'retention_after': '90 days',
            'compress_segmentby': 'level'
        }
    ]
    
    for policy in policies:
        try:
            # 启用压缩
            await conn.execute(text(f"""
                ALTER TABLE {policy['table']} SET (
                    timescaledb.compress,
                    timescaledb.compress_segmentby = '{policy['compress_segmentby']}'
                );
            """))
            
            # 添加压缩策略
            await conn.execute(text(f"""
                SELECT add_compression_policy(
                    '{policy['table']}', 
                    INTERVAL '{policy['compress_after']}'
                );
            """))
            
            # 添加保留策略
            await conn.execute(text(f"""
                SELECT add_retention_policy(
                    '{policy['table']}', 
                    INTERVAL '{policy['retention_after']}'
                );
            """))
            
            logger.info(f"表 {policy['table']} 的TimescaleDB策略设置完成")
            
        except Exception as e:
            # 策略可能已经存在，记录警告但继续
            logger.warning(f"设置表 {policy['table']} 的策略时出现警告: {e}")


async def create_indexes(conn):
    """创建必要的索引"""
    logger.info("正在创建数据库索引...")
    
    indexes = [
        # 商品表索引
        "CREATE INDEX IF NOT EXISTS idx_products_url ON products(url);",
        "CREATE INDEX IF NOT EXISTS idx_products_domain ON products(domain);",
        "CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);",
        "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);",
        
        # 商品快照表索引
        "CREATE INDEX IF NOT EXISTS idx_snapshots_product_time ON product_snapshots(product_id, timestamp DESC);",
        "CREATE INDEX IF NOT EXISTS idx_snapshots_task ON product_snapshots(task_id);",
        "CREATE INDEX IF NOT EXISTS idx_snapshots_success ON product_snapshots(success);",
        
        # 任务表索引
        "CREATE INDEX IF NOT EXISTS idx_tasks_status ON crawl_tasks(status);",
        "CREATE INDEX IF NOT EXISTS idx_tasks_scheduled ON crawl_tasks(scheduled_at);",
        "CREATE INDEX IF NOT EXISTS idx_tasks_created ON crawl_tasks(created_at);",
        
        # 配置表索引
        "CREATE INDEX IF NOT EXISTS idx_configs_domain ON crawl_configs(domain);",
        "CREATE INDEX IF NOT EXISTS idx_configs_enabled ON crawl_configs(enabled);",
        
        # 代理池索引
        "CREATE INDEX IF NOT EXISTS idx_proxy_status ON proxy_pools(status);",
        "CREATE INDEX IF NOT EXISTS idx_proxy_last_used ON proxy_pools(last_used_at);",
        
        # 系统日志索引
        "CREATE INDEX IF NOT EXISTS idx_logs_level ON system_logs(level);",
        "CREATE INDEX IF NOT EXISTS idx_logs_task ON system_logs(task_id);",
        "CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON system_logs(logged_at DESC);",
    ]
    
    for index_sql in indexes:
        try:
            await conn.execute(text(index_sql))
        except Exception as e:
            logger.warning(f"创建索引时出现警告: {e}")
    
    logger.info("数据库索引创建完成")


async def check_database_connection():
    """检查数据库连接状态"""
    try:
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            result.fetchone()
            return True
    except Exception as e:
        logger.error(f"数据库连接检查失败: {e}")
        return False


async def get_database_info():
    """获取数据库信息"""
    try:
        async with engine.begin() as conn:
            # PostgreSQL版本
            pg_version = await conn.execute(text("SELECT version()"))
            pg_version = pg_version.fetchone()[0]
            
            # TimescaleDB版本
            ts_version = await conn.execute(text("SELECT extversion FROM pg_extension WHERE extname = 'timescaledb'"))
            ts_version_result = ts_version.fetchone()
            ts_version = ts_version_result[0] if ts_version_result else "未安装"
            
            # 数据库大小
            db_size = await conn.execute(text("""
                SELECT pg_size_pretty(pg_database_size(current_database()))
            """))
            db_size = db_size.fetchone()[0]
            
            return {
                "postgresql_version": pg_version,
                "timescaledb_version": ts_version,
                "database_size": db_size,
                "connection_status": "connected"
            }
    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        return {
            "connection_status": "error",
            "error": str(e)
        }


# 在应用启动时执行数据库初始化
async def startup_database():
    """应用启动时的数据库初始化"""
    logger.info("应用启动 - 检查数据库连接...")
    
    # 检查连接
    if not await check_database_connection():
        raise RuntimeError("无法连接到数据库")
    
    # 执行初始化
    await init_database()
    
    logger.info("数据库初始化完成，应用可以正常启动")


# 在应用关闭时清理资源
async def shutdown_database():
    """应用关闭时的数据库清理"""
    logger.info("应用关闭 - 清理数据库连接...")
    await engine.dispose()
    logger.info("数据库连接已清理") 