#!/usr/bin/env python3
"""
端到端工作流程测试

测试完整的MonIt系统工作流程：
Excel上传 → URL池管理 → 任务创建 → 任务管理
"""

import requests
import json
import time
import os
import pandas as pd
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def create_test_excel():
    """创建测试Excel文件"""
    print("📝 创建测试Excel文件")
    
    urls = [
        "https://www.amazon.com/dp/B08N5WRWNW",
        "https://www.amazon.com.mx/dp/B08N5WRWNW", 
        "https://articulo.mercadolibre.com.mx/MLM-123456789",
        "https://www.ebay.com/itm/123456789",
        "https://www.aliexpress.com/item/123456789.html"
    ]
    
    df = pd.DataFrame({'URL': urls})
    filename = 'test_workflow.xlsx'
    df.to_excel(filename, index=False)
    
    print(f"✅ 测试文件创建成功: {filename}")
    print(f"   包含 {len(urls)} 个URL")
    
    return filename

def test_excel_upload(filename):
    """测试Excel上传功能"""
    print(f"\n📤 测试Excel上传: {filename}")
    
    try:
        with open(filename, 'rb') as f:
            files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/api/v1/excel/upload-and-parse", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Excel上传成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 新增URL: {result['new_urls']}")
            print(f"   - 重复URL: {result['duplicate_urls']}")
            return True
        else:
            print(f"❌ Excel上传失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Excel上传异常: {e}")
        return False

def test_url_pool_management():
    """测试URL池管理功能"""
    print(f"\n📋 测试URL池管理功能")
    
    # 获取URL池列表
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool")
    if response.status_code != 200:
        print(f"❌ 获取URL池失败: {response.status_code}")
        return None
    
    data = response.json()
    urls = data['data']
    
    print(f"✅ URL池管理:")
    print(f"   - 总URL数: {data['total']}")
    print(f"   - 当前页URL: {len(urls)}")
    
    if urls:
        print(f"   - 示例URL: {urls[0]['url'][:50]}...")
        
        # 返回前3个URL的ID用于任务创建
        url_ids = [url['id'] for url in urls[:3]]
        print(f"   - 选择 {len(url_ids)} 个URL用于任务创建")
        return url_ids
    else:
        print("❌ 没有可用的URL")
        return None

def test_task_creation_workflow(url_ids):
    """测试任务创建工作流程"""
    print(f"\n🚀 测试任务创建工作流程")
    
    # 1. URL预览
    print("1️⃣ URL预览...")
    preview_response = requests.post(f"{BASE_URL}/api/v1/tasks/preview-urls", 
                                   json={"url_ids": url_ids})
    
    if preview_response.status_code != 200:
        print(f"❌ URL预览失败: {preview_response.status_code}")
        return None
    
    preview_data = preview_response.json()
    print(f"   ✅ 预览成功: {preview_data['valid_count']}/{preview_data['total_count']} 个有效URL")
    
    # 2. 任务验证
    print("2️⃣ 任务验证...")
    validation_payload = {
        "name": f"端到端测试任务_{int(time.time())}",
        "url_ids": url_ids,
        "schedule": {
            "type": "daily",
            "enabled": True,
            "timezone": "Asia/Shanghai"
        }
    }
    
    validation_response = requests.post(f"{BASE_URL}/api/v1/tasks/validate", 
                                      json=validation_payload)
    
    if validation_response.status_code != 200:
        print(f"❌ 任务验证失败: {validation_response.status_code}")
        return None
    
    validation_data = validation_response.json()
    print(f"   ✅ 验证结果: {'通过' if validation_data['success'] else '失败'}")
    
    if not validation_data['success']:
        print(f"   ❌ 验证错误: {validation_data['errors']}")
        return None
    
    # 3. 任务创建
    print("3️⃣ 任务创建...")
    now = datetime.now()
    start_time = now + timedelta(minutes=5)
    
    creation_payload = {
        "name": f"端到端测试任务_{int(time.time())}",
        "description": "通过端到端测试创建的任务",
        "url_ids": url_ids,
        "schedule": {
            "type": "daily",
            "enabled": True,
            "start_time": start_time.isoformat(),
            "timezone": "Asia/Shanghai"
        },
        "config": {
            "platform": "all",
            "priority": "normal",
            "retry_count": 3,
            "timeout": 300,
            "concurrent_limit": 5,
            "batch_size": 10,
            "enable_notifications": True
        },
        "tags": ["端到端测试", "自动化测试"]
    }
    
    creation_response = requests.post(f"{BASE_URL}/api/v1/tasks/create-from-urls", 
                                    json=creation_payload)
    
    if creation_response.status_code != 200:
        print(f"❌ 任务创建失败: {creation_response.status_code}")
        if creation_response.content:
            try:
                error_data = creation_response.json()
                print(f"   错误详情: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   错误内容: {creation_response.text}")
        return None
    
    creation_data = creation_response.json()
    task_id = creation_data['task_id']
    
    print(f"   ✅ 任务创建成功:")
    print(f"      - 任务ID: {task_id}")
    print(f"      - 任务名称: {creation_data['task_name']}")
    print(f"      - URL数量: {creation_data['url_count']}")
    print(f"      - 调度信息: {creation_data['schedule_info']}")
    
    return task_id

def test_task_management(task_id):
    """测试任务管理功能"""
    print(f"\n📊 测试任务管理功能")
    
    # 1. 获取任务详情
    print("1️⃣ 获取任务详情...")
    detail_response = requests.get(f"{BASE_URL}/api/v1/tasks/{task_id}")
    
    if detail_response.status_code != 200:
        print(f"❌ 获取任务详情失败: {detail_response.status_code}")
        return False
    
    detail_data = detail_response.json()
    task_info = detail_data['data']
    
    print(f"   ✅ 任务详情:")
    print(f"      - 名称: {task_info['name']}")
    print(f"      - 状态: {task_info['status']}")
    print(f"      - URL数量: {task_info['url_count']}")
    print(f"      - 关联URL: {len(task_info.get('urls', []))}")
    
    # 2. 获取任务统计
    print("2️⃣ 获取任务统计...")
    stats_response = requests.get(f"{BASE_URL}/api/v1/tasks/stats")
    
    if stats_response.status_code != 200:
        print(f"❌ 获取任务统计失败: {stats_response.status_code}")
        return False
    
    stats_data = stats_response.json()
    stats_info = stats_data['data']
    
    print(f"   ✅ 任务统计:")
    print(f"      - 总任务数: {stats_info['total_tasks']}")
    print(f"      - 活跃任务: {stats_info['active_tasks']}")
    print(f"      - 已调度任务: {stats_info['scheduled_tasks']}")
    
    return True

def cleanup_test_files(filename):
    """清理测试文件"""
    try:
        if os.path.exists(filename):
            os.remove(filename)
            print(f"\n🧹 已清理测试文件: {filename}")
    except Exception as e:
        print(f"\n⚠️ 清理文件失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始端到端工作流程测试")
    print("=" * 60)
    print("测试流程: Excel上传 → URL池管理 → 任务创建 → 任务管理")
    print("=" * 60)
    
    filename = None
    passed_steps = 0
    total_steps = 4
    
    try:
        # 步骤1: 创建和上传Excel
        filename = create_test_excel()
        if test_excel_upload(filename):
            passed_steps += 1
            print("✅ 步骤1完成: Excel上传")
        else:
            print("❌ 步骤1失败: Excel上传")
            return
        
        # 等待数据处理
        time.sleep(2)
        
        # 步骤2: URL池管理
        url_ids = test_url_pool_management()
        if url_ids:
            passed_steps += 1
            print("✅ 步骤2完成: URL池管理")
        else:
            print("❌ 步骤2失败: URL池管理")
            return
        
        # 步骤3: 任务创建
        task_id = test_task_creation_workflow(url_ids)
        if task_id:
            passed_steps += 1
            print("✅ 步骤3完成: 任务创建")
        else:
            print("❌ 步骤3失败: 任务创建")
            return
        
        # 步骤4: 任务管理
        if test_task_management(task_id):
            passed_steps += 1
            print("✅ 步骤4完成: 任务管理")
        else:
            print("❌ 步骤4失败: 任务管理")
        
    finally:
        # 清理测试文件
        if filename:
            cleanup_test_files(filename)
    
    # 测试结果
    print(f"\n📊 端到端测试结果: {passed_steps}/{total_steps} 个步骤通过")
    
    if passed_steps == total_steps:
        print("🎉 端到端测试完全成功！")
        print("✨ MonIt系统工作流程运行正常")
        print("🚀 系统已准备好投入使用")
    else:
        print("⚠️ 部分步骤失败，请检查相关功能")
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    main()
