/**
 * 爬虫配置服务
 */

import axios, { AxiosInstance } from 'axios';

// 深度部分类型定义
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export interface BrowserConfig {
  headless: boolean;
  verbose: boolean;
  viewport_width: number;
  viewport_height: number;
  wait_for: number;
  timeout: number;
  ignore_https_errors: boolean;
  extra_args: string[];
}

export interface CrawlerConfig {
  method: string;
  verbose: boolean;
  check_robots_txt: boolean;
  fetch_ssl_certificate: boolean;
  simulate_user: boolean;
  magic: boolean;
  override_navigator: boolean;
  remove_overlay_elements: boolean;
  ignore_body_visibility: boolean;
  adjust_viewport_to_content: boolean;
  wait_until: string;
  wait_for_images: boolean;
  page_timeout: number;
  delay_before_return_html: number;
  js_only: boolean;
  scan_full_page: boolean;
  process_iframes: boolean;
  scroll_delay: number;
  cache_mode: string;
  screenshot: boolean;
  pdf: boolean;
  capture_mhtml: boolean;
  exclude_external_images: boolean;
  exclude_all_images: boolean;
  image_score_threshold: number;
  image_description_min_word_threshold: number;
  table_score_threshold: number;
  capture_network_requests: boolean;
  capture_console_messages: boolean;
  log_console: boolean;
  extraction_strategy: string;
  chunking_strategy: string;
  markdown_generator: string;
  bypass_cache: boolean;
}

export interface LLMConfig {
  query: string;
  provider: string;
  model: string;
  api_key: string;
  base_url: string;
  temperature: number;
  max_tokens: number;
  top_p: number;
}

export interface SchemaExtractionConfig {
  extraction_schema: any;
  instructions: string;
  validate_schema: boolean;
  return_raw: boolean;
}

export interface ContentProcessingConfig {
  word_count_threshold: number;
  css_selector: string;
  target_elements: string[];
  excluded_tags: string[];
  excluded_selector: string;
  remove_forms: boolean;
  only_text: boolean;
  prettify: boolean;
  parser_type: string;
  keep_data_attributes: boolean;
  keep_attrs: string[];
}

export interface LinkFilteringConfig {
  exclude_external_links: boolean;
  exclude_internal_links: boolean;
  exclude_social_media_links: boolean;
  exclude_domains: string[];
  social_media_domains: string[];
  exclude_external_images: boolean;
  exclude_all_images: boolean;
  image_score_threshold: number;
  image_description_min_word_threshold: number;
  table_score_threshold: number;
}

export interface SchedulerConfig {
  semaphore_count: number;
  mean_delay: number;
  max_range: number;
  pool_size: number;
  memory_threshold: number;
}

export interface MonitorConfig {
  display_mode: string;
  show_progress: boolean;
  log_errors: boolean;
}

export interface APIConfig {
  base_url: string;
  timeout: number;
  max_retries: number;
}

export interface CrawlerFullConfig {
  api: APIConfig;
  browser: BrowserConfig;
  crawler: CrawlerConfig;
  llm: LLMConfig;
  schema_extraction: SchemaExtractionConfig;
  content_processing: ContentProcessingConfig;
  link_filtering: LinkFilteringConfig;
  scheduler: SchedulerConfig;
  monitor: MonitorConfig;
}

export interface ServiceTestResult {
  status: 'success' | 'error' | 'warning' | 'unknown';
  message: string;
  response_time: number;
}

export interface ConnectionTestResult {
  api_server: ServiceTestResult;
  llm_service: ServiceTestResult;
  overall: {
    status: 'success' | 'error' | 'warning' | 'unknown';
    message: string;
  };
}

class CrawlerConfigService {
  private client: AxiosInstance;
  private baseUrl = '/api/v1/crawler-configs'; // 更新为新架构API
  private instanceBaseUrl = '/api/v1/crawler/instances'; // 实例配置API

  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
  }

  /**
   * 获取爬虫配置 - 迁移到新架构
   * 现在返回第一个可用的实例配置，保持向后兼容
   */
  async getConfig(): Promise<CrawlerFullConfig> {
    try {
      // 尝试获取实例配置列表
      const response = await this.client.get(this.instanceBaseUrl);
      const instances = response.data;

      if (instances && instances.length > 0) {
        // 返回第一个实例配置，转换为传统格式
        const instance = instances[0];
        return this._convertInstanceToLegacyFormat(instance);
      } else {
        // 如果没有实例配置，返回默认配置
        return this.getDefaultConfigFromServer();
      }
    } catch (error) {
      console.warn('Failed to get instance config, falling back to default:', error);
      return this.getDefaultConfigFromServer();
    }
  }

  /**
   * 获取默认爬虫配置 - 使用本地默认值
   */
  async getDefaultConfigFromServer(): Promise<CrawlerFullConfig> {
    // 返回本地默认配置，不再依赖传统API
    return this.getDefaultConfig();
  }

  /**
   * 更新爬虫配置 - 迁移到实例配置
   */
  async updateConfig(config: CrawlerFullConfig): Promise<{ message: string }> {
    try {
      // 检查是否已有实例配置
      const response = await this.client.get(this.instanceBaseUrl);
      const instances = response.data;

      if (instances && instances.length > 0) {
        // 更新第一个实例配置
        const instanceId = instances[0].config_id;
        const updateData = this._convertLegacyToInstanceFormat(config);
        await this.client.put(`${this.instanceBaseUrl}/${instanceId}`, updateData);
      } else {
        // 创建新的实例配置
        const createData = this._convertLegacyToInstanceFormat(config);
        await this.client.post(this.instanceBaseUrl, createData);
      }

      return { message: '配置更新成功' };
    } catch (error) {
      console.error('Failed to update config:', error);
      throw new Error('配置更新失败');
    }
  }

  /**
   * 测试爬虫连接 - 使用实例配置测试
   */
  async testConnection(): Promise<ConnectionTestResult> {
    try {
      // 获取第一个实例配置进行测试
      const response = await this.client.get(this.instanceBaseUrl);
      const instances = response.data;

      if (instances && instances.length > 0) {
        const instanceId = instances[0].config_id;
        const testResponse = await this.client.post(`${this.instanceBaseUrl}/${instanceId}/test`);
        return testResponse.data;
      } else {
        // 如果没有实例配置，返回模拟测试结果
        return {
          api_server: { status: 'warning', message: '未找到配置实例', response_time: 0 },
          llm_service: { status: 'warning', message: '未找到配置实例', response_time: 0 },
          overall: { status: 'warning', message: '请先创建爬虫实例配置' }
        };
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      return {
        api_server: { status: 'error', message: '连接测试失败', response_time: 0 },
        llm_service: { status: 'error', message: '连接测试失败', response_time: 0 },
        overall: { status: 'error', message: '连接测试失败，请检查配置' }
      };
    }
  }

  /**
   * 构建爬虫请求 - 使用实例配置构建
   */
  async buildCrawlRequest(urls: string[]): Promise<any> {
    try {
      // 获取第一个实例配置
      const response = await this.client.get(this.instanceBaseUrl);
      const instances = response.data;

      if (instances && instances.length > 0) {
        const instance = instances[0];
        // 基于实例配置构建请求
        return this._buildRequestFromInstance(instance, urls);
      } else {
        throw new Error('未找到可用的爬虫实例配置');
      }
    } catch (error) {
      console.error('Failed to build crawl request:', error);
      throw new Error('构建爬虫请求失败');
    }
  }

  /**
   * 将实例配置转换为传统格式 - 向后兼容
   */
  private _convertInstanceToLegacyFormat(instance: any): CrawlerFullConfig {
    return {
      api: {
        base_url: instance.api_endpoint || 'http://localhost:11234',
        timeout: instance.timeout || 30000,
        max_retries: instance.max_retries || 3
      },
      browser: instance.browser || this.getDefaultConfig().browser,
      crawler: instance.crawler || this.getDefaultConfig().crawler,
      llm: instance.llm || this.getDefaultConfig().llm,
      schema_extraction: instance.schema_extraction || this.getDefaultConfig().schema_extraction,
      content_processing: instance.content_processing || this.getDefaultConfig().content_processing,
      link_filtering: instance.link_filtering || this.getDefaultConfig().link_filtering,
      scheduler: instance.scheduler || this.getDefaultConfig().scheduler,
      monitor: instance.monitor || this.getDefaultConfig().monitor
    };
  }

  /**
   * 将传统格式转换为实例配置格式
   */
  private _convertLegacyToInstanceFormat(config: CrawlerFullConfig): any {
    return {
      config_name: `爬虫配置_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
      description: '从传统配置页面更新',
      api_endpoint: config.api.base_url,
      timeout: config.api.timeout,
      max_retries: config.api.max_retries,
      auth_config: {
        auth_type: 'api_key',
        api_key: config.llm.api_key || ''
      },
      browser: config.browser,
      crawler: config.crawler,
      llm: config.llm,
      schema_extraction: config.schema_extraction,
      content_processing: config.content_processing,
      link_filtering: config.link_filtering,
      scheduler: config.scheduler,
      monitor: config.monitor,
      max_concurrent: 2,
      weight: 1,
      priority: 1
    };
  }

  /**
   * 基于实例配置构建爬虫请求
   */
  private _buildRequestFromInstance(instance: any, urls: string[]): any {
    return {
      urls: urls,
      config: {
        api_endpoint: instance.api_endpoint,
        timeout: instance.timeout,
        browser: instance.browser,
        crawler: instance.crawler,
        llm: instance.llm,
        schema_extraction: instance.schema_extraction,
        content_processing: instance.content_processing,
        link_filtering: instance.link_filtering
      }
    };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): CrawlerFullConfig {
    return {
      api: {
        base_url: 'http://localhost:11234',
        timeout: 30000,
        max_retries: 3
      },
      browser: {
        headless: true,
        verbose: false,
        viewport_width: 1920,
        viewport_height: 1080,
        wait_for: 2,
        timeout: 30,
        ignore_https_errors: true,
        extra_args: [
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-blink-features=AutomationControlled',
          '--exclude-switches=enable-automation'
        ]
      },
      crawler: {
        method: 'arun_many',
        verbose: true,
        check_robots_txt: false,
        fetch_ssl_certificate: false,
        simulate_user: true,
        magic: true,
        override_navigator: true,
        remove_overlay_elements: true,
        ignore_body_visibility: true,
        adjust_viewport_to_content: true,
        wait_until: 'domcontentloaded',
        wait_for_images: false,
        page_timeout: 60000,
        delay_before_return_html: 0.1,
        js_only: false,
        scan_full_page: true,
        process_iframes: true,
        scroll_delay: 0.2,
        cache_mode: 'BYPASS',
        screenshot: false,
        pdf: false,
        capture_mhtml: false,
        exclude_external_images: false,
        exclude_all_images: false,
        image_score_threshold: 50,
        image_description_min_word_threshold: 50,
        table_score_threshold: 7,
        capture_network_requests: false,
        capture_console_messages: false,
        log_console: false,
        extraction_strategy: 'LLMExtractionStrategy',
        chunking_strategy: 'IdentityChunking',
        markdown_generator: 'DefaultMarkdownGenerator',
        bypass_cache: true
      },
      llm: {
        query: '提取页面中所有文章的标题、作者和发布时间',
        provider: 'openai',
        model: 'deepseek-v3-0324',
        api_key: 'sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi',
        base_url: 'https://api.lkeap.cloud.tencent.com/v1',
        temperature: 0,
        max_tokens: 16384,
        top_p: 0.9
      },
      schema_extraction: {
        extraction_schema: {
          "type": "object",
          "description": "电商商品完整信息提取结构",
          "properties": {
            "task_info": {
              "type": "object",
              "description": "任务执行信息",
              "properties": {
                "start_timestamp": {
                  "type": "string",
                  "pattern": "^[0-9]{14}$",
                  "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
                }
              },
              "required": ["start_timestamp"]
            },
            "product_basic_info": {
              "type": "object",
              "description": "商品基础信息",
              "properties": {
                "product_url": {
                  "type": "string",
                  "format": "uri",
                  "description": "商品链接"
                },
                "product_name": {
                  "type": "string",
                  "description": "商品名称"
                },
                "mlm_id": {
                  "type": "number",
                  "description": "商品MLM-ID中的数值部分"
                }
              },
              "required": ["product_url", "product_name", "mlm_id"]
            },
            "pricing_info": {
              "type": "object",
              "description": "价格和库存信息",
              "properties": {
                "sales_count": {
                  "type": "number",
                  "description": "商品销量（格式：+xxx vendidos中的数值）"
                },
                "current_price": {
                  "type": "number",
                  "description": "商品现价（数值）"
                },
                "original_price": {
                  "type": "number",
                  "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
                },
                "discount_rate": {
                  "type": "number",
                  "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
                },
                "stock_quantity": {
                  "type": "number",
                  "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
                },
                "stocktype_IsFull": {
                  "type": "number",
                  "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"
                }
              },
              "required": ["current_price"]
            },
            "category_info": {
              "type": "object",
              "description": "商品目录分类信息",
              "properties": {
                "category_breadcrumb": {
                  "type": "string",
                  "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"
                },
                "category_hierarchy": {
                  "type": "array",
                  "description": "商品各级目录详细信息",
                  "items": {
                    "type": "object",
                    "properties": {
                      "category_name": {
                        "type": "string",
                        "description": "目录名称"
                      },
                      "category_url": {
                        "type": "string",
                        "format": "uri",
                        "description": "目录链接"
                      },
                      "level": {
                        "type": "integer",
                        "description": "目录层级（1为顶级，2为二级，以此类推）"
                      }
                    },
                    "required": ["category_name", "level"]
                  }
                }
              },
              "required": ["category_breadcrumb"]
            },
            "seller_info": {
              "type": "object",
              "description": "销售商信息",
              "properties": {
                "seller_name": {
                  "type": "string",
                  "description": "商品销售商名称"
                },
                "seller_url": {
                  "type": "string",
                  "format": "uri",
                  "description": "商品销售商链接"
                }
              },
              "required": ["seller_name"]
            },
            "media_info": {
              "type": "object",
              "description": "商品媒体信息",
              "properties": {
                "main_image_url": {
                  "type": "string",
                  "format": "uri",
                  "description": "商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）"
                }
              },
              "required": ["main_image_url"]
            },
            "qa_section": {
              "type": "object",
              "description": "问答区域信息",
              "properties": {
                "questions": {
                  "type": "array",
                  "description": "商品页面的问题列表",
                  "items": {
                    "type": "object",
                    "properties": {
                      "question_content": {
                        "type": "string",
                        "description": "问题内容"
                      },
                      "question_time": {
                        "type": "string",
                        "description": "问题时间"
                      },
                      "answer_content": {
                        "type": "string",
                        "description": "问题回答"
                      }
                    },
                    "required": ["question_content", "answer_content"]
                  }
                },
                "all_questions_url": {
                  "type": "string",
                  "format": "uri",
                  "description": "商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接"
                }
              }
            },
            "rating_info": {
              "type": "object",
              "description": "评分信息",
              "properties": {
                "rating_score": {
                  "type": "number",
                  "minimum": 0,
                  "maximum": 5,
                  "description": "商品评分数（0-5分）"
                },
                "rating_count": {
                  "type": "integer",
                  "description": "商品评分数量"
                }
              },
              "required": ["rating_score", "rating_count"]
            },
            "reviews_section": {
              "type": "object",
              "description": "评论区域信息",
              "properties": {
                "reviews": {
                  "type": "array",
                  "description": "商品评论列表",
                  "items": {
                    "type": "object",
                    "properties": {
                      "review_content": {
                        "type": "string",
                        "description": "评论内容"
                      },
                      "review_rating": {
                        "type": "number",
                        "minimum": 0,
                        "maximum": 5,
                        "description": "评论评分（0-5分）"
                      },
                      "review_time": {
                        "type": "string",
                        "description": "评论时间"
                      }
                    },
                    "required": ["review_content", "review_time"]
                  }
                },
                "all_reviews_url": {
                  "type": "string",
                  "format": "uri",
                  "description": "Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接"
                }
              }
            }
          },
          "required": [
            "product_basic_info",
            "category_info",
            "pricing_info",
            "media_info",
            "rating_info"
          ]
        },
        instructions: `请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\":\\"Full\\"，商品有就是1，没有就是0)；
获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；
商品销售商名称、商品销售商链接；
商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；
商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；
商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；
商品评分数、商品评分数量、
商品的评论内容、评论评分、评论时间；
商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；
任务开始的时间戳YYYYMMDDHHMMSS`,
        validate_schema: true,
        return_raw: false
      },
      content_processing: {
        word_count_threshold: 200,
        css_selector: '',
        target_elements: [],
        excluded_tags: ['nav', 'footer', 'aside'],
        excluded_selector: '',
        remove_forms: false,
        only_text: false,
        prettify: false,
        parser_type: 'lxml',
        keep_data_attributes: false,
        keep_attrs: []
      },
      link_filtering: {
        exclude_external_links: false,
        exclude_internal_links: false,
        exclude_social_media_links: false,
        exclude_domains: ['example.com', 'ads.google.com', 'facebook.com', 'twitter.com'],
        social_media_domains: [
          'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
          'youtube.com', 'tiktok.com', 'pinterest.com', 'reddit.com'
        ],
        exclude_external_images: false,
        exclude_all_images: false,
        image_score_threshold: 3,
        image_description_min_word_threshold: 50,
        table_score_threshold: 7
      },
      scheduler: {
        semaphore_count: 2,
        mean_delay: 0.1,
        max_range: 3,
        pool_size: 10,
        memory_threshold: 4096
      },
      monitor: {
        display_mode: 'detailed',
        show_progress: true,
        log_errors: true
      }
    };
  }

  /**
   * 验证配置
   */
  validateConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证API配置
    if (config.api) {
      if (!config.api.base_url) {
        errors.push('API基础URL不能为空');
      }
      if (config.api.timeout && config.api.timeout < 1000) {
        errors.push('API超时时间不能小于1000ms');
      }
    }

    // 验证浏览器配置
    if (config.browser) {
      if (config.browser.viewport_width && config.browser.viewport_width < 800) {
        errors.push('视口宽度不能小于800px');
      }
      if (config.browser.viewport_height && config.browser.viewport_height < 600) {
        errors.push('视口高度不能小于600px');
      }
    }

    // 验证LLM配置
    if (config.llm) {
      if (!config.llm.api_key || config.llm.api_key.length < 10) {
        errors.push('LLM API密钥不能为空且长度至少10个字符');
      }
      if (!config.llm.base_url) {
        errors.push('LLM API基础URL不能为空');
      }
    }

    // 验证调度器配置
    if (config.scheduler) {
      if (config.scheduler.semaphore_count && config.scheduler.semaphore_count < 1) {
        errors.push('并发数量不能小于1');
      }
      if (config.scheduler.pool_size && config.scheduler.pool_size < 5) {
        errors.push('线程池大小不能小于5');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取配置预设
   */
  getConfigPresets(): { [key: string]: DeepPartial<CrawlerFullConfig> } {
    return {
      '高性能': {
        scheduler: {
          semaphore_count: 10,
          pool_size: 20,
          memory_threshold: 8192,
          mean_delay: 0.05,
          max_range: 2
        },
        crawler: {
          page_timeout: 30000,
          wait_for_images: false,
          screenshot: false,
          pdf: false
        }
      },
      '高质量': {
        browser: {
          wait_for: 5,
          timeout: 60
        },
        crawler: {
          scroll_delay: 0.5,
          image_score_threshold: 70,
          wait_for_images: true,
          page_timeout: 90000
        },
        scheduler: {
          semaphore_count: 3,
          mean_delay: 0.5,
          max_range: 5
        }
      },
      '反检测': {
        browser: {
          headless: false,
          extra_args: [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--exclude-switches=enable-automation',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
          ]
        },
        crawler: {
          simulate_user: true,
          magic: true,
          override_navigator: true,
          scroll_delay: 1.0,
          delay_before_return_html: 2.0
        },
        scheduler: {
          semaphore_count: 1,
          mean_delay: 2.0,
          max_range: 10
        }
      }
    };
  }
}

export const crawlerConfigService = new CrawlerConfigService();
