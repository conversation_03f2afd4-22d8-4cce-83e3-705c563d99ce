import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Progress, Typography, Space, Button } from 'antd';
import {
  ControlOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../store';
import { fetchSystemStatus } from '../../store/slices/systemSlice';
import { useNotification } from '../../hooks';
import { getMonitoringTasks, type MonitoringTask } from '../../services/monitoringTaskApi';
import './index.css';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const notification = useNotification();

  const [tasks, setTasks] = React.useState<MonitoringTask[]>([]);
  const [tasksLoading, setTasksLoading] = React.useState(false);
  const { status, currentMetrics, loading: systemLoading } = useAppSelector(state => state.system);

  // 加载监控任务数据
  const fetchMonitoringTasks = async () => {
    try {
      setTasksLoading(true);
      const response = await getMonitoringTasks();
      if (response.success) {
        setTasks(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch monitoring tasks:', error);
    } finally {
      setTasksLoading(false);
    }
  };

  useEffect(() => {
    // 加载初始数据
    fetchMonitoringTasks();
    dispatch(fetchSystemStatus());
  }, [dispatch]);

  // 计算任务统计 (使用MonitoringTask的状态值)
  const taskStats = {
    total: tasks.length,
    running: tasks.filter(t => t.status === 'active').length,
    completed: tasks.filter(t => t.status === 'stopped').length, // 已停止的任务视为已完成
    failed: 0, // MonitoringTask没有failed状态，暂时设为0
    pending: tasks.filter(t => t.status === 'paused' || t.status === 'draft').length,
  };

  const handleTestNotification = () => {
    notification.success('操作成功', '这是一个成功通知，用于测试成功样式的显示效果');
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <Title level={2}>系统概览</Title>
        <Text type="secondary">实时监控爬虫系统运行状态</Text>
      </div>

      {/* 任务统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={taskStats.total}
              prefix={<ControlOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={taskStats.running}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={taskStats.completed}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="失败"
              value={taskStats.failed}
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} className="content-row">
        {/* 系统状态 */}
        <Col xs={24} lg={12}>
          <Card title="系统状态" loading={systemLoading}>
            {status && (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div className="status-item">
                  <Text strong>系统状态:</Text>
                  <Text type={status.status === 'healthy' ? 'success' : 'danger'}>
                    {status.status === 'healthy' ? '正常' : '异常'}
                  </Text>
                </div>
                <div className="status-item">
                  <Text strong>运行时间:</Text>
                  <Text>{Math.floor(status.uptime / 3600)}小时</Text>
                </div>
                <div className="status-item">
                  <Text strong>版本:</Text>
                  <Text>{status.version}</Text>
                </div>
                
                <div className="services-status">
                  <Text strong>服务状态:</Text>
                  <div className="services-grid">
                    {Object.entries(status.services).map(([service, isRunning]) => (
                      <div key={service} className="service-item">
                        <div className={`service-indicator ${isRunning ? 'running' : 'stopped'}`} />
                        <Text>{service}</Text>
                      </div>
                    ))}
                  </div>
                </div>
              </Space>
            )}
          </Card>
        </Col>

        {/* 系统资源 */}
        <Col xs={24} lg={12}>
          <Card title="系统资源">
            {currentMetrics ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div className="metric-item">
                  <div className="metric-header">
                    <Text>CPU 使用率</Text>
                    <Text strong>{currentMetrics.cpu_usage.toFixed(1)}%</Text>
                  </div>
                  <Progress
                    percent={currentMetrics.cpu_usage}
                    status={currentMetrics.cpu_usage > 80 ? 'exception' : 'normal'}
                    showInfo={false}
                  />
                </div>
                
                <div className="metric-item">
                  <div className="metric-header">
                    <Text>内存使用率</Text>
                    <Text strong>{currentMetrics.memory_usage.toFixed(1)}%</Text>
                  </div>
                  <Progress
                    percent={currentMetrics.memory_usage}
                    status={currentMetrics.memory_usage > 80 ? 'exception' : 'normal'}
                    showInfo={false}
                  />
                </div>
                
                <div className="metric-item">
                  <div className="metric-header">
                    <Text>磁盘使用率</Text>
                    <Text strong>{currentMetrics.disk_usage.toFixed(1)}%</Text>
                  </div>
                  <Progress
                    percent={currentMetrics.disk_usage}
                    status={currentMetrics.disk_usage > 90 ? 'exception' : 'normal'}
                    showInfo={false}
                  />
                </div>
                
                <div className="queue-stats">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="队列任务"
                        value={currentMetrics.queue_size}
                        prefix={<ArrowUpOutlined style={{ color: '#1890ff' }} />}
                        valueStyle={{ fontSize: '16px', color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="活跃任务"
                        value={currentMetrics.active_tasks}
                        prefix={<ArrowDownOutlined style={{ color: '#52c41a' }} />}
                        valueStyle={{ fontSize: '16px', color: '#52c41a' }}
                      />
                    </Col>
                  </Row>
                </div>
              </Space>
            ) : (
              <Text type="secondary">暂无系统资源数据</Text>
            )}
          </Card>
        </Col>
      </Row>

      {/* 测试区域 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="测试功能">
            <Space>
              <Button
                type="primary"
                onClick={handleTestNotification}
              >
                测试成功通知
              </Button>
              <Button
                onClick={() => notification.error('错误测试', '这是一个错误通知，用于测试错误样式的显示效果')}
                danger
              >
                测试错误通知
              </Button>
              <Button
                onClick={() => notification.warning('警告测试', '这是一个警告通知，用于测试警告样式的显示效果')}
              >
                测试警告通知
              </Button>
              <Button
                onClick={() => notification.info('信息测试', '这是一个信息通知，用于测试信息样式的显示效果')}
              >
                测试信息通知
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
