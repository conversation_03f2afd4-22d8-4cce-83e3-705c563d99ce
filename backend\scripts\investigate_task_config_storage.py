#!/usr/bin/env python3
"""
调查任务配置存储和读取问题

检查URL池创建任务时的配置存储情况
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def investigate_task_config_storage():
    """调查任务配置存储和读取问题"""
    base_url = "http://localhost:8000"
    
    print("🔍 调查任务配置存储和读取问题")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/config-storage-test",
            "platform": "mercadolibre",
            "title": "配置存储测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'config_storage_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'config_storage_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:config_storage_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 创建任务（模拟URL池创建）
        print(f"\n📋 步骤2: 创建任务（模拟URL池创建）")
        
        create_data = {
            "name": "配置存储调查测试任务",
            "description": "用于调查配置存储和读取问题的测试任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "17:45",  # 每日执行时间
                "end_time": "23:15",  # 结束时间
                "timezone": "Asia/Shanghai",
                "max_runs": 25,
                "enable_random_delay": True,
                "random_delay_min": 10,
                "random_delay_max": 45
            },
            "config": {
                "platform": "mercadolibre",
                "priority": "high",
                "batch_size": 18,
                "timeout": 480,  # 超时时间
                "retry_count": 6,  # 重试次数
                "concurrent_limit": 15,  # 并发限制
                "enable_notifications": True
            },
            "tags": ["config_storage_test"]
        }
        
        print(f"  发送的配置数据:")
        print(f"    基础配置:")
        print(f"      重试次数: {create_data['config']['retry_count']}")
        print(f"      超时时间: {create_data['config']['timeout']}")
        print(f"      并发限制: {create_data['config']['concurrent_limit']}")
        print(f"    调度配置:")
        print(f"      每日执行时间: {create_data['schedule']['time']}")
        print(f"      结束时间: {create_data['schedule']['end_time']}")
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建成功: {task_id}")
            
            # 步骤3: 检查Redis中的原始存储数据
            print(f"\n📋 步骤3: 检查Redis中的原始存储数据")
            
            # 检查任务基础信息
            task_key = f"monitoring_tasks:tasks:{task_id}"
            task_basic_data = r.hgetall(task_key)
            print(f"  任务基础信息: {dict(task_basic_data)}")
            
            # 检查配置数据
            config_key = f"monitoring_tasks:tasks:{task_id}:config"
            config_data = r.hgetall(config_key)
            print(f"  配置数据: {dict(config_data)}")
            
            # 检查调度数据
            schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
            schedule_data = r.hgetall(schedule_key)
            print(f"  调度数据: {dict(schedule_data)}")
            
            # 步骤4: 通过API获取任务详情
            print(f"\n📋 步骤4: 通过API获取任务详情")
            
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if detail_response.status_code == 200:
                detail_result = detail_response.json()
                task_data = detail_result.get('data', {})
                api_config = task_data.get('config', {})
                api_schedule = task_data.get('schedule', {})
                
                print(f"  API返回的配置:")
                print(f"    重试次数: {api_config.get('retry_count')}")
                print(f"    超时时间: {api_config.get('timeout')}")
                print(f"    并发限制: {api_config.get('concurrent_limit')}")
                print(f"  API返回的调度:")
                print(f"    每日执行时间: {api_schedule.get('time')}")
                print(f"    结束时间: {api_schedule.get('end_time')}")
                
                # 步骤5: 对比分析
                print(f"\n📋 步骤5: 存储vs读取对比分析")
                
                print(f"  🔍 基础配置对比:")
                
                # 重试次数
                stored_retry = config_data.get('retry_count')
                api_retry = api_config.get('retry_count')
                original_retry = create_data['config']['retry_count']
                print(f"    重试次数:")
                print(f"      原始值: {original_retry}")
                print(f"      Redis存储: {stored_retry} (类型: {type(stored_retry)})")
                print(f"      API返回: {api_retry} (类型: {type(api_retry)})")
                print(f"      存储正确: {'✅' if str(stored_retry) == str(original_retry) else '❌'}")
                print(f"      读取正确: {'✅' if api_retry == original_retry else '❌'}")
                
                # 超时时间
                stored_timeout = config_data.get('timeout')
                api_timeout = api_config.get('timeout')
                original_timeout = create_data['config']['timeout']
                print(f"    超时时间:")
                print(f"      原始值: {original_timeout}")
                print(f"      Redis存储: {stored_timeout} (类型: {type(stored_timeout)})")
                print(f"      API返回: {api_timeout} (类型: {type(api_timeout)})")
                print(f"      存储正确: {'✅' if str(stored_timeout) == str(original_timeout) else '❌'}")
                print(f"      读取正确: {'✅' if api_timeout == original_timeout else '❌'}")
                
                # 并发限制
                stored_concurrent = config_data.get('concurrent_limit')
                api_concurrent = api_config.get('concurrent_limit')
                original_concurrent = create_data['config']['concurrent_limit']
                print(f"    并发限制:")
                print(f"      原始值: {original_concurrent}")
                print(f"      Redis存储: {stored_concurrent} (类型: {type(stored_concurrent)})")
                print(f"      API返回: {api_concurrent} (类型: {type(api_concurrent)})")
                print(f"      存储正确: {'✅' if str(stored_concurrent) == str(original_concurrent) else '❌'}")
                print(f"      读取正确: {'✅' if api_concurrent == original_concurrent else '❌'}")
                
                print(f"\n  🔍 调度配置对比:")
                
                # 执行时间
                stored_time = schedule_data.get('time')
                api_time = api_schedule.get('time')
                original_time = create_data['schedule']['time']
                print(f"    每日执行时间:")
                print(f"      原始值: {original_time}")
                print(f"      Redis存储: {stored_time} (类型: {type(stored_time)})")
                print(f"      API返回: {api_time} (类型: {type(api_time)})")
                print(f"      存储正确: {'✅' if stored_time == original_time else '❌'}")
                print(f"      读取正确: {'✅' if api_time == original_time else '❌'}")
                
                # 结束时间
                stored_end_time = schedule_data.get('end_time')
                api_end_time = api_schedule.get('end_time')
                original_end_time = create_data['schedule']['end_time']
                print(f"    结束时间:")
                print(f"      原始值: {original_end_time}")
                print(f"      Redis存储: {stored_end_time} (类型: {type(stored_end_time)})")
                print(f"      API返回: {api_end_time} (类型: {type(api_end_time)})")
                print(f"      存储正确: {'✅' if stored_end_time == original_end_time else '❌'}")
                print(f"      读取正确: {'✅' if api_end_time == original_end_time else '❌'}")
                
                # 步骤6: 问题诊断
                print(f"\n📋 步骤6: 问题诊断")
                
                storage_issues = []
                reading_issues = []
                
                # 检查存储问题
                if str(stored_retry) != str(original_retry):
                    storage_issues.append("重试次数存储错误")
                if str(stored_timeout) != str(original_timeout):
                    storage_issues.append("超时时间存储错误")
                if str(stored_concurrent) != str(original_concurrent):
                    storage_issues.append("并发限制存储错误")
                if stored_time != original_time:
                    storage_issues.append("执行时间存储错误")
                if stored_end_time != original_end_time:
                    storage_issues.append("结束时间存储错误")
                
                # 检查读取问题
                if api_retry != original_retry:
                    reading_issues.append("重试次数读取错误")
                if api_timeout != original_timeout:
                    reading_issues.append("超时时间读取错误")
                if api_concurrent != original_concurrent:
                    reading_issues.append("并发限制读取错误")
                if api_time != original_time:
                    reading_issues.append("执行时间读取错误")
                if api_end_time != original_end_time:
                    reading_issues.append("结束时间读取错误")
                
                print(f"  📊 诊断结果:")
                if storage_issues:
                    print(f"    ❌ 存储问题:")
                    for issue in storage_issues:
                        print(f"      - {issue}")
                else:
                    print(f"    ✅ 存储正常: 所有配置都正确存储到Redis")
                
                if reading_issues:
                    print(f"    ❌ 读取问题:")
                    for issue in reading_issues:
                        print(f"      - {issue}")
                else:
                    print(f"    ✅ 读取正常: API正确返回所有配置")
                
                # 结论
                print(f"\n  🎯 问题根源:")
                if storage_issues and not reading_issues:
                    print(f"    📝 问题在于存储: URL池创建任务时没有正确存储配置")
                elif not storage_issues and reading_issues:
                    print(f"    📖 问题在于读取: 监控任务页面没有正确读取配置")
                elif storage_issues and reading_issues:
                    print(f"    🔄 问题在于存储和读取: 两个环节都有问题")
                else:
                    print(f"    ✅ 后端存储和读取都正常: 问题可能在前端显示")
                
            else:
                print(f"  ❌ 获取任务详情失败: {detail_response.text}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 配置存储调查完成!")
        
    except Exception as e:
        print(f"❌ 调查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:config_storage_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    investigate_task_config_storage()
