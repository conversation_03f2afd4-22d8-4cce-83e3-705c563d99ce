#!/usr/bin/env python3
"""
简化的Redis持久化测试
专注于测试Redis存储和恢复功能，不依赖复杂的爬虫组件
"""

import asyncio
import redis
import json
import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleRedisTaskManager:
    """简化的Redis任务管理器，专注于持久化功能"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self.redis_client = None
        
        # Redis键名定义
        self.pending_queue_key = "taskmanager:pending_batches"
        self.running_hash_key = "taskmanager:running_batches"
        self.completed_set_key = "taskmanager:completed_batches"
        self.failed_set_key = "taskmanager:failed_batches"
        self.manager_state_key = "taskmanager:state"
        
        # 内存状态
        self.pending_tasks = []
        self.running_tasks = {}
        self.completed_tasks = set()
        self.failed_tasks = set()
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            self.redis_client.ping()  # 同步调用
            logger.info("✅ Connected to Redis")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            return False
    
    async def submit_task(self, task_data: Dict[str, Any]) -> str:
        """提交任务到Redis"""
        try:
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(task_data.get('urls', []))}"
            
            # 创建任务批次
            batch_data = {
                "batch_id": f"batch_{task_id}",
                "task_id": task_id,
                "urls": task_data.get("urls", []),
                "platform": task_data.get("platform", "mercadolibre"),
                "priority": task_data.get("priority", "normal"),
                "status": "pending",
                "created_at": datetime.now().isoformat(),
                "options": task_data.get("options", {})
            }
            
            # 保存到内存
            self.pending_tasks.append(batch_data)
            
            # 持久化到Redis
            await self._persist_to_redis(batch_data)
            
            logger.info(f"✅ Task submitted: {task_id} with {len(batch_data['urls'])} URLs")
            return task_id
            
        except Exception as e:
            logger.error(f"❌ Failed to submit task: {e}")
            raise
    
    async def _persist_to_redis(self, batch_data: Dict[str, Any]):
        """持久化批次数据到Redis"""
        try:
            # 添加到待处理队列
            self.redis_client.lpush(
                self.pending_queue_key,
                json.dumps(batch_data)
            )

            # 保存管理器状态
            state_data = {
                "pending_count": len(self.pending_tasks),
                "running_count": len(self.running_tasks),
                "completed_count": len(self.completed_tasks),
                "failed_count": len(self.failed_tasks),
                "last_update": datetime.now().isoformat()
            }

            self.redis_client.set(
                self.manager_state_key,
                json.dumps(state_data),
                ex=3600  # 1小时过期
            )
            
            logger.info(f"💾 Persisted batch to Redis: {batch_data['batch_id']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to persist to Redis: {e}")
            raise
    
    async def get_redis_state(self) -> Dict[str, Any]:
        """获取Redis中的状态"""
        try:
            state = {}

            # 获取队列长度
            state["pending_count"] = self.redis_client.llen(self.pending_queue_key)
            state["running_count"] = self.redis_client.hlen(self.running_hash_key)
            state["completed_count"] = self.redis_client.scard(self.completed_set_key)
            state["failed_count"] = self.redis_client.scard(self.failed_set_key)

            # 获取管理器状态
            manager_state_raw = self.redis_client.get(self.manager_state_key)
            if manager_state_raw:
                state["manager_state"] = json.loads(manager_state_raw)

            # 获取一些示例数据
            pending_sample = self.redis_client.lrange(self.pending_queue_key, 0, 2)
            state["pending_sample"] = [json.loads(item) for item in pending_sample]
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Failed to get Redis state: {e}")
            return {}
    
    async def clear_redis_data(self):
        """清理Redis数据"""
        try:
            keys_to_delete = [
                self.pending_queue_key,
                self.running_hash_key,
                self.completed_set_key,
                self.failed_set_key,
                self.manager_state_key
            ]

            for key in keys_to_delete:
                self.redis_client.delete(key)
            
            logger.info("🧹 Cleared Redis data")
            
        except Exception as e:
            logger.error(f"❌ Failed to clear Redis data: {e}")
    
    async def close(self):
        """关闭连接"""
        if self.redis_client:
            self.redis_client.close()
            logger.info("🔌 Redis connection closed")

async def test_redis_persistence():
    """测试Redis持久化功能"""
    logger.info("🧪 Starting Redis Persistence Test")
    logger.info("=" * 50)
    
    manager = SimpleRedisTaskManager()
    
    try:
        # 连接Redis
        if not await manager.connect():
            logger.error("❌ Cannot connect to Redis, test aborted")
            return False
        
        # 清理之前的数据
        await manager.clear_redis_data()
        
        # 测试数据
        test_tasks = [
            {
                "urls": [f"https://mercadolibre.com/product{i}" for i in range(1, 11)],
                "platform": "mercadolibre",
                "priority": "normal",
                "options": {"test": True, "batch": 1}
            },
            {
                "urls": [f"https://mercadolibre.com/product{i}" for i in range(11, 21)],
                "platform": "mercadolibre", 
                "priority": "high",
                "options": {"test": True, "batch": 2}
            }
        ]
        
        # 提交测试任务
        logger.info("\n📤 Submitting test tasks...")
        task_ids = []
        for i, task_data in enumerate(test_tasks):
            task_id = await manager.submit_task(task_data)
            task_ids.append(task_id)
            logger.info(f"  Task {i+1}: {task_id}")
        
        # 检查Redis状态
        logger.info("\n💾 Checking Redis state...")
        redis_state = await manager.get_redis_state()
        
        logger.info(f"  Pending tasks: {redis_state['pending_count']}")
        logger.info(f"  Running tasks: {redis_state['running_count']}")
        logger.info(f"  Completed tasks: {redis_state['completed_count']}")
        logger.info(f"  Failed tasks: {redis_state['failed_count']}")
        
        if redis_state.get("manager_state"):
            logger.info(f"  Manager state: {redis_state['manager_state']}")
        
        if redis_state.get("pending_sample"):
            logger.info(f"  Sample pending task: {redis_state['pending_sample'][0]['batch_id']}")
        
        # 验证结果
        success = (
            redis_state['pending_count'] == len(test_tasks) and
            len(task_ids) == len(test_tasks)
        )
        
        if success:
            logger.info("\n🎉 Redis persistence test PASSED!")
            logger.info(f"✅ Successfully stored {len(test_tasks)} tasks in Redis")
        else:
            logger.info("\n❌ Redis persistence test FAILED!")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        await manager.close()

if __name__ == "__main__":
    success = asyncio.run(test_redis_persistence())
    sys.exit(0 if success else 1)
