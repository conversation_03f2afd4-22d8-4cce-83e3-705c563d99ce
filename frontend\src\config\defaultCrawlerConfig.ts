/**
 * 统一的爬虫配置默认值
 * 用于传统配置页面和新配置管理页面
 */

export interface DefaultCrawlerConfig {
  // Browser配置
  browser: {
    headless: boolean;
    verbose: boolean;
    viewport_width: number;
    viewport_height: number;
    wait_for: number;
    timeout: number;
    ignore_https_errors: boolean;
    extra_args: string[];
  };
  
  // Crawler配置
  crawler: {
    method: string;
    verbose: boolean;
    check_robots_txt: boolean;
    fetch_ssl_certificate: boolean;
    simulate_user: boolean;
    magic: boolean;
    override_navigator: boolean;
    remove_overlay_elements: boolean;
    ignore_body_visibility: boolean;
    adjust_viewport_to_content: boolean;
    wait_until: string;
    wait_for_images: boolean;
    page_timeout: number;
    delay_before_return_html: number;
    js_only: boolean;
    scan_full_page: boolean;
    process_iframes: boolean;
    scroll_delay: number;
    cache_mode: string;
    screenshot: boolean;
    pdf: boolean;
    capture_mhtml: boolean;
    exclude_external_images: boolean;
    exclude_all_images: boolean;
    image_score_threshold: number;
    image_description_min_word_threshold: number;
    table_score_threshold: number;
    capture_network_requests: boolean;
    capture_console_messages: boolean;
    log_console: boolean;
    extraction_strategy: string;
    chunking_strategy: string;
    markdown_generator: string;
    bypass_cache: boolean;
  };
  
  // LLM配置
  llm: {
    query: string;
    provider: string;
    model: string;
    api_key: string;
    base_url: string;
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
  
  // Schema Extraction配置
  schema_extraction: {
    enabled: boolean;
    schema_type: string;
    validate_schema: boolean;
    return_raw: boolean;
    instructions: string;
    extraction_schema: any;
  };
  
  // Content Processing配置
  content_processing: {
    word_count_threshold: number;
    excluded_tags: string[];
    excluded_selector: string;
    parser_type: string;
    css_selector: string;
    remove_forms: boolean;
    only_text: boolean;
    prettify: boolean;
    keep_data_attributes: boolean;
  };
  
  // Link Filtering配置
  link_filtering: {
    exclude_external_links: boolean;
    exclude_internal_links: boolean;
    exclude_social_media_links: boolean;
    exclude_domains: string[];
    social_media_domains: string[];
    exclude_external_images: boolean;
    exclude_all_images: boolean;
    image_score_threshold: number;
    image_description_min_word_threshold: number;
    table_score_threshold: number;
  };
  

  // Monitor配置
  monitor: {
    display_mode: string;
    show_progress: boolean;
    log_errors: boolean;
  };
}

/**
 * 统一的默认配置值
 */
export const DEFAULT_CRAWLER_CONFIG: DefaultCrawlerConfig = {
  browser: {
    headless: true,
    verbose: false,
    viewport_width: 1920,
    viewport_height: 1080,
    wait_for: 2.0,
    timeout: 30,
    ignore_https_errors: true,
    extra_args: [
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--disable-blink-features=AutomationControlled',
      '--exclude-switches=enable-automation'
    ]
  },
  
  crawler: {
    method: 'arun_many',
    verbose: true,
    check_robots_txt: false,
    fetch_ssl_certificate: false,
    simulate_user: true,
    magic: true,
    override_navigator: true,
    remove_overlay_elements: true,
    ignore_body_visibility: true,
    adjust_viewport_to_content: true,
    wait_until: 'domcontentloaded',
    wait_for_images: false,
    page_timeout: 60000,
    delay_before_return_html: 0.1,
    js_only: false,
    scan_full_page: true,
    process_iframes: true,
    scroll_delay: 0.2,
    cache_mode: 'BYPASS',
    screenshot: false,
    pdf: false,
    capture_mhtml: false,
    exclude_external_images: false,
    exclude_all_images: false,
    image_score_threshold: 50,
    image_description_min_word_threshold: 50,
    table_score_threshold: 7,
    capture_network_requests: false,
    capture_console_messages: false,
    log_console: false,
    extraction_strategy: 'LLMExtractionStrategy',
    chunking_strategy: 'IdentityChunking',
    markdown_generator: 'DefaultMarkdownGenerator',
    bypass_cache: true
  },
  
  llm: {
    query: '提取页面中所有文章的标题、作者和发布时间',
    provider: 'openai',
    model: 'deepseek-v3-0324',
    api_key: 'sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi',
    base_url: 'https://api.lkeap.cloud.tencent.com/v1',
    temperature: 0.0,
    max_tokens: 16384,
    top_p: 0.9
  },
  
  schema_extraction: {
    enabled: true,
    schema_type: 'auto',
    validate_schema: true,
    return_raw: false,
    instructions: `请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\":\\"Full\\"，商品有就是1，没有就是0)；
获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；
商品销售商名称、商品销售商链接；
商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；
商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；
商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；
商品评分数、商品评分数量、
商品的评论内容、评论评分、评论时间；
商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；
任务开始的时间戳YYYYMMDDHHMMSS`,
    extraction_schema: {
      "type": "object",
      "description": "电商商品完整信息提取结构",
      "properties": {
        "task_info": {
          "type": "object",
          "description": "任务执行信息",
          "properties": {
            "start_timestamp": {
              "type": "string",
              "pattern": "^[0-9]{14}$",
              "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
            }
          },
          "required": ["start_timestamp"]
        },
        "product_basic_info": {
          "type": "object",
          "description": "商品基础信息",
          "properties": {
            "product_url": {
              "type": "string",
              "format": "uri",
              "description": "商品链接"
            },
            "product_name": {
              "type": "string",
              "description": "商品名称"
            },
            "mlm_id": {
              "type": "number",
              "description": "商品MLM-ID中的数值部分"
            }
          },
          "required": ["product_url", "product_name", "mlm_id"]
        },
        "pricing_info": {
          "type": "object",
          "description": "价格和库存信息",
          "properties": {
            "sales_count": {
              "type": "number",
              "description": "商品销量（格式：+xxx vendidos中的数值）"
            },
            "current_price": {
              "type": "number",
              "description": "商品现价（数值）"
            },
            "original_price": {
              "type": "number",
              "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
            },
            "discount_rate": {
              "type": "number",
              "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
            },
            "stock_quantity": {
              "type": "number",
              "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
            }
          },
          "required": ["sales_count", "current_price", "original_price", "discount_rate", "stock_quantity"]
        }
      },
      "required": ["task_info", "product_basic_info", "pricing_info"]
    }
  },
  
  content_processing: {
    word_count_threshold: 200,
    excluded_tags: ['nav', 'footer', 'aside'],
    excluded_selector: '.ads, .sidebar',
    parser_type: 'lxml',
    css_selector: '.content, #main',
    remove_forms: false,
    only_text: false,
    prettify: false,
    keep_data_attributes: false
  },

  link_filtering: {
    exclude_external_links: false,
    exclude_internal_links: false,
    exclude_social_media_links: false,
    exclude_domains: ['example.com', 'ads.google.com'],
    social_media_domains: ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com', 'youtube.com', 'tiktok.com', 'pinterest.com', 'reddit.com'],
    exclude_external_images: false,
    exclude_all_images: false,
    image_score_threshold: 3,
    image_description_min_word_threshold: 50,
    table_score_threshold: 7
  },


  monitor: {
    display_mode: 'detailed',
    show_progress: true,
    log_errors: true
  }
};

/**
 * 获取新配置管理页面的默认表单值
 */
export const getNewConfigFormDefaults = () => {
  const config = DEFAULT_CRAWLER_CONFIG;
  
  return {
    // Browser配置
    'browser.headless': config.browser.headless,
    'browser.verbose': config.browser.verbose,
    'browser.viewport_width': config.browser.viewport_width,
    'browser.viewport_height': config.browser.viewport_height,
    'browser.wait_for': config.browser.wait_for,
    'browser.timeout': config.browser.timeout,
    'browser.ignore_https_errors': config.browser.ignore_https_errors,
    'browser.extra_args': config.browser.extra_args.join('\n'),
    
    // Crawler配置
    'crawler.method': config.crawler.method,
    'crawler.verbose': config.crawler.verbose,
    'crawler.simulate_user': config.crawler.simulate_user,
    'crawler.magic': config.crawler.magic,
    'crawler.override_navigator': config.crawler.override_navigator,
    'crawler.remove_overlay_elements': config.crawler.remove_overlay_elements,
    'crawler.scan_full_page': config.crawler.scan_full_page,
    'crawler.process_iframes': config.crawler.process_iframes,
    'crawler.ignore_body_visibility': config.crawler.ignore_body_visibility,
    'crawler.page_timeout': config.crawler.page_timeout,
    'crawler.delay_before_return_html': config.crawler.delay_before_return_html,
    'crawler.scroll_delay': config.crawler.scroll_delay,
    'crawler.wait_until': config.crawler.wait_until,
    'crawler.image_score_threshold': config.crawler.image_score_threshold,
    'crawler.table_score_threshold': config.crawler.table_score_threshold,
    'crawler.cache_mode': config.crawler.cache_mode,
    'crawler.check_robots_txt': config.crawler.check_robots_txt,
    'crawler.fetch_ssl_certificate': config.crawler.fetch_ssl_certificate,
    'crawler.bypass_cache': config.crawler.bypass_cache,
    'crawler.extraction_strategy': config.crawler.extraction_strategy,
    'crawler.chunking_strategy': config.crawler.chunking_strategy,
    'crawler.markdown_generator': config.crawler.markdown_generator,
    
    // LLM配置
    'llm.query': config.llm.query,
    'llm.provider': config.llm.provider,
    'llm.model': config.llm.model,
    'llm.api_key': config.llm.api_key,
    'llm.base_url': config.llm.base_url,
    'llm.temperature': config.llm.temperature,
    'llm.max_tokens': config.llm.max_tokens,
    'llm.top_p': config.llm.top_p,
    
    // Schema Extraction配置
    'schema_extraction.enabled': config.schema_extraction.enabled,
    'schema_extraction.schema_type': config.schema_extraction.schema_type,
    'schema_extraction.validate_schema': config.schema_extraction.validate_schema,
    'schema_extraction.return_raw': config.schema_extraction.return_raw,
    'schema_extraction.instructions': config.schema_extraction.instructions,
    'schema_extraction.extraction_schema': JSON.stringify(config.schema_extraction.extraction_schema, null, 2),
    
    // Content Processing配置
    'content_processing.word_count_threshold': config.content_processing.word_count_threshold,
    'content_processing.excluded_tags': config.content_processing.excluded_tags,
    'content_processing.excluded_selector': config.content_processing.excluded_selector,
    'content_processing.parser_type': config.content_processing.parser_type,
    'content_processing.css_selector': config.content_processing.css_selector,
    'content_processing.remove_forms': config.content_processing.remove_forms,
    'content_processing.only_text': config.content_processing.only_text,
    'content_processing.prettify': config.content_processing.prettify,
    'content_processing.keep_data_attributes': config.content_processing.keep_data_attributes,

    // Link Filtering配置
    'link_filtering.exclude_external_links': config.link_filtering.exclude_external_links,
    'link_filtering.exclude_internal_links': config.link_filtering.exclude_internal_links,
    'link_filtering.exclude_social_media_links': config.link_filtering.exclude_social_media_links,
    'link_filtering.exclude_domains': config.link_filtering.exclude_domains,
    'link_filtering.social_media_domains': config.link_filtering.social_media_domains,
    'link_filtering.exclude_external_images': config.link_filtering.exclude_external_images,
    'link_filtering.exclude_all_images': config.link_filtering.exclude_all_images,
    'link_filtering.image_score_threshold': config.link_filtering.image_score_threshold,
    'link_filtering.image_description_min_word_threshold': config.link_filtering.image_description_min_word_threshold,
    'link_filtering.table_score_threshold': config.link_filtering.table_score_threshold,


    // Monitor配置
    'monitor.display_mode': config.monitor.display_mode,
    'monitor.show_progress': config.monitor.show_progress,
    'monitor.log_errors': config.monitor.log_errors,
  };
};
