"""
爬虫引擎核心接口定义

定义了爬虫系统各个组件的核心接口，确保模块间的解耦和可扩展性。
基于API客户端模式重新设计。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    PAUSED = "paused"


class ICrawlerEngine(ABC):
    """爬虫引擎核心接口 - API客户端模式"""

    @abstractmethod
    async def start(self) -> bool:
        """启动爬虫引擎

        Returns:
            bool: 启动是否成功
        """
        pass

    @abstractmethod
    async def stop(self) -> bool:
        """停止爬虫引擎

        Returns:
            bool: 停止是否成功
        """
        pass

    @abstractmethod
    async def crawl_url(
        self,
        url: str,
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """爬取单个URL

        Args:
            url: 目标URL
            platform: 平台类型 (mercadolibre, amazon等)
            options: 爬取选项

        Returns:
            Dict[str, Any]: 爬取结果
        """
        pass

    @abstractmethod
    async def crawl_batch(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """批量爬取URL

        Args:
            urls: URL列表
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 批量任务信息
        """
        pass

    @abstractmethod
    async def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态

        Returns:
            Dict[str, Any]: 引擎状态信息
        """
        pass

    @abstractmethod
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        pass

    @abstractmethod
    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务结果
        """
        pass


class ITaskScheduler(ABC):
    """任务调度器接口 - 简化版"""

    @abstractmethod
    async def start(self) -> bool:
        """启动调度器"""
        pass

    @abstractmethod
    async def stop(self) -> bool:
        """停止调度器"""
        pass

    @abstractmethod
    async def add_task(
        self,
        task_id: str,
        url: str,
        platform: str = "mercadolibre",
        priority: int = 0,
        options: Optional[Dict[str, Any]] = None
    ) -> bool:
        """添加任务"""
        pass

    @abstractmethod
    async def get_task_status(self, task_id: str) -> TaskStatus:
        """获取任务状态"""
        pass