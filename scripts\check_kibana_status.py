#!/usr/bin/env python3
"""
检查Kibana状态的脚本
"""

import requests
import time
import json

def check_kibana_status():
    """检查Kibana状态"""
    
    kibana_url = "http://localhost:5601"
    elasticsearch_url = "http://localhost:9200"
    
    print("🔍 检查ELK Stack状态...")
    
    # 检查Elasticsearch
    print("1. 检查Elasticsearch...")
    try:
        response = requests.get(f"{elasticsearch_url}/_cluster/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            status = health.get('status', 'unknown')
            nodes = health.get('number_of_nodes', 0)
            print(f"   ✅ Elasticsearch: {status} ({nodes} 节点)")
        else:
            print(f"   ❌ Elasticsearch响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Elasticsearch连接失败: {e}")
        return False
    
    # 检查Kibana
    print("2. 检查Kibana...")
    max_retries = 6  # 最多等待3分钟
    for i in range(max_retries):
        try:
            response = requests.get(f"{kibana_url}/api/status", timeout=10)
            if response.status_code == 200:
                status_data = response.json()
                overall_status = status_data.get('status', {}).get('overall', {}).get('state', 'unknown')
                print(f"   ✅ Kibana: {overall_status}")
                
                if overall_status == 'green':
                    print("   🎉 Kibana完全就绪!")
                    break
                else:
                    print(f"   ⏳ Kibana状态: {overall_status} (等待中...)")
            else:
                print(f"   ⏳ Kibana响应: {response.status_code} (启动中...)")
        except Exception as e:
            print(f"   ⏳ Kibana连接: {e} (启动中...)")
        
        if i < max_retries - 1:
            print(f"   等待30秒后重试... ({i+1}/{max_retries})")
            time.sleep(30)
    
    # 检查索引
    print("3. 检查Elasticsearch索引...")
    try:
        response = requests.get(f"{elasticsearch_url}/_cat/indices?v", timeout=5)
        if response.status_code == 200:
            indices = response.text.strip()
            if indices:
                print("   📊 现有索引:")
                for line in indices.split('\n')[1:]:  # 跳过标题行
                    if line.strip():
                        print(f"     {line}")
            else:
                print("   📋 暂无索引 (正常，等待日志数据)")
        else:
            print(f"   ⚠️  索引检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ⚠️  索引检查错误: {e}")
    
    print("\n" + "="*50)
    print("📋 ELK Stack状态总结")
    print("="*50)
    
    print(f"🔗 访问地址:")
    print(f"  - Kibana:        {kibana_url}")
    print(f"  - Elasticsearch: {elasticsearch_url}")
    
    print(f"\n📖 下一步操作:")
    print(f"  1. 访问 {kibana_url}")
    print(f"  2. 等待Kibana完全加载")
    print(f"  3. 当前没有日志数据是正常的")
    print(f"  4. Task 04完成后会有真实日志")
    
    print(f"\n💡 提示:")
    print(f"  - Kibana首次启动需要1-2分钟")
    print(f"  - 如果页面显示加载中，请耐心等待")
    print(f"  - 无需登录，直接使用")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("MonIt ELK Stack状态检查工具")
    print("=" * 50)
    
    try:
        check_kibana_status()
    except KeyboardInterrupt:
        print("\n\n⏹️  检查被用户中断")
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")

if __name__ == "__main__":
    main()
