# Redis统一存储结构设计

> **设计目标**：建立监控任务、URL、爬虫Worker、Celery任务之间的完整关联关系，支持高效的双向查询

## 🎯 核心需求分析

### 关联关系矩阵
```
监控任务 ←→ URL ←→ 爬虫Worker ←→ Celery任务
    ↓        ↓         ↓           ↓
  配置信息   状态信息   执行信息    结果信息
```

### 查询场景
1. **运行时查询**：当前哪个URL被哪个Celery Worker发送给了哪个爬虫Worker
2. **调试查询**：通过监控任务UUID查找所有相关的执行信息
3. **状态查询**：通过URL查找所有相关的任务和执行状态
4. **性能查询**：通过Worker查找当前负载和历史执行记录

## 🏗️ 统一存储架构设计

### 1. 核心实体存储

#### 1.1 监控任务存储（保持现有结构）
```redis
# 基本信息
monitoring_tasks:tasks:{task_uuid}:basic = Hash{
  "id": "task_uuid",
  "name": "任务名称", 
  "description": "任务描述",
  "created_at": "2025-08-07T10:00:00Z",
  "updated_at": "2025-08-07T10:00:00Z",
  "status": "active"
}

# 配置信息
monitoring_tasks:tasks:{task_uuid}:config = Hash{
  "platform": "mercadolibre",
  "priority": "normal",
  "retry_count": "3",
  "timeout": "300",
  "batch_size": "10"
}

# 调度信息
monitoring_tasks:tasks:{task_uuid}:schedule = Hash{
  "type": "daily",
  "enabled": "true", 
  "time": "01:13",
  "timezone": "Asia/Shanghai"
}

# URL关联
monitoring_tasks:tasks:{task_uuid}:urls = Set[url_id1, url_id2, ...]
```

#### 1.2 URL存储（新增）
```redis
# URL基本信息
urls:{url_id}:info = Hash{
  "id": "url_id",
  "url": "https://example.com/product/123",
  "url_hash": "md5_hash_16_chars",
  "platform": "mercadolibre",
  "status": "active",
  "created_at": "2025-08-07T10:00:00Z",
  "last_crawled": "2025-08-07T12:00:00Z"
}

# URL关联的监控任务
urls:{url_id}:tasks = Set[task_uuid1, task_uuid2, ...]
```

#### 1.3 爬虫Worker存储（保持现有结构）
```redis
# Worker基本信息
crawler_worker:{worker_id} = JSON{
  "worker_id": "worker_id",
  "worker_name": "11235w",
  "backend_config": {...},
  "crawler_config": {...},
  "status": "online"
}
```

### 2. 执行关联存储（核心新增）

#### 2.1 执行会话存储
```redis
# 执行会话 - 一次监控任务执行的完整记录
execution_sessions:{session_id} = Hash{
  "session_id": "exec_20250807_120000_001",
  "monitoring_task_id": "task_uuid",
  "celery_task_id": "celery_task_uuid", 
  "execution_type": "manual|scheduled",
  "started_at": "2025-08-07T12:00:00Z",
  "status": "running|completed|failed",
  "total_urls": "100",
  "completed_urls": "45",
  "failed_urls": "2"
}

# 会话关联的URL执行记录
execution_sessions:{session_id}:urls = Set[url_execution_id1, url_execution_id2, ...]
```

#### 2.2 URL执行记录存储
```redis
# URL执行记录 - 单个URL的执行详情
url_executions:{url_execution_id} = Hash{
  "execution_id": "url_exec_20250807_120001_001",
  "session_id": "exec_20250807_120000_001",
  "monitoring_task_id": "task_uuid",
  "celery_task_id": "celery_task_uuid",
  "url_id": "url_id",
  "url": "https://example.com/product/123",
  "assigned_worker_id": "worker_id",
  "crawler_endpoint": "http://localhost:11235/crawl",
  "started_at": "2025-08-07T12:00:01Z",
  "completed_at": "2025-08-07T12:00:05Z",
  "status": "pending|running|completed|failed",
  "response_time": "4.2",
  "error_message": "",
  "retry_count": "0"
}
```

### 3. 索引和反向关联

#### 3.1 时间索引
```redis
# 按日期索引执行会话
execution_sessions:by_date:2025-08-07 = Set[session_id1, session_id2, ...]

# 按小时索引URL执行
url_executions:by_hour:2025-08-07-12 = Set[url_execution_id1, ...]
```

#### 3.2 状态索引
```redis
# 按状态索引执行会话
execution_sessions:by_status:running = Set[session_id1, ...]
execution_sessions:by_status:completed = Set[session_id2, ...]

# 按状态索引URL执行
url_executions:by_status:pending = Set[url_execution_id1, ...]
url_executions:by_status:running = Set[url_execution_id2, ...]
```

#### 3.3 关联索引
```redis
# 监控任务 → 执行会话
monitoring_task_sessions:{task_uuid} = Set[session_id1, session_id2, ...]

# Celery任务 → 执行会话  
celery_task_sessions:{celery_task_id} = Set[session_id1, ...]

# Worker → URL执行记录
worker_url_executions:{worker_id} = Set[url_execution_id1, ...]

# URL → 执行记录
url_execution_history:{url_id} = Set[url_execution_id1, ...]
```

### 4. 实时状态存储

#### 4.1 当前执行状态
```redis
# 当前正在执行的URL（用于实时查询）
current_executions:by_worker:{worker_id} = Hash{
  "url_execution_id1": "url1",
  "url_execution_id2": "url2"
}

# 当前正在执行的会话
current_sessions:active = Set[session_id1, session_id2, ...]

# Worker当前负载
worker_load:{worker_id} = Hash{
  "current_tasks": "3",
  "max_tasks": "10", 
  "last_updated": "2025-08-07T12:00:00Z"
}
```

## 🔍 查询优化设计

### 核心查询场景实现

#### 1. 运行时查询：当前URL执行状态
```python
async def get_current_url_execution_status(url: str):
    """查询URL当前执行状态"""
    # 1. 通过URL获取url_id
    url_hash = get_url_hash(url)
    url_id = await redis.get(f"url_hash_to_id:{url_hash}")
    
    # 2. 获取URL当前执行记录
    current_executions = await redis.smembers(f"url_execution_history:{url_id}")
    
    # 3. 过滤正在执行的记录
    for execution_id in current_executions:
        execution_data = await redis.hgetall(f"url_executions:{execution_id}")
        if execution_data.get("status") in ["pending", "running"]:
            return {
                "url": url,
                "execution_id": execution_id,
                "monitoring_task_id": execution_data.get("monitoring_task_id"),
                "celery_task_id": execution_data.get("celery_task_id"),
                "assigned_worker_id": execution_data.get("assigned_worker_id"),
                "status": execution_data.get("status"),
                "started_at": execution_data.get("started_at")
            }
    return None
```

#### 2. 调试查询：监控任务完整执行链路
```python
async def get_monitoring_task_execution_chain(task_uuid: str):
    """获取监控任务完整执行链路"""
    # 1. 获取任务基本信息
    task_basic = await redis.hgetall(f"monitoring_tasks:tasks:{task_uuid}:basic")
    
    # 2. 获取所有执行会话
    session_ids = await redis.smembers(f"monitoring_task_sessions:{task_uuid}")
    
    execution_chain = []
    for session_id in session_ids:
        # 3. 获取会话信息
        session_data = await redis.hgetall(f"execution_sessions:{session_id}")
        
        # 4. 获取会话中的URL执行记录
        url_execution_ids = await redis.smembers(f"execution_sessions:{session_id}:urls")
        
        url_executions = []
        for url_execution_id in url_execution_ids:
            url_execution = await redis.hgetall(f"url_executions:{url_execution_id}")
            url_executions.append(url_execution)
        
        execution_chain.append({
            "session": session_data,
            "url_executions": url_executions
        })
    
    return {
        "task": task_basic,
        "execution_chain": execution_chain
    }
```

## 📊 性能优化策略

### 1. 数据过期策略
```redis
# 执行记录保留30天
EXPIRE url_executions:{url_execution_id} 2592000

# 会话记录保留90天  
EXPIRE execution_sessions:{session_id} 7776000

# 实时状态保留24小时
EXPIRE current_executions:by_worker:{worker_id} 86400
```

### 2. 批量操作优化
- 使用Redis Pipeline进行批量写入
- 使用MGET进行批量读取
- 使用Lua脚本保证原子性操作

### 3. 内存优化
- 使用Hash结构存储结构化数据
- 使用Set结构存储关联关系
- 定期清理过期数据

## 🔄 数据一致性保证

### 1. 事务操作
```python
async def create_url_execution_record(execution_data):
    """原子性创建URL执行记录"""
    pipe = redis.pipeline()
    
    # 1. 创建执行记录
    pipe.hset(f"url_executions:{execution_id}", mapping=execution_data)
    
    # 2. 更新索引
    pipe.sadd(f"execution_sessions:{session_id}:urls", execution_id)
    pipe.sadd(f"url_execution_history:{url_id}", execution_id)
    pipe.sadd(f"worker_url_executions:{worker_id}", execution_id)
    
    # 3. 更新状态索引
    pipe.sadd(f"url_executions:by_status:{status}", execution_id)
    
    await pipe.execute()
```

### 2. 数据清理策略
- 定期清理过期的执行记录
- 自动更新状态索引
- 维护数据一致性检查

这个设计提供了完整的关联关系存储和高效的查询能力，支持所有运行时调试和监控需求。
