#!/usr/bin/env python3
"""
测试多Worker显示修复功能的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_multi_worker_assignment_display():
    """测试多Worker分配显示"""
    
    print("🧪 测试多Worker分配显示功能...")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    # 1. 获取可用Worker
    print("\n📋 获取可用Worker...")
    try:
        workers_response = requests.get(f"{BASE_URL}/crawler-workers/")
        if workers_response.status_code == 200:
            workers = workers_response.json()
            print(f"✅ 找到 {len(workers)} 个Worker")
            
            if len(workers) >= 2:
                # 选择前两个Worker
                worker_ids = [workers[0]['worker_id'], workers[1]['worker_id']]
                worker_names = [workers[0]['worker_name'], workers[1]['worker_name']]
                
                print(f"选择的Worker:")
                for i, (wid, wname) in enumerate(zip(worker_ids, worker_names), 1):
                    print(f"  {i}. {wname} ({wid})")
                
                # 2. 创建多Worker分配
                print(f"\n🔧 创建多Worker分配...")
                assignment_data = {
                    "task_id": task_id,
                    "worker_ids": worker_ids,
                    "assignment_name": "多Worker显示测试",
                    "description": "测试多Worker分配显示功能",
                    "assignment_strategy": "health_based",
                    "auto_failover": True,
                    "total_urls": 49
                }
                
                response = requests.post(
                    f"{BASE_URL}/task-assignments/",
                    json=assignment_data,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    assignment = response.json()
                    print("✅ 多Worker分配创建成功!")
                    print(f"  分配ID: {assignment.get('assignment_id')}")
                    print(f"  Worker IDs: {assignment.get('worker_ids')}")
                    print(f"  Worker数量: {len(assignment.get('worker_ids', []))}")
                    
                    # 3. 获取任务分配摘要，检查显示数据
                    print(f"\n📊 检查分配摘要数据...")
                    summary_response = requests.get(f"{BASE_URL}/task-assignments/task/{task_id}/assignments")
                    
                    if summary_response.status_code == 200:
                        summaries = summary_response.json()
                        print(f"✅ 获取到 {len(summaries)} 个分配摘要")
                        
                        # 找到刚创建的分配
                        created_assignment = None
                        for summary in summaries:
                            if summary.get('assignment_id') == assignment.get('assignment_id'):
                                created_assignment = summary
                                break
                        
                        if created_assignment:
                            print(f"\n🔍 分配摘要详情:")
                            print(f"  分配ID: {created_assignment.get('assignment_id')}")
                            print(f"  分配名称: {created_assignment.get('assignment_name')}")
                            print(f"  Worker数量: {created_assignment.get('worker_count')}")
                            print(f"  Worker IDs: {created_assignment.get('worker_ids')}")
                            print(f"  Worker名称: {created_assignment.get('worker_names')}")
                            print(f"  主要Worker: {created_assignment.get('primary_worker_name')}")
                            print(f"  当前Worker: {created_assignment.get('current_worker_name')}")
                            
                            # 验证数据完整性
                            worker_ids_in_summary = created_assignment.get('worker_ids', [])
                            worker_names_in_summary = created_assignment.get('worker_names', [])
                            worker_count = created_assignment.get('worker_count', 0)
                            
                            print(f"\n✅ 数据完整性检查:")
                            print(f"  Worker IDs数量: {len(worker_ids_in_summary)} (期望: 2)")
                            print(f"  Worker名称数量: {len(worker_names_in_summary)} (期望: 2)")
                            print(f"  Worker数量字段: {worker_count} (期望: 2)")
                            
                            # 检查是否包含所有指定的Worker
                            all_workers_included = all(wid in worker_ids_in_summary for wid in worker_ids)
                            print(f"  所有Worker都包含: {'✅ 是' if all_workers_included else '❌ 否'}")
                            
                            if all_workers_included and len(worker_ids_in_summary) == 2 and worker_count == 2:
                                print(f"\n🎉 多Worker分配显示测试通过!")
                                return True
                            else:
                                print(f"\n❌ 多Worker分配显示测试失败!")
                                return False
                        else:
                            print(f"❌ 在摘要列表中找不到刚创建的分配")
                            return False
                    else:
                        print(f"❌ 获取分配摘要失败: {summary_response.text}")
                        return False
                elif response.status_code == 400:
                    error_detail = response.json().get('detail', '')
                    if '已经指派给此任务' in error_detail:
                        print(f"ℹ️ Worker已经指派，这是正常的重复检查: {error_detail}")
                        return True
                    else:
                        print(f"❌ 分配被拒绝: {error_detail}")
                        return False
                else:
                    print(f"❌ 创建分配失败: {response.text}")
                    return False
            else:
                print("❌ 可用Worker数量不足 (需要至少2个)")
                return False
        else:
            print(f"❌ 获取Worker列表失败: {workers_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def get_current_assignments_display():
    """获取当前分配的显示信息"""
    
    print(f"\n📋 获取当前所有分配的显示信息...")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    try:
        response = requests.get(f"{BASE_URL}/task-assignments/task/{task_id}/assignments")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ 当前有 {len(assignments)} 个分配")
            
            for i, assignment in enumerate(assignments, 1):
                print(f"\n分配 {i}:")
                print(f"  ID: {assignment.get('assignment_id')}")
                print(f"  名称: {assignment.get('assignment_name')}")
                print(f"  状态: {assignment.get('status')}")
                print(f"  Worker数量: {assignment.get('worker_count')}")
                print(f"  Worker IDs: {assignment.get('worker_ids')}")
                print(f"  Worker名称: {assignment.get('worker_names')}")
                
                # 检查数据一致性
                worker_ids = assignment.get('worker_ids', [])
                worker_names = assignment.get('worker_names', [])
                worker_count = assignment.get('worker_count', 0)
                
                if len(worker_ids) == len(worker_names) == worker_count:
                    print(f"  ✅ 数据一致性: 正常")
                else:
                    print(f"  ❌ 数据一致性: 异常 (IDs:{len(worker_ids)}, Names:{len(worker_names)}, Count:{worker_count})")
            
            return assignments
        else:
            print(f"❌ 获取失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []

def main():
    """主函数"""
    print("🚀 开始测试多Worker显示修复功能\n")
    
    # 1. 获取当前分配状态
    print("=" * 60)
    current_assignments = get_current_assignments_display()
    
    # 2. 测试多Worker分配显示
    print("=" * 60)
    test_result = test_multi_worker_assignment_display()
    
    # 3. 再次获取分配状态，查看变化
    print("=" * 60)
    print(f"\n📊 测试后的分配状态:")
    final_assignments = get_current_assignments_display()
    
    # 4. 总结测试结果
    print("=" * 60)
    print(f"\n🎯 测试结果总结:")
    print(f"  初始分配数量: {len(current_assignments)}")
    print(f"  最终分配数量: {len(final_assignments)}")
    print(f"  多Worker显示测试: {'✅ 通过' if test_result else '❌ 失败'}")
    
    # 检查是否有多Worker分配
    multi_worker_assignments = [a for a in final_assignments if a.get('worker_count', 0) > 1]
    print(f"  多Worker分配数量: {len(multi_worker_assignments)}")
    
    if multi_worker_assignments:
        print(f"  多Worker分配详情:")
        for assignment in multi_worker_assignments:
            worker_names = assignment.get('worker_names', [])
            print(f"    - {assignment.get('assignment_name')}: {', '.join(worker_names)}")

if __name__ == "__main__":
    main()
