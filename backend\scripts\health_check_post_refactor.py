#!/usr/bin/env python3
"""
重构后系统健康检查脚本

验证内存任务存储重构后的系统状态，确保所有功能正常工作。
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import redis.asyncio as redis
from app.services.task_storage_service import TaskStorageService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SystemHealthChecker:
    """系统健康检查器"""

    def __init__(self, redis_url: str = None):
        # 自动检测Redis连接地址
        if redis_url is None:
            redis_url = self._detect_redis_url()

        self.redis_url = redis_url
        self.task_storage = TaskStorageService(redis_url)
        self.health_results = {
            "redis_connection": False,
            "task_storage_service": False,
            "data_integrity": False,
            "api_compatibility": False,
            "performance": {},
            "errors": [],
            "warnings": []
        }

    def _detect_redis_url(self) -> str:
        """自动检测Redis连接地址"""
        # 尝试的Redis地址列表（按优先级）
        redis_urls = [
            "redis://localhost:6379/0",  # 本地环境
            "redis://127.0.0.1:6379/0",  # 本地环境备选
            "redis://redis:6379/0"       # Docker环境
        ]

        for url in redis_urls:
            try:
                # 简单的连接测试
                import redis.asyncio as redis_sync
                client = redis_sync.from_url(url)
                # 这里不能用async，所以先返回第一个本地地址
                logger.info(f"尝试Redis连接: {url}")
                return url
            except:
                continue

        # 默认返回本地地址
        return "redis://localhost:6379/0"
    
    async def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        try:
            redis_client = redis.from_url(self.redis_url)
            
            # 测试基本连接
            await redis_client.ping()
            
            # 测试基本操作
            test_key = "health_check_test"
            await redis_client.set(test_key, "test_value")
            value = await redis_client.get(test_key)
            await redis_client.delete(test_key)
            
            if value and value.decode() == "test_value":
                logger.info("✅ Redis连接正常")
                await redis_client.close()
                return True
            else:
                logger.error("❌ Redis基本操作失败")
                await redis_client.close()
                return False
                
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            self.health_results["errors"].append(f"Redis连接失败: {e}")
            return False
    
    async def check_task_storage_service(self) -> bool:
        """检查TaskStorageService功能"""
        try:
            # 测试基本CRUD操作
            test_task_id = f"health_check_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            test_task_data = {
                "id": test_task_id,
                "name": "健康检查测试任务",
                "description": "用于验证TaskStorageService功能的测试任务",
                "status": "draft",
                "schedule": {"type": "manual"},
                "config": {"platform": "test"},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # 测试保存任务
            save_success = await self.task_storage.save_task(test_task_id, test_task_data)
            if not save_success:
                logger.error("❌ 任务保存失败")
                return False
            
            # 测试获取任务
            retrieved_task = await self.task_storage.get_task(test_task_id)
            if not retrieved_task:
                logger.error("❌ 任务获取失败")
                return False
            
            # 测试更新任务
            update_success = await self.task_storage.update_task(
                test_task_id, 
                {"description": "更新后的描述", "status": "active"}
            )
            if not update_success:
                logger.error("❌ 任务更新失败")
                return False
            
            # 测试任务存在检查
            exists = await self.task_storage.task_exists(test_task_id)
            if not exists:
                logger.error("❌ 任务存在检查失败")
                return False
            
            # 测试任务列表
            task_list = await self.task_storage.list_tasks()
            if not isinstance(task_list, dict) or "tasks" not in task_list:
                logger.error("❌ 任务列表获取失败")
                return False
            
            # 清理测试数据
            delete_success = await self.task_storage.delete_task(test_task_id)
            if not delete_success:
                logger.warning("⚠️ 测试任务清理失败")
                self.health_results["warnings"].append("测试任务清理失败")
            
            logger.info("✅ TaskStorageService功能正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ TaskStorageService检查失败: {e}")
            self.health_results["errors"].append(f"TaskStorageService检查失败: {e}")
            return False
    
    async def check_data_integrity(self) -> bool:
        """检查数据完整性"""
        try:
            redis_client = redis.from_url(self.redis_url)
            
            # 获取所有任务ID
            task_ids = await redis_client.smembers('monitoring_tasks:all_ids')
            
            integrity_stats = {
                "total_tasks": len(task_ids),
                "valid_tasks": 0,
                "invalid_tasks": 0,
                "tasks_with_urls": 0,
                "orphaned_urls": 0
            }
            
            for task_id in task_ids:
                task_id_str = task_id.decode() if isinstance(task_id, bytes) else task_id
                
                # 检查任务数据
                task_data = await self.task_storage.get_task(task_id_str)
                if task_data:
                    integrity_stats["valid_tasks"] += 1
                    
                    # 检查URL关联
                    url_ids = await self.task_storage.get_task_urls(task_id_str)
                    if url_ids:
                        integrity_stats["tasks_with_urls"] += 1
                else:
                    integrity_stats["invalid_tasks"] += 1
            
            await redis_client.close()
            
            # 评估数据完整性
            if integrity_stats["invalid_tasks"] == 0:
                logger.info(f"✅ 数据完整性检查通过: {integrity_stats}")
                return True
            else:
                logger.warning(f"⚠️ 发现数据完整性问题: {integrity_stats}")
                self.health_results["warnings"].append(f"数据完整性问题: {integrity_stats}")
                return integrity_stats["invalid_tasks"] < integrity_stats["total_tasks"] * 0.1  # 允许10%的错误率
                
        except Exception as e:
            logger.error(f"❌ 数据完整性检查失败: {e}")
            self.health_results["errors"].append(f"数据完整性检查失败: {e}")
            return False
    
    async def check_api_compatibility(self) -> bool:
        """检查API兼容性（模拟API调用）"""
        try:
            # 模拟API调用的基本功能
            
            # 1. 测试任务列表获取
            task_list = await self.task_storage.list_tasks(page=1, page_size=5)
            if not isinstance(task_list, dict):
                logger.error("❌ 任务列表API兼容性失败")
                return False
            
            # 2. 测试过滤功能
            filtered_list = await self.task_storage.list_tasks(
                filters={"status": "active"}, 
                page=1, 
                page_size=10
            )
            if not isinstance(filtered_list, dict):
                logger.error("❌ 任务过滤API兼容性失败")
                return False
            
            # 3. 测试状态统计
            status_counts = await self.task_storage.get_task_count_by_status()
            if not isinstance(status_counts, dict):
                logger.error("❌ 状态统计API兼容性失败")
                return False
            
            logger.info("✅ API兼容性检查通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ API兼容性检查失败: {e}")
            self.health_results["errors"].append(f"API兼容性检查失败: {e}")
            return False
    
    async def check_performance(self) -> Dict[str, float]:
        """检查性能指标"""
        try:
            performance_results = {}
            
            # 测试任务获取性能
            start_time = datetime.now()
            task_list = await self.task_storage.list_tasks(page=1, page_size=20)
            end_time = datetime.now()
            
            list_time = (end_time - start_time).total_seconds()
            performance_results["task_list_time"] = list_time
            
            # 测试单个任务获取性能
            if task_list.get("tasks"):
                task_id = task_list["tasks"][0]["id"]
                
                start_time = datetime.now()
                task_data = await self.task_storage.get_task(task_id)
                end_time = datetime.now()
                
                get_time = (end_time - start_time).total_seconds()
                performance_results["task_get_time"] = get_time
            
            # 评估性能
            if list_time < 2.0:  # 2秒内完成列表获取
                logger.info(f"✅ 性能检查通过: {performance_results}")
            else:
                logger.warning(f"⚠️ 性能可能需要优化: {performance_results}")
                self.health_results["warnings"].append(f"性能警告: {performance_results}")
            
            return performance_results
            
        except Exception as e:
            logger.error(f"❌ 性能检查失败: {e}")
            self.health_results["errors"].append(f"性能检查失败: {e}")
            return {}
    
    async def generate_health_report(self) -> str:
        """生成健康检查报告"""
        report_lines = [
            "=" * 60,
            "系统健康检查报告（重构后）",
            "=" * 60,
            f"检查时间: {datetime.now().isoformat()}",
            "",
            "检查结果:",
        ]
        
        # 添加各项检查结果
        checks = [
            ("Redis连接", self.health_results["redis_connection"]),
            ("TaskStorageService", self.health_results["task_storage_service"]),
            ("数据完整性", self.health_results["data_integrity"]),
            ("API兼容性", self.health_results["api_compatibility"])
        ]
        
        for check_name, result in checks:
            status = "✅ 通过" if result else "❌ 失败"
            report_lines.append(f"  - {check_name}: {status}")
        
        report_lines.append("")
        
        # 添加性能指标
        if self.health_results["performance"]:
            report_lines.extend([
                "性能指标:",
                *[f"  - {key}: {value:.3f}秒" for key, value in self.health_results["performance"].items()],
                ""
            ])
        
        # 添加警告
        if self.health_results["warnings"]:
            report_lines.extend([
                "警告:",
                *[f"  - {warning}" for warning in self.health_results["warnings"]],
                ""
            ])
        
        # 添加错误
        if self.health_results["errors"]:
            report_lines.extend([
                "错误:",
                *[f"  - {error}" for error in self.health_results["errors"]],
                ""
            ])
        
        # 总体评估
        all_passed = all([
            self.health_results["redis_connection"],
            self.health_results["task_storage_service"],
            self.health_results["data_integrity"],
            self.health_results["api_compatibility"]
        ])
        
        if all_passed:
            report_lines.extend([
                "=" * 60,
                "🎉 系统健康状态：优秀",
                "重构成功，所有功能正常运行！",
                "=" * 60
            ])
        else:
            report_lines.extend([
                "=" * 60,
                "⚠️ 系统健康状态：需要关注",
                "发现一些问题，请查看上述错误和警告。",
                "=" * 60
            ])
        
        return "\n".join(report_lines)
    
    async def run_health_check(self) -> bool:
        """运行完整的健康检查"""
        try:
            logger.info("开始系统健康检查...")
            
            # 执行各项检查
            self.health_results["redis_connection"] = await self.check_redis_connection()
            self.health_results["task_storage_service"] = await self.check_task_storage_service()
            self.health_results["data_integrity"] = await self.check_data_integrity()
            self.health_results["api_compatibility"] = await self.check_api_compatibility()
            self.health_results["performance"] = await self.check_performance()
            
            # 生成报告
            report = await self.generate_health_report()
            
            # 保存报告
            report_file = f"health_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"健康检查报告已保存到: {report_file}")
            print(report)
            
            # 返回总体健康状态
            return all([
                self.health_results["redis_connection"],
                self.health_results["task_storage_service"],
                self.health_results["data_integrity"],
                self.health_results["api_compatibility"]
            ])
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
        finally:
            await self.task_storage.close()


async def main():
    """主函数"""
    print("系统健康检查工具（重构后）")
    print("=" * 40)
    
    checker = SystemHealthChecker()
    
    try:
        success = await checker.run_health_check()
        
        if success:
            print("\n🎉 系统健康检查通过！")
            return 0
        else:
            print("\n⚠️ 系统健康检查发现问题！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 健康检查被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 健康检查过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
