"""
重新设计的爬虫任务执行服务
基于外部爬虫API的任务执行和管理
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from app.services.crawler_api_service import (
    CrawlerAPIService,
    BatchTaskRequest,
    LlmJobRequest,
    TaskPriority,
    TaskStatus,
    get_crawler_api_service
)
from app.services.task_storage_service import TaskStorageService
from app.api.v1.crawler_config import CrawlerConfigService, CrawlerTaskConfig

logger = logging.getLogger(__name__)


class ExecutionMode(str, Enum):
    """执行模式"""
    BATCH = "batch"  # 批量执行
    SINGLE = "single"  # 单个执行
    STREAMING = "streaming"  # 流式执行


@dataclass
class TaskExecutionRequest:
    """任务执行请求"""
    monitoring_task_id: str
    urls: List[str]
    config_id: str
    execution_mode: ExecutionMode = ExecutionMode.BATCH
    priority: TaskPriority = TaskPriority.MEDIUM
    callback_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class TaskExecutionResult:
    """任务执行结果"""
    execution_id: str
    monitoring_task_id: str
    batch_id: Optional[str] = None
    task_ids: List[str] = None
    status: str = "submitted"
    total_urls: int = 0
    submitted_at: datetime = None
    message: str = ""
    external_result: Optional[Dict[str, Any]] = None


class CrawlerTaskExecutor:
    """爬虫任务执行器"""
    
    def __init__(self):
        self.crawler_api: Optional[CrawlerAPIService] = None
        self.config_service = CrawlerConfigService()
        self.task_storage = TaskStorageService()
        
        # 执行统计
        self.stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "total_urls_processed": 0,
            "avg_execution_time": 0.0
        }
    
    async def initialize(self):
        """初始化执行器"""
        self.crawler_api = await get_crawler_api_service()
        logger.info("Crawler task executor initialized")
    
    async def execute_monitoring_task(
        self, 
        request: TaskExecutionRequest
    ) -> TaskExecutionResult:
        """执行监控任务
        
        Args:
            request: 任务执行请求
            
        Returns:
            任务执行结果
        """
        if not self.crawler_api:
            await self.initialize()
        
        start_time = datetime.now()
        execution_id = f"exec_{request.monitoring_task_id}_{int(start_time.timestamp())}"
        
        try:
            # 获取爬虫配置
            config = await self.config_service.get_config(request.config_id)
            if not config:
                raise ValueError(f"爬虫配置不存在: {request.config_id}")
            
            # 根据执行模式选择执行方法
            if request.execution_mode == ExecutionMode.BATCH:
                result = await self._execute_batch_task(request, config, execution_id)
            elif request.execution_mode == ExecutionMode.SINGLE:
                result = await self._execute_single_tasks(request, config, execution_id)
            else:
                raise ValueError(f"不支持的执行模式: {request.execution_mode}")
            
            # 更新统计信息
            execution_time = (datetime.now() - start_time).total_seconds()
            self.stats["total_executions"] += 1
            self.stats["successful_executions"] += 1
            self.stats["total_urls_processed"] += len(request.urls)
            
            # 更新配置使用统计
            config.usage_count += 1
            config.last_used_at = datetime.now()
            await self.config_service.update_config(
                request.config_id, 
                type('ConfigUpdate', (), {
                    'dict': lambda exclude_unset=False: {
                        'usage_count': config.usage_count,
                        'last_used_at': config.last_used_at
                    }
                })()
            )
            
            logger.info(f"Task execution completed: {execution_id}, duration: {execution_time:.2f}s")
            return result
            
        except Exception as e:
            # 更新失败统计
            self.stats["total_executions"] += 1
            self.stats["failed_executions"] += 1
            
            logger.error(f"Task execution failed: {execution_id}, error: {e}")
            
            return TaskExecutionResult(
                execution_id=execution_id,
                monitoring_task_id=request.monitoring_task_id,
                status="failed",
                total_urls=len(request.urls),
                submitted_at=start_time,
                message=f"执行失败: {str(e)}"
            )
    
    async def _execute_batch_task(
        self, 
        request: TaskExecutionRequest, 
        config: CrawlerTaskConfig,
        execution_id: str
    ) -> TaskExecutionResult:
        """执行批量任务"""
        
        # 构建批量任务请求
        batch_request = BatchTaskRequest(
            urls=request.urls,
            q=config.llm_query,
            schema_str=config.llm_schema,
            cache=config.cache_enabled,
            priority=request.priority,
            batch_name=f"MonIt-{request.monitoring_task_id}",
            batch_description=f"MonIt监控任务执行: {config.name}",
            callback_url=request.callback_url or config.callback_url,
            max_concurrent_tasks=config.max_concurrent_tasks,
            batch_timeout=config.batch_timeout
        )
        
        # 提交到外部爬虫API
        api_result = await self.crawler_api.create_batch_tasks(batch_request)
        
        # 构建执行结果
        result = TaskExecutionResult(
            execution_id=execution_id,
            monitoring_task_id=request.monitoring_task_id,
            batch_id=api_result.get("batch_id"),
            task_ids=api_result.get("task_ids", []),
            status="submitted",
            total_urls=api_result.get("total_tasks", len(request.urls)),
            submitted_at=datetime.now(),
            message=api_result.get("message", "批量任务提交成功"),
            external_result=api_result
        )
        
        # 存储执行记录到Redis
        await self._store_execution_record(result, config, request)
        
        return result
    
    async def _execute_single_tasks(
        self, 
        request: TaskExecutionRequest, 
        config: CrawlerTaskConfig,
        execution_id: str
    ) -> TaskExecutionResult:
        """执行单个任务（逐个提交）"""
        
        task_ids = []
        successful_urls = 0
        failed_urls = 0
        
        for url in request.urls:
            try:
                # 构建单个任务请求
                single_request = LlmJobRequest(
                    url=url,
                    q=config.llm_query,
                    schema=config.llm_schema,
                    cache=config.cache_enabled
                )
                
                # 提交到外部爬虫API
                api_result = await self.crawler_api.create_llm_job(single_request)
                
                if api_result.get("success", True):
                    task_ids.append(api_result.get("task_id", f"task_{len(task_ids)}"))
                    successful_urls += 1
                else:
                    failed_urls += 1
                    
            except Exception as e:
                logger.error(f"Failed to submit single task for URL {url}: {e}")
                failed_urls += 1
        
        # 构建执行结果
        result = TaskExecutionResult(
            execution_id=execution_id,
            monitoring_task_id=request.monitoring_task_id,
            task_ids=task_ids,
            status="submitted" if successful_urls > 0 else "failed",
            total_urls=len(request.urls),
            submitted_at=datetime.now(),
            message=f"单个任务提交完成: 成功{successful_urls}, 失败{failed_urls}"
        )
        
        # 存储执行记录到Redis
        await self._store_execution_record(result, config, request)
        
        return result
    
    async def _store_execution_record(
        self, 
        result: TaskExecutionResult, 
        config: CrawlerTaskConfig,
        request: TaskExecutionRequest
    ):
        """存储执行记录到Redis"""
        
        # 执行记录
        execution_record = {
            "execution_id": result.execution_id,
            "monitoring_task_id": result.monitoring_task_id,
            "config_id": request.config_id,
            "config_name": config.name,
            "execution_mode": request.execution_mode.value,
            "batch_id": result.batch_id,
            "task_ids": result.task_ids or [],
            "status": result.status,
            "total_urls": result.total_urls,
            "submitted_at": result.submitted_at.isoformat(),
            "priority": request.priority.value,
            "callback_url": request.callback_url,
            "metadata": request.metadata or {},
            "external_result": result.external_result or {}
        }
        
        # 存储执行记录
        redis_client = self.task_storage.redis_client
        execution_key = f"crawler_executions:{result.execution_id}"
        
        # 转换为字符串格式存储
        for key, value in execution_record.items():
            if isinstance(value, (list, dict)):
                execution_record[key] = json.dumps(value)
        
        redis_client.hset(execution_key, mapping=execution_record)
        redis_client.expire(execution_key, 86400 * 7)  # 7天过期
        
        # 添加到监控任务的执行历史
        task_executions_key = f"monitoring_tasks:executions:{result.monitoring_task_id}"
        redis_client.lpush(task_executions_key, result.execution_id)
        redis_client.ltrim(task_executions_key, 0, 99)  # 保留最近100次执行
        redis_client.expire(task_executions_key, 86400 * 30)  # 30天过期
    
    async def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取执行状态"""
        redis_client = self.task_storage.redis_client
        execution_key = f"crawler_executions:{execution_id}"
        
        execution_data = redis_client.hgetall(execution_key)
        if not execution_data:
            return None
        
        # 转换JSON字符串
        for key in ['task_ids', 'metadata', 'external_result']:
            if execution_data.get(key):
                try:
                    execution_data[key] = json.loads(execution_data[key])
                except:
                    pass
        
        # 如果有batch_id，获取批次状态
        if execution_data.get('batch_id') and self.crawler_api:
            try:
                batch_status = await self.crawler_api.get_batch_tasks(execution_data['batch_id'])
                execution_data['batch_status'] = batch_status
            except Exception as e:
                logger.warning(f"Failed to get batch status for {execution_data['batch_id']}: {e}")
        
        return execution_data
    
    async def get_task_executions(self, monitoring_task_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取监控任务的执行历史"""
        redis_client = self.task_storage.redis_client
        task_executions_key = f"monitoring_tasks:executions:{monitoring_task_id}"
        
        execution_ids = redis_client.lrange(task_executions_key, 0, limit - 1)
        
        executions = []
        for execution_id in execution_ids:
            execution_data = await self.get_execution_status(execution_id)
            if execution_data:
                executions.append(execution_data)
        
        return executions
    
    def get_executor_stats(self) -> Dict[str, Any]:
        """获取执行器统计信息"""
        success_rate = 0.0
        if self.stats["total_executions"] > 0:
            success_rate = self.stats["successful_executions"] / self.stats["total_executions"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "crawler_api_stats": self.crawler_api.get_stats() if self.crawler_api else {}
        }


# 全局任务执行器实例
task_executor = CrawlerTaskExecutor()


async def get_task_executor() -> CrawlerTaskExecutor:
    """获取任务执行器实例"""
    if not task_executor.crawler_api:
        await task_executor.initialize()
    return task_executor
