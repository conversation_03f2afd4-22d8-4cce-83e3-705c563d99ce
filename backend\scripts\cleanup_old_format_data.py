#!/usr/bin/env python3
"""
清理旧格式的Redis数据

移除旧的扁平存储格式，只保留新的分层存储格式
"""

import redis

def cleanup_old_format_data():
    """清理旧格式的Redis数据"""
    print("🔍 清理旧格式的Redis数据")
    print("=" * 50)
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 查找所有旧格式的task_urls键
        old_task_url_keys = r.keys("monitoring_tasks:task_urls:*")
        
        print(f"\n📋 找到旧格式的task_urls键: {len(old_task_url_keys)} 个")
        for key in old_task_url_keys:
            print(f"  {key}")
        
        if old_task_url_keys:
            # 删除旧格式的键
            deleted_count = r.delete(*old_task_url_keys)
            print(f"\n✅ 成功删除 {deleted_count} 个旧格式的键")
        else:
            print(f"\n✅ 没有找到需要清理的旧格式键")
        
        # 查找其他可能的旧格式键
        other_old_keys = []
        
        # 检查是否有其他旧格式的键
        all_monitoring_keys = r.keys("monitoring_tasks:*")
        for key in all_monitoring_keys:
            # 排除新格式的键
            if not (key.startswith("monitoring_tasks:tasks:") or 
                   key.startswith("monitoring_tasks:indexes:")):
                other_old_keys.append(key)
        
        if other_old_keys:
            print(f"\n📋 找到其他可能的旧格式键: {len(other_old_keys)} 个")
            for key in other_old_keys:
                print(f"  {key}")
            
            # 询问是否删除
            print(f"\n⚠️ 这些键可能是旧格式的，建议手动检查后删除")
        else:
            print(f"\n✅ 没有找到其他旧格式的键")
        
        print(f"\n🎉 旧格式数据清理完成!")
        
    except Exception as e:
        print(f"❌ 清理旧格式数据时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    cleanup_old_format_data()
