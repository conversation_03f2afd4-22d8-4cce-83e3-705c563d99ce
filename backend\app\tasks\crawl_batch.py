"""
批量爬取任务

处理分片后的URL批次，调用外部爬虫API进行数据采集
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from celery import Task
from celery.exceptions import Retry
import threading

from app.celery_app import celery_app
from app.core.rate_limiter import RateLimiter, RateLimitConfig
from crawler.api_client import CrawlerAPIClient, APIConfig
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.snapshot import ProductSnapshot
from app.services.crawler_request_builder import CrawlerRequestBuilder
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

logger = logging.getLogger(__name__)


async def _crawl_batch_with_request_builder(
    celery_task_self,
    task_id: str,
    execution_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    使用CrawlerRequestBuilder的批量爬取实现

    这是新的实现方式，使用标准化的请求构建逻辑
    """
    start_time = datetime.now()

    try:
        logger.info(f"Starting batch crawl with request builder for task: {task_id}")

        # 添加Celery任务信息到执行上下文
        if execution_context is None:
            execution_context = {}

        execution_context.update({
            "celery_task_id": celery_task_self.request.id,
            "celery_task_name": "crawl_batch_task_enhanced",
            "start_time": start_time.isoformat()
        })

        # 1. 使用CrawlerRequestBuilder构建标准化请求
        logger.info("Building crawler request using CrawlerRequestBuilder...")
        crawler_request = await CrawlerRequestBuilder.build_request_for_task(
            task_id=task_id,
            execution_context=execution_context
        )

        # 记录请求信息
        urls_count = len(crawler_request["request"]["urls"])
        priority = crawler_request["request"]["priority"]
        batch_name = crawler_request["request"]["batch_name"]

        logger.info(f"Built crawler request: {urls_count} URLs, priority: {priority}, batch: {batch_name}")

        # 2. 发送请求到爬虫API
        logger.info("Sending request to crawler API...")
        from app.services.crawler_api_service import CrawlerAPIService
        crawler_api = CrawlerAPIService()

        try:
            # 发送批量爬取请求
            api_result = await crawler_api.submit_batch_request(crawler_request)

            logger.info(f"Crawler API request successful: {api_result}")

            # 构建成功结果
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            result = {
                "status": "success",
                "task_id": task_id,
                "celery_task_id": celery_task_self.request.id,
                "batch_name": batch_name,
                "urls_count": urls_count,
                "priority": priority,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration": duration,
                "crawler_api_result": api_result,
                "execution_context": execution_context,
                "method": "request_builder"  # 标识使用了新方法
            }

            logger.info(f"Batch crawl with request builder completed: {task_id}, duration: {duration:.2f}s")
            return result

        except Exception as api_error:
            logger.error(f"Crawler API request failed: {api_error}")
            raise

    except ValueError as ve:
        # 处理预期的错误（如没有活跃URL、没有配置等）
        logger.warning(f"Batch crawl validation error: {ve}")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        return {
            "status": "validation_error",
            "task_id": task_id,
            "celery_task_id": celery_task_self.request.id,
            "error": str(ve),
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration": duration,
            "execution_context": execution_context,
            "method": "request_builder"
        }

    except Exception as e:
        # 处理其他异常
        logger.error(f"Batch crawl with request builder failed: {e}")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 重新抛出异常以触发Celery重试
        raise Exception(f"Batch crawl with request builder failed for {task_id}: {str(e)}")


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True
)
def crawl_batch_task_enhanced(
    self,
    task_id: str,
    execution_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    增强的批量爬取任务

    使用CrawlerRequestBuilder构建标准化请求

    Args:
        task_id: 监控任务ID
        execution_context: 执行上下文

    Returns:
        爬取结果
    """
    return _run_async_in_thread(_crawl_batch_with_request_builder(
        self, task_id, execution_context
    ))


class CrawlBatchTask(Task):
    """批量爬取任务基类"""
    
    def __init__(self):
        self.rate_limiter: Optional[RateLimiter] = None
        self.api_client: Optional[CrawlerAPIClient] = None
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """重试时的回调"""
        logger.warning(f"Task {task_id} retrying due to: {exc}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """失败时的回调"""
        logger.error(f"Task {task_id} failed: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """成功时的回调"""
        logger.info(f"Task {task_id} completed successfully")


def _run_async_in_thread(coro):
    """在新线程中运行异步协程"""
    def run_in_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    # 在新线程中运行
    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(run_in_thread)
        return future.result()


@celery_app.task(
    bind=True,
    base=CrawlBatchTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True
)
def crawl_batch_task(
    self,
    batch_id: str,
    urls: List[str],
    platform: str = "mercadolibre",
    task_id: Optional[str] = None,  # 改为字符串类型，支持UUID
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """批量爬取任务

    Args:
        batch_id: 批次ID
        urls: URL列表
        platform: 平台类型
        task_id: 任务ID（支持UUID字符串或数字字符串）
        options: 爬取选项

    Returns:
        Dict[str, Any]: 爬取结果
    """
    # 检查是否使用新的请求构建方式
    if options and options.get("use_request_builder", False):
        # 使用新的CrawlerRequestBuilder方式
        return _run_async_in_thread(_crawl_batch_with_request_builder(
            self, task_id, options.get("execution_context", {})
        ))
    else:
        # 使用原有的方式（向后兼容）
        return _run_async_in_thread(_crawl_batch_async(
            self, batch_id, urls, platform, task_id, options
        ))


async def _crawl_batch_async(
    celery_task_self,
    batch_id: str,
    urls: List[str],
    platform: str,
    task_id: Optional[str],  # 改为字符串类型，支持UUID
    options: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步批量爬取实现"""
    
    start_time = datetime.now()
    results = {
        "batch_id": batch_id,
        "total_urls": len(urls),
        "successful_urls": 0,
        "failed_urls": 0,
        "results": [],
        "errors": [],
        "start_time": start_time.isoformat(),
        "end_time": None,
        "duration": 0.0
    }
    
    # 初始化限流器和API客户端
    rate_limiter = RateLimiter(RateLimitConfig())

    # 检查是否有指定的爬虫后端 - 强制要求后端配置
    crawler_backend = options.get("crawler_backend")
    if not crawler_backend:
        error_msg = f"Batch {batch_id}: 没有指定爬虫后端配置。任务必须分配Worker才能执行。"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 验证后端配置的完整性
    required_fields = ["name", "base_url"]
    missing_fields = [field for field in required_fields if not crawler_backend.get(field)]
    if missing_fields:
        error_msg = f"Batch {batch_id}: 爬虫后端配置不完整，缺少字段: {missing_fields}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 使用指定的后端配置
    api_config = APIConfig(
        base_url=crawler_backend.get("base_url"),
        timeout=crawler_backend.get("timeout", 30.0),
        max_retries=crawler_backend.get("max_retries", 3),
        api_key=crawler_backend.get("api_key"),
        auth_type=crawler_backend.get("auth_type", "none")
    )
    logger.info(f"Using crawler backend: {crawler_backend.get('name')} ({crawler_backend.get('base_url')})")

    api_client = CrawlerAPIClient(api_config)
    
    try:
        await rate_limiter.start()
        await api_client.start()
        
        # 更新任务状态为运行中
        if task_id:
            await _update_task_status(task_id, "running", 0)
        
        logger.info(f"Starting batch {batch_id} with {len(urls)} URLs")

        # 创建统一执行会话
        execution_session = None
        try:
            from ..services.unified_execution_service import unified_execution_service

            # 获取监控任务ID和Celery任务ID
            monitoring_task_id = None
            celery_task_id = None

            if options and "monitoring_task_id" in options:
                monitoring_task_id = options["monitoring_task_id"]
            elif task_id:
                monitoring_task_id = str(task_id)
            else:
                monitoring_task_id = f"batch_{batch_id}"

            if celery_task_self:
                celery_task_id = celery_task_self.request.id
            else:
                celery_task_id = f"unknown_{batch_id}"

            # 创建执行会话
            execution_session = await unified_execution_service.create_execution_session(
                monitoring_task_id=monitoring_task_id,
                celery_task_id=celery_task_id,
                execution_type="manual" if options and options.get("manual_execution") else "scheduled",
                urls=urls
            )

            logger.info(f"Created execution session: {execution_session.session_id}")

        except Exception as e:
            logger.warning(f"Failed to create execution session: {e}")

        # 处理URL批次
        for i, url in enumerate(urls):
            url_start_time = datetime.now()
            url_start_time_iso = url_start_time.isoformat()

            try:
                # 获取限流许可（带指数退避重试）
                request_id = f"{batch_id}_{i}"
                acquired = await _acquire_rate_limit_with_backoff(rate_limiter, request_id)
                if not acquired:
                    raise Exception("Rate limit exceeded after multiple attempts")

                # 发送URL开始处理状态
                if task_id:
                    await _send_url_status_update(
                        task_id, url, "processing", f"worker_{batch_id}",
                        start_time=url_start_time_iso
                    )

                # 记录爬虫请求（用于调试）
                crawler_request_id = None
                try:
                    from ..api.crawler_debug_routes import log_crawler_api_call

                    # 构建API请求信息 - 使用实际发送给爬虫Worker的格式
                    # 创建符合标准API格式的载荷
                    try:
                        actual_payload = api_client._create_worker_api_payload([url], platform, options)
                        logger.info(f"Created standard API payload for URL: {url}")
                    except Exception as payload_error:
                        logger.warning(f"Failed to create standard API payload, using fallback: {payload_error}")
                        # 使用fallback载荷格式
                        actual_payload = {
                            "url": url,
                            "platform": platform,
                            "options": options or {}
                        }

                    # 获取实际的爬虫Worker端点
                    crawler_endpoint = "unknown"
                    if options and "crawler_backend" in options:
                        backend = options["crawler_backend"]
                        base_url = backend.get("base_url", "unknown")
                        crawler_endpoint = f"{base_url}/crawl"
                    elif hasattr(api_client, 'config') and hasattr(api_client.config, 'base_url'):
                        crawler_endpoint = f"{api_client.config.base_url}/crawl"

                    # 创建URL执行记录
                    url_execution = None
                    if execution_session:
                        try:
                            url_execution = await unified_execution_service.create_url_execution(
                                session_id=execution_session.session_id,
                                monitoring_task_id=monitoring_task_id,
                                celery_task_id=celery_task_id,
                                url=url,
                                assigned_worker_id=actual_worker_name,
                                crawler_endpoint=crawler_endpoint
                            )
                            logger.info(f"Created URL execution record: {url_execution.execution_id}")
                        except Exception as e:
                            logger.warning(f"Failed to create URL execution record: {e}")

                    api_request = {
                        "method": "POST",
                        "endpoint": crawler_endpoint,  # 添加实际端点信息
                        "headers": {
                            "Content-Type": "application/json",
                            "User-Agent": "MonIt-Crawler/1.0"
                        },
                        "payload": actual_payload
                    }

                    # 获取当前Celery Worker名称
                    celery_worker_name = None
                    try:
                        import socket

                        # 使用celery_task_self参数获取当前任务信息
                        if celery_task_self and hasattr(celery_task_self, 'request'):
                            # 尝试多种方式获取worker信息
                            if hasattr(celery_task_self.request, 'hostname') and celery_task_self.request.hostname:
                                celery_worker_name = celery_task_self.request.hostname
                            elif hasattr(celery_task_self.request, 'id'):
                                # 使用任务ID和主机名组合
                                hostname = socket.gethostname()
                                celery_worker_name = f"celery@{hostname}"

                        # 如果还是没有，使用默认值
                        if not celery_worker_name:
                            hostname = socket.gethostname()
                            celery_worker_name = f"celery@{hostname}"

                    except Exception as e:
                        # 最后的备用方案
                        try:
                            import socket
                            hostname = socket.gethostname()
                            celery_worker_name = f"celery@{hostname}"
                        except:
                            celery_worker_name = "celery@unknown"

                    # 获取监控任务ID和实际分配的爬虫Worker名称
                    # 优先使用options中的monitoring_task_id（监控任务UUID）
                    # 如果没有，则使用task_id（可能是UUID或数字ID）
                    monitoring_task_id = None
                    if options and "monitoring_task_id" in options:
                        monitoring_task_id = options["monitoring_task_id"]
                    elif task_id:
                        monitoring_task_id = str(task_id)
                    else:
                        monitoring_task_id = f"batch_{batch_id}"

                    # 获取Celery任务ID
                    celery_task_id = None
                    if celery_task_self:
                        celery_task_id = celery_task_self.request.id

                    actual_worker_name = "unknown"

                    try:
                        # 方法1: 从任务分配中获取Worker信息
                        from ..services.task_assignment_service import task_assignment_service
                        task_assignments = await task_assignment_service.list_assignments(task_id=monitoring_task_id)

                        if task_assignments:
                            # 使用第一个分配的Worker
                            assignment = task_assignments[0]
                            if assignment.worker_ids:
                                primary_worker_id = assignment.worker_ids[0]

                                # 获取Worker详细信息
                                from ..services.crawler_worker_service import crawler_worker_service
                                worker = await crawler_worker_service.get_worker(primary_worker_id)
                                if worker:
                                    actual_worker_name = worker.worker_name
                                    logger.info(f"Found assigned worker: {actual_worker_name} for task {monitoring_task_id}")
                                else:
                                    actual_worker_name = primary_worker_id
                                    logger.info(f"Using worker ID as name: {actual_worker_name}")

                        # 如果还是unknown，尝试其他方法
                        if actual_worker_name == "unknown":
                            # 方法2: 从options中获取Worker信息
                            if options and "assigned_worker" in options:
                                actual_worker_name = options["assigned_worker"]
                                logger.info(f"Using worker from options: {actual_worker_name}")
                            elif options and "crawler_backend" in options:
                                backend = options["crawler_backend"]
                                actual_worker_name = backend.get("name", "crawler_backend")
                                logger.info(f"Using backend name as worker: {actual_worker_name}")
                            else:
                                # 方法3: 使用默认Worker命名
                                actual_worker_name = f"crawler_worker_{batch_id}"
                                logger.info(f"Using default worker name: {actual_worker_name}")

                    except Exception as worker_error:
                        logger.warning(f"Failed to get actual worker name for task {monitoring_task_id}: {worker_error}")
                        # 最后的fallback - 尝试从options获取
                        if options and "crawler_backend" in options:
                            backend = options["crawler_backend"]
                            actual_worker_name = backend.get("name", f"crawler_worker_{batch_id}")
                        else:
                            actual_worker_name = f"crawler_worker_{batch_id}"

                    # 记录任务关联信息
                    try:
                        from ..services.task_relation_service import task_relation_service
                        await task_relation_service.create_task_relation(
                            monitoring_task_id=monitoring_task_id,
                            celery_task_id=celery_task_self.request.id if celery_task_self else "unknown",
                            worker_id=actual_worker_name,
                            url=url,
                            batch_id=batch_id
                        )
                    except Exception as relation_error:
                        logger.warning(f"Failed to record task relation: {relation_error}")

                    crawler_request_id = await log_crawler_api_call(
                        task_id=monitoring_task_id,  # 使用监控任务UUID
                        url=url,
                        worker_id=actual_worker_name,
                        api_request=api_request,
                        celery_worker=celery_worker_name
                    )

                except Exception as log_error:
                    logger.warning(f"Failed to log crawler request: {log_error}")

                # 调用外部API爬取（确保限流许可释放）
                api_success = False
                try:
                    result = await api_client.crawl_url(url, platform, options)
                    response_time = (datetime.now() - url_start_time).total_seconds()
                    api_success = True

                    # 更新URL执行状态为成功
                    if url_execution:
                        try:
                            await unified_execution_service.update_url_execution_status(
                                url_execution.execution_id,
                                "completed",
                                response_time=response_time
                            )
                        except Exception as e:
                            logger.warning(f"Failed to update URL execution status: {e}")

                    # 记录成功响应
                    if crawler_request_id:
                        try:
                            from ..api.crawler_debug_routes import log_crawler_api_response
                            await log_crawler_api_response(
                                crawler_request_id,
                                response={
                                    "status": 200,
                                    "headers": {"Content-Type": "application/json"},
                                    "data": result,
                                    "response_time": int(response_time * 1000)  # 转换为毫秒
                                }
                            )
                        except Exception as log_error:
                            logger.warning(f"Failed to log crawler response: {log_error}")

                except Exception as api_error:
                    response_time = (datetime.now() - url_start_time).total_seconds()
                    logger.error(f"API call failed for {url}: {api_error}")

                    # 更新URL执行状态为失败
                    if url_execution:
                        try:
                            await unified_execution_service.update_url_execution_status(
                                url_execution.execution_id,
                                "failed",
                                response_time=response_time,
                                error_message=str(api_error)
                            )
                        except Exception as e:
                            logger.warning(f"Failed to update URL execution status: {e}")

                    raise api_error
                finally:
                    # 确保在所有情况下都释放限流许可
                    try:
                        await rate_limiter.release(request_id, api_success, response_time)
                        logger.debug(f"Released rate limit for {request_id}, success: {api_success}")
                    except Exception as release_error:
                        logger.error(f"Failed to release rate limit for {request_id}: {release_error}")

                if api_success:
                    # 处理成功结果
                    results["successful_urls"] += 1
                    result_data = {
                        "url": url,
                        "success": True,
                        "data": result,
                        "response_time": response_time
                    }
                    results["results"].append(result_data)

                    # 保存到数据库
                    if task_id and result.get("data"):
                        await _save_product_snapshot(task_id, url, result["data"])

                    # 发送URL完成状态（包含结果和响应时间）
                    if task_id:
                        await _send_url_status_update(
                            task_id, url, "completed", f"worker_{batch_id}",
                            result=result_data, response_time=response_time,
                            start_time=url_start_time_iso
                        )

                    logger.debug(f"Successfully crawled {url} in {response_time:.2f}s")
                else:
                    # API调用失败，处理失败结果
                    results["failed_urls"] += 1
                    error_info = {
                        "url": url,
                        "success": False,
                        "error": "API call failed",
                        "response_time": response_time
                    }
                    results["errors"].append(error_info)

                    # 发送URL失败状态（包含错误信息和响应时间）
                    if task_id:
                        await _send_url_status_update(
                            task_id, url, "failed", f"worker_{batch_id}",
                            result=error_info, response_time=response_time,
                            start_time=url_start_time_iso
                        )

                    logger.error(f"Failed to crawl {url}: API call failed")
                
                # 更新进度
                if task_id:
                    progress = int((i + 1) / len(urls) * 100)
                    await _update_task_progress(task_id, progress)

                    # 发送WebSocket进度更新
                    await _send_progress_update(task_id, {
                        "total_urls": len(urls),
                        "processed_urls": i + 1,
                        "successful_urls": results["successful_urls"],
                        "failed_urls": results["failed_urls"],
                        "progress": progress,
                        "current_url": url,
                        "batch_id": batch_id
                    })

                # 存储当前处理URL到Redis（用于Celery监控显示）
                await _store_current_processing_url(task_id, url, progress, batch_id)
                
                # 避免过快请求，并检查任务健康状态
                await asyncio.sleep(1.0)

                # 检查任务是否应该被终止（防止卡死）
                if task_id:
                    await _check_task_health(task_id, i + 1, len(urls))
                
            except Exception as e:
                logger.error(f"Error processing URL {url}: {e}")
                results["failed_urls"] += 1
                results["errors"].append({
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "response_time": 0.0
                })
        
        # 完成处理
        end_time = datetime.now()
        results["end_time"] = end_time.isoformat()
        results["duration"] = (end_time - start_time).total_seconds()
        
        # 更新任务状态
        if task_id:
            final_status = "completed" if results["failed_urls"] == 0 else "partial"
            await _update_task_status(
                task_id, 
                final_status, 
                100,
                results["successful_urls"],
                results["failed_urls"]
            )
        
        logger.info(
            f"Batch {batch_id} completed: "
            f"{results['successful_urls']}/{results['total_urls']} successful "
            f"in {results['duration']:.2f}s"
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Batch {batch_id} failed: {e}")
        
        # 更新任务状态为失败
        if task_id:
            await _update_task_status(task_id, "failed", 0)
        
        results["errors"].append({
            "batch_error": str(e),
            "timestamp": datetime.now().isoformat()
        })
        
        raise
        
    finally:
        # 清理资源
        if rate_limiter:
            await rate_limiter.stop()
        if api_client:
            await api_client.close()


async def _update_task_status(
    task_id: int,
    status: str,
    progress: int,
    success_count: int = 0,
    failed_count: int = 0
) -> None:
    """更新任务状态"""
    try:
        async with get_async_session() as session:
            update_data = {
                "status": status,
                "progress": progress
            }
            
            if status == "running" and progress == 0:
                update_data["started_at"] = datetime.now()
            elif status in ["completed", "partial", "failed"]:
                update_data["completed_at"] = datetime.now()
                if success_count > 0:
                    update_data["success_count"] = success_count
                if failed_count > 0:
                    update_data["failed_count"] = failed_count
            
            await session.execute(
                update(CrawlTask)
                .where(CrawlTask.id == task_id)
                .values(**update_data)
            )
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to update task status: {e}")


async def _update_task_progress(task_id: int, progress: int) -> None:
    """更新任务进度"""
    try:
        async with get_async_session() as session:
            await session.execute(
                update(CrawlTask)
                .where(CrawlTask.id == task_id)
                .values(progress=progress)
            )
            await session.commit()

    except Exception as e:
        logger.error(f"Failed to update task progress: {e}")


async def _send_progress_update(task_id: int, progress_data: dict) -> None:
    """发送WebSocket进度更新"""
    try:
        # 导入WebSocket管理器
        from ..main import manager

        # 构造进度消息
        message = {
            "type": "task_progress",
            "task_id": str(task_id),
            "timestamp": datetime.now().isoformat(),
            "data": progress_data
        }

        # 广播到订阅该任务的连接
        await manager.broadcast_to_task(str(task_id), json.dumps(message))

        logger.debug(f"Sent progress update for task {task_id}: {progress_data['progress']}%")

    except Exception as e:
        logger.error(f"Failed to send progress update: {e}")


async def _acquire_rate_limit_with_backoff(rate_limiter, request_id: str, max_attempts: int = 5) -> bool:
    """带指数退避的限流许可获取"""
    for attempt in range(max_attempts):
        try:
            if await rate_limiter.acquire(request_id):
                logger.debug(f"Rate limit acquired for {request_id} on attempt {attempt + 1}")
                return True

            # 指数退避，但限制最大延迟时间
            base_delay = 10  # 基础延迟10秒
            delay = min(base_delay * (2 ** attempt), 300)  # 最大5分钟

            # 添加随机抖动，避免多个任务同时重试
            import random
            jitter = random.uniform(0.8, 1.2)
            actual_delay = delay * jitter

            logger.warning(f"Rate limit not available for {request_id}, waiting {actual_delay:.1f}s (attempt {attempt + 1}/{max_attempts})")
            await asyncio.sleep(actual_delay)

        except Exception as e:
            logger.error(f"Error acquiring rate limit for {request_id}: {e}")
            if attempt == max_attempts - 1:
                return False
            await asyncio.sleep(5)  # 短暂等待后重试

    logger.error(f"Failed to acquire rate limit for {request_id} after {max_attempts} attempts")
    return False


async def _store_current_processing_url(task_id: int, current_url: str, progress: int, batch_id: str = None) -> None:
    """存储当前处理的URL到Redis，用于Celery监控显示"""
    try:
        import redis.asyncio as redis
        from celery import current_task

        redis_client = redis.from_url("redis://redis:6379/0")

        # 存储任务进度和当前URL
        progress_data = {
            "progress": progress,
            "current_url": current_url,
            "updated_at": datetime.now().isoformat()
        }

        # 方式1: 存储到任务ID键（如果有task_id）
        if task_id:
            progress_key = f"task_progress:{task_id}"
            await redis_client.setex(
                progress_key,
                300,  # 5分钟过期
                json.dumps(progress_data, ensure_ascii=False)
            )

            # 同时存储到最近进度键（用于备用查询）
            recent_progress_key = f"task_recent_progress:{task_id}"
            await redis_client.setex(
                recent_progress_key,
                300,  # 5分钟过期
                json.dumps(progress_data, ensure_ascii=False)
            )

        # 方式2: 存储到batch_id键
        if batch_id:
            batch_progress_key = f"batch_progress:{batch_id}"
            await redis_client.setex(
                batch_progress_key,
                300,  # 5分钟过期
                json.dumps(progress_data, ensure_ascii=False)
            )

        # 方式3: 存储到Celery任务ID键（最重要的方式）
        if current_task and current_task.request.id:
            celery_task_key = f"celery_task_progress:{current_task.request.id}"
            await redis_client.setex(
                celery_task_key,
                300,  # 5分钟过期
                json.dumps(progress_data, ensure_ascii=False)
            )
            logger.debug(f"Stored current URL for Celery task {current_task.request.id}: {current_url}")

        await redis_client.close()

        logger.debug(f"Stored current processing URL for task {task_id}, batch {batch_id}: {current_url}")

    except Exception as e:
        logger.error(f"Failed to store current processing URL: {e}")


async def _check_task_health(task_id: int, processed_count: int, total_count: int) -> None:
    """检查任务健康状态，防止任务卡死"""
    try:
        import redis.asyncio as redis

        redis_client = redis.from_url("redis://redis:6379/0")

        # 获取任务开始时间
        task_start_key = f"task_start_time:{task_id}"
        start_time_str = await redis_client.get(task_start_key)

        if not start_time_str:
            # 首次记录任务开始时间
            await redis_client.setex(
                task_start_key,
                7200,  # 2小时过期
                datetime.now().isoformat()
            )
            await redis_client.close()
            return

        # 计算任务运行时间
        start_time = datetime.fromisoformat(start_time_str)
        runtime = (datetime.now() - start_time).total_seconds()

        # 计算预期完成时间（假设每个URL平均2秒）
        expected_time = total_count * 2
        progress_ratio = processed_count / total_count

        # 检查是否超时
        if runtime > expected_time * 2:  # 超过预期时间2倍
            logger.warning(
                f"Task {task_id} may be stuck: runtime={runtime:.1f}s, "
                f"expected={expected_time:.1f}s, progress={processed_count}/{total_count}"
            )

            # 记录可能卡死的任务
            stuck_task_key = f"stuck_task:{task_id}"
            stuck_info = {
                "task_id": task_id,
                "runtime": runtime,
                "expected_time": expected_time,
                "processed_count": processed_count,
                "total_count": total_count,
                "detected_at": datetime.now().isoformat()
            }
            await redis_client.setex(
                stuck_task_key,
                3600,  # 1小时过期
                json.dumps(stuck_info)
            )

        await redis_client.close()

    except Exception as e:
        logger.error(f"Failed to check task health: {e}")


async def _persist_url_execution_status(task_id: str, url: str, status: str, worker_id: str = None, result: dict = None, response_time: float = 0.0, start_time: str = None) -> None:
    """持久化URL执行状态到Redis"""
    try:
        from ..core.redis_client import get_redis_client
        redis_client = await get_redis_client()

        # URL执行状态键
        url_execution_key = f"url_execution:{task_id}:{url}"

        # 构建执行状态数据
        execution_data = {
            "task_id": task_id,
            "url": url,
            "status": status,
            "worker_id": worker_id or "unknown",
            "updated_at": datetime.now().isoformat(),
            "response_time": response_time
        }

        # 添加开始时间
        if start_time:
            execution_data["started_at"] = start_time

        # 添加完成时间
        if status in ["completed", "failed"]:
            execution_data["completed_at"] = datetime.now().isoformat()

        # 添加结果数据
        if result:
            execution_data["result"] = result
            execution_data["success"] = result.get("success", False)
            execution_data["data_extracted"] = bool(result.get("data"))

        # 存储到Redis (24小时过期)
        await redis_client.setex(
            url_execution_key,
            86400,  # 24小时
            json.dumps(execution_data, default=str, ensure_ascii=False)
        )

        # 更新任务级别的URL状态索引
        task_url_status_key = f"task_url_status:{task_id}"
        await redis_client.hset(task_url_status_key, url, status)
        await redis_client.expire(task_url_status_key, 86400)

        logger.debug(f"Persisted URL execution status: {task_id}:{url} -> {status}")

    except Exception as e:
        logger.error(f"Failed to persist URL execution status: {e}")


async def _send_url_status_update(task_id: int, url: str, status: str, worker_id: str = None, result: dict = None, response_time: float = 0.0, start_time: str = None) -> None:
    """发送URL状态更新并持久化"""
    try:
        from ..main import manager

        message = {
            "type": "url_status_change",
            "task_id": str(task_id),
            "timestamp": datetime.now().isoformat(),
            "data": {
                "url": url,
                "status": status,
                "worker_id": worker_id,
                "updated_at": datetime.now().isoformat(),
                "response_time": response_time,
                "result": result
            }
        }

        # 发送WebSocket消息
        await manager.broadcast_to_task(str(task_id), json.dumps(message))

        # 持久化状态到Redis
        await _persist_url_execution_status(
            str(task_id), url, status, worker_id, result, response_time, start_time
        )

    except Exception as e:
        logger.error(f"Failed to send URL status update: {e}")


async def _save_product_snapshot(
    task_id: int,
    url: str,
    product_data: Dict[str, Any]
) -> None:
    """保存商品快照数据"""
    try:
        async with get_async_session() as session:
            snapshot = ProductSnapshot(
                task_id=task_id,
                product_url=url,
                product_name=product_data.get("title", ""),
                current_price=product_data.get("price", 0.0),
                original_price=product_data.get("original_price", 0.0),
                discount_rate=product_data.get("discount_rate", 0.0),
                stock_quantity=product_data.get("stock", 0),
                sales_count=product_data.get("sales", 0),
                rating_score=product_data.get("rating", 0.0),
                rating_count=product_data.get("rating_count", 0),
                image_url=product_data.get("image_url", ""),
                raw_data=product_data,
                snapshot_time=datetime.now()
            )
            
            session.add(snapshot)
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to save product snapshot: {e}")


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30}
)
def crawl_single_url(
    self,
    url: str,
    platform: str = "mercadolibre",
    task_id: Optional[str] = None,  # 改为字符串类型，支持UUID
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """单URL爬取任务

    Args:
        url: 目标URL
        platform: 平台类型
        task_id: 任务ID（支持UUID字符串或数字字符串）
        options: 爬取选项

    Returns:
        Dict[str, Any]: 爬取结果
    """
    return _run_async_in_thread(_crawl_single_url_async(url, platform, task_id, options))


async def _crawl_single_url_async(
    url: str,
    platform: str,
    task_id: Optional[str],  # 改为字符串类型，支持UUID
    options: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步单URL爬取实现"""
    
    start_time = datetime.now()
    rate_limiter = RateLimiter(RateLimitConfig())

    # 检查是否有指定的爬虫后端 - 强制要求后端配置
    crawler_backend = options.get("crawler_backend")
    if not crawler_backend:
        error_msg = f"Single URL task for {url}: 没有指定爬虫后端配置。任务必须分配Worker才能执行。"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 验证后端配置的完整性
    required_fields = ["name", "base_url"]
    missing_fields = [field for field in required_fields if not crawler_backend.get(field)]
    if missing_fields:
        error_msg = f"Single URL task for {url}: 爬虫后端配置不完整，缺少字段: {missing_fields}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 使用指定的后端配置
    api_config = APIConfig(
        base_url=crawler_backend.get("base_url"),
        timeout=crawler_backend.get("timeout", 30.0),
        max_retries=crawler_backend.get("max_retries", 3),
        api_key=crawler_backend.get("api_key"),
        auth_type=crawler_backend.get("auth_type", "none")
    )
    logger.info(f"Using crawler backend for single URL: {crawler_backend.get('name')} ({crawler_backend.get('base_url')})")

    api_client = CrawlerAPIClient(api_config)
    
    try:
        await rate_limiter.start()
        await api_client.start()
        
        # 获取限流许可
        request_id = f"single_{int(start_time.timestamp())}"
        if not await rate_limiter.acquire(request_id):
            raise Exception("Rate limit exceeded")
        
        # 调用外部API
        try:
            result = await api_client.crawl_url(url, platform, options)
            response_time = (datetime.now() - start_time).total_seconds()
            
            await rate_limiter.release(request_id, True, response_time)
            
            # 保存结果
            if task_id and result.get("data"):
                await _save_product_snapshot(task_id, url, result["data"])
            
            return {
                "url": url,
                "success": True,
                "data": result,
                "response_time": response_time
            }
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            await rate_limiter.release(request_id, False, response_time)
            raise
            
    finally:
        if rate_limiter:
            await rate_limiter.stop()
        if api_client:
            await api_client.close()
