#!/usr/bin/env python3
"""
测试Prometheus可用指标
"""

import requests
import json

def test_prometheus_metrics():
    """测试Prometheus中可用的指标"""
    
    prometheus_url = "http://localhost:9090"
    
    print("🔍 测试Prometheus可用指标...")
    
    # 测试查询列表
    test_queries = [
        ("服务状态", "up"),
        ("CPU使用率", "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)"),
        ("内存使用率", "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100"),
        ("系统负载1分钟", "node_load1"),
        ("系统负载5分钟", "node_load5"),
        ("系统负载15分钟", "node_load15"),
        ("网络接收", "irate(node_network_receive_bytes_total{device!=\"lo\"}[5m])"),
        ("网络发送", "irate(node_network_transmit_bytes_total{device!=\"lo\"}[5m])"),
        ("磁盘使用率", "100 - ((node_filesystem_avail_bytes{mountpoint=\"/\"} * 100) / node_filesystem_size_bytes{mountpoint=\"/\"})"),
    ]
    
    print(f"📊 测试 {len(test_queries)} 个查询...")
    print()
    
    working_queries = []
    failed_queries = []
    
    for name, query in test_queries:
        try:
            response = requests.get(
                f"{prometheus_url}/api/v1/query",
                params={"query": query},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    results = data.get('data', {}).get('result', [])
                    if results:
                        print(f"✅ {name}: {len(results)} 个结果")
                        working_queries.append((name, query))
                        
                        # 显示第一个结果的值
                        if results[0].get('value'):
                            value = results[0]['value'][1]
                            metric = results[0].get('metric', {})
                            instance = metric.get('instance', 'unknown')
                            print(f"   📈 当前值: {value} (实例: {instance})")
                    else:
                        print(f"⚠️  {name}: 查询成功但无数据")
                        failed_queries.append((name, query, "无数据"))
                else:
                    print(f"❌ {name}: {data.get('error', '查询失败')}")
                    failed_queries.append((name, query, data.get('error', '查询失败')))
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                failed_queries.append((name, query, f"HTTP {response.status_code}"))
                
        except Exception as e:
            print(f"❌ {name}: {e}")
            failed_queries.append((name, query, str(e)))
        
        print()
    
    print("=" * 50)
    print("📋 测试总结")
    print("=" * 50)
    
    print(f"✅ 可用查询: {len(working_queries)}")
    print(f"❌ 失败查询: {len(failed_queries)}")
    
    if working_queries:
        print("\n🎯 推荐在Grafana中使用的查询:")
        for i, (name, query) in enumerate(working_queries, 1):
            print(f"{i}. {name}")
            print(f"   查询: {query}")
            print()
    
    if failed_queries:
        print("\n⚠️  需要检查的查询:")
        for name, query, error in failed_queries:
            print(f"- {name}: {error}")
    
    print("\n📖 下一步:")
    print("1. 访问 http://localhost:3001")
    print("2. 登录 Grafana (admin/admin123)")
    print("3. 创建新的 Dashboard")
    print("4. 使用上面的可用查询创建面板")
    print("5. 参考: docs/grafana_manual_setup_guide.md")

def main():
    """主函数"""
    print("=" * 50)
    print("MonIt Prometheus指标测试工具")
    print("=" * 50)
    
    try:
        test_prometheus_metrics()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
