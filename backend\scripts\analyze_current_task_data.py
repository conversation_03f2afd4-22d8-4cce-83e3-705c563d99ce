#!/usr/bin/env python3
"""
分析当前任务数据结构

详细分析当前Redis中存储的任务数据，为重构提供依据
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis


async def analyze_task_data():
    """分析任务数据结构"""
    try:
        # 连接Redis
        redis_client = redis.from_url("redis://localhost:6379/0")
        await redis_client.ping()
        
        print("🔍 当前任务数据结构分析")
        print("=" * 60)
        
        # 获取任务ID
        task_ids = await redis_client.smembers('monitoring_tasks:all_ids')
        print(f"📊 总任务数: {len(task_ids)}")
        
        if not task_ids:
            print("❌ 没有找到任务数据")
            return
        
        # 分析第一个任务的详细结构
        sample_task_id = list(task_ids)[0]
        if isinstance(sample_task_id, bytes):
            sample_task_id = sample_task_id.decode()
        
        print(f"\n📝 分析任务: {sample_task_id}")
        print("-" * 40)
        
        # 获取任务完整数据
        task_key = f"monitoring_tasks:{sample_task_id}"
        task_data = await redis_client.hgetall(task_key)
        
        print(f"当前存储字段 ({len(task_data)}个):")
        
        # 分析每个字段
        field_analysis = {
            "basic_info": [],      # 基本信息字段
            "config_info": [],     # 配置信息字段  
            "schedule_info": [],   # 调度信息字段
            "stats_info": [],      # 统计信息字段
            "other_info": []       # 其他字段
        }
        
        for field, value in task_data.items():
            field_str = field.decode() if isinstance(field, bytes) else field
            value_str = value.decode() if isinstance(value, bytes) else value
            
            # 分类字段
            if field_str in ['id', 'name', 'description', 'platform', 'created_at', 'updated_at']:
                category = "basic_info"
            elif field_str in ['config', 'batch_size', 'max_retries', 'timeout', 'rate_limit']:
                category = "config_info"
            elif field_str in ['schedule', 'next_run', 'last_run', 'timezone']:
                category = "schedule_info"
            elif field_str in ['status', 'total_runs', 'success_count', 'failed_count', 'progress', 'is_running']:
                category = "stats_info"
            else:
                category = "other_info"
            
            field_analysis[category].append({
                "field": field_str,
                "value_preview": value_str[:100] + "..." if len(value_str) > 100 else value_str,
                "value_type": "JSON" if value_str.startswith('{') or value_str.startswith('[') else "String",
                "size": len(value_str)
            })
        
        # 显示分类结果
        for category, fields in field_analysis.items():
            if fields:
                print(f"\n{category.replace('_', ' ').title()} ({len(fields)}个字段):")
                for field_info in fields:
                    print(f"  - {field_info['field']}: {field_info['value_type']} ({field_info['size']}字节)")
                    print(f"    预览: {field_info['value_preview']}")
        
        # 分析URL关联
        url_key = f"monitoring_tasks:task_urls:{sample_task_id}"
        url_ids = await redis_client.smembers(url_key)
        print(f"\n🔗 URL关联: {len(url_ids)}个URL")
        
        # 分析设计结构的适用性
        print(f"\n🎯 设计结构适用性分析:")
        print("-" * 40)
        
        print("✅ 分层存储的优势:")
        print("  - basic: 基本信息相对稳定，查询频繁")
        print("  - config: 配置信息修改频率中等")
        print("  - schedule: 调度信息独立管理")
        print("  - stats: 统计信息更新频繁，可独立优化")
        print("  - urls: URL关联独立管理，支持批量操作")
        
        print("\n✅ 索引系统的必要性:")
        print("  - 按状态查询: 当前只有active索引，需要完整状态索引")
        print("  - 按平台查询: 支持平台维度的任务管理")
        print("  - 按时间查询: 支持调度时间范围查询")
        
        print("\n✅ URL反向关联的价值:")
        print("  - 快速查找URL属于哪些任务")
        print("  - 支持URL级别的操作和统计")
        print("  - 避免全量扫描任务")
        
        # 检查当前数据是否适合迁移
        print(f"\n📊 迁移可行性评估:")
        print("-" * 40)
        
        total_size = sum(len(str(v)) for v in task_data.values())
        print(f"  单任务数据大小: {total_size}字节")
        print(f"  预估总数据大小: {total_size * len(task_ids)}字节")
        print(f"  迁移复杂度: 低 (数据量小，结构简单)")
        print(f"  停机时间: 预计 < 5分钟")
        print(f"  回滚难度: 低 (可保留原数据作为备份)")
        
        await redis_client.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


async def main():
    """主函数"""
    await analyze_task_data()


if __name__ == "__main__":
    asyncio.run(main())
