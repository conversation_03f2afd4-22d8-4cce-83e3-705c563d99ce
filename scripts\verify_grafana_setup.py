#!/usr/bin/env python3
"""
验证Grafana设置的脚本
检查Grafana服务状态和数据源配置
"""

import requests
import json

def verify_grafana():
    """验证Grafana设置"""
    
    grafana_url = "http://localhost:3001"
    auth = ("admin", "admin123")
    
    print("🔍 验证Grafana设置...")
    
    try:
        # 检查健康状态
        print("1. 检查Grafana健康状态...")
        response = requests.get(f"{grafana_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Grafana服务正常")
        else:
            print(f"   ❌ Grafana服务异常: {response.status_code}")
            return False
        
        # 检查数据源
        print("2. 检查数据源配置...")
        response = requests.get(f"{grafana_url}/api/datasources", auth=auth)
        if response.status_code == 200:
            datasources = response.json()
            print(f"   📊 找到 {len(datasources)} 个数据源")
            
            prometheus_found = False
            for ds in datasources:
                print(f"   - {ds['name']} ({ds['type']}): {ds['url']}")
                if ds['type'] == 'prometheus':
                    prometheus_found = True
            
            if prometheus_found:
                print("   ✅ Prometheus数据源已配置")
            else:
                print("   ⚠️  未找到Prometheus数据源")
        else:
            print(f"   ❌ 数据源检查失败: {response.status_code}")
        
        # 检查现有面板
        print("3. 检查现有面板...")
        response = requests.get(f"{grafana_url}/api/search", auth=auth)
        if response.status_code == 200:
            dashboards = response.json()
            print(f"   📋 找到 {len(dashboards)} 个面板")
            for db in dashboards:
                print(f"   - {db['title']} (ID: {db['id']})")
        else:
            print(f"   ❌ 面板检查失败: {response.status_code}")
        
        # 测试Prometheus连接
        print("4. 测试Prometheus查询...")
        query_data = {
            "query": "up",
            "time": "now"
        }
        
        # 通过Grafana代理查询Prometheus
        response = requests.post(
            f"{grafana_url}/api/datasources/proxy/1/api/v1/query",
            data=query_data,
            auth=auth
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                targets = len(result.get('data', {}).get('result', []))
                print(f"   ✅ Prometheus查询成功，监控目标: {targets}")
            else:
                print(f"   ⚠️  Prometheus查询返回错误: {result}")
        else:
            print(f"   ❌ Prometheus查询失败: {response.status_code}")
        
        print("\n📋 验证完成")
        print(f"\n🔗 Grafana访问信息:")
        print(f"   URL: {grafana_url}")
        print(f"   用户名: admin")
        print(f"   密码: admin123")
        
        print(f"\n📖 手动创建面板步骤:")
        print(f"   1. 访问 {grafana_url}")
        print(f"   2. 点击 '+' → 'Dashboard'")
        print(f"   3. 点击 'Add panel'")
        print(f"   4. 在查询框输入: up")
        print(f"   5. 设置面板标题和类型")
        print(f"   6. 点击 'Apply' 保存")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("MonIt Grafana设置验证工具")
    print("=" * 50)
    
    if verify_grafana():
        print("\n🎉 Grafana设置验证完成！")
    else:
        print("\n❌ Grafana设置存在问题")

if __name__ == "__main__":
    main()
