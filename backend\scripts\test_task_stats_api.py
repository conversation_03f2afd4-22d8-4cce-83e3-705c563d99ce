#!/usr/bin/env python3
"""
测试任务统计API

验证修复后的任务统计API是否正常工作
"""

import requests
import json

def test_task_stats_api():
    """测试任务统计API"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试任务统计API")
    print("=" * 50)
    
    try:
        # 首先获取任务列表，找到一个任务ID
        print("\n📋 步骤1: 获取任务列表")
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response.status_code}")
        
        if list_response.status_code != 200:
            print("  ❌ 获取任务列表失败")
            print(f"  错误: {list_response.text}")
            return
        
        list_data = list_response.json()
        tasks = list_data.get('data', [])
        
        if not tasks:
            print("  ℹ️ 没有任务可供测试")
            return
        
        task_id = tasks[0]['id']
        print(f"  ✅ 找到任务: {task_id}")
        
        # 测试任务统计API
        print(f"\n📋 步骤2: 获取任务统计")
        print(f"  任务ID: {task_id}")
        
        stats_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}/stats", timeout=10)
        print(f"  状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print("  ✅ 任务统计获取成功")
            
            # 检查响应结构
            data = stats_data.get('data', {})
            required_sections = ['url_stats', 'execution_stats', 'task_info']
            
            print(f"\n📋 步骤3: 验证响应结构")
            for section in required_sections:
                if section in data:
                    print(f"  ✅ {section}: {data[section]}")
                else:
                    print(f"  ❌ 缺少部分: {section}")
            
            # 显示完整的统计信息
            print(f"\n📋 完整统计信息:")
            print(json.dumps(stats_data, indent=2, ensure_ascii=False))
            
        else:
            print("  ❌ 任务统计获取失败")
            print(f"  错误: {stats_response.text}")
        
        print("\n🎉 任务统计API测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_task_stats_api()
