# MonIt 混合开发环境指南

## 🏗️ 架构概述

MonIt采用混合开发架构，结合Docker和本地开发的优势：

### 🐳 Docker服务 (基础设施)
- **TimescaleDB** (`localhost:5432`) - 时序数据库
- **Redis** (`localhost:6379`) - 缓存和消息队列
- **Prometheus** (`localhost:9090`) - 监控数据收集
- **Grafana** (`localhost:3001`) - 监控可视化
- **Elasticsearch** (`localhost:9200`) - 日志搜索引擎
- **Kibana** (`localhost:5601`) - 日志可视化

### 💻 本地开发服务
- **前端** (`localhost:3000`) - React开发服务器
- **后端** (`localhost:8001`) - FastAPI开发服务器

## 🚀 快速启动

### 1. 启动Docker基础设施
```bash
# 启动数据库和缓存服务
docker-compose -f docker-compose.dev.yml up -d timescaledb redis

# 启动监控服务 (可选)
docker-compose -f docker-compose.monitoring.yml up -d
```

### 2. 启动本地开发服务

#### 方式A: 使用启动脚本 (推荐)
```bash
# Windows
scripts\start_dev_hybrid.bat

# Linux/Mac
bash scripts/start_dev_hybrid.sh
```

#### 方式B: 手动启动
```bash
# 启动后端 (终端1)
conda activate monit
python simple_backend.py

# 启动前端 (终端2)
cd frontend
npm start
```

## 🔍 状态检查

```bash
# 检查所有服务状态
python scripts/check_dev_status.py

# 检查Docker服务
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 检查本地服务
curl http://localhost:3000  # 前端
curl http://localhost:8001/health  # 后端
```

## 📱 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:3000 | React开发服务器 |
| 后端API | http://localhost:8001 | FastAPI服务器 |
| API文档 | http://localhost:8001/docs | Swagger UI |
| Grafana | http://localhost:3001 | 监控面板 |
| Kibana | http://localhost:5601 | 日志分析 |
| Prometheus | http://localhost:9090 | 监控数据 |

## 🛠️ 开发工作流

### 前端开发
1. 修改 `frontend/src/` 下的文件
2. 浏览器自动热重载
3. 在浏览器开发者工具中调试

### 后端开发
1. 修改 `simple_backend.py` 或后端代码
2. 服务器自动重启 (如果使用 `--reload`)
3. 访问 http://localhost:8001/docs 测试API

### 数据库操作
```bash
# 连接数据库
psql -h localhost -p 5432 -U crawler_user -d monit_crawler_dev

# 连接Redis
redis-cli -h localhost -p 6379
```

## 🔧 常见问题

### Q: 端口冲突怎么办？
A: 修改对应服务的端口配置：
- 前端: `frontend/package.json` 中的启动脚本
- 后端: `simple_backend.py` 中的端口设置
- Docker: `docker-compose.dev.yml` 中的端口映射

### Q: conda环境问题？
A: 确保使用正确的环境：
```bash
conda activate monit
pip install -r backend/requirements.txt
```

### Q: 前端依赖问题？
A: 重新安装依赖：
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### Q: Docker服务启动失败？
A: 检查端口占用和Docker状态：
```bash
docker ps -a
docker logs <container_name>
```

## 📦 部署准备

当开发完成后，可以轻松切换到完整Docker环境：

```bash
# 构建并启动所有服务
docker-compose -f docker-compose.dev.yml up --build

# 或者生产环境
docker-compose -f docker-compose.yml up -d
```

## 🎯 优势

1. **快速开发**: 前后端热重载，修改即生效
2. **稳定基础设施**: Docker提供可靠的数据库和缓存
3. **易于调试**: 直接在IDE中设置断点
4. **环境隔离**: 基础设施与开发代码分离
5. **灵活切换**: 可随时切换到完整Docker环境
