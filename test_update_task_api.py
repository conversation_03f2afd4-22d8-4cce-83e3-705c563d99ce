#!/usr/bin/env python3
"""
测试更新任务API
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_update_task_api():
    """测试更新任务API"""
    
    async with aiohttp.ClientSession() as session:
        print("✏️ 测试更新任务API...")
        
        # 1. 获取任务列表，找到一个任务进行测试
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        task_status = tasks[0]['status']
                        is_running = tasks[0].get('is_running', False)
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                        print(f"   当前状态: {task_status}, 运行中: {is_running}")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 获取任务详情，查看当前配置
        print(f"\n2. 获取任务详情 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    task_detail = data.get('data', {})
                    print(f"✅ 当前任务配置:")
                    print(f"   名称: {task_detail.get('name')}")
                    print(f"   描述: {task_detail.get('description')}")
                    print(f"   调度类型: {task_detail.get('schedule', {}).get('type')}")
                    print(f"   执行时间: {task_detail.get('schedule', {}).get('time')}")
                    print(f"   平台: {task_detail.get('config', {}).get('platform')}")
                    print(f"   优先级: {task_detail.get('config', {}).get('priority')}")
                else:
                    print(f"❌ 获取任务详情失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务详情异常: {e}")
            return
        
        # 3. 测试更新任务基本信息
        print(f"\n3. 测试更新任务基本信息...")
        update_data = {
            "name": f"{task_name} (已编辑)",
            "description": "这是通过API编辑的任务描述"
        }
        
        try:
            async with session.put(
                f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}",
                json=update_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    updated_task = data.get('data', {})
                    print(f"✅ 基本信息更新成功:")
                    print(f"   新名称: {updated_task.get('name')}")
                    print(f"   新描述: {updated_task.get('description')}")
                else:
                    error_data = await response.json()
                    print(f"❌ 基本信息更新失败: {response.status} - {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 基本信息更新异常: {e}")
        
        # 4. 测试更新调度配置（如果任务未运行）
        if not is_running:
            print(f"\n4. 测试更新调度配置...")
            schedule_update_data = {
                "schedule": {
                    "type": "daily",
                    "time": "15:30",
                    "timezone": "Asia/Shanghai"
                }
            }
            
            try:
                async with session.put(
                    f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}",
                    json=schedule_update_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        updated_task = data.get('data', {})
                        schedule = updated_task.get('schedule', {})
                        print(f"✅ 调度配置更新成功:")
                        print(f"   类型: {schedule.get('type')}")
                        print(f"   时间: {schedule.get('time')}")
                        print(f"   时区: {schedule.get('timezone')}")
                    else:
                        error_data = await response.json()
                        print(f"❌ 调度配置更新失败: {response.status} - {error_data.get('detail')}")
            except Exception as e:
                print(f"❌ 调度配置更新异常: {e}")
        else:
            print(f"\n4. 跳过调度配置更新（任务正在运行）")
        
        # 5. 测试更新任务配置（如果任务未运行）
        if not is_running:
            print(f"\n5. 测试更新任务配置...")
            config_update_data = {
                "config": {
                    "platform": "taobao",
                    "priority": "high",
                    "retry_count": 5,
                    "timeout": 60,
                    "batch_size": 20
                }
            }
            
            try:
                async with session.put(
                    f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}",
                    json=config_update_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        updated_task = data.get('data', {})
                        config = updated_task.get('config', {})
                        print(f"✅ 任务配置更新成功:")
                        print(f"   平台: {config.get('platform')}")
                        print(f"   优先级: {config.get('priority')}")
                        print(f"   重试次数: {config.get('retry_count')}")
                        print(f"   超时时间: {config.get('timeout')}")
                        print(f"   批次大小: {config.get('batch_size')}")
                    else:
                        error_data = await response.json()
                        print(f"❌ 任务配置更新失败: {response.status} - {error_data.get('detail')}")
            except Exception as e:
                print(f"❌ 任务配置更新异常: {e}")
        else:
            print(f"\n5. 跳过任务配置更新（任务正在运行）")
        
        # 6. 验证更新结果
        print(f"\n6. 验证更新结果...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    task_detail = data.get('data', {})
                    print(f"✅ 验证成功，最终任务配置:")
                    print(f"   名称: {task_detail.get('name')}")
                    print(f"   描述: {task_detail.get('description')}")
                    print(f"   调度类型: {task_detail.get('schedule', {}).get('type')}")
                    print(f"   执行时间: {task_detail.get('schedule', {}).get('time')}")
                    print(f"   平台: {task_detail.get('config', {}).get('platform')}")
                    print(f"   优先级: {task_detail.get('config', {}).get('priority')}")
                    print(f"   更新时间: {task_detail.get('updated_at')}")
                else:
                    print(f"❌ 验证失败: {response.status}")
        except Exception as e:
            print(f"❌ 验证异常: {e}")
        
        print("\n🎉 更新任务API测试完成!")
        print("\n📋 测试总结:")
        print("   ✅ 基本信息更新 - 已测试")
        print("   ✅ 调度配置更新 - 已测试（如果任务未运行）")
        print("   ✅ 任务配置更新 - 已测试（如果任务未运行）")
        print("   ✅ 运行中任务限制 - 已验证")

if __name__ == "__main__":
    asyncio.run(test_update_task_api())
