"""
爬虫引擎核心模块

包含爬虫引擎的核心接口、基类和实现。
"""

from .interfaces import (
    ICrawlerEngine,
    ITaskScheduler,
    TaskStatus
)

from .base import (
    BaseCrawlerEngine,
    BaseDataExtractor,
    BaseTaskManager
)

from .crawler_engine import CrawlerEngine

__all__ = [
    # Interfaces
    'ICrawlerEngine',
    'IDataExtractor',
    'ITaskScheduler',
    'IBrowserManager',
    'ISessionManager',
    'IAntiDetectionManager',
    'IProxyManager',
    'IErrorHandler',
    'IConfigManager',
    'TaskStatus',
    
    # Base classes
    'BaseCrawlerEngine',
    'BaseDataExtractor',
    'BaseTaskManager',
    
    # Main implementation
    'CrawlerEngine'
] 