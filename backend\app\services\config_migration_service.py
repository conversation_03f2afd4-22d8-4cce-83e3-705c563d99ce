"""
配置迁移服务
用于在传统爬虫配置和新架构配置之间进行数据转换
"""

from typing import Dict, Any, Optional
from datetime import datetime
import uuid
import logging

from ..schemas.crawler_config_new import CrawlerConfig, CrawlerConfigCreate
from ..schemas.crawler_instance_config import CrawlerInstanceConfig, CrawlerInstanceConfigCreate
from ..schemas.backend_config import BackendConfig, BackendConfigCreate
from ..api.v1.crawler_config import CrawlerFullConfig

logger = logging.getLogger(__name__)


class ConfigMigrationService:
    """配置迁移服务"""
    
    @staticmethod
    def convert_legacy_to_new_config(legacy_config: CrawlerFullConfig) -> CrawlerConfigCreate:
        """
        将传统爬虫配置转换为新架构爬取配置
        
        Args:
            legacy_config: 传统的完整爬虫配置
            
        Returns:
            新架构的爬取配置创建数据
        """
        try:
            return CrawlerConfigCreate(
                config_name=f"迁移配置_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                description="从传统配置迁移而来",
                browser=legacy_config.browser,
                crawler=legacy_config.crawler,
                llm=legacy_config.llm,
                schema_extraction=legacy_config.schema_extraction,
                content_processing=legacy_config.content_processing,
                link_filtering=legacy_config.link_filtering,
                monitor=legacy_config.monitor
            )
        except Exception as e:
            logger.error(f"Failed to convert legacy config to new config: {e}")
            raise ValueError(f"配置转换失败: {str(e)}")
    
    @staticmethod
    def convert_legacy_to_instance_config(legacy_config: CrawlerFullConfig) -> CrawlerInstanceConfigCreate:
        """
        将传统爬虫配置转换为爬虫实例配置
        
        Args:
            legacy_config: 传统的完整爬虫配置
            
        Returns:
            爬虫实例配置创建数据
        """
        try:
            return CrawlerInstanceConfigCreate(
                config_name=f"实例配置_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                description="从传统配置迁移的实例配置",
                api_endpoint=legacy_config.api.base_url,
                timeout=legacy_config.api.timeout,
                max_retries=legacy_config.api.max_retries,
                auth_config={
                    "auth_type": "api_key",
                    "api_key": legacy_config.llm.api_key if legacy_config.llm.api_key else ""
                },
                browser=legacy_config.browser,
                crawler=legacy_config.crawler,
                llm=legacy_config.llm,
                schema_extraction=legacy_config.schema_extraction,
                content_processing=legacy_config.content_processing,
                link_filtering=legacy_config.link_filtering,
                scheduler=legacy_config.scheduler,
                monitor=legacy_config.monitor,
                max_concurrent=2,  # 默认并发数
                weight=1,  # 默认权重
                priority=1  # 默认优先级
            )
        except Exception as e:
            logger.error(f"Failed to convert legacy config to instance config: {e}")
            raise ValueError(f"实例配置转换失败: {str(e)}")
    
    @staticmethod
    def convert_legacy_to_backend_config(legacy_config: CrawlerFullConfig) -> BackendConfigCreate:
        """
        将传统爬虫配置转换为后端配置
        
        Args:
            legacy_config: 传统的完整爬虫配置
            
        Returns:
            后端配置创建数据
        """
        try:
            return BackendConfigCreate(
                backend_name=f"后端配置_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                description="从传统配置迁移的后端配置",
                api_endpoint=legacy_config.api.base_url,
                timeout=legacy_config.api.timeout,
                max_retries=legacy_config.api.max_retries,
                auth_config={
                    "auth_type": "api_key",
                    "api_key": legacy_config.llm.api_key if legacy_config.llm.api_key else ""
                }
            )
        except Exception as e:
            logger.error(f"Failed to convert legacy config to backend config: {e}")
            raise ValueError(f"后端配置转换失败: {str(e)}")
    
    @staticmethod
    def create_compatibility_mapping(legacy_config: CrawlerFullConfig) -> Dict[str, Any]:
        """
        创建兼容性映射，用于前端平滑过渡
        
        Args:
            legacy_config: 传统的完整爬虫配置
            
        Returns:
            兼容性映射字典
        """
        return {
            "legacy_format": True,
            "migration_timestamp": datetime.now().isoformat(),
            "api_endpoint": legacy_config.api.base_url,
            "timeout": legacy_config.api.timeout,
            "max_retries": legacy_config.api.max_retries,
            "llm_model": legacy_config.llm.model,
            "has_api_key": bool(legacy_config.llm.api_key),
            "browser_headless": legacy_config.browser.headless,
            "extraction_enabled": legacy_config.schema_extraction.enabled,
            "schema_type": legacy_config.schema_extraction.schema_type,
            "monitor_mode": legacy_config.monitor.display_mode
        }
    
    @staticmethod
    def validate_migration_compatibility(legacy_config: CrawlerFullConfig) -> Dict[str, Any]:
        """
        验证迁移兼容性
        
        Args:
            legacy_config: 传统的完整爬虫配置
            
        Returns:
            验证结果
        """
        issues = []
        warnings = []
        
        # 检查必需字段
        if not legacy_config.api.base_url:
            issues.append("API基础URL不能为空")
        
        if not legacy_config.llm.api_key:
            warnings.append("LLM API密钥为空，可能影响功能")
        
        if legacy_config.api.timeout < 1000:
            warnings.append("API超时时间过短，建议至少1000ms")
        
        # 检查配置合理性
        if legacy_config.scheduler.semaphore_count > 10:
            warnings.append("信号量计数过高，可能影响性能")
        
        return {
            "compatible": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "migration_recommended": len(issues) == 0 and len(warnings) <= 2
        }


# 全局迁移服务实例
config_migration_service = ConfigMigrationService()
