# Grafana监控面板设置指南

## 🎯 概述

Task 06监控系统已经包含了完整的Grafana面板设计，但需要手动创建面板。本指南将帮助您在Grafana中创建MonIt系统的监控面板。

## 🔗 访问Grafana

- **URL**: http://localhost:3001
- **用户名**: `admin`
- **密码**: `admin123`

## 📊 推荐面板配置

### 1. 系统概览面板 (System Overview)

#### 面板1: 服务状态
- **类型**: Stat
- **查询**: `up`
- **图例**: `{{job}}`
- **阈值**: 0=红色(DOWN), 1=绿色(UP)

#### 面板2: CPU使用率
- **类型**: Time series
- **查询**: `100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)`
- **单位**: percent
- **阈值**: 0-70%=绿色, 70-90%=黄色, 90%+=红色

#### 面板3: 内存使用率
- **类型**: Time series
- **查询**: `(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100`
- **单位**: percent
- **阈值**: 0-80%=绿色, 80-95%=黄色, 95%+=红色

#### 面板4: 系统负载
- **类型**: Time series
- **查询**: 
  - `node_load1` (1分钟负载)
  - `node_load5` (5分钟负载)
  - `node_load15` (15分钟负载)

### 2. 爬虫性能面板 (Crawler Performance)

#### 面板1: 任务处理速率
- **类型**: Time series
- **查询**: `rate(monit_crawler_tasks_total[5m])`
- **图例**: `{{status}} tasks/sec`

#### 面板2: 任务执行时间
- **类型**: Time series
- **查询**: 
  - `histogram_quantile(0.50, rate(monit_crawler_task_duration_seconds_bucket[5m]))`
  - `histogram_quantile(0.95, rate(monit_crawler_task_duration_seconds_bucket[5m]))`

#### 面板3: URL处理统计
- **类型**: Time series
- **查询**: `rate(monit_crawler_urls_processed_total[5m])`
- **图例**: `{{status}} URLs/sec`

### 3. API性能面板 (API Performance)

#### 面板1: HTTP请求速率
- **类型**: Time series
- **查询**: `rate(monit_http_requests_total[5m])`
- **图例**: `{{method}} {{endpoint}} ({{status}})`

#### 面板2: 响应时间
- **类型**: Time series
- **查询**: 
  - `histogram_quantile(0.50, rate(monit_http_request_duration_seconds_bucket[5m]))`
  - `histogram_quantile(0.95, rate(monit_http_request_duration_seconds_bucket[5m]))`

#### 面板3: 错误率
- **类型**: Stat
- **查询**: `rate(monit_http_requests_total{status=~"5.."}[5m]) / rate(monit_http_requests_total[5m]) * 100`
- **单位**: percent
- **阈值**: 0-1%=绿色, 1-5%=黄色, 5%+=红色

### 4. 数据库性能面板 (Database Performance)

#### 面板1: 数据库操作
- **类型**: Time series
- **查询**: `rate(monit_db_operations_total[5m])`
- **图例**: `{{operation}} on {{table}}`

#### 面板2: 数据库响应时间
- **类型**: Time series
- **查询**: `histogram_quantile(0.95, rate(monit_db_operation_duration_seconds_bucket[5m]))`

#### 面板3: 活跃连接数
- **类型**: Time series
- **查询**: `monit_db_connections_active`

## 🛠️ 手动创建步骤

### 步骤1: 创建文件夹
1. 登录Grafana (http://localhost:3001)
2. 点击左侧菜单 "+" → "Folder"
3. 输入文件夹名称: "MonIt Dashboards"
4. 点击 "Create"

### 步骤2: 创建面板
1. 点击 "+" → "Dashboard"
2. 点击 "Add panel"
3. 在查询编辑器中输入Prometheus查询
4. 设置面板标题和类型
5. 配置阈值和颜色
6. 点击 "Apply"

### 步骤3: 保存面板
1. 点击右上角保存图标
2. 输入面板名称
3. 选择文件夹 "MonIt Dashboards"
4. 点击 "Save"

## 📋 当前可用指标

由于API服务尚未开发完成，目前只有系统级指标可用：

### ✅ 可用指标
- `up` - 服务状态
- `node_cpu_seconds_total` - CPU使用情况
- `node_memory_*` - 内存使用情况
- `node_load*` - 系统负载
- `node_network_*` - 网络流量
- `node_disk_*` - 磁盘I/O

### 📋 待开发指标 (Task 04完成后)
- `monit_http_requests_total` - HTTP请求统计
- `monit_crawler_tasks_total` - 爬虫任务统计
- `monit_db_operations_total` - 数据库操作统计
- `monit_task_queue_size` - 任务队列大小

## 🎨 面板设计建议

### 布局建议
- **第一行**: 服务状态概览 (全宽)
- **第二行**: 核心指标 (CPU、内存、负载，各占1/3宽度)
- **第三行**: 详细指标 (网络、磁盘I/O，各占1/2宽度)

### 颜色方案
- **绿色**: 正常状态 (0-70%)
- **黄色**: 警告状态 (70-90%)
- **红色**: 危险状态 (90%+)
- **蓝色**: 信息状态

### 刷新设置
- **刷新间隔**: 30秒
- **时间范围**: 最近1小时
- **自动刷新**: 启用

## 🔧 故障排查

### 问题1: 没有数据显示
- 检查Prometheus是否正常运行 (http://localhost:9090)
- 确认数据源配置正确
- 验证查询语法

### 问题2: 面板显示错误
- 检查查询语法是否正确
- 确认指标名称拼写
- 查看Prometheus目标状态

### 问题3: 性能问题
- 减少查询时间范围
- 降低刷新频率
- 简化复杂查询

## 📚 参考资源

- [Prometheus查询语法](https://prometheus.io/docs/prometheus/latest/querying/basics/)
- [Grafana面板配置](https://grafana.com/docs/grafana/latest/panels/)
- [Node Exporter指标](https://github.com/prometheus/node_exporter)

---

**注意**: 当Task 04 API服务开发完成后，将会有更多业务指标可用，届时可以创建更丰富的监控面板。
