# MonIt监控系统指南 (2025年7月重构版)

> **📅 最后更新**：2025年7月13日
> **🔄 重构状态**：已完成内存存储重构，监控系统优化
> **📊 测试状态**：✅ 全面验证通过

## 🚀 系统监控快速启动

### 1. 系统健康检查 (推荐)
```bash
# 激活环境
conda activate monit

# 运行完整健康检查
cd backend
python scripts/health_check_post_refactor.py

# 测试Redis连接
python scripts/test_redis_connection.py

# 验证数据迁移状态
python scripts/migrate_memory_to_redis.py
```

### 2. 启动监控面板 (可选)
```bash
# 启动Grafana和Prometheus
docker-compose -f docker-compose.monitoring.yml up -d

# 验证监控服务
curl http://localhost:9090/api/v1/status/config
curl http://localhost:3001/api/health
```

### 3. 访问监控界面
- **系统健康报告**: 通过健康检查脚本生成
- **Grafana仪表板**: http://localhost:3001 (用户名: admin, 密码: admin123)
- **Prometheus指标**: http://localhost:9090
- **Redis监控**: 通过健康检查脚本

## 📊 当前监控功能

### ✅ 已启用
- 系统资源监控 (CPU、内存、网络)
- 服务健康状态监控
- Grafana可视化面板 (需手动创建)
- 基础告警规则
- 8个可用监控指标

### 📋 Grafana面板状态
- ✅ Grafana服务运行正常
- ✅ Prometheus数据源已配置
- ✅ 8个监控查询已验证可用
- 📋 面板需要手动创建 (5分钟完成)

### 📋 待集成 (API开发完成后)
- 应用性能指标
- 业务监控面板
- 完整日志系统
- 自定义告警通知

## 🔧 管理命令

```bash
# 启动监控服务
docker-compose -f docker-compose.monitoring-basic.yml up -d

# 停止监控服务
docker-compose -f docker-compose.monitoring-basic.yml down

# 查看服务状态
docker-compose -f docker-compose.monitoring-basic.yml ps

# 查看服务日志
docker-compose -f docker-compose.monitoring-basic.yml logs -f [service_name]
```

## 📁 重要文件

- `docker-compose.monitoring-basic.yml` - 基础监控服务配置
- `monitoring/prometheus/prometheus-basic.yml` - Prometheus配置
- `monitoring/grafana/provisioning/` - Grafana配置
- `docs/monitoring_setup_guide.md` - 详细部署指南
- `docs/quick_grafana_setup.md` - 5分钟快速创建面板指南 ⭐
- `scripts/test_prometheus_metrics.py` - 验证可用指标

## 🆘 故障排查

### 服务无法启动
1. 检查Docker是否运行
2. 确认端口未被占用 (9090, 3001, 9100)
3. 查看容器日志

### Grafana无法访问
1. 等待服务完全启动 (约30秒)
2. 检查防火墙设置
3. 使用正确的登录信息 (admin/admin123)

### 监控数据缺失
1. 检查Prometheus配置
2. 验证Node Exporter运行状态
3. 确认网络连接正常

## 🚀 创建监控面板

### 快速创建 (推荐)
```bash
# 验证可用指标
python scripts/test_prometheus_metrics.py

# 按照5分钟指南手动创建
# 参考: docs/quick_grafana_setup.md
```

### 可用监控指标
- ✅ 服务状态 (up)
- ✅ CPU使用率 (约5.4%)
- ✅ 内存使用率 (约9.2%)
- ✅ 系统负载 (1m/5m/15m)
- ✅ 网络流量 (接收/发送)

## 📞 技术支持

详细文档请参考:
- [5分钟快速创建面板](docs/quick_grafana_setup.md) ⭐
- [详细手动设置指南](docs/grafana_manual_setup_guide.md)
- [监控系统部署指南](docs/monitoring_setup_guide.md)
- [Task 06完成报告](docs/task06_monitoring_completion_report.md)
