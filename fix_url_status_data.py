#!/usr/bin/env python3
"""
修复URL状态数据

将Redis中的旧状态格式（UrlStatus.DISABLED）转换为新格式（disabled）
"""

import redis
import json
from datetime import datetime, timezone

def connect_redis():
    """连接Redis"""
    return redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)

def fix_url_status_data():
    """修复URL状态数据"""
    print("🔧 开始修复URL状态数据")
    
    r = connect_redis()
    
    # 获取所有URL ID
    all_url_ids = r.smembers('url_pool:all_ids')
    print(f"📊 找到 {len(all_url_ids)} 个URL")
    
    fixed_count = 0
    error_count = 0
    
    # 状态映射
    status_mapping = {
        'UrlStatus.ACTIVE': 'active',
        'UrlStatus.DISABLED': 'disabled',
        'UrlStatus.DELETED': 'deleted'
    }
    
    for url_id in all_url_ids:
        try:
            # 获取URL数据
            url_key = f'url_pool:items:{url_id}'
            url_data = r.hgetall(url_key)
            
            if not url_data:
                print(f"⚠️ URL数据不存在: {url_id}")
                continue
            
            current_status = url_data.get('status')
            if not current_status:
                print(f"⚠️ URL状态为空: {url_id}")
                continue
            
            # 检查是否需要修复
            if current_status in status_mapping:
                new_status = status_mapping[current_status]
                print(f"🔄 修复URL状态: {url_id}, {current_status} -> {new_status}")
                
                # 更新URL状态
                r.hset(url_key, 'status', new_status)
                r.hset(url_key, 'updated_at', datetime.now(timezone.utc).isoformat())
                
                # 更新状态索引
                # 从旧状态索引中移除
                old_status_key = f'url_pool:status:{current_status}'
                r.srem(old_status_key, url_id)
                
                # 添加到新状态索引
                new_status_key = f'url_pool:status:{new_status}'
                r.sadd(new_status_key, url_id)
                
                fixed_count += 1
            else:
                # 检查状态是否有效
                if current_status not in ['active', 'disabled', 'deleted']:
                    print(f"⚠️ 未知状态: {url_id}, status={current_status}")
                    error_count += 1
        
        except Exception as e:
            print(f"❌ 处理URL失败: {url_id}, 错误: {e}")
            error_count += 1
    
    print(f"\n📊 修复完成:")
    print(f"   - 修复数量: {fixed_count}")
    print(f"   - 错误数量: {error_count}")
    print(f"   - 总数量: {len(all_url_ids)}")
    
    # 清理空的状态索引
    print(f"\n🧹 清理空的状态索引")
    old_status_keys = [
        'url_pool:status:UrlStatus.ACTIVE',
        'url_pool:status:UrlStatus.DISABLED', 
        'url_pool:status:UrlStatus.DELETED'
    ]
    
    for key in old_status_keys:
        count = r.scard(key)
        if count > 0:
            print(f"⚠️ 旧状态索引仍有数据: {key}, count={count}")
        else:
            r.delete(key)
            print(f"✅ 删除空的状态索引: {key}")

def verify_status_data():
    """验证状态数据"""
    print(f"\n🔍 验证状态数据")
    
    r = connect_redis()
    
    # 检查状态分布
    status_keys = [
        'url_pool:status:active',
        'url_pool:status:disabled',
        'url_pool:status:deleted'
    ]
    
    total_in_status = 0
    for key in status_keys:
        count = r.scard(key)
        status = key.split(':')[-1]
        print(f"   - {status}: {count} 个URL")
        total_in_status += count
    
    # 检查总数
    total_urls = r.scard('url_pool:all_ids')
    print(f"   - 总URL数: {total_urls}")
    
    if total_in_status == total_urls:
        print("✅ 状态数据一致")
    else:
        print(f"❌ 状态数据不一致，差异: {abs(total_in_status - total_urls)}")
    
    # 检查是否还有旧格式的状态
    sample_urls = list(r.smembers('url_pool:all_ids'))[:5]
    print(f"\n🔍 检查样本URL状态格式:")
    
    for url_id in sample_urls:
        url_key = f'url_pool:items:{url_id}'
        status = r.hget(url_key, 'status')
        url = r.hget(url_key, 'url')
        if url:
            url_short = url[:50] + "..." if len(url) > 50 else url
        else:
            url_short = "N/A"
        print(f"   - {url_short}: {status}")

def main():
    """主函数"""
    print("🚀 URL状态数据修复工具")
    print("=" * 50)
    
    try:
        # 修复状态数据
        fix_url_status_data()
        
        # 验证修复结果
        verify_status_data()
        
        print(f"\n" + "=" * 50)
        print("✅ 修复完成！现在可以重启后端服务")
        
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")

if __name__ == "__main__":
    main()
