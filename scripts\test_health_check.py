#!/usr/bin/env python3
"""
测试健康检查功能
验证Docker环境中的网络连接
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime


async def test_health_check():
    """测试健康检查功能"""
    print("🧪 测试健康检查功能...")
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. 创建使用正确Docker地址的后端配置
        config_data = {
            "backend_name": "健康检查测试后端",
            "description": "用于测试健康检查的后端配置",
            "api_endpoint": "http://host.docker.internal:11235",
            "timeout": 30000,
            "max_retries": 3,
            "auth_config": {
                "auth_type": "none"
            },
            "max_concurrent": 2,
            "weight": 1,
            "priority": 1
        }
        
        print("1. 创建后端配置...")
        async with session.post(f"{base_url}/api/v1/backend-configs/", json=config_data) as response:
            if response.status == 200:
                result = await response.json()
                backend_id = result["backend_id"]
                print(f"  ✅ 创建成功: {backend_id[:8]}...")
                print(f"  📊 初始状态: {result['status']}")
                print(f"  📊 初始健康评分: {result['health_score']}")
            else:
                error = await response.text()
                print(f"  ❌ 创建失败: {error}")
                return False
        
        # 2. 等待健康检查完成
        print("2. 等待健康检查完成...")
        await asyncio.sleep(3)
        
        # 3. 获取更新后的配置状态
        print("3. 检查健康检查结果...")
        async with session.get(f"{base_url}/api/v1/backend-configs/{backend_id}") as response:
            if response.status == 200:
                result = await response.json()
                print(f"  📊 最终状态: {result['status']}")
                print(f"  📊 最终健康评分: {result['health_score']}")
                print(f"  📊 最后健康检查: {result.get('last_health_check', 'None')}")
                
                if result['status'] == 'active' and result['health_score'] > 0.5:
                    print("  ✅ 健康检查成功！")
                    success = True
                else:
                    print("  ❌ 健康检查失败")
                    success = False
            else:
                print(f"  ❌ 获取配置失败: {response.status}")
                success = False
        
        # 4. 手动触发健康检查
        print("4. 手动触发健康检查...")
        async with session.post(f"{base_url}/api/v1/backend-configs/{backend_id}/health-check") as response:
            if response.status == 200:
                health_result = await response.json()
                print(f"  ✅ 手动健康检查成功")
                print(f"  📊 健康状态: {health_result.get('is_healthy')}")
                print(f"  📊 响应时间: {health_result.get('response_time')}ms")
                print(f"  📊 错误信息: {health_result.get('error_message', 'None')}")
            else:
                error = await response.text()
                print(f"  ❌ 手动健康检查失败: {error}")
        
        # 5. 清理
        print("5. 清理测试资源...")
        async with session.delete(f"{base_url}/api/v1/backend-configs/{backend_id}") as response:
            if response.status == 200:
                print("  ✅ 清理成功")
            else:
                print("  ⚠️ 清理失败")
        
        return success


async def test_network_connectivity():
    """测试网络连接性"""
    print("🌐 测试网络连接性...")
    
    # 测试地址列表
    test_urls = [
        "http://localhost:11235/health",
        "http://host.docker.internal:11235/health",
        "http://127.0.0.1:11235/health"
    ]
    
    async with aiohttp.ClientSession() as session:
        for url in test_urls:
            try:
                print(f"  测试: {url}")
                async with session.get(url, timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"    ✅ 成功 - 状态: {data.get('status')}")
                    else:
                        print(f"    ❌ HTTP {response.status}")
            except Exception as e:
                print(f"    ❌ 连接失败: {e}")


async def main():
    """主函数"""
    print("🚀 开始健康检查测试...")
    print("=" * 50)
    
    # 1. 测试网络连接性
    await test_network_connectivity()
    
    print("\n" + "=" * 50)
    
    # 2. 测试健康检查功能
    success = await test_health_check()
    
    print("\n" + "=" * 50)
    print("📊 健康检查测试报告")
    print("=" * 50)
    
    if success:
        print("🎉 健康检查功能正常！")
        print("✅ Docker网络连接正常")
        print("✅ 后端配置健康检查正常")
        return 0
    else:
        print("❌ 健康检查功能异常")
        print("⚠️ 需要检查Docker网络配置")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
