#!/usr/bin/env python3
"""
简单的后端服务器，用于测试Excel上传功能
"""
import sys
import os
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import pandas as pd
import io
from typing import Dict, Any, List, Optional
import logging
import json
import asyncio
from datetime import datetime
from pydantic import BaseModel, Field
import redis
import uuid
# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from sqlalchemy import create_engine, text, select, and_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

# 尝试导入数据库模型（如果可用）
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
    from backend.app.models.task import CrawlTask
    from backend.app.models.snapshot import ProductSnapshot
    from backend.app.models.product import Product
    MODELS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Database models not available: {e}")
    MODELS_AVAILABLE = False

# 数据库配置
DATABASE_URL = "postgresql+asyncpg://monit_user:monit_password@localhost:5432/monit_db"

# 创建异步数据库引擎
async_engine = create_async_engine(DATABASE_URL, echo=False)
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 数据库依赖
async def get_async_session():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# Pydantic模型
class TaskSubmissionRequest(BaseModel):
    urls: List[str] = Field(..., description="URL列表", min_items=1)
    platform: str = Field(default="mercadolibre", description="平台类型")
    priority: str = Field(default="normal", description="任务优先级")
    options: Dict[str, Any] = Field(default_factory=dict, description="爬取选项")

class TaskSubmissionResponse(BaseModel):
    success: bool
    submission_id: str
    message: str
    batches_created: int
    total_urls: int

class TaskDetailResponse(BaseModel):
    success: bool
    task: Dict[str, Any]
    batches: List[Dict[str, Any]]

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)

        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)

# 创建连接管理器实例
manager = ConnectionManager()

# 内存存储（用于测试）
task_storage = []

# Redis连接（可选）
redis_client = None
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    redis_client.ping()
    logger.info("✅ Redis连接成功")
except Exception as e:
    logger.warning(f"⚠️ Redis连接失败，将仅使用内存存储: {e}")
    redis_client = None

# 创建FastAPI应用
app = FastAPI(
    title="MonIt Simple Backend",
    description="简单的后端服务器用于测试Excel上传",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "MonIt Simple Backend is running!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "message": "Simple backend is running",
        "version": "1.0.0",
        "websocket_connections": len(manager.active_connections)
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        # 发送欢迎消息
        welcome_message = {
            "type": "system_message",
            "data": {
                "message": "WebSocket连接已建立",
                "timestamp": datetime.now().isoformat()
            },
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(welcome_message), websocket)

        # 保持连接并处理消息
        while True:
            try:
                # 等待客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                # 处理ping消息
                if message.get("type") == "ping":
                    pong_message = {
                        "type": "pong",
                        "data": {"message": "pong"},
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(pong_message), websocket)

                # 回显其他消息
                else:
                    echo_message = {
                        "type": "echo",
                        "data": message,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(echo_message), websocket)

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                error_message = {
                    "type": "error",
                    "data": {"message": "Invalid JSON format"},
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(error_message), websocket)
            except Exception as e:
                logger.error(f"WebSocket处理消息时出错: {e}")
                break

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket连接出错: {e}")
    finally:
        manager.disconnect(websocket)

@app.post("/api/v1/tasks/submit", response_model=TaskSubmissionResponse)
async def submit_task(request: TaskSubmissionRequest):
    """
    提交任务到TaskManager
    """
    try:
        logger.info(f"Received task submission: {len(request.urls)} URLs, platform: {request.platform}, priority: {request.priority}")
        logger.info(f"First 3 URLs: {request.urls[:3]}")

        # 验证URL
        if not request.urls:
            raise HTTPException(status_code=400, detail="URL列表不能为空")

        # 模拟任务分组（实际应该调用真正的TaskManager）
        batch_size = request.options.get('batch_size', 10)
        total_batches = (len(request.urls) + batch_size - 1) // batch_size

        # 生成提交ID
        submission_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(request.urls)}"

        # 保存任务数据到内存存储（关键修复！）
        task_data = {
            "id": submission_id,
            "name": f"提交任务 - {len(request.urls)}个链接",
            "urls": request.urls,  # 保存真实的URL列表
            "platform": request.platform,
            "priority": request.priority,
            "status": "pending",  # 修复：使用标准的pending状态而不是submitted
            "total_urls": len(request.urls),
            "batches_created": total_batches,
            "batch_size": batch_size,
            "options": request.options or {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # 检查是否已存在，如果存在则更新，否则添加
        existing_task_index = None
        for i, task in enumerate(task_storage):
            if task["id"] == submission_id:
                existing_task_index = i
                break

        if existing_task_index is not None:
            task_storage[existing_task_index] = task_data
            logger.info(f"Updated existing task: {submission_id}")
        else:
            task_storage.append(task_data)
            logger.info(f"Added new task to storage: {submission_id}")

        # 模拟任务创建过程
        logger.info(f"Creating {total_batches} batches for {len(request.urls)} URLs")
        logger.info(f"First 3 URLs in task: {request.urls[:3]}")

        # 构建响应
        response = TaskSubmissionResponse(
            success=True,
            submission_id=submission_id,
            message=f"任务创建成功，共 {len(request.urls)} 个URL分为 {total_batches} 个批次",
            batches_created=total_batches,
            total_urls=len(request.urls)
        )

        logger.info(f"Task submitted successfully: {submission_id}, stored {len(task_storage)} tasks total")

        # 广播任务创建消息
        task_message = {
            "type": "task_submitted",
            "data": {
                "submission_id": submission_id,
                "total_urls": len(request.urls),
                "batches_created": total_batches,
                "platform": request.platform,
                "priority": request.priority,
                "status": "pending"  # 修复：使用标准的pending状态
            },
            "timestamp": datetime.now().isoformat()
        }
        await manager.broadcast(json.dumps(task_message))

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/tasks/{task_id}/detail", response_model=TaskDetailResponse)
async def get_task_detail(task_id: str):
    """
    获取任务详情，包括真实的批次和URL信息
    """
    try:
        logger.info(f"Getting task detail for task_id: {task_id}")

        # 如果有数据库模型，从数据库查询真实数据
        if MODELS_AVAILABLE:
            try:
                async with AsyncSessionLocal() as session:
                    # 查询任务信息
                    task_query = await session.execute(
                        select(CrawlTask).where(CrawlTask.id == int(task_id.split('_')[-1]) if task_id.startswith('task_') else int(task_id))
                    )
                    task = task_query.scalar_one_or_none()

                    if not task:
                        raise HTTPException(status_code=404, detail=f"Task {task_id} not found in database")

                    # 查询任务的产品快照
                    snapshots_query = await session.execute(
                        select(ProductSnapshot).where(ProductSnapshot.task_id == task.id)
                    )
                    snapshots = snapshots_query.scalars().all()

                    # 构建真实的任务信息
                    total_urls = len(snapshots) if snapshots else task.total_products or 0
                    batch_size = 10
                    total_batches = (total_urls + batch_size - 1) // batch_size if total_urls > 0 else 1

                    # 使用真实的任务数据
                    task_info = {
                        "id": task_id,
                        "name": task.name,
                        "status": task.status,
                        "platform": "mercadolibre",  # 从任务配置获取
                        "priority": "normal",
                        "progress": task.progress or 0,
                        "totalUrls": total_urls,
                        "completedUrls": task.success_count or 0,
                        "failedUrls": task.failed_count or 0,
                        "createdAt": task.created_at.isoformat() if task.created_at else datetime.now().isoformat(),
                        "updatedAt": task.updated_at.isoformat() if task.updated_at else datetime.now().isoformat()
                    }

                    # 构建真实的批次信息
                    batches = []
                    for i in range(total_batches):
                        batch_start = i * batch_size
                        batch_end = min(batch_start + batch_size, total_urls)
                        batch_snapshots = snapshots[batch_start:batch_end] if snapshots else []

                        # 计算批次状态
                        completed_count = sum(1 for s in batch_snapshots if s.success)
                        failed_count = sum(1 for s in batch_snapshots if not s.success)
                        pending_count = len(batch_snapshots) - completed_count - failed_count

                        if completed_count == len(batch_snapshots) and len(batch_snapshots) > 0:
                            status = "completed"
                            progress = 100
                        elif completed_count > 0 or failed_count > 0:
                            status = "running"
                            progress = int((completed_count / len(batch_snapshots)) * 100) if batch_snapshots else 0
                        else:
                            status = "pending"
                            progress = 0

                        # 构建批次URL列表
                        batch_urls = []
                        for snapshot in batch_snapshots:
                            url_status = "completed" if snapshot.success else "failed"
                            price = f"${snapshot.current_price}" if snapshot.current_price else None
                            last_check = snapshot.snapshot_time.strftime("%H:%M:%S") if snapshot.snapshot_time else None

                            batch_urls.append({
                                "url": snapshot.product_url,
                                "status": url_status,
                                "price": price,
                                "lastCheck": last_check
                            })

                        # 如果没有快照数据，生成占位符
                        if not batch_urls and i == 0:
                            for j in range(min(batch_size, total_urls)):
                                batch_urls.append({
                                    "url": f"https://mercadolibre.com/product{j+1}",
                                    "status": "pending",
                                    "price": None,
                                    "lastCheck": None
                                })

                        batches.append({
                            "id": f"batch-{i+1}",
                            "name": f"批次 {i+1}",
                            "status": status,
                            "progress": progress,
                            "totalUrls": len(batch_urls),
                            "completedUrls": completed_count,
                            "failedUrls": failed_count,
                            "startTime": task.started_at.strftime("%Y-%m-%d %H:%M:%S") if task.started_at else None,
                            "endTime": task.completed_at.strftime("%Y-%m-%d %H:%M:%S") if task.completed_at and status == "completed" else None,
                            "urls": batch_urls
                        })

                    response = TaskDetailResponse(
                        success=True,
                        task=task_info,
                        batches=batches
                    )

                    logger.info(f"Task detail retrieved from database: {task_id}, {len(batches)} batches")
                    return response

            except Exception as e:
                logger.error(f"Database query failed: {e}")
                # 降级到模拟数据

        # 降级处理：从内存存储获取数据
        task_data = None
        for task in task_storage:
            if task["id"] == task_id:
                task_data = task
                break

        if task_data:
            # 使用内存存储的真实数据
            urls = task_data.get("urls", [])
            total_urls = len(urls)
            batch_size = 10
            total_batches = (total_urls + batch_size - 1) // batch_size if total_urls > 0 else 1

            # 构建任务信息
            task_info = {
                "id": task_id,
                "name": task_data.get("name", f"Excel任务 - {total_urls}个链接"),
                "status": task_data.get("status", "running"),
                "platform": task_data.get("platform", "mercadolibre"),
                "priority": task_data.get("priority", "normal"),
                "progress": 35,  # 模拟进度
                "totalUrls": total_urls,
                "completedUrls": int(total_urls * 0.35),
                "failedUrls": int(total_urls * 0.05),
                "createdAt": task_data.get("created_at", datetime.now().isoformat()),
                "updatedAt": task_data.get("updated_at", datetime.now().isoformat())
            }

            # 构建真实的批次信息
            batches = []
            for i in range(total_batches):
                batch_start = i * batch_size
                batch_end = min(batch_start + batch_size, total_urls)
                batch_urls_data = urls[batch_start:batch_end]

                # 模拟不同批次的状态
                if i < total_batches // 3:
                    status = "completed"
                    progress = 100
                    completed = len(batch_urls_data)
                    failed = 0
                    start_time = f"2025-06-30 0{3+i}:00:00"
                    end_time = f"2025-06-30 0{3+i}:05:30"
                elif i < total_batches * 2 // 3:
                    status = "running"
                    progress = 60
                    completed = int(len(batch_urls_data) * 0.6)
                    failed = int(len(batch_urls_data) * 0.1)
                    start_time = f"2025-06-30 0{3+i}:00:00"
                    end_time = None
                else:
                    status = "pending"
                    progress = 0
                    completed = 0
                    failed = 0
                    start_time = None
                    end_time = None

                # 构建批次内的真实URL列表
                batch_urls = []
                for j, url in enumerate(batch_urls_data):
                    if j < completed:
                        url_status = "completed"
                        price = f"${20 + (j % 50)}.99"
                        last_check = f"0{3+i}:0{5+j}:{30-j}"
                    elif j < completed + failed:
                        url_status = "failed"
                        price = None
                        last_check = f"0{3+i}:0{5+j}:{30-j}"
                    elif status == "running":
                        url_status = "running"
                        price = None
                        last_check = None
                    else:
                        url_status = "pending"
                        price = None
                        last_check = None

                    batch_urls.append({
                        "url": url,  # 使用真实的URL
                        "status": url_status,
                        "price": price,
                        "lastCheck": last_check
                    })

                batches.append({
                    "id": f"batch-{i+1}",
                    "name": f"批次 {i+1}",
                    "status": status,
                    "progress": progress,
                    "totalUrls": len(batch_urls),
                    "completedUrls": completed,
                    "failedUrls": failed,
                    "startTime": start_time,
                    "endTime": end_time,
                    "urls": batch_urls
                })

        else:
            # 最后的降级：解析submission_id生成模拟数据
            if task_id.startswith("task_"):
                parts = task_id.split("_")
                if len(parts) >= 3:
                    total_urls = int(parts[-1])
                    batch_size = 10
                    total_batches = (total_urls + batch_size - 1) // batch_size
                else:
                    total_urls = 100
                    total_batches = 10
            else:
                total_urls = 100
                total_batches = 10

            # 构建任务基本信息（最后降级）
            task_info = {
                "id": task_id,
                "name": f"Excel任务 - {total_urls}个链接",
                "status": "running",
                "platform": "mercadolibre",
                "priority": "normal",
                "progress": 35,
                "totalUrls": total_urls,
                "completedUrls": int(total_urls * 0.35),
                "failedUrls": int(total_urls * 0.05),
                "createdAt": datetime.now().isoformat(),
                "updatedAt": datetime.now().isoformat()
            }

            # 构建模拟批次信息
            batches = []
            for i in range(total_batches):
                batch_urls_count = min(batch_size, total_urls - i * batch_size)

                # 模拟不同批次的状态
                if i < total_batches // 3:
                    status = "completed"
                    progress = 100
                    completed = batch_urls_count
                    failed = 0
                    start_time = f"2025-06-30 0{3+i}:00:00"
                    end_time = f"2025-06-30 0{3+i}:05:30"
                elif i < total_batches * 2 // 3:
                    status = "running"
                    progress = 60
                    completed = int(batch_urls_count * 0.6)
                    failed = int(batch_urls_count * 0.1)
                    start_time = f"2025-06-30 0{3+i}:00:00"
                    end_time = None
                else:
                    status = "pending"
                    progress = 0
                    completed = 0
                    failed = 0
                    start_time = None
                    end_time = None

                # 构建批次内的URL列表
                batch_urls = []
                for j in range(batch_urls_count):
                    url_index = i * batch_size + j + 1

                    if j < completed:
                        url_status = "completed"
                        price = f"${20 + (url_index % 50)}.99"
                        last_check = f"0{3+i}:0{5+j}:{30-j}"
                    elif j < completed + failed:
                        url_status = "failed"
                        price = None
                        last_check = f"0{3+i}:0{5+j}:{30-j}"
                    elif status == "running":
                        url_status = "running"
                        price = None
                        last_check = None
                    else:
                        url_status = "pending"
                        price = None
                        last_check = None

                    batch_urls.append({
                        "url": f"https://mercadolibre.com/product{url_index}",
                        "status": url_status,
                        "price": price,
                        "lastCheck": last_check
                    })

                batches.append({
                    "id": f"batch-{i+1}",
                    "name": f"批次 {i+1}",
                    "status": status,
                    "progress": progress,
                    "totalUrls": batch_urls_count,
                    "completedUrls": completed,
                    "failedUrls": failed,
                    "startTime": start_time,
                    "endTime": end_time,
                    "urls": batch_urls
                })

        # 构建响应
        response = TaskDetailResponse(
            success=True,
            task=task_info,
            batches=batches
        )

        logger.info(f"Task detail retrieved successfully: {task_id}, {len(batches)} batches")
        return response

    except Exception as e:
        logger.error(f"Failed to get task detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/tasks/upload-excel")
async def upload_excel(
    file: UploadFile = File(...),
    priority: str = Form("normal"),
    platform: str = Form("mercadolibre")
):
    """
    上传和解析Excel文件
    """
    try:
        logger.info(f"Received file: {file.filename}, priority: {priority}, platform: {platform}")
        
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel文件 (.xlsx, .xls)")
        
        # 读取文件内容
        contents = await file.read()
        
        # 使用pandas读取Excel
        try:
            # 尝试读取Excel文件
            df = pd.read_excel(io.BytesIO(contents))
            logger.info(f"Excel file read successfully. Shape: {df.shape}")
            
            # 查找包含URL的列
            url_columns = []
            for col in df.columns:
                # 检查列中是否包含URL
                sample_values = df[col].dropna().astype(str).head(10)
                if any('http' in str(val).lower() for val in sample_values):
                    url_columns.append(col)
            
            if not url_columns:
                # 如果没找到URL列，假设第一列是URL
                url_columns = [df.columns[0]]
            
            logger.info(f"Found URL columns: {url_columns}")
            
            # 提取所有URL
            all_urls = []
            for col in url_columns:
                urls = df[col].dropna().astype(str).tolist()
                all_urls.extend(urls)
            
            # 去重
            unique_urls = list(set(all_urls))
            
            # 简单的URL验证
            valid_urls = []
            invalid_urls = []
            
            for url in unique_urls:
                if 'http' in str(url).lower() and '.' in str(url):
                    valid_urls.append(url)
                else:
                    invalid_urls.append(url)
            
            # 平台分析
            platforms = {}
            for url in valid_urls:
                url_lower = str(url).lower()
                if 'amazon' in url_lower:
                    platform_name = 'Amazon'
                elif 'mercadolibre' in url_lower or 'mercadolivre' in url_lower:
                    platform_name = 'MercadoLibre'
                elif 'ebay' in url_lower:
                    platform_name = 'eBay'
                elif 'aliexpress' in url_lower:
                    platform_name = 'AliExpress'
                else:
                    platform_name = 'Other'
                
                platforms[platform_name] = platforms.get(platform_name, 0) + 1
            
            # 构建详细的URL数据
            url_details = []
            for i, url in enumerate(valid_urls):
                # 确定平台
                url_lower = str(url).lower()
                if 'amazon' in url_lower:
                    platform_name = 'Amazon'
                elif 'mercadolibre' in url_lower or 'mercadolivre' in url_lower:
                    platform_name = 'MercadoLibre'
                elif 'ebay' in url_lower:
                    platform_name = 'eBay'
                elif 'aliexpress' in url_lower:
                    platform_name = 'AliExpress'
                else:
                    platform_name = 'Other'

                url_details.append({
                    "id": f"url-{i+1}",
                    "url": url,
                    "status": "valid",
                    "platform": platform_name,
                    "row_index": i + 1
                })

            # 添加无效URL
            for i, url in enumerate(invalid_urls):
                url_details.append({
                    "id": f"invalid-url-{i+1}",
                    "url": url,
                    "status": "invalid",
                    "platform": "Unknown",
                    "row_index": len(valid_urls) + i + 1
                })

            # 构建响应
            response = {
                "success": True,
                "message": "Excel文件解析成功",
                "total_urls": len(unique_urls),
                "valid_urls": len(valid_urls),
                "invalid_urls": len(invalid_urls),
                "platforms": platforms,
                "urls": url_details,  # 添加实际的URL数据
                "file_info": {
                    "filename": file.filename,
                    "size": len(contents),
                    "rows": len(df),
                    "columns": len(df.columns)
                }
            }
            
            logger.info(f"Response: {response}")

            # 创建任务ID
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(valid_urls)}"

            # 保存任务数据到内存存储
            task_data = {
                "id": task_id,
                "name": f"Excel任务 - {file.filename}",
                "filename": file.filename,
                "urls": valid_urls,
                "platform": platform,
                "priority": priority,
                "status": "created",
                "total_urls": len(valid_urls),
                "valid_urls": len(valid_urls),
                "invalid_urls": len(invalid_urls),
                "platforms": platforms,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            task_storage.append(task_data)

            # 添加submission_id到响应
            response["submission_id"] = task_id

            # 广播任务创建消息到所有WebSocket连接
            task_message = {
                "type": "task_update",
                "data": {
                    "action": "task_created",
                    "task_id": task_id,
                    "filename": file.filename,
                    "total_urls": response["total_urls"],
                    "valid_urls": response["valid_urls"],
                    "status": "created"
                },
                "timestamp": datetime.now().isoformat()
            }
            await manager.broadcast(json.dumps(task_message))

            return response

        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            raise HTTPException(status_code=400, detail=f"Excel文件读取失败: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

# 添加一个测试端点来发送模拟消息
@app.post("/api/v1/test/broadcast")
async def test_broadcast(message: str = "Test message"):
    """测试WebSocket广播功能"""
    test_message = {
        "type": "test_message",
        "data": {"message": message},
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(json.dumps(test_message))
    return {"message": "Broadcast sent", "connections": len(manager.active_connections)}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Simple Backend Server...")
    uvicorn.run(
        "simple_backend:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # 禁用reload避免multiprocessing问题
        log_level="info"
    )
