@echo off
chcp 65001 >nul
echo ====================================================
echo 🚀 MonIt 任务调度系统 - 快速启动
echo ====================================================
echo.

cd /d "%~dp0\.."

echo 📦 检查环境...
where conda >nul 2>&1
if errorlevel 1 (
    echo ❌ Conda未安装或不在PATH中
    echo 请先安装Miniconda: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

where docker >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装或未启动
    echo 请先安装Docker: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ 基础环境检查通过

echo.
echo 🔍 检查MonIt环境...
call conda activate monit 2>nul
if errorlevel 1 (
    echo ❌ MonIt conda环境未找到
    echo 请先运行: scripts\setup_environment.bat
    pause
    exit /b 1
)

echo ✅ MonIt环境已激活

echo.
echo 🐳 启动Redis容器...
docker run -d --name monit-redis-test -p 6379:6379 redis:7-alpine --appendonly yes 2>nul
if errorlevel 1 (
    echo ⚠️ Redis容器可能已存在，尝试启动现有容器...
    docker start monit-redis-test >nul 2>&1
)

echo.
echo ⏳ 等待Redis启动 (3秒)...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 验证Redis连接...
docker exec monit-redis-test redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo ❌ Redis连接失败
    pause
    exit /b 1
)
echo ✅ Redis连接正常

echo.
echo 📊 运行快速测试...
python backend\scripts\quick_test.py
if errorlevel 1 (
    echo ❌ 快速测试失败
    pause
    exit /b 1
)

echo.
echo ====================================================
echo 🎉 任务调度系统启动成功！
echo ====================================================
echo.
echo 📌 可用功能:
echo    ✅ Excel处理 (624个URL)
echo    ✅ 任务分片 (智能批次分解)
echo    ✅ 限流控制 (2并发限制)
echo    ✅ 优先级调度 (4级优先级)
echo    ✅ 重试机制 (智能重试策略)
echo.
echo 🚀 下一步操作:
echo    1. 运行真实场景测试: python backend\scripts\run_real_test.py
echo    2. 启动任务管理器: python backend\scripts\start_task_manager.py
echo    3. 启动完整系统: scripts\start_dev.bat
echo.
echo 🛑 停止服务:
echo    docker stop monit-redis-test
echo    docker rm monit-redis-test
echo ====================================================

pause
