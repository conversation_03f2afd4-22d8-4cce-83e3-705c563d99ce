#!/usr/bin/env python3
"""
直接测试Celery任务
"""

import requests
import json
import time

def test_direct_celery_task():
    """直接调用Celery任务API"""
    print("直接测试Celery任务...")
    
    # 构造任务数据
    task_data = {
        "batch_id": "test_batch_001",
        "urls": ["https://httpbin.org/json", "https://httpbin.org/delay/1"],
        "platform": "test",
        "task_id": 12345,
        "options": {
            "crawler_backend": {
                "name": "test_backend",
                "base_url": "http://localhost:11235",
                "timeout": 30.0,
                "max_retries": 3,
                "auth_type": "none"
            }
        }
    }
    
    try:
        # 直接调用后端的任务提交API
        response = requests.post(
            "http://localhost:8000/api/v1/tasks/submit-batch",
            json=task_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务提交成功: {result}")
            
            # 等待任务执行
            print("等待任务执行...")
            time.sleep(10)
            
            # 检查新的爬虫请求记录
            check_response = requests.get("http://localhost:8000/api/v1/crawler-debug/requests?limit=3")
            if check_response.status_code == 200:
                requests_data = check_response.json()
                print(f"检查到 {len(requests_data)} 条记录")
                
                for req in requests_data:
                    if req['task_id'] == '12345':  # 查找我们的测试任务
                        print(f"找到测试任务记录:")
                        print(f"  - Celery Worker: {req['celery_worker']}")
                        if req['celery_worker'] and req['celery_worker'] != 'null':
                            print("🎉 Celery Worker修复成功!")
                            return True
                        else:
                            print("❌ Celery Worker仍然为空")
                            return False
                
                print("没有找到测试任务的记录")
            else:
                print(f"检查请求记录失败: {check_response.status_code}")
        else:
            print(f"任务提交失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"测试失败: {e}")
    
    return False

def test_task_manager_api():
    """测试任务管理器API"""
    print("\n测试任务管理器API...")
    
    try:
        # 使用任务管理器API提交任务
        task_data = {
            "urls": ["https://httpbin.org/json"],
            "platform": "test",
            "task_id": 54321,
            "priority": "normal"
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/task-manager/submit",
            json=task_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务管理器提交成功: {result}")
            return True
        else:
            print(f"任务管理器提交失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"任务管理器测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🔧 直接测试Celery任务执行...\n")
    
    # 测试直接Celery任务
    if not test_direct_celery_task():
        # 测试任务管理器API
        test_task_manager_api()

if __name__ == "__main__":
    main()
