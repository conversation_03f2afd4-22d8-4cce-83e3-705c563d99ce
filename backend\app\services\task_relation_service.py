"""
任务关联信息管理服务
用于管理监控任务、Celery任务、Worker、URL之间的关联关系
"""

import json
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass
import redis

logger = logging.getLogger(__name__)


@dataclass
class TaskRelation:
    """任务关联信息"""
    monitoring_task_id: str
    celery_task_id: str
    worker_id: str
    url: str
    batch_id: str
    created_at: str
    status: str = "active"  # active, completed, failed


@dataclass
class MonitoringTaskRelations:
    """监控任务的完整关联信息"""
    monitoring_task_id: str
    celery_task_ids: List[str]
    worker_ids: List[str]
    urls: List[str]
    created_at: str
    updated_at: str


class TaskRelationService:
    """任务关联信息管理服务"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        
        # Redis键模式
        self.keys = {
            'monitoring_task': 'task_relations:monitoring_task:{}',
            'celery_task': 'task_relations:celery_task:{}',
            'worker_tasks': 'task_relations:worker:{}',
            'url_tasks': 'task_relations:url:{}',
            'all_monitoring_tasks': 'task_relations:indexes:all_monitoring_tasks',
            'all_celery_tasks': 'task_relations:indexes:all_celery_tasks',
            'active_tasks': 'task_relations:indexes:active_tasks'
        }
    
    def _get_url_hash(self, url: str) -> str:
        """获取URL的哈希值作为键"""
        return hashlib.md5(url.encode()).hexdigest()[:16]
    
    async def create_task_relation(
        self,
        monitoring_task_id: str,
        celery_task_id: str,
        worker_id: str,
        url: str,
        batch_id: str
    ) -> TaskRelation:
        """创建任务关联关系"""
        try:
            now = datetime.now().isoformat()
            url_hash = self._get_url_hash(url)
            
            # 创建关联记录
            relation = TaskRelation(
                monitoring_task_id=monitoring_task_id,
                celery_task_id=celery_task_id,
                worker_id=worker_id,
                url=url,
                batch_id=batch_id,
                created_at=now
            )
            
            # 存储Celery任务关联信息
            celery_task_key = self.keys['celery_task'].format(celery_task_id)
            self.redis_client.hset(celery_task_key, mapping={
                "monitoring_task_id": monitoring_task_id,
                "worker_id": worker_id,
                "url": url,
                "batch_id": batch_id,
                "created_at": now,
                "status": "active"
            })
            
            # 更新监控任务关联信息
            await self._update_monitoring_task_relations(
                monitoring_task_id, celery_task_id, worker_id, url
            )
            
            # 更新Worker关联信息
            worker_key = self.keys['worker_tasks'].format(worker_id)
            self.redis_client.sadd(worker_key, celery_task_id)
            
            # 更新URL关联信息
            url_key = self.keys['url_tasks'].format(url_hash)
            self.redis_client.sadd(url_key, celery_task_id)
            
            # 更新索引
            self.redis_client.sadd(self.keys['all_monitoring_tasks'], monitoring_task_id)
            self.redis_client.sadd(self.keys['all_celery_tasks'], celery_task_id)
            self.redis_client.sadd(self.keys['active_tasks'], celery_task_id)
            
            logger.info(f"Created task relation: {monitoring_task_id} -> {celery_task_id}")
            return relation
            
        except Exception as e:
            logger.error(f"Failed to create task relation: {e}")
            raise
    
    async def _update_monitoring_task_relations(
        self,
        monitoring_task_id: str,
        celery_task_id: str,
        worker_id: str,
        url: str
    ):
        """更新监控任务的关联信息"""
        try:
            monitoring_key = self.keys['monitoring_task'].format(monitoring_task_id)
            now = datetime.now().isoformat()
            
            # 获取现有数据
            existing_data = self.redis_client.hgetall(monitoring_key)
            
            if existing_data:
                # 更新现有数据
                celery_task_ids = existing_data.get('celery_task_ids', '').split(',')
                worker_ids = existing_data.get('worker_ids', '').split(',')
                urls = existing_data.get('urls', '').split(',')
                
                # 去重并添加新数据
                if celery_task_id not in celery_task_ids:
                    celery_task_ids.append(celery_task_id)
                if worker_id not in worker_ids:
                    worker_ids.append(worker_id)
                if url not in urls:
                    urls.append(url)
                
                # 清理空字符串
                celery_task_ids = [tid for tid in celery_task_ids if tid]
                worker_ids = [wid for wid in worker_ids if wid]
                urls = [u for u in urls if u]
                
            else:
                # 创建新数据
                celery_task_ids = [celery_task_id]
                worker_ids = [worker_id]
                urls = [url]
            
            # 保存更新后的数据
            self.redis_client.hset(monitoring_key, mapping={
                "celery_task_ids": ','.join(celery_task_ids),
                "worker_ids": ','.join(worker_ids),
                "urls": ','.join(urls),
                "created_at": existing_data.get('created_at', now),
                "updated_at": now
            })
            
        except Exception as e:
            logger.error(f"Failed to update monitoring task relations: {e}")
            raise
    
    async def get_task_relation_by_celery_id(self, celery_task_id: str) -> Optional[TaskRelation]:
        """通过Celery任务ID获取关联信息"""
        try:
            celery_task_key = self.keys['celery_task'].format(celery_task_id)
            data = self.redis_client.hgetall(celery_task_key)
            
            if not data:
                return None
            
            return TaskRelation(
                monitoring_task_id=data.get('monitoring_task_id', ''),
                celery_task_id=celery_task_id,
                worker_id=data.get('worker_id', ''),
                url=data.get('url', ''),
                batch_id=data.get('batch_id', ''),
                created_at=data.get('created_at', ''),
                status=data.get('status', 'active')
            )
            
        except Exception as e:
            logger.error(f"Failed to get task relation by celery ID {celery_task_id}: {e}")
            return None
    
    async def get_monitoring_task_relations(self, monitoring_task_id: str) -> Optional[MonitoringTaskRelations]:
        """获取监控任务的完整关联信息"""
        try:
            monitoring_key = self.keys['monitoring_task'].format(monitoring_task_id)
            data = self.redis_client.hgetall(monitoring_key)
            
            if not data:
                return None
            
            return MonitoringTaskRelations(
                monitoring_task_id=monitoring_task_id,
                celery_task_ids=data.get('celery_task_ids', '').split(','),
                worker_ids=data.get('worker_ids', '').split(','),
                urls=data.get('urls', '').split(','),
                created_at=data.get('created_at', ''),
                updated_at=data.get('updated_at', '')
            )
            
        except Exception as e:
            logger.error(f"Failed to get monitoring task relations {monitoring_task_id}: {e}")
            return None
    
    async def get_worker_tasks(self, worker_id: str) -> List[str]:
        """获取Worker关联的所有Celery任务ID"""
        try:
            worker_key = self.keys['worker_tasks'].format(worker_id)
            return list(self.redis_client.smembers(worker_key))
        except Exception as e:
            logger.error(f"Failed to get worker tasks for {worker_id}: {e}")
            return []
    
    async def get_url_tasks(self, url: str) -> List[str]:
        """获取URL关联的所有Celery任务ID"""
        try:
            url_hash = self._get_url_hash(url)
            url_key = self.keys['url_tasks'].format(url_hash)
            return list(self.redis_client.smembers(url_key))
        except Exception as e:
            logger.error(f"Failed to get URL tasks for {url}: {e}")
            return []
    
    async def update_task_status(self, celery_task_id: str, status: str):
        """更新任务状态"""
        try:
            celery_task_key = self.keys['celery_task'].format(celery_task_id)
            self.redis_client.hset(celery_task_key, "status", status)
            
            # 更新索引
            if status == "completed" or status == "failed":
                self.redis_client.srem(self.keys['active_tasks'], celery_task_id)
            elif status == "active":
                self.redis_client.sadd(self.keys['active_tasks'], celery_task_id)
                
        except Exception as e:
            logger.error(f"Failed to update task status for {celery_task_id}: {e}")


# 全局服务实例
task_relation_service = TaskRelationService()
