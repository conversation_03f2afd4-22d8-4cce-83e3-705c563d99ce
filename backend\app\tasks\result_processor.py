"""
结果处理任务

处理爬取结果、数据聚合和后处理
"""

import asyncio
import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from celery import Task
from decimal import Decimal

from app.celery_app import celery_app
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.snapshot import ProductSnapshot
from app.models.log import SystemLog
from sqlalchemy import select, func, and_, desc
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class ResultProcessorTask(Task):
    """结果处理任务基类"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """失败时的回调"""
        logger.error(f"Result processor task {task_id} failed: {exc}")


@celery_app.task(
    bind=True,
    base=ResultProcessorTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60}
)
def process_results(
    self,
    task_id: int,
    batch_results: Dict[str, Any]
) -> Dict[str, Any]:
    """处理批次结果"""
    return asyncio.run(_process_results_async(task_id, batch_results))


async def _process_results_async(
    task_id: int,
    batch_results: Dict[str, Any]
) -> Dict[str, Any]:
    """异步处理批次结果"""
    
    processing_stats = {
        "start_time": datetime.now().isoformat(),
        "task_id": task_id,
        "batch_id": batch_results.get("batch_id"),
        "processed_results": 0,
        "data_quality_issues": 0,
        "price_alerts": 0,
        "stock_alerts": 0,
        "errors": []
    }
    
    try:
        async with get_async_session() as session:
            # 获取任务信息
            task_query = await session.execute(
                select(CrawlTask).where(CrawlTask.id == task_id)
            )
            task = task_query.scalar_one_or_none()
            
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            # 处理每个URL的结果
            for result in batch_results.get("results", []):
                if result.get("success") and result.get("data"):
                    try:
                        await _process_single_result(
                            session, task_id, result, processing_stats
                        )
                        processing_stats["processed_results"] += 1
                    except Exception as e:
                        error_msg = f"Failed to process result for {result.get('url')}: {e}"
                        logger.error(error_msg)
                        processing_stats["errors"].append(error_msg)
            
            # 更新任务统计
            await _update_task_statistics(session, task_id)
            
            await session.commit()
        
        # 生成告警
        await _generate_alerts(task_id, processing_stats)
        
        processing_stats["end_time"] = datetime.now().isoformat()
        processing_stats["success"] = True
        
        logger.info(
            f"Processed {processing_stats['processed_results']} results for task {task_id}"
        )
        
        return processing_stats
        
    except Exception as e:
        error_msg = f"Result processing failed for task {task_id}: {e}"
        logger.error(error_msg)
        processing_stats["errors"].append(error_msg)
        processing_stats["success"] = False
        processing_stats["end_time"] = datetime.now().isoformat()
        
        raise


async def _process_single_result(
    session: AsyncSession,
    task_id: int,
    result: Dict[str, Any],
    stats: Dict[str, Any]
) -> None:
    """处理单个结果"""
    
    url = result.get("url")
    data = result.get("data", {})
    
    # 数据清理和验证
    cleaned_data = await _clean_and_validate_data(data, stats)
    
    # 检查是否已存在该URL的快照
    existing_snapshot = await session.execute(
        select(ProductSnapshot).where(
            and_(
                ProductSnapshot.task_id == task_id,
                ProductSnapshot.product_url == url
            )
        )
    )
    
    existing = existing_snapshot.scalar_one_or_none()
    
    if existing:
        # 更新现有快照
        for key, value in cleaned_data.items():
            if hasattr(existing, key):
                setattr(existing, key, value)
        existing.snapshot_time = datetime.now()
        existing.raw_data = data
    else:
        # 创建新快照
        snapshot = ProductSnapshot(
            task_id=task_id,
            product_url=url,
            snapshot_time=datetime.now(),
            raw_data=data,
            **cleaned_data
        )
        session.add(snapshot)
    
    # 检查价格变化告警
    await _check_price_alerts(session, url, cleaned_data, stats)
    
    # 检查库存告警
    await _check_stock_alerts(session, url, cleaned_data, stats)


async def _clean_and_validate_data(
    raw_data: Dict[str, Any],
    stats: Dict[str, Any]
) -> Dict[str, Any]:
    """清理和验证数据"""
    
    cleaned = {}
    
    # 清理商品名称
    title = raw_data.get("title", "").strip()
    if len(title) > 500:
        title = title[:500]
        stats["data_quality_issues"] += 1
    cleaned["product_name"] = title
    
    # 清理价格数据
    try:
        current_price = float(raw_data.get("price", 0))
        if current_price < 0:
            current_price = 0
            stats["data_quality_issues"] += 1
        cleaned["current_price"] = current_price
    except (ValueError, TypeError):
        cleaned["current_price"] = 0.0
        stats["data_quality_issues"] += 1
    
    try:
        original_price = float(raw_data.get("original_price", 0))
        if original_price < 0:
            original_price = 0
            stats["data_quality_issues"] += 1
        cleaned["original_price"] = original_price
    except (ValueError, TypeError):
        cleaned["original_price"] = 0.0
        stats["data_quality_issues"] += 1
    
    # 计算折扣率
    if cleaned["original_price"] > 0 and cleaned["current_price"] > 0:
        discount_rate = (
            (cleaned["original_price"] - cleaned["current_price"]) / 
            cleaned["original_price"] * 100
        )
        cleaned["discount_rate"] = max(0, min(100, discount_rate))
    else:
        cleaned["discount_rate"] = 0.0
    
    # 清理库存数据
    try:
        stock = int(raw_data.get("stock", 0))
        if stock < 0:
            stock = 0
            stats["data_quality_issues"] += 1
        cleaned["stock_quantity"] = stock
    except (ValueError, TypeError):
        cleaned["stock_quantity"] = 0
        stats["data_quality_issues"] += 1
    
    # 清理销量数据
    try:
        sales = int(raw_data.get("sales", 0))
        if sales < 0:
            sales = 0
            stats["data_quality_issues"] += 1
        cleaned["sales_count"] = sales
    except (ValueError, TypeError):
        cleaned["sales_count"] = 0
        stats["data_quality_issues"] += 1
    
    # 清理评分数据
    try:
        rating = float(raw_data.get("rating", 0))
        if rating < 0 or rating > 5:
            rating = 0
            stats["data_quality_issues"] += 1
        cleaned["rating_score"] = rating
    except (ValueError, TypeError):
        cleaned["rating_score"] = 0.0
        stats["data_quality_issues"] += 1
    
    try:
        rating_count = int(raw_data.get("rating_count", 0))
        if rating_count < 0:
            rating_count = 0
            stats["data_quality_issues"] += 1
        cleaned["rating_count"] = rating_count
    except (ValueError, TypeError):
        cleaned["rating_count"] = 0
        stats["data_quality_issues"] += 1
    
    # 清理图片URL
    image_url = raw_data.get("image_url", "").strip()
    if image_url and not image_url.startswith(("http://", "https://")):
        image_url = ""
        stats["data_quality_issues"] += 1
    cleaned["image_url"] = image_url
    
    return cleaned


async def _check_price_alerts(
    session: AsyncSession,
    url: str,
    current_data: Dict[str, Any],
    stats: Dict[str, Any]
) -> None:
    """检查价格变化告警"""
    
    try:
        # 获取最近的价格记录
        recent_snapshot = await session.execute(
            select(ProductSnapshot)
            .where(ProductSnapshot.product_url == url)
            .order_by(desc(ProductSnapshot.snapshot_time))
            .limit(1)
        )
        
        recent = recent_snapshot.scalar_one_or_none()
        
        if recent and recent.current_price > 0:
            current_price = current_data.get("current_price", 0)
            previous_price = recent.current_price
            
            # 计算价格变化百分比
            if previous_price > 0:
                price_change = abs(current_price - previous_price) / previous_price * 100
                
                # 如果价格变化超过10%，生成告警
                if price_change > 10:
                    await _create_alert(
                        session,
                        "price_change",
                        f"Significant price change detected for {url}",
                        {
                            "url": url,
                            "previous_price": float(previous_price),
                            "current_price": current_price,
                            "change_percent": price_change
                        }
                    )
                    stats["price_alerts"] += 1
    
    except Exception as e:
        logger.error(f"Failed to check price alerts for {url}: {e}")


async def _check_stock_alerts(
    session: AsyncSession,
    url: str,
    current_data: Dict[str, Any],
    stats: Dict[str, Any]
) -> None:
    """检查库存告警"""
    
    try:
        current_stock = current_data.get("stock_quantity", 0)
        
        # 低库存告警（库存少于10）
        if 0 < current_stock < 10:
            await _create_alert(
                session,
                "low_stock",
                f"Low stock alert for {url}",
                {
                    "url": url,
                    "stock_quantity": current_stock
                }
            )
            stats["stock_alerts"] += 1
        
        # 缺货告警
        elif current_stock == 0:
            await _create_alert(
                session,
                "out_of_stock",
                f"Out of stock alert for {url}",
                {
                    "url": url,
                    "stock_quantity": current_stock
                }
            )
            stats["stock_alerts"] += 1
    
    except Exception as e:
        logger.error(f"Failed to check stock alerts for {url}: {e}")


async def _create_alert(
    session: AsyncSession,
    alert_type: str,
    message: str,
    details: Dict[str, Any]
) -> None:
    """创建告警记录"""
    
    try:
        alert_log = SystemLog(
            level="WARNING",
            message=message,
            module="result_processor",
            details={
                "alert_type": alert_type,
                **details
            },
            created_at=datetime.now()
        )
        
        session.add(alert_log)
    
    except Exception as e:
        logger.error(f"Failed to create alert: {e}")


async def _update_task_statistics(session: AsyncSession, task_id: int) -> None:
    """更新任务统计信息"""
    
    try:
        # 统计成功和失败的快照数量
        success_count = await session.execute(
            select(func.count(ProductSnapshot.id))
            .where(ProductSnapshot.task_id == task_id)
        )
        
        # 更新任务记录
        task_query = await session.execute(
            select(CrawlTask).where(CrawlTask.id == task_id)
        )
        task = task_query.scalar_one_or_none()
        
        if task:
            task.success_count = success_count.scalar()
            # 这里可以添加更多统计信息的更新
    
    except Exception as e:
        logger.error(f"Failed to update task statistics: {e}")


async def _generate_alerts(task_id: int, stats: Dict[str, Any]) -> None:
    """生成告警"""
    
    try:
        # 如果数据质量问题过多，生成告警
        if stats["data_quality_issues"] > 10:
            async with get_async_session() as session:
                await _create_alert(
                    session,
                    "data_quality",
                    f"High number of data quality issues in task {task_id}",
                    {
                        "task_id": task_id,
                        "data_quality_issues": stats["data_quality_issues"],
                        "processed_results": stats["processed_results"]
                    }
                )
                await session.commit()
    
    except Exception as e:
        logger.error(f"Failed to generate alerts: {e}")


@celery_app.task(
    bind=True,
    base=ResultProcessorTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 300}
)
def aggregate_daily_statistics(self) -> Dict[str, Any]:
    """聚合每日统计数据"""
    return asyncio.run(_aggregate_daily_statistics_async())


async def _aggregate_daily_statistics_async() -> Dict[str, Any]:
    """异步聚合每日统计数据"""
    
    try:
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        async with get_async_session() as session:
            # 统计昨天的任务数据
            daily_stats = await session.execute(
                select(
                    func.count(CrawlTask.id).label('total_tasks'),
                    func.sum(CrawlTask.success_count).label('total_success'),
                    func.sum(CrawlTask.failed_count).label('total_failed')
                ).where(
                    func.date(CrawlTask.created_at) == yesterday
                )
            )
            
            stats_row = daily_stats.first()
            
            # 统计产品快照数据
            snapshot_stats = await session.execute(
                select(
                    func.count(ProductSnapshot.id).label('total_snapshots'),
                    func.avg(ProductSnapshot.current_price).label('avg_price'),
                    func.min(ProductSnapshot.current_price).label('min_price'),
                    func.max(ProductSnapshot.current_price).label('max_price')
                ).where(
                    func.date(ProductSnapshot.snapshot_time) == yesterday
                )
            )
            
            snapshot_row = snapshot_stats.first()
            
            aggregated_data = {
                "date": yesterday.isoformat(),
                "tasks": {
                    "total": stats_row.total_tasks or 0,
                    "successful_urls": stats_row.total_success or 0,
                    "failed_urls": stats_row.total_failed or 0
                },
                "snapshots": {
                    "total": snapshot_row.total_snapshots or 0,
                    "avg_price": float(snapshot_row.avg_price or 0),
                    "min_price": float(snapshot_row.min_price or 0),
                    "max_price": float(snapshot_row.max_price or 0)
                },
                "generated_at": datetime.now().isoformat()
            }
            
            # 保存聚合数据到日志
            log_entry = SystemLog(
                level="INFO",
                message=f"Daily statistics for {yesterday}",
                module="result_processor",
                details=aggregated_data,
                created_at=datetime.now()
            )
            
            session.add(log_entry)
            await session.commit()
            
            logger.info(f"Daily statistics aggregated for {yesterday}")
            return aggregated_data
    
    except Exception as e:
        logger.error(f"Failed to aggregate daily statistics: {e}")
        raise
