"""
测试爬虫请求构建服务
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from app.services.crawler_request_builder import CrawlerRequestBuilder


class TestCrawlerRequestBuilder:
    """测试爬虫请求构建器"""
    
    @pytest.mark.asyncio
    async def test_build_crawler_request(self):
        """测试构建爬虫请求"""
        
        # 模拟数据
        task_id = "test-task-123"
        urls = ["https://example.com/1", "https://example.com/2"]
        crawl_config = {
            "llm_query": "提取商品信息",
            "llm_schema": '{"type": "object"}',
            "cache_enabled": True,
            "priority": "high",
            "auth_method": "api_key",
            "api_key_prefix": "test_key",
            "user_agent": "MonIt-Test/1.0"
        }
        execution_context = {
            "client_ip": "*************",
            "celery_task_id": "celery-123"
        }
        
        with patch('app.services.crawler_request_builder.SystemConfigService') as mock_system_config:
            # 模拟系统配置
            mock_config = MagicMock()
            mock_config.default_callback_url = "http://test.com/callback"
            mock_config.max_concurrent_tasks = 4
            mock_config.batch_timeout = 600
            mock_system_config.get_crawler_config.return_value = mock_config
            
            # 构建请求
            request = await CrawlerRequestBuilder.build_crawler_request(
                task_id=task_id,
                urls=urls,
                crawl_config=crawl_config,
                execution_context=execution_context
            )
            
            # 验证请求结构
            assert "request" in request
            assert "auth_context" in request
            
            # 验证request部分
            request_data = request["request"]
            assert request_data["urls"] == urls
            assert request_data["q"] == "提取商品信息"
            assert request_data["schema_str"] == '{"type": "object"}'
            assert request_data["cache"] is True
            assert request_data["priority"] == "high"
            assert "Task_test-task-123_" in request_data["batch_name"]
            assert "监控任务 test-task-123 的批次执行" in request_data["batch_description"]
            assert request_data["callback_url"] == "http://test.com/callback"
            assert request_data["max_concurrent_tasks"] == 4
            assert request_data["batch_timeout"] == 600
            
            # 验证auth_context部分
            auth_context = request["auth_context"]
            assert auth_context["user_id"] == "monit_system"
            assert auth_context["role"] == "user"
            assert auth_context["permissions"] == []
            assert auth_context["auth_method"] == "api_key"
            assert auth_context["api_key_prefix"] == "test_key"
            assert auth_context["client_ip"] == "*************"
            assert auth_context["user_agent"] == "MonIt-Test/1.0"
            assert auth_context["celery_task_id"] == "celery-123"
            assert "authenticated_at" in auth_context
    
    @pytest.mark.asyncio
    async def test_get_active_urls_for_task(self):
        """测试获取任务活跃URL"""
        
        task_id = "test-task-456"
        
        with patch('app.services.crawler_request_builder.TaskStorageService') as mock_task_storage, \
             patch('app.services.crawler_request_builder.UrlPoolService') as mock_url_pool_service, \
             patch('app.services.crawler_request_builder.redis') as mock_redis:
            
            # 模拟TaskStorageService
            mock_storage_instance = AsyncMock()
            mock_storage_instance.get_task_urls.return_value = ["url-1", "url-2", "url-3"]
            mock_task_storage.return_value = mock_storage_instance
            
            # 模拟UrlPoolService
            mock_pool_instance = AsyncMock()
            
            # 模拟URL项目
            mock_url_1 = MagicMock()
            mock_url_1.status = "active"
            mock_url_1.url = "https://example.com/1"
            
            mock_url_2 = MagicMock()
            mock_url_2.status = "active"
            mock_url_2.url = "https://example.com/2"
            
            mock_url_3 = MagicMock()
            mock_url_3.status = "disabled"  # 非活跃URL
            mock_url_3.url = "https://example.com/3"
            
            mock_pool_instance.get_url_by_id.side_effect = [mock_url_1, mock_url_2, mock_url_3]
            mock_url_pool_service.return_value = mock_pool_instance
            
            # 模拟Redis客户端
            mock_redis_client = AsyncMock()
            mock_redis.from_url.return_value = mock_redis_client
            
            # 获取活跃URL
            active_urls = await CrawlerRequestBuilder.get_active_urls_for_task(task_id)
            
            # 验证结果
            assert len(active_urls) == 2  # 只有2个活跃URL
            assert "https://example.com/1" in active_urls
            assert "https://example.com/2" in active_urls
            assert "https://example.com/3" not in active_urls  # 非活跃URL被过滤
            
            # 验证调用
            mock_storage_instance.get_task_urls.assert_called_once_with(task_id)
            assert mock_pool_instance.get_url_by_id.call_count == 3
            mock_redis_client.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_crawl_config_for_task(self):
        """测试获取任务爬取配置"""
        
        task_id = "test-task-789"
        
        with patch('app.services.crawler_request_builder.TaskStorageService') as mock_task_storage:
            # 模拟任务数据
            mock_task_data = {
                "id": task_id,
                "name": "测试任务",
                "config": {
                    "llm_query": "提取商品信息",
                    "llm_schema": '{"type": "object"}',
                    "cache_enabled": True,
                    "priority": "high",
                    "auth_method": "api_key",
                    "api_key_prefix": "test_key",
                    "user_agent": "MonIt-Test/1.0"
                }
            }
            
            mock_storage_instance = AsyncMock()
            mock_storage_instance.get_task.return_value = mock_task_data
            mock_task_storage.return_value = mock_storage_instance
            
            # 获取爬取配置
            config = await CrawlerRequestBuilder.get_crawl_config_for_task(task_id)
            
            # 验证结果
            assert config["llm_query"] == "提取商品信息"
            assert config["llm_schema"] == '{"type": "object"}'
            assert config["cache_enabled"] is True
            assert config["priority"] == "high"
            assert config["auth_method"] == "api_key"
            assert config["api_key_prefix"] == "test_key"
            assert config["user_agent"] == "MonIt-Test/1.0"
            
            # 验证调用
            mock_storage_instance.get_task.assert_called_once_with(task_id)
    
    @pytest.mark.asyncio
    async def test_build_request_for_task(self):
        """测试为任务构建完整请求"""
        
        task_id = "test-task-complete"
        
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config, \
             patch.object(CrawlerRequestBuilder, 'build_crawler_request') as mock_build_request:
            
            # 模拟返回值
            mock_urls = ["https://example.com/1", "https://example.com/2"]
            mock_config = {
                "llm_query": "提取商品信息",
                "priority": "high"
            }
            mock_request = {
                "request": {"urls": mock_urls},
                "auth_context": {"user_id": "monit_system"}
            }
            
            mock_get_urls.return_value = mock_urls
            mock_get_config.return_value = mock_config
            mock_build_request.return_value = mock_request
            
            # 构建完整请求
            execution_context = {"celery_task_id": "celery-456"}
            result = await CrawlerRequestBuilder.build_request_for_task(
                task_id=task_id,
                execution_context=execution_context
            )
            
            # 验证结果
            assert result == mock_request
            
            # 验证调用
            mock_get_urls.assert_called_once_with(task_id)
            mock_get_config.assert_called_once_with(task_id)
            mock_build_request.assert_called_once_with(
                task_id=task_id,
                urls=mock_urls,
                crawl_config=mock_config,
                execution_context=execution_context
            )
    
    @pytest.mark.asyncio
    async def test_build_request_for_task_no_urls(self):
        """测试任务没有活跃URL的情况"""
        
        task_id = "test-task-no-urls"
        
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls:
            mock_get_urls.return_value = []  # 没有活跃URL
            
            # 应该抛出异常
            with pytest.raises(ValueError, match="has no active URLs"):
                await CrawlerRequestBuilder.build_request_for_task(task_id)
    
    @pytest.mark.asyncio
    async def test_build_request_for_task_no_config(self):
        """测试任务没有爬取配置的情况"""
        
        task_id = "test-task-no-config"
        
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config:
            
            mock_get_urls.return_value = ["https://example.com/1"]
            mock_get_config.return_value = {}  # 没有配置
            
            # 应该抛出异常
            with pytest.raises(ValueError, match="has no crawl config"):
                await CrawlerRequestBuilder.build_request_for_task(task_id)


if __name__ == "__main__":
    pytest.main([__file__])
