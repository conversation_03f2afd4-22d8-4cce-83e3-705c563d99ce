#!/usr/bin/env python3
"""
测试文件大小限制移除

验证Excel上传功能是否已经移除文件大小限制
"""

import requests
import pandas as pd
import os
import tempfile

BASE_URL = "http://localhost:8000"

def create_large_test_excel(size_mb=60):
    """创建一个大的测试Excel文件"""
    print(f"📝 创建 {size_mb}MB 的测试Excel文件")
    
    # 计算需要多少行数据来达到目标大小
    # 每行大约包含一个长URL，估计每行约200字节
    target_bytes = size_mb * 1024 * 1024
    estimated_rows = target_bytes // 200
    
    print(f"   预计需要 {estimated_rows:,} 行数据")
    
    # 创建大量URL数据
    urls = []
    base_url = "https://www.amazon.com/dp/B08N5WRWNW?ref=very_long_reference_parameter_to_make_url_longer_"
    
    for i in range(estimated_rows):
        # 添加长参数使URL更大
        long_url = f"{base_url}{i:010d}&extra_param={'x' * 50}&another_param={'y' * 30}"
        urls.append(long_url)
        
        # 每10000行显示一次进度
        if (i + 1) % 10000 == 0:
            print(f"   已生成 {i + 1:,} 行数据")
    
    # 创建DataFrame
    df = pd.DataFrame({'URL': urls})
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
        filename = tmp_file.name
    
    print(f"   正在保存Excel文件...")
    df.to_excel(filename, index=False)
    
    # 检查实际文件大小
    actual_size = os.path.getsize(filename) / 1024 / 1024
    print(f"✅ 测试文件创建成功:")
    print(f"   - 文件路径: {filename}")
    print(f"   - 实际大小: {actual_size:.2f} MB")
    print(f"   - 数据行数: {len(urls):,}")
    
    return filename, actual_size

def test_large_file_upload(filename, file_size):
    """测试大文件上传"""
    print(f"\n📤 测试大文件上传 ({file_size:.2f} MB)")
    
    try:
        with open(filename, 'rb') as f:
            files = {
                'file': (os.path.basename(filename), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            print("   正在上传文件...")
            response = requests.post(
                f"{BASE_URL}/api/v1/excel/upload-and-parse", 
                files=files,
                timeout=600  # 10分钟超时
            )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 大文件上传成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 新增URL: {result['new_urls']}")
            print(f"   - 重复URL: {result['duplicate_urls']}")
            print(f"   - 处理时间: {result.get('processing_time', 'N/A')}")
            return True
        else:
            print(f"❌ 大文件上传失败:")
            print(f"   - 状态码: {response.status_code}")
            print(f"   - 错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ 上传超时（可能文件太大或网络问题）")
        return False
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return False

def test_normal_file_upload():
    """测试正常大小文件上传（确保基本功能正常）"""
    print(f"\n📤 测试正常大小文件上传")
    
    # 创建小文件
    urls = [
        "https://www.amazon.com/dp/B08N5WRWNW",
        "https://articulo.mercadolibre.com.mx/MLM-123456789",
        "https://www.ebay.com/itm/123456789"
    ]
    
    df = pd.DataFrame({'URL': urls})
    
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
        filename = tmp_file.name
    
    df.to_excel(filename, index=False)
    file_size = os.path.getsize(filename) / 1024 / 1024
    
    print(f"   小文件大小: {file_size:.2f} MB")
    
    try:
        with open(filename, 'rb') as f:
            files = {
                'file': (os.path.basename(filename), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            response = requests.post(f"{BASE_URL}/api/v1/excel/upload-and-parse", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 正常文件上传成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 新增URL: {result['new_urls']}")
            
            # 清理文件
            os.unlink(filename)
            return True
        else:
            print(f"❌ 正常文件上传失败: {response.status_code}")
            os.unlink(filename)
            return False
            
    except Exception as e:
        print(f"❌ 正常文件上传异常: {e}")
        os.unlink(filename)
        return False

def cleanup_file(filename):
    """清理测试文件"""
    try:
        if os.path.exists(filename):
            os.unlink(filename)
            print(f"\n🧹 已清理测试文件: {filename}")
    except Exception as e:
        print(f"\n⚠️ 清理文件失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试文件大小限制移除")
    print("=" * 50)
    
    # 测试1: 正常大小文件
    print("测试1: 验证基本功能正常")
    normal_test_passed = test_normal_file_upload()
    
    if not normal_test_passed:
        print("❌ 基本功能测试失败，停止后续测试")
        return
    
    # 测试2: 大文件上传
    print("\n" + "=" * 50)
    print("测试2: 大文件上传测试")
    
    large_filename = None
    try:
        # 创建60MB的测试文件
        large_filename, file_size = create_large_test_excel(60)
        
        # 测试上传
        large_test_passed = test_large_file_upload(large_filename, file_size)
        
        if large_test_passed:
            print(f"\n🎉 文件大小限制移除成功！")
            print(f"✅ 系统现在支持上传 {file_size:.2f} MB 的大文件")
        else:
            print(f"\n⚠️ 大文件上传失败，可能存在其他限制")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
    
    finally:
        # 清理测试文件
        if large_filename:
            cleanup_file(large_filename)
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
