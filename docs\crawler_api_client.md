# 爬虫API客户端文档

## 概述

MonIt爬虫系统采用API客户端模式，通过HTTP API调用外部爬虫服务来获取商品数据。这种架构具有以下优势：

- **轻量化**: 无需维护复杂的浏览器实例和反爬虫逻辑
- **稳定性**: 外部爬虫服务专门处理反爬虫和数据提取
- **可扩展性**: 支持分布式部署和水平扩展
- **维护性**: 降低了系统复杂度，便于维护和升级

## 架构设计

```
MonIt系统 → API客户端 → 外部爬虫服务 → 目标网站
    ↓           ↓              ↓
  数据存储   HTTP请求      数据提取
```

## 核心组件

### 1. CrawlerAPIClient

主要的API客户端类，负责与外部爬虫服务通信。

```python
from backend.crawler.api_client import CrawlerAPIClient, APIConfig

# 创建配置
config = APIConfig(
    base_url="http://crawler-service.example.com",
    api_key="your-api-key",
    timeout=30.0
)

# 创建客户端
async with CrawlerAPIClient(config) as client:
    # 爬取单个URL
    result = await client.crawl_url(
        "https://mercadolibre.com.ar/product/123",
        platform="mercadolibre"
    )
    
    # 批量爬取
    batch_result = await client.crawl_batch([
        "https://mercadolibre.com.ar/product/123",
        "https://mercadolibre.com.ar/product/456"
    ], platform="mercadolibre")
```

### 2. APIConfig

配置管理类，支持环境变量和字典配置。

```python
# 从环境变量创建配置
config = APIConfig.from_env()

# 从字典创建配置
config = APIConfig.from_dict({
    "base_url": "http://api.example.com",
    "api_key": "key123",
    "timeout": 30.0
})
```

### 3. CrawlerEngine

重构后的爬虫引擎，基于API客户端模式。

```python
from backend.crawler import CrawlerEngine

# 创建引擎
engine = CrawlerEngine({
    "api_client": {
        "base_url": "http://crawler-api.example.com",
        "api_key": "your-key"
    },
    "max_concurrent_tasks": 10
})

# 启动引擎
await engine.start()

# 爬取数据
result = await engine.crawl_url(
    "https://mercadolibre.com.ar/product/123"
)

# 停止引擎
await engine.stop()
```

## API接口规范

### 爬取单个URL

**请求**:
```http
POST /api/v1/crawl
Content-Type: application/json

{
    "url": "https://mercadolibre.com.ar/product/123",
    "platform": "mercadolibre",
    "options": {
        "wait_for_load": true,
        "extract_images": true
    }
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "title": "Product Title",
        "price": "$99.99",
        "currency": "ARS",
        "availability": "in_stock",
        "images": ["url1", "url2"],
        "description": "Product description"
    },
    "url": "https://mercadolibre.com.ar/product/123",
    "timestamp": "2024-12-29T10:00:00Z",
    "processing_time": 2.5
}
```

### 批量爬取

**请求**:
```http
POST /api/v1/crawl/batch
Content-Type: application/json

{
    "urls": [
        "https://mercadolibre.com.ar/product/123",
        "https://mercadolibre.com.ar/product/456"
    ],
    "platform": "mercadolibre",
    "options": {}
}
```

**响应**:
```json
{
    "task_id": "batch-abc123",
    "status": "pending",
    "urls_count": 2,
    "estimated_time": 30
}
```

### 获取任务状态

**请求**:
```http
GET /api/v1/tasks/{task_id}/status
```

**响应**:
```json
{
    "task_id": "batch-abc123",
    "status": "completed",
    "progress": 100,
    "completed_urls": 2,
    "failed_urls": 0
}
```

## 配置选项

### 环境变量

```bash
# API服务配置
CRAWLER_API_BASE_URL=http://crawler-service.example.com
CRAWLER_API_KEY=your-api-key
CRAWLER_API_TIMEOUT=30.0
CRAWLER_API_MAX_RETRIES=3

# 引擎配置
CRAWLER_MAX_CONCURRENT=10
CRAWLER_DEFAULT_PLATFORM=mercadolibre
```

### 支持的平台

- `mercadolibre`: MercadoLibre (阿根廷、墨西哥、巴西等)
- `amazon`: Amazon (全球)
- `ebay`: eBay (全球)

## 错误处理

系统提供完整的异常处理机制：

```python
from backend.crawler.api_client.api_exceptions import (
    APIException,
    APITimeoutException,
    APIAuthException,
    APIRateLimitException
)

try:
    result = await client.crawl_url(url)
except APITimeoutException:
    # 处理超时
    pass
except APIAuthException:
    # 处理认证错误
    pass
except APIRateLimitException as e:
    # 处理限流，可以获取重试时间
    retry_after = e.retry_after
    pass
```

## 监控和统计

客户端提供详细的统计信息：

```python
stats = client.get_stats()
print(f"成功率: {stats['success_rate']:.2%}")
print(f"平均响应时间: {stats['avg_response_time']:.2f}s")
print(f"总请求数: {stats['requests_sent']}")
```

## 最佳实践

1. **使用上下文管理器**: 确保客户端正确关闭
2. **配置合理的超时**: 根据网站响应时间调整
3. **实现重试逻辑**: 处理临时网络问题
4. **监控API配额**: 避免超出限流限制
5. **缓存结果**: 避免重复请求相同数据

## 迁移指南

从旧的Playwright模式迁移到API客户端模式：

1. 更新依赖: 移除playwright, beautifulsoup4等
2. 更新配置: 添加API服务配置
3. 更新代码: 使用新的API客户端接口
4. 测试验证: 确保功能正常工作

详细的迁移步骤请参考 [迁移指南](migration_guide.md)。
