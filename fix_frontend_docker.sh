#!/bin/bash

# 修复前端Docker编译错误的脚本

echo "🔧 开始修复前端Docker编译错误..."

# 1. 停止并删除前端容器
echo "1. 停止并删除前端容器..."
docker stop monit-frontend 2>/dev/null || true
docker rm monit-frontend 2>/dev/null || true

# 2. 删除前端镜像
echo "2. 删除前端镜像..."
docker rmi monit-frontend 2>/dev/null || true

# 3. 清理Docker构建缓存
echo "3. 清理Docker构建缓存..."
docker builder prune -f

# 4. 重新构建前端镜像（无缓存）
echo "4. 重新构建前端镜像..."
docker-compose -f docker-compose.dev.yml build --no-cache frontend

# 5. 启动前端服务
echo "5. 启动前端服务..."
docker-compose -f docker-compose.dev.yml up -d frontend

# 6. 等待服务启动
echo "6. 等待前端服务启动..."
sleep 10

# 7. 检查服务状态
echo "7. 检查服务状态..."
docker ps | grep monit-frontend

# 8. 显示前端日志
echo "8. 显示前端日志（最后20行）..."
docker logs --tail 20 monit-frontend

echo "✅ 前端Docker修复完成！"
echo "📝 如果还有问题，请运行: docker logs -f monit-frontend 查看详细日志"
