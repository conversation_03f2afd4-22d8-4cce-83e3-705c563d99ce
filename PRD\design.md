# 电商商品监控系统设计文档

## 概述

电商商品监控系统是一个基于现有task-middleware和webui基础设施的专业化监控解决方案。系统通过定期爬取国际电商平台（1688、Amazon、MercadoLibre、eBay、AliExpress等）的商品信息，建立时间序列数据库，并提供智能分析和决策支持功能。

### 核心特性

- **多平台支持**：支持主要国际电商平台的商品监控
- **智能翻译**：集成LLM API实现多语言商品信息翻译
- **时间序列分析**：基于TimescaleDB的历史数据分析
- **实时预警**：智能价格和库存变化通知
- **可视化分析**：丰富的图表和趋势分析工具

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "电商监控系统 (独立部署)"
        subgraph "前端层"
            A[WebUI - React/TypeScript]
            B[移动端响应式界面]
        end
        
        subgraph "API网关层"
            C[FastAPI Gateway]
            D[认证中间件]
            E[限流中间件]
        end
        
        subgraph "业务服务层"
            F[商品管理服务]
            G[监控任务服务]
            H[数据分析服务]
            I[预警服务]
            J[平台配置服务]
        end
        
        subgraph "存储层"
            K[TimescaleDB - 时序数据]
            L[PostgreSQL - 配置数据]
            M[Redis - 缓存]
        end
    end
    
    subgraph "爬虫服务系统 (独立部署)"
        N[Task Middleware API]
        O[Crawl4AI Workers]
        P[Redis - 任务队列]
    end
    
    subgraph "翻译服务系统 (独立部署)"
        Q[Translation Service API]
        R[翻译Workers]
    end
    
    subgraph "外部服务"
        S[LLM API - OpenAI/Claude]
        T[电商平台 - 1688/Amazon等]
        U[通知服务 - 邮件/短信]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    D --> F
    E --> F
    F --> G
    G --> H
    H --> I
    F --> J
    
    G -.->|HTTP API| N
    G -.->|HTTP API| Q
    I -.->|HTTP API| U
    
    N --> O
    O --> T
    Q --> R
    R --> S
    
    F --> L
    G --> K
    H --> K
    I --> M
```

### 技术栈

- **前端**: React 18, TypeScript, Redux Toolkit, Ant Design, ECharts
- **后端**: Python 3.11, FastAPI, Pydantic, SQLAlchemy
- **任务处理**: Celery, Redis, Crawl4AI
- **数据库**: TimescaleDB, PostgreSQL, Redis
- **监控**: Prometheus, Grafana
- **部署**: Docker, Docker Compose

## 组件设计

### 1. 商品管理服务 (ProductService)

负责商品URL的管理和配置。

```python
class ProductService:
    """商品管理服务"""
    
    async def import_products_from_excel(self, file_data: bytes) -> ImportResult
    async def add_product_url(self, url: str, metadata: ProductMetadata) -> Product
    async def update_product(self, product_id: str, updates: ProductUpdate) -> Product
    async def delete_product(self, product_id: str) -> bool
    async def get_products(self, filters: ProductFilter) -> List[Product]
    async def categorize_products(self, products: List[Product]) -> Dict[str, List[Product]]
```

### 2. 监控任务服务 (MonitoringService)

管理监控任务的创建、调度和执行，通过API调用外部爬虫服务。

```python
class MonitoringService:
    """监控任务服务"""
    
    def __init__(self, crawler_client: CrawlerApiClient, translation_client: TranslationApiClient):
        self.crawler_client = crawler_client
        self.translation_client = translation_client
    
    async def create_monitoring_task(self, product: Product, schedule: Schedule) -> MonitoringTask
    async def schedule_batch_monitoring(self, products: List[Product]) -> BatchMonitoringResult
    async def execute_monitoring_task(self, task_id: str) -> TaskResult
    async def get_task_status(self, task_id: str) -> TaskStatus
    async def cancel_monitoring_task(self, task_id: str) -> bool
    async def process_crawl_result(self, crawl_result: CrawlResult) -> ProcessedData

class CrawlerApiClient:
    """爬虫服务API客户端"""
    
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.client = httpx.AsyncClient()
    
    async def submit_crawl_task(self, urls: List[str], config: CrawlConfig) -> CrawlTaskResponse
    async def get_task_status(self, task_id: str) -> CrawlTaskStatus
    async def get_task_result(self, task_id: str) -> CrawlResult
    async def cancel_task(self, task_id: str) -> bool

class TranslationApiClient:
    """翻译服务API客户端"""
    
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.client = httpx.AsyncClient()
    
    async def translate_text(self, text: str, source_lang: str, target_lang: str) -> TranslationResult
    async def batch_translate(self, texts: List[str], source_lang: str, target_lang: str) -> List[TranslationResult]
    async def detect_language(self, text: str) -> LanguageDetectionResult
```

### 3. 平台配置服务 (PlatformConfigService)

管理不同电商平台的爬取配置。

```python
class PlatformConfigService:
    """平台配置服务"""
    
    async def get_platform_config(self, platform: str) -> PlatformConfig
    async def update_platform_config(self, platform: str, config: PlatformConfig) -> bool
    async def test_platform_config(self, config: PlatformConfig, test_url: str) -> TestResult
    async def create_platform_template(self, platform: str, template: ConfigTemplate) -> bool
```

### 4. 翻译服务 (TranslationService)

处理多语言商品信息的翻译，支持多种LLM API提供商。

```python
class TranslationService:
    """翻译服务"""
    
    async def translate_product_info(self, product_data: ProductData, target_lang: str) -> TranslatedProductData
    async def batch_translate(self, products: List[ProductData], target_lang: str) -> List[TranslatedProductData]
    async def detect_language(self, text: str) -> LanguageDetectionResult
    async def get_translation_quality_score(self, original: str, translated: str) -> float

class LLMApiClient:
    """LLM API客户端基类"""
    
    async def translate(self, text: str, source_lang: str, target_lang: str, prompt_template: str) -> str
    async def batch_translate(self, texts: List[str], source_lang: str, target_lang: str) -> List[str]
    async def health_check(self) -> bool

class OpenAIClient(LLMApiClient):
    """OpenAI API客户端"""
    
    def __init__(self, api_key: str, base_url: str, model: str):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.client = AsyncOpenAI(api_key=api_key, base_url=base_url)

class ClaudeClient(LLMApiClient):
    """Claude API客户端"""
    
    def __init__(self, api_key: str, base_url: str, model: str):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model

class LocalLLMClient(LLMApiClient):
    """本地部署LLM客户端"""
    
    def __init__(self, base_url: str, model: str):
        self.base_url = base_url
        self.model = model
```

### 5. 数据分析服务 (AnalyticsService)

提供数据分析和趋势预测功能。

```python
class AnalyticsService:
    """数据分析服务"""
    
    async def get_price_trend(self, product_id: str, time_range: TimeRange) -> PriceTrend
    async def analyze_market_opportunity(self, category: str) -> MarketAnalysis
    async def generate_purchase_recommendations(self, criteria: PurchaseCriteria) -> List[Recommendation]
    async def calculate_profit_margin(self, product_id: str, cost_price: float) -> ProfitAnalysis
```

### 6. 预警服务 (AlertService)

管理智能预警和通知。

```python
class AlertService:
    """预警服务"""
    
    async def create_alert_rule(self, rule: AlertRule) -> str
    async def evaluate_alert_conditions(self, product_data: ProductData) -> List[Alert]
    async def send_alert_notification(self, alert: Alert, channels: List[NotificationChannel]) -> bool
    async def get_alert_history(self, product_id: str) -> List[AlertHistory]
```

## 数据模型

### 核心数据模型

```python
# 商品模型
class Product(BaseModel):
    id: str
    url: str
    platform: str
    name: str
    category: str
    status: ProductStatus
    monitoring_frequency: int  # 监控频率（小时）
    created_at: datetime
    updated_at: datetime
    metadata: Dict[str, Any]

# 商品数据模型
class ProductData(BaseModel):
    product_id: str
    timestamp: datetime
    title: str
    title_translated: Optional[str]
    price: Optional[float]
    currency: str
    stock_quantity: Optional[int]
    sales_count: Optional[int]
    rating: Optional[float]
    review_count: Optional[int]
    description: Optional[str]
    description_translated: Optional[str]
    images: List[str]
    specifications: Dict[str, Any]
    raw_data: Dict[str, Any]

# 平台配置模型
class PlatformConfig(BaseModel):
    platform: str
    name: str
    base_url: str
    selectors: Dict[str, str]  # CSS选择器配置
    headers: Dict[str, str]
    proxy_settings: Optional[ProxyConfig]
    rate_limit: RateLimit
    translation_config: TranslationConfig
    created_at: datetime
    updated_at: datetime

# 翻译配置模型
class TranslationConfig(BaseModel):
    enabled: bool
    llm_provider: str  # 'openai', 'claude', 'local', etc.
    api_base_url: str
    api_key: Optional[str]  # 本地部署可能不需要
    model_name: str
    prompt_template: str
    batch_size: int
    timeout: int
    max_retries: int
    retry_delay: float
    rate_limit: RateLimit

# LLM提供商配置
class LLMProviderConfig(BaseModel):
    provider_type: str
    name: str
    api_base_url: str
    api_key: Optional[str]
    available_models: List[str]
    default_model: str
    rate_limit: RateLimit
    timeout: int
    health_check_url: str

# 速率限制配置
class RateLimit(BaseModel):
    requests_per_minute: int
    requests_per_hour: int
    tokens_per_minute: Optional[int]
    concurrent_requests: int
```

### 时间序列数据模型

```sql
-- 商品历史数据表 (TimescaleDB)
CREATE TABLE product_history (
    time TIMESTAMPTZ NOT NULL,
    product_id TEXT NOT NULL,
    platform TEXT NOT NULL,
    title TEXT,
    title_translated TEXT,
    price DECIMAL(10,2),
    currency TEXT,
    stock_quantity INTEGER,
    sales_count INTEGER,
    rating DECIMAL(3,2),
    review_count INTEGER,
    change_type TEXT, -- 'price_change', 'stock_change', 'new_data'
    change_value DECIMAL(10,2),
    raw_data JSONB
);

-- 创建时间序列超表
SELECT create_hypertable('product_history', 'time');

-- 创建索引
CREATE INDEX idx_product_history_product_id ON product_history (product_id, time DESC);
CREATE INDEX idx_product_history_platform ON product_history (platform, time DESC);
```

## 接口设计

### RESTful API 设计

#### 电商监控系统 API

```python
# 商品管理 API
@router.post("/products/import")
async def import_products(file: UploadFile) -> ImportResult

@router.get("/products")
async def get_products(filters: ProductFilter = Depends()) -> PaginatedResponse[Product]

@router.post("/products")
async def create_product(product: ProductCreate) -> Product

@router.put("/products/{product_id}")
async def update_product(product_id: str, updates: ProductUpdate) -> Product

@router.delete("/products/{product_id}")
async def delete_product(product_id: str) -> SuccessResponse

# 监控任务 API
@router.post("/monitoring/tasks")
async def create_monitoring_task(task: MonitoringTaskCreate) -> MonitoringTask

@router.get("/monitoring/tasks/{task_id}")
async def get_monitoring_task(task_id: str) -> MonitoringTask

@router.post("/monitoring/batch")
async def start_batch_monitoring(request: BatchMonitoringRequest) -> BatchMonitoringResult

@router.put("/monitoring/tasks/{task_id}/cancel")
async def cancel_monitoring_task(task_id: str) -> SuccessResponse

@router.get("/monitoring/tasks/{task_id}/status")
async def get_task_status(task_id: str) -> TaskStatusResponse

# 数据分析 API
@router.get("/analytics/price-trend/{product_id}")
async def get_price_trend(product_id: str, time_range: TimeRange = Depends()) -> PriceTrend

@router.get("/analytics/market-analysis")
async def get_market_analysis(category: str, time_range: TimeRange = Depends()) -> MarketAnalysis

@router.get("/analytics/recommendations")
async def get_purchase_recommendations(criteria: PurchaseCriteria = Depends()) -> List[Recommendation]

# 平台配置 API
@router.get("/platforms")
async def get_platforms() -> List[Platform]

@router.get("/platforms/{platform}/config")
async def get_platform_config(platform: str) -> PlatformConfig

@router.put("/platforms/{platform}/config")
async def update_platform_config(platform: str, config: PlatformConfigUpdate) -> PlatformConfig

@router.post("/platforms/{platform}/test")
async def test_platform_config(platform: str, test_request: ConfigTestRequest) -> TestResult
```

#### Task Middleware API 集成详情

基于现有task-middleware系统（版本1.0.0）的完整API接口，电商监控系统将调用以下API：

##### 1. 批量任务提交 API

**端点**: `POST /api/v1/tasks/batch`

**认证**: 支持两种认证方式
- API Key认证: `X-API-Key: your-api-key`
- Bearer Token认证: `Authorization: Bearer your-token`

**请求限制**:
- 每批次最多1000个URL
- 查询指令最长10000字符
- 批次名称最长100字符

**请求示例**:
```json
{
    "urls": [
        "https://www.1688.com/product/123456.html",
        "https://www.amazon.com/dp/B08N5WRWNW",
        "https://articulo.mercadolibre.com.ar/MLA-123456"
    ],
    "q": "Extract comprehensive e-commerce product information including title, price, currency, stock quantity, sales count, rating, review count, specifications, images, and seller information. Focus on structured data extraction for price monitoring.",
    "schema_str": "{\"title\": \"string\", \"price\": \"number\", \"currency\": \"string\", \"stock_quantity\": \"number\", \"sales_count\": \"number\", \"rating\": \"number\", \"review_count\": \"number\", \"specifications\": \"object\", \"images\": \"array\", \"seller_info\": \"object\"}",
    "cache": true,
    "priority": "medium",
    "batch_name": "E-commerce Product Monitoring - 2024-01-01",
    "batch_description": "Daily monitoring batch for tracked products",
    "callback_url": "http://ecommerce-monitoring-api:8000/api/callbacks/crawl-complete",
    "max_concurrent_tasks": 10,
    "batch_timeout": 3600
}
```

**响应示例**:
```json
{
    "success": true,
    "batch_id": "batch_1234567890",
    "task_ids": [
        "task_abc123",
        "task_def456",
        "task_ghi789"
    ],
    "total_tasks": 3,
    "valid_tasks": 3,
    "invalid_tasks": 0,
    "invalid_urls": [],
    "created_at": "2025-01-01T00:00:00Z",
    "message": "Successfully created batch with 3 valid tasks"
}

##### 2. 任务状态查询 API

**端点**: `GET /api/v1/tasks/{task_id}`

**参数**:
- `task_id`: 任务唯一标识符，格式为 `task_[a-zA-Z0-9]+`

**响应示例**:
```json
{
    "id": "task_abc123",
    "batch_id": "batch_1234567890",
    "url": "https://www.1688.com/product/123456.html",
    "status": "completed",
    "priority": "medium",
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:01:30Z",
    "started_at": "2025-01-01T00:00:30Z",
    "completed_at": "2025-01-01T00:01:30Z",
    "retry_count": 0,
    "max_retries": 3,
    "crawl4ai_job_id": "llm_1234567890",
    "processing_duration": 5.2,
    "result": {
        "title": "高品质电子产品",
        "price": 299.99,
        "currency": "CNY",
        "stock_quantity": 1500,
        "sales_count": 2847,
        "rating": 4.8,
        "review_count": 156,
        "specifications": {
            "brand": "优质品牌",
            "model": "XYZ-2024",
            "warranty": "1年保修"
        },
        "images": [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
        ],
        "seller_info": {
            "name": "优质供应商",
            "rating": 4.9,
            "location": "广东省"
        }
    },
    "error": null,
    "config": {
        "cache": true,
        "priority": "medium"
    }
}

##### 3. 批次状态查询 API

**端点**: `GET /api/v1/tasks/batch/{batch_id}`

**参数**:
- `batch_id`: 批次唯一标识符，格式为 `batch_[a-zA-Z0-9]+`
- `page`: 页码（从1开始），默认1
- `size`: 每页任务数量（1-100），默认20

**响应示例**:
```json
{
    "batch_id": "batch_1234567890",
    "batch_info": {
        "id": "batch_1234567890",
        "name": "E-commerce Product Monitoring - 2025-01-01",
        "description": "Daily monitoring batch for tracked products",
        "status": "completed",
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:05:00Z",
        "started_at": "2025-01-01T00:00:30Z",
        "completed_at": "2025-01-01T00:05:00Z",
        "processing_duration": 270.0,
        "total_tasks": 10,
        "pending_tasks": 0,
        "processing_tasks": 0,
        "completed_tasks": 8,
        "failed_tasks": 2,
        "permanently_failed_tasks": 0,
        "success_rate": 0.8,
        "failure_rate": 0.2,
        "completion_rate": 1.0
    },
    "tasks": [
        {
            "id": "task_abc123",
            "batch_id": "batch_1234567890",
            "url": "https://www.1688.com/product/123456.html",
            "status": "completed",
            "priority": "medium",
            "created_at": "2025-01-01T00:00:00Z",
            "updated_at": "2025-01-01T00:01:30Z",
            "started_at": "2025-01-01T00:00:30Z",
            "completed_at": "2025-01-01T00:01:30Z",
            "retry_count": 0,
            "max_retries": 3,
            "processing_duration": 5.2
        }
    ],
    "pagination": {
        "page": 1,
        "size": 20,
        "total": 10,
        "pages": 1,
        "has_next": false,
        "has_prev": false
    }
}

##### 4. 任务结果获取 API

**端点**: `GET /api/v1/tasks/{task_id}/result`

**响应根据任务状态而异**:

**已完成任务**:
```json
{
    "task_id": "task_abc123",
    "status": "completed",
    "completed_at": "2025-01-01T00:05:00Z",
    "processing_duration": 4.5,
    "result": {
        "title": "高品质电子产品",
        "price": 299.99,
        "currency": "CNY",
        "stock_quantity": 1500,
        "sales_count": 2847,
        "rating": 4.8,
        "review_count": 156,
        "specifications": {
            "brand": "优质品牌",
            "model": "XYZ-2024"
        },
        "images": ["https://example.com/image1.jpg"],
        "seller_info": {
            "name": "优质供应商",
            "rating": 4.9
        }
    },
    "crawl4ai_job_id": "llm_1234567890"
}
```

**失败任务**:
```json
{
    "task_id": "task_def456",
    "status": "failed",
    "failed_at": "2025-01-01T00:10:00Z",
    "retry_count": 3,
    "max_retries": 3,
    "error": {
        "type": "NetworkError",
        "message": "连接超时",
        "code": "TIMEOUT_ERROR",
        "details": {
            "url": "https://example.com/timeout",
            "timeout_duration": 30
        },
        "timestamp": "2025-01-01T00:10:00Z"
    },
    "crawl4ai_job_id": "llm_1234567891"
}
```

##### 5. 系统状态查询 API

**端点**: `GET /api/v1/system/status`

**响应示例**:
```json
{
    "status": "healthy",
    "timestamp": "2025-01-01T00:00:00Z",
    "version": "1.0.0",
    "uptime": "1d 2h 30m 45s",
    "services": {
        "redis": {
            "status": "healthy",
            "response_time": 0.5,
            "message": "Redis connection successful"
        },
        "task_manager": {
            "status": "healthy",
            "response_time": 1.2,
            "message": "Task manager operational"
        },
        "crawl4ai": {
            "status": "healthy",
            "response_time": 2.1,
            "message": "Crawl4AI backend available"
        }
    },
    "resources": {
        "cpu_percent": 25.5,
        "memory_percent": 45.2,
        "memory_info": {
            "rss": 536870912,
            "vms": **********
        },
        "open_files": 25,
        "connections": 10,
        "threads": 8
    }
}

##### 6. 系统指标查询 API

**端点**: `GET /api/v1/system/metrics`

**响应示例**:
```json
{
    "timestamp": "2025-01-01T00:00:00Z",
    "task_metrics": {
        "total_batches_created": 150,
        "total_tasks_created": 1500,
        "total_tasks_completed": 1350,
        "total_tasks_failed": 50,
        "current_processing_tasks": 10,
        "average_processing_time": 45.5
    },
    "queue_metrics": {
        "queue_lengths": {
            "high": 5,
            "medium": 20,
            "low": 15,
            "retry": 3
        },
        "total_pending": 43,
        "total_processing": 10,
        "total_dead_letter": 2
    },
    "system_metrics": {
        "cpu_percent": 25.5,
        "memory_percent": 45.2,
        "memory_info": {
            "rss": 536870912,
            "vms": **********,
            "shared": 67108864
        },
        "io_counters": {
            "read_count": 1000,
            "write_count": 500,
            "read_bytes": 10485760,
            "write_bytes": 5242880
        },
        "open_files": 25,
        "connections": 10,
        "threads": 8
    },
    "performance_metrics": {
        "uptime_seconds": 86400,
        "redis_metrics": {
            "connected_clients": 5,
            "used_memory": 2621440,
            "used_memory_human": "2.5M",
            "total_commands_processed": 10000,
            "keyspace_hits": 8500,
            "keyspace_misses": 1500
        }
    }
}

##### 7. 队列统计查询 API

**端点**: `GET /api/v1/system/queues`

**响应示例**:
```json
{
    "timestamp": "2025-01-01T00:00:00Z",
    "queue_lengths": {
        "high": 5,
        "medium": 20,
        "low": 15,
        "retry": 3,
        "dead_letter": 2
    },
    "queue_health": {
        "manager_health": {
            "running": true,
            "background_tasks_count": 3
        },
        "queue_status": {
            "high": {
                "healthy": true,
                "length": 5,
                "last_processed": "2025-01-01T00:00:00Z"
            },
            "medium": {
                "healthy": true,
                "length": 20,
                "last_processed": "2025-01-01T00:00:00Z"
            }
        }
    },
    "processing_stats": {
        "total_pending": 43,
        "total_processing": 10,
        "total_dead_letter": 2,
        "manager_stats": {
            "active_workers": 5,
            "max_workers": 10
        }
    },
    "retry_stats": {
        "total_retries": 25,
        "retry_success_rate": 0.8,
        "average_retry_delay": 5.5
    }
}

##### 8. 健康检查 API

**端点**: `GET /health`

**响应示例**:
```json
{
    "status": "healthy",
    "timestamp": "2025-01-01T00:00:00Z",
    "version": "1.0.0",
    "uptime": "1d 2h 30m 45s",
    "services": {
        "api": "healthy",
        "redis": "healthy",
        "task_manager": "healthy",
        "queue_manager": "healthy",
        "crawl4ai": "unknown"
    },
    "health_score": 95,
    "active_alerts": 0,
    "monitoring_active": true
}
```

##### 9. 认证相关 API

**生成访问令牌**: `POST /api/v1/auth/token`
```json
{
    "user_id": "ecommerce_monitor",
    "role": "user"
}
```

**验证令牌**: `POST /api/v1/auth/validate`

##### 10. WebUI 专用 API

**WebSocket连接**: `WS /webui/ws/{session_id}`
- 支持实时数据推送
- 任务状态更新通知
- 系统状态变化通知

**仪表板数据**: `GET /webui/dashboard`
**实时指标**: `GET /webui/dashboard/realtime`
**高级任务搜索**: `GET /webui/tasks`

##### 11. Prometheus 指标

**端点**: `GET /metrics`
**格式**: Prometheus文本格式
**内容**: 任务处理指标、系统资源指标、队列统计等

#### Task Middleware API 客户端实现

电商监控系统中的CrawlerApiClient完整实现：

```python
import json
import httpx
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

class CrawlerApiClient:
    """Task Middleware API客户端"""
    
    def __init__(self, base_url: str, api_key: str, auth_type: str = "bearer"):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.auth_type = auth_type.lower()
        
        # 设置认证头
        if self.auth_type == "bearer":
            auth_header = {"Authorization": f"Bearer {api_key}"}
        else:  # api_key
            auth_header = {"X-API-Key": api_key}
        
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            headers={
                **auth_header,
                "Content-Type": "application/json",
                "User-Agent": "EcommerceMonitor/1.0.0"
            }
        )
    
    async def submit_product_monitoring_batch(
        self, 
        products: List[Product], 
        monitoring_config: MonitoringConfig
    ) -> BatchTaskResponse:
        """提交商品监控批次任务"""
        
        # 构建URL列表
        urls = [product.url for product in products]
        
        # 根据平台生成专门的提取指令
        extraction_query = self._build_extraction_query(products)
        schema_str = self._build_extraction_schema()
        
        # 构建请求数据
        request_data = {
            "urls": urls,
            "q": extraction_query,
            "schema_str": schema_str,
            "cache": True,
            "priority": monitoring_config.priority,
            "batch_name": f"Product Monitoring - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            "batch_description": f"Monitoring {len(products)} products across multiple platforms",
            "callback_url": f"{monitoring_config.callback_base_url}/api/callbacks/crawl-complete",
            "max_concurrent_tasks": monitoring_config.max_concurrent_tasks,
            "batch_timeout": monitoring_config.batch_timeout
        }
        
        # 发送请求
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/batch",
            json=request_data
        )
        response.raise_for_status()
        
        return BatchTaskResponse(**response.json())
    
    async def get_task_status(self, task_id: str) -> TaskResponse:
        """获取任务状态"""
        response = await self.client.get(f"{self.base_url}/api/v1/tasks/{task_id}")
        response.raise_for_status()
        
        return TaskResponse(**response.json())
    
    async def get_task_result(self, task_id: str) -> TaskResult:
        """获取任务结果"""
        response = await self.client.get(f"{self.base_url}/api/v1/tasks/{task_id}/result")
        response.raise_for_status()
        
        result_data = response.json()
        
        # 根据状态返回不同的结果类型
        if result_data["status"] == "completed":
            return CompletedTaskResult(**result_data)
        elif result_data["status"] in ["failed", "permanently_failed"]:
            return FailedTaskResult(**result_data)
        else:
            return ProcessingTaskResult(**result_data)
    
    async def get_batch_status(self, batch_id: str, page: int = 1, size: int = 20) -> BatchTasksResponse:
        """获取批次状态"""
        params = {"page": page, "size": size}
        response = await self.client.get(
            f"{self.base_url}/api/v1/tasks/batch/{batch_id}",
            params=params
        )
        response.raise_for_status()
        
        return BatchTasksResponse(**response.json())
    
    async def get_system_status(self) -> SystemStatusResponse:
        """获取系统状态"""
        response = await self.client.get(f"{self.base_url}/api/v1/system/status")
        response.raise_for_status()
        
        return SystemStatusResponse(**response.json())
    
    async def get_system_metrics(self) -> SystemMetricsResponse:
        """获取系统指标"""
        response = await self.client.get(f"{self.base_url}/api/v1/system/metrics")
        response.raise_for_status()
        
        return SystemMetricsResponse(**response.json())
    
    async def get_queue_statistics(self) -> QueueStatisticsResponse:
        """获取队列统计"""
        response = await self.client.get(f"{self.base_url}/api/v1/system/queues")
        response.raise_for_status()
        
        return QueueStatisticsResponse(**response.json())
    
    async def get_health_status(self) -> HealthResponse:
        """获取健康状态"""
        response = await self.client.get(f"{self.base_url}/health")
        response.raise_for_status()
        
        return HealthResponse(**response.json())
    
    async def generate_token(self, user_id: str, role: str = "user") -> TokenResponse:
        """生成访问令牌"""
        request_data = {
            "user_id": user_id,
            "role": role
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/v1/auth/token",
            json=request_data
        )
        response.raise_for_status()
        
        return TokenResponse(**response.json())
    
    async def validate_token(self) -> TokenValidationResponse:
        """验证当前令牌"""
        response = await self.client.post(f"{self.base_url}/api/v1/auth/validate")
        response.raise_for_status()
        
        return TokenValidationResponse(**response.json())
    
    async def clear_dead_letter_queue(self) -> OperationResponse:
        """清空死信队列"""
        response = await self.client.post(f"{self.base_url}/api/v1/system/queues/clear-dead-letter")
        response.raise_for_status()
        
        return OperationResponse(**response.json())
    
    async def reprocess_dead_letter_tasks(self, max_count: int = 100) -> OperationResponse:
        """重新处理死信队列任务"""
        params = {"max_count": max_count}
        response = await self.client.post(
            f"{self.base_url}/api/v1/system/queues/reprocess-dead-letter",
            params=params
        )
        response.raise_for_status()
        
        return OperationResponse(**response.json())
    
    async def get_prometheus_metrics(self) -> str:
        """获取Prometheus格式指标"""
        response = await self.client.get(f"{self.base_url}/metrics")
        response.raise_for_status()
        
        return response.text
    
    def _build_extraction_query(self, products: List[Product]) -> str:
        """构建提取查询指令"""
        platforms = set(product.platform for product in products)
        
        base_query = """
        Extract comprehensive e-commerce product information from the webpage.
        Focus on structured data extraction and ensure accuracy for price monitoring.
        
        Required information:
        - Product title/name (original language)
        - Current price and currency
        - Stock/inventory quantity
        - Sales count or popularity metrics
        - Customer rating and review count
        - Product specifications and attributes
        - Product images (URLs)
        - Seller/brand information
        - Product availability status
        - Shipping information (if available)
        
        Platform-specific considerations:
        """
        
        if "1688" in platforms:
            base_query += "\n- For 1688: Extract wholesale prices, MOQ (minimum order quantity), supplier information, and trade assurance status"
        if "amazon" in platforms:
            base_query += "\n- For Amazon: Extract ASIN, Prime eligibility, variant information, and fulfillment method"
        if "mercadolibre" in platforms:
            base_query += "\n- For MercadoLibre: Extract shipping information, seller reputation, and payment methods"
        if "ebay" in platforms:
            base_query += "\n- For eBay: Extract auction/buy-it-now status, shipping costs, and seller feedback"
        if "aliexpress" in platforms:
            base_query += "\n- For AliExpress: Extract shipping time, buyer protection, and store information"
        
        base_query += """
        
        Important notes:
        - Extract numeric values as numbers, not strings
        - Preserve original currency symbols and codes
        - Handle multiple price formats (retail, wholesale, bulk)
        - Extract all available product variants
        - Focus on data that changes frequently (price, stock, sales count)
        """
        
        return base_query.strip()
    
    def _build_extraction_schema(self) -> str:
        """构建提取数据结构"""
        schema = {
            "title": "string",
            "price": "number",
            "currency": "string",
            "original_price": "number",
            "discount_price": "number",
            "stock_quantity": "number",
            "sales_count": "number",
            "rating": "number",
            "review_count": "number",
            "availability": "string",
            "specifications": {
                "brand": "string",
                "model": "string",
                "category": "string",
                "attributes": "object"
            },
            "images": "array",
            "seller_info": {
                "name": "string",
                "rating": "number",
                "location": "string",
                "verification_status": "string"
            },
            "shipping_info": {
                "cost": "number",
                "time": "string",
                "methods": "array"
            },
            "platform_specific": {
                "product_id": "string",
                "category_path": "array",
                "tags": "array",
                "promotion_info": "object"
            }
        }
        
        return json.dumps(schema, ensure_ascii=False)
    
    async def close(self):
        """关闭客户端连接"""
        await self.client.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


# 响应数据模型
class BatchTaskResponse(BaseModel):
    success: bool
    batch_id: str
    task_ids: List[str]
    total_tasks: int
    valid_tasks: int
    invalid_tasks: int
    invalid_urls: List[str]
    created_at: str
    message: str

class TaskResponse(BaseModel):
    id: str
    batch_id: str
    url: str
    status: str
    priority: str
    created_at: str
    updated_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    retry_count: int
    max_retries: int
    crawl4ai_job_id: Optional[str] = None
    processing_duration: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None

class CompletedTaskResult(BaseModel):
    task_id: str
    status: str
    completed_at: str
    processing_duration: Optional[float]
    result: Dict[str, Any]
    crawl4ai_job_id: Optional[str] = None

class FailedTaskResult(BaseModel):
    task_id: str
    status: str
    failed_at: str
    retry_count: int
    max_retries: int
    error: Dict[str, Any]
    crawl4ai_job_id: Optional[str] = None

class ProcessingTaskResult(BaseModel):
    task_id: str
    status: str
    started_at: str
    processing_duration: Optional[float] = None
    progress: Optional[Dict[str, Any]] = None
    crawl4ai_job_id: Optional[str] = None

# 其他响应模型...
```

#### WebSocket 实时通信集成

电商监控系统将集成Task Middleware的WebSocket功能，实现实时数据推送：

```python
class TaskMiddlewareWebSocketClient:
    """Task Middleware WebSocket客户端"""
    
    def __init__(self, ws_url: str, session_id: str, user_id: Optional[str] = None):
        self.ws_url = ws_url
        self.session_id = session_id
        self.user_id = user_id
        self.websocket = None
        self.event_handlers = {}
        self.is_connected = False
    
    async def connect(self):
        """建立WebSocket连接"""
        url = f"{self.ws_url}/webui/ws/{self.session_id}"
        if self.user_id:
            url += f"?user_id={self.user_id}"
        
        self.websocket = await websockets.connect(url)
        self.is_connected = True
        
        # 启动消息监听
        asyncio.create_task(self._listen_messages())
        
        # 订阅相关事件
        await self.subscribe_events([
            "TASK_STATUS_CHANGED",
            "BATCH_STATUS_CHANGED", 
            "STATISTICS_UPDATED",
            "SYSTEM_ALERT"
        ])
    
    async def subscribe_events(self, event_types: List[str]):
        """订阅事件"""
        message = {
            "type": "subscribe",
            "events": event_types
        }
        await self._send_message(message)
    
    async def request_dashboard_update(self):
        """请求仪表板数据更新"""
        message = {
            "type": "request_dashboard_update"
        }
        await self._send_message(message)
    
    def on_event(self, event_type: str, handler):
        """注册事件处理器"""
        self.event_handlers[event_type] = handler
    
    async def _listen_messages(self):
        """监听WebSocket消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self._handle_message(data)
        except websockets.exceptions.ConnectionClosed:
            self.is_connected = False
            logger.info("WebSocket connection closed")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
            self.is_connected = False
    
    async def _handle_message(self, data: Dict[str, Any]):
        """处理接收到的消息"""
        message_type = data.get("type")
        
        if message_type == "pong":
            # 心跳响应
            logger.debug("Received pong from server")
        elif message_type in self.event_handlers:
            # 调用注册的事件处理器
            handler = self.event_handlers[message_type]
            await handler(data.get("data", {}))
        else:
            logger.debug(f"Unhandled message type: {message_type}")
    
    async def _send_message(self, message: Dict[str, Any]):
        """发送消息"""
        if self.websocket and self.is_connected:
            await self.websocket.send(json.dumps(message))
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
        self.is_connected = False


# 在监控服务中使用WebSocket
class MonitoringService:
    def __init__(self):
        self.ws_client = None
    
    async def initialize_realtime_updates(self):
        """初始化实时更新"""
        self.ws_client = TaskMiddlewareWebSocketClient(
            ws_url="ws://task-middleware:11238",
            session_id=f"ecommerce_monitor_{uuid.uuid4()}",
            user_id="ecommerce_system"
        )
        
        # 注册事件处理器
        self.ws_client.on_event("TASK_STATUS_CHANGED", self._handle_task_status_change)
        self.ws_client.on_event("BATCH_STATUS_CHANGED", self._handle_batch_status_change)
        self.ws_client.on_event("STATISTICS_UPDATED", self._handle_statistics_update)
        
        await self.ws_client.connect()
    
    async def _handle_task_status_change(self, data: Dict[str, Any]):
        """处理任务状态变化"""
        task_id = data.get("task_id")
        new_status = data.get("status")
        
        # 更新本地任务状态
        await self._update_local_task_status(task_id, new_status)
        
        # 如果任务完成，处理结果
        if new_status == "completed":
            await self._process_completed_task(task_id)
    
    async def _handle_batch_status_change(self, data: Dict[str, Any]):
        """处理批次状态变化"""
        batch_id = data.get("batch_id")
        new_status = data.get("status")
        
        # 更新批次状态
        await self._update_batch_status(batch_id, new_status)
        
        # 如果批次完成，生成报告
        if new_status == "completed":
            await self._generate_batch_report(batch_id)
```

#### 错误处理和重试策略

```python
class TaskMiddlewareErrorHandler:
    """Task Middleware错误处理器"""
    
    # 错误类型映射
    ERROR_TYPE_MAPPING = {
        "ValidationError": "request_validation_failed",
        "AuthenticationError": "authentication_failed", 
        "TaskNotFound": "task_not_found",
        "BatchNotFound": "batch_not_found",
        "RateLimitExceeded": "rate_limit_exceeded",
        "InternalServerError": "server_internal_error",
        "NetworkError": "network_connection_failed",
        "TimeoutError": "request_timeout"
    }
    
    # 可重试的错误类型
    RETRYABLE_ERRORS = {
        "network_connection_failed",
        "request_timeout", 
        "server_internal_error",
        "rate_limit_exceeded"
    }
    
    @classmethod
    def should_retry(cls, error_type: str) -> bool:
        """判断是否应该重试"""
        return error_type in cls.RETRYABLE_ERRORS
    
    @classmethod
    def get_retry_delay(cls, attempt: int, error_type: str) -> float:
        """计算重试延迟"""
        base_delay = 1.0
        
        if error_type == "rate_limit_exceeded":
            # 速率限制错误使用更长的延迟
            return min(60.0, base_delay * (3 ** attempt))
        else:
            # 其他错误使用指数退避
            return min(30.0, base_delay * (2 ** attempt))
    
    @classmethod
    async def handle_api_error(cls, response: httpx.Response) -> Exception:
        """处理API错误响应"""
        try:
            error_data = response.json()
            error_type = error_data.get("error", "UnknownError")
            error_message = error_data.get("message", "Unknown error occurred")
            
            # 映射错误类型
            mapped_error_type = cls.ERROR_TYPE_MAPPING.get(error_type, "unknown_error")
            
            # 创建自定义异常
            if response.status_code == 400:
                return ValidationError(error_message, error_type=mapped_error_type)
            elif response.status_code == 401:
                return AuthenticationError(error_message, error_type=mapped_error_type)
            elif response.status_code == 404:
                return ResourceNotFoundError(error_message, error_type=mapped_error_type)
            elif response.status_code == 429:
                return RateLimitError(error_message, error_type=mapped_error_type)
            elif response.status_code >= 500:
                return ServerError(error_message, error_type=mapped_error_type)
            else:
                return APIError(error_message, error_type=mapped_error_type, status_code=response.status_code)
                
        except json.JSONDecodeError:
            return APIError(f"HTTP {response.status_code}: {response.text}", error_type="unknown_error")


class RetryableApiClient:
    """支持重试的API客户端"""
    
    def __init__(self, base_client: CrawlerApiClient, max_retries: int = 3):
        self.base_client = base_client
        self.max_retries = max_retries
        self.error_handler = TaskMiddlewareErrorHandler()
    
    async def execute_with_retry(self, operation, *args, **kwargs):
        """执行带重试的操作"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
                
            except httpx.HTTPStatusError as e:
                # 处理HTTP错误
                api_error = await self.error_handler.handle_api_error(e.response)
                last_exception = api_error
                
                if not self.error_handler.should_retry(api_error.error_type):
                    raise api_error
                
                if attempt < self.max_retries:
                    delay = self.error_handler.get_retry_delay(attempt, api_error.error_type)
                    logger.warning(f"API call failed, retrying in {delay}s (attempt {attempt + 1}/{self.max_retries})")
                    await asyncio.sleep(delay)
                
            except (httpx.ConnectError, httpx.TimeoutException) as e:
                # 处理网络错误
                error_type = "network_connection_failed" if isinstance(e, httpx.ConnectError) else "request_timeout"
                last_exception = NetworkError(str(e), error_type=error_type)
                
                if attempt < self.max_retries:
                    delay = self.error_handler.get_retry_delay(attempt, error_type)
                    logger.warning(f"Network error, retrying in {delay}s (attempt {attempt + 1}/{self.max_retries})")
                    await asyncio.sleep(delay)
        
        # 所有重试都失败了
        raise last_exception


# 自定义异常类
class APIError(Exception):
    def __init__(self, message: str, error_type: str, status_code: int = None):
        super().__init__(message)
        self.error_type = error_type
        self.status_code = status_code

class ValidationError(APIError):
    pass

class AuthenticationError(APIError):
    pass

class ResourceNotFoundError(APIError):
    pass

class RateLimitError(APIError):
    pass

class ServerError(APIError):
    pass

class NetworkError(APIError):
    pass
```

#### 翻译服务 API

```python
# 单文本翻译 API
@router.post("/translate")
async def translate_text(request: TranslationRequest) -> TranslationResponse
"""
翻译单个文本
Request Body:
{
    "text": "Product title in English",
    "source_lang": "en",
    "target_lang": "zh",
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "prompt_template": "Translate the following e-commerce product information to Chinese: {text}"
}
Response:
{
    "translated_text": "英文产品标题",
    "source_lang": "en",
    "target_lang": "zh",
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "confidence": 0.95,
    "processing_time": 1.2
}
"""

# 批量翻译 API
@router.post("/translate/batch")
async def batch_translate(request: BatchTranslationRequest) -> BatchTranslationResponse
"""
批量翻译文本
Request Body:
{
    "texts": ["Product 1", "Product 2", "Product 3"],
    "source_lang": "en",
    "target_lang": "zh",
    "provider": "openai",
    "model": "gpt-3.5-turbo"
}
Response:
{
    "translations": [
        {
            "original": "Product 1",
            "translated": "产品1",
            "confidence": 0.95
        },
        {
            "original": "Product 2",
            "translated": "产品2",
            "confidence": 0.92
        }
    ],
    "total_count": 3,
    "success_count": 2,
    "failed_count": 1,
    "processing_time": 3.5
}
"""

# 语言检测 API
@router.post("/detect-language")
async def detect_language(request: LanguageDetectionRequest) -> LanguageDetectionResponse
"""
检测文本语言
Request Body:
{
    "text": "This is an English text"
}
Response:
{
    "detected_language": "en",
    "confidence": 0.98,
    "possible_languages": [
        {"language": "en", "confidence": 0.98},
        {"language": "es", "confidence": 0.02}
    ]
}
"""

# 翻译任务状态查询 API
@router.get("/tasks/{task_id}")
async def get_translation_task(task_id: str) -> TranslationTaskResponse
"""
获取翻译任务状态
Response:
{
    "id": "translation-task-uuid",
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z",
    "completed_at": "2024-01-01T00:01:00Z",
    "input": {
        "text": "Original text",
        "source_lang": "en",
        "target_lang": "zh"
    },
    "result": {
        "translated_text": "翻译文本",
        "confidence": 0.95
    },
    "error": null
}
"""

# 支持的语言列表 API
@router.get("/languages")
async def get_supported_languages() -> SupportedLanguagesResponse
"""
获取支持的语言列表
Response:
{
    "languages": [
        {"code": "en", "name": "English"},
        {"code": "zh", "name": "Chinese"},
        {"code": "es", "name": "Spanish"},
        {"code": "fr", "name": "French"}
    ]
}
"""

# LLM提供商配置 API
@router.get("/providers")
async def get_llm_providers() -> LLMProvidersResponse
"""
获取可用的LLM提供商
Response:
{
    "providers": [
        {
            "name": "openai",
            "display_name": "OpenAI",
            "models": ["gpt-3.5-turbo", "gpt-4"],
            "status": "available"
        },
        {
            "name": "claude",
            "display_name": "Claude",
            "models": ["claude-3-sonnet", "claude-3-haiku"],
            "status": "available"
        }
    ]
}
"""
```

#### API 数据模型

```python
# 爬虫任务相关模型
class BatchTaskRequest(BaseModel):
    urls: List[str]
    q: str  # LLM查询指令
    schema_str: Optional[str] = None  # JSON schema字符串
    cache: bool = True
    priority: TaskPriority = TaskPriority.MEDIUM
    batch_name: Optional[str] = None
    batch_description: Optional[str] = None
    callback_url: Optional[str] = None
    max_concurrent_tasks: Optional[int] = None
    batch_timeout: Optional[int] = None

class BatchTaskResponse(BaseModel):
    batch_id: str
    task_ids: List[str]
    total_tasks: int
    created_at: datetime

class TaskStatusResponse(BaseModel):
    id: str
    batch_id: str
    url: str
    status: TaskStatus
    priority: TaskPriority
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime]
    result: Optional[Dict[str, Any]]
    error: Optional[ErrorInfo]
    retry_count: int
    processing_duration: Optional[float]

# 翻译服务相关模型
class TranslationRequest(BaseModel):
    text: str
    source_lang: str
    target_lang: str
    provider: str = "openai"
    model: str = "gpt-3.5-turbo"
    prompt_template: Optional[str] = None

class TranslationResponse(BaseModel):
    translated_text: str
    source_lang: str
    target_lang: str
    provider: str
    model: str
    confidence: float
    processing_time: float

class BatchTranslationRequest(BaseModel):
    texts: List[str]
    source_lang: str
    target_lang: str
    provider: str = "openai"
    model: str = "gpt-3.5-turbo"
    prompt_template: Optional[str] = None

class BatchTranslationResponse(BaseModel):
    translations: List[TranslationResult]
    total_count: int
    success_count: int
    failed_count: int
    processing_time: float

class TranslationResult(BaseModel):
    original: str
    translated: str
    confidence: float
    error: Optional[str] = None
```

### WebSocket API 设计

```python
# 实时数据推送
class WebSocketManager:
    async def connect(self, websocket: WebSocket, client_id: str)
    async def disconnect(self, client_id: str)
    async def send_task_update(self, task_id: str, status: TaskStatus)
    async def send_price_alert(self, product_id: str, alert: PriceAlert)
    async def send_system_notification(self, notification: SystemNotification)
```

## 错误处理

### 错误类型定义

```python
class EcommerceMonitoringError(Exception):
    """基础异常类"""
    pass

class ProductNotFoundError(EcommerceMonitoringError):
    """商品未找到错误"""
    pass

class PlatformConfigError(EcommerceMonitoringError):
    """平台配置错误"""
    pass

class TranslationError(EcommerceMonitoringError):
    """翻译服务错误"""
    pass

class CrawlingError(EcommerceMonitoringError):
    """爬取错误"""
    pass

class DataValidationError(EcommerceMonitoringError):
    """数据验证错误"""
    pass
```

### 错误处理策略

1. **爬取失败处理**：
   - 自动重试机制（最多3次）
   - 降级策略（使用缓存数据）
   - 错误日志记录和告警

2. **翻译失败处理**：
   - 保留原文数据
   - 标记翻译状态
   - 支持手动重试

3. **数据质量问题**：
   - 数据验证和清洗
   - 异常数据标记
   - 质量报告生成

## 测试策略

### 单元测试

```python
# 商品服务测试
class TestProductService:
    async def test_import_products_from_excel(self)
    async def test_add_product_url(self)
    async def test_validate_product_url(self)

# 翻译服务测试
class TestTranslationService:
    async def test_translate_product_info(self)
    async def test_batch_translation(self)
    async def test_language_detection(self)

# 平台配置测试
class TestPlatformConfigService:
    async def test_get_platform_config(self)
    async def test_validate_config(self)
    async def test_config_template_creation(self)
```

### 集成测试

```python
# 端到端监控流程测试
class TestMonitoringWorkflow:
    async def test_complete_monitoring_cycle(self)
    async def test_batch_monitoring_with_translation(self)
    async def test_alert_generation_and_notification(self)

# 数据分析测试
class TestAnalyticsWorkflow:
    async def test_price_trend_analysis(self)
    async def test_market_opportunity_analysis(self)
    async def test_recommendation_generation(self)
```

### 性能测试

- **负载测试**：模拟大量商品同时监控
- **并发测试**：测试多用户同时访问
- **数据库性能测试**：时间序列数据查询性能
- **翻译服务性能测试**：批量翻译响应时间

## 部署架构

### 电商监控系统 - 开发环境配置

独立部署的电商监控系统，支持源码挂载和热重载：

```yaml
# ecommerce-monitoring/docker-compose.dev.yml
version: '3.8'

services:
  # Web UI (开发模式)
  webui:
    build: 
      context: ./webui
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - NODE_ENV=development
    volumes:
      - ./webui/src:/app/src:ro
      - ./webui/public:/app/public:ro
      - ./webui/package.json:/app/package.json:ro
      - ./webui/vite.config.ts:/app/vite.config.ts:ro
    command: npm run dev -- --host 0.0.0.0
    depends_on:
      - ecommerce-api

  # 电商监控 API 服务 (开发模式)
  ecommerce-api:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***************************************/ecommerce_monitoring
      - REDIS_URL=redis://redis:6379
      - CRAWLER_API_URL=http://host.docker.internal:8002
      - TRANSLATION_API_URL=http://host.docker.internal:8003
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - DEBUG=true
      - RELOAD=true
    volumes:
      - ./api:/app:rw
      - /app/__pycache__
      - /app/.pytest_cache
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app
    depends_on:
      - timescaledb
      - redis
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # TimescaleDB
  timescaledb:
    image: timescale/timescaledb:latest-pg14
    environment:
      - POSTGRES_DB=ecommerce_monitoring
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"

  # Redis (监控系统专用)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # Celery Worker (开发模式)
  celery-worker:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
    environment:
      - DATABASE_URL=***************************************/ecommerce_monitoring
      - REDIS_URL=redis://redis:6379
      - CRAWLER_API_URL=http://host.docker.internal:8002
      - TRANSLATION_API_URL=http://host.docker.internal:8003
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./api:/app:rw
      - /app/__pycache__
    command: watchmedo auto-restart --directory=/app --pattern="*.py" --recursive -- celery -A app.celery worker --loglevel=info --concurrency=2
    depends_on:
      - timescaledb
      - redis
      - ecommerce-api
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Celery Beat (开发模式)
  celery-beat:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
    environment:
      - DATABASE_URL=***************************************/ecommerce_monitoring
      - REDIS_URL=redis://redis:6379
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./api:/app:rw
      - /app/__pycache__
    command: watchmedo auto-restart --directory=/app --pattern="*.py" --recursive -- celery -A app.celery beat --loglevel=info
    depends_on:
      - timescaledb
      - redis
      - ecommerce-api

volumes:
  timescaledb_data:
  redis_data:

networks:
  default:
    name: ecommerce-monitoring-network
```

### 爬虫服务系统 - 开发环境配置

独立部署的爬虫服务系统：

```yaml
# crawler-service/docker-compose.dev.yml
version: '3.8'

services:
  # Task Middleware (开发模式)
  task-middleware:
    build:
      context: ./task-middleware
      dockerfile: Dockerfile.dev
    ports:
      - "8002:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - CRAWL4AI_URL=http://crawl4ai:8000
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./task-middleware:/app:rw
      - /app/__pycache__
      - /app/.pytest_cache
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app
    depends_on:
      - redis
      - crawl4ai

  # Crawl4AI 服务
  crawl4ai:
    image: crawl4ai/crawl4ai:latest
    ports:
      - "8001:8000"
    environment:
      - PYTHONPATH=/app
    volumes:
      - crawl4ai_cache:/app/cache

  # Redis (爬虫任务队列)
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"  # 使用不同端口避免冲突
    volumes:
      - crawler_redis_data:/data
    command: redis-server --appendonly yes

volumes:
  crawl4ai_cache:
  crawler_redis_data:

networks:
  default:
    name: crawler-service-network
```

### 翻译服务系统 - 开发环境配置

独立部署的翻译服务系统：

```yaml
# translation-service/docker-compose.dev.yml
version: '3.8'

services:
  # 翻译服务 (开发模式)
  translation-api:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
    ports:
      - "8003:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - DEBUG=true
      # LLM API 配置
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - LOCAL_LLM_URL=${LOCAL_LLM_URL}
    volumes:
      - ./api:/app:rw
      - /app/__pycache__
      - /app/.pytest_cache
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app
    depends_on:
      - redis

  # Redis (翻译缓存)
  redis:
    image: redis:7-alpine
    ports:
      - "6381:6379"  # 使用不同端口避免冲突
    volumes:
      - translation_redis_data:/data
    command: redis-server --appendonly yes

  # Celery Worker (翻译任务处理)
  celery-worker:
    build:
      context: ./api
      dockerfile: Dockerfile.dev
    environment:
      - REDIS_URL=redis://redis:6379
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - LOCAL_LLM_URL=${LOCAL_LLM_URL}
    volumes:
      - ./api:/app:rw
      - /app/__pycache__
    command: watchmedo auto-restart --directory=/app --pattern="*.py" --recursive -- celery -A app.celery worker --loglevel=info --concurrency=4
    depends_on:
      - redis
      - translation-api

volumes:
  translation_redis_data:

networks:
  default:
    name: translation-service-network
```

### 服务间通信配置

各服务通过HTTP API进行通信，配置示例：

```yaml
# .env 文件示例
# 电商监控系统配置
CRAWLER_API_URL=http://localhost:8002
CRAWLER_API_KEY=your-crawler-api-key
TRANSLATION_API_URL=http://localhost:8003
TRANSLATION_API_KEY=your-translation-api-key

# 爬虫服务配置
TASK_MIDDLEWARE_API_KEY=your-crawler-api-key

# 翻译服务配置
TRANSLATION_API_KEY=your-translation-api-key
OPENAI_API_KEY=your-openai-key
CLAUDE_API_KEY=your-claude-key
LOCAL_LLM_URL=http://localhost:8004
```

### 生产环境部署

生产环境中，各服务可以部署在不同的服务器上：

```yaml
# 生产环境配置示例
# ecommerce-monitoring/docker-compose.prod.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro

  ecommerce-api:
    image: ecommerce-monitoring-api:latest
    environment:
      - DATABASE_URL=***************************************/ecommerce_monitoring
      - REDIS_URL=redis://redis:6379
      - CRAWLER_API_URL=https://crawler-service.example.com
      - TRANSLATION_API_URL=https://translation-service.example.com
      - ENVIRONMENT=production
    restart: unless-stopped

  # 其他服务...
```

### 开发环境 Dockerfile 示例

```dockerfile
# ecommerce-api/Dockerfile.dev
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
COPY requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt

# 安装开发工具
RUN pip install watchdog[watchmedo]

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 开发模式启动命令 (在docker-compose中覆盖)
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### 扩展性考虑

1. **水平扩展**：
   - API服务支持多实例部署
   - Celery Worker可根据负载动态扩展
   - 数据库读写分离

2. **缓存策略**：
   - Redis缓存热点数据
   - CDN缓存静态资源
   - 应用层缓存查询结果

3. **监控和告警**：
   - Prometheus监控系统指标
   - Grafana可视化监控面板
   - 自定义告警规则

## 安全考虑

### 数据安全

1. **敏感数据加密**：
   - API密钥加密存储
   - 数据库连接加密
   - 传输层TLS加密

2. **访问控制**：
   - JWT认证机制
   - 基于角色的权限控制
   - API访问限流

3. **数据隐私**：
   - 个人信息脱敏
   - 数据访问日志记录
   - 符合GDPR要求

### 系统安全

1. **输入验证**：
   - URL格式验证
   - 文件上传安全检查
   - SQL注入防护

2. **网络安全**：
   - 防火墙配置
   - VPN访问控制
   - DDoS防护

## 性能优化

### 数据库优化

1. **TimescaleDB优化**：
   - 合理的时间分区策略
   - 压缩策略配置
   - 索引优化

2. **查询优化**：
   - 分页查询优化
   - 聚合查询优化
   - 缓存热点查询

### 应用优化

1. **异步处理**：
   - 异步IO操作
   - 任务队列处理
   - 批量操作优化

2. **资源管理**：
   - 连接池管理
   - 内存使用优化
   - CPU密集任务优化

## Redis 任务执行细节存储结构设计

为了实现任务执行过程的精确监控、调试和反查，系统在Redis中维护详细的任务执行状态和历史记录。

### 核心存储结构

#### 1. 监控任务主记录
```redis
# 键格式: ecommerce:task:{task_id}
# 类型: Hash
# TTL: 7天 (可配置)

HSET ecommerce:task:mon_20240101_001 
    "id" "mon_20240101_001"
    "batch_id" "batch_20240101_001"
    "product_id" "prod_12345"
    "product_url" "https://www.1688.com/product/123456.html"
    "platform" "1688"
    "status" "processing"
    "priority" "medium"
    "created_at" "2024-01-01T10:00:00Z"
    "updated_at" "2024-01-01T10:05:30Z"
    "started_at" "2024-01-01T10:01:00Z"
    "completed_at" ""
    "retry_count" "0"
    "max_retries" "3"
    "assigned_worker" "celery-worker-01@hostname"
    "crawler_backend" "task-middleware-01"
    "crawler_task_id" "task_abc123"
    "translation_task_id" "trans_xyz789"
    "execution_phase" "crawling"
    "error_count" "0"
    "last_error" ""
    "processing_duration" "0"
    "metadata" "{\"monitoring_frequency\": 24, \"alert_rules\": [\"price_change\"]}"
```

#### 2. 任务执行时间线
```redis
# 键格式: ecommerce:task:{task_id}:timeline
# 类型: Sorted Set (按时间戳排序)
# TTL: 7天

ZADD ecommerce:task:mon_20240101_001:timeline
    1704103200.000 "created:task_created_by_scheduler"
    1704103260.000 "queued:task_added_to_celery_queue"
    1704103270.000 "assigned:worker=celery-worker-01@hostname"
    1704103275.000 "started:worker_started_processing"
    1704103280.000 "crawler_request:sent_to_task-middleware-01"
    1704103285.000 "crawler_response:received_task_id=task_abc123"
    1704103350.000 "crawler_completed:status=completed"
    1704103355.000 "translation_request:sent_to_translation-service"
    1704103365.000 "translation_completed:status=completed"
    1704103370.000 "data_processed:saved_to_timescaledb"
    1704103375.000 "completed:task_finished_successfully"
```

#### 3. 爬虫请求详情
```redis
# 键格式: ecommerce:task:{task_id}:crawler_request
# 类型: Hash
# TTL: 7天

HSET ecommerce:task:mon_20240101_001:crawler_request
    "backend_url" "http://task-middleware-01:8000"
    "endpoint" "/api/v1/tasks/batch"
    "method" "POST"
    "headers" "{\"Authorization\": \"Bearer ***\", \"Content-Type\": \"application/json\"}"
    "request_body" "{\"urls\": [\"https://www.1688.com/product/123456.html\"], \"q\": \"Extract product info...\", \"schema_str\": \"{...}\"}"
    "request_id" "req_20240101_001"
    "sent_at" "2024-01-01T10:01:20Z"
    "response_status" "200"
    "response_body" "{\"batch_id\": \"batch_abc123\", \"task_ids\": [\"task_abc123\"]}"
    "response_time" "1.25"
    "worker_id" "celery-worker-01@hostname"
    "worker_pid" "12345"
    "worker_thread" "MainThread"
```

#### 4. 爬虫响应详情
```redis
# 键格式: ecommerce:task:{task_id}:crawler_response
# 类型: Hash
# TTL: 7天

HSET ecommerce:task:mon_20240101_001:crawler_response
    "crawler_task_id" "task_abc123"
    "crawler_batch_id" "batch_abc123"
    "status" "completed"
    "received_at" "2024-01-01T10:05:50Z"
    "processing_duration" "4.5"
    "result_size" "2048"
    "result_hash" "sha256:abc123..."
    "raw_result" "{\"title\": \"高品质电子产品\", \"price\": 299.99, ...}"
    "extracted_fields" "[\"title\", \"price\", \"currency\", \"stock_quantity\"]"
    "quality_score" "0.95"
    "validation_errors" "[]"
    "crawl4ai_job_id" "llm_1234567890"
```

#### 5. 翻译请求详情
```redis
# 键格式: ecommerce:task:{task_id}:translation_request
# 类型: Hash
# TTL: 7天

HSET ecommerce:task:mon_20240101_001:translation_request
    "service_url" "http://translation-service:8000"
    "endpoint" "/api/v1/translate/batch"
    "method" "POST"
    "provider" "openai"
    "model" "gpt-3.5-turbo"
    "source_lang" "zh"
    "target_lang" "en"
    "texts_count" "3"
    "request_body" "{\"texts\": [\"高品质电子产品\", \"产品描述\", \"规格参数\"], ...}"
    "sent_at" "2024-01-01T10:06:00Z"
    "response_status" "200"
    "response_time" "2.1"
    "translation_task_id" "trans_xyz789"
```

#### 6. Worker 执行上下文
```redis
# 键格式: ecommerce:worker:{worker_id}:context:{task_id}
# 类型: Hash
# TTL: 24小时

HSET ecommerce:worker:celery-worker-01@hostname:context:mon_20240101_001
    "worker_id" "celery-worker-01@hostname"
    "worker_pid" "12345"
    "worker_thread" "MainThread"
    "task_id" "mon_20240101_001"
    "started_at" "2024-01-01T10:01:15Z"
    "current_phase" "translation"
    "memory_usage" "256MB"
    "cpu_usage" "15%"
    "network_connections" "2"
    "temp_files" "[]"
    "log_file" "/var/log/celery/worker-01-task-mon_20240101_001.log"
    "debug_info" "{\"request_id\": \"req_20240101_001\", \"correlation_id\": \"corr_001\"}"
```

#### 7. 错误和异常记录
```redis
# 键格式: ecommerce:task:{task_id}:errors
# 类型: List (按时间顺序)
# TTL: 30天

LPUSH ecommerce:task:mon_20240101_001:errors
    "{\"timestamp\": \"2024-01-01T10:03:00Z\", \"phase\": \"crawler_request\", \"error_type\": \"NetworkTimeout\", \"error_message\": \"Connection timeout after 30s\", \"stack_trace\": \"...\", \"retry_attempt\": 1}"
    "{\"timestamp\": \"2024-01-01T10:07:00Z\", \"phase\": \"translation\", \"error_type\": \"RateLimitExceeded\", \"error_message\": \"OpenAI API rate limit exceeded\", \"retry_attempt\": 1}"
```

#### 8. 性能指标记录
```redis
# 键格式: ecommerce:task:{task_id}:metrics
# 类型: Hash
# TTL: 7天

HSET ecommerce:task:mon_20240101_001:metrics
    "total_duration" "275.5"
    "queue_wait_time" "15.2"
    "crawler_request_time" "1.25"
    "crawler_processing_time" "4.5"
    "crawler_response_time" "0.8"
    "translation_request_time" "2.1"
    "translation_processing_time" "1.8"
    "data_processing_time" "0.5"
    "database_save_time" "0.3"
    "memory_peak" "128MB"
    "network_bytes_sent" "2048"
    "network_bytes_received" "4096"
```

### 索引和查询结构

#### 1. 按状态索引
```redis
# 活跃任务索引
SADD ecommerce:tasks:active mon_20240101_001 mon_20240101_002
SADD ecommerce:tasks:processing mon_20240101_001
SADD ecommerce:tasks:completed mon_20240101_003
SADD ecommerce:tasks:failed mon_20240101_004
```

#### 2. 按Worker索引
```redis
# Worker任务分配索引
SADD ecommerce:worker:celery-worker-01@hostname:tasks mon_20240101_001 mon_20240101_005
SADD ecommerce:worker:celery-worker-02@hostname:tasks mon_20240101_002 mon_20240101_006
```

#### 3. 按时间索引
```redis
# 按创建时间索引 (Sorted Set)
ZADD ecommerce:tasks:by_created_time
    1704103200 mon_20240101_001
    1704103260 mon_20240101_002
    1704103320 mon_20240101_003
```

#### 4. 按产品索引
```redis
# 产品任务历史
LPUSH ecommerce:product:prod_12345:tasks mon_20240101_001 mon_20240101_010 mon_20240101_020
```

#### 5. 批次任务索引
```redis
# 批次任务列表
SADD ecommerce:batch:batch_20240101_001:tasks mon_20240101_001 mon_20240101_002 mon_20240101_003
```

### 实时监控结构

#### 1. 系统状态概览
```redis
# 键格式: ecommerce:system:status
# 类型: Hash
# TTL: 5分钟 (定期更新)

HSET ecommerce:system:status
    "total_tasks" "1500"
    "active_tasks" "25"
    "processing_tasks" "10"
    "completed_tasks" "1450"
    "failed_tasks" "15"
    "active_workers" "5"
    "crawler_backends" "3"
    "translation_services" "2"
    "avg_processing_time" "45.5"
    "success_rate" "0.96"
    "last_updated" "2024-01-01T10:10:00Z"
```

#### 2. Worker状态监控
```redis
# 键格式: ecommerce:worker:{worker_id}:status
# 类型: Hash
# TTL: 1分钟 (心跳更新)

HSET ecommerce:worker:celery-worker-01@hostname:status
    "worker_id" "celery-worker-01@hostname"
    "status" "active"
    "current_tasks" "2"
    "max_concurrent" "4"
    "total_processed" "150"
    "success_count" "145"
    "error_count" "5"
    "last_heartbeat" "2024-01-01T10:10:00Z"
    "memory_usage" "512MB"
    "cpu_usage" "25%"
    "uptime" "3600"
```

#### 3. 后端服务状态
```redis
# 键格式: ecommerce:backend:{service_type}:{instance_id}:status
# 类型: Hash
# TTL: 30秒

HSET ecommerce:backend:crawler:task-middleware-01:status
    "service_type" "crawler"
    "instance_id" "task-middleware-01"
    "url" "http://task-middleware-01:8000"
    "status" "healthy"
    "response_time" "1.2"
    "active_tasks" "15"
    "queue_length" "5"
    "success_rate" "0.98"
    "last_check" "2024-01-01T10:10:00Z"
    "version" "1.0.0"
```

### 调试和故障排查结构

#### 1. 调试会话
```redis
# 键格式: ecommerce:debug:session:{session_id}
# 类型: Hash
# TTL: 1小时

HSET ecommerce:debug:session:debug_001
    "session_id" "debug_001"
    "user_id" "admin"
    "created_at" "2024-01-01T10:00:00Z"
    "target_task" "mon_20240101_001"
    "debug_level" "verbose"
    "filters" "{\"phase\": \"crawler\", \"worker\": \"celery-worker-01\"}"
    "active" "true"
```

#### 2. 实时日志流
```redis
# 键格式: ecommerce:debug:logs:{task_id}
# 类型: Stream
# TTL: 1小时

XADD ecommerce:debug:logs:mon_20240101_001 * 
    level INFO 
    timestamp 2024-01-01T10:01:20Z 
    phase crawler_request 
    worker celery-worker-01@hostname 
    message "Sending request to crawler backend"
    request_id req_20240101_001
    correlation_id corr_001
```

#### 3. 性能分析数据
```redis
# 键格式: ecommerce:perf:{task_id}:trace
# 类型: List
# TTL: 24小时

LPUSH ecommerce:perf:mon_20240101_001:trace
    "{\"span_id\": \"span_001\", \"parent_id\": null, \"operation\": \"task_execution\", \"start_time\": 1704103200.000, \"end_time\": 1704103475.500, \"duration\": 275.5}"
    "{\"span_id\": \"span_002\", \"parent_id\": \"span_001\", \"operation\": \"crawler_request\", \"start_time\": 1704103280.000, \"end_time\": 1704103281.250, \"duration\": 1.25}"
    "{\"span_id\": \"span_003\", \"parent_id\": \"span_001\", \"operation\": \"translation_request\", \"start_time\": 1704103355.000, \"end_time\": 1704103357.100, \"duration\": 2.1}"
```

### 数据清理和归档策略

#### 1. TTL策略
```python
# TTL配置
TTL_CONFIG = {
    "task_main_record": 7 * 24 * 3600,      # 7天
    "task_timeline": 7 * 24 * 3600,         # 7天
    "crawler_details": 7 * 24 * 3600,       # 7天
    "worker_context": 24 * 3600,            # 24小时
    "error_records": 30 * 24 * 3600,        # 30天
    "performance_metrics": 7 * 24 * 3600,   # 7天
    "system_status": 5 * 60,                # 5分钟
    "worker_status": 60,                     # 1分钟
    "backend_status": 30,                    # 30秒
    "debug_session": 3600,                   # 1小时
    "debug_logs": 3600,                      # 1小时
    "performance_trace": 24 * 3600,         # 24小时
}
```

#### 2. 数据归档
```redis
# 归档任务索引
ZADD ecommerce:archive:tasks:2024-01
    1704103200 mon_20240101_001
    1704103260 mon_20240101_002
```

### Redis存储服务实现

```python
class EcommerceTaskStorageService:
    """电商监控任务存储服务"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.ttl_config = TTL_CONFIG
    
    async def store_task_record(self, task: MonitoringTask) -> None:
        """存储任务主记录"""
        key = f"ecommerce:task:{task.id}"
        
        task_data = {
            "id": task.id,
            "batch_id": task.batch_id,
            "product_id": task.product_id,
            "product_url": task.product_url,
            "platform": task.platform,
            "status": task.status.value,
            "priority": task.priority.value,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else "",
            "completed_at": task.completed_at.isoformat() if task.completed_at else "",
            "retry_count": str(task.retry_count),
            "max_retries": str(task.max_retries),
            "assigned_worker": task.assigned_worker or "",
            "crawler_backend": task.crawler_backend or "",
            "crawler_task_id": task.crawler_task_id or "",
            "translation_task_id": task.translation_task_id or "",
            "execution_phase": task.execution_phase.value,
            "error_count": str(task.error_count),
            "last_error": task.last_error or "",
            "processing_duration": str(task.processing_duration or 0),
            "metadata": json.dumps(task.metadata or {})
        }
        
        await self.redis.hset(key, mapping=task_data)
        await self.redis.expire(key, self.ttl_config["task_main_record"])
        
        # 更新索引
        await self._update_task_indexes(task)
    
    async def add_timeline_event(self, task_id: str, event: str, timestamp: Optional[float] = None) -> None:
        """添加时间线事件"""
        if timestamp is None:
            timestamp = time.time()
        
        key = f"ecommerce:task:{task_id}:timeline"
        await self.redis.zadd(key, {event: timestamp})
        await self.redis.expire(key, self.ttl_config["task_timeline"])
    
    async def store_crawler_request(self, task_id: str, request_details: CrawlerRequestDetails) -> None:
        """存储爬虫请求详情"""
        key = f"ecommerce:task:{task_id}:crawler_request"
        
        request_data = {
            "backend_url": request_details.backend_url,
            "endpoint": request_details.endpoint,
            "method": request_details.method,
            "headers": json.dumps(request_details.headers),
            "request_body": json.dumps(request_details.request_body),
            "request_id": request_details.request_id,
            "sent_at": request_details.sent_at.isoformat(),
            "response_status": str(request_details.response_status or 0),
            "response_body": json.dumps(request_details.response_body or {}),
            "response_time": str(request_details.response_time or 0),
            "worker_id": request_details.worker_id,
            "worker_pid": str(request_details.worker_pid or 0),
            "worker_thread": request_details.worker_thread or ""
        }
        
        await self.redis.hset(key, mapping=request_data)
        await self.redis.expire(key, self.ttl_config["crawler_details"])
    
    async def store_crawler_response(self, task_id: str, response_details: CrawlerResponseDetails) -> None:
        """存储爬虫响应详情"""
        key = f"ecommerce:task:{task_id}:crawler_response"
        
        response_data = {
            "crawler_task_id": response_details.crawler_task_id,
            "crawler_batch_id": response_details.crawler_batch_id,
            "status": response_details.status,
            "received_at": response_details.received_at.isoformat(),
            "processing_duration": str(response_details.processing_duration or 0),
            "result_size": str(response_details.result_size or 0),
            "result_hash": response_details.result_hash or "",
            "raw_result": json.dumps(response_details.raw_result or {}),
            "extracted_fields": json.dumps(response_details.extracted_fields or []),
            "quality_score": str(response_details.quality_score or 0),
            "validation_errors": json.dumps(response_details.validation_errors or []),
            "crawl4ai_job_id": response_details.crawl4ai_job_id or ""
        }
        
        await self.redis.hset(key, mapping=response_data)
        await self.redis.expire(key, self.ttl_config["crawler_details"])
    
    async def store_worker_context(self, worker_id: str, task_id: str, context: WorkerContext) -> None:
        """存储Worker执行上下文"""
        key = f"ecommerce:worker:{worker_id}:context:{task_id}"
        
        context_data = {
            "worker_id": context.worker_id,
            "worker_pid": str(context.worker_pid),
            "worker_thread": context.worker_thread,
            "task_id": task_id,
            "started_at": context.started_at.isoformat(),
            "current_phase": context.current_phase,
            "memory_usage": context.memory_usage,
            "cpu_usage": context.cpu_usage,
            "network_connections": str(context.network_connections),
            "temp_files": json.dumps(context.temp_files or []),
            "log_file": context.log_file or "",
            "debug_info": json.dumps(context.debug_info or {})
        }
        
        await self.redis.hset(key, mapping=context_data)
        await self.redis.expire(key, self.ttl_config["worker_context"])
    
    async def add_error_record(self, task_id: str, error: TaskError) -> None:
        """添加错误记录"""
        key = f"ecommerce:task:{task_id}:errors"
        
        error_data = {
            "timestamp": error.timestamp.isoformat(),
            "phase": error.phase,
            "error_type": error.error_type,
            "error_message": error.error_message,
            "stack_trace": error.stack_trace or "",
            "retry_attempt": error.retry_attempt
        }
        
        await self.redis.lpush(key, json.dumps(error_data))
        await self.redis.expire(key, self.ttl_config["error_records"])
    
    async def store_performance_metrics(self, task_id: str, metrics: PerformanceMetrics) -> None:
        """存储性能指标"""
        key = f"ecommerce:task:{task_id}:metrics"
        
        metrics_data = {
            "total_duration": str(metrics.total_duration),
            "queue_wait_time": str(metrics.queue_wait_time),
            "crawler_request_time": str(metrics.crawler_request_time),
            "crawler_processing_time": str(metrics.crawler_processing_time),
            "crawler_response_time": str(metrics.crawler_response_time),
            "translation_request_time": str(metrics.translation_request_time),
            "translation_processing_time": str(metrics.translation_processing_time),
            "data_processing_time": str(metrics.data_processing_time),
            "database_save_time": str(metrics.database_save_time),
            "memory_peak": metrics.memory_peak,
            "network_bytes_sent": str(metrics.network_bytes_sent),
            "network_bytes_received": str(metrics.network_bytes_received)
        }
        
        await self.redis.hset(key, mapping=metrics_data)
        await self.redis.expire(key, self.ttl_config["performance_metrics"])
    
    async def get_task_execution_details(self, task_id: str) -> TaskExecutionDetails:
        """获取任务执行详情"""
        # 获取主记录
        main_key = f"ecommerce:task:{task_id}"
        main_data = await self.redis.hgetall(main_key)
        
        # 获取时间线
        timeline_key = f"ecommerce:task:{task_id}:timeline"
        timeline = await self.redis.zrange(timeline_key, 0, -1, withscores=True)
        
        # 获取爬虫请求详情
        crawler_req_key = f"ecommerce:task:{task_id}:crawler_request"
        crawler_request = await self.redis.hgetall(crawler_req_key)
        
        # 获取爬虫响应详情
        crawler_resp_key = f"ecommerce:task:{task_id}:crawler_response"
        crawler_response = await self.redis.hgetall(crawler_resp_key)
        
        # 获取错误记录
        errors_key = f"ecommerce:task:{task_id}:errors"
        errors = await self.redis.lrange(errors_key, 0, -1)
        
        # 获取性能指标
        metrics_key = f"ecommerce:task:{task_id}:metrics"
        metrics = await self.redis.hgetall(metrics_key)
        
        return TaskExecutionDetails(
            main_record=main_data,
            timeline=timeline,
            crawler_request=crawler_request,
            crawler_response=crawler_response,
            errors=[json.loads(error) for error in errors],
            performance_metrics=metrics
        )
    
    async def _update_task_indexes(self, task: MonitoringTask) -> None:
        """更新任务索引"""
        # 状态索引
        await self.redis.sadd(f"ecommerce:tasks:{task.status.value}", task.id)
        
        # Worker索引
        if task.assigned_worker:
            await self.redis.sadd(f"ecommerce:worker:{task.assigned_worker}:tasks", task.id)
        
        # 时间索引
        created_timestamp = task.created_at.timestamp()
        await self.redis.zadd("ecommerce:tasks:by_created_time", {task.id: created_timestamp})
        
        # 产品索引
        await self.redis.lpush(f"ecommerce:product:{task.product_id}:tasks", task.id)
        
        # 批次索引
        if task.batch_id:
            await self.redis.sadd(f"ecommerce:batch:{task.batch_id}:tasks", task.id)
```

这个详细的Redis存储结构设计提供了：

1. **完整的任务执行追踪**：从任务创建到完成的每个步骤都有详细记录
2. **精确的请求/响应监控**：记录与外部服务的每次交互细节
3. **Worker级别的监控**：跟踪具体哪个Worker处理了哪个任务
4. **实时调试支持**：提供调试会话和实时日志流
5. **性能分析数据**：详细的性能指标和追踪数据
6. **灵活的查询索引**：支持按状态、Worker、时间、产品等多维度查询
7. **合理的数据生命周期**：不同类型数据有不同的TTL策略

## 监控和运维

### 系统监控

1. **应用监控**：
   - API响应时间
   - 错误率统计
   - 资源使用情况

2. **业务监控**：
   - 监控任务成功率
   - 数据质量指标
   - 用户活跃度

### 日志管理

1. **结构化日志**：
   - JSON格式日志
   - 统一日志格式
   - 日志级别管理

2. **日志聚合**：
   - ELK Stack集成
   - 日志搜索和分析
   - 告警规则配置

### 备份和恢复

1. **数据备份**：
   - 定期数据库备份
   - 增量备份策略
   - 跨地域备份

2. **灾难恢复**：
   - 恢复流程文档
   - 恢复时间目标(RTO)
   - 恢复点目标(RPO)