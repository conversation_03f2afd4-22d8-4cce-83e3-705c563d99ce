"""
任务创建服务

提供从URL池创建监控任务的核心功能
"""

import json
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional, Tuple
import redis.asyncio as redis
from uuid import uuid4

from app.models.task_create import (
    TaskCreateFromUrlsRequest, TaskCreateResponse, UrlPreviewRequest, UrlPreviewResponse,
    TaskValidationRequest, TaskValidationResponse, ScheduleConfig, TaskConfig,
    generate_schedule_description
)
from app.services.url_pool_service import UrlPoolService
from app.services.task_storage_service import TaskStorageService
from app.services.system_config_service import SystemConfigService

logger = logging.getLogger(__name__)


class TaskCreateService:
    """任务创建服务"""
    
    def __init__(self, redis_client: redis.Redis, url_pool_service: UrlPoolService):
        self.redis = redis_client
        self.url_pool_service = url_pool_service
        self.task_storage = TaskStorageService()  # 使用新的分层存储服务

        # Redis键名模板（保留用于URL关联）
        self.keys = {
            'task_urls': 'monitoring_tasks:task_urls:{}',           # 任务关联的URL (标准方式)
            'url_tasks': 'monitoring_tasks:url_tasks:{}',           # URL关联的任务 (标准方式)
            'scheduled_tasks': 'monitoring_tasks:scheduled',        # 已调度任务
            'task_stats': 'monitoring_tasks:stats',                # 任务统计缓存
        }
    
    async def preview_urls(self, request: UrlPreviewRequest) -> UrlPreviewResponse:
        """
        预览选中的URL信息
        
        Args:
            request: URL预览请求
            
        Returns:
            URL预览响应
        """
        try:
            logger.info(f"预览URL: {len(request.url_ids)}个")
            
            urls = []
            valid_count = 0
            invalid_count = 0
            platform_distribution = {}
            
            for url_id in request.url_ids:
                url_item = await self.url_pool_service.get_url_by_id(url_id)
                
                if url_item:
                    valid_count += 1
                    urls.append({
                        'id': url_item.id,
                        'url': url_item.url,
                        'platform': url_item.platform,
                        'status': url_item.status,
                        'source_file': url_item.source_file,
                        'added_at': url_item.added_at.isoformat(),
                        'check_count': url_item.check_count,
                        'success_count': url_item.success_count
                    })
                    
                    # 统计平台分布
                    platform = url_item.platform
                    platform_distribution[platform] = platform_distribution.get(platform, 0) + 1
                else:
                    invalid_count += 1
            
            return UrlPreviewResponse(
                success=True,
                total_count=len(request.url_ids),
                valid_count=valid_count,
                invalid_count=invalid_count,
                platform_distribution=platform_distribution,
                urls=urls
            )
            
        except Exception as e:
            logger.error(f"预览URL失败: {e}")
            raise
    
    async def validate_task_creation(self, request: TaskValidationRequest) -> TaskValidationResponse:
        """
        验证任务创建请求
        
        Args:
            request: 任务验证请求
            
        Returns:
            任务验证响应
        """
        try:
            errors = []
            warnings = []
            suggestions = []
            
            # 验证任务名称
            if await self._task_name_exists(request.name):
                errors.append(f"任务名称 '{request.name}' 已存在")
            
            # 验证URL
            url_validation = await self._validate_urls(request.url_ids)
            if url_validation['invalid_count'] > 0:
                errors.append(f"发现 {url_validation['invalid_count']} 个无效URL")
            
            if url_validation['disabled_count'] > 0:
                warnings.append(f"发现 {url_validation['disabled_count']} 个已禁用的URL")
            
            # 验证调度配置
            schedule_validation = self._validate_schedule(request.schedule)
            errors.extend(schedule_validation['errors'])
            warnings.extend(schedule_validation['warnings'])
            suggestions.extend(schedule_validation['suggestions'])
            
            # 性能建议
            if len(request.url_ids) > 100:
                suggestions.append("URL数量较多，建议适当调整并发限制和批次大小")
            
            return TaskValidationResponse(
                success=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                suggestions=suggestions
            )
            
        except Exception as e:
            logger.error(f"验证任务创建失败: {e}")
            raise
    
    async def create_task_from_urls(self, request: TaskCreateFromUrlsRequest) -> TaskCreateResponse:
        """
        从URL池创建监控任务
        
        Args:
            request: 任务创建请求
            
        Returns:
            任务创建响应
        """
        try:
            logger.info(f"创建任务: {request.name}, URL数量: {len(request.url_ids)}")
            
            # 1. 验证请求
            validation = await self.validate_task_creation(TaskValidationRequest(
                name=request.name,
                url_ids=request.url_ids,
                schedule=request.schedule
            ))

            if not validation.success:
                raise ValueError(f"任务验证失败: {', '.join(validation.errors)}")
            
            # 2. 创建任务
            task_id = str(uuid4())
            task_data = await self._create_task_data(task_id, request)
            
            # 3. 存储任务
            await self._store_task(task_id, task_data)
            
            # 4. 建立URL关联
            await self._create_url_associations(task_id, request.url_ids)
            
            # 5. 配置调度（如果启用）
            next_run = None
            if request.schedule.enabled:
                next_run = await self._setup_schedule(task_id, request.schedule)
            
            # 6. 更新统计
            await self._update_task_stats()
            
            # 7. 生成调度描述
            schedule_info = generate_schedule_description(request.schedule)
            
            logger.info(f"任务创建成功: {task_id}")
            
            return TaskCreateResponse(
                success=True,
                message=f"任务 '{request.name}' 创建成功",
                task_id=task_id,
                task_name=request.name,
                url_count=len(request.url_ids),
                schedule_info=schedule_info,
                next_run=next_run
            )
            
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            raise
    
    async def _task_name_exists(self, name: str) -> bool:
        """检查任务名称是否已存在"""
        try:
            # 获取所有任务ID
            task_ids = await self.redis.smembers(self.keys['all_task_ids'])
            
            for task_id_bytes in task_ids:
                task_id = task_id_bytes.decode()
                task_key = self.keys['task'].format(task_id)
                task_name = await self.redis.hget(task_key, 'name')
                
                if task_name and task_name.decode() == name:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查任务名称失败: {e}")
            return False
    
    async def _validate_urls(self, url_ids: List[str]) -> Dict[str, int]:
        """验证URL列表"""
        try:
            valid_count = 0
            invalid_count = 0
            disabled_count = 0
            
            for url_id in url_ids:
                url_item = await self.url_pool_service.get_url_by_id(url_id)
                
                if url_item:
                    valid_count += 1
                    if url_item.status == 'disabled':
                        disabled_count += 1
                else:
                    invalid_count += 1
            
            return {
                'valid_count': valid_count,
                'invalid_count': invalid_count,
                'disabled_count': disabled_count
            }
            
        except Exception as e:
            logger.error(f"验证URL失败: {e}")
            return {'valid_count': 0, 'invalid_count': len(url_ids), 'disabled_count': 0}
    
    def _validate_schedule(self, schedule: ScheduleConfig) -> Dict[str, List[str]]:
        """验证调度配置"""
        errors = []
        warnings = []
        suggestions = []
        
        try:
            if schedule.enabled:
                # 验证开始时间
                if schedule.start_time:
                    start_time = schedule.start_time
                    if isinstance(start_time, str):
                        start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    if not start_time.tzinfo:
                        start_time = start_time.replace(tzinfo=timezone.utc)

                    if start_time <= datetime.now(timezone.utc):
                        warnings.append("开始时间已过期，任务将立即开始执行")
                
                # 验证结束时间
                if schedule.end_time and schedule.start_time:
                    start_time = schedule.start_time
                    end_time = schedule.end_time

                    # 处理字符串时间
                    if isinstance(start_time, str):
                        start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    if isinstance(end_time, str):
                        end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))

                    # 确保时区信息
                    if not start_time.tzinfo:
                        start_time = start_time.replace(tzinfo=timezone.utc)
                    if not end_time.tzinfo:
                        end_time = end_time.replace(tzinfo=timezone.utc)

                    if end_time <= start_time:
                        errors.append("结束时间必须晚于开始时间")
                
                # 验证cron表达式
                schedule_type = schedule.type.value if hasattr(schedule.type, 'value') else schedule.type
                if schedule_type == 'custom' and schedule.cron_expression:
                    try:
                        # 这里可以添加cron表达式验证逻辑
                        pass
                    except Exception:
                        errors.append("无效的cron表达式")
                
                # 性能建议
                if schedule_type == 'hourly' and schedule.interval and schedule.interval < 5:
                    suggestions.append("执行间隔过短可能影响系统性能，建议至少5分钟")
            
            return {
                'errors': errors,
                'warnings': warnings,
                'suggestions': suggestions
            }
            
        except Exception as e:
            logger.error(f"验证调度配置失败: {e}")
            return {
                'errors': [f"调度配置验证失败: {str(e)}"],
                'warnings': [],
                'suggestions': []
            }
    
    async def _create_task_data(self, task_id: str, request: TaskCreateFromUrlsRequest) -> Dict:
        """创建任务数据"""
        now = datetime.now(timezone.utc)

        # 获取系统配置并合并到用户配置中
        user_config = request.config.model_dump(mode='json')
        enhanced_config = SystemConfigService.get_system_config_for_task(user_config)

        return {
            'id': task_id,
            'name': request.name,
            'description': request.description or '',
            'url_count': len(request.url_ids),
            'schedule': request.schedule.model_dump(mode='json'),  # 字典格式，不是JSON字符串
            'config': enhanced_config,                             # 包含系统配置的完整配置
            'tags': request.tags or [],                           # 列表格式，不是JSON字符串
            'status': 'active' if request.schedule.enabled else 'draft',
            'is_running': False,                                  # 布尔值，不是字符串
            'created_at': now.isoformat(),
            'updated_at': now.isoformat(),
            'last_run': '',
            'next_run': '',
            'total_runs': 0,                                      # 数字，不是字符串
            'success_runs': 0,                                    # 数字，不是字符串
            'failed_runs': 0,                                     # 数字，不是字符串
            'avg_duration': 0.0,                                  # 浮点数，不是字符串
            'last_success_time': '',
            'success_rate': 0.0                                   # 浮点数，不是字符串
        }
    
    async def _store_task(self, task_id: str, task_data: Dict):
        """存储任务到Redis（使用新的分层存储）"""
        try:
            # 使用TaskStorageService保存任务
            success = await self.task_storage.save_task(task_id, task_data)
            if not success:
                raise Exception("TaskStorageService保存任务失败")

            logger.info(f"任务存储成功: {task_id}")

        except Exception as e:
            logger.error(f"存储任务失败: {task_id}, 错误: {e}")
            raise
    
    async def _create_url_associations(self, task_id: str, url_ids: List[str]):
        """创建任务与URL的关联关系"""
        try:
            # 使用TaskStorageService添加URL关联
            success = await self.task_storage.add_urls_to_task(task_id, url_ids)
            if not success:
                raise Exception("TaskStorageService添加URL关联失败")

            logger.info(f"URL关联创建成功: {task_id}, URLs: {len(url_ids)}")

        except Exception as e:
            logger.error(f"创建URL关联失败: {task_id}, 错误: {e}")
            raise
            raise
    
    async def _setup_schedule(self, task_id: str, schedule: ScheduleConfig) -> Optional[datetime]:
        """设置任务调度"""
        try:
            import pytz
            from datetime import timedelta

            # 获取用户设置的时区
            user_timezone = pytz.timezone(schedule.timezone)
            now_utc = datetime.now(timezone.utc)
            now_local = now_utc.astimezone(user_timezone)

            if schedule.start_time:
                next_run = schedule.start_time
            else:
                # 根据调度类型计算下次执行时间
                if schedule.type.value == 'daily':
                    # 解析用户设置的执行时间
                    if schedule.time:
                        try:
                            time_parts = schedule.time.split(':')
                            hour = int(time_parts[0])
                            minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                        except (ValueError, IndexError):
                            # 如果时间格式错误，使用默认时间
                            hour, minute = 9, 0
                    else:
                        hour, minute = 9, 0

                    # 在用户时区中计算下次执行时间
                    next_run_local = now_local.replace(hour=hour, minute=minute, second=0, microsecond=0)

                    # 如果今天的执行时间已过，安排到明天
                    if next_run_local <= now_local:
                        next_run_local = next_run_local + timedelta(days=1)

                    # 应用随机延迟
                    if schedule.enable_random_delay:
                        import random
                        delay_min = schedule.random_delay_min or 0
                        delay_max = schedule.random_delay_max or 180
                        random_delay = random.randint(delay_min, delay_max)
                        next_run_local = next_run_local + timedelta(minutes=random_delay)

                    # 转换回UTC时间
                    next_run = next_run_local.astimezone(timezone.utc)
                else:
                    # 其他调度类型的处理
                    next_run = now_utc + timedelta(hours=1)  # 默认1小时后

            # 更新任务的下次执行时间
            task_key = self.keys['task'].format(task_id)
            await self.redis.hset(task_key, 'next_run', next_run.isoformat())

            # 添加到已调度任务集合
            await self.redis.sadd(self.keys['scheduled_tasks'], task_id)

            logger.info(f"任务调度设置完成: {task_id}, 下次执行: {next_run} (本地时间: {next_run.astimezone(user_timezone)})")
            return next_run
            
        except Exception as e:
            logger.error(f"设置任务调度失败: {task_id}, 错误: {e}")
            return None
    
    async def _update_task_stats(self):
        """更新任务统计信息"""
        try:
            # 使统计缓存失效，下次查询时重新计算
            await self.redis.delete(self.keys['task_stats'])
            
        except Exception as e:
            logger.error(f"更新任务统计失败: {e}")
    
    async def get_task_by_id(self, task_id: str) -> Optional[Dict]:
        """根据ID获取任务信息"""
        try:
            task_key = self.keys['task'].format(task_id)
            task_data = await self.redis.hgetall(task_key)
            
            if not task_data:
                return None
            
            # 解码数据
            task_data = {k.decode(): v.decode() for k, v in task_data.items()}
            
            # 解析JSON字段
            if task_data.get('schedule'):
                task_data['schedule'] = json.loads(task_data['schedule'])
            if task_data.get('config'):
                task_data['config'] = json.loads(task_data['config'])
            if task_data.get('tags'):
                task_data['tags'] = json.loads(task_data['tags'])
            
            return task_data
            
        except Exception as e:
            logger.error(f"获取任务失败: {task_id}, 错误: {e}")
            return None
