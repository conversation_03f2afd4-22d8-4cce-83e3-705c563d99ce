#!/usr/bin/env python3
"""
创建基础Grafana面板的脚本
通过API自动创建简单的系统监控面板
"""

import requests
import json
import time

def create_basic_dashboard():
    """创建基础监控面板"""
    
    grafana_url = "http://localhost:3001"
    auth = ("admin", "admin123")
    
    # 基础面板配置
    dashboard = {
        "dashboard": {
            "id": None,
            "title": "MonIt Basic Monitoring",
            "tags": ["monit", "basic"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "Services Status",
                    "type": "stat",
                    "targets": [
                        {
                            "expr": "up",
                            "legendFormat": "{{job}}"
                        }
                    ],
                    "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}
                },
                {
                    "id": 2,
                    "title": "CPU Usage",
                    "type": "timeseries",
                    "targets": [
                        {
                            "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
                            "legendFormat": "CPU %"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "percent",
                            "min": 0,
                            "max": 100
                        }
                    },
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}
                },
                {
                    "id": 3,
                    "title": "Memory Usage",
                    "type": "timeseries",
                    "targets": [
                        {
                            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
                            "legendFormat": "Memory %"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "percent",
                            "min": 0,
                            "max": 100
                        }
                    },
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}
                }
            ],
            "time": {
                "from": "now-1h",
                "to": "now"
            },
            "refresh": "30s"
        },
        "overwrite": True
    }
    
    try:
        print("🔍 等待Grafana服务...")
        time.sleep(5)
        
        print("📊 创建基础监控面板...")
        response = requests.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard,
            auth=auth,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 面板创建成功: {result['title']}")
            print(f"🔗 访问地址: {grafana_url}/d/{result['uid']}")
            return True
        else:
            print(f"❌ 面板创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("MonIt基础监控面板创建工具")
    print("=" * 50)
    
    print("📋 将创建以下面板:")
    print("  - 服务状态监控")
    print("  - CPU使用率")
    print("  - 内存使用率")
    print()
    
    if create_basic_dashboard():
        print("\n🎉 基础监控面板创建成功！")
        print("\n📖 更多面板配置请参考:")
        print("  docs/grafana_dashboard_setup.md")
    else:
        print("\n❌ 面板创建失败")
        print("\n🛠️  手动创建步骤:")
        print("  1. 访问 http://localhost:3001")
        print("  2. 登录 (admin/admin123)")
        print("  3. 创建新面板")
        print("  4. 参考 docs/grafana_dashboard_setup.md")

if __name__ == "__main__":
    main()
