#!/usr/bin/env python3
"""
清理重复的dashboard并创建一个有效的监控面板
"""

import requests
import json
import time

def cleanup_and_create_dashboard():
    """清理并创建有效的dashboard"""
    
    grafana_url = "http://localhost:3001"
    auth = ("admin", "admin123")
    
    print("🧹 清理和创建Grafana面板...")
    
    try:
        # 1. 获取现有的dashboards
        print("1. 获取现有dashboards...")
        response = requests.get(f"{grafana_url}/api/search", auth=auth)
        if response.status_code == 200:
            dashboards = response.json()
            print(f"   找到 {len(dashboards)} 个dashboard")
            
            # 删除空的或重复的dashboards
            for db in dashboards:
                if db['title'] in ['MonIt Dashboards', 'MonIt Monitoring']:
                    print(f"   🗑️  删除空dashboard: {db['title']}")
                    delete_response = requests.delete(
                        f"{grafana_url}/api/dashboards/uid/{db['uid']}", 
                        auth=auth
                    )
                    if delete_response.status_code == 200:
                        print(f"   ✅ 删除成功: {db['title']}")
                    else:
                        print(f"   ⚠️  删除失败: {db['title']}")
        
        time.sleep(2)
        
        # 2. 创建一个真正有效的dashboard
        print("2. 创建有效的监控面板...")
        
        dashboard_data = {
            "dashboard": {
                "id": None,
                "title": "MonIt System Monitor",
                "tags": ["monit", "system", "live"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Services Status",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "up",
                                "refId": "A",
                                "legendFormat": "{{job}}"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "mappings": [
                                    {
                                        "options": {
                                            "0": {
                                                "text": "DOWN",
                                                "color": "red"
                                            },
                                            "1": {
                                                "text": "UP", 
                                                "color": "green"
                                            }
                                        },
                                        "type": "value"
                                    }
                                ],
                                "thresholds": {
                                    "steps": [
                                        {"color": "red", "value": 0},
                                        {"color": "green", "value": 1}
                                    ]
                                }
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "orientation": "horizontal",
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"]
                            }
                        },
                        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "CPU Usage %",
                        "type": "timeseries",
                        "targets": [
                            {
                                "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
                                "refId": "A",
                                "legendFormat": "CPU Usage"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "unit": "percent",
                                "min": 0,
                                "max": 100,
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": 0},
                                        {"color": "yellow", "value": 70},
                                        {"color": "red", "value": 90}
                                    ]
                                }
                            }
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}
                    },
                    {
                        "id": 3,
                        "title": "Memory Usage %",
                        "type": "timeseries",
                        "targets": [
                            {
                                "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
                                "refId": "A",
                                "legendFormat": "Memory Usage"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "unit": "percent",
                                "min": 0,
                                "max": 100,
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": 0},
                                        {"color": "yellow", "value": 80},
                                        {"color": "red", "value": 95}
                                    ]
                                }
                            }
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}
                    },
                    {
                        "id": 4,
                        "title": "System Load",
                        "type": "timeseries",
                        "targets": [
                            {
                                "expr": "node_load1",
                                "refId": "A",
                                "legendFormat": "1m load"
                            },
                            {
                                "expr": "node_load5",
                                "refId": "B", 
                                "legendFormat": "5m load"
                            },
                            {
                                "expr": "node_load15",
                                "refId": "C",
                                "legendFormat": "15m load"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "unit": "short",
                                "min": 0
                            }
                        },
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 12}
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "refresh": "30s",
                "schemaVersion": 30,
                "version": 1
            },
            "overwrite": True
        }
        
        response = requests.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard_data,
            auth=auth,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 新面板创建成功!")
            print(f"   📊 标题: MonIt System Monitor")
            print(f"   🔗 访问: {grafana_url}{result['url']}")
            return True
        else:
            print(f"   ❌ 面板创建失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Grafana面板清理和重建工具")
    print("=" * 50)
    
    print("🎯 将执行以下操作:")
    print("  1. 删除空的dashboard")
    print("  2. 创建新的有效监控面板")
    print("  3. 包含服务状态、CPU、内存、负载监控")
    print()
    
    if cleanup_and_create_dashboard():
        print("\n🎉 操作完成!")
        print("\n📖 现在您可以:")
        print("  1. 刷新Grafana页面")
        print("  2. 查看 'MonIt System Monitor' 面板")
        print("  3. 看到真实的监控数据")
    else:
        print("\n❌ 操作失败")
        print("\n🛠️  建议手动操作:")
        print("  1. 在Grafana中删除空的dashboard")
        print("  2. 按照 docs/quick_grafana_setup.md 手动创建")

if __name__ == "__main__":
    main()
