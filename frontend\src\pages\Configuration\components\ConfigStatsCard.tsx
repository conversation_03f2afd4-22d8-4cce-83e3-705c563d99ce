/**
 * 配置统计卡片组件
 * 显示配置的统计信息和监控数据
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Progress,
  Tag,
  Space,
  Typography,
  Tooltip,
  Alert,
  Spin
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  <PERSON>boltOutlined,
  CloudServerOutlined,
  CodeOutlined
} from '@ant-design/icons';

import { 
  backendConfigApi,
  BackendConfigStats
} from '../../../services';

const { Text } = Typography;

interface ConfigStatsCardProps {
  type: 'overview' | 'backend-detail';
  backendId?: string;
}

const ConfigStatsCard: React.FC<ConfigStatsCardProps> = ({ type, backendId }) => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<BackendConfigStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (type === 'backend-detail' && backendId) {
      fetchBackendStats();
    }
  }, [type, backendId]);

  const fetchBackendStats = async () => {
    if (!backendId) return;
    
    setLoading(true);
    setError(null);
    try {
      const statsData = await backendConfigApi.getConfigStats(backendId);
      setStats(statsData);
    } catch (err) {
      setError(`获取统计数据失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  if (type === 'overview') {
    return (
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总配置数"
              value={0}
              prefix={<CodeOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃配置"
              value={0}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="错误配置"
              value={0}
              prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均健康评分"
              value={0}
              prefix={<ThunderboltOutlined />}
              suffix="%"
              precision={1}
            />
          </Card>
        </Col>
      </Row>
    );
  }

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <Alert
          message="暂无数据"
          description="没有找到统计数据"
          type="info"
          showIcon
        />
      </Card>
    );
  }

  return (
    <div>
      {/* 总体统计 */}
      <Card title="总体统计" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总后端数"
              value={stats.total_backends}
              prefix={<CloudServerOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="活跃后端"
              value={stats.active_backends}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="健康后端"
              value={stats.healthy_backends}
              prefix={<ThunderboltOutlined style={{ color: '#1890ff' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="可用后端"
              value={stats.available_backends}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Col>
        </Row>
      </Card>

      {/* 容量统计 */}
      <Card title="容量统计" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="总容量"
              value={stats.total_capacity}
              suffix="个并发"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="已用容量"
              value={stats.used_capacity}
              suffix="个并发"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="可用容量"
              value={stats.available_capacity}
              suffix="个并发"
            />
          </Col>
        </Row>
        <div style={{ marginTop: 16 }}>
          <Text strong>容量使用率</Text>
          <Progress
            percent={stats.total_capacity > 0 ? (stats.used_capacity / stats.total_capacity) * 100 : 0}
            status={stats.used_capacity / stats.total_capacity > 0.8 ? 'exception' : 'normal'}
            format={(percent) => `${stats.used_capacity}/${stats.total_capacity}`}
          />
        </div>
      </Card>

      {/* 性能统计 */}
      <Card title="性能统计" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="平均健康评分"
              value={stats.avg_health_score * 100}
              precision={1}
              suffix="%"
            />
            <Progress
              percent={stats.avg_health_score * 100}
              size="small"
              status={stats.avg_health_score >= 0.8 ? 'success' : stats.avg_health_score >= 0.5 ? 'normal' : 'exception'}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="平均响应时间"
              value={stats.avg_response_time}
              precision={0}
              suffix="ms"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="总体成功率"
              value={stats.total_success_rate * 100}
              precision={1}
              suffix="%"
            />
            <Progress
              percent={stats.total_success_rate * 100}
              size="small"
              status={stats.total_success_rate >= 0.9 ? 'success' : stats.total_success_rate >= 0.7 ? 'normal' : 'exception'}
            />
          </Col>
        </Row>
      </Card>

      {/* 性能级别分布 */}
      <Card title="性能级别分布" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          {Object.entries(stats.performance_distribution).map(([level, count]) => (
            <Col span={6} key={level}>
              <Card size="small">
                <Statistic
                  title={
                    <Space>
                      <span>{level}</span>
                      <Tag color={
                        level === 'ultra' ? 'purple' :
                        level === 'high' ? 'green' :
                        level === 'medium' ? 'blue' : 'default'
                      }>
                        {level}
                      </Tag>
                    </Space>
                  }
                  value={count}
                  suffix="个"
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 最近状态 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="最近创建" size="small">
            {stats.recently_created.length > 0 ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                {stats.recently_created.slice(0, 3).map((backend) => (
                  <div key={backend.backend_id}>
                    <Space>
                      <Text strong>{backend.backend_name}</Text>
                      <Tag color={backend.status === 'active' ? 'green' : 'default'}>
                        {backend.status}
                      </Tag>
                    </Space>
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {backend.created_at ? new Date(backend.created_at).toLocaleString() : ''}
                      </Text>
                    </div>
                  </div>
                ))}
              </Space>
            ) : (
              <Text type="secondary">暂无最近创建的后端</Text>
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近失败" size="small">
            {stats.recently_failed.length > 0 ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                {stats.recently_failed.slice(0, 3).map((backend) => (
                  <div key={backend.backend_id}>
                    <Space>
                      <Text strong>{backend.backend_name}</Text>
                      <Tag color="red">失败</Tag>
                    </Space>
                    <div>
                      <Tooltip title={`健康评分: ${(backend.health_score * 100).toFixed(1)}%`}>
                        <Progress
                          percent={backend.health_score * 100}
                          size="small"
                          status="exception"
                          showInfo={false}
                        />
                      </Tooltip>
                    </div>
                  </div>
                ))}
              </Space>
            ) : (
              <Text type="secondary">暂无失败的后端</Text>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ConfigStatsCard;
