"""
爬虫API请求构建服务
负责构建发送给爬虫API的标准化请求
集成监控和日志记录功能
"""

import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from ..services.system_config_service import SystemConfigService
from ..services.task_storage_service import TaskStorageService
from ..services.url_pool_service import UrlPoolService
from ..services.crawler_request_monitor import get_crawler_request_monitor
import redis.asyncio as redis
import logging

logger = logging.getLogger(__name__)


class CrawlerRequestBuilder:
    """爬虫API请求构建器"""
    
    @staticmethod
    async def build_crawler_request(
        task_id: str,
        urls: List[str],
        crawl_config: Dict[str, Any],
        execution_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        构建爬虫API请求
        
        Args:
            task_id: 监控任务ID
            urls: URL列表（已经是活跃URL）
            crawl_config: 爬取配置
            execution_context: 执行上下文
            
        Returns:
            标准化的爬虫API请求
        """
        if execution_context is None:
            execution_context = {}
        
        # 获取系统配置
        system_config = SystemConfigService.get_crawler_config()
        
        # 生成批次信息
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        batch_name = f"Task_{task_id}_{timestamp}"
        batch_description = f"监控任务 {task_id} 的批次执行 - {len(urls)} URLs"
        
        # 构建请求部分
        request_data = {
            "urls": urls,
            "q": crawl_config.get("llm_query", ""),
            "schema_str": crawl_config.get("llm_schema", ""),
            "cache": crawl_config.get("cache_enabled", False),
            "priority": crawl_config.get("priority", "medium"),
            "batch_name": batch_name,
            "batch_description": batch_description,
            "callback_url": system_config.default_callback_url or "",
            "max_concurrent_tasks": system_config.max_concurrent_tasks,
            "batch_timeout": system_config.batch_timeout
        }
        
        # 构建认证上下文
        auth_context = {
            "user_id": "monit_system",
            "role": "user",
            "permissions": [],
            "auth_method": crawl_config.get("auth_method", "api_key"),
            "api_key_prefix": crawl_config.get("api_key_prefix", ""),
            "client_ip": execution_context.get("client_ip", "**********"),  # Docker内部IP
            "user_agent": crawl_config.get("user_agent", "MonIt-Crawler/1.0"),
            "authenticated_at": datetime.now().isoformat()
        }
        
        # 添加执行上下文信息
        if "celery_task_id" in execution_context:
            auth_context["celery_task_id"] = execution_context["celery_task_id"]
        if "execution_id" in execution_context:
            auth_context["execution_id"] = execution_context["execution_id"]
        
        return {
            "request": request_data,
            "auth_context": auth_context
        }
    
    @staticmethod
    async def get_active_urls_for_task(task_id: str) -> List[str]:
        """
        获取任务的活跃URL列表
        
        Args:
            task_id: 监控任务ID
            
        Returns:
            活跃URL列表
        """
        try:
            # 1. 从TaskStorageService获取任务关联的URL ID
            task_storage = TaskStorageService()
            url_ids = await task_storage.get_task_urls(task_id)
            
            if not url_ids:
                logger.warning(f"Task {task_id} has no associated URLs")
                return []
            
            # 2. 从UrlPoolService获取活跃URL
            redis_client = redis.from_url("redis://redis:6379/0")
            url_pool_service = UrlPoolService(redis_client)
            
            active_urls = []
            for url_id in url_ids:
                url_item = await url_pool_service.get_url_by_id(url_id)
                if url_item and url_item.status == "active":
                    active_urls.append(url_item.url)
            
            await redis_client.close()
            
            logger.info(f"Retrieved {len(active_urls)} active URLs for task {task_id}")
            return active_urls
            
        except Exception as e:
            logger.error(f"Failed to get active URLs for task {task_id}: {e}")
            return []
    
    @staticmethod
    async def get_crawl_config_for_task(task_id: str) -> Dict[str, Any]:
        """
        获取任务的爬取配置
        
        Args:
            task_id: 监控任务ID
            
        Returns:
            爬取配置字典
        """
        try:
            task_storage = TaskStorageService()
            task_data = await task_storage.get_task(task_id)
            
            if not task_data:
                logger.error(f"Task {task_id} not found")
                return {}
            
            # 获取任务配置
            config = task_data.get('config', {})
            if isinstance(config, str):
                config = json.loads(config)
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to get crawl config for task {task_id}: {e}")
            return {}
    
    @staticmethod
    async def build_request_for_task(
        task_id: str,
        execution_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        为监控任务构建完整的爬虫API请求（带监控）

        Args:
            task_id: 监控任务ID
            execution_context: 执行上下文

        Returns:
            完整的爬虫API请求
        """
        if execution_context is None:
            execution_context = {}

        # 生成执行ID
        execution_id = execution_context.get("execution_id", f"exec_{task_id}_{int(time.time())}")

        # 获取监控器
        monitor = await get_crawler_request_monitor()

        # 使用监控上下文管理器
        async with monitor.monitor_request_building(task_id, execution_id, execution_context) as metrics:

            # 1. 获取活跃URL（带性能监控）
            url_start_time = time.time()
            await monitor.log_data_flow_step("开始获取活跃URL", task_id, f"任务ID: {task_id}")

            urls = await CrawlerRequestBuilder.get_active_urls_for_task(task_id)

            url_fetch_time = time.time() - url_start_time
            metrics.url_fetch_time = url_fetch_time
            metrics.urls_count = len(urls)

            await monitor.log_data_flow_step("获取活跃URL完成", task_id, urls, url_fetch_time)

            if not urls:
                raise ValueError(f"Task {task_id} has no active URLs")

            # 2. 获取爬取配置（带性能监控）
            config_start_time = time.time()
            await monitor.log_data_flow_step("开始获取爬取配置", task_id, f"任务ID: {task_id}")

            crawl_config = await CrawlerRequestBuilder.get_crawl_config_for_task(task_id)

            config_fetch_time = time.time() - config_start_time
            metrics.config_fetch_time = config_fetch_time

            await monitor.log_data_flow_step("获取爬取配置完成", task_id, crawl_config, config_fetch_time)

            if not crawl_config:
                raise ValueError(f"Task {task_id} has no crawl config")

            # 计算配置完整性
            required_fields = ["llm_query", "llm_schema", "priority", "auth_method"]
            present_fields = sum(1 for field in required_fields if crawl_config.get(field))
            metrics.config_completeness = present_fields / len(required_fields)
            metrics.config_source = f"任务配置 ({present_fields}/{len(required_fields)} 字段)"

            # 3. 构建请求（带性能监控）
            build_start_time = time.time()
            await monitor.log_data_flow_step("开始构建爬虫请求", task_id, f"URL数量: {len(urls)}")

            request = await CrawlerRequestBuilder.build_crawler_request(
                task_id=task_id,
                urls=urls,
                crawl_config=crawl_config,
                execution_context=execution_context
            )

            request_build_time = time.time() - build_start_time
            metrics.request_build_time = request_build_time

            await monitor.log_data_flow_step("构建爬虫请求完成", task_id, "请求构建成功", request_build_time)

            # 记录请求结构详情
            await monitor.log_request_structure(request, task_id)

            logger.info(f"✅ 爬虫请求构建完成 - 任务: {task_id}, URL数量: {len(urls)}, 总耗时: {time.time() - metrics.start_time:.3f}s")
            return request


class TaskUrlService:
    """任务URL服务（向后兼容）"""
    
    @staticmethod
    async def get_active_urls_for_task(task_id: str) -> List[str]:
        """获取任务的活跃URL列表（向后兼容方法）"""
        return await CrawlerRequestBuilder.get_active_urls_for_task(task_id)
