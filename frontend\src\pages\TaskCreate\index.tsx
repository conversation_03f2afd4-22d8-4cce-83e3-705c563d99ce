import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { message } from 'antd';

import TaskCreateWizard from '../../components/TaskCreateWizard';
import type { TaskCreateResponse } from '../../types/taskCreate';

const TaskCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从路由状态中获取初始URL（如果有的话）
  const initialUrls = location.state?.selectedUrls || [];

  // 处理任务创建成功
  const handleSuccess = (response: TaskCreateResponse) => {
    message.success(`任务 "${response.task_name}" 创建成功！`);

    // 跳转到监控任务管理页面
    setTimeout(() => {
      navigate('/monitoring', {
        state: {
          newTaskId: response.task_id,
          message: `任务 "${response.task_name}" 创建成功`
        }
      });
    }, 2000);
  };

  // 处理取消
  const handleCancel = () => {
    navigate(-1); // 返回上一页
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f5f5f5',
      padding: '24px 0'
    }}>
      <TaskCreateWizard
        initialUrls={initialUrls}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default TaskCreatePage;
