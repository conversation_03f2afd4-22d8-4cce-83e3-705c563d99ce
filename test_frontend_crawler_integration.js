/**
 * 前端爬虫集成测试脚本
 * 验证新的爬虫管理功能是否正常工作
 */

const API_BASE_URL = 'http://localhost:8000';
const CRAWLER_API_BASE = `${API_BASE_URL}/api/v1/crawler`;

// 测试数据
const testConfig = {
  name: '测试爬虫配置',
  description: '前端集成测试配置',
  config_type: 'batch_task',
  platform: 'general',
  llm_query: '提取页面标题和主要内容',
  llm_schema: '{"title": "string", "content": "string"}',
  priority: 'medium',
  cache_enabled: false,
  max_concurrent_tasks: 5,
  batch_timeout: 300
};

const testExecutionRequest = {
  monitoring_task_id: 'test_task_001',
  urls: [
    'https://example.com/page1',
    'https://example.com/page2'
  ],
  execution_mode: 'batch',
  priority: 'high',
  metadata: {
    test: true,
    source: 'frontend_test'
  }
};

// 辅助函数
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
    }
    
    return data;
  } catch (error) {
    console.error(`Request failed: ${url}`, error);
    throw error;
  }
}

// 测试函数
async function testHealthCheck() {
  console.log('\n=== 测试健康检查 ===');
  try {
    const result = await makeRequest(`${CRAWLER_API_BASE}/health`);
    console.log('✅ 健康检查通过:', result.data.status);
    return true;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
    return false;
  }
}

async function testConnectionTest() {
  console.log('\n=== 测试连接测试 ===');
  try {
    const result = await makeRequest(`${CRAWLER_API_BASE}/test`, { method: 'POST' });
    console.log('✅ 连接测试结果:', result.success ? '成功' : '失败');
    if (result.data.connection_status) {
      console.log('   连接状态:', result.data.connection_status);
    }
    return result.success;
  } catch (error) {
    console.error('❌ 连接测试失败:', error.message);
    return false;
  }
}

async function testGetPlatforms() {
  console.log('\n=== 测试获取平台列表 ===');
  try {
    const result = await makeRequest(`${CRAWLER_API_BASE}/platforms/`);
    console.log('✅ 平台列表获取成功:', result.data.length, '个平台');
    result.data.forEach(platform => {
      console.log(`   - ${platform.label}: ${platform.description}`);
    });
    return result.data;
  } catch (error) {
    console.error('❌ 获取平台列表失败:', error.message);
    return [];
  }
}

async function testGetTemplates() {
  console.log('\n=== 测试获取配置模板 ===');
  try {
    const result = await makeRequest(`${CRAWLER_API_BASE}/templates/`);
    console.log('✅ 配置模板获取成功:', result.data.length, '个模板');
    result.data.forEach(template => {
      console.log(`   - ${template.name} (${template.platform})`);
    });
    return result.data;
  } catch (error) {
    console.error('❌ 获取配置模板失败:', error.message);
    return [];
  }
}

async function testConfigCRUD() {
  console.log('\n=== 测试配置CRUD操作 ===');
  let configId = null;
  
  try {
    // 1. 创建配置
    console.log('1. 创建配置...');
    const createResult = await makeRequest(`${CRAWLER_API_BASE}/configs/`, {
      method: 'POST',
      body: JSON.stringify(testConfig)
    });
    configId = createResult.data.id;
    console.log('✅ 配置创建成功, ID:', configId);
    
    // 2. 获取配置
    console.log('2. 获取配置...');
    const getResult = await makeRequest(`${CRAWLER_API_BASE}/configs/${configId}`);
    console.log('✅ 配置获取成功:', getResult.data.name);
    
    // 3. 更新配置
    console.log('3. 更新配置...');
    const updateData = {
      description: '更新后的测试配置',
      priority: 'high'
    };
    const updateResult = await makeRequest(`${CRAWLER_API_BASE}/configs/${configId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
    console.log('✅ 配置更新成功, 优先级:', updateResult.data.priority);
    
    // 4. 验证配置
    console.log('4. 验证配置...');
    const validateResult = await makeRequest(`${CRAWLER_API_BASE}/configs/${configId}/validate`, {
      method: 'POST'
    });
    console.log('✅ 配置验证完成, 有效性:', validateResult.data.config_valid);
    
    // 5. 列出配置
    console.log('5. 列出配置...');
    const listResult = await makeRequest(`${CRAWLER_API_BASE}/configs/?page=1&page_size=10`);
    console.log('✅ 配置列表获取成功, 总数:', listResult.data.total);
    
    return configId;
    
  } catch (error) {
    console.error('❌ 配置CRUD操作失败:', error.message);
    return null;
  }
}

async function testTaskExecution(configId) {
  console.log('\n=== 测试任务执行 ===');
  
  if (!configId) {
    console.log('⚠️  跳过任务执行测试 (没有有效的配置ID)');
    return null;
  }
  
  try {
    // 1. 执行任务
    console.log('1. 执行任务...');
    const executeRequest = {
      ...testExecutionRequest,
      config_id: configId
    };
    
    const executeResult = await makeRequest(`${CRAWLER_API_BASE}/execute`, {
      method: 'POST',
      body: JSON.stringify(executeRequest)
    });
    
    const executionId = executeResult.data.execution_id;
    console.log('✅ 任务执行成功, 执行ID:', executionId);
    console.log('   批次ID:', executeResult.data.batch_id);
    console.log('   状态:', executeResult.data.status);
    
    // 2. 获取执行状态
    console.log('2. 获取执行状态...');
    const statusResult = await makeRequest(`${CRAWLER_API_BASE}/executions/${executionId}`);
    console.log('✅ 执行状态获取成功:', statusResult.data.status);
    
    // 3. 获取任务执行历史
    console.log('3. 获取执行历史...');
    const historyResult = await makeRequest(
      `${CRAWLER_API_BASE}/tasks/${testExecutionRequest.monitoring_task_id}/executions?limit=5`
    );
    console.log('✅ 执行历史获取成功, 记录数:', historyResult.data.executions.length);
    
    return executionId;
    
  } catch (error) {
    console.error('❌ 任务执行测试失败:', error.message);
    return null;
  }
}

async function testExecutorStats() {
  console.log('\n=== 测试执行器统计 ===');
  try {
    const result = await makeRequest(`${CRAWLER_API_BASE}/stats`);
    console.log('✅ 执行器统计获取成功:');
    console.log('   总执行次数:', result.data.total_executions);
    console.log('   成功次数:', result.data.successful_executions);
    console.log('   失败次数:', result.data.failed_executions);
    console.log('   成功率:', (result.data.success_rate * 100).toFixed(2) + '%');
    return result.data;
  } catch (error) {
    console.error('❌ 获取执行器统计失败:', error.message);
    return null;
  }
}

async function cleanup(configId) {
  console.log('\n=== 清理测试数据 ===');
  
  if (configId) {
    try {
      await makeRequest(`${CRAWLER_API_BASE}/configs/${configId}`, {
        method: 'DELETE'
      });
      console.log('✅ 测试配置已删除');
    } catch (error) {
      console.error('❌ 删除测试配置失败:', error.message);
    }
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始前端爬虫集成测试');
  console.log('测试目标:', CRAWLER_API_BASE);
  
  const results = {
    healthCheck: false,
    connectionTest: false,
    platforms: false,
    templates: false,
    configCRUD: false,
    taskExecution: false,
    executorStats: false
  };
  
  let configId = null;
  
  try {
    // 基础连接测试
    results.healthCheck = await testHealthCheck();
    results.connectionTest = await testConnectionTest();
    
    // 数据获取测试
    const platforms = await testGetPlatforms();
    results.platforms = platforms.length > 0;
    
    const templates = await testGetTemplates();
    results.templates = templates.length > 0;
    
    // 配置管理测试
    configId = await testConfigCRUD();
    results.configCRUD = configId !== null;
    
    // 任务执行测试
    const executionId = await testTaskExecution(configId);
    results.taskExecution = executionId !== null;
    
    // 统计信息测试
    const stats = await testExecutorStats();
    results.executorStats = stats !== null;
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 清理测试数据
    await cleanup(configId);
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果汇总');
  console.log('='.repeat(50));
  
  const testItems = [
    ['健康检查', results.healthCheck],
    ['连接测试', results.connectionTest],
    ['平台列表', results.platforms],
    ['配置模板', results.templates],
    ['配置管理', results.configCRUD],
    ['任务执行', results.taskExecution],
    ['执行统计', results.executorStats]
  ];
  
  let passedTests = 0;
  testItems.forEach(([name, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${name}: ${passed ? '通过' : '失败'}`);
    if (passed) passedTests++;
  });
  
  const totalTests = testItems.length;
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  
  console.log('='.repeat(50));
  console.log(`📈 总体结果: ${passedTests}/${totalTests} 通过 (${successRate}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！前端爬虫集成功能正常');
  } else {
    console.log('⚠️  部分测试失败，请检查相关功能');
  }
  
  console.log('='.repeat(50));
}

// 运行测试
if (typeof window === 'undefined') {
  // Node.js环境
  const fetch = require('node-fetch');
  global.fetch = fetch;
  runTests().catch(console.error);
} else {
  // 浏览器环境
  runTests().catch(console.error);
}

// 导出测试函数供浏览器使用
if (typeof window !== 'undefined') {
  window.testFrontendCrawlerIntegration = runTests;
}
