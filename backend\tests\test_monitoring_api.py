"""
测试监控API端点
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from app.main import app

client = TestClient(app)


class TestMonitoringAPI:
    """测试监控API"""
    
    def test_get_crawler_request_metrics_summary(self):
        """测试获取爬虫请求指标摘要"""
        
        # 模拟监控器返回的摘要数据
        mock_summary = {
            "period": "最近 1 天",
            "total_requests": 15,
            "success_rate": "86.7%",
            "status_breakdown": {
                "successful": 13,
                "validation_errors": 1,
                "api_errors": 1,
                "failed": 0
            },
            "performance": {
                "avg_duration": "2.340s",
                "max_duration": "4.500s",
                "min_duration": "1.200s"
            },
            "url_statistics": {
                "avg_urls_per_request": "22.3",
                "max_urls_per_request": 45
            }
        }
        
        with patch('app.api.monitoring_routes.get_crawler_request_monitor') as mock_get_monitor:
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.get_metrics_summary.return_value = mock_summary
            mock_monitor.close.return_value = None
            mock_get_monitor.return_value = mock_monitor
            
            # 发送请求
            response = client.get("/api/v1/monitoring/crawler-request-metrics/summary?days=1")
            
            # 验证响应
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert data["data"] == mock_summary
            assert data["query_params"]["days"] == 1
            assert "query_time" in data["query_params"]
    
    def test_get_task_crawler_request_metrics(self):
        """测试获取任务爬虫请求指标"""
        
        task_id = "test-task-123"
        
        # 模拟任务指标数据
        mock_metrics = [
            {
                "task_id": task_id,
                "execution_id": "exec-1",
                "start_time": 1754763900,
                "duration": 2.5,
                "status": "success",
                "urls_count": 10,
                "url_fetch_time": 1.2,
                "config_fetch_time": 0.8
            },
            {
                "task_id": task_id,
                "execution_id": "exec-2", 
                "start_time": 1754763800,
                "duration": 3.1,
                "status": "success",
                "urls_count": 15,
                "url_fetch_time": 1.8,
                "config_fetch_time": 0.9
            }
        ]
        
        with patch('app.api.monitoring_routes.get_crawler_request_monitor') as mock_get_monitor:
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.get_task_metrics.return_value = mock_metrics
            mock_monitor.close.return_value = None
            mock_get_monitor.return_value = mock_monitor
            
            # 发送请求
            response = client.get(f"/api/v1/monitoring/crawler-request-metrics/task/{task_id}")
            
            # 验证响应
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert data["data"]["task_id"] == task_id
            assert data["data"]["metrics_count"] == 2
            assert data["data"]["metrics"] == mock_metrics
    
    def test_get_task_monitoring_report(self):
        """测试获取任务监控报告"""
        
        task_id = "test-report-task"
        
        # 模拟监控报告
        mock_report = """📊 监控报告 - 任务: test-report-task
==================================================

📈 执行统计:
  • 总执行次数: 5
  • 成功次数: 4
  • 成功率: 80.0%

⏱️ 性能统计:
  • 平均耗时: 2.800s
  • 最长耗时: 4.200s
  • 最短耗时: 1.500s

🕐 最近执行:
  • 时间: 2025-08-10 14:30:00
  • 状态: success
  • URL数量: 12
"""
        
        with patch('app.api.monitoring_routes.get_crawler_request_monitor') as mock_get_monitor:
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.generate_monitoring_report.return_value = mock_report
            mock_monitor.close.return_value = None
            mock_get_monitor.return_value = mock_monitor
            
            # 发送请求
            response = client.get(f"/api/v1/monitoring/crawler-request-metrics/report/{task_id}")
            
            # 验证响应
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert data["data"]["task_id"] == task_id
            assert data["data"]["report"] == mock_report
            assert "generated_at" in data["data"]
    
    def test_get_performance_analysis(self):
        """测试获取性能分析"""
        
        # 模拟性能分析数据
        mock_metrics = [
            {
                "task_id": "perf-task-1",
                "duration": 2.5,
                "urls_count": 10,
                "status": "success"
            },
            {
                "task_id": "perf-task-1",
                "duration": 3.2,
                "urls_count": 15,
                "status": "success"
            },
            {
                "task_id": "perf-task-1",
                "duration": 1.8,
                "urls_count": 8,
                "status": "validation_error"
            }
        ]
        
        with patch('app.api.monitoring_routes.get_crawler_request_monitor') as mock_get_monitor:
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.get_task_metrics.return_value = mock_metrics
            mock_monitor.close.return_value = None
            mock_get_monitor.return_value = mock_monitor
            
            # 发送请求（带任务ID）
            response = client.get("/api/v1/monitoring/crawler-request-metrics/performance-analysis?days=7&task_id=perf-task-1")
            
            # 验证响应
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            analysis = data["data"]
            
            assert analysis["scope"] == "任务 perf-task-1"
            assert analysis["total_requests"] == 3
            assert "performance_metrics" in analysis
            assert "url_metrics" in analysis
            assert "status_distribution" in analysis
            
            # 验证性能指标计算
            perf_metrics = analysis["performance_metrics"]
            assert perf_metrics["avg_duration"] == pytest.approx(2.5, abs=0.1)  # (2.5+3.2+1.8)/3
            assert perf_metrics["max_duration"] == 3.2
            assert perf_metrics["min_duration"] == 1.8
            
            # 验证URL指标
            url_metrics = analysis["url_metrics"]
            assert url_metrics["avg_urls_per_request"] == pytest.approx(11.0, abs=0.1)  # (10+15+8)/3
            assert url_metrics["max_urls_per_request"] == 15
            assert url_metrics["total_urls_processed"] == 33  # 10+15+8
            
            # 验证状态分布
            status_dist = analysis["status_distribution"]
            assert status_dist["success"] == 2
            assert status_dist["validation_error"] == 1
            assert status_dist["api_error"] == 0
            assert status_dist["failed"] == 0
    
    def test_get_system_health(self):
        """测试获取系统健康状态"""
        
        # 模拟健康状态数据
        mock_recent_summary = {
            "total_requests": 25,
            "success_rate": "92.0%"
        }
        
        with patch('app.api.monitoring_routes.get_crawler_request_monitor') as mock_get_monitor:
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.get_redis_client.return_value.ping.return_value = True
            mock_monitor.get_metrics_summary.return_value = mock_recent_summary
            mock_monitor.metrics_retention_days = 7
            mock_monitor.performance_thresholds = {
                "url_fetch_warning": 2.0,
                "config_fetch_warning": 1.0,
                "total_build_warning": 5.0,
                "urls_count_warning": 100
            }
            mock_monitor.close.return_value = None
            mock_get_monitor.return_value = mock_monitor
            
            # 发送请求
            response = client.get("/api/v1/monitoring/system-health")
            
            # 验证响应
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            health_data = data["data"]
            
            assert "timestamp" in health_data
            assert "services" in health_data
            assert health_data["services"]["redis"] == "healthy"
            assert health_data["services"]["crawler_request_builder"] == "healthy"
            assert health_data["services"]["monitoring_system"] == "healthy"
            assert health_data["recent_activity"] == mock_recent_summary
            assert health_data["system_info"]["metrics_retention_days"] == 7
    
    def test_api_error_handling(self):
        """测试API错误处理"""
        
        with patch('app.api.monitoring_routes.get_crawler_request_monitor') as mock_get_monitor:
            # 模拟监控器抛出异常
            mock_monitor = AsyncMock()
            mock_monitor.get_metrics_summary.side_effect = Exception("Redis connection failed")
            mock_monitor.close.return_value = None
            mock_get_monitor.return_value = mock_monitor
            
            # 发送请求
            response = client.get("/api/v1/monitoring/crawler-request-metrics/summary?days=1")
            
            # 验证错误响应
            assert response.status_code == 500
            data = response.json()
            assert "获取指标摘要失败" in data["detail"]


if __name__ == "__main__":
    pytest.main([__file__])
