#!/usr/bin/env python3
"""
测试任务创建集成

验证URL池创建任务后，监控任务管理页面能否正确显示
"""

import asyncio
import sys
import os
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_task_creation_integration():
    """测试任务创建集成"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试任务创建集成")
    print("=" * 50)
    
    try:
        # 步骤1: 获取创建前的任务列表
        print("\n📋 步骤1: 获取创建前的任务列表")
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response.status_code}")
        
        if list_response.status_code == 200:
            before_data = list_response.json()
            before_count = before_data.get('total', 0)
            print(f"  创建前任务数量: {before_count}")
        else:
            print(f"  ❌ 获取任务列表失败: {list_response.text}")
            return
        
        # 步骤2: 通过URL池创建任务
        print(f"\n📋 步骤2: 通过URL池创建任务")
        create_data = {
            "name": "集成测试任务",
            "description": "用于测试任务创建集成的任务",
            "url_ids": ["test_url_1", "test_url_2"],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "10:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        print(f"  发送创建请求...")
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        print(f"  状态码: {create_response.status_code}")
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            print(f"  ✅ 任务创建成功")
            task_id = create_result.get('data', {}).get('task_id')
            print(f"  任务ID: {task_id}")
        else:
            print(f"  ❌ 任务创建失败")
            print(f"  错误: {create_response.text}")
            return
        
        # 步骤3: 获取创建后的任务列表
        print(f"\n📋 步骤3: 获取创建后的任务列表")
        list_response2 = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response2.status_code}")
        
        if list_response2.status_code == 200:
            after_data = list_response2.json()
            after_count = after_data.get('total', 0)
            print(f"  创建后任务数量: {after_count}")
            
            if after_count > before_count:
                print(f"  ✅ 任务数量增加了 {after_count - before_count} 个")
                
                # 检查新创建的任务是否在列表中
                tasks = after_data.get('data', [])
                found_task = None
                for task in tasks:
                    if task.get('id') == task_id:
                        found_task = task
                        break
                
                if found_task:
                    print(f"  ✅ 新创建的任务在列表中找到")
                    print(f"  任务名称: {found_task.get('name')}")
                    print(f"  任务状态: {found_task.get('status')}")
                else:
                    print(f"  ❌ 新创建的任务在列表中未找到")
            else:
                print(f"  ❌ 任务数量没有增加")
        else:
            print(f"  ❌ 获取任务列表失败: {list_response2.text}")
        
        # 步骤4: 直接获取新创建的任务详情
        if task_id:
            print(f"\n📋 步骤4: 获取新创建任务的详情")
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            print(f"  状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 任务详情获取成功")
                task_detail = detail_data.get('data', {})
                print(f"  任务名称: {task_detail.get('name')}")
                print(f"  任务状态: {task_detail.get('status')}")
                print(f"  URL数量: {task_detail.get('total_urls', 0)}")
            else:
                print(f"  ❌ 任务详情获取失败: {detail_response.text}")
        
        # 步骤5: 清理测试任务
        if task_id:
            print(f"\n📋 步骤5: 清理测试任务")
            delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            print(f"  状态码: {delete_response.status_code}")
            
            if delete_response.status_code in [200, 204]:
                print(f"  ✅ 测试任务已清理")
            else:
                print(f"  ⚠️ 测试任务清理失败: {delete_response.text}")
        
        print(f"\n🎉 集成测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_task_creation_integration()
