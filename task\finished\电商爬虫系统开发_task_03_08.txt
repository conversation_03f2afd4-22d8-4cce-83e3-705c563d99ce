# 子任务03-08: 会话和状态管理系统

## 任务描述
开发完善的会话和状态管理系统，实现Cookie池管理、登录状态维护、购物车状态模拟、本地存储处理等功能，为爬虫系统提供稳定的会话保持能力。

## 具体任务内容

### 1. Cookie池管理系统
- 实现Cookie的收集、存储和轮换
- 支持Cookie的有效性检查和更新
- 实现Cookie池的持久化和恢复
- 支持多域名Cookie管理

### 2. 会话状态维护
- 实现会话的创建、保持和销毁
- 支持会话超时和自动刷新
- 实现会话状态的序列化存储
- 支持会话故障恢复机制

### 3. 登录状态管理
- 实现自动登录和状态检测
- 支持多账户池管理和轮换
- 实现登录失效自动处理
- 支持验证码处理机制

### 4. 本地存储处理
- 实现LocalStorage数据管理
- 支持SessionStorage状态处理
- 实现IndexedDB数据操作
- 支持WebSQL数据库处理

### 5. 购物车状态模拟
- 实现购物车商品添加和移除
- 支持购物车状态持久化
- 实现购物车数据同步
- 支持多账户购物车管理

### 6. 用户行为状态追踪
- 实现浏览历史记录管理
- 支持用户偏好设置存储
- 实现搜索历史维护
- 支持用户行为模式分析

## 技术实现

### 会话管理器架构
```python
import json
import pickle
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import asyncio
from playwright.async_api import BrowserContext, Page

@dataclass
class SessionInfo:
    """会话信息模型"""
    session_id: str
    user_agent: str
    cookies: List[Dict]
    local_storage: Dict[str, str]
    session_storage: Dict[str, str]
    created_at: datetime
    last_used: datetime
    expires_at: datetime
    domain: str
    is_logged_in: bool
    user_info: Optional[Dict] = None

class SessionManager:
    """会话管理器"""
    
    def __init__(self, storage_path: str = "sessions.db"):
        self.storage_path = storage_path
        self.active_sessions: Dict[str, SessionInfo] = {}
        self.session_pool: List[SessionInfo] = []
        self.init_storage()
    
    def init_storage(self):
        """初始化存储"""
        self.conn = sqlite3.connect(self.storage_path)
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                user_agent TEXT,
                cookies TEXT,
                local_storage TEXT,
                session_storage TEXT,
                created_at TIMESTAMP,
                last_used TIMESTAMP,
                expires_at TIMESTAMP,
                domain TEXT,
                is_logged_in BOOLEAN,
                user_info TEXT
            )
        """)
        self.conn.commit()
    
    async def create_session(self, context: BrowserContext, domain: str) -> SessionInfo:
        """创建新会话"""
        session_id = self._generate_session_id()
        
        # 获取当前会话状态
        page = await context.new_page()
        await page.goto(f"https://{domain}")
        
        cookies = await context.cookies()
        local_storage = await page.evaluate("() => Object.assign({}, localStorage)")
        session_storage = await page.evaluate("() => Object.assign({}, sessionStorage)")
        user_agent = await page.evaluate("() => navigator.userAgent")
        
        session_info = SessionInfo(
            session_id=session_id,
            user_agent=user_agent,
            cookies=cookies,
            local_storage=local_storage,
            session_storage=session_storage,
            created_at=datetime.now(),
            last_used=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=24),
            domain=domain,
            is_logged_in=False
        )
        
        await page.close()
        await self.save_session(session_info)
        self.active_sessions[session_id] = session_info
        
        return session_info
    
    async def restore_session(self, context: BrowserContext, session_info: SessionInfo):
        """恢复会话状态"""
        # 设置Cookies
        await context.add_cookies(session_info.cookies)
        
        # 设置本地存储
        page = await context.new_page()
        await page.goto(f"https://{session_info.domain}")
        
        # 恢复LocalStorage
        for key, value in session_info.local_storage.items():
            await page.evaluate(f"localStorage.setItem('{key}', '{value}')")
        
        # 恢复SessionStorage
        for key, value in session_info.session_storage.items():
            await page.evaluate(f"sessionStorage.setItem('{key}', '{value}')")
        
        await page.close()
        session_info.last_used = datetime.now()
        await self.save_session(session_info)

class CookieManager:
    """Cookie管理器"""
    
    def __init__(self):
        self.cookie_pools: Dict[str, List[Dict]] = {}
        self.cookie_index: Dict[str, int] = {}
    
    async def add_cookies(self, domain: str, cookies: List[Dict]):
        """添加Cookie到池中"""
        if domain not in self.cookie_pools:
            self.cookie_pools[domain] = []
            self.cookie_index[domain] = 0
        
        # 去重并添加
        existing_cookies = {c['name']: c for c in self.cookie_pools[domain]}
        for cookie in cookies:
            existing_cookies[cookie['name']] = cookie
        
        self.cookie_pools[domain] = list(existing_cookies.values())
    
    async def get_fresh_cookies(self, domain: str) -> List[Dict]:
        """获取新鲜的Cookie"""
        if domain not in self.cookie_pools or not self.cookie_pools[domain]:
            return []
        
        # 检查Cookie有效性
        valid_cookies = []
        for cookie in self.cookie_pools[domain]:
            if self._is_cookie_valid(cookie):
                valid_cookies.append(cookie)
        
        self.cookie_pools[domain] = valid_cookies
        return valid_cookies
    
    def _is_cookie_valid(self, cookie: Dict) -> bool:
        """检查Cookie是否有效"""
        if 'expires' in cookie:
            expires = datetime.fromtimestamp(cookie['expires'])
            return expires > datetime.now()
        return True

class LoginManager:
    """登录管理器"""
    
    def __init__(self):
        self.account_pool: List[Dict] = []
        self.current_account_index = 0
        self.login_strategies: Dict[str, callable] = {}
    
    def add_account(self, username: str, password: str, platform: str, **kwargs):
        """添加账户到池中"""
        account = {
            'username': username,
            'password': password,
            'platform': platform,
            'last_used': None,
            'is_active': True,
            'metadata': kwargs
        }
        self.account_pool.append(account)
    
    async def login(self, page: Page, platform: str) -> bool:
        """执行登录"""
        account = self._get_next_account(platform)
        if not account:
            return False
        
        login_strategy = self.login_strategies.get(platform)
        if not login_strategy:
            return False
        
        try:
            success = await login_strategy(page, account)
            if success:
                account['last_used'] = datetime.now()
            return success
        except Exception as e:
            print(f"Login failed: {e}")
            return False
    
    def register_login_strategy(self, platform: str, strategy: callable):
        """注册登录策略"""
        self.login_strategies[platform] = strategy

class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self.session_manager = SessionManager()
        self.cookie_manager = CookieManager()
        self.login_manager = LoginManager()
    
    async def initialize(self):
        """初始化状态管理器"""
        await self._load_saved_sessions()
        await self._load_cookie_pools()
        await self._load_accounts()
    
    async def get_session_context(self, domain: str, require_login: bool = False) -> SessionInfo:
        """获取会话上下文"""
        # 尝试获取现有会话
        session = await self._find_valid_session(domain)
        
        if not session:
            # 创建新会话
            session = await self._create_new_session(domain)
        
        if require_login and not session.is_logged_in:
            # 执行登录
            await self._perform_login(session, domain)
        
        return session
    
    async def cleanup_expired_sessions(self):
        """清理过期会话"""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if session.expires_at < current_time:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            await self._remove_session(session_id)
```

### MercadoLibre特定配置
```json
{
  "mercadolibre_sessions": {
    "domains": [
      "mercadolibre.com.ar",
      "mercadolibre.com.mx", 
      "mercadolibre.com.br"
    ],
    "session_lifetime": 86400,
    "cookie_settings": {
      "essential_cookies": [
        "_mle_uuid",
        "_mle_tid", 
        "MLSID",
        "x-csrf-token"
      ],
      "tracking_cookies": [
        "_ga",
        "_gid",
        "_fbp"
      ]
    },
    "local_storage_keys": [
      "ml-device-id",
      "ml-user-preferences",
      "ml-cart-items"
    ],
    "login_detection": {
      "logged_in_indicators": [
        ".nav-menu-user-info",
        "[data-testid='user-menu']"
      ],
      "logged_out_indicators": [
        ".nav-login",
        "[data-testid='login-button']"
      ]
    }
  }
}
```

## 预期产出物
1. **会话管理器核心** (`session_manager.py`)
2. **Cookie池管理** (`cookie_manager.py`)
3. **登录状态管理** (`login_manager.py`)
4. **本地存储处理** (`storage_handler.py`)
5. **状态持久化模块** (`state_persistence.py`)
6. **会话恢复机制** (`session_recovery.py`)
7. **用户行为追踪** (`behavior_tracker.py`)
8. **配置文件** (`session_config.json`)
9. **单元测试文件** (`tests/test_session_manager.py`)
10. **集成测试用例** (`tests/integration/test_sessions.py`)

## 验收标准
- [ ] 会话管理器可以正常启动和停止
- [ ] Cookie池管理功能正常
- [ ] 登录状态检测和维护有效
- [ ] 本地存储操作正常
- [ ] 会话持久化和恢复功能正常
- [ ] 多账户管理机制有效
- [ ] 会话超时处理正确
- [ ] 状态同步功能稳定
- [ ] 通过所有单元测试和集成测试
- [ ] 性能测试达到预期指标

## 性能指标
- **会话创建时间**: ≤3秒
- **状态恢复时间**: ≤2秒
- **Cookie池大小**: 支持1000+Cookie
- **并发会话数**: 支持50个并发会话
- **内存使用**: 单会话≤10MB

## 技术要点
- 使用SQLite进行状态持久化
- 实现会话池和Cookie池管理
- 采用异步操作提高性能
- 实现会话故障恢复机制
- 支持多平台状态管理

## 预估工时
1天

## 依赖关系
- 前置任务: Task 03-02 (Playwright浏览器自动化)
- 后置任务: Task 03-07 (任务调度管理)

## 状态
待开始

## 备注
会话和状态管理是爬虫系统稳定运行的关键组件，需要确保在复杂的网站环境中保持会话的连续性和有效性。 