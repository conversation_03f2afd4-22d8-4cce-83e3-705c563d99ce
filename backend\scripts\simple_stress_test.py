#!/usr/bin/env python3
"""
简化的持久化TaskManager压力测试

专注于Redis持久化功能的压力测试，避免复杂依赖
"""

import asyncio
import json
import logging
import random
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import statistics

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class SimpleStressTest:
    """简化的压力测试"""
    
    def __init__(self):
        self.redis_url = "redis://localhost:6379/0"
        self.redis_client = None
        
        # 测试参数
        self.total_batches = 100
        self.concurrent_operations = 20
        self.urls_per_batch = 15
        
        # TaskManager队列键
        self.pending_queue_key = "taskmanager:pending_batches"
        self.running_hash_key = "taskmanager:running_batches"
        self.completed_set_key = "taskmanager:completed_batches"
        self.failed_set_key = "taskmanager:failed_batches"
        
        # 测试指标
        self.metrics = {
            "start_time": None,
            "end_time": None,
            "operations_completed": 0,
            "operations_failed": 0,
            "operation_times": [],
            "redis_metrics": []
        }
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up simple stress test...")
        
        try:
            # 连接Redis
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info("✅ Connected to Redis")
            
            # 清理测试数据
            await self._cleanup_test_data()
            
            self.metrics["start_time"] = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 Cleaning up...")
        
        try:
            if self.redis_client:
                await self._cleanup_test_data()
                await self.redis_client.aclose()
            
            self.metrics["end_time"] = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        test_keys = [
            self.pending_queue_key,
            self.running_hash_key,
            self.completed_set_key,
            self.failed_set_key
        ]
        
        for key in test_keys:
            await self.redis_client.delete(key)
    
    def _generate_batch_data(self, batch_id: str) -> Dict[str, Any]:
        """生成批次数据"""
        urls = [f"https://example.com/product/{i}" for i in range(self.urls_per_batch)]
        
        return {
            "batch_id": batch_id,
            "urls": urls,
            "platform": random.choice(["mercadolibre", "amazon", "ebay"]),
            "priority": random.choice(["urgent", "high", "normal", "low"]),
            "estimated_duration": random.uniform(30.0, 120.0),
            "created_at": datetime.now().isoformat(),
            "metadata": {
                "task_id": random.randint(1, 1000),
                "submission_id": f"submission_{batch_id}",
                "stress_test": True
            }
        }
    
    async def test_queue_operations(self, batch_count: int) -> Dict[str, Any]:
        """测试队列操作"""
        logger.info(f"📤 Testing queue operations with {batch_count} batches...")
        
        start_time = time.time()
        successful_ops = 0
        failed_ops = 0
        
        try:
            # 生成批次数据
            batches = []
            for i in range(batch_count):
                batch_id = f"stress_batch_{i}_{int(time.time())}"
                batch_data = self._generate_batch_data(batch_id)
                batches.append(batch_data)
            
            # 批量添加到队列
            batch_json_list = [json.dumps(batch) for batch in batches]
            await self.redis_client.lpush(self.pending_queue_key, *batch_json_list)
            successful_ops += len(batches)
            
            # 验证队列长度
            queue_length = await self.redis_client.llen(self.pending_queue_key)
            assert queue_length == batch_count, f"Expected {batch_count}, got {queue_length}"
            
            # 模拟处理：从队列取出并移动到运行状态
            for i in range(min(10, batch_count)):  # 处理前10个
                batch_data = await self.redis_client.rpop(self.pending_queue_key)
                if batch_data:
                    batch = json.loads(batch_data)
                    batch_id = batch["batch_id"]
                    
                    # 创建执行记录
                    execution_data = {
                        "batch_id": batch_id,
                        "celery_task_id": f"celery_{batch_id}",
                        "batch": batch,
                        "started_at": datetime.now().isoformat(),
                        "status": "running"
                    }
                    
                    # 添加到运行哈希
                    await self.redis_client.hset(
                        self.running_hash_key,
                        batch_id,
                        json.dumps(execution_data)
                    )
                    successful_ops += 1
            
            # 模拟完成一些任务
            running_batches = await self.redis_client.hkeys(self.running_hash_key)
            for batch_id in running_batches[:5]:  # 完成前5个
                batch_id = batch_id.decode() if isinstance(batch_id, bytes) else batch_id
                
                # 从运行哈希移除
                await self.redis_client.hdel(self.running_hash_key, batch_id)
                
                # 添加到完成集合
                if random.choice([True, False]):  # 随机成功或失败
                    await self.redis_client.sadd(self.completed_set_key, batch_id)
                else:
                    await self.redis_client.sadd(self.failed_set_key, batch_id)
                
                successful_ops += 1
            
        except Exception as e:
            logger.error(f"Queue operation failed: {e}")
            failed_ops += 1
        
        end_time = time.time()
        operation_time = end_time - start_time
        
        return {
            "operation_time": operation_time,
            "successful_ops": successful_ops,
            "failed_ops": failed_ops,
            "throughput": successful_ops / operation_time if operation_time > 0 else 0
        }
    
    async def test_concurrent_operations(self, concurrency: int) -> Dict[str, Any]:
        """测试并发操作"""
        logger.info(f"⚙️ Testing concurrent operations with {concurrency} workers...")
        
        semaphore = asyncio.Semaphore(concurrency)
        
        async def worker(worker_id: int):
            async with semaphore:
                return await self.test_queue_operations(10)  # 每个worker处理10个批次
        
        start_time = time.time()
        
        # 创建并发任务
        tasks = [worker(i) for i in range(concurrency)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        
        # 统计结果
        successful_workers = len([r for r in results if isinstance(r, dict)])
        failed_workers = len([r for r in results if isinstance(r, Exception)])
        
        total_ops = sum(r.get("successful_ops", 0) for r in results if isinstance(r, dict))
        total_time = end_time - start_time
        
        return {
            "total_time": total_time,
            "successful_workers": successful_workers,
            "failed_workers": failed_workers,
            "total_operations": total_ops,
            "overall_throughput": total_ops / total_time if total_time > 0 else 0
        }
    
    async def test_persistence_recovery(self) -> Dict[str, Any]:
        """测试持久化恢复"""
        logger.info("🔄 Testing persistence recovery...")
        
        try:
            # 添加一些测试数据
            test_batches = []
            for i in range(20):
                batch_id = f"recovery_test_{i}"
                batch_data = self._generate_batch_data(batch_id)
                test_batches.append(batch_data)
            
            # 添加到不同的队列
            for i, batch in enumerate(test_batches):
                if i < 10:
                    # 添加到待处理队列
                    await self.redis_client.lpush(self.pending_queue_key, json.dumps(batch))
                elif i < 15:
                    # 添加到运行哈希
                    execution_data = {
                        "batch_id": batch["batch_id"],
                        "celery_task_id": f"celery_{batch['batch_id']}",
                        "batch": batch,
                        "started_at": datetime.now().isoformat(),
                        "status": "running"
                    }
                    await self.redis_client.hset(
                        self.running_hash_key,
                        batch["batch_id"],
                        json.dumps(execution_data)
                    )
                elif i < 18:
                    # 添加到完成集合
                    await self.redis_client.sadd(self.completed_set_key, batch["batch_id"])
                else:
                    # 添加到失败集合
                    await self.redis_client.sadd(self.failed_set_key, batch["batch_id"])
            
            # 记录状态
            before_state = {
                "pending": await self.redis_client.llen(self.pending_queue_key),
                "running": await self.redis_client.hlen(self.running_hash_key),
                "completed": await self.redis_client.scard(self.completed_set_key),
                "failed": await self.redis_client.scard(self.failed_set_key)
            }
            
            # 模拟重启（断开并重连Redis）
            await self.redis_client.aclose()
            await asyncio.sleep(2)
            
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            
            # 检查恢复后的状态
            after_state = {
                "pending": await self.redis_client.llen(self.pending_queue_key),
                "running": await self.redis_client.hlen(self.running_hash_key),
                "completed": await self.redis_client.scard(self.completed_set_key),
                "failed": await self.redis_client.scard(self.failed_set_key)
            }
            
            # 验证数据一致性
            recovery_success = (
                before_state["pending"] == after_state["pending"] and
                before_state["running"] == after_state["running"] and
                before_state["completed"] == after_state["completed"] and
                before_state["failed"] == after_state["failed"]
            )
            
            return {
                "recovery_success": recovery_success,
                "before_state": before_state,
                "after_state": after_state
            }
            
        except Exception as e:
            logger.error(f"Persistence recovery test failed: {e}")
            return {"recovery_success": False, "error": str(e)}
    
    async def collect_redis_metrics(self) -> Dict[str, Any]:
        """收集Redis指标"""
        try:
            info = await self.redis_client.info()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "connected_clients": info.get("connected_clients", 0),
                "ops_per_sec": info.get("instantaneous_ops_per_sec", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to collect Redis metrics: {e}")
            return {}
    
    async def run_stress_test(self):
        """运行压力测试"""
        logger.info("🧪 Starting Simple Stress Test")
        logger.info("=" * 50)
        
        try:
            # 阶段1: 基础队列操作测试
            logger.info("\n📤 Phase 1: Basic Queue Operations")
            logger.info("-" * 30)
            
            basic_result = await self.test_queue_operations(50)
            logger.info(f"✅ Basic operations: {basic_result['successful_ops']} ops in {basic_result['operation_time']:.2f}s")
            logger.info(f"   Throughput: {basic_result['throughput']:.2f} ops/sec")
            
            # 阶段2: 并发操作测试
            logger.info("\n⚙️ Phase 2: Concurrent Operations")
            logger.info("-" * 30)
            
            concurrent_result = await self.test_concurrent_operations(10)
            logger.info(f"✅ Concurrent operations: {concurrent_result['total_operations']} ops in {concurrent_result['total_time']:.2f}s")
            logger.info(f"   Overall throughput: {concurrent_result['overall_throughput']:.2f} ops/sec")
            logger.info(f"   Workers: {concurrent_result['successful_workers']} successful, {concurrent_result['failed_workers']} failed")
            
            # 阶段3: 持久化恢复测试
            logger.info("\n🔄 Phase 3: Persistence Recovery")
            logger.info("-" * 30)
            
            recovery_result = await self.test_persistence_recovery()
            if recovery_result["recovery_success"]:
                logger.info("✅ Persistence recovery test PASSED")
                logger.info(f"   Before: {recovery_result['before_state']}")
                logger.info(f"   After:  {recovery_result['after_state']}")
            else:
                logger.error("❌ Persistence recovery test FAILED")
                if "error" in recovery_result:
                    logger.error(f"   Error: {recovery_result['error']}")
            
            # 阶段4: 最终状态检查
            logger.info("\n📊 Phase 4: Final State Check")
            logger.info("-" * 30)
            
            final_metrics = await self.collect_redis_metrics()
            logger.info(f"✅ Final Redis metrics:")
            logger.info(f"   Memory used: {final_metrics.get('used_memory_human', 'unknown')}")
            logger.info(f"   Connected clients: {final_metrics.get('connected_clients', 0)}")
            logger.info(f"   Total commands: {final_metrics.get('total_commands_processed', 0)}")
            
            # 生成总结报告
            self._generate_summary_report(basic_result, concurrent_result, recovery_result, final_metrics)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Stress test failed: {e}")
            return False
    
    def _generate_summary_report(self, basic_result, concurrent_result, recovery_result, final_metrics):
        """生成总结报告"""
        logger.info("\n" + "=" * 50)
        logger.info("📊 STRESS TEST SUMMARY")
        logger.info("=" * 50)

        if self.metrics["end_time"] and self.metrics["start_time"]:
            total_time = (self.metrics["end_time"] - self.metrics["start_time"]).total_seconds()
        else:
            total_time = 0
        
        logger.info(f"\n📋 Test Overview:")
        logger.info(f"   Total Duration: {total_time:.2f} seconds")
        logger.info(f"   Redis Memory Used: {final_metrics.get('used_memory_human', 'unknown')}")
        logger.info(f"   Total Redis Commands: {final_metrics.get('total_commands_processed', 0)}")
        
        logger.info(f"\n⚡ Performance Results:")
        logger.info(f"   Basic Operations Throughput: {basic_result['throughput']:.2f} ops/sec")
        logger.info(f"   Concurrent Operations Throughput: {concurrent_result['overall_throughput']:.2f} ops/sec")
        logger.info(f"   Persistence Recovery: {'✅ PASS' if recovery_result['recovery_success'] else '❌ FAIL'}")
        
        logger.info(f"\n🎯 Key Findings:")
        if basic_result['throughput'] > 10:
            logger.info("   ✅ Basic queue operations perform well")
        else:
            logger.info("   ⚠️ Basic queue operations may need optimization")
        
        if concurrent_result['failed_workers'] == 0:
            logger.info("   ✅ Concurrent operations are stable")
        else:
            logger.info("   ⚠️ Some concurrent operations failed")
        
        if recovery_result['recovery_success']:
            logger.info("   ✅ Persistence recovery works correctly")
        else:
            logger.info("   ❌ Persistence recovery needs attention")
        
        logger.info("\n" + "=" * 50)
        logger.info("🎉 Simple stress test completed!")


async def main():
    """主函数"""
    print("🧪 Simple PersistentTaskManager Stress Test")
    print("=" * 50)
    
    stress_test = SimpleStressTest()
    
    try:
        # 设置测试环境
        if not await stress_test.setup():
            sys.exit(1)
        
        # 运行压力测试
        success = await stress_test.run_stress_test()
        
        if success:
            logger.info("🎉 Stress test completed successfully!")
        else:
            logger.error("❌ Stress test failed!")
            sys.exit(1)
    
    finally:
        # 清理测试环境
        await stress_test.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
