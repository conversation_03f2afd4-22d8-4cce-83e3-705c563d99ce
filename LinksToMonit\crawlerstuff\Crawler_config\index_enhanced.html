<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl4AI 任务管理器 - 增强版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* 状态颜色 */
        .status-pending { color: #6c757d; }
        .status-processing { color: #0d6efd; }
        .status-completed { color: #198754; }
        .status-failed { color: #dc3545; }
        .status-cancelled { color: #6c757d; }
        .status-paused { color: #fd7e14; }
        .status-retrying { color: #20c997; }
        
        /* 优先级颜色 */
        .priority-urgent { color: #dc3545; font-weight: bold; }
        .priority-high { color: #fd7e14; font-weight: bold; }
        .priority-normal { color: #0d6efd; }
        .priority-low { color: #6c757d; }
        .priority-background { color: #adb5bd; }
        
        /* 优先级徽章 */
        .priority-badge {
            font-size: 0.75em;
            padding: 0.25em 0.5em;
            border-radius: 0.25rem;
        }
        .priority-badge.urgent { background-color: #dc3545; color: white; }
        .priority-badge.high { background-color: #fd7e14; color: white; }
        .priority-badge.normal { background-color: #0d6efd; color: white; }
        .priority-badge.low { background-color: #6c757d; color: white; }
        .priority-badge.background { background-color: #adb5bd; color: white; }

        /* 树形结构样式 */
        .tree-group-row {
            background-color: #f8f9fa;
            font-weight: bold;
            border-left: 4px solid #0d6efd;
        }

        .tree-task-row {
            background-color: #ffffff;
            border-left: 4px solid transparent;
            padding-left: 2rem;
        }

        .tree-task-row td:first-child {
            padding-left: 3rem;
        }

        .tree-expand-btn {
            border: none;
            background: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0;
            margin-right: 0.5rem;
        }

        .tree-expand-btn:hover {
            color: #0d6efd;
        }

        .tree-group-collapsed .tree-task-row {
            display: none;
        }
        
        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        .stats-card {
            border-left: 4px solid;
        }
        
        .stats-card.primary { border-left-color: #0d6efd; }
        .stats-card.success { border-left-color: #198754; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.danger { border-left-color: #dc3545; }
        .stats-card.info { border-left-color: #0dcaf0; }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 任务控制按钮样式 */
        .task-control-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            margin: 0.1rem;
        }
        
        /* 批量操作工具栏 */
        .batch-toolbar {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        
        /* 选择框样式 */
        .task-checkbox {
            transform: scale(1.2);
        }
        
        /* 任务行选中状态 */
        .task-row.selected {
            background-color: rgba(13, 110, 253, 0.1);
        }
        
        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        /* 操作结果提示 */
        .operation-result {
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .operation-result.success {
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        
        .operation-result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c2c7;
            color: #842029;
        }
        
        /* 筛选器样式 */
        .filter-section {
            background-color: #e3f2fd;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        /* 响应式表格 */
        @media (max-width: 768px) {
            .task-control-btn {
                padding: 0.125rem 0.25rem;
                font-size: 0.7rem;
            }
            
            .batch-toolbar {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-robot"></i> Crawl4AI 任务管理器 v2.0
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3" id="connectionStatus">
                    <i class="bi bi-circle-fill text-secondary"></i>
                    <small>连接状态检查中...</small>
                </span>
                <a class="nav-link me-2" href="task_control.html" title="任务控制中心">
                    <i class="bi bi-gear"></i> 控制中心
                </a>
                <a class="nav-link me-2" href="#" onclick="showKeyboardShortcuts()" title="键盘快捷键">
                    <i class="bi bi-question-circle"></i>
                </a>
                <a class="nav-link" href="#" onclick="manualRefresh()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 系统状态 -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card stats-card primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">总任务数</h6>
                                <h3 class="mb-0" id="totalTasks">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-list-task fs-2 text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">成功任务</h6>
                                <h3 class="mb-0" id="successTasks">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle fs-2 text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">处理中</h6>
                                <h3 class="mb-0" id="processingTasks">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-hourglass-split fs-2 text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">失败任务</h6>
                                <h3 class="mb-0" id="failedTasks">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-x-circle fs-2 text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">暂停任务</h6>
                                <h3 class="mb-0" id="pausedTasks">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-pause-circle fs-2 text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card secondary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">队列大小</h6>
                                <h3 class="mb-0" id="queueSize">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-stack fs-2 text-secondary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作结果提示 -->
        <div id="operationResult" style="display: none;"></div>

        <!-- 筛选和批量操作工具栏 -->
        <div class="batch-toolbar">
            <div class="row align-items-center">
                <div class="col-md-5">
                    <h6 class="mb-2"><i class="bi bi-funnel"></i> 筛选条件</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <select class="form-select form-select-sm" id="statusFilter" onchange="applyFilters()">
                                <option value="">所有状态</option>
                                <option value="PENDING">等待中</option>
                                <option value="PROCESSING">处理中</option>
                                <option value="COMPLETED">已完成</option>
                                <option value="FAILED">失败</option>
                                <option value="PAUSED">暂停</option>
                                <option value="CANCELLED">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select form-select-sm" id="priorityFilter" onchange="applyFilters()">
                                <option value="">所有优先级</option>
                                <option value="URGENT">紧急</option>
                                <option value="HIGH">高</option>
                                <option value="NORMAL">普通</option>
                                <option value="LOW">低</option>
                                <option value="BACKGROUND">后台</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control form-control-sm" id="urlFilter" 
                                   placeholder="URL关键词" onchange="applyFilters()">
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <h6 class="mb-2"><i class="bi bi-eye"></i> 视图模式</h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary active" id="groupedViewBtn" onclick="switchViewMode('grouped')">
                            <i class="bi bi-diagram-3"></i> 树形
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="flatViewBtn" onclick="switchViewMode('flat')">
                            <i class="bi bi-list"></i> 列表
                        </button>
                    </div>
                </div>
                <div class="col-md-5">
                    <h6 class="mb-2">
                        <i class="bi bi-check2-square"></i> 批量操作
                        <span class="badge bg-primary" id="selectedCount">0</span>
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="selectAllTasks()">
                            <i class="bi bi-check-all"></i> 全选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                            <i class="bi bi-x-square"></i> 清除
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="batchPauseTasks()" id="batchPauseBtn">
                            <i class="bi bi-pause"></i> 暂停
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="batchResumeTasks()" id="batchResumeBtn">
                            <i class="bi bi-play"></i> 恢复
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="batchCancelTasks()" id="batchCancelBtn">
                            <i class="bi bi-x-circle"></i> 取消
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="batchRestartTasks()" id="batchRestartBtn">
                            <i class="bi bi-arrow-clockwise"></i> 重启
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="batchDeleteTasks()" id="batchDeleteBtn">
                            <i class="bi bi-trash"></i> 删除任务
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="batchDeleteGroups()" id="batchDeleteGroupsBtn">
                            <i class="bi bi-trash3"></i> 删除任务组
                        </button>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-arrow-up-circle"></i> 优先级
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="batchSetPriority('URGENT')">
                                    <span class="priority-badge urgent">紧急</span></a></li>
                                <li><a class="dropdown-item" href="#" onclick="batchSetPriority('HIGH')">
                                    <span class="priority-badge high">高</span></a></li>
                                <li><a class="dropdown-item" href="#" onclick="batchSetPriority('NORMAL')">
                                    <span class="priority-badge normal">普通</span></a></li>
                                <li><a class="dropdown-item" href="#" onclick="batchSetPriority('LOW')">
                                    <span class="priority-badge low">低</span></a></li>
                                <li><a class="dropdown-item" href="#" onclick="batchSetPriority('BACKGROUND')">
                                    <span class="priority-badge background">后台</span></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list"></i> 任务列表</h5>
                        <div class="d-flex align-items-center">
                            <div class="form-check form-switch me-3">
                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                <label class="form-check-label" for="autoRefresh">
                                    <small>自动刷新</small>
                                </label>
                            </div>
                            <button class="btn btn-outline-primary btn-sm" onclick="manualRefresh()" id="refreshBtn">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body position-relative">
                        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在处理...</p>
                            </div>
                        </div>
                        <div id="tasksList">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">加载任务列表...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 配置
        const TASK_MANAGER_API = 'http://localhost:11235';

        // 全局变量
        let autoRefreshInterval;
        let selectedTasks = new Set();
        let selectedGroups = new Set();
        let allTasks = [];
        let allGroups = [];
        let filteredTasks = [];
        let filteredGroups = [];
        let viewMode = 'grouped'; // 'grouped' 或 'flat'
        let isLoading = false; // 防止重复请求
        let lastLoadTime = 0; // 上次加载时间
        let requestCache = new Map(); // 请求缓存

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 Crawl4AI 任务管理器增强版已加载');

            // 初始化
            checkConnectionStatus();
            loadTasks();
            loadPriorityQueueStatus();
            startAutoRefresh();

            // 绑定事件
            bindEvents();
        });

        // 绑定事件
        function bindEvents() {
            // 自动刷新开关
            const autoRefreshElement = document.getElementById('autoRefresh');
            if (autoRefreshElement) {
                autoRefreshElement.addEventListener('change', function() {
                    if (this.checked) {
                        startAutoRefresh();
                    } else {
                        stopAutoRefresh();
                    }
                });
            }

            // 页面可见性检测
            document.addEventListener('visibilitychange', function() {
                const autoRefreshElement = document.getElementById('autoRefresh');
                if (document.hidden) {
                    console.log('页面不可见，暂停自动刷新');
                    stopAutoRefresh();
                } else if (autoRefreshElement && autoRefreshElement.checked) {
                    console.log('页面可见，恢复自动刷新');
                    startAutoRefresh();
                    // 页面重新可见时立即刷新一次
                    if (!isLoading) {
                        manualRefresh();
                    }
                }
            });
        }

        // 检查连接状态
        async function checkConnectionStatus() {
            try {
                const response = await fetch(`${TASK_MANAGER_API}/health`, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                const statusElement = document.getElementById('connectionStatus');
                if (response.ok) {
                    const data = await response.json();
                    statusElement.innerHTML = `
                        <i class="bi bi-circle-fill text-success"></i>
                        <small>已连接 - ${data.status}</small>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('连接检查失败:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <i class="bi bi-circle-fill text-danger"></i>
                    <small>连接失败</small>
                `;
            }
        }

        // 加载任务列表
        async function loadTasks(page = 1, size = 20) {
            // 防止重复请求
            if (isLoading) {
                console.log('正在加载中，跳过重复请求');
                return;
            }

            // 检查是否需要限制请求频率（最少间隔10秒）
            const now = Date.now();
            if (now - lastLoadTime < 10000) {
                console.log('请求过于频繁，跳过本次请求');
                return;
            }

            isLoading = true;
            lastLoadTime = now;

            try {
                const tasksList = document.getElementById('tasksList');

                // 显示加载状态
                if (allTasks.length === 0) {
                    tasksList.innerHTML = `
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">加载任务列表...</p>
                        </div>
                    `;
                }

                // 获取任务组列表
                const groupsResponse = await fetch(`${TASK_MANAGER_API}/tasks/groups?page=${page}&size=${size}`);
                if (!groupsResponse.ok) {
                    throw new Error(`获取任务组失败: ${groupsResponse.statusText}`);
                }
                const groupsData = await groupsResponse.json();

                // 保存任务组数据
                allGroups = groupsData.task_groups || [];

                // 获取所有子任务 - 使用批量处理减少并发请求
                allTasks = [];
                const batchSize = 2; // 每批最多2个并发请求，避免429限流

                for (let i = 0; i < allGroups.length; i += batchSize) {
                    const batch = allGroups.slice(i, i + batchSize);

                    // 并发处理当前批次
                    const batchPromises = batch.map(async (group) => {
                        try {
                            // 检查缓存
                            const cacheKey = `tasks_${group.task_group_id}`;
                            const cached = requestCache.get(cacheKey);
                            if (cached && (now - cached.timestamp < 30000)) { // 30秒缓存
                                return cached.data;
                            }

                            const tasksResponse = await fetch(`${TASK_MANAGER_API}/tasks/group/${group.task_group_id}/tasks?size=100`);
                            if (tasksResponse.ok) {
                                const tasksData = await tasksResponse.json();
                                const tasks = tasksData.tasks || [];

                                // 缓存结果
                                requestCache.set(cacheKey, {
                                    data: tasks,
                                    timestamp: now
                                });

                                return tasks.map(task => ({
                                    ...task,
                                    group_info: {
                                        task_group_id: group.task_group_id,
                                        status: group.status,
                                        created_at: group.created_at
                                    }
                                }));
                            } else if (tasksResponse.status === 429) {
                                console.warn(`任务组 ${group.task_group_id} 请求被限流，跳过`);
                                return [];
                            } else {
                                console.warn(`获取任务组 ${group.task_group_id} 的子任务失败: ${tasksResponse.status}`);
                                return [];
                            }
                        } catch (error) {
                            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                                console.warn(`任务组 ${group.task_group_id} 网络错误，可能是CORS或连接问题`);
                            } else {
                                console.warn(`获取任务组 ${group.task_group_id} 的子任务失败:`, error);
                            }
                            return [];
                        }
                    });

                    // 等待当前批次完成
                    const batchResults = await Promise.all(batchPromises);

                    // 合并结果并验证任务完整性
                    for (const tasks of batchResults) {
                        if (Array.isArray(tasks)) {
                            // 过滤掉无效的任务对象
                            const validTasks = tasks.filter(task => {
                                if (!task || typeof task !== 'object') {
                                    console.warn('发现无效的任务对象:', task);
                                    return false;
                                }
                                if (!task.task_id || !task.status || !task.url) {
                                    console.warn('任务对象缺少必要字段:', task);
                                    return false;
                                }
                                if (!task.group_info || !task.group_info.task_group_id) {
                                    console.warn('任务对象缺少group_info:', task);
                                    return false;
                                }
                                return true;
                            });
                            allTasks.push(...validTasks);
                        }
                    }

                    // 批次间延迟，避免过快请求和429限流
                    if (i + batchSize < allGroups.length) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }

                // 应用筛选
                applyFilters();

                // 更新统计
                updateStatistics();

            } catch (error) {
                console.error('加载任务列表失败:', error);

                let errorMessage = error.message;
                let troubleshootingTips = '';

                // 根据错误类型提供不同的故障排除建议
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = '无法连接到任务管理器服务';
                    troubleshootingTips = `
                        <div class="mt-2">
                            <strong>故障排除建议：</strong>
                            <ul class="small mb-0">
                                <li>检查任务管理器服务是否在端口11235运行</li>
                                <li>确认Docker容器状态：<code>docker ps</code></li>
                                <li>查看服务日志：<code>docker logs crawl4ai-task-manager</code></li>
                                <li>检查网络连接和防火墙设置</li>
                            </ul>
                        </div>
                    `;
                } else if (error.name === 'AbortError') {
                    errorMessage = '请求超时，服务响应缓慢';
                    troubleshootingTips = `
                        <div class="mt-2">
                            <small class="text-muted">服务可能正在启动或负载较高，请稍后重试</small>
                        </div>
                    `;
                }

                const tasksList = document.getElementById('tasksList');
                tasksList.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>加载任务列表失败:</strong> ${errorMessage}
                        ${troubleshootingTips}
                        <div class="mt-3">
                            <button class="btn btn-outline-danger btn-sm" onclick="loadTasks()">
                                <i class="bi bi-arrow-clockwise"></i> 重试
                            </button>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="checkConnectionStatus()">
                                <i class="bi bi-wifi"></i> 检查连接
                            </button>
                        </div>
                    </div>
                `;
            } finally {
                // 确保重置加载状态
                isLoading = false;
            }
        }

        // 加载更多任务组
        async function loadMoreTasks() {
            try {
                showLoading(true);

                // 计算下一页
                const currentPage = Math.floor(allGroups.length / 20) + 1;
                const nextPage = currentPage + 1;

                console.log(`加载第 ${nextPage} 页任务组...`);

                // 获取下一页任务组
                const groupsResponse = await fetch(`${TASK_MANAGER_API}/tasks/groups?page=${nextPage}&size=20`);
                if (!groupsResponse.ok) {
                    throw new Error(`获取任务组失败: ${groupsResponse.statusText}`);
                }
                const groupsData = await groupsResponse.json();

                const newGroups = groupsData.task_groups || [];
                if (newGroups.length === 0) {
                    showInfo('没有更多任务组了');
                    return;
                }

                // 合并新的任务组
                allGroups.push(...newGroups);

                // 获取新任务组的子任务
                const batchSize = 2;
                for (let i = 0; i < newGroups.length; i += batchSize) {
                    const batch = newGroups.slice(i, i + batchSize);

                    const batchPromises = batch.map(async (group) => {
                        try {
                            const tasksResponse = await fetch(`${TASK_MANAGER_API}/tasks/group/${group.task_group_id}/tasks?size=100`);
                            if (tasksResponse.ok) {
                                const tasksData = await tasksResponse.json();
                                const tasks = tasksData.tasks || [];
                                return tasks.map(task => ({
                                    ...task,
                                    group_info: {
                                        task_group_id: group.task_group_id,
                                        status: group.status,
                                        created_at: group.created_at
                                    }
                                }));
                            }
                            return [];
                        } catch (error) {
                            console.warn(`获取任务组 ${group.task_group_id} 的子任务失败:`, error);
                            return [];
                        }
                    });

                    const batchResults = await Promise.all(batchPromises);
                    for (const tasks of batchResults) {
                        if (Array.isArray(tasks)) {
                            allTasks.push(...tasks);
                        }
                    }

                    // 批次间延迟
                    if (i + batchSize < newGroups.length) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }

                // 重新应用筛选和渲染
                applyFilters();
                showSuccess(`已加载 ${newGroups.length} 个新任务组`);

            } catch (error) {
                console.error('加载更多任务组失败:', error);
                showError(`加载更多失败: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // 清理无效任务数据
        function cleanupInvalidTasks() {
            // 清理allTasks中的无效任务
            const validTasks = allTasks.filter(task => {
                if (!task || typeof task !== 'object') {
                    return false;
                }
                if (!task.task_id || !task.status || !task.url) {
                    return false;
                }
                if (!task.group_info || !task.group_info.task_group_id) {
                    return false;
                }
                return true;
            });

            if (validTasks.length !== allTasks.length) {
                console.log(`清理了 ${allTasks.length - validTasks.length} 个无效任务`);
                allTasks = validTasks;
            }

            // 清理选择状态中的无效任务ID
            const validTaskIds = new Set(allTasks.map(task => task.task_id));
            for (const taskId of selectedTasks) {
                if (!validTaskIds.has(taskId)) {
                    selectedTasks.delete(taskId);
                }
            }

            // 清理选择状态中的无效任务组ID
            const validGroupIds = new Set(allGroups.map(group => group.task_group_id));
            for (const groupId of selectedGroups) {
                if (!validGroupIds.has(groupId)) {
                    selectedGroups.delete(groupId);
                }
            }
        }

        // 手动刷新函数
        function manualRefresh() {
            // 重置时间限制，允许立即刷新
            lastLoadTime = 0;
            isLoading = false;

            // 清除缓存
            requestCache.clear();

            // 立即加载
            loadTasks();
            loadPriorityQueueStatus();
        }

        // 应用筛选条件
        function applyFilters() {
            // 首先清理无效的任务数据
            cleanupInvalidTasks();

            const statusFilter = document.getElementById('statusFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;
            const urlFilter = document.getElementById('urlFilter').value.toLowerCase();

            filteredTasks = allTasks.filter(task => {
                // 检查任务对象的完整性
                if (!task || !task.status || !task.url) {
                    console.warn('发现不完整的任务对象:', task);
                    return false;
                }

                // 状态筛选
                if (statusFilter) {
                    const taskStatus = task.status.toUpperCase();
                    const filterStatus = statusFilter.toUpperCase();

                    // 处理状态映射
                    if (filterStatus === 'PROCESSING') {
                        const processingStatuses = ['PROCESSING', 'QUEUED', 'INITIALIZING', 'CACHE_CHECKING', 'FETCHING', 'SCRAPING', 'EXTRACTING', 'LLM_PROCESSING', 'POST_PROCESSING', 'PENDING'];
                        if (!processingStatuses.includes(taskStatus)) {
                            return false;
                        }
                    } else if (taskStatus !== filterStatus) {
                        return false;
                    }
                }

                // 优先级筛选
                if (priorityFilter && task.priority !== priorityFilter) {
                    return false;
                }

                // URL筛选
                if (urlFilter && !task.url.toLowerCase().includes(urlFilter)) {
                    return false;
                }

                return true;
            });

            // 筛选任务组
            filteredGroups = allGroups.filter(group => {
                // 如果没有任何筛选条件，显示所有任务组
                const hasFilters = statusFilter || priorityFilter || urlFilter;

                if (!hasFilters) {
                    // 没有筛选条件时，显示所有任务组
                    return true;
                }

                // 有筛选条件时，只显示包含匹配任务的组
                const groupTasks = filteredTasks.filter(task => {
                    // 检查任务的group_info是否存在
                    if (!task || !task.group_info || !task.group_info.task_group_id) {
                        return false;
                    }
                    return task.group_info.task_group_id === group.task_group_id;
                });

                // 如果有匹配的任务，或者子任务数据为空但任务组存在，都显示
                return groupTasks.length > 0 || allTasks.length === 0;
            });

            // 根据视图模式渲染
            if (viewMode === 'grouped') {
                renderGroupedView();
            } else {
                renderTasksList();
            }
        }

        // 清除筛选条件
        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('priorityFilter').value = '';
            document.getElementById('urlFilter').value = '';
            applyFilters();
        }

        // 更新统计信息
        function updateStatistics() {
            const stats = {
                total: allTasks.length,
                completed: allTasks.filter(t => {
                    const status = t.status.toLowerCase();
                    return status === 'completed';
                }).length,
                processing: allTasks.filter(t => {
                    const status = t.status.toLowerCase();
                    return ['processing', 'queued', 'initializing', 'cache_checking', 'fetching', 'scraping', 'extracting', 'llm_processing', 'post_processing', 'pending'].includes(status);
                }).length,
                failed: allTasks.filter(t => {
                    const status = t.status.toLowerCase();
                    return status === 'failed';
                }).length,
                paused: allTasks.filter(t => {
                    const status = t.status.toLowerCase();
                    return status === 'paused';
                }).length
            };

            document.getElementById('totalTasks').textContent = stats.total;
            document.getElementById('successTasks').textContent = stats.completed;
            document.getElementById('processingTasks').textContent = stats.processing;
            document.getElementById('failedTasks').textContent = stats.failed;
            document.getElementById('pausedTasks').textContent = stats.paused;
        }

        // 加载优先级队列状态
        async function loadPriorityQueueStatus() {
            try {
                const response = await fetch(`${TASK_MANAGER_API}/control/priority/queue-status`);
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('queueSize').textContent = data.queue_size || 0;
                }
            } catch (error) {
                console.warn('获取优先级队列状态失败:', error);
                document.getElementById('queueSize').textContent = '-';
            }
        }

        // 切换视图模式
        function switchViewMode(mode) {
            viewMode = mode;

            // 更新按钮状态
            document.getElementById('groupedViewBtn').classList.toggle('active', mode === 'grouped');
            document.getElementById('flatViewBtn').classList.toggle('active', mode === 'flat');

            // 重新渲染
            if (mode === 'grouped') {
                renderGroupedView();
            } else {
                renderTasksList();
            }

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        // 渲染分组视图（树形结构）
        function renderGroupedView() {
            const container = document.getElementById('tasksList');

            if (filteredGroups.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-inbox fs-1 text-muted"></i>
                        <p class="text-muted mt-2">没有找到任务组</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAllGrouped" onchange="toggleSelectAll()">
                                </th>
                                <th>任务组/任务</th>
                                <th>URL/状态</th>
                                <th>状态/进度</th>
                                <th>创建时间</th>
                                <th>重试次数</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            filteredGroups.forEach(group => {
                const groupTasks = filteredTasks.filter(task =>
                    task && task.group_info && task.group_info.task_group_id === group.task_group_id
                );
                const isGroupSelected = selectedGroups.has(group.task_group_id);
                const isExpanded = !document.getElementById(`group-${group.task_group_id}`)?.classList.contains('tree-group-collapsed');

                // 计算实际的任务统计 - 安全处理空任务列表
                const totalTasks = groupTasks.length;
                const completedTasks = groupTasks.filter(task => {
                    if (!task || !task.status) return false;
                    const status = task.status.toUpperCase();
                    return status === 'COMPLETED';
                }).length;
                const failedTasks = groupTasks.filter(task => {
                    if (!task || !task.status) return false;
                    const status = task.status.toUpperCase();
                    return status === 'FAILED';
                }).length;
                const processingTasks = groupTasks.filter(task => {
                    if (!task || !task.status) return false;
                    const status = task.status.toUpperCase();
                    return ['PROCESSING', 'PENDING', 'QUEUED', 'INITIALIZING', 'CACHE_CHECKING', 'FETCHING', 'SCRAPING', 'EXTRACTING', 'LLM_PROCESSING', 'POST_PROCESSING'].includes(status);
                }).length;

                // 计算进度百分比
                const progressPercent = totalTasks > 0 ? (completedTasks / totalTasks * 100) : 0;

                // 确定任务组状态
                let groupStatus = group.status;
                if (completedTasks === totalTasks && totalTasks > 0) {
                    groupStatus = 'COMPLETED';
                } else if (failedTasks > 0 && (completedTasks + failedTasks) === totalTasks) {
                    groupStatus = 'FAILED';
                } else if (processingTasks > 0) {
                    groupStatus = 'PROCESSING';
                } else if (completedTasks === 0 && failedTasks === 0) {
                    groupStatus = 'PENDING';
                }

                // 任务组行
                html += `
                    <tr class="tree-group-row ${isGroupSelected ? 'table-primary' : ''}" id="group-${group.task_group_id}">
                        <td>
                            <input type="checkbox" id="group-checkbox-${group.task_group_id}"
                                   ${isGroupSelected ? 'checked' : ''}
                                   onchange="toggleGroupSelection('${group.task_group_id}')">
                        </td>
                        <td>
                            <button class="tree-expand-btn" onclick="toggleGroupExpansion('${group.task_group_id}')">
                                <i class="bi ${isExpanded ? 'bi-chevron-down' : 'bi-chevron-right'}" id="expand-icon-${group.task_group_id}"></i>
                            </button>
                            <i class="bi bi-collection text-primary"></i>
                            <strong>${group.task_group_id}</strong>
                            <small class="text-muted ms-2">
                                ${totalTasks > 0 ? `(${totalTasks} 个任务)` : '(子任务加载中...)'}
                            </small>
                        </td>
                        <td>
                            <span class="badge ${getStatusBadgeClass(groupStatus)}">${getStatusText(groupStatus)}</span>
                        </td>
                        <td>
                            <div class="progress" style="height: 8px; width: 100px;">
                                <div class="progress-bar" style="width: ${progressPercent}%"></div>
                            </div>
                            <small class="text-muted">
                                ${totalTasks > 0 ? `${progressPercent.toFixed(1)}% (${completedTasks}/${totalTasks})` : '加载中...'}
                            </small>
                        </td>
                        <td>
                            <small>${formatDateTime(group.created_at)}</small>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                ${totalTasks > 0 ? groupTasks.filter(t => t && t.retry_count > 0).length : '-'}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-success btn-sm" onclick="downloadGroupResults('${group.task_group_id}')" title="下载组结果">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="pauseGroup('${group.task_group_id}')" title="暂停组">
                                    <i class="bi bi-pause"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="cancelGroup('${group.task_group_id}')" title="取消组">
                                    <i class="bi bi-x-circle"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteGroup('${group.task_group_id}')" title="删除组">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;

                // 子任务行
                groupTasks.forEach(task => {
                    const isSelected = selectedTasks.has(task.task_id);
                    const taskRowClass = `tree-task-row ${isSelected ? 'table-secondary' : ''}`;

                    html += `
                        <tr class="${taskRowClass}" data-group="${group.task_group_id}">
                            <td>
                                <input type="checkbox" ${isSelected ? 'checked' : ''}
                                       onchange="toggleTaskSelection('${task.task_id}')">
                            </td>
                            <td>
                                <i class="bi bi-arrow-return-right text-muted me-2"></i>
                                <code class="small">${task.task_id}</code>
                            </td>
                            <td>
                                <a href="${task.url}" target="_blank" class="text-decoration-none small" title="${task.url}">
                                    ${truncateUrl(task.url, 60)}
                                </a>
                            </td>
                            <td>
                                <span class="badge ${getStatusBadgeClass(task.status)}">${getStatusText(task.status)}</span>
                            </td>
                            <td>
                                <small>${formatDateTime(task.created_at)}</small>
                            </td>
                            <td>
                                <span class="badge ${task.retry_count > 0 ? 'bg-warning' : 'bg-secondary'}">${task.retry_count || 0}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm" onclick="downloadTaskResult('${task.task_id}')" title="下载结果">
                                        <i class="bi bi-download"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" onclick="performTaskAction('${task.task_id}', 'pause', '暂停')" title="暂停">
                                        <i class="bi bi-pause"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="performTaskAction('${task.task_id}', 'resume', '恢复')" title="恢复">
                                        <i class="bi bi-play"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteTask('${task.task_id}')" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
            });

            html += `
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <span class="text-muted">
                        显示 ${filteredGroups.length} 个任务组，共 ${filteredTasks.length} 个任务
                        ${allGroups.length > 0 ? `(总共 ${allGroups.length} 个任务组)` : ''}
                    </span>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary btn-sm" onclick="loadTasks()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        ${allGroups.length > 20 ? `
                            <button class="btn btn-outline-primary btn-sm" onclick="loadMoreTasks()">
                                <i class="bi bi-plus-circle"></i> 加载更多
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            container.innerHTML = html;

            // 初始化展开状态（默认展开第一个任务组）
            if (filteredGroups.length > 0) {
                const firstGroupId = filteredGroups[0].task_group_id;
                // 不调用 toggleGroupExpansion，因为默认就是展开状态
            }
        }

        // 渲染任务列表
        function renderTasksList() {
            const container = document.getElementById('tasksList');

            if (filteredTasks.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-inbox fs-1 text-muted"></i>
                        <p class="mt-2 text-muted">没有找到匹配的任务</p>
                        <button class="btn btn-outline-primary" onclick="clearFilters()">
                            <i class="bi bi-funnel"></i> 清除筛选条件
                        </button>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input task-checkbox"
                                           id="selectAllFlat" onchange="toggleSelectAll()">
                                </th>
                                <th>状态</th>
                                <th>优先级</th>
                                <th>URL</th>
                                <th>任务组</th>
                                <th>进度</th>
                                <th>创建时间</th>
                                <th>重试次数</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            filteredTasks.forEach(task => {
                const isSelected = selectedTasks.has(task.task_id);
                const rowClass = isSelected ? 'task-row selected' : 'task-row';

                html += `
                    <tr class="${rowClass}" data-task-id="${task.task_id}">
                        <td>
                            <input type="checkbox" class="form-check-input task-checkbox"
                                   ${isSelected ? 'checked' : ''}
                                   onchange="toggleTaskSelection('${task.task_id}')">
                        </td>
                        <td>
                            <span class="badge bg-${getStatusColor(task.status)} status-${task.status.toLowerCase()}">
                                ${getStatusText(task.status)}
                            </span>
                        </td>
                        <td>
                            <span class="priority-badge ${task.priority?.toLowerCase() || 'normal'}">
                                ${getPriorityText(task.priority)}
                            </span>
                        </td>
                        <td>
                            <a href="${task.url}" target="_blank" class="text-decoration-none" title="${task.url}">
                                ${truncateUrl(task.url, 50)}
                                <i class="bi bi-box-arrow-up-right ms-1"></i>
                            </a>
                        </td>
                        <td>
                            <small class="text-muted">${task.group_info?.task_group_id || '-'}</small>
                        </td>
                        <td>
                            ${renderProgress(task)}
                        </td>
                        <td>
                            <small>${formatDateTime(task.created_at)}</small>
                        </td>
                        <td>
                            <span class="badge bg-${task.retry_count > 0 ? 'warning' : 'secondary'}">
                                ${task.retry_count || 0}
                            </span>
                        </td>
                        <td>
                            ${renderTaskActions(task)}
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <span class="text-muted">
                        显示 ${filteredTasks.length} 个任务，共 ${allTasks.length} 个
                    </span>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm" onclick="loadTasks()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 渲染任务操作按钮
        function renderTaskActions(task) {
            const status = task.status.toUpperCase();
            const canPause = ['PENDING', 'PROCESSING', 'QUEUED', 'INITIALIZING', 'CACHE_CHECKING', 'FETCHING', 'SCRAPING', 'EXTRACTING', 'LLM_PROCESSING', 'POST_PROCESSING'].includes(status);
            const canResume = ['PAUSED'].includes(status);
            const canCancel = !['COMPLETED', 'FAILED', 'CANCELLED'].includes(status);
            const canRetry = ['FAILED'].includes(status);

            let html = '<div class="btn-group btn-group-sm" role="group">';

            if (canPause) {
                html += `<button class="btn btn-outline-warning task-control-btn"
                                onclick="pauseTask('${task.task_id}')" title="暂停任务">
                            <i class="bi bi-pause"></i>
                         </button>`;
            }

            if (canResume) {
                html += `<button class="btn btn-outline-success task-control-btn"
                                onclick="resumeTask('${task.task_id}')" title="恢复任务">
                            <i class="bi bi-play"></i>
                         </button>`;
            }

            if (canCancel) {
                html += `<button class="btn btn-outline-danger task-control-btn"
                                onclick="cancelTask('${task.task_id}')" title="取消任务">
                            <i class="bi bi-x"></i>
                         </button>`;
            }

            if (canRetry) {
                html += `<button class="btn btn-outline-info task-control-btn"
                                onclick="retryTask('${task.task_id}')" title="重试任务">
                            <i class="bi bi-arrow-clockwise"></i>
                         </button>`;
            }

            // 重启按钮（适用于已完成、失败、取消的任务）
            const canRestart = ['COMPLETED', 'FAILED', 'CANCELLED'].includes(status);
            if (canRestart) {
                html += `<button class="btn btn-outline-primary task-control-btn"
                                onclick="restartTask('${task.task_id}')" title="重启任务">
                            <i class="bi bi-arrow-clockwise"></i>
                         </button>`;
            }

            // 下载结果按钮（仅对已完成的任务显示）
            if (status === 'COMPLETED') {
                html += `<button class="btn btn-outline-success task-control-btn"
                                onclick="downloadTaskResult('${task.task_id}')" title="下载任务结果">
                            <i class="bi bi-download"></i>
                         </button>`;
            }

            // 对比结果按钮（仅对已完成的任务显示）
            if (status === 'COMPLETED') {
                html += `<button class="btn btn-outline-info task-control-btn"
                                onclick="compareTaskResults('${task.task_id}')" title="对比WebSocket和API结果">
                            <i class="bi bi-files"></i>
                         </button>`;
            }

            // 删除按钮（适用于所有状态的任务，但需要确认）
            html += `<button class="btn btn-outline-danger task-control-btn"
                            onclick="deleteTask('${task.task_id}')" title="删除任务">
                        <i class="bi bi-trash"></i>
                     </button>`;

            // 优先级设置下拉菜单
            html += `
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary task-control-btn dropdown-toggle"
                            data-bs-toggle="dropdown" title="设置优先级">
                        <i class="bi bi-arrow-up"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="setTaskPriority('${task.task_id}', 'URGENT')">
                            <span class="priority-badge urgent">紧急</span></a></li>
                        <li><a class="dropdown-item" href="#" onclick="setTaskPriority('${task.task_id}', 'HIGH')">
                            <span class="priority-badge high">高</span></a></li>
                        <li><a class="dropdown-item" href="#" onclick="setTaskPriority('${task.task_id}', 'NORMAL')">
                            <span class="priority-badge normal">普通</span></a></li>
                        <li><a class="dropdown-item" href="#" onclick="setTaskPriority('${task.task_id}', 'LOW')">
                            <span class="priority-badge low">低</span></a></li>
                        <li><a class="dropdown-item" href="#" onclick="setTaskPriority('${task.task_id}', 'BACKGROUND')">
                            <span class="priority-badge background">后台</span></a></li>
                    </ul>
                </div>
            `;

            html += '</div>';
            return html;
        }

        // 渲染进度条
        function renderProgress(task) {
            const status = task.status.toUpperCase();
            const progressStyle = 'height: 8px; min-width: 60px;';

            if (status === 'COMPLETED') {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar bg-success" style="width: 100%"></div></div><small class="text-muted">100%</small>`;
            } else if (['PROCESSING', 'FETCHING', 'SCRAPING', 'EXTRACTING', 'LLM_PROCESSING', 'POST_PROCESSING'].includes(status)) {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" style="width: 60%"></div></div><small class="text-muted">处理中</small>`;
            } else if (status === 'FAILED') {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar bg-danger" style="width: 100%"></div></div><small class="text-muted">失败</small>`;
            } else if (status === 'PAUSED') {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar bg-warning" style="width: 50%"></div></div><small class="text-muted">暂停</small>`;
            } else if (status === 'CANCELLED') {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar bg-dark" style="width: 100%"></div></div><small class="text-muted">已取消</small>`;
            } else if (['PENDING', 'QUEUED', 'INITIALIZING'].includes(status)) {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar bg-secondary" style="width: 20%"></div></div><small class="text-muted">等待中</small>`;
            } else if (status === 'RETRYING') {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar progress-bar-striped bg-info" style="width: 40%"></div></div><small class="text-muted">重试中</small>`;
            } else {
                return `<div class="progress" style="${progressStyle}"><div class="progress-bar bg-secondary" style="width: 10%"></div></div><small class="text-muted">${getStatusText(task.status)}</small>`;
            }
        }

        // 任务选择功能
        function toggleTaskSelection(taskId) {
            if (selectedTasks.has(taskId)) {
                selectedTasks.delete(taskId);
            } else {
                selectedTasks.add(taskId);
            }
            updateSelectedCount();

            // 根据视图模式更新显示 - 使用高效的单行更新而不是重新渲染整个视图
            if (viewMode === 'grouped') {
                updateGroupedTaskSelection(taskId);
            } else {
                updateTaskRowSelection(taskId);
            }

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        function toggleSelectAll() {
            // 根据当前视图模式获取对应的复选框
            const selectAllCheckbox = viewMode === 'grouped'
                ? document.getElementById('selectAllGrouped')
                : document.getElementById('selectAllFlat');

            if (!selectAllCheckbox) {
                console.warn('找不到全选复选框');
                return;
            }

            // 根据复选框的实际状态进行操作
            const isChecked = selectAllCheckbox.checked;

            if (isChecked) {
                // 全选所有任务
                filteredTasks.forEach(task => selectedTasks.add(task.task_id));
                // 在树形视图中，也选中所有任务组
                if (viewMode === 'grouped') {
                    filteredGroups.forEach(group => selectedGroups.add(group.task_group_id));
                }
            } else {
                // 清除所有选择
                selectedTasks.clear();
                selectedGroups.clear();
            }

            updateSelectedCount();

            // 使用高效的局部更新而不是重新渲染整个视图
            if (viewMode === 'grouped') {
                // 更新所有任务组的选择状态
                filteredGroups.forEach(group => {
                    updateGroupSelectionUI(group.task_group_id);
                });
                // 更新所有任务的选择状态
                filteredTasks.forEach(task => {
                    updateGroupedTaskSelection(task.task_id);
                });
            } else {
                // 更新所有任务行的选择状态
                filteredTasks.forEach(task => {
                    updateTaskRowSelection(task.task_id);
                });
            }

            // 注意：不要在这里调用updateSelectAllCheckbox()，因为这个函数本身就是由全选复选框触发的
        }

        function selectAllTasks() {
            // 全选所有任务
            filteredTasks.forEach(task => selectedTasks.add(task.task_id));
            // 在树形视图中，也选中所有任务组
            if (viewMode === 'grouped') {
                filteredGroups.forEach(group => selectedGroups.add(group.task_group_id));
            }

            updateSelectedCount();

            // 使用高效的局部更新
            if (viewMode === 'grouped') {
                filteredGroups.forEach(group => {
                    updateGroupSelectionUI(group.task_group_id);
                });
                filteredTasks.forEach(task => {
                    updateGroupedTaskSelection(task.task_id);
                });
            } else {
                filteredTasks.forEach(task => {
                    updateTaskRowSelection(task.task_id);
                });
            }

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        function clearSelection() {
            selectedTasks.clear();
            selectedGroups.clear();
            updateSelectedCount();

            // 使用高效的局部更新
            if (viewMode === 'grouped') {
                filteredGroups.forEach(group => {
                    updateGroupSelectionUI(group.task_group_id);
                });
                filteredTasks.forEach(task => {
                    updateGroupedTaskSelection(task.task_id);
                });
            } else {
                filteredTasks.forEach(task => {
                    updateTaskRowSelection(task.task_id);
                });
            }

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        function updateSelectedCount() {
            const selectedCountElement = document.getElementById('selectedCount');
            if (viewMode === 'grouped') {
                // 在分组视图中显示任务组和任务的选择数量
                selectedCountElement.textContent = `${selectedGroups.size} 组 / ${selectedTasks.size} 任务`;
            } else {
                // 在平铺视图中只显示任务数量
                selectedCountElement.textContent = selectedTasks.size;
            }

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        function updateSelectAllCheckbox() {
            const selectAllCheckbox = viewMode === 'grouped'
                ? document.getElementById('selectAllGrouped')
                : document.getElementById('selectAllFlat');

            if (selectAllCheckbox) {
                const allSelected = filteredTasks.length > 0 && filteredTasks.every(task => selectedTasks.has(task.task_id));
                const someSelected = filteredTasks.some(task => selectedTasks.has(task.task_id));

                selectAllCheckbox.checked = allSelected;
                selectAllCheckbox.indeterminate = someSelected && !allSelected;
            }
        }

        // 任务组选择相关函数
        function toggleGroupSelection(groupId) {
            // 获取复选框当前状态
            const groupCheckbox = document.getElementById(`group-checkbox-${groupId}`);
            if (!groupCheckbox) {
                console.warn(`找不到任务组复选框: group-checkbox-${groupId}`);
                return;
            }

            // 根据复选框的实际状态来更新选择
            const isChecked = groupCheckbox.checked;

            if (isChecked) {
                selectedGroups.add(groupId);
            } else {
                selectedGroups.delete(groupId);
            }

            // 更新组内所有任务的选择状态
            const groupTasks = filteredTasks.filter(task => task.group_info.task_group_id === groupId);

            if (isChecked) {
                groupTasks.forEach(task => selectedTasks.add(task.task_id));
            } else {
                groupTasks.forEach(task => selectedTasks.delete(task.task_id));
            }

            updateSelectedCount();

            // 更新任务组行的样式
            const groupRow = document.getElementById(`group-${groupId}`);
            if (groupRow) {
                if (isChecked) {
                    groupRow.classList.add('table-primary');
                } else {
                    groupRow.classList.remove('table-primary');
                }
            }

            // 更新子任务的选择状态
            groupTasks.forEach(task => {
                if (viewMode === 'grouped') {
                    updateGroupedTaskSelection(task.task_id);
                } else {
                    updateTaskRowSelection(task.task_id);
                }
            });

            // 更新全选复选框状态
            updateSelectAllCheckbox();
        }

        function updateGroupSelectionUI(groupId) {
            // 更新任务组复选框状态 - 使用ID选择器
            const groupCheckbox = document.getElementById(`group-checkbox-${groupId}`);
            if (groupCheckbox) {
                const isSelected = selectedGroups.has(groupId);
                groupCheckbox.checked = isSelected;
            }

            // 更新任务组行的样式
            const groupRow = document.getElementById(`group-${groupId}`);
            if (groupRow) {
                if (selectedGroups.has(groupId)) {
                    groupRow.classList.add('table-primary');
                } else {
                    groupRow.classList.remove('table-primary');
                }
            }
        }

        function toggleGroupTasksSelection(groupId, checked) {
            const groupTasks = filteredTasks.filter(task => task.group_info.task_group_id === groupId);

            if (checked) {
                groupTasks.forEach(task => selectedTasks.add(task.task_id));
                selectedGroups.add(groupId);
            } else {
                groupTasks.forEach(task => selectedTasks.delete(task.task_id));
                selectedGroups.delete(groupId);
            }

            updateSelectedCount();

            // 使用高效的局部更新
            updateGroupSelectionUI(groupId);
            groupTasks.forEach(task => {
                if (viewMode === 'grouped') {
                    updateGroupedTaskSelection(task.task_id);
                } else {
                    updateTaskRowSelection(task.task_id);
                }
            });
        }

        function toggleGroupExpansion(groupId) {
            const groupRow = document.getElementById(`group-${groupId}`);
            const icon = document.getElementById(`expand-icon-${groupId}`);
            const taskRows = document.querySelectorAll(`tr[data-group="${groupId}"]`);

            if (groupRow.classList.contains('tree-group-collapsed')) {
                // 展开
                groupRow.classList.remove('tree-group-collapsed');
                taskRows.forEach(row => row.style.display = '');
                icon.classList.remove('bi-chevron-right');
                icon.classList.add('bi-chevron-down');
            } else {
                // 收起
                groupRow.classList.add('tree-group-collapsed');
                taskRows.forEach(row => row.style.display = 'none');
                icon.classList.remove('bi-chevron-down');
                icon.classList.add('bi-chevron-right');
            }
        }

        // 任务组操作函数
        async function downloadGroupResults(groupId) {
            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/tasks/group/${groupId}/download?format=json`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `group_${groupId}_results.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showSuccess('下载成功', `任务组 ${groupId} 的结果已下载`);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('下载任务组结果失败:', error);
                showError('下载失败', error.message);
            } finally {
                showLoading(false);
            }
        }

        async function pauseGroup(groupId) {
            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/tasks/group/${groupId}/pause`, {
                    method: 'POST',
                    headers: { 'Accept': 'application/json' }
                });

                if (response.ok) {
                    showSuccess('暂停成功', `任务组 ${groupId} 已暂停`);
                    loadTasks();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('暂停任务组失败:', error);
                showError('暂停失败', error.message);
            } finally {
                showLoading(false);
            }
        }

        async function cancelGroup(groupId) {
            if (!confirm(`确定要取消任务组 ${groupId} 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/tasks/group/${groupId}/cancel`, {
                    method: 'POST',
                    headers: { 'Accept': 'application/json' }
                });

                if (response.ok) {
                    showSuccess('取消成功', `任务组 ${groupId} 已取消`);
                    loadTasks();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('取消任务组失败:', error);
                showError('取消失败', error.message);
            } finally {
                showLoading(false);
            }
        }

        // 删除任务组
        async function deleteGroup(groupId) {
            if (!confirm(`⚠️ 确定要删除任务组 ${groupId} 吗？\n\n此操作不可撤销，将永久删除任务组及其所有子任务！`)) {
                return;
            }

            try {
                showLoading(true);
                const response = await fetch(`${TASK_MANAGER_API}/tasks/group/${groupId}/remove`, {
                    method: 'POST',
                    headers: { 'Accept': 'application/json' }
                });

                if (response.ok) {
                    const result = await response.json();
                    showSuccess('删除成功', `任务组 ${groupId} 已删除`);

                    // 从选择状态中移除
                    selectedGroups.delete(groupId);

                    // 重新加载任务列表
                    loadTasks();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('删除任务组失败:', error);
                showError('删除失败', error.message);
            } finally {
                showLoading(false);
            }
        }

        function updateTaskRowSelection(taskId) {
            const row = document.querySelector(`tr[data-task-id="${taskId}"]`);
            if (row) {
                const checkbox = row.querySelector('input[type="checkbox"]');
                const isSelected = selectedTasks.has(taskId);

                if (checkbox) {
                    checkbox.checked = isSelected;
                }

                if (isSelected) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            }
        }

        function updateGroupedTaskSelection(taskId) {
            // 在树形视图中更新任务选择状态
            const taskRow = document.querySelector(`tr[data-group] input[onchange*="${taskId}"]`);
            if (taskRow) {
                taskRow.checked = selectedTasks.has(taskId);
                const row = taskRow.closest('tr');
                if (row) {
                    if (selectedTasks.has(taskId)) {
                        row.classList.add('selected');
                    } else {
                        row.classList.remove('selected');
                    }
                }
            }
        }

        // 单任务控制功能
        async function pauseTask(taskId) {
            await performTaskAction(taskId, 'pause', '暂停');
        }

        async function resumeTask(taskId) {
            await performTaskAction(taskId, 'resume', '恢复');
        }

        async function cancelTask(taskId) {
            if (!confirm('确定要取消这个任务吗？此操作不可撤销。')) {
                return;
            }
            await performTaskAction(taskId, 'cancel', '取消');
        }

        async function retryTask(taskId) {
            await performTaskAction(taskId, 'retry', '重试');
        }

        async function setTaskPriority(taskId, priority) {
            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/control/task/${taskId}/priority`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ priority: priority })
                });

                if (response.ok) {
                    showSuccess(`任务优先级已设置为${getPriorityText(priority)}`);
                    loadTasks();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('设置任务优先级失败:', error);
                showError('设置优先级失败', error.message);
            } finally {
                showLoading(false);
            }
        }

        // 通用任务操作函数
        async function performTaskAction(taskId, action, actionName) {
            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/control/task/${taskId}/${action}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    showSuccess(`任务${actionName}成功`);
                    loadTasks();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error(`${actionName}任务失败:`, error);
                showError(`${actionName}失败`, error.message);
            } finally {
                showLoading(false);
            }
        }

        // 批量操作功能
        async function batchPauseTasks() {
            await performBatchOperation('pause', '暂停');
        }

        async function batchResumeTasks() {
            await performBatchOperation('resume', '恢复');
        }

        async function batchCancelTasks() {
            if (!confirm(`确定要取消选中的 ${selectedTasks.size} 个任务吗？此操作不可撤销。`)) {
                return;
            }
            await performBatchOperation('cancel', '取消');
        }

        async function batchRestartTasks() {
            if (!confirm(`确定要重启选中的 ${selectedTasks.size} 个任务吗？这将重新执行已完成、失败或取消的任务。`)) {
                return;
            }
            await performBatchOperation('restart', '重启');
        }

        async function batchDeleteTasks() {
            if (!confirm(`⚠️ 确定要删除选中的 ${selectedTasks.size} 个任务吗？\n\n此操作不可撤销，将永久删除任务数据！`)) {
                return;
            }
            await performBatchOperation('delete', '删除');
        }

        // 批量删除任务组
        async function batchDeleteGroups() {
            if (selectedGroups.size === 0) {
                showError('请先选择要删除的任务组');
                return;
            }

            if (!confirm(`⚠️ 确定要删除选中的 ${selectedGroups.size} 个任务组吗？\n\n此操作不可撤销，将永久删除任务组及其所有子任务！`)) {
                return;
            }

            try {
                showLoading(true);
                const groupIds = Array.from(selectedGroups);
                const results = [];

                // 逐个删除任务组
                for (const groupId of groupIds) {
                    try {
                        const response = await fetch(`${TASK_MANAGER_API}/tasks/group/${groupId}/remove`, {
                            method: 'POST',
                            headers: { 'Accept': 'application/json' }
                        });

                        if (response.ok) {
                            results.push({ groupId, success: true });
                        } else {
                            const errorData = await response.json().catch(() => ({}));
                            results.push({
                                groupId,
                                success: false,
                                error: errorData.detail || `HTTP ${response.status}`
                            });
                        }
                    } catch (error) {
                        results.push({
                            groupId,
                            success: false,
                            error: error.message
                        });
                    }
                }

                // 统计结果
                const successCount = results.filter(r => r.success).length;
                const failedCount = results.filter(r => !r.success).length;

                // 清除选择状态
                selectedGroups.clear();
                updateSelectedCount();

                // 显示结果
                if (failedCount === 0) {
                    showSuccess(`批量删除成功`, `已删除 ${successCount} 个任务组`);
                } else {
                    const failedGroups = results.filter(r => !r.success);
                    let detailMessage = `成功删除 ${successCount} 个任务组`;
                    if (failedCount > 0) {
                        detailMessage += `\n失败 ${failedCount} 个任务组:\n`;
                        failedGroups.forEach(r => {
                            detailMessage += `- ${r.groupId}: ${r.error}\n`;
                        });
                    }
                    showError(`批量删除部分失败`, detailMessage);
                }

                // 重新加载任务列表
                loadTasks();

            } catch (error) {
                console.error('批量删除任务组失败:', error);
                showError(`批量删除失败: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // 删除单个任务
        async function deleteTask(taskId) {
            if (!confirm(`⚠️ 确定要删除任务 ${taskId} 吗？\n\n此操作不可撤销，将永久删除任务数据！`)) {
                return;
            }

            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/control/task/${taskId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showSuccess('删除成功', `任务 ${taskId} 已删除`);
                    loadTasks(); // 重新加载任务列表
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                showError(`删除失败: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        async function batchSetPriority(priority) {
            await performBatchOperation('set-priority', '设置优先级', { priority: priority });
        }

        // 通用批量操作函数
        async function performBatchOperation(operation, operationName, params = {}) {
            if (selectedTasks.size === 0) {
                showError('请先选择要操作的任务');
                return;
            }

            try {
                showLoading(true);

                // 将操作名称转换为API期望的格式
                const operationTypeMap = {
                    'pause': 'PAUSE',
                    'resume': 'RESUME',
                    'cancel': 'CANCEL',
                    'restart': 'RESTART',
                    'set-priority': 'SET_PRIORITY',
                    'retry': 'RETRY',
                    'delete': 'DELETE'
                };

                const requestBody = {
                    operation_type: operationTypeMap[operation] || operation.toUpperCase(),
                    task_ids: Array.from(selectedTasks),
                    operation_params: params
                };

                // 根据操作类型选择HTTP方法
                const httpMethod = operation === 'delete' ? 'DELETE' : 'POST';

                const response = await fetch(`${TASK_MANAGER_API}/control/batch/${operation}`, {
                    method: httpMethod,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    const result = await response.json();

                    // 构建详细的结果消息
                    let message = `成功: ${result.success_count}, 失败: ${result.failed_count}`;
                    if (result.skipped_count > 0) {
                        message += `, 跳过: ${result.skipped_count}`;
                    }

                    // 如果有失败的任务，显示失败原因
                    let detailMessage = message;
                    if (result.failed_tasks && result.failed_tasks.length > 0) {
                        const failureDetails = result.failed_tasks.map(task =>
                            `${task.task_id}: ${task.error}`
                        ).join('\n');
                        detailMessage = `${message}\n\n失败详情:\n${failureDetails}`;
                    }

                    // 如果有跳过的任务，显示跳过原因
                    if (result.skipped_tasks && result.skipped_tasks.length > 0) {
                        const skippedDetails = result.skipped_tasks.map(task =>
                            `${task.task_id}: ${task.reason}`
                        ).join('\n');
                        detailMessage += `\n\n跳过详情:\n${skippedDetails}`;
                    }

                    showSuccess(`批量${operationName}操作完成`, detailMessage);
                    clearSelection();
                    loadTasks();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error(`批量${operationName}失败:`, error);
                showError(`批量${operationName}失败`, error.message);
            } finally {
                showLoading(false);
            }
        }

        // 自动刷新功能
        function startAutoRefresh() {
            stopAutoRefresh();
            autoRefreshInterval = setInterval(() => {
                // 只有在页面可见且没有正在进行的请求时才刷新
                if (!document.hidden && !isLoading) {
                    loadTasks();
                    loadPriorityQueueStatus();
                }
            }, 60000); // 改为60秒刷新一次，减少请求频率
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        function manualRefresh() {
            const refreshBtn = document.getElementById('refreshBtn');
            const originalContent = refreshBtn.innerHTML;

            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
            refreshBtn.disabled = true;

            Promise.all([
                loadTasks(),
                loadPriorityQueueStatus(),
                checkConnectionStatus()
            ]).finally(() => {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            });
        }

        // UI辅助函数
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        function showSuccess(title, message = '') {
            showOperationResult('success', title, message);
        }

        function showError(title, message = '') {
            showOperationResult('error', title, message);
        }

        function showOperationResult(type, title, message = '') {
            const container = document.getElementById('operationResult');
            const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

            container.className = `operation-result ${type}`;
            container.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-${icon} me-2"></i>
                    <div>
                        <strong>${title}</strong>
                        ${message ? `<div class="small">${message}</div>` : ''}
                    </div>
                    <button type="button" class="btn-close ms-auto" onclick="hideOperationResult()"></button>
                </div>
            `;
            container.style.display = 'block';

            // 5秒后自动隐藏
            setTimeout(() => {
                hideOperationResult();
            }, 5000);
        }

        function hideOperationResult() {
            document.getElementById('operationResult').style.display = 'none';
        }

        // 工具函数
        function getStatusColor(status) {
            const colors = {
                // 基础状态
                'pending': 'secondary',
                'processing': 'primary',
                'completed': 'success',
                'failed': 'danger',
                'cancelled': 'dark',
                'paused': 'warning',
                // 大写状态（兼容）
                'PENDING': 'secondary',
                'PROCESSING': 'primary',
                'COMPLETED': 'success',
                'FAILED': 'danger',
                'CANCELLED': 'dark',
                'PAUSED': 'warning',
                'RETRYING': 'info',
                // 详细状态
                'queued': 'secondary',
                'initializing': 'info',
                'cache_checking': 'info',
                'fetching': 'primary',
                'scraping': 'primary',
                'extracting': 'primary',
                'llm_processing': 'primary',
                'post_processing': 'primary'
            };
            return colors[status] || 'secondary';
        }

        function getStatusText(status) {
            const texts = {
                // 基础状态
                'pending': '等待中',
                'processing': '处理中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消',
                'paused': '暂停',
                // 大写状态（兼容）
                'PENDING': '等待中',
                'PROCESSING': '处理中',
                'COMPLETED': '已完成',
                'FAILED': '失败',
                'CANCELLED': '已取消',
                'PAUSED': '暂停',
                'RETRYING': '重试中',
                // 详细状态
                'queued': '排队中',
                'initializing': '初始化',
                'cache_checking': '检查缓存',
                'fetching': '获取页面',
                'scraping': '解析页面',
                'extracting': '提取信息',
                'llm_processing': 'AI处理',
                'post_processing': '后处理'
            };
            return texts[status] || status;
        }

        function getPriorityText(priority) {
            const texts = {
                'URGENT': '紧急',
                'HIGH': '高',
                'NORMAL': '普通',
                'LOW': '低',
                'BACKGROUND': '后台'
            };
            return texts[priority] || '普通';
        }

        function truncateUrl(url, maxLength) {
            if (url.length <= maxLength) return url;
            return url.substring(0, maxLength) + '...';
        }

        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function getStatusBadgeClass(status) {
            const statusMap = {
                'PENDING': 'bg-secondary',
                'PROCESSING': 'bg-primary',
                'COMPLETED': 'bg-success',
                'FAILED': 'bg-danger',
                'CANCELLED': 'bg-secondary',
                'PAUSED': 'bg-warning',
                'RETRYING': 'bg-info'
            };
            return statusMap[status.toUpperCase()] || 'bg-secondary';
        }

        function getStatusText(status) {
            const statusMap = {
                'PENDING': '等待中',
                'PROCESSING': '处理中',
                'COMPLETED': '已完成',
                'FAILED': '失败',
                'CANCELLED': '已取消',
                'PAUSED': '已暂停',
                'RETRYING': '重试中'
            };
            return statusMap[status.toUpperCase()] || status;
        }

        // 键盘快捷键
        function showKeyboardShortcuts() {
            alert(`键盘快捷键：

F5 或 Ctrl+R - 刷新页面
Ctrl+A - 全选任务
Escape - 清除选择
Space - 暂停/恢复自动刷新

批量操作：
选中任务后使用工具栏按钮进行批量操作`);
        }

        // 键盘事件监听
        document.addEventListener('keydown', function(e) {
            // Ctrl+A 全选
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                selectAllTasks();
            }

            // Escape 清除选择
            if (e.key === 'Escape') {
                clearSelection();
            }

            // Space 切换自动刷新
            if (e.key === ' ' && e.target.tagName !== 'INPUT') {
                e.preventDefault();
                const autoRefreshCheckbox = document.getElementById('autoRefresh');
                autoRefreshCheckbox.checked = !autoRefreshCheckbox.checked;
                autoRefreshCheckbox.dispatchEvent(new Event('change'));
            }
        });

        // 重启任务
        async function restartTask(taskId) {
            await performTaskAction(taskId, 'restart', '重启');
        }

        // 下载任务结果
        async function downloadTaskResult(taskId, includeRaw = false) {
            try {
                showLoading(true);

                const suffix = includeRaw ? '&include_raw=true' : '';
                const response = await fetch(`${TASK_MANAGER_API}/tasks/task/${taskId}/download?format=json${suffix}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;

                    // 从响应头获取文件名，或生成默认文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = `task_result_${taskId}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showSuccess('下载成功', `任务结果已下载: ${filename}`);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('下载任务结果失败:', error);
                showError(`下载失败: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // 对比任务结果
        async function compareTaskResults(taskId) {
            try {
                showLoading(true);

                const response = await fetch(`${TASK_MANAGER_API}/tasks/task/${taskId}/raw-results`);

                if (response.ok) {
                    const data = await response.json();
                    showComparisonModal(taskId, data);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('获取对比数据失败:', error);
                showError(`获取对比数据失败: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // 显示对比模态框
        function showComparisonModal(taskId, data) {
            const hasWebSocket = data.has_websocket_result;
            const hasAPI = data.has_api_result;
            const hasValidation = data.has_validation;

            let modalContent = `
                <div class="modal fade" id="comparisonModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-files"></i> 任务结果对比 - ${taskId}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            <strong>数据状态:</strong>
                                            WebSocket结果: ${hasWebSocket ? '✅ 有' : '❌ 无'} |
                                            API结果: ${hasAPI ? '✅ 有' : '❌ 无'} |
                                            一致性验证: ${hasValidation ? '✅ 有' : '❌ 无'}
                                        </div>
                                    </div>
                                </div>

                                <ul class="nav nav-tabs" id="comparisonTabs" role="tablist">
                                    ${hasWebSocket ? '<li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#websocket-tab">WebSocket结果</a></li>' : ''}
                                    ${hasAPI ? '<li class="nav-item"><a class="nav-link' + (!hasWebSocket ? ' active' : '') + '" data-bs-toggle="tab" href="#api-tab">API结果</a></li>' : ''}
                                    ${hasValidation ? '<li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#validation-tab">一致性验证</a></li>' : ''}
                                </ul>

                                <div class="tab-content mt-3">
                                    ${hasWebSocket ? `
                                        <div class="tab-pane fade show active" id="websocket-tab">
                                            <h6>WebSocket推送结果</h6>
                                            <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"><code>${JSON.stringify(data.raw_results.websocket, null, 2)}</code></pre>
                                        </div>
                                    ` : ''}

                                    ${hasAPI ? `
                                        <div class="tab-pane fade${!hasWebSocket ? ' show active' : ''}" id="api-tab">
                                            <h6>API轮询结果</h6>
                                            <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"><code>${JSON.stringify(data.raw_results.api, null, 2)}</code></pre>
                                        </div>
                                    ` : ''}

                                    ${hasValidation ? `
                                        <div class="tab-pane fade" id="validation-tab">
                                            <h6>一致性验证结果</h6>
                                            <div class="alert alert-${data.raw_results.validation.is_consistent ? 'success' : 'warning'}">
                                                <strong>验证结果:</strong> ${data.raw_results.validation.is_consistent ? '✅ 一致' : '⚠️ 不一致'}
                                            </div>
                                            <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"><code>${JSON.stringify(data.raw_results.validation, null, 2)}</code></pre>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" onclick="downloadTaskResult('${taskId}', true)">
                                    <i class="bi bi-download"></i> 下载完整数据
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除现有的模态框
            const existingModal = document.getElementById('comparisonModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', modalContent);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('comparisonModal'));
            modal.show();
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>
