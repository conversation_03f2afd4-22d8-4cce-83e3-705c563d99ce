#!/usr/bin/env python3
"""
监控任务系统测试脚本

测试新的监控任务API是否正常工作
"""

import requests
import json
import time
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000"

def test_monitoring_tasks():
    """测试监控任务API"""
    print("🧪 开始测试监控任务系统...")
    
    # 1. 测试获取任务列表（应该为空）
    print("\n1. 测试获取任务列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/monitoring-tasks/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取任务列表成功: {len(data.get('data', []))} 个任务")
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 2. 测试创建监控任务
    print("\n2. 测试创建监控任务...")
    task_data = {
        "name": "测试监控任务",
        "description": "这是一个测试用的监控任务",
        "schedule": {
            "type": "daily",
            "time": "09:00",
            "timezone": "Asia/Shanghai"
        },
        "config": {
            "platform": "taobao",
            "priority": "medium",
            "retry_count": 3,
            "timeout": 30,
            "batch_size": 10
        },
        "urls": [
            "https://item.taobao.com/item.htm?id=123456789",
            "https://item.taobao.com/item.htm?id=987654321"
        ]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/monitoring-tasks/",
            json=task_data,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            task_id = data.get('data', {}).get('id')
            print(f"✅ 创建监控任务成功: {task_id}")
            print(f"   任务名称: {data.get('data', {}).get('name')}")
            print(f"   URL数量: {data.get('data', {}).get('total_urls')}")
            return task_id
        else:
            print(f"❌ 创建监控任务失败: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_task_operations(task_id):
    """测试任务操作"""
    if not task_id:
        print("⏭️ 跳过任务操作测试（没有有效的任务ID）")
        return
    
    print(f"\n3. 测试任务操作 (任务ID: {task_id})...")
    
    # 3.1 获取任务详情
    print("   3.1 获取任务详情...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/monitoring-tasks/{task_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取任务详情成功")
            print(f"      状态: {data.get('data', {}).get('status')}")
        else:
            print(f"   ❌ 获取任务详情失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 3.2 启动任务
    print("   3.2 启动任务...")
    try:
        response = requests.post(f"{BASE_URL}/api/v1/monitoring-tasks/{task_id}/start")
        if response.status_code == 200:
            print(f"   ✅ 启动任务成功")
        else:
            print(f"   ❌ 启动任务失败: {response.status_code}")
            print(f"      {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 3.3 暂停任务
    print("   3.3 暂停任务...")
    try:
        response = requests.post(f"{BASE_URL}/api/v1/monitoring-tasks/{task_id}/pause")
        if response.status_code == 200:
            print(f"   ✅ 暂停任务成功")
        else:
            print(f"   ❌ 暂停任务失败: {response.status_code}")
            print(f"      {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def test_final_check():
    """最终检查"""
    print("\n4. 最终检查...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/monitoring-tasks/")
        if response.status_code == 200:
            data = response.json()
            tasks = data.get('data', [])
            print(f"✅ 最终任务数量: {len(tasks)}")
            for task in tasks:
                print(f"   - {task.get('name')} (状态: {task.get('status')})")
        else:
            print(f"❌ 最终检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 监控任务系统测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务器运行正常")
        else:
            print("⚠️ 后端服务器响应异常")
    except Exception as e:
        print(f"❌ 无法连接到后端服务器: {e}")
        print("请确保后端服务器正在运行 (docker-compose up -d)")
        return
    
    # 运行测试
    task_id = test_monitoring_tasks()
    test_task_operations(task_id)
    test_final_check()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n📋 下一步:")
    print("1. 访问 http://localhost:3000/monitoring 查看前端页面")
    print("2. 尝试创建新的监控任务")
    print("3. 测试任务的启动和暂停功能")

if __name__ == "__main__":
    main()
