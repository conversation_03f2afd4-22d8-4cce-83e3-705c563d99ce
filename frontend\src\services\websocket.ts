import React from 'react';
import { WebSocketMessage } from '../types';

type MessageHandler = (message: WebSocketMessage) => void;
type ConnectionHandler = () => void;
type ErrorHandler = (error: Event) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000;
  private messageHandlers: Map<string, MessageHandler[]> = new Map();
  private connectionHandlers: ConnectionHandler[] = [];
  private disconnectionHandlers: ConnectionHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];
  private isConnecting = false;
  private connectionQueue: Array<{ resolve: Function; reject: Function }> = [];

  constructor(url?: string) {
    this.url = url || process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws';
  }

  // 连接WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        // 如果正在连接，将请求加入队列
        this.connectionQueue.push({ resolve, reject });
        return;
      }

      this.isConnecting = true;

      // 如果Celery监控页面活跃，延迟连接以避免冲突
      const delay = (window as any).celeryMonitoringActive ? 1000 : 0;

      setTimeout(() => {
        try {
          this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.connectionHandlers.forEach(handler => handler());

          // 解决主要的连接请求
          resolve();

          // 解决队列中的所有连接请求
          this.connectionQueue.forEach(({ resolve: queueResolve }) => {
            queueResolve();
          });
          this.connectionQueue = [];
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.disconnectionHandlers.forEach(handler => handler());
          
          // 自动重连
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.errorHandlers.forEach(handler => handler(error));

          // 拒绝主要的连接请求
          reject(error);

          // 拒绝队列中的所有连接请求
          this.connectionQueue.forEach(({ reject: queueReject }) => {
            queueReject(error);
          });
          this.connectionQueue = [];
        };

        } catch (error) {
          this.isConnecting = false;
          reject(error);
        }
      }, delay);
    });
  }

  // 断开连接
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
  }

  // 发送消息
  send(message: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  // 订阅消息类型
  subscribe(messageType: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, []);
    }
    this.messageHandlers.get(messageType)!.push(handler);

    // 返回取消订阅函数
    return () => {
      const handlers = this.messageHandlers.get(messageType);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  // 订阅连接事件
  onConnect(handler: ConnectionHandler): () => void {
    this.connectionHandlers.push(handler);
    return () => {
      const index = this.connectionHandlers.indexOf(handler);
      if (index > -1) {
        this.connectionHandlers.splice(index, 1);
      }
    };
  }

  // 订阅断开连接事件
  onDisconnect(handler: ConnectionHandler): () => void {
    this.disconnectionHandlers.push(handler);
    return () => {
      const index = this.disconnectionHandlers.indexOf(handler);
      if (index > -1) {
        this.disconnectionHandlers.splice(index, 1);
      }
    };
  }

  // 订阅错误事件
  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.push(handler);
    return () => {
      const index = this.errorHandlers.indexOf(handler);
      if (index > -1) {
        this.errorHandlers.splice(index, 1);
      }
    };
  }

  // 获取连接状态
  getConnectionState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED;
  }

  // 是否已连接
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // 处理消息
  private handleMessage(message: WebSocketMessage): void {
    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in message handler:', error);
        }
      });
    }
  }

  // 安排重连
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }
}

// 创建全局WebSocket服务实例
export const wsService = new WebSocketService();

// React Hook for WebSocket
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = React.useState(false);
  const [connectionState, setConnectionState] = React.useState(WebSocket.CLOSED);

  React.useEffect(() => {
    const updateConnectionState = () => {
      const state = wsService.getConnectionState();
      setConnectionState(state);
      setIsConnected(state === WebSocket.OPEN);
    };

    const unsubscribeConnect = wsService.onConnect(updateConnectionState);
    const unsubscribeDisconnect = wsService.onDisconnect(updateConnectionState);
    const unsubscribeError = wsService.onError(updateConnectionState);

    // 初始状态更新
    updateConnectionState();

    // 自动连接
    if (!wsService.isConnected()) {
      wsService.connect().catch(console.error);
    }

    return () => {
      unsubscribeConnect();
      unsubscribeDisconnect();
      unsubscribeError();
    };
  }, []);

  return {
    isConnected,
    connectionState,
    subscribe: wsService.subscribe.bind(wsService),
    send: wsService.send.bind(wsService),
    connect: wsService.connect.bind(wsService),
    disconnect: wsService.disconnect.bind(wsService),
  };
};


