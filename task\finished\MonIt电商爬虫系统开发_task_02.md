# Task 02: 数据模型与数据库设计

## 任务概述
设计适合电商爬虫系统的完整数据模型，基于TimescaleDB实现时间序列数据存储，支持高效的数据查询和分析。

## 核心需求分析

### 1. 数据类型分析
根据ToDo.txt中的需求，系统需要存储以下数据：

**基础信息：**
- 中文品名 (A列) 
- 原始西班牙语品名 (B列)
- 商品链接 (C列)

**爬取数据：**
- 任务开始时间戳 YYYYMMDDHHMMSS (A列)
- 商品首图 (D列)
- 商品当前售价 (E列) 
- 商品现价 (F列)
- 商品折扣率 (G列)
- 商品销售数量 (H列)
- 商品库存数量 (I列)
- 商品评分 (J列)
- 商品评分数量 (K列)
- 其他JSON配置中的字段

### 2. 数据特点
- **时间序列性**: 同一商品需要定期抓取，记录价格变化趋势
- **多样性**: 不同站点可能有不同的数据字段
- **高频率**: 需要支持大量商品的频繁数据更新
- **查询需求**: 需要支持时间范围查询、商品对比、趋势分析

### 3. 性能要求
- 支持大量商品的并发爬取
- 快速的时间序列查询
- 高效的数据插入性能
- 支持数据压缩和自动清理

## 数据库设计方案

### 1. 表结构设计

#### 主表设计
- `products`: 商品基础信息表
- `product_snapshots`: 商品快照时间序列表 (TimescaleDB hypertable)
- `crawl_tasks`: 爬取任务表
- `crawl_configs`: 爬取配置表
- `system_logs`: 系统日志表

#### 支持表设计
- `product_categories`: 商品分类表
- `scraping_rules`: 爬取规则表
- `proxy_pools`: 代理池表
- `user_agents`: UA池表

### 2. 索引策略
- 时间索引：用于快速时间范围查询
- 商品索引：用于快速商品查询
- 复合索引：支持多维度查询

### 3. 分区策略
- 基于时间的自动分区
- 数据压缩策略
- 历史数据清理策略

## 预期产出

1. **数据库模型定义**
   - SQLAlchemy ORM模型
   - Pydantic数据模型
   - 数据库迁移脚本

2. **数据库初始化**
   - 表结构创建脚本
   - 索引创建脚本
   - TimescaleDB特性配置

3. **数据访问层**
   - 基础CRUD操作
   - 时间序列查询函数
   - 数据聚合函数

4. **测试与验证**
   - 单元测试
   - 性能测试
   - 数据一致性测试

## 技术选型

- **ORM**: SQLAlchemy 2.0 (异步)
- **数据验证**: Pydantic v2
- **迁移工具**: Alembic
- **连接池**: asyncpg
- **时间序列**: TimescaleDB扩展

## 执行计划

1. **第1天**: 设计数据模型和表结构
2. **第2天**: 实现SQLAlchemy模型和Pydantic schema
3. **第3天**: 创建数据库初始化和迁移脚本
4. **第4天**: 实现数据访问层和测试

## 成功标准

- [ ] 数据模型完整设计并文档化
- [ ] 所有表结构正确创建
- [ ] TimescaleDB时间序列功能正常工作
- [ ] 基础CRUD操作全部实现
- [ ] 性能测试通过
- [ ] 单元测试覆盖率 > 80% 