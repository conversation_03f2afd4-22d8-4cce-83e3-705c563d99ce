# Redis存储结构重构实施计划

> **📅 计划时间**：2025年7月13日  
> **🎯 目标**：实施分层存储和完整索引系统  
> **⏱️ 预计时间**：2-3小时  
> **🔄 停机时间**：<5分钟  

## 🎯 重构目标

### 从当前结构迁移到优化结构

#### 当前结构 (扁平化)
```redis
monitoring_tasks:{task_id} = Hash{19个混合字段}
monitoring_tasks:all_ids = Set[task_ids]
monitoring_tasks:active_tasks = Set[active_task_ids]
monitoring_tasks:task_urls:{task_id} = Set[url_ids]
```

#### 目标结构 (分层化)
```redis
monitoring_tasks/
├── tasks/{task_id}/
│   ├── basic                     # 基本信息 (Hash)
│   ├── config                    # 任务配置 (Hash)
│   ├── schedule                  # 调度配置 (Hash)
│   ├── stats                     # 统计信息 (Hash)
│   └── urls                      # 关联的URL ID列表 (Set)
├── indexes/
│   ├── all_task_ids              # 所有任务ID (Set)
│   ├── active_tasks              # 活跃任务ID (Set)
│   ├── by_platform/
│   │   └── mercadolibre         # 按平台分组的任务ID (Set)
│   └── by_status/
│       ├── active               # 按状态分组的任务ID (Set)
│       ├── paused               # 按状态分组的任务ID (Set)
│       └── completed            # 按状态分组的任务ID (Set)
└── url_tasks/{url_id}           # URL反向关联 (Set)
```

## 📊 数据映射方案

### 字段分类映射

#### 1. Basic Info (5字段)
```redis
monitoring_tasks:tasks:{task_id}:basic = Hash{
  "id": "fe2ae36c-cffa-460e-942a-4c95a78122fe",
  "name": "00350",
  "description": "",
  "created_at": "2025-07-07T16:34:52.141484+00:00",
  "updated_at": "2025-07-12T01:16:05.851430+08:00"
}
```

#### 2. Config Info (1字段 + 解析)
```redis
monitoring_tasks:tasks:{task_id}:config = Hash{
  "platform": "mercadolibre",
  "priority": "medium", 
  "retry_count": "3",
  "timeout": "300",
  "concurrent_limit": "2",
  "rate_limit": "{...}",
  "batch_size": "50"
}
```

#### 3. Schedule Info (3字段 + 解析)
```redis
monitoring_tasks:tasks:{task_id}:schedule = Hash{
  "type": "daily",
  "enabled": "true",
  "time": "01:13",
  "timezone": "Asia/Shanghai",
  "next_run": "2025-07-07T16:48:00+00:00",
  "last_run": ""
}
```

#### 4. Stats Info (10字段)
```redis
monitoring_tasks:tasks:{task_id}:stats = Hash{
  "status": "active",
  "total_runs": "0",
  "success_runs": "0", 
  "failed_runs": "0",
  "success_rate": "0.0",
  "avg_duration": "0.0",
  "is_running": "false",
  "last_success_time": "",
  "url_count": "625"
}
```

#### 5. URLs (现有结构保持)
```redis
monitoring_tasks:tasks:{task_id}:urls = Set[url_id1, url_id2, ...]
```

### 索引系统建立

#### 1. 基础索引
```redis
monitoring_tasks:indexes:all_task_ids = Set[task_id1, ...]
monitoring_tasks:indexes:active_tasks = Set[active_task_id1, ...]
```

#### 2. 平台索引
```redis
monitoring_tasks:indexes:by_platform:mercadolibre = Set[task_id1, ...]
monitoring_tasks:indexes:by_platform:amazon = Set[...]
```

#### 3. 状态索引
```redis
monitoring_tasks:indexes:by_status:active = Set[task_id1, ...]
monitoring_tasks:indexes:by_status:paused = Set[...]
monitoring_tasks:indexes:by_status:completed = Set[...]
```

#### 4. URL反向关联
```redis
monitoring_tasks:url_tasks:{url_id} = Set[task_id1, task_id2, ...]
```

## 🔧 实施步骤

### 阶段1: 准备工作 (30分钟)

#### 1.1 创建迁移工具
- [ ] 数据备份脚本
- [ ] 结构转换脚本  
- [ ] 数据验证脚本
- [ ] 回滚脚本

#### 1.2 更新TaskStorageService
- [ ] 新增分层存储方法
- [ ] 新增索引管理方法
- [ ] 保持向后兼容接口

#### 1.3 创建测试用例
- [ ] 数据完整性测试
- [ ] 性能对比测试
- [ ] API兼容性测试

### 阶段2: 数据迁移 (30分钟)

#### 2.1 备份现有数据
```bash
# 导出当前数据
redis-cli --rdb backup_before_migration.rdb
```

#### 2.2 执行结构转换
- [ ] 读取现有任务数据
- [ ] 按新结构写入分层数据
- [ ] 建立索引系统
- [ ] 创建URL反向关联

#### 2.3 数据验证
- [ ] 验证数据完整性
- [ ] 验证索引正确性
- [ ] 验证关联关系

### 阶段3: 服务更新 (60分钟)

#### 3.1 更新TaskStorageService实现
- [ ] 实现分层读写方法
- [ ] 实现索引查询方法
- [ ] 实现URL反向查询
- [ ] 保持API接口不变

#### 3.2 性能优化
- [ ] 批量操作优化
- [ ] 索引更新优化
- [ ] 连接池配置

#### 3.3 测试验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能测试

### 阶段4: 清理和文档 (30分钟)

#### 4.1 清理旧数据
- [ ] 删除旧的扁平化数据
- [ ] 清理无用的键
- [ ] 优化内存使用

#### 4.2 更新文档
- [ ] 更新API文档
- [ ] 更新架构文档
- [ ] 创建迁移报告

## ⚠️ 风险控制

### 风险识别
1. **数据丢失风险**: 迁移过程中数据损坏
2. **服务中断风险**: 迁移期间API不可用
3. **性能回退风险**: 新结构性能不如预期
4. **兼容性风险**: 前端或其他服务受影响

### 风险缓解
1. **完整备份**: 迁移前完整备份Redis数据
2. **分步迁移**: 先迁移数据，再更新服务
3. **双写验证**: 新旧结构并行验证
4. **快速回滚**: 准备完整的回滚方案

### 回滚方案
1. **数据回滚**: 从备份恢复原始数据
2. **代码回滚**: 切换回原始TaskStorageService
3. **验证回滚**: 确保系统恢复正常

## 📊 成功标准

### 功能标准
- [ ] 所有API功能正常工作
- [ ] 数据完整性100%保持
- [ ] 新增查询功能正常
- [ ] URL反向查询正常

### 性能标准
- [ ] 查询性能提升≥30%
- [ ] 索引查询<10ms
- [ ] 批量操作性能保持
- [ ] 内存使用优化

### 质量标准
- [ ] 0数据丢失
- [ ] 0功能回退
- [ ] 100%向后兼容
- [ ] 完整的测试覆盖

## 🚀 实施时间表

### 今天 (2025-07-13)
- **14:00-14:30**: 创建迁移工具和脚本
- **14:30-15:00**: 更新TaskStorageService基础结构
- **15:00-15:30**: 执行数据迁移和验证
- **15:30-16:30**: 更新服务实现和测试
- **16:30-17:00**: 清理、文档和验证

### 验收标准
- [ ] 健康检查100%通过
- [ ] 性能测试达标
- [ ] API兼容性验证通过
- [ ] 文档更新完成

## 💡 后续优化

### 短期优化 (1周内)
- [ ] 查询性能监控
- [ ] 索引使用统计
- [ ] 内存使用优化

### 中期优化 (1月内)  
- [ ] 高级查询功能
- [ ] 缓存策略优化
- [ ] 分片策略规划

### 长期优化 (3月内)
- [ ] 数据压缩
- [ ] 分布式存储
- [ ] 实时统计系统

---

**🎯 总结**: 这是一个低风险、高收益的重构项目。当前数据量小，结构简单，是实施的最佳时机。预计能够显著提升系统的查询性能和扩展性。
