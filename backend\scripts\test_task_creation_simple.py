#!/usr/bin/env python3
"""
简单的任务创建集成测试

跳过URL验证，直接测试任务创建和显示的集成
"""

import asyncio
import sys
import os
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_task_creation_simple():
    """简单的任务创建集成测试"""
    base_url = "http://localhost:8000"
    
    print("🔍 简单的任务创建集成测试")
    print("=" * 50)
    
    task_id = None
    
    try:
        # 步骤1: 获取创建前的任务列表
        print("\n📋 步骤1: 获取创建前的任务列表")
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response.status_code}")
        
        if list_response.status_code == 200:
            before_data = list_response.json()
            before_count = before_data.get('total', 0)
            print(f"  创建前任务数量: {before_count}")
        else:
            print(f"  ❌ 获取任务列表失败: {list_response.text}")
            return
        
        # 步骤2: 跳过验证，直接创建任务（修改验证逻辑）
        print(f"\n📋 步骤2: 创建任务（跳过URL验证）")
        create_data = {
            "name": "集成测试任务",
            "description": "用于测试任务创建集成的任务",
            "url_ids": ["fake_url_1", "fake_url_2"],  # 使用假的URL ID
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "10:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        print(f"  发送创建请求...")
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        print(f"  状态码: {create_response.status_code}")
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            print(f"  ✅ 任务创建成功")
            task_id = create_result.get('task_id')
            print(f"  任务ID: {task_id}")
            print(f"  任务名称: {create_result.get('task_name')}")
            print(f"  URL数量: {create_result.get('url_count')}")
        elif create_response.status_code == 400:
            error_text = create_response.text
            if "无效URL" in error_text:
                print(f"  ⚠️ 任务创建失败（URL验证失败）: {error_text}")
                print(f"  这是预期的，因为我们使用了假的URL ID")
                return
            else:
                print(f"  ❌ 任务创建失败: {error_text}")
                return
        else:
            print(f"  ❌ 任务创建失败")
            print(f"  错误: {create_response.text}")
            return
        
        # 步骤3: 获取创建后的任务列表
        print(f"\n📋 步骤3: 获取创建后的任务列表")
        list_response2 = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response2.status_code}")
        
        if list_response2.status_code == 200:
            after_data = list_response2.json()
            after_count = after_data.get('total', 0)
            print(f"  创建后任务数量: {after_count}")
            
            if after_count > before_count:
                print(f"  ✅ 任务数量增加了 {after_count - before_count} 个")
                
                # 检查新创建的任务是否在列表中
                tasks = after_data.get('data', [])
                found_task = None
                for task in tasks:
                    if task.get('id') == task_id:
                        found_task = task
                        break
                
                if found_task:
                    print(f"  ✅ 新创建的任务在列表中找到")
                    print(f"  任务名称: {found_task.get('name')}")
                    print(f"  任务状态: {found_task.get('status')}")
                    print(f"  URL数量: {found_task.get('total_urls', 0)}")
                else:
                    print(f"  ❌ 新创建的任务在列表中未找到")
                    print(f"  任务列表中的任务ID: {[t.get('id') for t in tasks]}")
                    print(f"  查找的任务ID: {task_id}")
            else:
                print(f"  ❌ 任务数量没有增加")
        else:
            print(f"  ❌ 获取任务列表失败: {list_response2.text}")
        
        # 步骤4: 直接获取新创建的任务详情
        if task_id:
            print(f"\n📋 步骤4: 获取新创建任务的详情")
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            print(f"  状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 任务详情获取成功")
                task_detail = detail_data.get('data', {})
                print(f"  任务名称: {task_detail.get('name')}")
                print(f"  任务状态: {task_detail.get('status')}")
                print(f"  URL数量: {task_detail.get('total_urls', 0)}")
                print(f"  创建时间: {task_detail.get('created_at')}")
            else:
                print(f"  ❌ 任务详情获取失败: {detail_response.text}")
        
        # 步骤5: 检查Redis存储
        print(f"\n📋 步骤5: 检查Redis存储结构")
        if task_id:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            
            # 检查新的分层存储
            basic_key = f"monitoring_tasks:tasks:{task_id}:basic"
            schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
            config_key = f"monitoring_tasks:tasks:{task_id}:config"
            urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            
            print(f"  检查分层存储:")
            print(f"    basic存在: {r.exists(basic_key)}")
            print(f"    schedule存在: {r.exists(schedule_key)}")
            print(f"    config存在: {r.exists(config_key)}")
            print(f"    urls存在: {r.exists(urls_key)}")
            
            # 检查索引
            all_ids_key = "monitoring_tasks:indexes:all_task_ids"
            active_ids_key = "monitoring_tasks:indexes:active_task_ids"
            
            print(f"  检查索引:")
            print(f"    all_task_ids包含任务: {r.sismember(all_ids_key, task_id)}")
            print(f"    active_task_ids包含任务: {r.sismember(active_ids_key, task_id)}")
        
        print(f"\n🎉 集成测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        if task_id:
            print(f"\n📋 清理测试数据")
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id}")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {delete_response.text}")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {e}")


if __name__ == "__main__":
    test_task_creation_simple()
