import { useState, useEffect, useCallback } from 'react';

export const useAutoRefresh = (defaultInterval: number = 30000) => {
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(defaultInterval);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const updateLastRefresh = useCallback(() => {
    setLastRefresh(new Date());
  }, []);

  // 自动刷新逻辑
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // 触发全局刷新事件
      window.dispatchEvent(new CustomEvent('celery-auto-refresh'));
      updateLastRefresh();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, updateLastRefresh]);

  return {
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval,
    lastRefresh,
    updateLastRefresh
  };
};
