#!/bin/bash

# MonIt监控系统启动脚本
echo "🚀 启动MonIt监控系统..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs/backend logs/celery logs/crawler logs/frontend

# 创建网络（如果不存在）
echo "🌐 创建Docker网络..."
docker network create monit-network 2>/dev/null || true

# 启动监控服务
echo "📊 启动监控服务..."
docker-compose -f docker-compose.monitoring.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.monitoring.yml ps

# 显示访问地址
echo ""
echo "✅ 监控系统启动完成！"
echo ""
echo "📊 监控面板访问地址："
echo "  - Grafana:      http://localhost:3001 (admin/admin123)"
echo "  - Prometheus:   http://localhost:9090"
echo "  - <PERSON>bana:       http://localhost:5601"
echo "  - Elasticsearch: http://localhost:9200"
echo ""
echo "📝 日志目录: ./logs/"
echo "⚙️  配置目录: ./monitoring/"
echo ""
echo "🔧 使用以下命令管理监控系统："
echo "  启动: docker-compose -f docker-compose.monitoring.yml up -d"
echo "  停止: docker-compose -f docker-compose.monitoring.yml down"
echo "  查看日志: docker-compose -f docker-compose.monitoring.yml logs -f [service_name]"
echo ""
