"""
告警管理器

处理告警规则、通知发送和告警历史管理
"""

import asyncio
import json
import logging
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """告警严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    component: str
    metric: str
    operator: str  # gt, lt, eq, gte, lte
    threshold: float
    severity: AlertSeverity
    enabled: bool = True
    description: Optional[str] = None
    cooldown_minutes: int = 5  # 冷却时间，避免重复告警


@dataclass
class Alert:
    """告警实例"""
    id: str
    rule_name: str
    component: str
    metric: str
    current_value: float
    threshold: float
    severity: AlertSeverity
    status: AlertStatus
    message: str
    created_at: datetime
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class NotificationChannel:
    """通知渠道基类"""
    
    async def send_notification(self, alert: Alert) -> bool:
        """发送通知"""
        raise NotImplementedError


class EmailNotificationChannel(NotificationChannel):
    """邮件通知渠道"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str, 
                 from_email: str, to_emails: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
    
    async def send_notification(self, alert: Alert) -> bool:
        """发送邮件通知"""
        try:
            # 创建邮件内容
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{alert.severity.value.upper()}] MonIt Alert: {alert.rule_name}"
            
            # 邮件正文
            body = f"""
MonIt系统告警通知

告警信息:
- 规则名称: {alert.rule_name}
- 组件: {alert.component}
- 指标: {alert.metric}
- 当前值: {alert.current_value}
- 阈值: {alert.threshold}
- 严重程度: {alert.severity.value}
- 消息: {alert.message}
- 时间: {alert.created_at.strftime('%Y-%m-%d %H:%M:%S')}

告警ID: {alert.id}

请及时处理此告警。
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"Email notification sent for alert {alert.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification for alert {alert.id}: {e}")
            return False


class WebhookNotificationChannel(NotificationChannel):
    """Webhook通知渠道"""
    
    def __init__(self, webhook_url: str, headers: Optional[Dict[str, str]] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {}
    
    async def send_notification(self, alert: Alert) -> bool:
        """发送Webhook通知"""
        try:
            # 准备数据
            payload = {
                "alert_id": alert.id,
                "rule_name": alert.rule_name,
                "component": alert.component,
                "metric": alert.metric,
                "current_value": alert.current_value,
                "threshold": alert.threshold,
                "severity": alert.severity.value,
                "status": alert.status.value,
                "message": alert.message,
                "created_at": alert.created_at.isoformat(),
                "metadata": alert.metadata
            }
            
            # 发送HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"Webhook notification sent for alert {alert.id}")
                        return True
                    else:
                        logger.error(f"Webhook notification failed for alert {alert.id}: HTTP {response.status}")
                        return False
            
        except Exception as e:
            logger.error(f"Failed to send webhook notification for alert {alert.id}: {e}")
            return False


class SlackNotificationChannel(NotificationChannel):
    """Slack通知渠道"""
    
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
    
    async def send_notification(self, alert: Alert) -> bool:
        """发送Slack通知"""
        try:
            # 根据严重程度选择颜色
            color_map = {
                AlertSeverity.INFO: "#36a64f",      # 绿色
                AlertSeverity.WARNING: "#ff9500",   # 橙色
                AlertSeverity.ERROR: "#ff0000",     # 红色
                AlertSeverity.CRITICAL: "#8b0000"   # 深红色
            }
            
            # 准备Slack消息
            payload = {
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, "#ff0000"),
                        "title": f"MonIt Alert: {alert.rule_name}",
                        "text": alert.message,
                        "fields": [
                            {
                                "title": "组件",
                                "value": alert.component,
                                "short": True
                            },
                            {
                                "title": "指标",
                                "value": alert.metric,
                                "short": True
                            },
                            {
                                "title": "当前值",
                                "value": str(alert.current_value),
                                "short": True
                            },
                            {
                                "title": "阈值",
                                "value": str(alert.threshold),
                                "short": True
                            },
                            {
                                "title": "严重程度",
                                "value": alert.severity.value.upper(),
                                "short": True
                            },
                            {
                                "title": "时间",
                                "value": alert.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                                "short": True
                            }
                        ],
                        "footer": f"Alert ID: {alert.id}",
                        "ts": int(alert.created_at.timestamp())
                    }
                ]
            }
            
            # 发送到Slack
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"Slack notification sent for alert {alert.id}")
                        return True
                    else:
                        logger.error(f"Slack notification failed for alert {alert.id}: HTTP {response.status}")
                        return False
            
        except Exception as e:
            logger.error(f"Failed to send Slack notification for alert {alert.id}: {e}")
            return False


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.notification_channels: List[NotificationChannel] = []
        self.alert_cooldowns: Dict[str, datetime] = {}
        
        # 加载默认规则
        self._load_default_rules()
    
    def _load_default_rules(self):
        """加载默认告警规则"""
        default_rules = [
            AlertRule(
                name="redis_memory_fragmentation",
                component="redis",
                metric="mem_fragmentation_ratio",
                operator="gt",
                threshold=2.0,
                severity=AlertSeverity.WARNING,
                description="Redis内存碎片率过高"
            ),
            AlertRule(
                name="redis_connected_clients",
                component="redis",
                metric="connected_clients",
                operator="gt",
                threshold=1000,
                severity=AlertSeverity.WARNING,
                description="Redis连接数过多"
            ),
            AlertRule(
                name="taskmanager_pending_queue",
                component="taskmanager",
                metric="pending_count",
                operator="gt",
                threshold=100,
                severity=AlertSeverity.WARNING,
                description="TaskManager待处理队列积压"
            ),
            AlertRule(
                name="taskmanager_failed_queue",
                component="taskmanager",
                metric="failed_count",
                operator="gt",
                threshold=50,
                severity=AlertSeverity.ERROR,
                description="TaskManager失败任务过多"
            ),
            AlertRule(
                name="redis_memory_usage",
                component="redis",
                metric="used_memory_percentage",
                operator="gt",
                threshold=90.0,
                severity=AlertSeverity.CRITICAL,
                description="Redis内存使用率过高"
            )
        ]
        
        for rule in default_rules:
            self.rules[rule.name] = rule
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
    
    def add_notification_channel(self, channel: NotificationChannel):
        """添加通知渠道"""
        self.notification_channels.append(channel)
        logger.info(f"Added notification channel: {type(channel).__name__}")
    
    def check_metrics(self, redis_metrics: Dict[str, Any], tm_metrics: Dict[str, Any]) -> List[Alert]:
        """检查指标并生成告警"""
        new_alerts = []
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            if rule_name in self.alert_cooldowns:
                cooldown_end = self.alert_cooldowns[rule_name] + timedelta(minutes=rule.cooldown_minutes)
                if datetime.now() < cooldown_end:
                    continue
            
            try:
                # 获取指标值
                value = self._get_metric_value(rule, redis_metrics, tm_metrics)
                if value is None:
                    continue
                
                # 检查阈值
                if self._check_threshold(value, rule.operator, rule.threshold):
                    # 生成告警
                    alert_id = f"{rule_name}_{int(datetime.now().timestamp())}"
                    
                    alert = Alert(
                        id=alert_id,
                        rule_name=rule_name,
                        component=rule.component,
                        metric=rule.metric,
                        current_value=value,
                        threshold=rule.threshold,
                        severity=rule.severity,
                        status=AlertStatus.ACTIVE,
                        message=rule.description or f"{rule.metric} threshold exceeded",
                        created_at=datetime.now(),
                        metadata={
                            "operator": rule.operator,
                            "redis_metrics": redis_metrics if rule.component == "redis" else None,
                            "taskmanager_metrics": tm_metrics if rule.component == "taskmanager" else None
                        }
                    )
                    
                    new_alerts.append(alert)
                    self.active_alerts[alert_id] = alert
                    self.alert_history.append(alert)
                    self.alert_cooldowns[rule_name] = datetime.now()
                    
                    logger.warning(f"Alert triggered: {rule_name} - {alert.message}")
            
            except Exception as e:
                logger.error(f"Error checking rule {rule_name}: {e}")
        
        return new_alerts
    
    def _get_metric_value(self, rule: AlertRule, redis_metrics: Dict[str, Any], tm_metrics: Dict[str, Any]) -> Optional[float]:
        """获取指标值"""
        try:
            if rule.component == "redis" and redis_metrics:
                if rule.metric == "mem_fragmentation_ratio":
                    return redis_metrics.get("memory", {}).get("mem_fragmentation_ratio", 0)
                elif rule.metric == "connected_clients":
                    return redis_metrics.get("server", {}).get("connected_clients", 0)
                elif rule.metric == "used_memory_percentage":
                    used = redis_metrics.get("memory", {}).get("used_memory", 0)
                    max_mem = redis_metrics.get("memory", {}).get("maxmemory", 0)
                    if max_mem > 0:
                        return (used / max_mem) * 100
                    return 0
            
            elif rule.component == "taskmanager" and tm_metrics:
                if rule.metric == "pending_count":
                    return tm_metrics.get("queues", {}).get("pending", {}).get("count", 0)
                elif rule.metric == "failed_count":
                    return tm_metrics.get("queues", {}).get("failed", {}).get("count", 0)
                elif rule.metric == "total_items":
                    return tm_metrics.get("total_items", 0)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting metric value for {rule.metric}: {e}")
            return None
    
    def _check_threshold(self, value: float, operator: str, threshold: float) -> bool:
        """检查阈值"""
        if operator == "gt":
            return value > threshold
        elif operator == "lt":
            return value < threshold
        elif operator == "gte":
            return value >= threshold
        elif operator == "lte":
            return value <= threshold
        elif operator == "eq":
            return value == threshold
        else:
            return False
    
    async def send_notifications(self, alerts: List[Alert]):
        """发送告警通知"""
        for alert in alerts:
            for channel in self.notification_channels:
                try:
                    await channel.send_notification(alert)
                except Exception as e:
                    logger.error(f"Failed to send notification via {type(channel).__name__}: {e}")
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.now()
            logger.info(f"Alert acknowledged: {alert_id}")
            return True
        return False
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.now()
            del self.active_alerts[alert_id]
            logger.info(f"Alert resolved: {alert_id}")
            return True
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert.created_at >= cutoff_time]
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计"""
        active_alerts = self.get_active_alerts()
        recent_alerts = self.get_alert_history(24)
        
        return {
            "active_alerts_count": len(active_alerts),
            "recent_alerts_count": len(recent_alerts),
            "severity_breakdown": {
                "critical": len([a for a in active_alerts if a.severity == AlertSeverity.CRITICAL]),
                "error": len([a for a in active_alerts if a.severity == AlertSeverity.ERROR]),
                "warning": len([a for a in active_alerts if a.severity == AlertSeverity.WARNING]),
                "info": len([a for a in active_alerts if a.severity == AlertSeverity.INFO])
            },
            "component_breakdown": {
                "redis": len([a for a in active_alerts if a.component == "redis"]),
                "taskmanager": len([a for a in active_alerts if a.component == "taskmanager"]),
                "system": len([a for a in active_alerts if a.component == "system"])
            }
        }
