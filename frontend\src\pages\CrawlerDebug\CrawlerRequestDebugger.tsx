import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Tabs,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Badge,
  Tooltip,
  Descriptions,
  Tag,
  Collapse,
  Select,
  DatePicker,
  Table,
  Modal,
  message
} from 'antd';
import {
  BugOutlined,
  SendOutlined,
  EyeOutlined,
  CopyOutlined,
  DownloadOutlined,
  FilterOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
// 简化版本，使用pre标签替代语法高亮

const { Title, Text } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface CrawlerRequest {
  id: string;
  taskId: string;
  url: string;
  workerId: string;
  timestamp: string;
  method: string;
  headers: Record<string, string>;
  payload: any;
  response?: {
    status: number;
    headers: Record<string, string>;
    data: any;
    responseTime: number;
  };
  error?: string;
  status: 'pending' | 'sent' | 'success' | 'failed';
}

interface CrawlerWorkerInfo {
  workerId: string;
  version: string;
  endpoint: string;
  status: 'online' | 'offline' | 'busy';
  capabilities: string[];
  lastSeen: string;
}

const CrawlerRequestDebugger: React.FC = () => {
  const [activeTab, setActiveTab] = useState('live');
  const [requests, setRequests] = useState<CrawlerRequest[]>([]);
  const [workers, setWorkers] = useState<CrawlerWorkerInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<CrawlerRequest | null>(null);
  const [isLiveMode, setIsLiveMode] = useState(false);
  const [filters, setFilters] = useState({
    workerId: '',
    status: '',
    timeRange: null as any
  });



  // 获取爬虫Worker信息
  const fetchWorkers = async () => {
    try {
      const response = await fetch('/api/v1/crawler-workers');
      if (response.ok) {
        const data = await response.json();
        setWorkers(data);
      } else {
        setWorkers([]);
      }
    } catch (error) {
      console.error('Failed to fetch workers:', error);
      setWorkers([]);
    }
  };



  // 获取请求历史
  const fetchRequestHistory = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams();

      if (filters.workerId) params.append('worker_id', filters.workerId);
      if (filters.status) params.append('status', filters.status);
      if (filters.timeRange) {
        params.append('start_time', filters.timeRange[0].toISOString());
        params.append('end_time', filters.timeRange[1].toISOString());
      }

      const response = await fetch(`/api/v1/crawler-debug/requests?${params}`);
      if (response.ok) {
        const data = await response.json();
        setRequests(data);
      } else {
        setRequests([]);
      }
    } catch (error) {
      console.error('Failed to fetch request history:', error);
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  // 实时监听功能
  const startLiveMode = () => {
    setIsLiveMode(true);
    // TODO: 集成真实WebSocket监听
    message.success('实时监听已开启');

    // 设置定时刷新，每5秒获取最新数据
    const interval = setInterval(() => {
      if (!isLiveMode) {
        clearInterval(interval);
        return;
      }
      fetchRequestHistory();
    }, 5000);

    // 保存interval引用以便清理
    (window as any).liveInterval = interval;
  };

  const stopLiveMode = () => {
    setIsLiveMode(false);
    message.info('实时监听已停止');

    // 清理定时器
    if ((window as any).liveInterval) {
      clearInterval((window as any).liveInterval);
      (window as any).liveInterval = null;
    }
  };

  // 复制请求信息
  const copyRequest = (request: CrawlerRequest) => {
    const requestInfo = {
      url: request.url,
      method: request.method,
      headers: request.headers,
      payload: request.payload
    };
    
    navigator.clipboard.writeText(JSON.stringify(requestInfo, null, 2));
    message.success('请求信息已复制到剪贴板');
  };

  // 导出请求数据
  const exportRequests = () => {
    const dataStr = JSON.stringify(requests, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `crawler-requests-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // 重发请求
  const resendRequest = async (request: CrawlerRequest) => {
    try {
      const response = await fetch('/api/v1/crawler-debug/resend', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: request.url,
          method: request.method,
          headers: request.headers,
          payload: request.payload
        })
      });

      if (response.ok) {
        message.success('请求已重新发送');
        fetchRequestHistory();
      } else {
        message.error('重发请求失败');
      }
    } catch (error) {
      message.error('重发请求时发生错误');
    }
  };

  useEffect(() => {
    fetchWorkers();
    fetchRequestHistory();
  }, []);

  useEffect(() => {
    fetchRequestHistory();
  }, [filters]);

  // 请求列表表格列定义
  const requestColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: string) => new Date(timestamp).toLocaleString()
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code>{url.length > 50 ? `${url.substring(0, 50)}...` : url}</Text>
        </Tooltip>
      )
    },
    {
      title: 'Worker',
      dataIndex: 'workerId',
      key: 'workerId',
      width: 120,
      render: (workerId: string) => <Tag color="blue">{workerId}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const colors = {
          pending: 'orange',
          sent: 'blue',
          success: 'green',
          failed: 'red'
        };
        return <Badge color={colors[status as keyof typeof colors]} text={status} />;
      }
    },
    {
      title: '响应时间',
      dataIndex: ['response', 'responseTime'],
      key: 'responseTime',
      width: 100,
      render: (time: number) => time ? `${time}ms` : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: any, record: CrawlerRequest) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => setSelectedRequest(record)}
          >
            查看
          </Button>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyRequest(record)}
          />
          <Button
            size="small"
            icon={<SendOutlined />}
            onClick={() => resendRequest(record)}
          />
        </Space>
      )
    }
  ];

  // 实时监听面板
  const LiveMonitorPanel = () => (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Space>
            <Title level={4} style={{ margin: 0 }}>实时请求监听</Title>
            <Badge 
              status={isLiveMode ? 'processing' : 'default'} 
              text={isLiveMode ? '监听中' : '已停止'} 
            />
          </Space>
        </Col>
        <Col>
          <Space>
            {!isLiveMode ? (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={startLiveMode}
              >
                开始监听
              </Button>
            ) : (
              <Button
                icon={<StopOutlined />}
                onClick={stopLiveMode}
              >
                停止监听
              </Button>
            )}
            <Button icon={<ReloadOutlined />} onClick={fetchRequestHistory}>
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {isLiveMode && (
        <Alert
          message="实时监听已开启"
          description="正在监听所有发送到爬虫Worker的请求，新请求将自动显示在下方列表中。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Table
        columns={requestColumns}
        dataSource={requests}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
        scroll={{ x: 1000 }}
      />
    </div>
  );

  // Worker状态面板
  const WorkerStatusPanel = () => (
    <div>
      <Row gutter={16}>
        {workers.map(worker => (
          <Col span={8} key={worker.workerId} style={{ marginBottom: 16 }}>
            <Card
              title={
                <Space>
                  <BugOutlined />
                  <Text strong>{worker.workerId}</Text>
                  <Badge 
                    status={worker.status === 'online' ? 'success' : 'error'} 
                    text={worker.status} 
                  />
                </Space>
              }
              size="small"
            >
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="版本">{worker.version}</Descriptions.Item>
                <Descriptions.Item label="端点">{worker.endpoint}</Descriptions.Item>
                <Descriptions.Item label="最后活跃">{new Date(worker.lastSeen).toLocaleString()}</Descriptions.Item>
              </Descriptions>
              
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">支持功能:</Text>
                <div style={{ marginTop: 4 }}>
                  {worker.capabilities.map(cap => (
                    <Tag key={cap}>{cap}</Tag>
                  ))}
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  // 请求详情模态框
  const RequestDetailModal = () => (
    <Modal
      title="请求详情"
      open={!!selectedRequest}
      onCancel={() => setSelectedRequest(null)}
      footer={[
        <Button key="copy" icon={<CopyOutlined />} onClick={() => selectedRequest && copyRequest(selectedRequest)}>
          复制
        </Button>,
        <Button key="resend" type="primary" icon={<SendOutlined />} onClick={() => selectedRequest && resendRequest(selectedRequest)}>
          重发
        </Button>,
        <Button key="close" onClick={() => setSelectedRequest(null)}>
          关闭
        </Button>
      ]}
      width={800}
    >
      {selectedRequest && (
        <Collapse defaultActiveKey={['request']}>
          <Panel header="请求信息" key="request">
            <Descriptions column={2} size="small">
              <Descriptions.Item label="URL">{selectedRequest.url}</Descriptions.Item>
              <Descriptions.Item label="方法">{selectedRequest.method}</Descriptions.Item>
              <Descriptions.Item label="Worker">{selectedRequest.workerId}</Descriptions.Item>
              <Descriptions.Item label="时间">{new Date(selectedRequest.timestamp).toLocaleString()}</Descriptions.Item>
            </Descriptions>
            
            <div style={{ marginTop: 16 }}>
              <Text strong>请求头:</Text>
              <pre style={{
                background: '#f5f5f5',
                padding: 12,
                borderRadius: 4,
                marginTop: 8,
                maxHeight: 200,
                overflow: 'auto',
                fontSize: '12px'
              }}>
                {JSON.stringify(selectedRequest.headers, null, 2)}
              </pre>
            </div>

            <div style={{ marginTop: 16 }}>
              <Text strong>请求体:</Text>
              <pre style={{
                background: '#f5f5f5',
                padding: 12,
                borderRadius: 4,
                marginTop: 8,
                maxHeight: 200,
                overflow: 'auto',
                fontSize: '12px'
              }}>
                {JSON.stringify(selectedRequest.payload, null, 2)}
              </pre>
            </div>
          </Panel>
          
          {selectedRequest.response && (
            <Panel header="响应信息" key="response">
              <Descriptions column={2} size="small">
                <Descriptions.Item label="状态码">{selectedRequest.response.status}</Descriptions.Item>
                <Descriptions.Item label="响应时间">{selectedRequest.response.responseTime}ms</Descriptions.Item>
              </Descriptions>
              
              <div style={{ marginTop: 16 }}>
                <Text strong>响应头:</Text>
                <pre style={{
                  background: '#f5f5f5',
                  padding: 12,
                  borderRadius: 4,
                  marginTop: 8,
                  maxHeight: 200,
                  overflow: 'auto',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedRequest.response.headers, null, 2)}
                </pre>
              </div>

              <div style={{ marginTop: 16 }}>
                <Text strong>响应数据:</Text>
                <pre style={{
                  background: '#f5f5f5',
                  padding: 12,
                  borderRadius: 4,
                  marginTop: 8,
                  maxHeight: 200,
                  overflow: 'auto',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedRequest.response.data, null, 2)}
                </pre>
              </div>
            </Panel>
          )}
          
          {selectedRequest.error && (
            <Panel header="错误信息" key="error">
              <Alert
                message="请求失败"
                description={selectedRequest.error}
                type="error"
                showIcon
              />
            </Panel>
          )}
        </Collapse>
      )}
    </Modal>
  );

  return (
    <div style={{ padding: 24, background: '#f5f5f5', minHeight: '100vh' }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            <BugOutlined /> 爬虫请求调试器
          </Title>
          <Text type="secondary">
            实时监控和调试发送到爬虫Worker的请求
          </Text>
        </Col>
        <Col>
          <Space>
            <Button icon={<DownloadOutlined />} onClick={exportRequests}>
              导出数据
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 过滤器 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Text strong>过滤条件:</Text>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择Worker"
              value={filters.workerId}
              onChange={(value) => setFilters(prev => ({ ...prev, workerId: value }))}
              style={{ width: '100%' }}
              allowClear
            >
              {workers.map(worker => (
                <Option key={worker.workerId} value={worker.workerId}>
                  {worker.workerId}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="请求状态"
              value={filters.status}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="pending">等待中</Option>
              <Option value="sent">已发送</Option>
              <Option value="success">成功</Option>
              <Option value="failed">失败</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              showTime
              value={filters.timeRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, timeRange: dates }))}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Button
              icon={<FilterOutlined />}
              onClick={fetchRequestHistory}
              loading={loading}
            >
              应用过滤
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'live',
              label: '实时监听',
              children: <LiveMonitorPanel />
            },
            {
              key: 'workers',
              label: 'Worker状态',
              children: <WorkerStatusPanel />
            }
          ]}
        />
      </Card>

      <RequestDetailModal />
    </div>
  );
};

export default CrawlerRequestDebugger;
