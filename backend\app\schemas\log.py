"""
系统日志数据模式定义
"""
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict

from .common import BaseSchema


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class LogCategory(str, Enum):
    """日志分类枚举"""
    SYSTEM = "system"
    CRAWLER = "crawler"
    API = "api"
    DATABASE = "database"
    PROXY = "proxy"
    TASK = "task"
    ERROR = "error"


class SystemLogResponse(BaseSchema):
    """系统日志响应数据模式"""
    
    id: int = Field(description="日志ID")
    level: LogLevel = Field(description="日志级别")
    category: LogCategory = Field(description="日志分类")
    message: str = Field(description="日志消息")
    
    # 详细信息
    details: Optional[Dict[str, Any]] = Field(description="日志详情")
    
    # 来源信息
    source: Optional[str] = Field(description="日志来源")
    function_name: Optional[str] = Field(description="函数名")
    line_number: Optional[int] = Field(description="行号")
    
    # 执行信息
    execution_time: Optional[float] = Field(description="执行时间（秒）")
    memory_usage: Optional[float] = Field(description="内存使用（MB）")
    
    # 错误信息
    error_type: Optional[str] = Field(description="错误类型")
    error_message: Optional[str] = Field(description="错误消息")
    stack_trace: Optional[str] = Field(description="堆栈跟踪")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class LogQuery(BaseModel):
    """日志查询条件"""
    
    level: Optional[LogLevel] = Field(None, description="日志级别")
    category: Optional[LogCategory] = Field(None, description="日志分类")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    keyword: Optional[str] = Field(None, description="关键词搜索", max_length=200)
    source: Optional[str] = Field(None, description="日志来源", max_length=100)
    
    # 分页参数
    page: int = Field(1, description="页码", ge=1)
    size: int = Field(20, description="每页大小", ge=1, le=100)
    
    # 排序参数
    sort_by: str = Field("logged_at", description="排序字段")
    sort_order: str = Field("desc", description="排序顺序 (asc/desc)")

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class LogStatistics(BaseModel):
    """日志统计信息"""
    
    total_logs: int = Field(description="总日志数")
    error_count: int = Field(description="错误日志数")
    warning_count: int = Field(description="警告日志数")
    info_count: int = Field(description="信息日志数")
    debug_count: int = Field(description="调试日志数")
    
    # 按分类统计
    category_stats: Dict[str, int] = Field(description="分类统计")
    
    # 时间段统计
    hourly_stats: Dict[str, int] = Field(description="小时统计")
    daily_stats: Dict[str, int] = Field(description="日统计")

    model_config = ConfigDict(from_attributes=True) 