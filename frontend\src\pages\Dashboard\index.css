.dashboard {
  padding: 24px;
}

/* 全局文本颜色优化 */
.dashboard .ant-typography {
  color: #333333;
}

.dashboard .ant-typography.ant-typography-secondary {
  color: #666666;
}

.dashboard-header {
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.dashboard-header h2 {
  margin-bottom: 8px;
  color: #1a1a1a;
  font-weight: 600;
}

.dashboard-header .ant-typography {
  color: #666666;
  font-size: 16px;
}

.stats-row {
  margin-bottom: 32px;
}

.stats-row .ant-card {
  position: relative;
  overflow: hidden;
}

.stats-row .ant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.stats-row .ant-col:nth-child(2) .ant-card::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.stats-row .ant-col:nth-child(3) .ant-card::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.stats-row .ant-col:nth-child(4) .ant-card::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.content-row {
  margin-bottom: 24px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
  transition: background-color 0.2s;
}

.status-item:hover {
  background: #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.services-status {
  margin-top: 16px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
}

.service-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.service-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
}

.service-indicator.running {
  background-color: #52c41a;
}

.service-indicator.stopped {
  background-color: #ff4d4f;
}

.metric-item {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metric-header .ant-typography {
  font-weight: 500;
  color: #333333;
}

.queue-stats {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    text-align: center;
  }
  
  .stats-row .ant-col {
    margin-bottom: 16px;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .queue-stats .ant-row {
    text-align: center;
  }
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  background: #ffffff;
  animation: fadeInUp 0.6s ease-out;
}

.ant-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
  border-color: #d9d9d9;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-row .ant-col:nth-child(1) .ant-card {
  animation-delay: 0.1s;
}

.stats-row .ant-col:nth-child(2) .ant-card {
  animation-delay: 0.2s;
}

.stats-row .ant-col:nth-child(3) .ant-card {
  animation-delay: 0.3s;
}

.stats-row .ant-col:nth-child(4) .ant-card {
  animation-delay: 0.4s;
}

.ant-statistic-title {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  font-weight: 500;
}

.ant-statistic-content {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
}

/* 进度条样式 */
.ant-progress-line {
  margin-bottom: 0;
}

.ant-progress-bg {
  border-radius: 6px;
  height: 8px !important;
}

.ant-progress-inner {
  background-color: #f5f5f5 !important;
  border-radius: 6px;
}

/* 暗色主题适配 */
.app-layout.dark .dashboard .ant-typography {
  color: #ffffff;
}

.app-layout.dark .dashboard .ant-typography.ant-typography-secondary {
  color: #d9d9d9;
}

.app-layout.dark .dashboard-header {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .dashboard-header h2 {
  color: #ffffff;
}

.app-layout.dark .dashboard-header .ant-typography {
  color: #d9d9d9;
}

.app-layout.dark .stats-row .ant-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .stats-row .ant-statistic-title {
  color: #d9d9d9 !important;
}

.app-layout.dark .stats-row .ant-statistic-content-value {
  color: #ffffff !important;
}

.app-layout.dark .status-item {
  border-bottom-color: #303030;
}

.app-layout.dark .service-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .service-item:hover {
  background: #303030;
}

.app-layout.dark .service-name {
  color: #ffffff;
}

.app-layout.dark .service-status {
  color: #d9d9d9;
}

.app-layout.dark .service-description {
  color: #8c8c8c;
}

.app-layout.dark .queue-stats {
  border-top-color: #303030;
}

.app-layout.dark .queue-stat-item {
  color: #d9d9d9;
}

.app-layout.dark .queue-stat-value {
  color: #ffffff;
}

.app-layout.dark .chart-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .chart-card .ant-card-head {
  border-bottom-color: #303030;
}

.app-layout.dark .chart-card .ant-card-head-title {
  color: #ffffff;
}

.app-layout.dark .recent-tasks-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .task-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .task-item:hover {
  background: #303030;
}

.app-layout.dark .task-title {
  color: #ffffff;
}

.app-layout.dark .task-description {
  color: #d9d9d9;
}

.app-layout.dark .task-meta {
  color: #8c8c8c;
}
