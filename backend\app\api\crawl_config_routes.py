"""
爬取配置管理API路由
"""

from fastapi import APIRouter, HTTPException, Query, Depends, Request
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import uuid

import redis
from ..schemas.common import ResponseStatus
from ..services.system_config_service import SystemConfigService

# 爬取配置数据模型
from pydantic import BaseModel, Field

router = APIRouter(prefix="/crawl-configs", tags=["爬取配置管理"])


# 系统配置端点（必须在 /{config_id} 路由之前定义）
@router.get("/system-config")
async def get_system_config():
    """获取系统级配置信息"""
    try:
        system_info = SystemConfigService.get_system_info()

        return {
            "status": "success",
            "data": system_info,
            "message": "系统配置获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")


# 默认的JSON Schema
DEFAULT_MERCADOLIBRE_SCHEMA = """{
  "type": "object",
  "description": "电商商品完整信息提取结构",
  "properties": {
    "task_info": {
      "type": "object",
      "properties": {
        "start_timestamp": {
          "type": "string",
          "pattern": "^[0-9]{14}$"
        }
      },
      "required": ["start_timestamp"]
    },
    "product_basic_info": {
      "type": "object",
      "properties": {
        "product_url": {"type": "string", "format": "uri"},
        "product_name": {"type": "string"},
        "mlm_id": {"type": "number"}
      },
      "required": ["product_url", "product_name", "mlm_id"]
    },
    "pricing_info": {
      "type": "object",
      "properties": {
        "sales_count": {"type": "number"},
        "current_price": {"type": "number"},
        "original_price": {"type": "number"},
        "discount_rate": {"type": "number"},
        "stock_quantity": {"type": "number"},
        "stocktype_IsFull": {"type": "number"}
      },
      "required": ["current_price"]
    },
    "category_info": {
      "type": "object",
      "properties": {
        "category_breadcrumb": {"type": "string"},
        "category_hierarchy": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "category_name": {"type": "string"},
              "category_url": {"type": "string", "format": "uri"},
              "level": {"type": "integer"}
            },
            "required": ["category_name", "level"]
          }
        }
      },
      "required": ["category_breadcrumb"]
    },
    "seller_info": {
      "type": "object",
      "properties": {
        "seller_name": {"type": "string"},
        "seller_url": {"type": "string", "format": "uri"}
      },
      "required": ["seller_name"]
    },
    "media_info": {
      "type": "object",
      "properties": {
        "main_image_url": {"type": "string", "format": "uri"}
      },
      "required": ["main_image_url"]
    },
    "rating_info": {
      "type": "object",
      "properties": {
        "rating_score": {"type": "number", "minimum": 0, "maximum": 5},
        "rating_count": {"type": "integer"}
      },
      "required": ["rating_score", "rating_count"]
    }
  },
  "required": ["product_basic_info", "category_info", "pricing_info", "media_info", "rating_info"]
}"""

class CrawlRequestConfig(BaseModel):
    """爬取请求配置（用户级配置，不包含系统级配置）"""
    q: str = Field("", description="查询参数")
    schema_str: str = Field("", description="数据提取模式")
    cache: bool = Field(False, description="缓存设置")
    priority: str = Field("medium", description="优先级")
    batch_name: str = Field("", description="批次名称")
    batch_description: str = Field("", description="批次描述")

    # 注意：callback_url、max_concurrent_tasks、batch_timeout 已移至系统配置

class AuthContextConfig(BaseModel):
    """认证上下文配置"""
    user_id: str = Field("monit_system", description="用户ID")
    role: str = Field("user", description="用户角色")
    permissions: List[str] = Field(default_factory=list, description="权限列表")
    auth_method: str = Field("api_key", description="认证方法")
    api_key_prefix: str = Field("", description="API密钥前缀")
    client_ip: str = Field("127.0.0.1", description="客户端IP")
    user_agent: str = Field("MonIt-Crawler/1.0", description="用户代理")

class CrawlConfigTemplate(BaseModel):
    """爬取配置模板"""
    id: str = Field(description="配置ID")
    name: str = Field(description="配置名称")
    description: str = Field("", description="配置描述")
    platform: str = Field(description="目标平台")
    config: CrawlRequestConfig = Field(description="爬取配置")
    auth_config: AuthContextConfig = Field(description="认证配置")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    is_active: bool = Field(True, description="是否启用")
    usage_count: int = Field(0, description="使用次数")
    tags: List[str] = Field(default_factory=list, description="标签")

class CrawlConfigFormData(BaseModel):
    """爬取配置表单数据（用户级配置）"""
    name: str = Field(description="配置名称")
    description: str = Field("", description="配置描述")
    platform: str = Field(description="目标平台")

    # 爬取参数
    llm_query: str = Field("", description="LLM查询指令")
    llm_schema: str = Field("", description="JSON Schema配置")
    cache_enabled: bool = Field(False, description="启用缓存")
    priority: str = Field("medium", description="优先级")

    # 认证配置
    auth_method: str = Field("api_key", description="认证方法")
    api_key_prefix: str = Field("", description="API密钥前缀")
    user_agent: str = Field("MonIt-Crawler/1.0", description="用户代理")

    # 其他设置
    tags: List[str] = Field(default_factory=list, description="标签")
    is_active: bool = Field(True, description="是否启用")

    # 注意：callback_url、max_concurrent_tasks、batch_timeout 已移至系统配置

class CrawlConfigService:
    """爬取配置服务"""

    def __init__(self):
        self.redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )
        self.config_key_prefix = "crawl_configs"
    
    async def create_config(self, form_data: CrawlConfigFormData) -> CrawlConfigTemplate:
        """创建爬取配置"""
        config_id = str(uuid.uuid4())
        now = datetime.now()
        
        # 构建配置对象
        config = CrawlConfigTemplate(
            id=config_id,
            name=form_data.name,
            description=form_data.description,
            platform=form_data.platform,
            config=CrawlRequestConfig(
                q=form_data.query,
                schema_str=form_data.schema_str,
                cache=form_data.cache,
                priority=form_data.priority,
                batch_name="",  # 运行时生成
                batch_description="",  # 运行时生成
                callback_url=form_data.callback_url,
                max_concurrent_tasks=form_data.max_concurrent_tasks,
                batch_timeout=form_data.batch_timeout
            ),
            auth_config=AuthContextConfig(
                auth_method=form_data.auth_method,
                api_key_prefix=form_data.api_key_prefix,
                user_agent=form_data.user_agent
            ),
            created_at=now,
            updated_at=now,
            is_active=form_data.is_active,
            usage_count=0,
            tags=form_data.tags
        )
        
        # 保存到Redis
        config_key = f"{self.config_key_prefix}:{config_id}"
        config_data = config.dict()
        
        # 转换datetime为字符串
        config_data["created_at"] = config_data["created_at"].isoformat()
        config_data["updated_at"] = config_data["updated_at"].isoformat()
        
        # 保存配置
        self.redis_client.hset(config_key, mapping={
            k: json.dumps(v, ensure_ascii=False) if isinstance(v, (dict, list)) else str(v)
            for k, v in config_data.items()
        })
        
        # 添加到索引
        self.redis_client.sadd(f"{self.config_key_prefix}:index", config_id)
        self.redis_client.sadd(f"{self.config_key_prefix}:by_platform:{form_data.platform}", config_id)
        
        return config
    
    async def get_config(self, config_id: str) -> Optional[CrawlConfigTemplate]:
        """获取爬取配置"""
        config_key = f"{self.config_key_prefix}:{config_id}"
        config_data = self.redis_client.hgetall(config_key)
        
        if not config_data:
            return None
        
        # 转换数据类型
        for key in ['config', 'auth_config', 'tags']:
            if config_data.get(key):
                config_data[key] = json.loads(config_data[key])
        
        for key in ['created_at', 'updated_at']:
            if config_data.get(key):
                config_data[key] = datetime.fromisoformat(config_data[key])
        
        for key in ['usage_count']:
            if config_data.get(key):
                config_data[key] = int(config_data[key])
        
        for key in ['is_active']:
            if config_data.get(key):
                config_data[key] = config_data[key].lower() == 'true'
        
        return CrawlConfigTemplate(**config_data)
    
    async def list_configs(
        self,
        platform: Optional[str] = None,
        is_active: Optional[bool] = None,
        tags: Optional[List[str]] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """列出爬取配置"""
        
        # 获取配置ID列表
        if platform:
            config_ids = self.redis_client.smembers(f"{self.config_key_prefix}:by_platform:{platform}")
        else:
            config_ids = self.redis_client.smembers(f"{self.config_key_prefix}:index")
        
        # 获取配置详情
        configs = []
        for config_id in config_ids:
            config = await self.get_config(config_id)
            if config:
                # 应用过滤条件
                if is_active is not None and config.is_active != is_active:
                    continue
                if tags and not any(tag in config.tags for tag in tags):
                    continue
                configs.append(config)
        
        # 排序
        configs.sort(key=lambda x: x.updated_at, reverse=True)
        
        # 分页
        total = len(configs)
        start = (page - 1) * page_size
        end = start + page_size
        configs = configs[start:end]
        
        return {
            "configs": configs,
            "total": total,
            "page": page,
            "page_size": page_size
        }

# 全局服务实例
config_service = CrawlConfigService()

@router.get("/debug")
async def debug_redis():
    """调试Redis连接"""
    try:
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        # 测试连接
        redis_client.ping()

        # 获取所有键
        all_keys = redis_client.keys("crawl_configs:*")

        # 获取索引
        index_members = redis_client.smembers("crawl_configs:index")

        return {
            "success": True,
            "data": {
                "all_keys": list(all_keys),
                "index_members": list(index_members),
                "connection": "ok"
            }
        }
    except Exception as e:
        return {"success": False, "message": f"调试失败: {str(e)}"}

@router.get("/")
async def list_crawl_configs():
    """获取爬取配置列表"""
    try:
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        # 获取所有配置ID
        config_ids = redis_client.smembers("crawl_configs:index")

        # 获取配置详情
        configs = []
        for config_id in config_ids:
            config_key = f"crawl_configs:{config_id}"
            config_data = redis_client.hgetall(config_key)
            if config_data:
                # 转换数据类型
                for key in ['config', 'auth_config', 'tags']:
                    if config_data.get(key):
                        try:
                            # 确保正确处理中文字符
                            json_str = config_data[key]
                            if isinstance(json_str, bytes):
                                json_str = json_str.decode('utf-8')
                            config_data[key] = json.loads(json_str)
                        except Exception as e:
                            print(f"JSON解析错误 {key}: {e}")
                            pass

                # 转换为前端期望的格式
                config_obj = config_data.get('config', {})
                transformed_config = {
                    "id": config_data.get("id"),
                    "name": config_data.get("name"),
                    "description": config_data.get("description", ""),
                    "config_type": "batch_task",  # 默认类型
                    "platform": config_data.get("platform"),
                    "llm_query": config_obj.get("q", "") if isinstance(config_obj, dict) else "",
                    "llm_schema": config_obj.get("schema_str", "") if isinstance(config_obj, dict) else "",
                    "priority": config_obj.get("priority", "medium") if isinstance(config_obj, dict) else "medium",
                    "cache_enabled": config_obj.get("cache", False) if isinstance(config_obj, dict) else False,
                    "max_concurrent_tasks": config_obj.get("max_concurrent_tasks", 2) if isinstance(config_obj, dict) else 2,
                    "batch_timeout": config_obj.get("batch_timeout", 300) if isinstance(config_obj, dict) else 300,
                    "callback_url": config_obj.get("callback_url", "") if isinstance(config_obj, dict) else "",
                    "created_at": config_data.get("created_at"),
                    "updated_at": config_data.get("updated_at"),
                    "is_active": config_data.get("is_active", "True").lower() == "true",
                    "usage_count": int(config_data.get("usage_count", "0")),
                    "last_used_at": config_data.get("last_used_at")
                }
                configs.append(transformed_config)

        total = len(configs)
        page = 1
        page_size = 20
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1

        return {
            "success": True,
            "data": {
                "configs": configs,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            },
            "message": "获取配置列表成功"
        }
    except Exception as e:
        return {"success": False, "message": f"获取配置列表失败: {str(e)}"}

@router.post("/test")
async def test_endpoint():
    """测试端点"""
    return {"success": True, "message": "测试成功"}

@router.post("/simple")
async def create_simple_config():
    """简单创建配置"""
    try:
        config_id = str(uuid.uuid4())
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        # 保存简单配置
        config_data = {
            "id": config_id,
            "name": "默认配置",
            "platform": "mercadolibre",
            "created_at": datetime.now().isoformat()
        }

        config_key = f"crawl_configs:{config_id}"
        redis_client.hset(config_key, mapping=config_data)
        redis_client.sadd("crawl_configs:index", config_id)

        return {"success": True, "data": config_data, "message": "配置创建成功"}
    except Exception as e:
        return {"success": False, "message": f"错误: {str(e)}"}

@router.post("/")
async def create_crawl_config(request: Request):
    """创建爬取配置"""
    try:
        # 解析请求体
        form_data = await request.json()

        config_id = str(uuid.uuid4())
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )
        now = datetime.now()

        # 默认的LLM查询指令
        default_llm_query = """请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量（格式 +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品；
获取商品的各级目录文本、获取商品各级目录的链接；
商品销售商名称、商品销售商链接；
商品第一张图片的最大尺寸版链接；
商品页面的问题相关的内容、问题时间、问题回答；
商品所有问题页面的链接；
商品评分数、商品评分数量；
商品的评论内容、评论评分、评论时间；
商品所有评论页面的链接；
任务开始的时间戳YYYYMMDDHHMMSS"""

        # 构建配置数据
        config_data = {
            "id": config_id,
            "name": form_data.get("name", ""),
            "description": form_data.get("description", ""),
            "platform": form_data.get("platform", "mercadolibre"),
            "config": json.dumps({
                "q": form_data.get("llm_query", "") or default_llm_query,
                "schema_str": form_data.get("llm_schema", ""),
                "cache": form_data.get("cache_enabled", False),
                "priority": form_data.get("priority", "medium"),
                "callback_url": form_data.get("callback_url", ""),
                "max_concurrent_tasks": form_data.get("max_concurrent_tasks", 2),
                "batch_timeout": form_data.get("batch_timeout", 300)
            }, ensure_ascii=False),
            "auth_config": json.dumps({
                "user_id": "monit_system",
                "role": "user",
                "permissions": [],
                "auth_method": form_data.get("auth_method", "api_key"),
                "api_key_prefix": form_data.get("api_key_prefix", ""),
                "client_ip": "127.0.0.1",
                "user_agent": form_data.get("user_agent", "MonIt-Crawler/1.0")
            }, ensure_ascii=False),
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "is_active": str(form_data.get("is_active", True)),
            "usage_count": "0",
            "tags": json.dumps(form_data.get("tags", []), ensure_ascii=False)
        }

        # 保存到Redis
        config_key = f"crawl_configs:{config_id}"

        # 分别设置每个字段，确保中文编码正确
        for key, value in config_data.items():
            if isinstance(value, str):
                redis_client.hset(config_key, key, value.encode('utf-8').decode('utf-8'))
            else:
                redis_client.hset(config_key, key, value)

        redis_client.sadd("crawl_configs:index", config_id)
        redis_client.sadd(f"crawl_configs:by_platform:{config_data['platform']}", config_id)

        # 转换为前端期望的格式（与获取配置API一致）
        config_obj = json.loads(config_data['config'])
        transformed_config = {
            "id": config_data.get("id"),
            "name": config_data.get("name"),
            "description": config_data.get("description", ""),
            "config_type": "batch_task",  # 默认类型
            "platform": config_data.get("platform"),
            "llm_query": config_obj.get("q", ""),
            "llm_schema": config_obj.get("schema_str", ""),
            "priority": config_obj.get("priority", "medium"),
            "cache_enabled": config_obj.get("cache", False),
            "created_at": config_data.get("created_at"),
            "updated_at": config_data.get("updated_at"),
            "is_active": config_data.get("is_active", "True").lower() == "true",
            "usage_count": int(config_data.get("usage_count", "0")),
            "tags": json.loads(config_data.get("tags", "[]"))
        }

        return {
            "success": True,
            "data": transformed_config,
            "message": "配置创建成功"
        }
    except Exception as e:
        return {"success": False, "message": f"配置创建失败: {str(e)}"}

@router.get("/{config_id}")
async def get_crawl_config(config_id: str):
    """获取单个爬取配置"""
    try:
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        config_key = f"crawl_configs:{config_id}"
        config_data = redis_client.hgetall(config_key)

        if not config_data:
            return {"success": False, "message": "配置不存在"}

        # 转换JSON字段
        for key in ['config', 'auth_config', 'tags']:
            if config_data.get(key):
                try:
                    config_data[key] = json.loads(config_data[key])
                except:
                    pass

        # 转换为前端期望的格式
        config_obj = config_data.get('config', {})
        transformed_config = {
            "id": config_data.get("id"),
            "name": config_data.get("name"),
            "description": config_data.get("description", ""),
            "config_type": "batch_task",  # 默认类型
            "platform": config_data.get("platform"),
            "llm_query": config_obj.get("q", "") if isinstance(config_obj, dict) else "",
            "llm_schema": config_obj.get("schema_str", "") if isinstance(config_obj, dict) else "",
            "priority": config_obj.get("priority", "medium") if isinstance(config_obj, dict) else "medium",
            "cache_enabled": config_obj.get("cache", False) if isinstance(config_obj, dict) else False,
            "max_concurrent_tasks": config_obj.get("max_concurrent_tasks", 2) if isinstance(config_obj, dict) else 2,
            "batch_timeout": config_obj.get("batch_timeout", 300) if isinstance(config_obj, dict) else 300,
            "callback_url": config_obj.get("callback_url", "") if isinstance(config_obj, dict) else "",
            "created_at": config_data.get("created_at"),
            "updated_at": config_data.get("updated_at"),
            "is_active": config_data.get("is_active", "True").lower() == "true",
            "usage_count": int(config_data.get("usage_count", "0")),
            "last_used_at": config_data.get("last_used_at")
        }

        return {
            "success": True,
            "data": transformed_config,
            "message": "获取配置成功"
        }
    except Exception as e:
        return {"success": False, "message": f"获取配置失败: {str(e)}"}

@router.delete("/{config_id}")
async def delete_crawl_config(config_id: str):
    """删除爬取配置"""
    try:
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        config_key = f"crawl_configs:{config_id}"

        # 检查配置是否存在
        if not redis_client.exists(config_key):
            return {"success": False, "message": "配置不存在"}

        # 获取平台信息用于清理索引
        platform = redis_client.hget(config_key, "platform")

        # 删除配置
        redis_client.delete(config_key)
        redis_client.srem("crawl_configs:index", config_id)
        if platform:
            redis_client.srem(f"crawl_configs:by_platform:{platform}", config_id)

        return {
            "success": True,
            "message": "配置删除成功"
        }
    except Exception as e:
        return {"success": False, "message": f"配置删除失败: {str(e)}"}

@router.post("/{config_id}/validate")
async def validate_crawl_config(config_id: str):
    """验证爬取配置"""
    try:
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        config_key = f"crawl_configs:{config_id}"
        config_data = redis_client.hgetall(config_key)

        if not config_data:
            return {"success": False, "message": "配置不存在"}

        # 简单验证逻辑
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }

        # 检查必要字段
        if not config_data.get("name"):
            validation_result["errors"].append("配置名称不能为空")
            validation_result["is_valid"] = False

        if not config_data.get("platform"):
            validation_result["errors"].append("平台不能为空")
            validation_result["is_valid"] = False

        return {
            "success": True,
            "data": validation_result,
            "message": "配置验证完成"
        }
    except Exception as e:
        return {"success": False, "message": f"配置验证失败: {str(e)}"}

@router.post("/{config_id}/test")
async def test_crawl_config(config_id: str):
    """测试爬取配置"""
    try:
        redis_client = redis.from_url(
            "redis://redis:6379/0",
            decode_responses=True,
            encoding='utf-8',
            encoding_errors='strict'
        )

        config_key = f"crawl_configs:{config_id}"
        config_data = redis_client.hgetall(config_key)

        if not config_data:
            return {"success": False, "message": "配置不存在"}

        # 模拟测试结果
        test_result = {
            "test_passed": True,
            "response_time": 1.23,
            "status_code": 200,
            "sample_data": {
                "title": "测试商品标题",
                "price": "$99.99",
                "description": "这是一个测试商品描述"
            },
            "errors": [],
            "warnings": []
        }

        return {
            "success": True,
            "data": test_result,
            "message": "配置测试完成"
        }
    except Exception as e:
        return {"success": False, "message": f"配置测试失败: {str(e)}"}

@router.get("/platforms/")
async def get_platforms():
    """获取支持的平台列表"""
    try:
        platforms = [
            {
                "value": "mercadolibre",
                "label": "MercadoLibre",
                "description": "南美最大的电商平台",
                "supported_features": ["product_search", "price_monitoring", "inventory_tracking"],
                "rate_limit": {"requests_per_minute": 60, "concurrent_requests": 2}
            },
            {
                "value": "amazon",
                "label": "Amazon",
                "description": "全球最大的电商平台",
                "supported_features": ["product_search", "price_monitoring", "review_analysis"],
                "rate_limit": {"requests_per_minute": 30, "concurrent_requests": 1}
            },
            {
                "value": "ebay",
                "label": "eBay",
                "description": "全球知名拍卖和购物网站",
                "supported_features": ["product_search", "auction_monitoring"],
                "rate_limit": {"requests_per_minute": 45, "concurrent_requests": 2}
            }
        ]

        return {
            "success": True,
            "data": platforms,
            "message": "获取平台列表成功"
        }
    except Exception as e:
        return {"success": False, "message": f"获取平台列表失败: {str(e)}"}

@router.get("/templates/")
async def get_templates():
    """获取配置模板列表"""
    try:
        templates = [
            {
                "id": "product_monitor",
                "name": "商品监控模板",
                "description": "用于监控商品价格和库存变化",
                "platform": "mercadolibre",
                "config": {
                    "q": "",
                    "schema_str": '{"title": "string", "price": "number", "availability": "boolean"}',
                    "cache": True,
                    "priority": "medium",
                    "max_concurrent_tasks": 2,
                    "batch_timeout": 300
                }
            },
            {
                "id": "price_tracker",
                "name": "价格追踪模板",
                "description": "专门用于追踪商品价格变化",
                "platform": "amazon",
                "config": {
                    "q": "",
                    "schema_str": '{"title": "string", "price": "number", "currency": "string"}',
                    "cache": False,
                    "priority": "high",
                    "max_concurrent_tasks": 1,
                    "batch_timeout": 180
                }
            }
        ]

        return {
            "success": True,
            "data": templates,
            "message": "获取模板列表成功"
        }
    except Exception as e:
        return {"success": False, "message": f"获取模板列表失败: {str(e)}"}

@router.post("/execute")
async def execute_task(request: Request):
    """执行爬虫任务"""
    try:
        task_data = await request.json()

        # 模拟任务执行结果
        execution_result = {
            "task_id": str(uuid.uuid4()),
            "status": "completed",
            "start_time": datetime.now().isoformat(),
            "end_time": datetime.now().isoformat(),
            "results": [
                {
                    "url": "https://example.com/product/1",
                    "data": {
                        "title": "示例商品1",
                        "price": "$29.99",
                        "availability": True
                    },
                    "status": "success"
                },
                {
                    "url": "https://example.com/product/2",
                    "data": {
                        "title": "示例商品2",
                        "price": "$39.99",
                        "availability": False
                    },
                    "status": "success"
                }
            ],
            "total_processed": 2,
            "success_count": 2,
            "error_count": 0,
            "errors": []
        }

        return {
            "success": True,
            "data": execution_result,
            "message": "任务执行成功"
        }
    except Exception as e:
        return {"success": False, "message": f"任务执行失败: {str(e)}"}

@router.post("/execute/batch")
async def execute_batch_tasks(request: Request):
    """批量执行爬虫任务"""
    try:
        batch_data = await request.json()
        tasks = batch_data.get("tasks", [])

        # 模拟批量任务执行结果
        batch_result = {
            "batch_id": str(uuid.uuid4()),
            "status": "completed",
            "start_time": datetime.now().isoformat(),
            "end_time": datetime.now().isoformat(),
            "total_tasks": len(tasks),
            "completed_tasks": len(tasks),
            "failed_tasks": 0,
            "results": [
                {
                    "task_id": str(uuid.uuid4()),
                    "status": "completed",
                    "processed_count": 10,
                    "success_count": 9,
                    "error_count": 1
                } for _ in tasks
            ]
        }

        return {
            "success": True,
            "data": batch_result,
            "message": "批量任务执行成功"
        }
    except Exception as e:
        return {"success": False, "message": f"批量任务执行失败: {str(e)}"}

@router.get("/executions/{execution_id}")
async def get_execution_status(execution_id: str):
    """获取执行状态"""
    try:
        # 模拟执行状态
        execution_status = {
            "id": execution_id,
            "status": "completed",
            "start_time": "2025-08-09T14:00:00",
            "end_time": "2025-08-09T14:05:00",
            "progress": 100,
            "total_urls": 10,
            "processed_urls": 10,
            "success_count": 9,
            "error_count": 1,
            "current_url": "",
            "errors": [
                {"url": "https://example.com/error", "error": "连接超时"}
            ]
        }

        return {
            "success": True,
            "data": execution_status,
            "message": "获取执行状态成功"
        }
    except Exception as e:
        return {"success": False, "message": f"获取执行状态失败: {str(e)}"}

@router.get("/tasks/{task_id}/executions")
async def get_task_executions(task_id: str, limit: int = 10):
    """获取任务执行历史"""
    try:
        # 模拟执行历史
        executions = [
            {
                "id": str(uuid.uuid4()),
                "status": "completed",
                "start_time": "2025-08-09T14:00:00",
                "end_time": "2025-08-09T14:05:00",
                "total_urls": 10,
                "success_count": 9,
                "error_count": 1
            },
            {
                "id": str(uuid.uuid4()),
                "status": "completed",
                "start_time": "2025-08-09T13:00:00",
                "end_time": "2025-08-09T13:03:00",
                "total_urls": 5,
                "success_count": 5,
                "error_count": 0
            }
        ]

        return {
            "success": True,
            "data": {"executions": executions},
            "message": "获取执行历史成功"
        }
    except Exception as e:
        return {"success": False, "message": f"获取执行历史失败: {str(e)}"}

@router.post("/executions/{execution_id}/cancel")
async def cancel_execution(execution_id: str):
    """取消执行"""
    try:
        return {
            "success": True,
            "message": "执行已取消"
        }
    except Exception as e:
        return {"success": False, "message": f"取消执行失败: {str(e)}"}

@router.get("/{config_id}", response_model=Dict[str, Any])
async def get_crawl_config(config_id: str):
    """获取单个爬取配置"""
    try:
        config = await config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        return {
            "success": True,
            "data": config,
            "message": "获取配置成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.put("/{config_id}", response_model=Dict[str, Any])
async def update_crawl_config(config_id: str, form_data: CrawlConfigFormData):
    """更新爬取配置"""
    try:
        # 检查配置是否存在
        existing_config = await config_service.get_config(config_id)
        if not existing_config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 更新配置（使用系统配置服务）
        now = datetime.now()

        # 构建用户级配置
        user_config = {
            "q": form_data.llm_query,
            "schema_str": form_data.llm_schema,
            "cache": form_data.cache_enabled,
            "priority": form_data.priority
        }

        # 获取包含系统配置的完整配置
        enhanced_config = SystemConfigService.get_system_config_for_task(user_config)

        updated_config = CrawlConfigTemplate(
            id=config_id,
            name=form_data.name,
            description=form_data.description,
            platform=form_data.platform,
            config=CrawlRequestConfig(
                q=enhanced_config["q"],
                schema_str=enhanced_config["schema_str"],
                cache=enhanced_config["cache"],
                priority=enhanced_config["priority"],
                batch_name="",
                batch_description=""
            ),
            auth_config=AuthContextConfig(
                auth_method=form_data.auth_method,
                api_key_prefix=form_data.api_key_prefix,
                user_agent=form_data.user_agent
            ),
            created_at=existing_config.created_at,  # 保持原创建时间
            updated_at=now,
            is_active=form_data.is_active,
            usage_count=existing_config.usage_count,  # 保持使用次数
            tags=form_data.tags
        )

        # 保存到Redis
        config_key = f"{config_service.config_key_prefix}:{config_id}"
        config_data = updated_config.dict()

        # 转换datetime为字符串
        config_data["created_at"] = config_data["created_at"].isoformat()
        config_data["updated_at"] = config_data["updated_at"].isoformat()

        # 更新配置
        config_service.redis_client.hset(config_key, mapping={
            k: json.dumps(v, ensure_ascii=False) if isinstance(v, (dict, list)) else str(v)
            for k, v in config_data.items()
        })

        # 更新平台索引
        config_service.redis_client.sadd(f"{config_service.config_key_prefix}:by_platform:{form_data.platform}", config_id)

        # 转换为前端期望的格式（与创建配置API一致）
        transformed_config = {
            "id": config_id,
            "name": form_data.name,
            "description": form_data.description,
            "config_type": "batch_task",
            "platform": form_data.platform,
            "llm_query": form_data.llm_query,
            "llm_schema": form_data.llm_schema,
            "priority": form_data.priority,
            "cache_enabled": form_data.cache_enabled,
            "created_at": updated_config.created_at.isoformat(),
            "updated_at": updated_config.updated_at.isoformat(),
            "is_active": form_data.is_active,
            "usage_count": updated_config.usage_count,
            "tags": form_data.tags
        }

        return {
            "success": True,
            "data": transformed_config,
            "message": "配置更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")

@router.delete("/{config_id}", response_model=Dict[str, Any])
async def delete_crawl_config(config_id: str):
    """删除爬取配置"""
    try:
        # 检查配置是否存在
        config = await config_service.get_config(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 删除配置
        config_key = f"{config_service.config_key_prefix}:{config_id}"
        config_service.redis_client.delete(config_key)

        # 从索引中移除
        config_service.redis_client.srem(f"{config_service.config_key_prefix}:index", config_id)
        config_service.redis_client.srem(f"{config_service.config_key_prefix}:by_platform:{config.platform}", config_id)

        return {
            "success": True,
            "message": "配置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置删除失败: {str(e)}")

@router.post("/{config_id}/clone", response_model=Dict[str, Any])
async def clone_crawl_config(config_id: str, clone_data: Dict[str, str]):
    """复制爬取配置"""
    try:
        # 获取原配置
        original_config = await config_service.get_config(config_id)
        if not original_config:
            raise HTTPException(status_code=404, detail="原配置不存在")

        # 创建新配置
        new_config_id = str(uuid.uuid4())
        now = datetime.now()

        cloned_config = CrawlConfigTemplate(
            id=new_config_id,
            name=clone_data.get("name", f"{original_config.name} - 副本"),
            description=f"从 {original_config.name} 复制",
            platform=original_config.platform,
            config=original_config.config,
            auth_config=original_config.auth_config,
            created_at=now,
            updated_at=now,
            is_active=True,
            usage_count=0,
            tags=original_config.tags.copy()
        )

        # 保存到Redis
        config_key = f"{config_service.config_key_prefix}:{new_config_id}"
        config_data = cloned_config.dict()

        # 转换datetime为字符串
        config_data["created_at"] = config_data["created_at"].isoformat()
        config_data["updated_at"] = config_data["updated_at"].isoformat()

        # 保存配置
        config_service.redis_client.hset(config_key, mapping={
            k: json.dumps(v, ensure_ascii=False) if isinstance(v, (dict, list)) else str(v)
            for k, v in config_data.items()
        })

        # 添加到索引
        config_service.redis_client.sadd(f"{config_service.config_key_prefix}:index", new_config_id)
        config_service.redis_client.sadd(f"{config_service.config_key_prefix}:by_platform:{cloned_config.platform}", new_config_id)

        return {
            "success": True,
            "data": cloned_config,
            "message": "配置复制成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置复制失败: {str(e)}")



