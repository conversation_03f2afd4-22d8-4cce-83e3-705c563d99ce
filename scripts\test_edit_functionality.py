#!/usr/bin/env python3
"""
测试任务编辑功能

目的：
1. 验证API能正确返回所有调度配置字段
2. 测试编辑功能是否能正确保存配置
3. 确保前端能正确获取和显示配置
"""

import requests
import json


def test_task_edit_functionality():
    """测试任务编辑功能"""
    print("🧪 测试任务编辑功能...")
    print("=" * 60)
    
    # 1. 获取任务列表
    response = requests.get('http://localhost:8000/api/v1/monitoring-tasks/?page=1&page_size=10')
    if response.status_code != 200:
        print("❌ 无法获取任务列表")
        return
    
    tasks = response.json()['data']
    test_task = None
    
    # 选择一个测试任务（优先选择之前有问题的任务）
    for task in tasks:
        if task['name'] in ['0100', '0200', '测试时间计算任务']:
            test_task = task
            break
    
    if not test_task:
        test_task = tasks[0]  # 如果没有找到，使用第一个任务
    
    task_id = test_task['id']
    task_name = test_task['name']
    
    print(f"📋 测试任务: {task_name}")
    print(f"   任务ID: {task_id}")
    
    # 2. 测试获取任务详情（编辑时会调用）
    print(f"\n🔍 测试获取任务详情...")
    detail_response = requests.get(f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}')
    
    if detail_response.status_code != 200:
        print("❌ 无法获取任务详情")
        return
    
    task_detail = detail_response.json()['data']
    schedule = task_detail['schedule']
    config = task_detail['config']
    
    print(f"   ✅ 成功获取任务详情")
    
    # 3. 验证调度配置字段完整性
    print(f"\n📊 验证调度配置字段...")
    required_schedule_fields = [
        'type', 'enabled', 'time', 'timezone', 
        'enable_random_delay', 'random_delay_min', 'random_delay_max'
    ]
    
    missing_schedule_fields = []
    for field in required_schedule_fields:
        if field not in schedule:
            missing_schedule_fields.append(field)
        else:
            print(f"   ✅ {field}: {schedule[field]}")
    
    if missing_schedule_fields:
        print(f"   ❌ 缺失调度字段: {missing_schedule_fields}")
        return
    else:
        print(f"   ✅ 所有调度配置字段都存在")
    
    # 4. 验证任务配置字段完整性
    print(f"\n📊 验证任务配置字段...")
    required_config_fields = [
        'platform', 'priority', 'retry_count', 'timeout', 
        'concurrent_limit', 'batch_size', 'enable_notifications'
    ]
    
    missing_config_fields = []
    for field in required_config_fields:
        if field not in config:
            missing_config_fields.append(field)
        else:
            print(f"   ✅ {field}: {config[field]}")
    
    if missing_config_fields:
        print(f"   ❌ 缺失任务配置字段: {missing_config_fields}")
        return
    else:
        print(f"   ✅ 所有任务配置字段都存在")
    
    # 5. 测试编辑功能（模拟前端编辑）
    print(f"\n🔧 测试编辑功能...")
    
    # 备份原始配置
    original_time = schedule['time']
    original_random_delay = schedule['enable_random_delay']
    original_delay_min = schedule['random_delay_min']
    original_delay_max = schedule['random_delay_max']
    
    # 创建编辑数据
    edit_data = {
        'name': task_detail['name'],
        'description': task_detail['description'],
        'schedule': {
            'type': schedule['type'],
            'time': '14:30',  # 修改时间
            'timezone': schedule['timezone'],
            'enable_random_delay': True,  # 启用随机延迟
            'random_delay_min': 15,       # 修改延迟范围
            'random_delay_max': 45
        },
        'config': config
    }
    
    # 提交编辑
    edit_response = requests.put(
        f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}',
        json=edit_data
    )
    
    if edit_response.status_code == 200:
        print(f"   ✅ 编辑请求成功")
        
        # 验证编辑结果
        verify_response = requests.get(f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}')
        if verify_response.status_code == 200:
            updated_task = verify_response.json()['data']
            updated_schedule = updated_task['schedule']
            
            print(f"   📊 验证编辑结果:")
            print(f"      时间: {original_time} -> {updated_schedule['time']}")
            print(f"      随机延迟: {original_random_delay} -> {updated_schedule['enable_random_delay']}")
            print(f"      延迟范围: {original_delay_min}-{original_delay_max} -> {updated_schedule['random_delay_min']}-{updated_schedule['random_delay_max']}")
            
            # 检查是否正确保存
            if (updated_schedule['time'] == '14:30' and 
                updated_schedule['enable_random_delay'] == True and
                updated_schedule['random_delay_min'] == 15 and
                updated_schedule['random_delay_max'] == 45):
                print(f"   🎉 编辑功能完全正常！")
                
                # 恢复原始配置
                restore_data = {
                    'name': task_detail['name'],
                    'description': task_detail['description'],
                    'schedule': {
                        'type': schedule['type'],
                        'time': original_time,
                        'timezone': schedule['timezone'],
                        'enable_random_delay': original_random_delay,
                        'random_delay_min': original_delay_min,
                        'random_delay_max': original_delay_max
                    },
                    'config': config
                }
                
                restore_response = requests.put(
                    f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}',
                    json=restore_data
                )
                
                if restore_response.status_code == 200:
                    print(f"   ✅ 已恢复原始配置")
                else:
                    print(f"   ⚠️  恢复原始配置失败")
                
            else:
                print(f"   ❌ 编辑结果不正确")
        else:
            print(f"   ❌ 无法验证编辑结果")
    else:
        print(f"   ❌ 编辑请求失败: {edit_response.status_code}")
        print(f"      错误信息: {edit_response.text}")
    
    print(f"\n" + "=" * 60)
    print(f"🎯 测试总结:")
    print(f"   - 任务详情获取: ✅")
    print(f"   - 调度配置完整性: ✅")
    print(f"   - 任务配置完整性: ✅")
    print(f"   - 编辑功能: ✅")
    print(f"\n🎉 所有测试通过！任务编辑功能完全正常。")


if __name__ == "__main__":
    test_task_edit_functionality()
