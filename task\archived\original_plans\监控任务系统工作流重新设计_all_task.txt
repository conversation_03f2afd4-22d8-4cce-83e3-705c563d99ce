# 监控任务系统工作流重新设计 - 总体规划

## 📋 项目背景

### 用户需求分析
用户期望的工作流程：
1. 前端上传Excel → 后端解析URL → 返回前端显示
2. URL列表持久化到Redis，支持筛选、多选、反选
3. 从URL池选择链接创建监控任务，支持定时配置
4. 任务管理：修改配置、管理链接、手动执行、终止

### 当前系统问题
- 缺少URL池管理功能
- 任务创建流程不符合用户期望
- 缺少定时调度的完整实现
- URL与任务的关联关系设计不合理

## 🏗️ 系统架构重新设计

### 数据流架构
```
Excel上传 → URL解析 → URL池(Redis) → 任务创建 → 定时调度(Celery) → 执行监控
```

### 核心组件
1. **URL池管理器** - 管理所有解析的URL
2. **任务调度器** - 基于Celery Beat的定时任务
3. **任务执行器** - 实际的监控执行逻辑
4. **状态管理器** - 实时状态更新和同步

## 📊 数据模型设计

### URL池数据结构
```typescript
interface UrlPoolItem {
  id: string;                    // 唯一标识
  url: string;                   // URL地址
  platform: string;              // 平台类型
  source_file: string;           // 来源Excel文件
  added_at: string;              // 添加时间
  status: 'active' | 'disabled'; // 状态
  last_check?: string;           // 最后检查时间
  check_count: number;           // 检查次数
  success_count: number;         // 成功次数
  metadata?: Record<string, any>; // 元数据
}
```

### 监控任务数据结构
```typescript
interface MonitoringTask {
  id: string;                    // 任务ID
  name: string;                  // 任务名称
  description?: string;          // 任务描述
  url_ids: string[];            // 关联的URL ID列表
  schedule: ScheduleConfig;      // 调度配置
  config: TaskConfig;           // 任务配置
  status: TaskStatus;           // 任务状态
  is_running: boolean;          // 是否正在运行
  created_at: string;           // 创建时间
  updated_at: string;           // 更新时间
  last_run?: string;            // 最后运行时间
  next_run?: string;            // 下次运行时间
  stats: TaskStats;             // 统计信息
}
```

## 🔧 API接口设计

### URL池管理API
```
POST /api/v1/excel/upload-and-parse     # 上传Excel并解析URL
GET /api/v1/urls/pool                   # 获取URL池列表
POST /api/v1/urls/pool/batch-update     # 批量更新URL状态
DELETE /api/v1/urls/pool/batch          # 批量删除URL
GET /api/v1/urls/pool/stats             # 获取URL池统计信息
```

### 任务管理API
```
POST /api/v1/monitoring-tasks/          # 创建监控任务
PUT /api/v1/monitoring-tasks/{id}/urls  # 更新任务关联的URL
POST /api/v1/monitoring-tasks/{id}/execute    # 手动执行任务
POST /api/v1/monitoring-tasks/{id}/terminate  # 终止任务执行
GET /api/v1/monitoring-tasks/{id}/logs        # 获取任务执行日志
```

## 🎯 前端页面结构

### 页面路由设计
```
/monitoring
├── /url-pool              # URL池管理页面
│   ├── 上传Excel功能
│   ├── URL列表展示
│   ├── 筛选和搜索
│   └── 批量操作
├── /tasks                 # 任务列表页面
│   ├── 任务概览
│   ├── 状态监控
│   └── 快速操作
├── /tasks/create          # 创建任务页面
│   ├── 从URL池选择
│   ├── 任务配置
│   └── 定时设置
├── /tasks/:id             # 任务详情页面
│   ├── 基本信息
│   ├── URL管理
│   ├── 执行历史
│   └── 统计图表
└── /tasks/:id/edit        # 编辑任务页面
    ├── 修改配置
    ├── 调整调度
    └── 管理URL
```

## 📝 实施计划

### 阶段1：Excel上传与URL解析功能实现
- 前端Excel上传组件开发
- 后端Excel解析API实现
- URL数据存储到Redis
- 基础的URL列表展示

### 阶段2：URL池管理页面开发
- URL池管理页面UI开发
- 筛选、搜索、分页功能
- 多选、批量操作功能
- URL状态管理

### 阶段3：任务创建流程重构
- 从URL池选择链接的界面
- 任务配置表单重构
- 定时调度配置界面
- 任务创建API调整

### 阶段4：任务管理功能增强
- 任务详情页面完善
- URL关联管理功能
- 手动执行和终止功能
- 任务配置修改功能

### 阶段5：定时调度系统集成
- Celery Beat集成
- 动态任务调度
- 任务状态实时更新
- 执行日志记录

### 阶段6：数据持久化优化
- Redis数据结构优化
- 查询性能优化
- 数据一致性保证
- 备份和恢复机制

## 🎯 预期效果

### 用户体验改进
- 符合用户期望的工作流程
- 直观的URL池管理界面
- 灵活的任务创建和管理
- 实时的状态反馈

### 系统性能提升
- 高效的数据存储和查询
- 可扩展的任务调度架构
- 稳定的定时执行机制
- 完善的错误处理和恢复

### 功能完整性
- 完整的URL生命周期管理
- 灵活的任务配置选项
- 丰富的监控和统计功能
- 便捷的批量操作支持

## 📊 成功指标

### 功能指标
- URL池管理功能完整度：100%
- 任务创建流程用户满意度：>90%
- 定时调度准确性：>99%
- 系统稳定性：>99.5%

### 性能指标
- URL池查询响应时间：<200ms
- 任务创建响应时间：<500ms
- 页面加载时间：<3s
- 并发任务支持：>100个

## 🔧 技术债务和重构计划

### 统一数据访问层重构 (新增任务)
**发现时间**: 2025年7月5日
**问题描述**: 系统存在多套存储系统，数据格式不统一
- 内存存储 (`monitoring_tasks_storage`)
- Redis存储 (`monitoring_tasks:{id}`)
- SQLAlchemy/TimescaleDB (`MonitoringTaskDB`)

**影响范围**:
- 数据一致性问题
- 维护复杂度高
- 扩展性受限

**解决方案**:
- 设计统一的Repository模式
- 实现数据适配器和转换层
- 建立配置驱动的存储策略

**实施计划**:
- 时间: 第四阶段完成后 (7月16日-7月25日)
- 工作量: 1.5-2.5周
- 优先级: 高 (架构技术债务)

**详细计划**: 参见 `task/统一数据访问层重构计划_20250705.md`

## 🔄 下一步行动

1. ✅ 完成阶段1-3：Excel上传、URL池管理、任务创建 (已完成)
2. 🚀 进行阶段4：任务管理功能增强 (90%完成)
   - ✅ 任务列表页面重构 (已完成)
   - ✅ 任务详情页面开发 (已完成)
   - ✅ 编辑任务功能完整实现 (2025年7月5日完成)
   - 🚀 URL关联管理功能 (进行中)
   - 📋 手动执行和终止功能 (待开始)
3. 📋 实施阶段5：定时调度系统集成
4. 🔧 执行统一数据访问层重构 (新增)
5. 📋 完成阶段6：数据持久化优化
6. 📋 建立完整的测试体系和部署计划

---

**项目负责人**: AI Assistant
**创建时间**: 2025-07-04
**最后更新**: 2025-07-05 (编辑任务功能完成)
**预计完成时间**: 2025-07-30 (包含重构)
**当前状态**: 阶段4进行中，核心功能75%完成
