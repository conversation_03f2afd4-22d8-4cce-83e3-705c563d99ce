/**
 * 新爬虫API服务
 * 基于重新设计的后端爬虫API架构
 */

// 基础类型定义
export interface TaskPriority {
  HIGH: 'high';
  MEDIUM: 'medium';
  LOW: 'low';
}

export interface ExecutionMode {
  BATCH: 'batch';
  SINGLE: 'single';
  STREAMING: 'streaming';
}

export type PlatformType = 'mercadolibre' | 'amazon' | '1688' | 'general';

export type CrawlerConfigType = 'batch_task' | 'single_task' | 'template';

// 配置相关类型
export interface CrawlerTaskConfig {
  id: string;
  name: string;
  description?: string;
  config_type: CrawlerConfigType;
  platform: PlatformType;
  llm_query: string;
  llm_schema?: string;
  priority: 'high' | 'medium' | 'low';
  cache_enabled: boolean;
  max_concurrent_tasks?: number;
  batch_timeout?: number;
  callback_url?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  is_active: boolean;
  usage_count: number;
  last_used_at?: string;
}

export interface CrawlerConfigCreate {
  name: string;
  description?: string;
  config_type: CrawlerConfigType;
  platform: PlatformType;
  llm_query: string;
  llm_schema?: string;
  priority: 'high' | 'medium' | 'low';
  cache_enabled: boolean;
  max_concurrent_tasks?: number;
  batch_timeout?: number;
  callback_url?: string;
}

export interface CrawlerConfigUpdate {
  name?: string;
  description?: string;
  platform?: PlatformType;
  llm_query?: string;
  llm_schema?: string;
  priority?: 'high' | 'medium' | 'low';
  cache_enabled?: boolean;
  max_concurrent_tasks?: number;
  batch_timeout?: number;
  callback_url?: string;
  is_active?: boolean;
}

// 执行相关类型
export interface ExecuteTaskRequest {
  monitoring_task_id: string;
  urls: string[];
  config_id: string;
  execution_mode: 'batch' | 'single' | 'streaming';
  priority: 'high' | 'medium' | 'low';
  callback_url?: string;
  metadata?: Record<string, any>;
}

export interface ExecutionResult {
  execution_id: string;
  monitoring_task_id: string;
  batch_id?: string;
  task_ids?: string[];
  status: string;
  total_urls: number;
  submitted_at?: string;
  external_result?: Record<string, any>;
}

export interface ExecutionStatus {
  execution_id: string;
  monitoring_task_id: string;
  config_id: string;
  config_name: string;
  execution_mode: string;
  batch_id?: string;
  task_ids: string[];
  status: string;
  total_urls: number;
  submitted_at: string;
  priority: string;
  callback_url?: string;
  metadata: Record<string, any>;
  external_result: Record<string, any>;
  batch_status?: Record<string, any>;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
}

export interface ConfigListResponse {
  configs: CrawlerTaskConfig[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface PlatformInfo {
  value: string;
  label: string;
  description: string;
}

export interface ConfigTemplate {
  id: string;
  name: string;
  platform: string;
  llm_query: string;
  llm_schema?: string;
  is_builtin: boolean;
}

const API_BASE_URL = process.env.REACT_APP_API_URL || (
  // 在Docker环境中，前端通过代理访问后端
  window.location.hostname === 'localhost' ? 'http://localhost:8000' : ''
);
const CRAWLER_API_BASE = `${API_BASE_URL}/api/v1/crawl-configs`;

class NewCrawlerApiService {
  
  // ==================== 配置管理 ====================
  
  /**
   * 获取爬虫配置列表
   */
  async getConfigs(params?: {
    platform?: PlatformType;
    config_type?: CrawlerConfigType;
    is_active?: boolean;
    page?: number;
    page_size?: number;
  }): Promise<ConfigListResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.platform) searchParams.append('platform', params.platform as string);
    if (params?.config_type) searchParams.append('config_type', params.config_type as string);
    if (params?.is_active !== undefined) searchParams.append('is_active', params.is_active.toString());
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.page_size) searchParams.append('page_size', params.page_size.toString());

    const response = await fetch(`${CRAWLER_API_BASE}/?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取配置列表失败: ${response.statusText}`);
    }
    
    const result: ApiResponse<ConfigListResponse> = await response.json();
    return result.data;
  }

  /**
   * 获取单个爬虫配置
   */
  async getConfig(configId: string): Promise<CrawlerTaskConfig> {
    const response = await fetch(`${CRAWLER_API_BASE}/${configId}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('配置不存在');
      }
      throw new Error(`获取配置失败: ${response.statusText}`);
    }
    
    const result: ApiResponse<CrawlerTaskConfig> = await response.json();
    return result.data;
  }

  /**
   * 创建爬虫配置
   */
  async createConfig(configData: CrawlerConfigCreate): Promise<CrawlerTaskConfig> {
    const response = await fetch(`${CRAWLER_API_BASE}/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(configData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '创建配置失败');
    }

    const result: ApiResponse<CrawlerTaskConfig> = await response.json();
    return result.data;
  }

  /**
   * 更新爬虫配置
   */
  async updateConfig(configId: string, updateData: CrawlerConfigUpdate): Promise<CrawlerTaskConfig> {
    const response = await fetch(`${CRAWLER_API_BASE}/${configId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '更新配置失败');
    }

    const result: ApiResponse<CrawlerTaskConfig> = await response.json();
    return result.data;
  }

  /**
   * 删除爬虫配置
   */
  async deleteConfig(configId: string): Promise<void> {
    const response = await fetch(`${CRAWLER_API_BASE}/${configId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '删除配置失败');
    }
  }

  /**
   * 验证爬虫配置
   */
  async validateConfig(configId: string): Promise<any> {
    const response = await fetch(`${CRAWLER_API_BASE}/${configId}/validate`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '配置验证失败');
    }

    const result: ApiResponse = await response.json();
    return result.data;
  }

  /**
   * 测试爬虫配置
   */
  async testConfig(configId: string, testUrl: string): Promise<any> {
    const response = await fetch(`${CRAWLER_API_BASE}/${configId}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ test_url: testUrl }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '配置测试失败');
    }

    const result: ApiResponse = await response.json();
    return result.data;
  }

  /**
   * 获取支持的平台列表
   */
  async getPlatforms(): Promise<PlatformInfo[]> {
    const response = await fetch(`${CRAWLER_API_BASE}/platforms/`);
    if (!response.ok) {
      throw new Error(`获取平台列表失败: ${response.statusText}`);
    }
    
    const result: ApiResponse<PlatformInfo[]> = await response.json();
    return result.data;
  }

  /**
   * 获取配置模板
   */
  async getTemplates(): Promise<ConfigTemplate[]> {
    const response = await fetch(`${CRAWLER_API_BASE}/templates/`);
    if (!response.ok) {
      throw new Error(`获取模板列表失败: ${response.statusText}`);
    }
    
    const result: ApiResponse<ConfigTemplate[]> = await response.json();
    return result.data;
  }

  // ==================== 任务执行 ====================

  /**
   * 执行爬虫任务
   */
  async executeTask(request: ExecuteTaskRequest): Promise<ExecutionResult> {
    const response = await fetch(`${CRAWLER_API_BASE}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '任务执行失败');
    }

    const result: ApiResponse<ExecutionResult> = await response.json();
    return result.data;
  }

  /**
   * 批量执行爬虫任务
   */
  async executeBatchTasks(tasks: ExecuteTaskRequest[]): Promise<any> {
    const response = await fetch(`${CRAWLER_API_BASE}/execute/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ tasks }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '批量执行失败');
    }

    const result: ApiResponse = await response.json();
    return result.data;
  }

  /**
   * 获取执行状态
   */
  async getExecutionStatus(executionId: string): Promise<ExecutionStatus> {
    const response = await fetch(`${CRAWLER_API_BASE}/executions/${executionId}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('执行记录不存在');
      }
      throw new Error(`获取执行状态失败: ${response.statusText}`);
    }
    
    const result: ApiResponse<ExecutionStatus> = await response.json();
    return result.data;
  }

  /**
   * 获取任务执行历史
   */
  async getTaskExecutions(monitoringTaskId: string, limit: number = 10): Promise<ExecutionStatus[]> {
    const response = await fetch(`${CRAWLER_API_BASE}/tasks/${monitoringTaskId}/executions?limit=${limit}`);
    if (!response.ok) {
      throw new Error(`获取执行历史失败: ${response.statusText}`);
    }
    
    const result: ApiResponse<{executions: ExecutionStatus[]}> = await response.json();
    return result.data.executions;
  }

  /**
   * 取消执行
   */
  async cancelExecution(executionId: string): Promise<void> {
    const response = await fetch(`${CRAWLER_API_BASE}/executions/${executionId}/cancel`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '取消执行失败');
    }
  }

  // ==================== 系统监控 ====================

  /**
   * 获取执行器统计
   */
  async getExecutorStats(): Promise<any> {
    const response = await fetch(`${CRAWLER_API_BASE}/stats`);
    if (!response.ok) {
      throw new Error(`获取统计信息失败: ${response.statusText}`);
    }
    
    const result: ApiResponse = await response.json();
    return result.data;
  }

  /**
   * 测试爬虫API连接
   */
  async testConnection(): Promise<any> {
    const response = await fetch(`${CRAWLER_API_BASE}/test`, {
      method: 'POST',
    });

    const result: ApiResponse = await response.json();
    return result;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    const response = await fetch(`${CRAWLER_API_BASE}/health`);
    const result: ApiResponse = await response.json();
    return result;
  }
}

// 导出单例实例
export const newCrawlerApi = new NewCrawlerApiService();
export default newCrawlerApi;
