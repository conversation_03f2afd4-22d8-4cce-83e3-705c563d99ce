# MonIt API 接口参考文档

## 📋 概述

本文档详细描述了MonIt系统新架构下的所有API接口，包括请求格式、响应格式、错误处理等。

## 🌐 基础信息

- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 📡 API 接口详情

### 1. CrawlerConfig API (爬取配置)

#### 1.1 获取所有爬取配置
```http
GET /api/v1/crawler-configs/
```

**响应示例:**
```json
[
  {
    "config_id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "通用网页爬取配置",
    "description": "适用于大多数网站的通用配置",
    "browser_config": {
      "headless": true,
      "window_size": [1920, 1080],
      "user_agent": "Mozilla/5.0...",
      "timeout": 30
    },
    "llm_config": {
      "provider": "openai",
      "model": "gpt-4",
      "api_key": "sk-***",
      "temperature": 0.7
    },
    "crawler_config": {
      "max_depth": 3,
      "delay_range": [1, 3],
      "concurrent_requests": 5,
      "respect_robots_txt": true
    },
    "schema_config": {
      "auto_extract": true,
      "fields": ["title", "content", "links"],
      "custom_selectors": {}
    },
    "content_config": {
      "extract_text": true,
      "extract_images": false,
      "clean_html": true,
      "min_content_length": 100
    },
    "links_config": {
      "follow_external": false,
      "url_patterns": [".*"],
      "exclude_patterns": ["\\.pdf$", "\\.jpg$"]
    },
    "monitor_config": {
      "enable_screenshots": false,
      "log_level": "INFO",
      "metrics_enabled": true
    },
    "tags": ["general", "web"],
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "version": 1
  }
]
```

#### 1.2 创建爬取配置
```http
POST /api/v1/crawler-configs/
Content-Type: application/json

{
  "name": "新闻网站爬取配置",
  "description": "专门用于新闻网站的配置",
  "browser_config": {
    "headless": true,
    "window_size": [1920, 1080],
    "timeout": 30
  },
  "llm_config": {
    "provider": "openai",
    "model": "gpt-4",
    "temperature": 0.7
  },
  "crawler_config": {
    "max_depth": 2,
    "delay_range": [2, 5],
    "concurrent_requests": 3
  },
  "schema_config": {
    "auto_extract": true,
    "fields": ["title", "content", "publish_date", "author"]
  },
  "content_config": {
    "extract_text": true,
    "clean_html": true,
    "min_content_length": 200
  },
  "links_config": {
    "follow_external": false,
    "url_patterns": [".*\\/news\\/.*", ".*\\/article\\/.*"]
  },
  "monitor_config": {
    "enable_screenshots": true,
    "log_level": "DEBUG"
  },
  "tags": ["news", "media"],
  "is_active": true
}
```

#### 1.3 获取单个爬取配置
```http
GET /api/v1/crawler-configs/{config_id}/
```

#### 1.4 更新爬取配置
```http
PUT /api/v1/crawler-configs/{config_id}/
```

#### 1.5 删除爬取配置
```http
DELETE /api/v1/crawler-configs/{config_id}/
```

#### 1.6 验证爬取配置
```http
POST /api/v1/crawler-configs/{config_id}/validate/
```

**响应示例:**
```json
{
  "is_valid": true,
  "validation_results": {
    "browser_config": {"status": "valid", "issues": []},
    "llm_config": {"status": "valid", "issues": []},
    "crawler_config": {"status": "warning", "issues": ["delay_range较小可能被反爬"]},
    "schema_config": {"status": "valid", "issues": []},
    "content_config": {"status": "valid", "issues": []},
    "links_config": {"status": "valid", "issues": []},
    "monitor_config": {"status": "valid", "issues": []}
  },
  "overall_score": 95,
  "recommendations": [
    "建议增加delay_range以避免被反爬虫检测"
  ]
}
```

### 2. BackendConfig API (后端配置)

#### 2.1 获取所有后端配置
```http
GET /api/v1/backend-configs/
```

**响应示例:**
```json
[
  {
    "backend_id": "660e8400-e29b-41d4-a716-446655440001",
    "name": "主要爬虫后端",
    "description": "主要的爬虫处理后端服务",
    "host": "crawler-backend-1",
    "port": 8080,
    "protocol": "http",
    "timeout": 30,
    "max_retries": 3,
    "health_check_interval": 60,
    "performance_level": "high",
    "auth_config": {
      "type": "api_key",
      "api_key": "***"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "last_health_check": "2024-01-01T12:00:00Z",
    "health_status": "healthy"
  }
]
```

#### 2.2 创建后端配置
```http
POST /api/v1/backend-configs/
Content-Type: application/json

{
  "name": "备用爬虫后端",
  "description": "备用的爬虫处理后端",
  "host": "crawler-backend-2",
  "port": 8080,
  "protocol": "http",
  "timeout": 45,
  "max_retries": 5,
  "health_check_interval": 120,
  "performance_level": "medium",
  "auth_config": {
    "type": "basic",
    "username": "admin",
    "password": "***"
  }
}
```

#### 2.3 测试后端连接
```http
POST /api/v1/backend-configs/{backend_id}/test-connection/
```

**响应示例:**
```json
{
  "connection_successful": true,
  "response_time": 150,
  "status_code": 200,
  "health_status": "healthy",
  "capabilities": {
    "max_concurrent_requests": 10,
    "supported_protocols": ["http", "https"],
    "features": ["javascript", "cookies", "proxy"]
  },
  "test_timestamp": "2024-01-01T12:00:00Z"
}
```

#### 2.4 获取后端统计信息
```http
GET /api/v1/backend-configs/{backend_id}/stats/
```

**响应示例:**
```json
{
  "backend_id": "660e8400-e29b-41d4-a716-446655440001",
  "stats_period": "24h",
  "total_requests": 1250,
  "successful_requests": 1180,
  "failed_requests": 70,
  "average_response_time": 2.5,
  "current_load": 0.75,
  "uptime_percentage": 99.2,
  "last_updated": "2024-01-01T12:00:00Z"
}
```

### 3. CrawlerWorker API (爬虫工作器)

#### 3.1 获取所有工作器
```http
GET /api/v1/crawler-workers/
```

**查询参数:**
- `status`: 过滤状态 (active/inactive/error)
- `priority`: 过滤优先级 (high/medium/low)
- `tags`: 过滤标签
- `page`: 页码
- `limit`: 每页数量

**响应示例:**
```json
{
  "total": 25,
  "page": 1,
  "limit": 10,
  "workers": [
    {
      "worker_id": "770e8400-e29b-41d4-a716-446655440002",
      "name": "新闻爬虫Worker-01",
      "description": "专门处理新闻网站的工作器",
      "crawler_config_id": "550e8400-e29b-41d4-a716-446655440000",
      "backend_config_id": "660e8400-e29b-41d4-a716-446655440001",
      "status": "active",
      "priority": "high",
      "max_concurrent_tasks": 5,
      "current_tasks": 2,
      "total_tasks_completed": 1250,
      "total_tasks_failed": 15,
      "tags": ["news", "media", "high-priority"],
      "auto_start": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z",
      "last_activity": "2024-01-01T11:55:00Z"
    }
  ]
}
```

#### 3.2 创建工作器
```http
POST /api/v1/crawler-workers/
Content-Type: application/json

{
  "name": "电商爬虫Worker-01",
  "description": "专门处理电商网站的工作器",
  "crawler_config_id": "550e8400-e29b-41d4-a716-446655440000",
  "backend_config_id": "660e8400-e29b-41d4-a716-446655440001",
  "priority": "medium",
  "max_concurrent_tasks": 3,
  "tags": ["ecommerce", "product"],
  "auto_start": true
}
```

#### 3.3 检查工作器兼容性
```http
POST /api/v1/crawler-workers/check-compatibility/
Content-Type: application/json

{
  "crawler_config_id": "550e8400-e29b-41d4-a716-446655440000",
  "backend_config_id": "660e8400-e29b-41d4-a716-446655440001"
}
```

**响应示例:**
```json
{
  "is_compatible": true,
  "compatibility_score": 95,
  "checks": {
    "performance_match": {
      "status": "compatible",
      "score": 90,
      "details": "后端性能级别与爬虫配置需求匹配"
    },
    "feature_support": {
      "status": "compatible",
      "score": 100,
      "details": "后端支持所有必需功能"
    },
    "resource_requirements": {
      "status": "warning",
      "score": 85,
      "details": "资源需求较高，建议监控使用情况"
    }
  },
  "recommendations": [
    "建议监控资源使用情况",
    "可以考虑调整并发数以优化性能"
  ]
}
```

#### 3.4 启动工作器
```http
POST /api/v1/crawler-workers/{worker_id}/start/
```

#### 3.5 停止工作器
```http
POST /api/v1/crawler-workers/{worker_id}/stop/
```

#### 3.6 获取工作器详情
```http
GET /api/v1/crawler-workers/{worker_id}/
```

#### 3.7 更新工作器
```http
PUT /api/v1/crawler-workers/{worker_id}/
```

#### 3.8 删除工作器
```http
DELETE /api/v1/crawler-workers/{worker_id}/
```

### 4. TaskAssignment API (任务分配)

#### 4.1 获取所有任务分配
```http
GET /api/v1/task-assignments/
```

**查询参数:**
- `status`: 过滤状态 (pending/assigned/running/completed/failed)
- `worker_id`: 过滤工作器ID
- `task_id`: 过滤任务ID
- `date_from`: 开始日期
- `date_to`: 结束日期

#### 4.2 创建任务分配
```http
POST /api/v1/task-assignments/
Content-Type: application/json

{
  "task_id": "880e8400-e29b-41d4-a716-446655440003",
  "worker_ids": [
    "770e8400-e29b-41d4-a716-446655440002",
    "770e8400-e29b-41d4-a716-446655440004"
  ],
  "assignment_strategy": "least_connections",
  "auto_failover": true,
  "max_retries": 3,
  "mutual_exclusion": {
    "enabled": true,
    "scope": "url_domain",
    "timeout": 3600
  },
  "priority": "high"
}
```

**响应示例:**
```json
{
  "assignment_id": "990e8400-e29b-41d4-a716-446655440005",
  "task_id": "880e8400-e29b-41d4-a716-446655440003",
  "worker_ids": [
    "770e8400-e29b-41d4-a716-446655440002",
    "770e8400-e29b-41d4-a716-446655440004"
  ],
  "assigned_worker_id": "770e8400-e29b-41d4-a716-446655440002",
  "assignment_strategy": "least_connections",
  "auto_failover": true,
  "max_retries": 3,
  "retry_count": 0,
  "mutual_exclusion": {
    "enabled": true,
    "scope": "url_domain",
    "timeout": 3600,
    "locks_acquired": ["domain:example.com"]
  },
  "priority": "high",
  "status": "assigned",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "started_at": null,
  "completed_at": null
}
```

#### 4.3 获取分配建议
```http
POST /api/v1/task-assignments/recommend/
Content-Type: application/json

{
  "task_requirements": {
    "estimated_duration": 3600,
    "resource_requirements": "medium",
    "priority": "high",
    "required_features": ["javascript", "cookies"]
  },
  "constraints": {
    "exclude_workers": ["770e8400-e29b-41d4-a716-446655440006"],
    "required_tags": ["news"],
    "max_workers": 3
  }
}
```

**响应示例:**
```json
{
  "recommended_workers": [
    {
      "worker_id": "770e8400-e29b-41d4-a716-446655440002",
      "score": 95,
      "reasons": [
        "当前负载较低",
        "具备所需功能",
        "历史成功率高"
      ]
    },
    {
      "worker_id": "770e8400-e29b-41d4-a716-446655440004",
      "score": 88,
      "reasons": [
        "性能匹配",
        "具备所需标签"
      ]
    }
  ],
  "assignment_strategy": "least_connections",
  "estimated_completion_time": "2024-01-01T13:00:00Z",
  "confidence": 0.92
}
```

## 🚨 错误处理

### 错误响应格式
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求数据验证失败",
    "details": {
      "field": "crawler_config_id",
      "issue": "配置ID不存在"
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
  }
}
```

### 常见错误码
- `400 BAD_REQUEST`: 请求格式错误
- `401 UNAUTHORIZED`: 认证失败
- `403 FORBIDDEN`: 权限不足
- `404 NOT_FOUND`: 资源不存在
- `409 CONFLICT`: 资源冲突
- `422 VALIDATION_ERROR`: 数据验证失败
- `500 INTERNAL_ERROR`: 服务器内部错误
- `503 SERVICE_UNAVAILABLE`: 服务不可用

## 📊 响应状态码

| 状态码 | 说明 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | GET请求成功 |
| 201 | 已创建 | POST请求创建成功 |
| 204 | 无内容 | DELETE请求成功 |
| 400 | 请求错误 | 请求格式不正确 |
| 401 | 未授权 | 需要认证 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 资源不存在 |
| 409 | 冲突 | 资源冲突 |
| 422 | 无法处理 | 数据验证失败 |
| 500 | 服务器错误 | 内部错误 |

## 🔐 认证和授权

### JWT Token格式
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token Payload
```json
{
  "user_id": "user_123",
  "username": "admin",
  "roles": ["admin", "operator"],
  "permissions": ["read", "write", "delete"],
  "exp": 1640995200,
  "iat": 1640908800
}
```

## 📈 限流和配额

### 请求限制
- **普通用户**: 100 requests/minute
- **高级用户**: 500 requests/minute
- **管理员**: 1000 requests/minute

### 响应头
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640908860
```
