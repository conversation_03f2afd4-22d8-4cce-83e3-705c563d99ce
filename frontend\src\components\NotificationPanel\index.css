.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 4px;
}

.notification-header .ant-typography {
  color: #1a1a1a !important;
  font-weight: 600 !important;
}

.notification-header .ant-tag {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: none;
}

.notification-list {
  height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 8px 16px;
}

.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.notification-item {
  padding: 16px;
  border-bottom: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  margin-bottom: 8px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification-item:hover {
  background-color: #f8f9fa !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  transform: translateX(4px);
}

.notification-item.unread {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
  padding-left: 12px;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.1);
}

/* 已读通知的样式 */
.notification-item.read {
  background-color: #ffffff;
  border-left: 4px solid #f0f0f0;
  padding-left: 12px;
}

.notification-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.notification-title .ant-typography {
  color: #1a1a1a !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notification-content .ant-typography {
  color: #666666 !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
}

.notification-time {
  font-size: 12px;
  margin-top: 4px;
}

/* 不同类型通知的样式 */
.notification-item[data-type="success"] {
  border-left-color: #52c41a;
}

.notification-item[data-type="success"].unread {
  background-color: #f6ffed;
  box-shadow: 0 1px 4px rgba(82, 196, 26, 0.1);
}

/* 统一所有类型的hover状态 - 使用最高优先级 */
.notification-item:hover,
.notification-item.read:hover,
.notification-item.unread:hover,
.notification-item[data-type="success"]:hover,
.notification-item[data-type="success"].read:hover,
.notification-item[data-type="success"].unread:hover,
.notification-item[data-type="error"]:hover,
.notification-item[data-type="error"].read:hover,
.notification-item[data-type="error"].unread:hover,
.notification-item[data-type="warning"]:hover,
.notification-item[data-type="warning"].read:hover,
.notification-item[data-type="warning"].unread:hover,
.notification-item[data-type="info"]:hover,
.notification-item[data-type="info"].read:hover,
.notification-item[data-type="info"].unread:hover {
  background-color: #f8f9fa !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  transform: translateX(4px) !important;
  border-color: #d9d9d9 !important;
}

.notification-item[data-type="error"] {
  border-left-color: #ff4d4f;
}

.notification-item[data-type="error"].unread {
  background-color: #fff2f0;
  box-shadow: 0 1px 4px rgba(255, 77, 79, 0.1);
}

.notification-item[data-type="warning"] {
  border-left-color: #faad14;
}

.notification-item[data-type="warning"].unread {
  background-color: #fffbe6;
  box-shadow: 0 1px 4px rgba(250, 173, 20, 0.1);
}

.notification-item[data-type="info"] {
  border-left-color: #1890ff;
}

.notification-item[data-type="info"].unread {
  background-color: #e6f7ff;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.1);
}

.ant-list-item-action {
  margin-left: 12px;
}

.ant-list-item-action > li {
  padding: 0 4px;
}

.ant-list-item-action .ant-btn {
  border-radius: 6px;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
}

.ant-list-item-action .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ant-list-item-action .ant-btn-dangerous {
  color: #ff4d4f;
  border-color: #ffccc7;
  background: #fff2f0;
}

.ant-list-item-action .ant-btn-dangerous:hover {
  color: #ffffff;
  background: #ff4d4f;
  border-color: #ff4d4f;
}

/* 暗色主题 */
.ant-drawer-content-wrapper .ant-drawer-body {
  padding: 0;
}

.notification-item {
  border-bottom-color: #303030;
}

.notification-item:hover {
  background-color: #262626;
}

.notification-item.unread {
  background-color: #162312;
  border-left-color: #52c41a;
}

.notification-item.unread:hover {
  background-color: #1c2e1a;
}

/* 夜间模式适配 */
.app-layout.dark .notification-header .ant-typography {
  color: #ffffff !important;
}

.app-layout.dark .notification-list::-webkit-scrollbar-track {
  background: #262626;
}

.app-layout.dark .notification-list::-webkit-scrollbar-thumb {
  background: #434343;
}

.app-layout.dark .notification-list::-webkit-scrollbar-thumb:hover {
  background: #595959;
}

.app-layout.dark .notification-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .notification-item:hover {
  background: #303030;
}

.app-layout.dark .notification-item.unread {
  background-color: #162312;
  border-left-color: #52c41a;
}

.app-layout.dark .notification-item.unread:hover {
  background-color: #1c2e1a;
}

.app-layout.dark .notification-title {
  color: #ffffff;
}

.app-layout.dark .notification-message {
  color: #d9d9d9;
}

.app-layout.dark .notification-time {
  color: #8c8c8c;
}

.app-layout.dark .notification-actions {
  border-top-color: #303030;
}

.app-layout.dark .empty-state {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-icon {
  color: #595959;
}

.app-layout.dark .empty-state-text {
  color: #d9d9d9;
}
