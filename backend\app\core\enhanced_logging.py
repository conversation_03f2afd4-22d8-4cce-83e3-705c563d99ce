"""
增强的日志配置
为爬虫请求构建过程提供详细的日志记录和格式化
"""

import logging
import json
import sys
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class CrawlerRequestFormatter(logging.Formatter):
    """爬虫请求专用的日志格式化器"""
    
    def __init__(self):
        # 定义日志格式
        fmt = (
            "%(asctime)s | %(levelname)-8s | %(name)-30s | "
            "%(funcName)-20s:%(lineno)-4d | %(message)s"
        )
        super().__init__(fmt, datefmt="%Y-%m-%d %H:%M:%S")
    
    def format(self, record):
        """格式化日志记录"""
        
        # 添加颜色（如果支持）
        if hasattr(record, 'levelno'):
            if record.levelno >= logging.ERROR:
                record.levelname = f"\033[91m{record.levelname}\033[0m"  # 红色
            elif record.levelno >= logging.WARNING:
                record.levelname = f"\033[93m{record.levelname}\033[0m"  # 黄色
            elif record.levelno >= logging.INFO:
                record.levelname = f"\033[92m{record.levelname}\033[0m"  # 绿色
            elif record.levelno >= logging.DEBUG:
                record.levelname = f"\033[94m{record.levelname}\033[0m"  # 蓝色
        
        # 格式化消息
        formatted = super().format(record)
        
        # 如果消息包含特定的标识符，添加特殊格式
        if "🚀" in record.getMessage() or "✅" in record.getMessage() or "❌" in record.getMessage():
            # 这是监控相关的重要日志，添加分隔线
            formatted = f"\n{'-' * 80}\n{formatted}\n{'-' * 80}"
        
        return formatted


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.name = name
    
    def log_request_building_start(
        self, 
        task_id: str, 
        execution_id: str, 
        context: Optional[Dict[str, Any]] = None
    ):
        """记录请求构建开始"""
        
        log_data = {
            "event": "request_building_start",
            "task_id": task_id,
            "execution_id": execution_id,
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        
        self.logger.info(f"🚀 开始构建爬虫请求", extra={"structured_data": log_data})
    
    def log_request_building_complete(
        self, 
        task_id: str, 
        execution_id: str, 
        urls_count: int,
        duration: float,
        status: str = "success"
    ):
        """记录请求构建完成"""
        
        log_data = {
            "event": "request_building_complete",
            "task_id": task_id,
            "execution_id": execution_id,
            "urls_count": urls_count,
            "duration": duration,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        
        if status == "success":
            self.logger.info(f"✅ 爬虫请求构建成功", extra={"structured_data": log_data})
        else:
            self.logger.error(f"❌ 爬虫请求构建失败", extra={"structured_data": log_data})
    
    def log_data_source_access(
        self, 
        source_type: str, 
        task_id: str, 
        data_info: Dict[str, Any],
        duration: Optional[float] = None
    ):
        """记录数据源访问"""
        
        log_data = {
            "event": "data_source_access",
            "source_type": source_type,
            "task_id": task_id,
            "data_info": data_info,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.debug(f"📊 访问数据源: {source_type}", extra={"structured_data": log_data})
    
    def log_performance_warning(
        self, 
        task_id: str, 
        warning_type: str, 
        threshold: float, 
        actual_value: float
    ):
        """记录性能警告"""
        
        log_data = {
            "event": "performance_warning",
            "task_id": task_id,
            "warning_type": warning_type,
            "threshold": threshold,
            "actual_value": actual_value,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.warning(
            f"⚠️ 性能警告: {warning_type} - 阈值: {threshold}, 实际: {actual_value}",
            extra={"structured_data": log_data}
        )
    
    def log_validation_error(
        self, 
        task_id: str, 
        validation_type: str, 
        error_details: Dict[str, Any]
    ):
        """记录验证错误"""
        
        log_data = {
            "event": "validation_error",
            "task_id": task_id,
            "validation_type": validation_type,
            "error_details": error_details,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.warning(
            f"⚠️ 验证错误: {validation_type}",
            extra={"structured_data": log_data}
        )


def setup_enhanced_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """设置增强的日志配置"""
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(CrawlerRequestFormatter())
    root_logger.addHandler(console_handler)
    
    # 创建文件处理器（如果指定了日志文件）
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(CrawlerRequestFormatter())
        root_logger.addHandler(file_handler)
    
    # 为特定模块设置更详细的日志
    crawler_modules = [
        "app.services.crawler_request_builder",
        "app.services.crawler_request_monitor", 
        "app.tasks.enhanced_crawl_task",
        "app.tasks.monitoring_executor"
    ]
    
    for module in crawler_modules:
        module_logger = logging.getLogger(module)
        module_logger.setLevel(logging.DEBUG if level <= logging.DEBUG else level)
    
    # 记录日志配置完成
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 增强日志配置完成 - 级别: {log_level}, 文件: {log_file or '仅控制台'}")


class LogAnalyzer:
    """日志分析器"""
    
    @staticmethod
    def analyze_log_file(log_file: str, hours: int = 24) -> Dict[str, Any]:
        """分析日志文件"""
        
        try:
            log_path = Path(log_file)
            if not log_path.exists():
                return {"error": "日志文件不存在"}
            
            # 读取日志文件
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 分析最近几小时的日志
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 统计各种事件
            events = {
                "request_building_start": 0,
                "request_building_success": 0,
                "request_building_error": 0,
                "performance_warnings": 0,
                "validation_errors": 0
            }
            
            errors = []
            warnings = []
            
            for line in lines:
                try:
                    # 简单的日志解析
                    if "🚀 开始构建爬虫请求" in line:
                        events["request_building_start"] += 1
                    elif "✅ 爬虫请求构建成功" in line:
                        events["request_building_success"] += 1
                    elif "❌ 爬虫请求构建失败" in line:
                        events["request_building_error"] += 1
                        errors.append(line.strip())
                    elif "⚠️ 性能警告" in line:
                        events["performance_warnings"] += 1
                        warnings.append(line.strip())
                    elif "⚠️ 验证错误" in line:
                        events["validation_errors"] += 1
                        warnings.append(line.strip())
                        
                except Exception:
                    continue
            
            analysis = {
                "analysis_period": f"最近 {hours} 小时",
                "log_file": str(log_path),
                "total_lines": len(lines),
                "events": events,
                "success_rate": f"{(events['request_building_success'] / max(events['request_building_start'], 1) * 100):.1f}%",
                "recent_errors": errors[-10:],  # 最近10个错误
                "recent_warnings": warnings[-10:],  # 最近10个警告
                "analysis_time": datetime.now().isoformat()
            }
            
            return analysis
            
        except Exception as e:
            return {"error": f"日志分析失败: {str(e)}"}


# 创建全局的结构化日志器
crawler_request_logger = StructuredLogger("app.services.crawler_request_builder")
enhanced_crawl_logger = StructuredLogger("app.tasks.enhanced_crawl_task")
monitoring_executor_logger = StructuredLogger("app.tasks.monitoring_executor")
