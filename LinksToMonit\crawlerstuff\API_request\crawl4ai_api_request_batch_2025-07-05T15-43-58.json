{"api_info": {"description": "Crawl4AI API请求 - 可直接发送给后端", "endpoint": "http://localhost:11234/crawl/stream", "method": "POST", "task_type": "batch", "streaming_mode": true, "generated_at": "2025-07-05T15:43:58.338Z", "generated_from": "crawl4ai_api_tester.html"}, "request": {"url": "http://localhost:11234/crawl/stream", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"urls": ["https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM#polycard_client=recommendations_pdp-v2p&reco_backend=ranker_retrieval_system_vpp_v2p_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=pdp-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=b455fd04-c241-4472-add9-422c3ea1bf2b", "https://www.mercadolibre.com.mx/mini-platillo-para-auto-cymbal-bateria-tambor-crash-auto/up/MLMU3190246156#polycard_client=search_best-seller&wid=MLM3703411410&sid=search", "https://articulo.mercadolibre.com.mx/MLM-3699672552-car-crash-cymbal-platillo-de-ventilacion-de-aire-para-auto-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=coldstart_low_exposition%2C+ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=5&reco_backend_type=low_level&reco_id=71ca2090-c8ed-48de-afd0-e1e7555f6b7a", "https://articulo.mercadolibre.com.mx/MLM-3699703058-ventilacion-de-aire-para-platillos-de-coche-2025-divertido-_JM#polycard_client=search_best-seller", "https://www.mercadolibre.com.mx/car-crash-cymbal-air-vent-haz-tu-conduccion-mas-divertida/up/MLMU3162357664#polycard_client=search_best-seller&wid=MLM3674104316&sid=search"], "browser_config": {"headless": true, "verbose": false, "viewport_width": 1920, "viewport_height": 1080, "wait_for": 2, "timeout": 30, "ignore_https_errors": true, "extra_args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--exclude-switches=enable-automation"]}, "crawler_config": {"method": "arun_many", "verbose": true, "check_robots_txt": false, "fetch_ssl_certificate": false, "simulate_user": true, "magic": true, "override_navigator": true, "remove_overlay_elements": true, "ignore_body_visibility": true, "adjust_viewport_to_content": true, "wait_until": "domcontentloaded", "wait_for_images": false, "page_timeout": 60000, "delay_before_return_html": 0.1, "js_only": false, "scan_full_page": true, "process_iframes": true, "scroll_delay": 0.2, "cache_mode": "BYPASS", "screenshot": false, "pdf": false, "capture_mhtml": false, "exclude_external_images": false, "exclude_all_images": false, "image_score_threshold": 50, "image_description_min_word_threshold": 50, "table_score_threshold": 7, "capture_network_requests": false, "capture_console_messages": false, "log_console": false, "extraction_strategy": "LLMExtractionStrategy", "chunking_strategy": "IdentityChunking", "markdown_generator": "DefaultMarkdownGenerator", "bypass_cache": true, "llm_extraction": {"query": "提取页面中所有文章的标题、作者和发布时间", "provider": "openai", "model": "deepseek-v3-0324", "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi", "temperature": 0, "max_tokens": 16384, "top_p": 0.9, "base_url": "https://api.lkeap.cloud.tencent.com/v1"}, "schema_extraction": {"schema": {"type": "object", "description": "电商商品完整信息提取结构", "properties": {"task_info": {"type": "object", "description": "任务执行信息", "properties": {"start_timestamp": {"type": "string", "pattern": "^[0-9]{14}$", "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"}}, "required": ["start_timestamp"]}, "product_basic_info": {"type": "object", "description": "商品基础信息", "properties": {"product_url": {"type": "string", "format": "uri", "description": "商品链接"}, "product_name": {"type": "string", "description": "商品名称"}, "mlm_id": {"type": "number", "description": "商品MLM-ID中的数值部分"}}, "required": ["product_url", "product_name", "mlm_id"]}, "pricing_info": {"type": "object", "description": "价格和库存信息", "properties": {"sales_count": {"type": "number", "description": "商品销量（格式：+xxx vendidos中的数值）"}, "current_price": {"type": "number", "description": "商品现价（数值）"}, "original_price": {"type": "number", "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"}, "discount_rate": {"type": "number", "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"}, "stock_quantity": {"type": "number", "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"}, "stocktype_IsFull": {"type": "number", "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"}}, "required": ["current_price"]}, "category_info": {"type": "object", "description": "商品目录分类信息", "properties": {"category_breadcrumb": {"type": "string", "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"}, "category_hierarchy": {"type": "array", "description": "商品各级目录详细信息", "items": {"type": "object", "properties": {"category_name": {"type": "string", "description": "目录名称"}, "category_url": {"type": "string", "format": "uri", "description": "目录链接"}, "level": {"type": "integer", "description": "目录层级（1为顶级，2为二级，以此类推）"}}, "required": ["category_name", "level"]}}}, "required": ["category_breadcrumb"]}, "seller_info": {"type": "object", "description": "销售商信息", "properties": {"seller_name": {"type": "string", "description": "商品销售商名称"}, "seller_url": {"type": "string", "format": "uri", "description": "商品销售商链接"}}, "required": ["seller_name"]}, "media_info": {"type": "object", "description": "商品媒体信息", "properties": {"main_image_url": {"type": "string", "format": "uri", "description": "商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）"}}, "required": ["main_image_url"]}, "qa_section": {"type": "object", "description": "问答区域信息", "properties": {"questions": {"type": "array", "description": "商品页面的问题列表", "items": {"type": "object", "properties": {"question_content": {"type": "string", "description": "问题内容"}, "question_time": {"type": "string", "description": "问题时间"}, "answer_content": {"type": "string", "description": "问题回答"}}, "required": ["question_content", "answer_content"]}}, "all_questions_url": {"type": "string", "format": "uri", "description": "商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接"}}}, "rating_info": {"type": "object", "description": "评分信息", "properties": {"rating_score": {"type": "number", "minimum": 0, "maximum": 5, "description": "商品评分数（0-5分）"}, "rating_count": {"type": "integer", "description": "商品评分数量"}}, "required": ["rating_score", "rating_count"]}, "reviews_section": {"type": "object", "description": "评论区域信息", "properties": {"reviews": {"type": "array", "description": "商品评论列表", "items": {"type": "object", "properties": {"review_content": {"type": "string", "description": "评论内容"}, "review_rating": {"type": "number", "minimum": 0, "maximum": 5, "description": "评论评分（0-5分）"}, "review_time": {"type": "string", "description": "评论时间"}}, "required": ["review_content", "review_time"]}}, "all_reviews_url": {"type": "string", "format": "uri", "description": "Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接"}}}}, "required": ["product_basic_info", "category_info", "pricing_info", "media_info", "rating_info"]}, "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS", "validate_schema": true, "return_raw": false}, "content_processing": {"word_count_threshold": 200, "css_selector": "", "target_elements": [], "excluded_tags": ["nav", "footer", "aside"], "excluded_selector": "", "remove_forms": false, "only_text": false, "prettify": false, "parser_type": "lxml", "keep_data_attributes": false, "keep_attrs": []}, "link_filtering": {"exclude_external_links": false, "exclude_internal_links": false, "exclude_social_media_links": false, "exclude_domains": ["example.com", "ads.google.com", "facebook.com", "twitter.com"], "social_media_domains": ["facebook.com", "twitter.com", "instagram.com", "linkedin.com", "youtube.com", "tiktok.com", "pinterest.com", "reddit.com"], "exclude_external_images": false, "exclude_all_images": false, "image_score_threshold": 3, "image_description_min_word_threshold": 50, "table_score_threshold": 7}, "_config_summary": {"enabled_features": {"llm_extraction": true, "css_extraction": false, "schema_extraction": true, "content_processing": true, "link_filtering": true}, "strategy_used": "LLMExtractionStrategy"}, "task_type": "batch", "semaphore_count": 2, "stream": true, "mean_delay": 0.1, "max_range": 3, "scheduler": {"type": "AsyncQueueManager", "pool_size": 10, "memory_threshold": 4096}, "monitor": {"display_mode": "detailed", "show_progress": true, "log_errors": true}}}}, "curl_command": "curl -X POST \"http://localhost:11234/crawl/stream\" \\\n  -H \"Content-Type: application/json\" \\\n  -d \"{\n  \\\"urls\\\": [\n    \\\"https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM#polycard_client=recommendations_pdp-v2p&reco_backend=ranker_retrieval_system_vpp_v2p_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=pdp-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=b455fd04-c241-4472-add9-422c3ea1bf2b\\\",\n    \\\"https://www.mercadolibre.com.mx/mini-platillo-para-auto-cymbal-bateria-tambor-crash-auto/up/MLMU3190246156#polycard_client=search_best-seller&wid=MLM3703411410&sid=search\\\",\n    \\\"https://articulo.mercadolibre.com.mx/MLM-3699672552-car-crash-cymbal-platillo-de-ventilacion-de-aire-para-auto-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=coldstart_low_exposition%2C+ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=5&reco_backend_type=low_level&reco_id=71ca2090-c8ed-48de-afd0-e1e7555f6b7a\\\",\n    \\\"https://articulo.mercadolibre.com.mx/MLM-3699703058-ventilacion-de-aire-para-platillos-de-coche-2025-divertido-_JM#polycard_client=search_best-seller\\\",\n    \\\"https://www.mercadolibre.com.mx/car-crash-cymbal-air-vent-haz-tu-conduccion-mas-divertida/up/MLMU3162357664#polycard_client=search_best-seller&wid=MLM3674104316&sid=search\\\"\n  ],\n  \\\"browser_config\\\": {\n    \\\"headless\\\": true,\n    \\\"verbose\\\": false,\n    \\\"viewport_width\\\": 1920,\n    \\\"viewport_height\\\": 1080,\n    \\\"wait_for\\\": 2,\n    \\\"timeout\\\": 30,\n    \\\"ignore_https_errors\\\": true,\n    \\\"extra_args\\\": [\n      \\\"--no-sandbox\\\",\n      \\\"--disable-dev-shm-usage\\\",\n      \\\"--disable-blink-features=AutomationControlled\\\",\n      \\\"--exclude-switches=enable-automation\\\"\n    ]\n  },\n  \\\"crawler_config\\\": {\n    \\\"method\\\": \\\"arun_many\\\",\n    \\\"verbose\\\": true,\n    \\\"check_robots_txt\\\": false,\n    \\\"fetch_ssl_certificate\\\": false,\n    \\\"simulate_user\\\": true,\n    \\\"magic\\\": true,\n    \\\"override_navigator\\\": true,\n    \\\"remove_overlay_elements\\\": true,\n    \\\"ignore_body_visibility\\\": true,\n    \\\"adjust_viewport_to_content\\\": true,\n    \\\"wait_until\\\": \\\"domcontentloaded\\\",\n    \\\"wait_for_images\\\": false,\n    \\\"page_timeout\\\": 60000,\n    \\\"delay_before_return_html\\\": 0.1,\n    \\\"js_only\\\": false,\n    \\\"scan_full_page\\\": true,\n    \\\"process_iframes\\\": true,\n    \\\"scroll_delay\\\": 0.2,\n    \\\"cache_mode\\\": \\\"BYPASS\\\",\n    \\\"screenshot\\\": false,\n    \\\"pdf\\\": false,\n    \\\"capture_mhtml\\\": false,\n    \\\"exclude_external_images\\\": false,\n    \\\"exclude_all_images\\\": false,\n    \\\"image_score_threshold\\\": 50,\n    \\\"image_description_min_word_threshold\\\": 50,\n    \\\"table_score_threshold\\\": 7,\n    \\\"capture_network_requests\\\": false,\n    \\\"capture_console_messages\\\": false,\n    \\\"log_console\\\": false,\n    \\\"extraction_strategy\\\": \\\"LLMExtractionStrategy\\\",\n    \\\"chunking_strategy\\\": \\\"IdentityChunking\\\",\n    \\\"markdown_generator\\\": \\\"DefaultMarkdownGenerator\\\",\n    \\\"bypass_cache\\\": true,\n    \\\"llm_extraction\\\": {\n      \\\"query\\\": \\\"提取页面中所有文章的标题、作者和发布时间\\\",\n      \\\"provider\\\": \\\"openai\\\",\n      \\\"model\\\": \\\"deepseek-v3-0324\\\",\n      \\\"api_key\\\": \\\"sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi\\\",\n      \\\"temperature\\\": 0,\n      \\\"max_tokens\\\": 16384,\n      \\\"top_p\\\": 0.9,\n      \\\"base_url\\\": \\\"https://api.lkeap.cloud.tencent.com/v1\\\"\n    },\n    \\\"schema_extraction\\\": {\n      \\\"schema\\\": {\n        \\\"type\\\": \\\"object\\\",\n        \\\"description\\\": \\\"电商商品完整信息提取结构\\\",\n        \\\"properties\\\": {\n          \\\"task_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"任务执行信息\\\",\n            \\\"properties\\\": {\n              \\\"start_timestamp\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"pattern\\\": \\\"^[0-9]{14}$\\\",\n                \\\"description\\\": \\\"任务开始的时间戳，格式：YYYYMMDDHHMMSS\\\"\n              }\n            },\n            \\\"required\\\": [\n              \\\"start_timestamp\\\"\n            ]\n          },\n          \\\"product_basic_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"商品基础信息\\\",\n            \\\"properties\\\": {\n              \\\"product_url\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"format\\\": \\\"uri\\\",\n                \\\"description\\\": \\\"商品链接\\\"\n              },\n              \\\"product_name\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"description\\\": \\\"商品名称\\\"\n              },\n              \\\"mlm_id\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品MLM-ID中的数值部分\\\"\n              }\n            },\n            \\\"required\\\": [\n              \\\"product_url\\\",\n              \\\"product_name\\\",\n              \\\"mlm_id\\\"\n            ]\n          },\n          \\\"pricing_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"价格和库存信息\\\",\n            \\\"properties\\\": {\n              \\\"sales_count\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品销量（格式：+xxx vendidos中的数值）\\\"\n              },\n              \\\"current_price\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品现价（数值）\\\"\n              },\n              \\\"original_price\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\\\"\n              },\n              \\\"discount_rate\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\\\"\n              },\n              \\\"stock_quantity\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\\\"\n              },\n              \\\"stocktype_IsFull\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"description\\\": \\\"商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\\\":\\\\\"Full\\\\\"，商品有就是1，没有就是0\\\"\n              }\n            },\n            \\\"required\\\": [\n              \\\"current_price\\\"\n            ]\n          },\n          \\\"category_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"商品目录分类信息\\\",\n            \\\"properties\\\": {\n              \\\"category_breadcrumb\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"description\\\": \\\"商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）\\\"\n              },\n              \\\"category_hierarchy\\\": {\n                \\\"type\\\": \\\"array\\\",\n                \\\"description\\\": \\\"商品各级目录详细信息\\\",\n                \\\"items\\\": {\n                  \\\"type\\\": \\\"object\\\",\n                  \\\"properties\\\": {\n                    \\\"category_name\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"description\\\": \\\"目录名称\\\"\n                    },\n                    \\\"category_url\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"format\\\": \\\"uri\\\",\n                      \\\"description\\\": \\\"目录链接\\\"\n                    },\n                    \\\"level\\\": {\n                      \\\"type\\\": \\\"integer\\\",\n                      \\\"description\\\": \\\"目录层级（1为顶级，2为二级，以此类推）\\\"\n                    }\n                  },\n                  \\\"required\\\": [\n                    \\\"category_name\\\",\n                    \\\"level\\\"\n                  ]\n                }\n              }\n            },\n            \\\"required\\\": [\n              \\\"category_breadcrumb\\\"\n            ]\n          },\n          \\\"seller_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"销售商信息\\\",\n            \\\"properties\\\": {\n              \\\"seller_name\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"description\\\": \\\"商品销售商名称\\\"\n              },\n              \\\"seller_url\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"format\\\": \\\"uri\\\",\n                \\\"description\\\": \\\"商品销售商链接\\\"\n              }\n            },\n            \\\"required\\\": [\n              \\\"seller_name\\\"\n            ]\n          },\n          \\\"media_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"商品媒体信息\\\",\n            \\\"properties\\\": {\n              \\\"main_image_url\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"format\\\": \\\"uri\\\",\n                \\\"description\\\": \\\"商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）\\\"\n              }\n            },\n            \\\"required\\\": [\n              \\\"main_image_url\\\"\n            ]\n          },\n          \\\"qa_section\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"问答区域信息\\\",\n            \\\"properties\\\": {\n              \\\"questions\\\": {\n                \\\"type\\\": \\\"array\\\",\n                \\\"description\\\": \\\"商品页面的问题列表\\\",\n                \\\"items\\\": {\n                  \\\"type\\\": \\\"object\\\",\n                  \\\"properties\\\": {\n                    \\\"question_content\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"description\\\": \\\"问题内容\\\"\n                    },\n                    \\\"question_time\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"description\\\": \\\"问题时间\\\"\n                    },\n                    \\\"answer_content\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"description\\\": \\\"问题回答\\\"\n                    }\n                  },\n                  \\\"required\\\": [\n                    \\\"question_content\\\",\n                    \\\"answer_content\\\"\n                  ]\n                }\n              },\n              \\\"all_questions_url\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"format\\\": \\\"uri\\\",\n                \\\"description\\\": \\\"商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接\\\"\n              }\n            }\n          },\n          \\\"rating_info\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"评分信息\\\",\n            \\\"properties\\\": {\n              \\\"rating_score\\\": {\n                \\\"type\\\": \\\"number\\\",\n                \\\"minimum\\\": 0,\n                \\\"maximum\\\": 5,\n                \\\"description\\\": \\\"商品评分数（0-5分）\\\"\n              },\n              \\\"rating_count\\\": {\n                \\\"type\\\": \\\"integer\\\",\n                \\\"description\\\": \\\"商品评分数量\\\"\n              }\n            },\n            \\\"required\\\": [\n              \\\"rating_score\\\",\n              \\\"rating_count\\\"\n            ]\n          },\n          \\\"reviews_section\\\": {\n            \\\"type\\\": \\\"object\\\",\n            \\\"description\\\": \\\"评论区域信息\\\",\n            \\\"properties\\\": {\n              \\\"reviews\\\": {\n                \\\"type\\\": \\\"array\\\",\n                \\\"description\\\": \\\"商品评论列表\\\",\n                \\\"items\\\": {\n                  \\\"type\\\": \\\"object\\\",\n                  \\\"properties\\\": {\n                    \\\"review_content\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"description\\\": \\\"评论内容\\\"\n                    },\n                    \\\"review_rating\\\": {\n                      \\\"type\\\": \\\"number\\\",\n                      \\\"minimum\\\": 0,\n                      \\\"maximum\\\": 5,\n                      \\\"description\\\": \\\"评论评分（0-5分）\\\"\n                    },\n                    \\\"review_time\\\": {\n                      \\\"type\\\": \\\"string\\\",\n                      \\\"description\\\": \\\"评论时间\\\"\n                    }\n                  },\n                  \\\"required\\\": [\n                    \\\"review_content\\\",\n                    \\\"review_time\\\"\n                  ]\n                }\n              },\n              \\\"all_reviews_url\\\": {\n                \\\"type\\\": \\\"string\\\",\n                \\\"format\\\": \\\"uri\\\",\n                \\\"description\\\": \\\"Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接\\\"\n              }\n            }\n          }\n        },\n        \\\"required\\\": [\n          \\\"product_basic_info\\\",\n          \\\"category_info\\\",\n          \\\"pricing_info\\\",\n          \\\"media_info\\\",\n          \\\"rating_info\\\"\n        ]\n      },\n      \\\"instructions\\\": \\\"请严格按照以下条件提取商品信息：\\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\\n2. 如果页面有多个商品，只提取最突出显示的主商品\\n3. 重点关注页面标题中提到的商品\\n4. 忽略广告推荐和次要商品信息\\n5. 确保提取的商品名称与页面URL或页面标题相匹配\\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\\n\\n需要提取的信息如下：\\n商品链接、商品名称、商品MLM-ID；\\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\\\\\\\":\\\\\\\\\"Full\\\\\\\\\"，商品有就是1，没有就是0)；\\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\\n商品销售商名称、商品销售商链接；\\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\\n商品评分数、商品评分数量、\\n商品的评论内容、评论评分、评论时间；\\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\\n任务开始的时间戳YYYYMMDDHHMMSS\\\",\n      \\\"validate_schema\\\": true,\n      \\\"return_raw\\\": false\n    },\n    \\\"content_processing\\\": {\n      \\\"word_count_threshold\\\": 200,\n      \\\"css_selector\\\": \\\"\\\",\n      \\\"target_elements\\\": [],\n      \\\"excluded_tags\\\": [\n        \\\"nav\\\",\n        \\\"footer\\\",\n        \\\"aside\\\"\n      ],\n      \\\"excluded_selector\\\": \\\"\\\",\n      \\\"remove_forms\\\": false,\n      \\\"only_text\\\": false,\n      \\\"prettify\\\": false,\n      \\\"parser_type\\\": \\\"lxml\\\",\n      \\\"keep_data_attributes\\\": false,\n      \\\"keep_attrs\\\": []\n    },\n    \\\"link_filtering\\\": {\n      \\\"exclude_external_links\\\": false,\n      \\\"exclude_internal_links\\\": false,\n      \\\"exclude_social_media_links\\\": false,\n      \\\"exclude_domains\\\": [\n        \\\"example.com\\\",\n        \\\"ads.google.com\\\",\n        \\\"facebook.com\\\",\n        \\\"twitter.com\\\"\n      ],\n      \\\"social_media_domains\\\": [\n        \\\"facebook.com\\\",\n        \\\"twitter.com\\\",\n        \\\"instagram.com\\\",\n        \\\"linkedin.com\\\",\n        \\\"youtube.com\\\",\n        \\\"tiktok.com\\\",\n        \\\"pinterest.com\\\",\n        \\\"reddit.com\\\"\n      ],\n      \\\"exclude_external_images\\\": false,\n      \\\"exclude_all_images\\\": false,\n      \\\"image_score_threshold\\\": 3,\n      \\\"image_description_min_word_threshold\\\": 50,\n      \\\"table_score_threshold\\\": 7\n    },\n    \\\"_config_summary\\\": {\n      \\\"enabled_features\\\": {\n        \\\"llm_extraction\\\": true,\n        \\\"css_extraction\\\": false,\n        \\\"schema_extraction\\\": true,\n        \\\"content_processing\\\": true,\n        \\\"link_filtering\\\": true\n      },\n      \\\"strategy_used\\\": \\\"LLMExtractionStrategy\\\"\n    },\n    \\\"task_type\\\": \\\"batch\\\",\n    \\\"semaphore_count\\\": 2,\n    \\\"stream\\\": true,\n    \\\"mean_delay\\\": 0.1,\n    \\\"max_range\\\": 3,\n    \\\"scheduler\\\": {\n      \\\"type\\\": \\\"AsyncQueueManager\\\",\n      \\\"pool_size\\\": 10,\n      \\\"memory_threshold\\\": 4096\n    },\n    \\\"monitor\\\": {\n      \\\"display_mode\\\": \\\"detailed\\\",\n      \\\"show_progress\\\": true,\n      \\\"log_errors\\\": true\n    }\n  }\n}\"", "python_code": "import requests\nimport json\n\n# API请求配置\nurl = \"http://localhost:11234/crawl/stream\"\nheaders = {\n    \"Content-Type\": \"application/json\"\n}\n\n# 请求数据\ndata = {\n    \"urls\": [\n        \"https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM#polycard_client=recommendations_pdp-v2p&reco_backend=ranker_retrieval_system_vpp_v2p_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=pdp-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=b455fd04-c241-4472-add9-422c3ea1bf2b\",\n        \"https://www.mercadolibre.com.mx/mini-platillo-para-auto-cymbal-bateria-tambor-crash-auto/up/MLMU3190246156#polycard_client=search_best-seller&wid=MLM3703411410&sid=search\",\n        \"https://articulo.mercadolibre.com.mx/MLM-3699672552-car-crash-cymbal-platillo-de-ventilacion-de-aire-para-auto-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=coldstart_low_exposition%2C+ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=5&reco_backend_type=low_level&reco_id=71ca2090-c8ed-48de-afd0-e1e7555f6b7a\",\n        \"https://articulo.mercadolibre.com.mx/MLM-3699703058-ventilacion-de-aire-para-platillos-de-coche-2025-divertido-_JM#polycard_client=search_best-seller\",\n        \"https://www.mercadolibre.com.mx/car-crash-cymbal-air-vent-haz-tu-conduccion-mas-divertida/up/MLMU3162357664#polycard_client=search_best-seller&wid=MLM3674104316&sid=search\"\n    ],\n    \"browser_config\": {\n        \"headless\": true,\n        \"verbose\": false,\n        \"viewport_width\": 1920,\n        \"viewport_height\": 1080,\n        \"wait_for\": 2,\n        \"timeout\": 30,\n        \"ignore_https_errors\": true,\n        \"extra_args\": [\n            \"--no-sandbox\",\n            \"--disable-dev-shm-usage\",\n            \"--disable-blink-features=AutomationControlled\",\n            \"--exclude-switches=enable-automation\"\n        ]\n    },\n    \"crawler_config\": {\n        \"method\": \"arun_many\",\n        \"verbose\": true,\n        \"check_robots_txt\": false,\n        \"fetch_ssl_certificate\": false,\n        \"simulate_user\": true,\n        \"magic\": true,\n        \"override_navigator\": true,\n        \"remove_overlay_elements\": true,\n        \"ignore_body_visibility\": true,\n        \"adjust_viewport_to_content\": true,\n        \"wait_until\": \"domcontentloaded\",\n        \"wait_for_images\": false,\n        \"page_timeout\": 60000,\n        \"delay_before_return_html\": 0.1,\n        \"js_only\": false,\n        \"scan_full_page\": true,\n        \"process_iframes\": true,\n        \"scroll_delay\": 0.2,\n        \"cache_mode\": \"BYPASS\",\n        \"screenshot\": false,\n        \"pdf\": false,\n        \"capture_mhtml\": false,\n        \"exclude_external_images\": false,\n        \"exclude_all_images\": false,\n        \"image_score_threshold\": 50,\n        \"image_description_min_word_threshold\": 50,\n        \"table_score_threshold\": 7,\n        \"capture_network_requests\": false,\n        \"capture_console_messages\": false,\n        \"log_console\": false,\n        \"extraction_strategy\": \"LLMExtractionStrategy\",\n        \"chunking_strategy\": \"IdentityChunking\",\n        \"markdown_generator\": \"DefaultMarkdownGenerator\",\n        \"bypass_cache\": true,\n        \"llm_extraction\": {\n            \"query\": \"提取页面中所有文章的标题、作者和发布时间\",\n            \"provider\": \"openai\",\n            \"model\": \"deepseek-v3-0324\",\n            \"api_key\": \"sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi\",\n            \"temperature\": 0,\n            \"max_tokens\": 16384,\n            \"top_p\": 0.9,\n            \"base_url\": \"https://api.lkeap.cloud.tencent.com/v1\"\n        },\n        \"schema_extraction\": {\n            \"schema\": {\n                \"type\": \"object\",\n                \"description\": \"电商商品完整信息提取结构\",\n                \"properties\": {\n                    \"task_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"任务执行信息\",\n                        \"properties\": {\n                            \"start_timestamp\": {\n                                \"type\": \"string\",\n                                \"pattern\": \"^[0-9]{14}$\",\n                                \"description\": \"任务开始的时间戳，格式：YYYYMMDDHHMMSS\"\n                            }\n                        },\n                        \"required\": [\n                            \"start_timestamp\"\n                        ]\n                    },\n                    \"product_basic_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"商品基础信息\",\n                        \"properties\": {\n                            \"product_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品链接\"\n                            },\n                            \"product_name\": {\n                                \"type\": \"string\",\n                                \"description\": \"商品名称\"\n                            },\n                            \"mlm_id\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品MLM-ID中的数值部分\"\n                            }\n                        },\n                        \"required\": [\n                            \"product_url\",\n                            \"product_name\",\n                            \"mlm_id\"\n                        ]\n                    },\n                    \"pricing_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"价格和库存信息\",\n                        \"properties\": {\n                            \"sales_count\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品销量（格式：+xxx vendidos中的数值）\"\n                            },\n                            \"current_price\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品现价（数值）\"\n                            },\n                            \"original_price\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\"\n                            },\n                            \"discount_rate\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\"\n                            },\n                            \"stock_quantity\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\"\n                            },\n                            \"stocktype_IsFull\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0\"\n                            }\n                        },\n                        \"required\": [\n                            \"current_price\"\n                        ]\n                    },\n                    \"category_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"商品目录分类信息\",\n                        \"properties\": {\n                            \"category_breadcrumb\": {\n                                \"type\": \"string\",\n                                \"description\": \"商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）\"\n                            },\n                            \"category_hierarchy\": {\n                                \"type\": \"array\",\n                                \"description\": \"商品各级目录详细信息\",\n                                \"items\": {\n                                    \"type\": \"object\",\n                                    \"properties\": {\n                                        \"category_name\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"目录名称\"\n                                        },\n                                        \"category_url\": {\n                                            \"type\": \"string\",\n                                            \"format\": \"uri\",\n                                            \"description\": \"目录链接\"\n                                        },\n                                        \"level\": {\n                                            \"type\": \"integer\",\n                                            \"description\": \"目录层级（1为顶级，2为二级，以此类推）\"\n                                        }\n                                    },\n                                    \"required\": [\n                                        \"category_name\",\n                                        \"level\"\n                                    ]\n                                }\n                            }\n                        },\n                        \"required\": [\n                            \"category_breadcrumb\"\n                        ]\n                    },\n                    \"seller_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"销售商信息\",\n                        \"properties\": {\n                            \"seller_name\": {\n                                \"type\": \"string\",\n                                \"description\": \"商品销售商名称\"\n                            },\n                            \"seller_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品销售商链接\"\n                            }\n                        },\n                        \"required\": [\n                            \"seller_name\"\n                        ]\n                    },\n                    \"media_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"商品媒体信息\",\n                        \"properties\": {\n                            \"main_image_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）\"\n                            }\n                        },\n                        \"required\": [\n                            \"main_image_url\"\n                        ]\n                    },\n                    \"qa_section\": {\n                        \"type\": \"object\",\n                        \"description\": \"问答区域信息\",\n                        \"properties\": {\n                            \"questions\": {\n                                \"type\": \"array\",\n                                \"description\": \"商品页面的问题列表\",\n                                \"items\": {\n                                    \"type\": \"object\",\n                                    \"properties\": {\n                                        \"question_content\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"问题内容\"\n                                        },\n                                        \"question_time\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"问题时间\"\n                                        },\n                                        \"answer_content\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"问题回答\"\n                                        }\n                                    },\n                                    \"required\": [\n                                        \"question_content\",\n                                        \"answer_content\"\n                                    ]\n                                }\n                            },\n                            \"all_questions_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接\"\n                            }\n                        }\n                    },\n                    \"rating_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"评分信息\",\n                        \"properties\": {\n                            \"rating_score\": {\n                                \"type\": \"number\",\n                                \"minimum\": 0,\n                                \"maximum\": 5,\n                                \"description\": \"商品评分数（0-5分）\"\n                            },\n                            \"rating_count\": {\n                                \"type\": \"integer\",\n                                \"description\": \"商品评分数量\"\n                            }\n                        },\n                        \"required\": [\n                            \"rating_score\",\n                            \"rating_count\"\n                        ]\n                    },\n                    \"reviews_section\": {\n                        \"type\": \"object\",\n                        \"description\": \"评论区域信息\",\n                        \"properties\": {\n                            \"reviews\": {\n                                \"type\": \"array\",\n                                \"description\": \"商品评论列表\",\n                                \"items\": {\n                                    \"type\": \"object\",\n                                    \"properties\": {\n                                        \"review_content\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"评论内容\"\n                                        },\n                                        \"review_rating\": {\n                                            \"type\": \"number\",\n                                            \"minimum\": 0,\n                                            \"maximum\": 5,\n                                            \"description\": \"评论评分（0-5分）\"\n                                        },\n                                        \"review_time\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"评论时间\"\n                                        }\n                                    },\n                                    \"required\": [\n                                        \"review_content\",\n                                        \"review_time\"\n                                    ]\n                                }\n                            },\n                            \"all_reviews_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接\"\n                            }\n                        }\n                    }\n                },\n                \"required\": [\n                    \"product_basic_info\",\n                    \"category_info\",\n                    \"pricing_info\",\n                    \"media_info\",\n                    \"rating_info\"\n                ]\n            },\n            \"instructions\": \"请严格按照以下条件提取商品信息：\\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\\n2. 如果页面有多个商品，只提取最突出显示的主商品\\n3. 重点关注页面标题中提到的商品\\n4. 忽略广告推荐和次要商品信息\\n5. 确保提取的商品名称与页面URL或页面标题相匹配\\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\\n\\n需要提取的信息如下：\\n商品链接、商品名称、商品MLM-ID；\\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\\\\\":\\\\\\\"Full\\\\\\\"，商品有就是1，没有就是0)；\\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\\n商品销售商名称、商品销售商链接；\\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\\n商品评分数、商品评分数量、\\n商品的评论内容、评论评分、评论时间；\\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\\n任务开始的时间戳YYYYMMDDHHMMSS\",\n            \"validate_schema\": true,\n            \"return_raw\": false\n        },\n        \"content_processing\": {\n            \"word_count_threshold\": 200,\n            \"css_selector\": \"\",\n            \"target_elements\": [],\n            \"excluded_tags\": [\n                \"nav\",\n                \"footer\",\n                \"aside\"\n            ],\n            \"excluded_selector\": \"\",\n            \"remove_forms\": false,\n            \"only_text\": false,\n            \"prettify\": false,\n            \"parser_type\": \"lxml\",\n            \"keep_data_attributes\": false,\n            \"keep_attrs\": []\n        },\n        \"link_filtering\": {\n            \"exclude_external_links\": false,\n            \"exclude_internal_links\": false,\n            \"exclude_social_media_links\": false,\n            \"exclude_domains\": [\n                \"example.com\",\n                \"ads.google.com\",\n                \"facebook.com\",\n                \"twitter.com\"\n            ],\n            \"social_media_domains\": [\n                \"facebook.com\",\n                \"twitter.com\",\n                \"instagram.com\",\n                \"linkedin.com\",\n                \"youtube.com\",\n                \"tiktok.com\",\n                \"pinterest.com\",\n                \"reddit.com\"\n            ],\n            \"exclude_external_images\": false,\n            \"exclude_all_images\": false,\n            \"image_score_threshold\": 3,\n            \"image_description_min_word_threshold\": 50,\n            \"table_score_threshold\": 7\n        },\n        \"_config_summary\": {\n            \"enabled_features\": {\n                \"llm_extraction\": true,\n                \"css_extraction\": false,\n                \"schema_extraction\": true,\n                \"content_processing\": true,\n                \"link_filtering\": true\n            },\n            \"strategy_used\": \"LLMExtractionStrategy\"\n        },\n        \"task_type\": \"batch\",\n        \"semaphore_count\": 2,\n        \"stream\": true,\n        \"mean_delay\": 0.1,\n        \"max_range\": 3,\n        \"scheduler\": {\n            \"type\": \"AsyncQueueManager\",\n            \"pool_size\": 10,\n            \"memory_threshold\": 4096\n        },\n        \"monitor\": {\n            \"display_mode\": \"detailed\",\n            \"show_progress\": true,\n            \"log_errors\": true\n        }\n    }\n}\n\n# 发送请求\ntry:\n    response = requests.post(url, headers=headers, json=data)\n    response.raise_for_status()  # 检查HTTP错误\n\n    # 处理响应\n    result = response.json()\n    print(\"请求成功:\")\n    print(json.dumps(result, indent=2, ensure_ascii=False))\n\nexcept requests.exceptions.RequestException as e:\n    print(f\"请求失败: {e}\")\nexcept json.JSONDecodeError as e:\n    print(f\"JSON解析失败: {e}\")\n    print(f\"响应内容: {response.text}\")", "javascript_code": "// JavaScript/Node.js 示例\nasync function callCrawl4aiAPI() {\n    const url = \"http://localhost:11234/crawl/stream\";\n    const headers = {\n    \"Content-Type\": \"application/json\"\n};\n    const data = {\n    \"urls\": [\n        \"https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM#polycard_client=recommendations_pdp-v2p&reco_backend=ranker_retrieval_system_vpp_v2p_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=pdp-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=b455fd04-c241-4472-add9-422c3ea1bf2b\",\n        \"https://www.mercadolibre.com.mx/mini-platillo-para-auto-cymbal-bateria-tambor-crash-auto/up/MLMU3190246156#polycard_client=search_best-seller&wid=MLM3703411410&sid=search\",\n        \"https://articulo.mercadolibre.com.mx/MLM-3699672552-car-crash-cymbal-platillo-de-ventilacion-de-aire-para-auto-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=coldstart_low_exposition%2C+ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=5&reco_backend_type=low_level&reco_id=71ca2090-c8ed-48de-afd0-e1e7555f6b7a\",\n        \"https://articulo.mercadolibre.com.mx/MLM-3699703058-ventilacion-de-aire-para-platillos-de-coche-2025-divertido-_JM#polycard_client=search_best-seller\",\n        \"https://www.mercadolibre.com.mx/car-crash-cymbal-air-vent-haz-tu-conduccion-mas-divertida/up/MLMU3162357664#polycard_client=search_best-seller&wid=MLM3674104316&sid=search\"\n    ],\n    \"browser_config\": {\n        \"headless\": true,\n        \"verbose\": false,\n        \"viewport_width\": 1920,\n        \"viewport_height\": 1080,\n        \"wait_for\": 2,\n        \"timeout\": 30,\n        \"ignore_https_errors\": true,\n        \"extra_args\": [\n            \"--no-sandbox\",\n            \"--disable-dev-shm-usage\",\n            \"--disable-blink-features=AutomationControlled\",\n            \"--exclude-switches=enable-automation\"\n        ]\n    },\n    \"crawler_config\": {\n        \"method\": \"arun_many\",\n        \"verbose\": true,\n        \"check_robots_txt\": false,\n        \"fetch_ssl_certificate\": false,\n        \"simulate_user\": true,\n        \"magic\": true,\n        \"override_navigator\": true,\n        \"remove_overlay_elements\": true,\n        \"ignore_body_visibility\": true,\n        \"adjust_viewport_to_content\": true,\n        \"wait_until\": \"domcontentloaded\",\n        \"wait_for_images\": false,\n        \"page_timeout\": 60000,\n        \"delay_before_return_html\": 0.1,\n        \"js_only\": false,\n        \"scan_full_page\": true,\n        \"process_iframes\": true,\n        \"scroll_delay\": 0.2,\n        \"cache_mode\": \"BYPASS\",\n        \"screenshot\": false,\n        \"pdf\": false,\n        \"capture_mhtml\": false,\n        \"exclude_external_images\": false,\n        \"exclude_all_images\": false,\n        \"image_score_threshold\": 50,\n        \"image_description_min_word_threshold\": 50,\n        \"table_score_threshold\": 7,\n        \"capture_network_requests\": false,\n        \"capture_console_messages\": false,\n        \"log_console\": false,\n        \"extraction_strategy\": \"LLMExtractionStrategy\",\n        \"chunking_strategy\": \"IdentityChunking\",\n        \"markdown_generator\": \"DefaultMarkdownGenerator\",\n        \"bypass_cache\": true,\n        \"llm_extraction\": {\n            \"query\": \"提取页面中所有文章的标题、作者和发布时间\",\n            \"provider\": \"openai\",\n            \"model\": \"deepseek-v3-0324\",\n            \"api_key\": \"sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi\",\n            \"temperature\": 0,\n            \"max_tokens\": 16384,\n            \"top_p\": 0.9,\n            \"base_url\": \"https://api.lkeap.cloud.tencent.com/v1\"\n        },\n        \"schema_extraction\": {\n            \"schema\": {\n                \"type\": \"object\",\n                \"description\": \"电商商品完整信息提取结构\",\n                \"properties\": {\n                    \"task_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"任务执行信息\",\n                        \"properties\": {\n                            \"start_timestamp\": {\n                                \"type\": \"string\",\n                                \"pattern\": \"^[0-9]{14}$\",\n                                \"description\": \"任务开始的时间戳，格式：YYYYMMDDHHMMSS\"\n                            }\n                        },\n                        \"required\": [\n                            \"start_timestamp\"\n                        ]\n                    },\n                    \"product_basic_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"商品基础信息\",\n                        \"properties\": {\n                            \"product_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品链接\"\n                            },\n                            \"product_name\": {\n                                \"type\": \"string\",\n                                \"description\": \"商品名称\"\n                            },\n                            \"mlm_id\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品MLM-ID中的数值部分\"\n                            }\n                        },\n                        \"required\": [\n                            \"product_url\",\n                            \"product_name\",\n                            \"mlm_id\"\n                        ]\n                    },\n                    \"pricing_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"价格和库存信息\",\n                        \"properties\": {\n                            \"sales_count\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品销量（格式：+xxx vendidos中的数值）\"\n                            },\n                            \"current_price\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品现价（数值）\"\n                            },\n                            \"original_price\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价\"\n                            },\n                            \"discount_rate\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100\"\n                            },\n                            \"stock_quantity\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0\"\n                            },\n                            \"stocktype_IsFull\": {\n                                \"type\": \"number\",\n                                \"description\": \"商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0\"\n                            }\n                        },\n                        \"required\": [\n                            \"current_price\"\n                        ]\n                    },\n                    \"category_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"商品目录分类信息\",\n                        \"properties\": {\n                            \"category_breadcrumb\": {\n                                \"type\": \"string\",\n                                \"description\": \"商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）\"\n                            },\n                            \"category_hierarchy\": {\n                                \"type\": \"array\",\n                                \"description\": \"商品各级目录详细信息\",\n                                \"items\": {\n                                    \"type\": \"object\",\n                                    \"properties\": {\n                                        \"category_name\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"目录名称\"\n                                        },\n                                        \"category_url\": {\n                                            \"type\": \"string\",\n                                            \"format\": \"uri\",\n                                            \"description\": \"目录链接\"\n                                        },\n                                        \"level\": {\n                                            \"type\": \"integer\",\n                                            \"description\": \"目录层级（1为顶级，2为二级，以此类推）\"\n                                        }\n                                    },\n                                    \"required\": [\n                                        \"category_name\",\n                                        \"level\"\n                                    ]\n                                }\n                            }\n                        },\n                        \"required\": [\n                            \"category_breadcrumb\"\n                        ]\n                    },\n                    \"seller_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"销售商信息\",\n                        \"properties\": {\n                            \"seller_name\": {\n                                \"type\": \"string\",\n                                \"description\": \"商品销售商名称\"\n                            },\n                            \"seller_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品销售商链接\"\n                            }\n                        },\n                        \"required\": [\n                            \"seller_name\"\n                        ]\n                    },\n                    \"media_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"商品媒体信息\",\n                        \"properties\": {\n                            \"main_image_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）\"\n                            }\n                        },\n                        \"required\": [\n                            \"main_image_url\"\n                        ]\n                    },\n                    \"qa_section\": {\n                        \"type\": \"object\",\n                        \"description\": \"问答区域信息\",\n                        \"properties\": {\n                            \"questions\": {\n                                \"type\": \"array\",\n                                \"description\": \"商品页面的问题列表\",\n                                \"items\": {\n                                    \"type\": \"object\",\n                                    \"properties\": {\n                                        \"question_content\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"问题内容\"\n                                        },\n                                        \"question_time\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"问题时间\"\n                                        },\n                                        \"answer_content\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"问题回答\"\n                                        }\n                                    },\n                                    \"required\": [\n                                        \"question_content\",\n                                        \"answer_content\"\n                                    ]\n                                }\n                            },\n                            \"all_questions_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接\"\n                            }\n                        }\n                    },\n                    \"rating_info\": {\n                        \"type\": \"object\",\n                        \"description\": \"评分信息\",\n                        \"properties\": {\n                            \"rating_score\": {\n                                \"type\": \"number\",\n                                \"minimum\": 0,\n                                \"maximum\": 5,\n                                \"description\": \"商品评分数（0-5分）\"\n                            },\n                            \"rating_count\": {\n                                \"type\": \"integer\",\n                                \"description\": \"商品评分数量\"\n                            }\n                        },\n                        \"required\": [\n                            \"rating_score\",\n                            \"rating_count\"\n                        ]\n                    },\n                    \"reviews_section\": {\n                        \"type\": \"object\",\n                        \"description\": \"评论区域信息\",\n                        \"properties\": {\n                            \"reviews\": {\n                                \"type\": \"array\",\n                                \"description\": \"商品评论列表\",\n                                \"items\": {\n                                    \"type\": \"object\",\n                                    \"properties\": {\n                                        \"review_content\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"评论内容\"\n                                        },\n                                        \"review_rating\": {\n                                            \"type\": \"number\",\n                                            \"minimum\": 0,\n                                            \"maximum\": 5,\n                                            \"description\": \"评论评分（0-5分）\"\n                                        },\n                                        \"review_time\": {\n                                            \"type\": \"string\",\n                                            \"description\": \"评论时间\"\n                                        }\n                                    },\n                                    \"required\": [\n                                        \"review_content\",\n                                        \"review_time\"\n                                    ]\n                                }\n                            },\n                            \"all_reviews_url\": {\n                                \"type\": \"string\",\n                                \"format\": \"uri\",\n                                \"description\": \"Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接\"\n                            }\n                        }\n                    }\n                },\n                \"required\": [\n                    \"product_basic_info\",\n                    \"category_info\",\n                    \"pricing_info\",\n                    \"media_info\",\n                    \"rating_info\"\n                ]\n            },\n            \"instructions\": \"请严格按照以下条件提取商品信息：\\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\\n2. 如果页面有多个商品，只提取最突出显示的主商品\\n3. 重点关注页面标题中提到的商品\\n4. 忽略广告推荐和次要商品信息\\n5. 确保提取的商品名称与页面URL或页面标题相匹配\\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\\n\\n需要提取的信息如下：\\n商品链接、商品名称、商品MLM-ID；\\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\\\\\":\\\\\\\"Full\\\\\\\"，商品有就是1，没有就是0)；\\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\\n商品销售商名称、商品销售商链接；\\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\\n商品评分数、商品评分数量、\\n商品的评论内容、评论评分、评论时间；\\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\\n任务开始的时间戳YYYYMMDDHHMMSS\",\n            \"validate_schema\": true,\n            \"return_raw\": false\n        },\n        \"content_processing\": {\n            \"word_count_threshold\": 200,\n            \"css_selector\": \"\",\n            \"target_elements\": [],\n            \"excluded_tags\": [\n                \"nav\",\n                \"footer\",\n                \"aside\"\n            ],\n            \"excluded_selector\": \"\",\n            \"remove_forms\": false,\n            \"only_text\": false,\n            \"prettify\": false,\n            \"parser_type\": \"lxml\",\n            \"keep_data_attributes\": false,\n            \"keep_attrs\": []\n        },\n        \"link_filtering\": {\n            \"exclude_external_links\": false,\n            \"exclude_internal_links\": false,\n            \"exclude_social_media_links\": false,\n            \"exclude_domains\": [\n                \"example.com\",\n                \"ads.google.com\",\n                \"facebook.com\",\n                \"twitter.com\"\n            ],\n            \"social_media_domains\": [\n                \"facebook.com\",\n                \"twitter.com\",\n                \"instagram.com\",\n                \"linkedin.com\",\n                \"youtube.com\",\n                \"tiktok.com\",\n                \"pinterest.com\",\n                \"reddit.com\"\n            ],\n            \"exclude_external_images\": false,\n            \"exclude_all_images\": false,\n            \"image_score_threshold\": 3,\n            \"image_description_min_word_threshold\": 50,\n            \"table_score_threshold\": 7\n        },\n        \"_config_summary\": {\n            \"enabled_features\": {\n                \"llm_extraction\": true,\n                \"css_extraction\": false,\n                \"schema_extraction\": true,\n                \"content_processing\": true,\n                \"link_filtering\": true\n            },\n            \"strategy_used\": \"LLMExtractionStrategy\"\n        },\n        \"task_type\": \"batch\",\n        \"semaphore_count\": 2,\n        \"stream\": true,\n        \"mean_delay\": 0.1,\n        \"max_range\": 3,\n        \"scheduler\": {\n            \"type\": \"AsyncQueueManager\",\n            \"pool_size\": 10,\n            \"memory_threshold\": 4096\n        },\n        \"monitor\": {\n            \"display_mode\": \"detailed\",\n            \"show_progress\": true,\n            \"log_errors\": true\n        }\n    }\n};\n\n    try {\n        const response = await fetch(url, {\n            method: 'POST',\n            headers: headers,\n            body: JSON.stringify(data)\n        });\n\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`HTTP ${response.status}: ${errorText}`);\n        }\n\n        const result = await response.json();\n        console.log('请求成功:', result);\n        return result;\n\n    } catch (error) {\n        console.error('请求失败:', error);\n        throw error;\n    }\n}\n\n// 调用API\ncallCrawl4aiAPI()\n    .then(result => {\n        console.log('API调用完成:', result);\n    })\n    .catch(error => {\n        console.error('API调用失败:', error);\n    });", "usage_notes": ["此请求配置与crawl4ai_api_tester.html实际发送的完全一致", "可直接复制request.body部分作为API请求体", "支持cURL、Python、JavaScript等多种调用方式", "任务类型: batch", "流式处理: 启用", "URL数量: 5"]}