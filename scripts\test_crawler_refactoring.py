#!/usr/bin/env python3
"""
爬虫重构验证测试脚本

验证重构后的爬虫API客户端功能是否正常工作。
"""

import asyncio
import sys
import os
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.crawler import CrawlerEngine, CrawlerAPIClient, APIConfig
from backend.crawler.api_client.api_exceptions import APIException
from config.crawler_api_config import get_crawler_api_config, get_crawler_engine_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_api_config():
    """测试API配置功能"""
    logger.info("🧪 测试API配置功能...")
    
    try:
        # 测试从环境变量创建配置
        config = APIConfig.from_env()
        logger.info(f"✅ 环境变量配置创建成功: {config.base_url}")
        
        # 测试配置验证
        config.validate()
        logger.info("✅ 配置验证通过")
        
        # 测试URL生成
        crawl_url = config.get_crawl_url()
        status_url = config.get_status_url("test-123")
        logger.info(f"✅ URL生成正常: {crawl_url}, {status_url}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ API配置测试失败: {e}")
        return False


async def test_api_client():
    """测试API客户端功能"""
    logger.info("🧪 测试API客户端功能...")
    
    try:
        config = get_crawler_api_config()
        
        # 测试客户端创建和生命周期
        async with CrawlerAPIClient(config) as client:
            logger.info("✅ API客户端创建成功")
            
            # 测试统计功能
            stats = client.get_stats()
            logger.info(f"✅ 统计功能正常: {stats}")
            
            # 测试健康检查 (如果API服务可用)
            try:
                health = await client.health_check()
                logger.info(f"✅ 健康检查: {'正常' if health else '服务不可用'}")
            except Exception as e:
                logger.warning(f"⚠️ 健康检查失败 (API服务可能未启动): {e}")
        
        logger.info("✅ API客户端生命周期管理正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ API客户端测试失败: {e}")
        return False


async def test_crawler_engine():
    """测试爬虫引擎功能"""
    logger.info("🧪 测试爬虫引擎功能...")
    
    try:
        config = get_crawler_engine_config()
        engine = CrawlerEngine(config)
        
        # 测试引擎启动
        await engine.start()
        logger.info("✅ 爬虫引擎启动成功")
        
        # 测试引擎状态
        status = await engine.get_engine_status()
        logger.info(f"✅ 引擎状态获取正常: 运行状态={status.get('is_running')}")
        
        # 测试引擎停止
        await engine.stop()
        logger.info("✅ 爬虫引擎停止成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 爬虫引擎测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理功能"""
    logger.info("🧪 测试错误处理功能...")
    
    try:
        # 创建一个无效的配置来测试错误处理
        invalid_config = APIConfig(
            base_url="http://invalid-url-that-does-not-exist.com",
            timeout=1.0,
            max_retries=1
        )
        
        async with CrawlerAPIClient(invalid_config) as client:
            try:
                # 这应该会失败
                await client.crawl_url("http://example.com/test")
                logger.warning("⚠️ 预期的错误没有发生")
                return False
            except APIException as e:
                logger.info(f"✅ 错误处理正常: {type(e).__name__}: {e}")
                return True
                
    except Exception as e:
        logger.error(f"❌ 错误处理测试失败: {e}")
        return False


async def test_configuration_loading():
    """测试配置加载功能"""
    logger.info("🧪 测试配置加载功能...")
    
    try:
        # 测试API配置加载
        api_config = get_crawler_api_config()
        logger.info(f"✅ API配置加载成功: {api_config.base_url}")
        
        # 测试引擎配置加载
        engine_config = get_crawler_engine_config()
        logger.info(f"✅ 引擎配置加载成功: 并发数={engine_config.get('max_concurrent_tasks')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置加载测试失败: {e}")
        return False


def print_test_summary(results: Dict[str, bool]):
    """打印测试总结"""
    logger.info("\n" + "="*60)
    logger.info("🎯 爬虫重构验证测试总结")
    logger.info("="*60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    failed_tests = total_tests - passed_tests
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info("-"*60)
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过: {passed_tests}")
    logger.info(f"失败: {failed_tests}")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if failed_tests == 0:
        logger.info("🎉 所有测试通过！爬虫重构验证成功！")
    else:
        logger.warning(f"⚠️ 有 {failed_tests} 个测试失败，请检查相关功能")
    
    logger.info("="*60)


async def main():
    """主测试函数"""
    logger.info("🚀 开始爬虫重构验证测试...")
    logger.info("="*60)
    
    # 定义测试用例
    test_cases = [
        ("API配置功能", test_api_config),
        ("API客户端功能", test_api_client),
        ("爬虫引擎功能", test_crawler_engine),
        ("错误处理功能", test_error_handling),
        ("配置加载功能", test_configuration_loading),
    ]
    
    # 执行测试
    results = {}
    for test_name, test_func in test_cases:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"❌ {test_name} 执行异常: {e}")
            results[test_name] = False
        
        logger.info("-"*40)
    
    # 打印测试总结
    print_test_summary(results)
    
    # 返回退出码
    return 0 if all(results.values()) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        sys.exit(1)
