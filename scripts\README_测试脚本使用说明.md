# Task 02 测试脚本使用说明

## 📋 可用测试脚本

### 1. 🚀 快速测试 (推荐)
```bash
scripts\test_task02_quick.bat
```
**用途**: 快速验证核心功能是否正常
**时间**: 约2-3分钟  
**包含测试**:
- ✅ 核心文件完整性检查
- ✅ Docker环境启动
- ✅ 数据库连接测试
- ✅ 模型导入验证
- ✅ API健康检查

### 2. 🧪 完整测试
```bash
scripts\test_task02_complete.bat
```
**用途**: 全面的功能验证和压力测试
**时间**: 约5-8分钟
**包含测试**:
- 📋 基础环境验证
- 🐳 Docker环境启动
- 🔗 数据库连接测试
- 📦 模型导入测试
- 🗃️ 完整数据库功能测试 (CRUD操作)
- 🌐 API服务测试
- 📊 详细测试报告生成

### 3. 🔍 原始验证脚本
```bash
scripts\verify_task02.bat
```
**用途**: 基础的文件和环境检查
**时间**: 约1-2分钟

## 🎯 推荐使用流程

### 首次测试 (新环境)
```bash
# 1. 先运行快速测试确认基础功能
scripts\test_task02_quick.bat

# 2. 如果快速测试通过，运行完整测试
scripts\test_task02_complete.bat
```

### 日常开发测试
```bash
# 代码修改后快速验证
scripts\test_task02_quick.bat
```

### 发布前验证
```bash
# 发布前完整验证
scripts\test_task02_complete.bat
```

## 📊 测试结果说明

### 成功情况
```
🎉 Task 02 核心功能测试全部通过！
✅ 验证完成:
  • 数据库模型文件完整
  • TimescaleDB连接正常
  • 核心模型导入成功
  • API服务正常启动

🚀 Task 02 状态: 就绪
✅ 可以开始 Task 03 开发
```

### 失败情况处理
根据错误提示进行相应处理：

**Docker启动失败**:
```bash
# 检查Docker服务
docker --version
# 重启Docker Desktop

# 清理旧容器
docker-compose -f docker-compose.dev.yml down
docker system prune -f
```

**Conda环境问题**:
```bash
# 重新创建环境
conda env remove -n monit-crawler
conda env create -f environment.yml
```

**数据库连接失败**:
```bash
# 检查容器状态
docker ps
# 重启数据库容器
docker-compose -f docker-compose.dev.yml restart timescaledb
```

**模型导入失败**:
```bash
# 检查依赖
conda activate monit-crawler
pip install -r backend/requirements.txt
```

## 🔧 故障排除

### 端口冲突
如果端口被占用，可以修改测试脚本中的端口号:
- 快速测试: 端口 8002
- 完整测试: 端口 8001
- 主开发环境: 端口 8000

### 权限问题
在管理员模式下运行 CMD 或 PowerShell

### 网络问题
确保防火墙允许Docker和conda访问网络

## 📝 日志文件

完整测试会生成详细日志文件:
```
test_task02_YYYYMMDD_HHMMSS.log
```

可以查看此文件获取详细的错误信息和调试信息。

## ⚡ 快速命令

```bash
# 一键启动开发环境
scripts\start_dev.bat

# 快速测试Task 02
scripts\test_task02_quick.bat

# 查看Docker状态
docker ps

# 清理环境
docker-compose -f docker-compose.dev.yml down
``` 