import React from 'react';
import {
  Modal,
  Descriptions,
  Tag,
  Space,
  Typography,
  Progress,
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Tooltip,
  Divider
} from 'antd';
import {
  LinkOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import type { UrlPoolItem } from '../../types/urlPool';
import { getPlatformDisplayName, getStatusDisplay } from '../../services/urlPoolApi';

const { Text, Paragraph } = Typography;

interface UrlDetailModalProps {
  visible: boolean;
  url: UrlPoolItem | null;
  onClose: () => void;
  onEdit?: (url: UrlPoolItem) => void;
  onDelete?: (url: UrlPoolItem) => void;
}

const UrlDetailModal: React.FC<UrlDetailModalProps> = ({
  visible,
  url,
  onClose,
  onEdit,
  onDelete
}) => {
  if (!url) return null;

  const { name: statusName, color: statusColor } = getStatusDisplay(url.status);
  const successRate = url.check_count > 0 ? (url.success_count / url.check_count) * 100 : 0;

  const renderBasicInfo = () => (
    <Card size="small" title="基本信息" style={{ marginBottom: 16 }}>
      <Descriptions column={1} size="small">
        <Descriptions.Item label="URL">
          <Paragraph
            copyable
            style={{ 
              fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
              fontSize: 12,
              margin: 0,
              wordBreak: 'break-all'
            }}
          >
            {url.url}
          </Paragraph>
        </Descriptions.Item>
        
        <Descriptions.Item label="平台">
          <Tag color={url.platform === 'mercadolibre' ? 'gold' : url.platform === 'amazon' ? 'orange' : 'blue'}>
            {getPlatformDisplayName(url.platform)}
          </Tag>
        </Descriptions.Item>
        
        <Descriptions.Item label="状态">
          <Tag color={statusColor}>{statusName}</Tag>
        </Descriptions.Item>
        
        <Descriptions.Item label="来源文件">
          <Space>
            <FileTextOutlined />
            <Text>{url.source_file}</Text>
          </Space>
        </Descriptions.Item>
        
        <Descriptions.Item label="添加时间">
          <Space>
            <CalendarOutlined />
            <Text>{new Date(url.added_at).toLocaleString('zh-CN')}</Text>
          </Space>
        </Descriptions.Item>
        
        {url.last_check && (
          <Descriptions.Item label="最后检查">
            <Space>
              <CalendarOutlined />
              <Text>{new Date(url.last_check).toLocaleString('zh-CN')}</Text>
            </Space>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );

  const renderStatistics = () => (
    <Card size="small" title="检查统计" style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <Statistic
            title="总检查次数"
            value={url.check_count}
            prefix={<BarChartOutlined />}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="成功次数"
            value={url.success_count}
            valueStyle={{ color: '#3f8600' }}
            prefix={<CheckCircleOutlined />}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="失败次数"
            value={url.error_count}
            valueStyle={{ color: '#cf1322' }}
            prefix={<ExclamationCircleOutlined />}
          />
        </Col>
      </Row>
      
      <Divider />
      
      <div>
        <Text strong>成功率</Text>
        <Progress
          percent={Math.round(successRate)}
          status={successRate >= 80 ? 'success' : successRate >= 60 ? 'normal' : 'exception'}
          style={{ marginTop: 8 }}
        />
        <Text type="secondary" style={{ fontSize: 12 }}>
          {url.check_count > 0 ? `${url.success_count}/${url.check_count} 次成功` : '暂无检查记录'}
        </Text>
      </div>
    </Card>
  );

  const renderMetadata = () => {
    if (!url.metadata || Object.keys(url.metadata).length === 0) {
      return null;
    }

    return (
      <Card size="small" title="元数据" style={{ marginBottom: 16 }}>
        <Descriptions column={1} size="small">
          {url.metadata.original_row && (
            <Descriptions.Item label="Excel原始行号">
              {url.metadata.original_row}
            </Descriptions.Item>
          )}
          {url.metadata.original_column && (
            <Descriptions.Item label="Excel原始列">
              {url.metadata.original_column}
            </Descriptions.Item>
          )}
          {url.metadata.detected_platform && (
            <Descriptions.Item label="检测到的平台">
              {getPlatformDisplayName(url.metadata.detected_platform)}
            </Descriptions.Item>
          )}
          {url.metadata.url_hash && (
            <Descriptions.Item label="URL哈希">
              <Text code style={{ fontSize: 11 }}>
                {url.metadata.url_hash}
              </Text>
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>
    );
  };

  const renderActions = () => (
    <Space>
      <Button
        type="primary"
        icon={<LinkOutlined />}
        onClick={() => window.open(url.url, '_blank')}
      >
        访问URL
      </Button>
      
      {onEdit && (
        <Button onClick={() => onEdit(url)}>
          编辑
        </Button>
      )}
      
      {onDelete && (
        <Button 
          danger 
          onClick={() => onDelete(url)}
        >
          删除
        </Button>
      )}
    </Space>
  );

  return (
    <Modal
      title={
        <Space>
          <LinkOutlined />
          URL详情
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        ...renderActions().props.children
      ]}
      width={800}
      style={{ top: 20 }}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {renderBasicInfo()}
        {renderStatistics()}
        {renderMetadata()}
      </div>
    </Modal>
  );
};

export default UrlDetailModal;
