#!/usr/bin/env python3
"""
测试前端修复
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_crawler_pool_page_data():
    """测试爬虫池页面数据结构"""
    print("🔧 测试爬虫池页面数据结构...")
    
    try:
        # 获取爬虫池列表
        response = requests.get(f"{BASE_URL}/api/v1/crawler/pools")
        if response.status_code == 200:
            pools = response.json()
            print(f"✅ 获取爬虫池列表成功: {len(pools)} 个池")
            
            for pool in pools:
                print(f"\n池: {pool['pool_name']}")
                print(f"ID: {pool['pool_id']}")
                
                # 测试前端需要的字段
                has_new_arch = 'crawler_config_ids' in pool and pool['crawler_config_ids']
                has_old_arch = 'backends' in pool and pool['backends']
                
                if has_new_arch:
                    print(f"✅ 新架构 - 配置数量: {len(pool['crawler_config_ids'])}")
                    print(f"   配置IDs: {pool['crawler_config_ids']}")
                elif has_old_arch:
                    print(f"✅ 旧架构 - 后端数量: {len(pool['backends'])}")
                else:
                    print(f"⚠️  空池或未知架构")
                
                # 测试前端安全访问逻辑
                safe_count = len(pool.get('crawler_config_ids', [])) or len(pool.get('backends', [])) or 0
                print(f"   前端安全计算: {safe_count}")
                
                # 测试负载均衡策略
                print(f"   负载均衡: {pool.get('load_balance_strategy', 'unknown')}")
                
            return pools
        else:
            print(f"❌ 获取爬虫池列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return []

def test_new_architecture_pool_creation():
    """测试新架构爬虫池创建"""
    print("\n🏗️ 测试新架构爬虫池创建...")
    
    # 首先获取可用的爬虫配置
    try:
        response = requests.get(f"{BASE_URL}/api/v1/crawler/instances")
        if response.status_code == 200:
            configs = response.json()
            if not configs:
                print("❌ 没有可用的爬虫配置，无法测试池创建")
                return None
            
            config_ids = [config['config_id'] for config in configs]
            print(f"✅ 找到 {len(configs)} 个可用配置: {config_ids}")
            
            # 创建新架构的爬虫池
            pool_data = {
                "pool_name": "前端修复测试池",
                "description": "用于测试前端修复的爬虫池",
                "crawler_config_ids": config_ids[:1],  # 使用第一个配置
                "load_balance_strategy": "round_robin",
                "health_check_interval": 60,
                "failure_threshold": 3,
                "recovery_threshold": 2
            }
            
            response = requests.post(f"{BASE_URL}/api/v1/crawler/pools", json=pool_data)
            if response.status_code == 200:
                new_pool = response.json()
                print(f"✅ 创建新架构爬虫池成功: {new_pool['pool_name']}")
                print(f"   池ID: {new_pool['pool_id']}")
                print(f"   配置数量: {len(new_pool['crawler_config_ids'])}")
                return new_pool['pool_id']
            else:
                print(f"❌ 创建爬虫池失败: {response.status_code} - {response.text}")
                return None
        else:
            print(f"❌ 获取爬虫配置失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def test_frontend_compatibility_scenarios():
    """测试前端兼容性场景"""
    print("\n🖥️ 测试前端兼容性场景...")
    
    # 模拟各种可能的数据结构
    test_scenarios = [
        {
            "name": "新架构池",
            "data": {
                "pool_id": "new-arch-test",
                "pool_name": "新架构测试池",
                "crawler_config_ids": ["config1", "config2", "config3"],
                "load_balance_strategy": "weighted_round_robin"
            }
        },
        {
            "name": "旧架构池",
            "data": {
                "pool_id": "old-arch-test",
                "pool_name": "旧架构测试池",
                "backends": [
                    {"id": "b1", "name": "后端1"},
                    {"id": "b2", "name": "后端2"}
                ],
                "load_balance_strategy": "round_robin"
            }
        },
        {
            "name": "空池",
            "data": {
                "pool_id": "empty-test",
                "pool_name": "空池测试",
                "load_balance_strategy": "health_based"
            }
        },
        {
            "name": "混合数据",
            "data": {
                "pool_id": "mixed-test",
                "pool_name": "混合数据测试",
                "crawler_config_ids": [],
                "backends": [],
                "load_balance_strategy": "least_connections"
            }
        }
    ]
    
    print("测试前端数据处理逻辑:")
    
    for scenario in test_scenarios:
        print(f"\n场景: {scenario['name']}")
        data = scenario['data']
        
        # 模拟前端的配置数量计算（修复后的逻辑）
        config_count = len(data.get('crawler_config_ids', []))
        backend_count = len(data.get('backends', []))
        safe_count = config_count or backend_count or 0
        
        print(f"   crawler_config_ids: {config_count}")
        print(f"   backends: {backend_count}")
        print(f"   安全计算结果: {safe_count}")
        
        # 模拟前端的架构检测
        is_new_arch = bool(data.get('crawler_config_ids'))
        is_old_arch = bool(data.get('backends'))
        
        if is_new_arch and config_count > 0:
            print(f"   ✅ 检测为新架构，配置数量: {config_count}")
        elif is_old_arch and backend_count > 0:
            print(f"   ✅ 检测为旧架构，后端数量: {backend_count}")
        else:
            print(f"   ⚠️  空池或未知架构")
        
        # 模拟前端的编辑页面路由选择
        if data.get('crawler_config_ids') is not None:
            edit_route = f"/crawler-pool/{data['pool_id']}/edit-new"
            print(f"   编辑路由: {edit_route} (新架构)")
        else:
            edit_route = f"/crawler-pool/{data['pool_id']}/edit"
            print(f"   编辑路由: {edit_route} (旧架构)")

def main():
    """主测试流程"""
    print("🚀 开始测试前端修复...")
    print("=" * 60)
    
    # 1. 测试爬虫池页面数据
    pools = test_crawler_pool_page_data()
    
    # 2. 测试新架构池创建
    new_pool_id = test_new_architecture_pool_creation()
    
    # 3. 测试前端兼容性
    test_frontend_compatibility_scenarios()
    
    print("\n" + "=" * 60)
    print("🎉 前端修复测试完成！")
    
    print("\n📋 修复总结:")
    print("✅ 修复了 'Cannot read properties of undefined (reading length)' 错误")
    print("✅ 添加了新旧架构兼容性支持")
    print("✅ 更新了TypeScript类型定义")
    print("✅ 添加了安全的数组访问")
    print("✅ 创建了新架构专用的编辑页面")
    print("✅ 实现了智能路由选择")
    
    print("\n🔗 前端页面:")
    print("   爬虫池管理: http://localhost:3000/crawler-pool")
    print("   新架构创建: http://localhost:3000/crawler-pool/create-new")
    print("   配置管理: http://localhost:3000/crawler-settings/instance")
    print("   监控仪表板: http://localhost:3000/crawler-settings/monitor")
    
    print("\n💡 主要修复:")
    print("   1. 使用 pool.crawler_config_ids?.length || pool.backends?.length || 0")
    print("   2. 添加 CrawlerPoolConfig 接口的可选字段支持")
    print("   3. 创建 CrawlerPoolEditNew 组件处理新架构")
    print("   4. 智能路由：根据数据结构选择编辑页面")
    print("   5. 完整的错误防护和类型安全")
    
    if new_pool_id:
        print(f"\n🎯 测试池ID: {new_pool_id}")
        print("   可以在前端界面中查看和编辑这个测试池")

if __name__ == "__main__":
    main()
