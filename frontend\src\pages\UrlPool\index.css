/* URL池管理页面样式 */

.url-pool-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.url-pool-stats {
  margin-bottom: 16px;
}

.url-pool-stats .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.url-pool-toolbar {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.url-pool-filters {
  margin-bottom: 16px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.url-pool-filters .ant-card-body {
  padding: 16px;
}

.url-pool-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.url-pool-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.url-pool-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* URL显示样式 */
.url-cell {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  word-break: break-all;
  line-height: 1.4;
}

.url-cell-tooltip {
  max-width: 500px;
  word-break: break-all;
}

/* 平台标签样式 */
.platform-tag {
  font-weight: 500;
  border-radius: 4px;
}

.platform-tag.mercadolibre {
  background-color: #fff7e6;
  border-color: #ffd666;
  color: #d48806;
}

.platform-tag.amazon {
  background-color: #fff2e8;
  border-color: #ffbb96;
  color: #d4380d;
}

.platform-tag.ebay {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #096dd9;
}

.platform-tag.unknown {
  background-color: #f6f6f6;
  border-color: #d9d9d9;
  color: #666666;
}

/* 状态标签样式 */
.status-tag {
  font-weight: 500;
  border-radius: 4px;
}

.status-tag.active {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #389e0d;
}

.status-tag.disabled {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #cf1322;
}

/* 统计信息样式 */
.stats-cell {
  font-size: 12px;
  line-height: 1.2;
}

.stats-progress {
  margin-top: 4px;
}

/* 批量操作栏样式 */
.batch-actions-alert {
  margin-bottom: 16px;
  border-radius: 6px;
}

.batch-actions-alert .ant-alert-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 筛选器样式 */
.filter-item {
  margin-bottom: 8px;
}

.filter-item .ant-typography {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #262626;
}

/* 搜索框样式 */
.search-input {
  border-radius: 6px;
}

.search-input .ant-input {
  border-radius: 6px 0 0 6px;
}

.search-input .ant-btn {
  border-radius: 0 6px 6px 0;
}

/* 操作按钮样式 */
.action-button {
  border-radius: 6px;
  font-weight: 500;
}

.action-button.primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.action-button.danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  border: none;
}

/* 表格行选择样式 */
.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #bae7ff;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination .ant-pagination-item {
  border-radius: 4px;
}

.ant-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
}

/* 加载状态样式 */
.loading-overlay {
  position: relative;
}

.loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #999;
}

.empty-state .ant-empty-image {
  margin-bottom: 16px;
}

.empty-state .ant-empty-description {
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .url-pool-page {
    padding: 16px;
  }
  
  .url-pool-toolbar {
    padding: 12px;
  }
  
  .url-pool-stats .ant-col {
    margin-bottom: 8px;
  }
  
  .filter-item {
    margin-bottom: 12px;
  }
  
  .batch-actions-alert .ant-alert-message {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .url-cell {
    font-size: 11px;
  }
}

@media (max-width: 576px) {
  .url-pool-page {
    padding: 12px;
  }
  
  .url-pool-stats .ant-col {
    span: 12;
  }
  
  .filter-item .ant-col {
    span: 24;
    margin-bottom: 8px;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 4px;
    font-size: 11px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-down {
  animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高亮效果 */
.highlight {
  background-color: #fff2e8;
  border: 1px solid #ffbb96;
  border-radius: 4px;
  padding: 2px 4px;
  animation: highlight 2s ease-in-out;
}

@keyframes highlight {
  0% {
    background-color: #fff2e8;
  }
  50% {
    background-color: #ffd591;
  }
  100% {
    background-color: #fff2e8;
  }
}
