#!/usr/bin/env python3
"""
持久化TaskManager测试脚本

测试持久化功能：
1. 生成测试任务
2. 验证Redis存储
3. 模拟重启恢复
4. 对比数据一致性
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis

from backend.app.core.persistent_task_manager import PersistentTaskManager
from backend.app.core.task_manager import TaskManagerConfig
from backend.app.core.task_splitter import SplitterConfig, TaskPriority
from backend.app.core.rate_limiter import RateLimitConfig
from backend.app.core.retry_manager import RetryConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class PersistenceTestSuite:
    """持久化测试套件"""
    
    def __init__(self):
        self.redis_url = "redis://localhost:6379/0"
        self.redis_client = None
        self.task_manager = None
        
        # 测试数据
        self.test_urls = [
            f"https://mercadolibre.com/product/{i}" 
            for i in range(1, 101)  # 100个测试URL
        ]
        
        # 测试结果
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up test environment...")
        
        # 连接Redis
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        logger.info("✅ Connected to Redis")
        
        # 清理测试数据
        await self._cleanup_test_data()
        
        # 创建TaskManager配置
        config = TaskManagerConfig(
            max_concurrent_batches=2,
            batch_check_interval=2.0,
            splitter_config=SplitterConfig(
                max_batch_size=20,
                min_batch_size=10,
                adaptive_sizing=True
            ),
            rate_limiter_config=RateLimitConfig(
                max_concurrent_requests=2,
                requests_per_minute=60,
                redis_url=self.redis_url
            ),
            retry_config=RetryConfig(
                max_retries=3,
                base_delay=5.0
            )
        )
        
        # 创建持久化TaskManager
        self.task_manager = PersistentTaskManager(config, self.redis_url)
        
        logger.info("✅ Test environment setup completed")
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 Cleaning up test environment...")
        
        if self.task_manager:
            await self.task_manager.stop()
        
        if self.redis_client:
            await self._cleanup_test_data()
            await self.redis_client.close()
        
        logger.info("✅ Test environment cleaned up")
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        test_keys = [
            "taskmanager:pending_batches",
            "taskmanager:running_batches", 
            "taskmanager:completed_batches",
            "taskmanager:failed_batches",
            "taskmanager:state"
        ]
        
        for key in test_keys:
            await self.redis_client.delete(key)
    
    async def test_task_submission_and_persistence(self):
        """测试1: 任务提交和持久化"""
        logger.info("\n📤 Test 1: Task Submission and Persistence")
        logger.info("=" * 60)
        
        try:
            # 启动TaskManager
            await self.task_manager.start()
            logger.info("✅ TaskManager started")
            
            # 提交测试任务
            submission_ids = []
            
            # 提交3个不同优先级的任务
            priorities = [TaskPriority.URGENT, TaskPriority.NORMAL, TaskPriority.LOW]
            
            for i, priority in enumerate(priorities):
                start_idx = i * 30
                end_idx = start_idx + 30
                task_urls = self.test_urls[start_idx:end_idx]
                
                submission_id = await self.task_manager.submit_task(
                    task_id=i + 1,
                    urls=task_urls,
                    platform="mercadolibre",
                    priority=priority,
                    options={"test_task": True, "batch_index": i}
                )
                
                submission_ids.append(submission_id)
                logger.info(f"✅ Submitted task {i+1} ({priority.name}): {len(task_urls)} URLs")
            
            # 等待一段时间让任务处理
            await asyncio.sleep(3)
            
            # 检查内存状态
            memory_state = await self._get_memory_state()
            logger.info(f"📊 Memory state: {memory_state}")
            
            # 检查Redis持久化状态
            redis_state = await self._get_redis_state()
            logger.info(f"💾 Redis state: {redis_state}")
            
            # 验证数据一致性
            consistency_check = await self._verify_consistency(memory_state, redis_state)
            
            self.test_results["task_submission"] = {
                "success": True,
                "submission_ids": submission_ids,
                "memory_state": memory_state,
                "redis_state": redis_state,
                "consistency": consistency_check
            }
            
            logger.info("✅ Task submission and persistence test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Task submission test failed: {e}")
            self.test_results["task_submission"] = {"success": False, "error": str(e)}
            return False
    
    async def test_restart_recovery(self):
        """测试2: 重启恢复功能"""
        logger.info("\n🔄 Test 2: Restart Recovery")
        logger.info("=" * 60)
        
        try:
            # 获取重启前的状态
            before_restart = await self._get_redis_state()
            logger.info(f"📊 State before restart: {before_restart}")
            
            # 停止TaskManager (模拟重启)
            await self.task_manager.stop()
            logger.info("🛑 TaskManager stopped (simulating restart)")
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 创建新的TaskManager实例 (模拟重启)
            config = self.task_manager.config
            new_task_manager = PersistentTaskManager(config, self.redis_url)
            
            # 启动新实例 (应该自动恢复状态)
            await new_task_manager.start()
            logger.info("🚀 New TaskManager started (simulating restart)")
            
            # 等待恢复完成
            await asyncio.sleep(3)
            
            # 获取恢复后的状态
            after_restart = await self._get_memory_state_from_manager(new_task_manager)
            logger.info(f"📊 State after restart: {after_restart}")
            
            # 验证恢复一致性
            recovery_check = await self._verify_recovery(before_restart, after_restart)
            
            # 更新task_manager引用
            self.task_manager = new_task_manager
            
            self.test_results["restart_recovery"] = {
                "success": True,
                "before_restart": before_restart,
                "after_restart": after_restart,
                "recovery_check": recovery_check
            }
            
            logger.info("✅ Restart recovery test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Restart recovery test failed: {e}")
            self.test_results["restart_recovery"] = {"success": False, "error": str(e)}
            return False
    
    async def test_redis_data_integrity(self):
        """测试3: Redis数据完整性"""
        logger.info("\n🔍 Test 3: Redis Data Integrity")
        logger.info("=" * 60)
        
        try:
            # 直接检查Redis中的数据结构
            redis_data = await self._inspect_redis_data()
            
            # 验证数据格式
            format_check = await self._verify_data_format(redis_data)
            
            # 验证数据完整性
            integrity_check = await self._verify_data_integrity(redis_data)
            
            self.test_results["redis_integrity"] = {
                "success": True,
                "redis_data": redis_data,
                "format_check": format_check,
                "integrity_check": integrity_check
            }
            
            logger.info("✅ Redis data integrity test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Redis data integrity test failed: {e}")
            self.test_results["redis_integrity"] = {"success": False, "error": str(e)}
            return False
    
    async def _get_memory_state(self) -> Dict[str, Any]:
        """获取内存状态"""
        return {
            "pending_count": len(self.task_manager.pending_batches),
            "running_count": len(self.task_manager.running_batches),
            "completed_count": len(self.task_manager.completed_batches),
            "failed_count": len(self.task_manager.failed_batches),
            "pending_batch_ids": [batch.batch_id for batch in self.task_manager.pending_batches],
            "running_batch_ids": list(self.task_manager.running_batches.keys()),
            "completed_batch_ids": self.task_manager.completed_batches[:5],  # 只显示前5个
            "failed_batch_ids": self.task_manager.failed_batches[:5]
        }
    
    async def _get_memory_state_from_manager(self, manager) -> Dict[str, Any]:
        """从指定manager获取内存状态"""
        return {
            "pending_count": len(manager.pending_batches),
            "running_count": len(manager.running_batches),
            "completed_count": len(manager.completed_batches),
            "failed_count": len(manager.failed_batches),
            "pending_batch_ids": [batch.batch_id for batch in manager.pending_batches],
            "running_batch_ids": list(manager.running_batches.keys()),
            "completed_batch_ids": manager.completed_batches[:5],
            "failed_batch_ids": manager.failed_batches[:5]
        }
    
    async def _get_redis_state(self) -> Dict[str, Any]:
        """获取Redis状态"""
        return {
            "pending_count": await self.redis_client.llen("taskmanager:pending_batches"),
            "running_count": await self.redis_client.hlen("taskmanager:running_batches"),
            "completed_count": await self.redis_client.scard("taskmanager:completed_batches"),
            "failed_count": await self.redis_client.scard("taskmanager:failed_batches"),
            "pending_sample": await self._get_pending_sample(),
            "running_sample": await self._get_running_sample(),
            "completed_sample": await self._get_completed_sample(),
            "failed_sample": await self._get_failed_sample()
        }
    
    async def _get_pending_sample(self) -> List[str]:
        """获取待处理队列样本"""
        try:
            samples = await self.redis_client.lrange("taskmanager:pending_batches", 0, 2)
            return [json.loads(sample)["batch_id"] for sample in samples]
        except:
            return []
    
    async def _get_running_sample(self) -> List[str]:
        """获取运行队列样本"""
        try:
            samples = await self.redis_client.hkeys("taskmanager:running_batches")
            return [key.decode() for key in samples[:3]]
        except:
            return []
    
    async def _get_completed_sample(self) -> List[str]:
        """获取完成队列样本"""
        try:
            samples = await self.redis_client.srandmember("taskmanager:completed_batches", 3)
            return [sample.decode() for sample in samples] if samples else []
        except:
            return []
    
    async def _get_failed_sample(self) -> List[str]:
        """获取失败队列样本"""
        try:
            samples = await self.redis_client.srandmember("taskmanager:failed_batches", 3)
            return [sample.decode() for sample in samples] if samples else []
        except:
            return []
    
    async def _verify_consistency(self, memory_state: Dict, redis_state: Dict) -> Dict[str, bool]:
        """验证内存和Redis状态一致性"""
        return {
            "pending_count_match": memory_state["pending_count"] == redis_state["pending_count"],
            "running_count_match": memory_state["running_count"] == redis_state["running_count"],
            "completed_count_match": memory_state["completed_count"] == redis_state["completed_count"],
            "failed_count_match": memory_state["failed_count"] == redis_state["failed_count"],
            "pending_ids_match": set(memory_state["pending_batch_ids"]) == set(redis_state["pending_sample"]),
            "running_ids_match": set(memory_state["running_batch_ids"]) == set(redis_state["running_sample"])
        }
    
    async def _verify_recovery(self, before: Dict, after: Dict) -> Dict[str, bool]:
        """验证重启恢复一致性"""
        return {
            "pending_count_recovered": before["pending_count"] == after["pending_count"],
            "running_count_recovered": before["running_count"] == after["running_count"],
            "completed_count_recovered": before["completed_count"] == after["completed_count"],
            "failed_count_recovered": before["failed_count"] == after["failed_count"]
        }
    
    async def _inspect_redis_data(self) -> Dict[str, Any]:
        """检查Redis中的原始数据"""
        data = {}
        
        # 检查待处理队列
        pending_raw = await self.redis_client.lrange("taskmanager:pending_batches", 0, 1)
        if pending_raw:
            data["pending_sample"] = json.loads(pending_raw[0])
        
        # 检查运行哈希
        running_raw = await self.redis_client.hgetall("taskmanager:running_batches")
        if running_raw:
            first_key = list(running_raw.keys())[0]
            data["running_sample"] = json.loads(running_raw[first_key])
        
        return data
    
    async def _verify_data_format(self, redis_data: Dict) -> Dict[str, bool]:
        """验证数据格式"""
        checks = {}
        
        # 检查待处理批次格式
        if "pending_sample" in redis_data:
            sample = redis_data["pending_sample"]
            checks["pending_has_batch_id"] = "batch_id" in sample
            checks["pending_has_urls"] = "urls" in sample
            checks["pending_has_priority"] = "priority" in sample
            checks["pending_has_metadata"] = "metadata" in sample
        
        # 检查运行批次格式
        if "running_sample" in redis_data:
            sample = redis_data["running_sample"]
            checks["running_has_batch_id"] = "batch_id" in sample
            checks["running_has_celery_task_id"] = "celery_task_id" in sample
            checks["running_has_started_at"] = "started_at" in sample
        
        return checks
    
    async def _verify_data_integrity(self, redis_data: Dict) -> Dict[str, bool]:
        """验证数据完整性"""
        checks = {}
        
        # 检查数据类型
        if "pending_sample" in redis_data:
            sample = redis_data["pending_sample"]
            checks["pending_urls_is_list"] = isinstance(sample.get("urls"), list)
            checks["pending_metadata_is_dict"] = isinstance(sample.get("metadata"), dict)
        
        if "running_sample" in redis_data:
            sample = redis_data["running_sample"]
            checks["running_batch_id_is_string"] = isinstance(sample.get("batch_id"), str)
            checks["running_celery_task_id_is_string"] = isinstance(sample.get("celery_task_id"), str)
        
        return checks
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 PERSISTENT TASK MANAGER TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        logger.info("\n📋 Test Details:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            logger.info(f"  {status} {test_name}")
            
            if not result.get("success", False) and "error" in result:
                logger.info(f"    Error: {result['error']}")
        
        # 详细结果
        if "task_submission" in self.test_results and self.test_results["task_submission"]["success"]:
            consistency = self.test_results["task_submission"]["consistency"]
            logger.info(f"\n🔍 Consistency Checks:")
            for check, passed in consistency.items():
                status = "✅" if passed else "❌"
                logger.info(f"  {status} {check}")
        
        if "restart_recovery" in self.test_results and self.test_results["restart_recovery"]["success"]:
            recovery = self.test_results["restart_recovery"]["recovery_check"]
            logger.info(f"\n🔄 Recovery Checks:")
            for check, passed in recovery.items():
                status = "✅" if passed else "❌"
                logger.info(f"  {status} {check}")


async def main():
    """主测试函数"""
    print("🧪 Persistent TaskManager Test Suite")
    print("=" * 60)
    
    test_suite = PersistenceTestSuite()
    
    try:
        # 设置测试环境
        await test_suite.setup()
        
        # 运行测试
        test1_success = await test_suite.test_task_submission_and_persistence()
        test2_success = await test_suite.test_restart_recovery()
        test3_success = await test_suite.test_redis_data_integrity()
        
        # 打印测试总结
        test_suite.print_test_summary()
        
        # 总体结果
        all_passed = test1_success and test2_success and test3_success
        
        if all_passed:
            logger.info("\n🎉 ALL TESTS PASSED! Persistent TaskManager is working correctly!")
        else:
            logger.info("\n⚠️ Some tests failed. Please check the details above.")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试环境
        await test_suite.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
