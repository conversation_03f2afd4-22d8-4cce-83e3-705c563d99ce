# Redis配置文件 - MonIt生产环境
# 针对TaskManager持久化优化的Redis配置

################################## NETWORK #####################################

# 绑定地址
bind 127.0.0.1 ::1

# 端口
port 6379

# TCP listen() backlog
tcp-backlog 511

# 客户端超时时间 (0 = 禁用)
timeout 0

# TCP keepalive
tcp-keepalive 300

################################# TLS/SSL ######################################

# TLS/SSL配置 (生产环境建议启用)
# port 0
# tls-port 6380
# tls-cert-file redis.crt
# tls-key-file redis.key

################################# GENERAL #####################################

# 以守护进程方式运行
daemonize yes

# PID文件
pidfile /var/run/redis_6379.pid

# 日志级别: debug, verbose, notice, warning
loglevel notice

# 日志文件
logfile /var/log/redis/redis-server.log

# 数据库数量
databases 16

# 显示Redis logo
always-show-logo no

# 设置服务器verbosity为'verbose'时，记录查询
set-proc-title yes
proc-title-template "{title} {listen-addr} {server-mode}"

################################ SNAPSHOTTING  ################################

# RDB持久化配置 - 针对TaskManager优化
# 格式: save <seconds> <changes>

# 15分钟内至少1个key变化时保存
save 900 1

# 5分钟内至少10个key变化时保存  
save 300 10

# 1分钟内至少10000个key变化时保存
save 60 10000

# 当RDB持久化出现错误时，是否停止接受写入
stop-writes-on-bgsave-error yes

# 是否压缩RDB文件
rdbcompression yes

# 是否校验RDB文件
rdbchecksum yes

# RDB文件名
dbfilename dump.rdb

# RDB和AOF文件存储目录
dir /var/lib/redis

################################# REPLICATION #################################

# 主从复制配置 (如果需要)
# replicaof <masterip> <masterport>

# 从服务器只读
replica-read-only yes

# 复制超时时间
repl-timeout 60

################################## SECURITY ###################################

# 密码认证 (生产环境强烈建议设置)
# requirepass your_strong_password_here

# 重命名危险命令
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG "CONFIG_9a8b7c6d5e4f3g2h1i"

################################### CLIENTS ####################################

# 最大客户端连接数
maxclients 10000

############################## MEMORY MANAGEMENT #############################

# 最大内存使用量 (建议设置为系统内存的75%)
maxmemory 2gb

# 内存淘汰策略 - 针对TaskManager队列优化
# allkeys-lru: 删除最近最少使用的key
# volatile-lru: 删除设置了过期时间的最近最少使用的key
# allkeys-lfu: 删除最少使用频率的key
# volatile-lfu: 删除设置了过期时间的最少使用频率的key
# allkeys-random: 随机删除key
# volatile-random: 随机删除设置了过期时间的key
# volatile-ttl: 删除即将过期的key
# noeviction: 不删除，返回错误

# 对于TaskManager，使用volatile-lru确保持久化数据不被误删
maxmemory-policy volatile-lru

# 内存采样精度
maxmemory-samples 5

# 副本忽略最大内存设置
replica-ignore-maxmemory yes

############################# LAZY FREEING ####################################

# 惰性删除配置
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# 惰性用户删除
lazyfree-lazy-user-del no

# 惰性用户刷新
lazyfree-lazy-user-flush no

################################ THREADED I/O ###############################

# I/O线程数 (建议设置为CPU核心数)
io-threads 4

# 启用I/O线程进行写操作
io-threads-do-reads yes

############################ KERNEL OOM CONTROL ##############################

# OOM分数调整
oom-score-adj no

# OOM分数调整值
oom-score-adj-values 0 200 800

#################### KERNEL TRANSPARENT HUGEPAGE CONTROL ######################

# 禁用透明大页
disable-thp yes

############################## APPEND ONLY MODE ###############################

# AOF持久化配置 - 针对TaskManager优化
appendonly yes

# AOF文件名
appendfilename "appendonly.aof"

# AOF同步策略
# always: 每个写命令都同步
# everysec: 每秒同步一次 (推荐)
# no: 由操作系统决定何时同步
appendfsync everysec

# 重写时是否同步
no-appendfsync-on-rewrite no

# AOF重写触发条件
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# AOF加载时是否忽略最后一个可能不完整的命令
aof-load-truncated yes

# 混合持久化 (RDB + AOF)
aof-use-rdb-preamble yes

# AOF重写增量文件同步策略
aof-rewrite-incremental-fsync yes

# AOF时间戳
aof-timestamp-enabled no

################################ REDIS CLUSTER  ###############################

# 集群配置 (如果需要)
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000

################################## SLOW LOG ###################################

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

################################ LATENCY MONITOR ##############################

# 延迟监控
latency-monitor-threshold 100

################################ EVENT NOTIFICATION ##############################

# 键空间通知
notify-keyspace-events ""

############################### GOPHER SERVER #################################

# Gopher协议支持
gopher-enabled no

############################### ADVANCED CONFIG ###############################

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议缓冲区限制
proto-max-bulk-len 512mb

# 频率
hz 10

# 动态频率
dynamic-hz yes

# AOF重写时CPU限制
aof-rewrite-incremental-fsync yes

# RDB保存时CPU限制
rdb-save-incremental-fsync yes

# LFU衰减时间
lfu-log-factor 10
lfu-decay-time 1

########################### ACTIVE DEFRAGMENTATION #######################

# 主动碎片整理
activedefrag no

# 碎片整理阈值
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10

# 碎片整理上限
active-defrag-threshold-upper 100

# 碎片整理CPU限制
active-defrag-cycle-min 1
active-defrag-cycle-max 25

# 碎片整理最大扫描字段
active-defrag-max-scan-fields 1000

# Jemalloc后台线程
jemalloc-bg-thread yes

# 不同数据类型的碎片整理阈值
active-defrag-skip-threshold 30
active-defrag-skip-threshold 100

######################### MonIt TaskManager 专用配置 #######################

# TaskManager队列专用配置
# 这些配置针对TaskManager的使用模式进行优化

# 禁用某些不需要的功能以提高性能
save ""  # 如果只使用AOF，可以禁用RDB

# 针对队列操作优化的内存策略
maxmemory-policy allkeys-lru

# 增加网络缓冲区以处理大批量操作
tcp-backlog 1024

# 优化列表和哈希的内存使用
list-max-ziplist-size -1
hash-max-ziplist-entries 1024

# 启用键过期事件通知 (用于监控)
notify-keyspace-events "Ex"
