import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Switch,
  Tag,
  Typography,
  Row,
  Col,
  message,
  Popconfirm,
  Tooltip,
  Empty
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

interface BeatScheduleEntry {
  name: string;
  task: string;
  schedule: string;
  args: any[];
  kwargs: any;
  enabled: boolean;
  last_run?: string;
  next_run?: string;
  total_runs: number;
}

const BeatSchedulePanel: React.FC = () => {
  const [schedules, setSchedules] = useState<BeatScheduleEntry[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取Beat调度列表
  const fetchSchedules = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/celery/beat/schedule');
      if (response.ok) {
        const data = await response.json();
        setSchedules(data || []);
      } else {
        message.error('获取Beat调度失败');
      }
    } catch (error) {
      console.error('Failed to fetch beat schedules:', error);
      message.error('获取Beat调度失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换任务启用状态
  const toggleTask = async (taskName: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/v1/celery/beat/schedule/${taskName}/toggle`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled })
      });
      
      if (response.ok) {
        message.success(`任务${enabled ? '启用' : '禁用'}成功`);
        fetchSchedules();
      } else {
        message.error(`任务${enabled ? '启用' : '禁用'}失败`);
      }
    } catch (error) {
      console.error('Failed to toggle task:', error);
      message.error(`任务${enabled ? '启用' : '禁用'}失败`);
    }
  };

  // 删除任务
  const removeTask = async (taskName: string) => {
    try {
      const response = await fetch(`/api/v1/celery/beat/schedule/${taskName}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        message.success('任务删除成功');
        fetchSchedules();
      } else {
        message.error('任务删除失败');
      }
    } catch (error) {
      console.error('Failed to remove task:', error);
      message.error('任务删除失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchSchedules();
  }, []);

  // 监听刷新事件
  useEffect(() => {
    const handleRefresh = () => fetchSchedules();
    window.addEventListener('celery-refresh', handleRefresh);
    return () => window.removeEventListener('celery-refresh', handleRefresh);
  }, []);

  // 格式化时间
  const formatTime = (timeStr?: string) => {
    if (!timeStr) return 'N/A';
    try {
      return new Date(timeStr).toLocaleString();
    } catch {
      return timeStr;
    }
  };

  // 解析调度表达式
  const parseSchedule = (schedule: string) => {
    try {
      const parsed = JSON.parse(schedule);
      if (parsed.crontab) {
        return `Cron: ${parsed.crontab}`;
      } else if (parsed.interval) {
        return `间隔: ${parsed.interval}秒`;
      } else if (parsed.solar) {
        return `太阳时: ${parsed.solar}`;
      }
      return schedule;
    } catch {
      return schedule;
    }
  };

  return (
    <div className="beat-schedule-panel">
      {/* 头部控制 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={4} style={{ margin: 0 }}>Beat调度管理</Title>
          <Text type="secondary">
            共 {schedules.length} 个调度任务，
            {schedules.filter(s => s.enabled).length} 个启用
          </Text>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchSchedules}
            loading={loading}
          >
            刷新调度
          </Button>
        </Col>
      </Row>

      {/* 调度列表 */}
      {schedules.length === 0 ? (
        <Empty
          description="暂无Beat调度任务"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <Row gutter={16}>
          {schedules.map((schedule) => (
            <Col span={24} key={schedule.name} style={{ marginBottom: 16 }}>
              <Card
                size="small"
                className={`schedule-item ${!schedule.enabled ? 'disabled' : ''}`}
                title={
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space>
                        <ClockCircleOutlined />
                        <Text strong>{schedule.name}</Text>
                        <Tag color={schedule.enabled ? 'green' : 'red'}>
                          {schedule.enabled ? '启用' : '禁用'}
                        </Tag>
                      </Space>
                    </Col>
                    <Col>
                      <Space>
                        <Tooltip title={schedule.enabled ? '禁用任务' : '启用任务'}>
                          <Switch
                            checked={schedule.enabled}
                            onChange={(checked) => toggleTask(schedule.name, checked)}
                            checkedChildren={<PlayCircleOutlined />}
                            unCheckedChildren={<PauseCircleOutlined />}
                          />
                        </Tooltip>
                        
                        <Popconfirm
                          title="确定要删除这个调度任务吗？"
                          onConfirm={() => removeTask(schedule.name)}
                        >
                          <Button
                            size="small"
                            icon={<DeleteOutlined />}
                            danger
                            type="text"
                          />
                        </Popconfirm>
                      </Space>
                    </Col>
                  </Row>
                }
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <div className="schedule-details">
                      <div>
                        <Text strong>任务:</Text> {schedule.task}
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text strong>调度:</Text> {parseSchedule(schedule.schedule)}
                      </div>
                      {schedule.args && schedule.args.length > 0 && (
                        <div style={{ marginTop: 4 }}>
                          <Text strong>参数:</Text> {JSON.stringify(schedule.args)}
                        </div>
                      )}
                    </div>
                  </Col>
                  
                  <Col span={12}>
                    <div className="schedule-timing">
                      <Row gutter={8}>
                        <Col span={12}>
                          <div>
                            <Text type="secondary" style={{ fontSize: 12 }}>上次执行:</Text>
                            <div>{formatTime(schedule.last_run)}</div>
                          </div>
                        </Col>
                        <Col span={12}>
                          <div>
                            <Text type="secondary" style={{ fontSize: 12 }}>下次执行:</Text>
                            <div>{formatTime(schedule.next_run)}</div>
                          </div>
                        </Col>
                      </Row>
                      
                      <Row style={{ marginTop: 8 }}>
                        <Col span={24}>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            总执行次数: {schedule.total_runs}
                          </Text>
                        </Col>
                      </Row>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default BeatSchedulePanel;
