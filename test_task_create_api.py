#!/usr/bin/env python3
"""
测试任务创建API功能

验证从URL池创建监控任务的完整流程
"""

import requests
import json
import time
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_task_templates():
    """测试任务模板API"""
    print("📋 测试任务模板功能")
    
    response = requests.get(f"{BASE_URL}/api/v1/tasks/templates")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取模板成功: {data['total']} 个模板")
        
        for template in data['templates']:
            print(f"   - {template['name']}: {template['description']}")
        
        return True
    else:
        print(f"❌ 获取模板失败: {response.status_code}")
        return False

def test_url_preview():
    """测试URL预览功能"""
    print("\n🔍 测试URL预览功能")
    
    # 先获取一些URL ID
    response = requests.get(f"{BASE_URL}/api/v1/urls/pool?page_size=3")
    if response.status_code != 200:
        print("❌ 无法获取URL列表")
        return False
    
    data = response.json()
    if not data['data']:
        print("❌ 没有URL可用于预览测试")
        return False
    
    url_ids = [url['id'] for url in data['data']]
    print(f"📝 选择 {len(url_ids)} 个URL进行预览测试")
    
    # 测试URL预览
    payload = {"url_ids": url_ids}
    response = requests.post(f"{BASE_URL}/api/v1/tasks/preview-urls", json=payload)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ URL预览成功:")
        print(f"   - 总数: {result['total_count']}")
        print(f"   - 有效: {result['valid_count']}")
        print(f"   - 无效: {result['invalid_count']}")
        print(f"   - 平台分布: {result['platform_distribution']}")
        return url_ids
    else:
        print(f"❌ URL预览失败: {response.status_code}")
        return None

def test_task_validation(url_ids):
    """测试任务验证功能"""
    print("\n✅ 测试任务验证功能")
    
    # 构建验证请求
    payload = {
        "name": "测试任务验证",
        "url_ids": url_ids,
        "schedule": {
            "type": "daily",
            "enabled": True,
            "timezone": "Asia/Shanghai"
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tasks/validate", json=payload)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 任务验证完成:")
        print(f"   - 验证结果: {'通过' if result['success'] else '失败'}")
        
        if result['errors']:
            print(f"   - 错误: {result['errors']}")
        if result['warnings']:
            print(f"   - 警告: {result['warnings']}")
        if result['suggestions']:
            print(f"   - 建议: {result['suggestions']}")
        
        return result['success']
    else:
        print(f"❌ 任务验证失败: {response.status_code}")
        return False

def test_task_creation(url_ids):
    """测试任务创建功能"""
    print("\n🚀 测试任务创建功能")
    
    # 构建任务创建请求
    now = datetime.now()
    start_time = now + timedelta(minutes=5)  # 5分钟后开始
    
    payload = {
        "name": f"测试任务_{int(time.time())}",
        "description": "这是一个通过API创建的测试任务",
        "url_ids": url_ids,
        "schedule": {
            "type": "daily",
            "enabled": True,
            "start_time": start_time.isoformat(),
            "timezone": "Asia/Shanghai"
        },
        "config": {
            "platform": "mercadolibre",
            "priority": "normal",
            "retry_count": 3,
            "timeout": 300,
            "concurrent_limit": 5,
            "batch_size": 10,
            "enable_notifications": True
        },
        "tags": ["测试", "API创建"]
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/tasks/create-from-urls", json=payload)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 任务创建成功:")
        print(f"   - 任务ID: {result['task_id']}")
        print(f"   - 任务名称: {result['task_name']}")
        print(f"   - URL数量: {result['url_count']}")
        print(f"   - 调度信息: {result['schedule_info']}")
        if result.get('next_run'):
            print(f"   - 下次执行: {result['next_run']}")
        
        return result['task_id']
    else:
        print(f"❌ 任务创建失败: {response.status_code}")
        if response.content:
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   错误内容: {response.text}")
        return None

def test_task_details(task_id):
    """测试任务详情获取"""
    print(f"\n📊 测试任务详情获取: {task_id}")
    
    response = requests.get(f"{BASE_URL}/api/v1/tasks/{task_id}")
    
    if response.status_code == 200:
        result = response.json()
        task_data = result['data']
        
        print(f"✅ 任务详情获取成功:")
        print(f"   - 任务名称: {task_data['name']}")
        print(f"   - 任务状态: {task_data['status']}")
        print(f"   - URL数量: {task_data['url_count']}")
        print(f"   - 创建时间: {task_data['created_at']}")
        print(f"   - 关联URL数量: {len(task_data.get('urls', []))}")
        
        return True
    else:
        print(f"❌ 获取任务详情失败: {response.status_code}")
        return False

def test_task_stats():
    """测试任务统计功能"""
    print("\n📈 测试任务统计功能")
    
    response = requests.get(f"{BASE_URL}/api/v1/tasks/stats")
    
    if response.status_code == 200:
        result = response.json()
        stats = result['data']
        
        print(f"✅ 任务统计获取成功:")
        print(f"   - 总任务数: {stats['total_tasks']}")
        print(f"   - 活跃任务: {stats['active_tasks']}")
        print(f"   - 已调度任务: {stats['scheduled_tasks']}")
        print(f"   - 平台分布: {stats['platform_distribution']}")
        print(f"   - 最近任务数: {len(stats['recent_tasks'])}")
        
        return True
    else:
        print(f"❌ 获取任务统计失败: {response.status_code}")
        return False

def test_task_deletion(task_id):
    """测试任务删除功能"""
    print(f"\n🗑️ 测试任务删除功能: {task_id}")
    
    response = requests.delete(f"{BASE_URL}/api/v1/tasks/{task_id}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 任务删除成功: {result['message']}")
        return True
    else:
        print(f"❌ 任务删除失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试任务创建API功能")
    print("=" * 50)
    
    tests = []
    passed = 0
    
    # 测试1: 任务模板
    if test_task_templates():
        passed += 1
    tests.append("任务模板")
    
    # 测试2: URL预览
    url_ids = test_url_preview()
    if url_ids:
        passed += 1
    tests.append("URL预览")
    
    if not url_ids:
        print("❌ 无法继续后续测试，因为没有可用的URL")
        return
    
    # 测试3: 任务验证
    if test_task_validation(url_ids):
        passed += 1
    tests.append("任务验证")
    
    # 测试4: 任务创建
    task_id = test_task_creation(url_ids)
    if task_id:
        passed += 1
    tests.append("任务创建")
    
    if not task_id:
        print("❌ 无法继续后续测试，因为任务创建失败")
        print(f"\n📊 测试结果: {passed}/{len(tests)} 个测试通过")
        return
    
    # 测试5: 任务详情
    if test_task_details(task_id):
        passed += 1
    tests.append("任务详情")
    
    # 测试6: 任务统计
    if test_task_stats():
        passed += 1
    tests.append("任务统计")
    
    # 测试7: 任务删除
    if test_task_deletion(task_id):
        passed += 1
    tests.append("任务删除")
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！任务创建功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        
        failed_tests = [test for i, test in enumerate(tests) if i >= passed]
        print(f"失败的测试: {', '.join(failed_tests)}")

if __name__ == "__main__":
    main()
