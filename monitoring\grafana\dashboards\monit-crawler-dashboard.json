{"dashboard": {"id": null, "title": "MonIt Crawler Performance", "tags": ["monit", "crawler", "performance"], "timezone": "browser", "editable": true, "graphTooltip": 1, "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "Crawler Tasks Overview", "type": "stat", "targets": [{"expr": "monit_crawler_tasks_total", "legendFormat": "{{status}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}, {"color": "green", "value": 1}]}, "displayName": "${__field.labels.status} Tasks"}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background"}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Task Processing Rate", "type": "timeseries", "targets": [{"expr": "rate(monit_crawler_tasks_total[5m])", "legendFormat": "{{status}} tasks/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}}, {"id": 3, "title": "Task Duration", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(monit_crawler_task_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(monit_crawler_task_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(monit_crawler_task_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}}, {"id": 4, "title": "URLs Processed", "type": "timeseries", "targets": [{"expr": "rate(monit_crawler_urls_processed_total[5m])", "legendFormat": "{{status}} URLs/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}}, {"id": 5, "title": "Success Rate", "type": "stat", "targets": [{"expr": "rate(monit_crawler_urls_processed_total{status=\"success\"}[5m]) / rate(monit_crawler_urls_processed_total[5m]) * 100", "legendFormat": "Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background"}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}}, {"id": 6, "title": "Task Queue Size", "type": "timeseries", "targets": [{"expr": "monit_task_queue_size", "legendFormat": "{{queue_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}