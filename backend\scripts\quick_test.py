#!/usr/bin/env python3
"""
快速测试脚本

验证任务调度系统的基本功能，使用少量URL进行测试
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def quick_test():
    """快速测试函数"""
    
    logger.info("🚀 Starting Quick Test of Task Scheduler")
    logger.info("=" * 50)
    
    try:
        # 导入必要模块
        from backend.app.utils.excel_processor import ExcelProcessor
        from backend.app.core.task_splitter import TaskSplitter, TaskPriority, SplitterConfig
        from backend.app.core.rate_limiter import RateLimiter, RateLimitConfig
        
        # 步骤1: 测试Excel处理
        logger.info("📊 Step 1: Testing Excel Processing")
        excel_processor = ExcelProcessor()
        excel_path = "LinksToMonit/all_task_item.xlsx"
        
        if not Path(excel_path).exists():
            logger.error(f"❌ Excel file not found: {excel_path}")
            return
        
        # 读取Excel文件
        products, stats = excel_processor.read_product_links(excel_path)
        logger.info(f"✅ Successfully read {len(products)} products")
        logger.info(f"Platform distribution: {stats['platforms']}")
        
        # 步骤2: 测试任务分片
        logger.info("🔪 Step 2: Testing Task Splitting")
        
        # 取前100个URL进行测试
        test_urls = [product['product_url'] for product in products[:100]]
        logger.info(f"Using {len(test_urls)} URLs for testing")
        
        # 配置分片器
        splitter_config = SplitterConfig(
            max_batch_size=20,  # 小批次便于观察
            min_batch_size=5,
            adaptive_sizing=False
        )
        
        splitter = TaskSplitter(splitter_config)
        
        # 执行分片
        batches = splitter.split_urls(
            urls=test_urls,
            platform="mercadolibre",
            priority=TaskPriority.NORMAL
        )
        
        logger.info(f"✅ Split {len(test_urls)} URLs into {len(batches)} batches")
        for i, batch in enumerate(batches):
            logger.info(f"  Batch {i+1}: {len(batch.urls)} URLs, ID: {batch.batch_id}")
        
        # 步骤3: 测试限流器
        logger.info("🚦 Step 3: Testing Rate Limiter")
        
        rate_limiter_config = RateLimitConfig(
            max_concurrent_requests=2,
            requests_per_minute=60,
            redis_url="redis://localhost:6379/0"
        )
        
        rate_limiter = RateLimiter(rate_limiter_config)
        
        try:
            await rate_limiter.start()
            
            # 测试获取许可
            success1 = await rate_limiter.acquire("test_req_1")
            success2 = await rate_limiter.acquire("test_req_2")
            success3 = await rate_limiter.acquire("test_req_3")  # 应该失败
            
            logger.info(f"Rate limiter test: req1={success1}, req2={success2}, req3={success3}")
            
            if success1 and success2 and not success3:
                logger.info("✅ Rate limiter working correctly")
            else:
                logger.warning("⚠️ Rate limiter behavior unexpected")
            
            # 释放许可
            if success1:
                await rate_limiter.release("test_req_1", True, 1.0)
            if success2:
                await rate_limiter.release("test_req_2", True, 1.0)
            
            await rate_limiter.stop()
            
        except Exception as e:
            logger.error(f"Rate limiter test failed: {e}")
            logger.info("This might be due to Redis not running. Start Redis with: redis-server")
        
        # 步骤4: 测试优先级计算
        logger.info("📊 Step 4: Testing Priority Calculation")
        
        now = datetime.now()
        priorities = {
            'urgent': splitter.calculate_priority_score(TaskPriority.URGENT, now, 50),
            'high': splitter.calculate_priority_score(TaskPriority.HIGH, now, 50),
            'normal': splitter.calculate_priority_score(TaskPriority.NORMAL, now, 50),
            'low': splitter.calculate_priority_score(TaskPriority.LOW, now, 50)
        }
        
        logger.info("Priority scores:")
        for level, score in priorities.items():
            logger.info(f"  {level}: {score:.2f}")
        
        if priorities['urgent'] > priorities['high'] > priorities['normal'] > priorities['low']:
            logger.info("✅ Priority calculation working correctly")
        else:
            logger.warning("⚠️ Priority calculation unexpected")
        
        # 步骤5: 模拟任务处理流程
        logger.info("🔄 Step 5: Simulating Task Processing Flow")
        
        # 模拟处理每个批次
        total_processing_time = 0
        for i, batch in enumerate(batches):
            batch_start = datetime.now()
            
            # 模拟处理时间（每个URL 0.1秒）
            simulated_time = len(batch.urls) * 0.1
            await asyncio.sleep(min(simulated_time, 2.0))  # 最多等待2秒
            
            batch_time = (datetime.now() - batch_start).total_seconds()
            total_processing_time += batch_time
            
            logger.info(f"  Processed batch {i+1}: {len(batch.urls)} URLs in {batch_time:.2f}s")
        
        # 计算性能指标
        urls_per_second = len(test_urls) / total_processing_time if total_processing_time > 0 else 0
        
        logger.info("📈 Performance Summary:")
        logger.info(f"  Total URLs: {len(test_urls)}")
        logger.info(f"  Total Batches: {len(batches)}")
        logger.info(f"  Total Time: {total_processing_time:.2f}s")
        logger.info(f"  Processing Speed: {urls_per_second:.2f} URLs/sec")
        logger.info(f"  Average Batch Size: {len(test_urls) / len(batches):.1f}")
        
        logger.info("🎉 Quick Test Completed Successfully!")
        
        return {
            'success': True,
            'urls_processed': len(test_urls),
            'batches_created': len(batches),
            'processing_time': total_processing_time,
            'urls_per_second': urls_per_second
        }
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("Please ensure you're in the correct directory and all dependencies are installed")
        return {'success': False, 'error': str(e)}
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}


async def main():
    """主函数"""
    
    print("🧪 MonIt Task Scheduler - Quick Test")
    print("=" * 40)
    
    # 检查基本环境
    logger.info("🔍 Checking Environment...")
    
    # 检查Excel文件
    excel_path = Path("LinksToMonit/all_task_item.xlsx")
    if excel_path.exists():
        logger.info(f"✅ Excel file found: {excel_path}")
    else:
        logger.error(f"❌ Excel file not found: {excel_path}")
        return
    
    # 检查pandas
    try:
        import pandas as pd
        logger.info(f"✅ pandas: {pd.__version__}")
    except ImportError:
        logger.error("❌ pandas not installed. Run: pip install pandas openpyxl")
        return
    
    # 运行测试
    result = await quick_test()
    
    if result.get('success'):
        logger.info("✅ All tests passed!")
    else:
        logger.error(f"❌ Tests failed: {result.get('error')}")


if __name__ == "__main__":
    asyncio.run(main())
