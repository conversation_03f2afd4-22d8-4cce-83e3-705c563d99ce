#!/usr/bin/env python3
"""
任务配置验证和修复脚本

目的：
1. 验证从URL池创建的任务配置是否完整
2. 检查调度配置和任务配置的完整性
3. 修复缺失的配置字段
4. 确保配置格式一致性
"""

import redis
import json
import sys
from datetime import datetime
from typing import Dict, List, Any


class TaskConfigVerifier:
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """初始化Redis连接"""
        self.redis = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
        
        # 标准配置模板
        self.standard_schedule_fields = {
            'type': 'daily',
            'enabled': True,
            'start_time': None,
            'time': None,
            'days': None,
            'interval': None,
            'cron_expression': None,
            'timezone': 'Asia/Shanghai',
            'end_time': None,
            'max_runs': None,
            'enable_random_delay': False,
            'random_delay_min': 0,
            'random_delay_max': 180
        }
        
        self.standard_config_fields = {
            'platform': 'mercadolibre',
            'priority': 'normal',
            'retry_count': 3,
            'timeout': 300,
            'concurrent_limit': 5,
            'batch_size': 10,
            'enable_notifications': True,
            'notification_config': None
        }
    
    def analyze_task_configs(self) -> Dict:
        """分析所有任务的配置完整性"""
        print("🔍 分析任务配置完整性...")
        
        analysis = {
            'total_tasks': 0,
            'tasks_with_complete_schedule': 0,
            'tasks_with_complete_config': 0,
            'tasks_with_issues': [],
            'schedule_issues': [],
            'config_issues': []
        }
        
        # 获取所有任务
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        analysis['total_tasks'] = len(task_ids)
        
        print(f"   总任务数: {analysis['total_tasks']}")
        
        for task_id in task_ids:
            task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
            task_name = task_data.get('name', '未知')
            
            print(f"\n   检查任务: {task_name} ({task_id[:8]}...)")
            
            # 检查调度配置
            schedule_issues = self._check_schedule_config(task_id, task_data)
            if not schedule_issues:
                analysis['tasks_with_complete_schedule'] += 1
                print(f"     ✅ 调度配置完整")
            else:
                analysis['schedule_issues'].extend(schedule_issues)
                print(f"     ❌ 调度配置问题: {len(schedule_issues)}个")
                for issue in schedule_issues:
                    print(f"       - {issue['issue']}")
            
            # 检查任务配置
            config_issues = self._check_task_config(task_id, task_data)
            if not config_issues:
                analysis['tasks_with_complete_config'] += 1
                print(f"     ✅ 任务配置完整")
            else:
                analysis['config_issues'].extend(config_issues)
                print(f"     ❌ 任务配置问题: {len(config_issues)}个")
                for issue in config_issues:
                    print(f"       - {issue['issue']}")
            
            # 记录有问题的任务
            if schedule_issues or config_issues:
                analysis['tasks_with_issues'].append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'schedule_issues': schedule_issues,
                    'config_issues': config_issues
                })
        
        return analysis
    
    def _check_schedule_config(self, task_id: str, task_data: Dict) -> List[Dict]:
        """检查调度配置"""
        issues = []
        
        schedule_raw = task_data.get('schedule', '{}')
        if not schedule_raw or schedule_raw == '{}':
            issues.append({
                'task_id': task_id,
                'type': 'schedule',
                'issue': '缺少调度配置'
            })
            return issues
        
        try:
            schedule = json.loads(schedule_raw)
        except json.JSONDecodeError:
            issues.append({
                'task_id': task_id,
                'type': 'schedule',
                'issue': '调度配置JSON格式错误'
            })
            return issues
        
        # 检查必需字段
        required_fields = ['type', 'enabled', 'timezone']
        for field in required_fields:
            if field not in schedule:
                issues.append({
                    'task_id': task_id,
                    'type': 'schedule',
                    'issue': f'缺少必需字段: {field}'
                })
        
        # 检查时间字段一致性
        if schedule.get('type') == 'daily':
            has_time = 'time' in schedule and schedule['time'] is not None
            has_start_time = 'start_time' in schedule and schedule['start_time'] is not None
            
            if not has_time and not has_start_time:
                issues.append({
                    'task_id': task_id,
                    'type': 'schedule',
                    'issue': 'daily类型任务缺少时间配置(time或start_time)'
                })
        
        return issues
    
    def _check_task_config(self, task_id: str, task_data: Dict) -> List[Dict]:
        """检查任务配置"""
        issues = []
        
        config_raw = task_data.get('config', '{}')
        if not config_raw or config_raw == '{}':
            issues.append({
                'task_id': task_id,
                'type': 'config',
                'issue': '缺少任务配置'
            })
            return issues
        
        try:
            config = json.loads(config_raw)
        except json.JSONDecodeError:
            issues.append({
                'task_id': task_id,
                'type': 'config',
                'issue': '任务配置JSON格式错误'
            })
            return issues
        
        # 检查必需字段
        required_fields = ['platform', 'priority', 'retry_count', 'timeout', 'batch_size']
        for field in required_fields:
            if field not in config:
                issues.append({
                    'task_id': task_id,
                    'type': 'config',
                    'issue': f'缺少必需字段: {field}'
                })
        
        # 检查推荐字段
        recommended_fields = ['concurrent_limit', 'enable_notifications']
        for field in recommended_fields:
            if field not in config:
                issues.append({
                    'task_id': task_id,
                    'type': 'config',
                    'issue': f'缺少推荐字段: {field}'
                })
        
        return issues
    
    def fix_task_configs(self, task_ids: List[str]) -> Dict:
        """修复任务配置"""
        print(f"\n🔧 修复 {len(task_ids)} 个任务的配置...")
        
        results = {
            'fixed_tasks': 0,
            'failed_tasks': 0,
            'fixes_applied': []
        }
        
        for task_id in task_ids:
            try:
                print(f"   修复任务: {task_id[:8]}...")
                
                task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
                fixes = []
                
                # 修复调度配置
                schedule_fixes = self._fix_schedule_config(task_id, task_data)
                fixes.extend(schedule_fixes)
                
                # 修复任务配置
                config_fixes = self._fix_task_config(task_id, task_data)
                fixes.extend(config_fixes)
                
                if fixes:
                    results['fixes_applied'].append({
                        'task_id': task_id,
                        'fixes': fixes
                    })
                    print(f"     ✅ 应用了 {len(fixes)} 个修复")
                else:
                    print(f"     ℹ️  无需修复")
                
                results['fixed_tasks'] += 1
                
            except Exception as e:
                print(f"     ❌ 修复失败: {e}")
                results['failed_tasks'] += 1
        
        return results
    
    def _fix_schedule_config(self, task_id: str, task_data: Dict) -> List[str]:
        """修复调度配置"""
        fixes = []
        
        schedule_raw = task_data.get('schedule', '{}')
        try:
            schedule = json.loads(schedule_raw)
        except:
            schedule = {}
        
        # 补充缺失的字段
        updated = False
        for field, default_value in self.standard_schedule_fields.items():
            if field not in schedule:
                schedule[field] = default_value
                fixes.append(f'添加调度字段: {field}')
                updated = True
        
        # 保存更新后的配置
        if updated:
            self.redis.hset(f'monitoring_tasks:{task_id}', 'schedule', json.dumps(schedule))
        
        return fixes
    
    def _fix_task_config(self, task_id: str, task_data: Dict) -> List[str]:
        """修复任务配置"""
        fixes = []
        
        config_raw = task_data.get('config', '{}')
        try:
            config = json.loads(config_raw)
        except:
            config = {}
        
        # 补充缺失的字段
        updated = False
        for field, default_value in self.standard_config_fields.items():
            if field not in config:
                config[field] = default_value
                fixes.append(f'添加任务字段: {field}')
                updated = True
        
        # 保存更新后的配置
        if updated:
            self.redis.hset(f'monitoring_tasks:{task_id}', 'config', json.dumps(config))
        
        return fixes
    
    def run_verification(self) -> Dict:
        """运行完整的验证和修复流程"""
        print("🚀 开始任务配置验证和修复...")
        print("=" * 60)
        
        # 1. 分析配置完整性
        analysis = self.analyze_task_configs()
        
        print(f"\n📊 分析结果:")
        print(f"   - 总任务数: {analysis['total_tasks']}")
        print(f"   - 调度配置完整: {analysis['tasks_with_complete_schedule']}")
        print(f"   - 任务配置完整: {analysis['tasks_with_complete_config']}")
        print(f"   - 有问题的任务: {len(analysis['tasks_with_issues'])}")
        
        if not analysis['tasks_with_issues']:
            print(f"\n✅ 所有任务配置都是完整的！")
            return analysis
        
        # 2. 修复有问题的任务
        print(f"\n🔧 开始修复...")
        task_ids_to_fix = [task['task_id'] for task in analysis['tasks_with_issues']]
        fix_results = self.fix_task_configs(task_ids_to_fix)
        
        # 3. 验证修复结果
        print(f"\n" + "=" * 60)
        print(f"📊 修复结果:")
        print(f"   - 成功修复: {fix_results['fixed_tasks']}")
        print(f"   - 修复失败: {fix_results['failed_tasks']}")
        print(f"   - 总修复项: {sum(len(f['fixes']) for f in fix_results['fixes_applied'])}")
        
        if fix_results['failed_tasks'] == 0:
            print(f"\n🎉 所有任务配置修复完成！")
        else:
            print(f"\n⚠️  部分任务修复失败，需要手动检查。")
        
        return {
            'analysis': analysis,
            'fix_results': fix_results
        }


if __name__ == "__main__":
    verifier = TaskConfigVerifier()
    results = verifier.run_verification()
