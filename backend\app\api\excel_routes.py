"""
Excel文件处理API路由

专门处理Excel文件的上传、解析和验证，不直接创建任务
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import tempfile
import os
from datetime import datetime

from app.utils.excel_processor import ExcelProcessor

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/excel", tags=["excel"])

# 响应模型
class URLData(BaseModel):
    """URL数据模型"""
    id: str
    url: str
    status: str = Field(..., description="valid, invalid, or pending")
    platform: str
    row_index: int
    chinese_name: Optional[str] = None
    spanish_name: Optional[str] = None
    error_message: Optional[str] = None

class ExcelParseResponse(BaseModel):
    """Excel解析响应模型"""
    success: bool
    message: str
    file_info: Dict[str, Any]
    total_urls: int
    valid_urls: int
    invalid_urls: int
    platforms: Dict[str, int]
    urls: List[URLData]
    parse_time: str
    warnings: List[str] = []

class ExcelValidationRequest(BaseModel):
    """Excel验证请求模型"""
    urls: List[str]
    platform: str = "mercadolibre"
    validate_accessibility: bool = False

class ExcelValidationResponse(BaseModel):
    """Excel验证响应模型"""
    success: bool
    message: str
    total_urls: int
    valid_urls: int
    invalid_urls: int
    validation_results: List[URLData]
    validation_time: str


@router.post("/parse", response_model=ExcelParseResponse)
async def parse_excel_file(
    file: UploadFile = File(..., description="Excel文件 (.xlsx, .xls)")
):
    """
    解析Excel文件，提取URL并进行基础验证
    
    这个端点只负责解析Excel文件，不创建任务
    """
    try:
        # 验证文件类型
        if not file.filename or not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400, 
                detail="只支持Excel文件格式 (.xlsx, .xls)"
            )
        
        # 读取文件内容
        content = await file.read()
        file_size = len(content)
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # 解析Excel文件
            excel_processor = ExcelProcessor()
            products, stats = excel_processor.read_product_links(tmp_file_path)
            
            # 构建URL数据列表
            urls_data = []
            for i, product in enumerate(products):
                url_data = URLData(
                    id=f"url_{i+1}",
                    url=product['product_url'],
                    status=product.get('status', 'pending'),
                    platform=product.get('platform', 'unknown'),
                    row_index=product.get('row_index', i+1),
                    chinese_name=product.get('chinese_name'),
                    spanish_name=product.get('spanish_name'),
                    error_message=product.get('error_message')
                )
                urls_data.append(url_data)
            
            # 构建文件信息
            file_info = {
                "filename": file.filename,
                "size": file_size,
                "size_mb": round(file_size / (1024 * 1024), 2),
                "rows": stats.get('total_rows', 0),
                "columns": stats.get('total_columns', 0),
                "upload_time": datetime.now().isoformat()
            }
            
            # 收集警告信息
            warnings = []
            if stats.get('invalid_urls', 0) > 0:
                warnings.append(f"发现 {stats['invalid_urls']} 个无效URL")
            if stats.get('duplicate_urls', 0) > 0:
                warnings.append(f"发现 {stats['duplicate_urls']} 个重复URL")
            
            response = ExcelParseResponse(
                success=True,
                message=f"Excel文件解析成功，共解析 {len(products)} 个链接",
                file_info=file_info,
                total_urls=len(products),
                valid_urls=stats.get('valid_urls', 0),
                invalid_urls=stats.get('invalid_urls', 0),
                platforms=stats.get('platforms', {}),
                urls=urls_data,
                parse_time=datetime.now().isoformat(),
                warnings=warnings
            )
            
            logger.info(f"Successfully parsed Excel file: {file.filename}, {len(products)} URLs found")
            return response
            
        finally:
            # 清理临时文件
            try:
                os.unlink(tmp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error parsing Excel file: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Excel文件解析失败: {str(e)}"
        )


@router.post("/validate", response_model=ExcelValidationResponse)
async def validate_urls(request: ExcelValidationRequest):
    """
    验证URL列表的有效性
    
    可以对解析后的URL进行进一步验证
    """
    try:
        # 这里可以添加URL验证逻辑
        # 例如：检查URL格式、平台识别、可访问性等
        
        validation_results = []
        valid_count = 0
        invalid_count = 0
        
        for i, url in enumerate(request.urls):
            # 基础URL验证逻辑
            status = "valid"  # 默认有效
            error_message = None
            
            # 简单的URL格式验证
            if not url.startswith(('http://', 'https://')):
                status = "invalid"
                error_message = "URL格式无效"
                invalid_count += 1
            else:
                valid_count += 1
            
            url_data = URLData(
                id=f"validated_{i+1}",
                url=url,
                status=status,
                platform=request.platform,
                row_index=i+1,
                error_message=error_message
            )
            validation_results.append(url_data)
        
        response = ExcelValidationResponse(
            success=True,
            message=f"URL验证完成，有效URL: {valid_count}，无效URL: {invalid_count}",
            total_urls=len(request.urls),
            valid_urls=valid_count,
            invalid_urls=invalid_count,
            validation_results=validation_results,
            validation_time=datetime.now().isoformat()
        )
        
        logger.info(f"Validated {len(request.urls)} URLs, {valid_count} valid, {invalid_count} invalid")
        return response
        
    except Exception as e:
        logger.error(f"Error validating URLs: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"URL验证失败: {str(e)}"
        )


@router.get("/platforms")
async def get_supported_platforms():
    """获取支持的平台列表"""
    return {
        "success": True,
        "platforms": {
            "mercadolibre": {
                "name": "MercadoLibre",
                "countries": ["ar", "mx", "br", "co", "cl", "pe", "uy"],
                "url_patterns": [
                    "mercadolibre.com.ar",
                    "mercadolibre.com.mx",
                    "mercadolibre.com.br"
                ]
            },
            "amazon": {
                "name": "Amazon",
                "countries": ["us", "uk", "de", "fr", "it", "es"],
                "url_patterns": [
                    "amazon.com",
                    "amazon.co.uk",
                    "amazon.de"
                ]
            }
        }
    }
