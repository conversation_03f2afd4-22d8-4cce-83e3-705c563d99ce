"""
简单的Worker分配服务
用于在任务执行时分配URL到可用的Worker
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import redis.asyncio as redis

logger = logging.getLogger(__name__)


class SimpleWorkerAssignment:
    """简单的Worker分配服务"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.redis_url = redis_url
        self.redis_client = None
    
    async def start(self):
        """启动服务"""
        try:
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info("Worker assignment service started")
        except Exception as e:
            logger.error(f"Failed to start worker assignment service: {e}")
            raise
    
    async def assign_urls_to_workers(self, task_id: str, urls: List[str]) -> Dict[str, List[str]]:
        """将URL分配给可用的Worker"""
        try:
            # 获取可用的Worker列表
            available_workers = await self._get_available_workers()
            
            if not available_workers:
                # 如果没有配置的Worker，创建虚拟Worker
                available_workers = [
                    {"worker_id": "worker_1", "name": "默认Worker 1", "max_concurrent": 5},
                    {"worker_id": "worker_2", "name": "默认Worker 2", "max_concurrent": 3}
                ]
            
            # 简单的轮询分配策略
            assignments = {}
            worker_index = 0
            
            for i, url in enumerate(urls):
                worker = available_workers[worker_index % len(available_workers)]
                worker_id = worker["worker_id"]
                
                if worker_id not in assignments:
                    assignments[worker_id] = []
                
                assignments[worker_id].append(url)
                worker_index += 1
            
            # 保存分配结果到Redis
            await self._save_assignments(task_id, assignments)
            
            logger.info(f"Assigned {len(urls)} URLs to {len(assignments)} workers for task {task_id}")
            return assignments
            
        except Exception as e:
            logger.error(f"Failed to assign URLs to workers: {e}")
            return {}
    
    async def get_task_assignments(self, task_id: str) -> Dict[str, Any]:
        """获取任务的Worker分配信息"""
        try:
            assignment_key = f"task_assignments:{task_id}"
            assignment_data = await self.redis_client.get(assignment_key)
            
            if assignment_data:
                return json.loads(assignment_data)
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get task assignments: {e}")
            return {}
    
    async def update_url_assignment(self, task_id: str, url: str, worker_id: str, status: str):
        """更新URL的分配状态"""
        try:
            # 更新URL分配状态
            url_assignment_key = f"url_assignments:{task_id}:{url}"
            assignment_data = {
                "task_id": task_id,
                "url": url,
                "worker_id": worker_id,
                "status": status,
                "updated_at": datetime.now().isoformat()
            }
            
            await self.redis_client.setex(
                url_assignment_key,
                3600,  # 1小时过期
                json.dumps(assignment_data)
            )
            
            # 发送WebSocket更新
            await self._send_assignment_update(task_id, assignment_data)
            
        except Exception as e:
            logger.error(f"Failed to update URL assignment: {e}")
    
    async def _get_available_workers(self) -> List[Dict[str, Any]]:
        """获取可用的Worker列表"""
        try:
            # 从Redis获取配置的Worker
            worker_keys = await self.redis_client.keys("crawler_workers:*")
            workers = []
            
            for key in worker_keys:
                worker_data = await self.redis_client.hgetall(key)
                if worker_data:
                    # 转换bytes到str
                    worker_info = {}
                    for k, v in worker_data.items():
                        key_str = k.decode() if isinstance(k, bytes) else k
                        value_str = v.decode() if isinstance(v, bytes) else v
                        worker_info[key_str] = value_str
                    
                    # 只返回活跃的Worker
                    if worker_info.get("status") == "active":
                        workers.append({
                            "worker_id": worker_info.get("worker_id", worker_info.get("id")),
                            "name": worker_info.get("worker_name", "Unknown"),
                            "max_concurrent": int(worker_info.get("allocated_concurrent", 5))
                        })
            
            return workers
            
        except Exception as e:
            logger.error(f"Failed to get available workers: {e}")
            return []
    
    async def _save_assignments(self, task_id: str, assignments: Dict[str, List[str]]):
        """保存分配结果到Redis"""
        try:
            assignment_key = f"task_assignments:{task_id}"
            assignment_data = {
                "task_id": task_id,
                "assignments": assignments,
                "created_at": datetime.now().isoformat(),
                "total_urls": sum(len(urls) for urls in assignments.values()),
                "worker_count": len(assignments)
            }
            
            await self.redis_client.setex(
                assignment_key,
                3600,  # 1小时过期
                json.dumps(assignment_data)
            )
            
        except Exception as e:
            logger.error(f"Failed to save assignments: {e}")
    
    async def _send_assignment_update(self, task_id: str, assignment_data: Dict[str, Any]):
        """发送Worker分配更新的WebSocket消息"""
        try:
            # 导入WebSocket管理器
            from ..main import manager
            
            message = {
                "type": "worker_assignment_update",
                "task_id": task_id,
                "timestamp": datetime.now().isoformat(),
                "data": assignment_data
            }
            
            await manager.broadcast_to_task(task_id, json.dumps(message))
            
        except Exception as e:
            logger.error(f"Failed to send assignment update: {e}")
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()


# 全局实例
worker_assignment_service = SimpleWorkerAssignment()


async def get_worker_assignment_service() -> SimpleWorkerAssignment:
    """获取Worker分配服务实例"""
    if not worker_assignment_service.redis_client:
        await worker_assignment_service.start()
    return worker_assignment_service
