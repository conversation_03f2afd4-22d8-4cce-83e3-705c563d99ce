#!/usr/bin/env python3
"""
测试任务统计信息修复
验证活跃URL和禁用URL数量是否正确显示
"""

import asyncio
import aiohttp
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

API_BASE_URL = "http://localhost:8000"

async def test_task_stats_fix():
    """测试任务统计信息修复"""
    
    # 测试任务ID
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    async with aiohttp.ClientSession() as session:
        print(f"🧪 测试任务统计信息修复")
        print(f"📋 任务ID: {task_id}")
        print("=" * 60)
        
        # 1. 获取任务统计信息
        print(f"\n1. 获取任务统计信息...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务统计信息成功")
                    
                    stats = data.get('data', {})
                    url_stats = stats.get('url_stats', {})
                    
                    print(f"\n📊 URL统计信息:")
                    print(f"   总URL数: {url_stats.get('total_urls', 0)}")
                    print(f"   活跃URL: {url_stats.get('active_urls', 0)}")
                    print(f"   禁用URL: {url_stats.get('disabled_urls', 0)}")
                    print(f"   错误URL: {url_stats.get('error_urls', 0)}")
                    
                    # 验证数据合理性
                    total = url_stats.get('total_urls', 0)
                    active = url_stats.get('active_urls', 0)
                    disabled = url_stats.get('disabled_urls', 0)
                    error = url_stats.get('error_urls', 0)
                    
                    calculated_total = active + disabled + error
                    
                    print(f"\n🔍 数据验证:")
                    print(f"   计算总数: {calculated_total}")
                    print(f"   实际总数: {total}")
                    
                    if calculated_total == total:
                        print(f"✅ 数据一致性验证通过")
                    else:
                        print(f"❌ 数据一致性验证失败")
                    
                    if active > 0 and disabled >= 0:
                        print(f"✅ URL状态统计正常")
                    else:
                        print(f"❌ URL状态统计异常")
                        
                else:
                    print(f"❌ 获取任务统计信息失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
                    
        except Exception as e:
            print(f"❌ 获取任务统计信息异常: {e}")
        
        # 2. 获取任务URL详情进行对比验证
        print(f"\n2. 获取任务URL详情进行对比验证...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务URL详情成功")
                    
                    urls = data.get('data', [])
                    total_urls_detail = len(urls)
                    
                    # 统计各状态URL数量
                    active_count = len([url for url in urls if url.get('status') == 'active'])
                    disabled_count = len([url for url in urls if url.get('status') == 'disabled'])
                    error_count = len([url for url in urls if url.get('status') == 'error'])
                    
                    print(f"\n📋 URL详情统计:")
                    print(f"   总URL数: {total_urls_detail}")
                    print(f"   活跃URL: {active_count}")
                    print(f"   禁用URL: {disabled_count}")
                    print(f"   错误URL: {error_count}")
                    
                    # 显示前几个URL的状态
                    print(f"\n🔍 前5个URL状态示例:")
                    for i, url in enumerate(urls[:5]):
                        print(f"   {i+1}. {url.get('url', 'N/A')[:50]}... - 状态: {url.get('status', 'unknown')}")
                        
                else:
                    print(f"❌ 获取任务URL详情失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 获取任务URL详情异常: {e}")
        
        print(f"\n" + "=" * 60)
        print(f"🎯 测试完成")

if __name__ == "__main__":
    asyncio.run(test_task_stats_fix())
