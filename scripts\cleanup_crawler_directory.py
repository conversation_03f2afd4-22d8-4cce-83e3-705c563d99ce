#!/usr/bin/env python3
"""
清理 backend/crawler 目录中的废弃组件

基于爬虫重构文档和实际使用情况分析，安全清理不再需要的组件。
"""

import os
import shutil
import sys
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    current_dir = Path(__file__).parent
    # 向上查找直到找到包含backend目录的根目录
    while current_dir.parent != current_dir:
        if (current_dir / 'backend').exists():
            return current_dir
        current_dir = current_dir.parent
    return Path.cwd()

def backup_directory(source_dir, backup_dir):
    """备份目录"""
    if source_dir.exists():
        print(f"📦 备份 {source_dir} 到 {backup_dir}")
        shutil.copytree(source_dir, backup_dir, dirs_exist_ok=True)
        return True
    return False

def remove_directory(dir_path):
    """删除目录"""
    if dir_path.exists():
        print(f"🗑️  删除 {dir_path}")
        shutil.rmtree(dir_path)
        return True
    else:
        print(f"⚠️  目录不存在: {dir_path}")
        return False

def remove_pycache_dirs(root_dir):
    """递归删除所有__pycache__目录"""
    removed_count = 0
    for pycache_dir in root_dir.rglob('__pycache__'):
        if pycache_dir.is_dir():
            print(f"🗑️  删除缓存: {pycache_dir}")
            shutil.rmtree(pycache_dir)
            removed_count += 1
    return removed_count

def cleanup_crawler_directory():
    """清理爬虫目录"""
    
    print("🧹 开始清理 backend/crawler 目录")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = get_project_root()
    crawler_dir = project_root / 'backend' / 'crawler'
    
    if not crawler_dir.exists():
        print(f"❌ 爬虫目录不存在: {crawler_dir}")
        return False
    
    print(f"📂 项目根目录: {project_root}")
    print(f"📂 爬虫目录: {crawler_dir}")
    
    # 创建备份目录
    backup_root = project_root / 'backup' / 'crawler_cleanup'
    backup_root.mkdir(parents=True, exist_ok=True)
    
    # 定义要清理的目录
    directories_to_remove = [
        'browser',           # 浏览器管理模块 - 已废弃
        'anti_detection',    # 反检测策略模块 - 已废弃
        'extractors',        # 数据提取器模块 - 已废弃
        'sessions',          # 会话管理模块 - 已废弃
        'tests',             # 测试文件 - 可清理
    ]
    
    # 定义要保留的核心目录
    directories_to_keep = [
        'api_client',        # API客户端模块 - 正在使用
        'engine',            # 引擎核心模块 - 正在使用
        'schedulers',        # 调度器模块 - 正在使用
        'exceptions',        # 异常处理模块 - 正在使用
        'utils',             # 工具模块 - 可能在使用
    ]
    
    print(f"\n📋 清理计划:")
    print(f"  🗑️  将删除: {', '.join(directories_to_remove)}")
    print(f"  ✅ 将保留: {', '.join(directories_to_keep)}")
    
    # 用户确认
    response = input(f"\n❓ 确认执行清理操作? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ 用户取消操作")
        return False
    
    print(f"\n🚀 开始执行清理...")
    
    # 统计信息
    removed_dirs = 0
    backed_up_dirs = 0
    
    # 1. 清理指定的废弃目录
    for dir_name in directories_to_remove:
        source_dir = crawler_dir / dir_name
        backup_dir = backup_root / dir_name
        
        if source_dir.exists():
            # 先备份
            if backup_directory(source_dir, backup_dir):
                backed_up_dirs += 1
            
            # 再删除
            if remove_directory(source_dir):
                removed_dirs += 1
    
    # 2. 清理所有__pycache__目录
    print(f"\n🧹 清理Python缓存文件...")
    pycache_count = remove_pycache_dirs(crawler_dir)
    
    # 3. 检查config目录
    config_dir = crawler_dir / 'config'
    if config_dir.exists():
        config_files = list(config_dir.iterdir())
        if not config_files:
            print(f"📁 config目录为空，删除...")
            backup_directory(config_dir, backup_root / 'config')
            remove_directory(config_dir)
            removed_dirs += 1
        else:
            print(f"📁 config目录包含文件，保留: {[f.name for f in config_files]}")
    
    # 4. 验证保留的目录
    print(f"\n✅ 验证保留的核心目录:")
    for dir_name in directories_to_keep:
        dir_path = crawler_dir / dir_name
        if dir_path.exists():
            print(f"  ✅ {dir_name} - 已保留")
        else:
            print(f"  ⚠️  {dir_name} - 目录不存在")
    
    # 5. 显示清理结果
    print(f"\n🎉 清理完成!")
    print(f"  📊 删除目录: {removed_dirs}个")
    print(f"  📦 备份目录: {backed_up_dirs}个")
    print(f"  🗑️  清理缓存: {pycache_count}个")
    print(f"  💾 备份位置: {backup_root}")
    
    # 6. 显示当前目录结构
    print(f"\n📂 清理后的目录结构:")
    for item in sorted(crawler_dir.iterdir()):
        if item.is_dir():
            print(f"  📁 {item.name}/")
        else:
            print(f"  📄 {item.name}")
    
    print(f"\n💡 提示:")
    print(f"  - 备份文件保存在: {backup_root}")
    print(f"  - 如需恢复，可从备份目录复制")
    print(f"  - 核心功能组件已保留，系统应正常工作")
    
    return True

def main():
    """主函数"""
    try:
        success = cleanup_crawler_directory()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
