"""
爬虫引擎抽象基类

实现了接口的基础功能，为具体实现类提供通用的功能和模板方法。
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from abc import ABC, abstractmethod

from .interfaces import (
    ICrawlerEngine,
    ITaskScheduler,
    TaskStatus
)
from ..exceptions import (
    CrawlerException,
    TaskException,
    ExtractionException,
    ConfigurationException
)


class BaseCrawlerEngine(ICrawlerEngine):
    """爬虫引擎抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化爬虫引擎
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.is_running = False
        self.start_time: Optional[datetime] = None
        self.task_count = 0
        self.success_count = 0
        self.failure_count = 0
        
        # 组件实例
        self.browser_manager = None
        self.data_extractor = None
        self.task_scheduler = None
        self.session_manager = None
        self.anti_detection_manager = None
        self.proxy_manager = None
        self.error_handler = None
    
    async def start(self) -> bool:
        """启动爬虫引擎"""
        try:
            self.logger.info("Starting crawler engine...")
            
            # 初始化组件
            await self._initialize_components()
            
            # 启动各个组件
            await self._start_components()
            
            self.is_running = True
            self.start_time = datetime.now()
            self.logger.info("Crawler engine started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start crawler engine: {e}")
            raise CrawlerException(f"Engine startup failed: {e}")
    
    async def stop(self) -> bool:
        """停止爬虫引擎"""
        try:
            self.logger.info("Stopping crawler engine...")
            
            # 停止各个组件
            await self._stop_components()
            
            self.is_running = False
            self.logger.info("Crawler engine stopped successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop crawler engine: {e}")
            return False
    
    async def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        uptime = None
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime_seconds": uptime,
            "task_count": self.task_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": self.success_count / max(self.task_count, 1) * 100,
            "components": await self._get_component_status()
        }
    
    @abstractmethod
    async def _initialize_components(self) -> None:
        """初始化组件 - 子类实现"""
        pass
    
    @abstractmethod
    async def _start_components(self) -> None:
        """启动组件 - 子类实现"""
        pass
    
    @abstractmethod
    async def _stop_components(self) -> None:
        """停止组件 - 子类实现"""
        pass
    
    @abstractmethod
    async def _get_component_status(self) -> Dict[str, Any]:
        """获取组件状态 - 子类实现"""
        pass
    
    async def _handle_crawl_error(self, error: Exception, url: str) -> None:
        """处理爬取错误"""
        self.failure_count += 1
        self.logger.error(f"Crawl error for {url}: {error}")
        
        if self.error_handler:
            await self.error_handler.handle_error(error, {"url": url})


class BaseDataExtractor(ABC):
    """数据提取器抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化数据提取器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.extraction_count = 0
        self.success_count = 0
        self.failure_count = 0
        
        # 数据清洗规则
        self.cleaning_rules = config.get('cleaning_rules', {})
        
        # 验证规则
        self.validation_rules = config.get('validation_rules', {})
    
    def validate_data(
        self, 
        data: Dict[str, Any], 
        rules: Optional[Dict[str, Any]] = None
    ) -> bool:
        """验证数据"""
        validation_rules = rules or self.validation_rules
        
        try:
            for field, rule in validation_rules.items():
                if field not in data:
                    # 检查是否为必需字段
                    if rule.get('required', False):
                        self.logger.warning(f"Required field missing: {field}")
                        return False
                    continue
                
                value = data[field]
                
                # 类型检查
                expected_type = rule.get('type')
                if expected_type and not isinstance(value, eval(expected_type)):
                    self.logger.warning(f"Type mismatch for {field}: expected {expected_type}, got {type(value)}")
                    return False
                
                # 值范围检查
                min_val = rule.get('min')
                max_val = rule.get('max')
                if min_val is not None and value < min_val:
                    return False
                if max_val is not None and value > max_val:
                    return False
                
                # 正则表达式检查
                pattern = rule.get('pattern')
                if pattern and isinstance(value, str):
                    import re
                    if not re.match(pattern, value):
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Data validation error: {e}")
            return False
    
    async def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗数据"""
        cleaned_data = {}
        
        for field, value in data.items():
            try:
                # 获取字段的清洗规则
                field_rules = self.cleaning_rules.get(field, {})
                
                cleaned_value = await self._clean_field_value(value, field_rules)
                cleaned_data[field] = cleaned_value
                
            except Exception as e:
                self.logger.warning(f"Failed to clean field {field}: {e}")
                cleaned_data[field] = value  # 保留原值
        
        return cleaned_data
    
    async def _clean_field_value(self, value: Any, rules: Dict[str, Any]) -> Any:
        """清洗单个字段值"""
        if value is None:
            return None
        
        # 字符串清洗
        if isinstance(value, str):
            # 去除空白字符
            if rules.get('strip', True):
                value = value.strip()
            
            # 替换字符
            replacements = rules.get('replacements', {})
            for old, new in replacements.items():
                value = value.replace(old, new)
            
            # 正则替换
            regex_replacements = rules.get('regex_replacements', {})
            for pattern, replacement in regex_replacements.items():
                import re
                value = re.sub(pattern, replacement, value)
        
        # 数字清洗
        elif isinstance(value, (int, float)):
            # 数值范围限制
            min_val = rules.get('min')
            max_val = rules.get('max')
            if min_val is not None:
                value = max(value, min_val)
            if max_val is not None:
                value = min(value, max_val)
        
        return value
    
    @abstractmethod
    async def extract(
        self, 
        content: Union[str, Any], 
        selectors: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """提取数据 - 子类实现"""
        pass


class BaseTaskManager:
    """任务管理器抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化任务管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.task_history: List[Dict[str, Any]] = []
        self.max_history_size = config.get('max_history_size', 1000)
    
    def create_task(
        self, 
        task_id: str, 
        url: str, 
        priority: int = 0,
        **kwargs
    ) -> Dict[str, Any]:
        """创建任务"""
        task = {
            'task_id': task_id,
            'url': url,
            'priority': priority,
            'status': TaskStatus.PENDING,
            'created_at': datetime.now(),
            'started_at': None,
            'completed_at': None,
            'retry_count': 0,
            'max_retries': kwargs.get('max_retries', 3),
            'result': None,
            'error': None,
            'metadata': kwargs
        }
        
        self.tasks[task_id] = task
        return task
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def update_task_status(
        self, 
        task_id: str, 
        status: TaskStatus,
        **kwargs
    ) -> bool:
        """更新任务状态"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        old_status = task['status']
        task['status'] = status
        
        # 更新时间戳
        if status == TaskStatus.RUNNING and old_status == TaskStatus.PENDING:
            task['started_at'] = datetime.now()
        elif status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            task['completed_at'] = datetime.now()
        
        # 更新其他字段
        for key, value in kwargs.items():
            task[key] = value
        
        # 如果任务完成，加入历史记录
        if status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            self._add_to_history(task)
        
        return True
    
    def _add_to_history(self, task: Dict[str, Any]) -> None:
        """添加到历史记录"""
        self.task_history.append(task.copy())
        
        # 限制历史记录大小
        if len(self.task_history) > self.max_history_size:
            self.task_history = self.task_history[-self.max_history_size:]
    
    def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total_tasks = len(self.tasks) + len(self.task_history)
        
        # 统计各状态的任务数量
        status_counts = {}
        for task in self.tasks.values():
            status = task['status'].value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for task in self.task_history:
            status = task['status'].value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'total_tasks': total_tasks,
            'active_tasks': len(self.tasks),
            'completed_tasks': len(self.task_history),
            'status_counts': status_counts,
            'success_rate': (
                status_counts.get('success', 0) / max(total_tasks, 1) * 100
            )
        } 