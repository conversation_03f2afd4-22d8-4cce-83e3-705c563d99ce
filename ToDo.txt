ItemFollow\task_to_do\all_task_item.xlsx 
中有一个excel表，这个表是我们所有等待获取信息的链接,表结构如下
A：中文品名
B：原始西班牙语品名
C：商品链接

我需要从这个excel的C列提取所有商品链接
然后每个链接依次按照
ScrapperRules\scrapper-config-articulo.mercadolibre.com.mx (4).json
网页选择器进行数据爬取，如果一个类目有多个选择器enable，那就爬取所有enable的选择器数据进行比对，互相校验

链接依次爬取后，将信息存储在一个以任务开始时的时间戳YYYYMMDDHHMM命名的excel中：

A：任务开始的时间戳YYYYMMDDHHMMSS
B：原始西班牙语品名
C：商品链接
D：商品首图
E：商品当前售价
F：商品现价
G：商品折扣率
H：商品销售数量
I：商品库存数量
J：商品评分
K：商品评分数量
...
其他json中剩余的列

目标网站是一个电商网站，有非常严格的反爬策略，请使用随机指纹随机请求事件，随机请求头，随机UA等各种反爬手段规避检测
我希望爬虫程序能够使用API来通信，通过API获取要爬取的网址列表，通过API传入爬取规则，通过API返回爬取结果，通过API返回自身运行状态，运行步骤
我希望程序有直观的前端UI可以观察运行状态、操作、日志、对比分析多日数据、查看结果等，方便使用
请帮我规划这个任务，做出完整的任务规划，包含技术方案、架构图、UI交互设计、调用流程、数据流向等等一切不确定的事情的计划，然后将任务拆分成负责度合适的子任务，然后我们再依次完成所有子任务