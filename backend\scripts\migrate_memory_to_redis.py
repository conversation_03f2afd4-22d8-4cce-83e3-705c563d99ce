#!/usr/bin/env python3
"""
数据迁移脚本：将内存任务迁移到Redis

这个脚本用于检查是否有遗留的内存任务数据，并将其迁移到Redis存储。
在重构完成后运行此脚本以确保数据完整性。
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import redis.asyncio as redis
from app.services.task_storage_service import TaskStorageService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MemoryToRedisMigrator:
    """内存数据到Redis的迁移器"""

    def __init__(self, redis_url: str = None):
        if redis_url is None:
            redis_url = self._detect_redis_url()
        self.redis_url = redis_url
        self.task_storage = TaskStorageService(redis_url)
        self.migration_stats = {
            "tasks_found": 0,
            "tasks_migrated": 0,
            "tasks_skipped": 0,
            "urls_found": 0,
            "urls_migrated": 0,
            "errors": []
        }

    def _detect_redis_url(self) -> str:
        """自动检测Redis连接地址"""
        import os

        # 检查环境变量
        if os.getenv('REDIS_URL'):
            return os.getenv('REDIS_URL')

        # 检查是否在Docker环境中
        if os.path.exists('/.dockerenv'):
            return "redis://redis:6379/0"

        # 默认本地环境
        return "redis://localhost:6379/0"
    
    async def check_memory_storage_exists(self) -> bool:
        """检查是否还有内存存储的引用"""
        try:
            # 这里可以检查是否有任何遗留的内存存储文件或配置
            logger.info("Checking for memory storage references...")
            
            # 由于我们已经完全移除了内存存储，这里主要是验证
            # 在实际环境中，可以检查是否有pickle文件或其他持久化的内存数据
            
            return False  # 没有发现内存存储
        except Exception as e:
            logger.error(f"Error checking memory storage: {e}")
            return False
    
    async def validate_redis_data_integrity(self) -> Dict[str, Any]:
        """验证Redis数据完整性"""
        try:
            redis_client = redis.from_url(self.redis_url)
            
            # 获取所有任务ID
            task_ids = await redis_client.smembers('monitoring_tasks:all_ids')
            
            validation_results = {
                "total_tasks": len(task_ids),
                "valid_tasks": 0,
                "invalid_tasks": 0,
                "missing_fields": [],
                "orphaned_urls": 0,
                "task_url_associations": 0
            }
            
            for task_id in task_ids:
                task_id_str = task_id.decode() if isinstance(task_id, bytes) else task_id
                task_key = f"monitoring_tasks:{task_id_str}"
                
                # 检查任务数据完整性
                task_data = await redis_client.hgetall(task_key)
                
                if not task_data:
                    validation_results["invalid_tasks"] += 1
                    continue
                
                # 检查必需字段
                required_fields = ['id', 'name', 'status', 'created_at']
                missing = []
                
                for field in required_fields:
                    if field not in task_data and field.encode() not in task_data:
                        missing.append(field)
                
                if missing:
                    validation_results["missing_fields"].extend(
                        [f"{task_id_str}:{field}" for field in missing]
                    )
                    validation_results["invalid_tasks"] += 1
                else:
                    validation_results["valid_tasks"] += 1
                
                # 检查URL关联
                url_key = f"monitoring_tasks:task_urls:{task_id_str}"
                url_count = await redis_client.scard(url_key)
                validation_results["task_url_associations"] += url_count
            
            await redis_client.close()
            
            logger.info(f"Data validation completed: {validation_results}")
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating Redis data: {e}")
            return {"error": str(e)}
    
    async def cleanup_orphaned_data(self) -> Dict[str, int]:
        """清理孤立的数据"""
        try:
            redis_client = redis.from_url(self.redis_url)
            cleanup_stats = {
                "orphaned_task_urls": 0,
                "orphaned_url_tasks": 0,
                "invalid_task_keys": 0
            }
            
            # 获取所有任务ID
            valid_task_ids = await redis_client.smembers('monitoring_tasks:all_ids')
            valid_task_ids = {
                task_id.decode() if isinstance(task_id, bytes) else task_id 
                for task_id in valid_task_ids
            }
            
            # 查找所有task_urls键
            task_url_keys = await redis_client.keys('monitoring_tasks:task_urls:*')
            
            for key in task_url_keys:
                key_str = key.decode() if isinstance(key, bytes) else key
                task_id = key_str.split(':')[-1]
                
                if task_id not in valid_task_ids:
                    # 删除孤立的task_urls键
                    await redis_client.delete(key)
                    cleanup_stats["orphaned_task_urls"] += 1
                    logger.info(f"Removed orphaned task_urls key: {key_str}")
            
            await redis_client.close()
            
            logger.info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return {"error": str(e)}
    
    async def generate_migration_report(self) -> str:
        """生成迁移报告"""
        report_lines = [
            "=" * 60,
            "内存任务存储迁移报告",
            "=" * 60,
            f"迁移时间: {datetime.now().isoformat()}",
            "",
            "迁移统计:",
            f"  - 发现的任务数量: {self.migration_stats['tasks_found']}",
            f"  - 成功迁移的任务: {self.migration_stats['tasks_migrated']}",
            f"  - 跳过的任务: {self.migration_stats['tasks_skipped']}",
            f"  - 发现的URL数量: {self.migration_stats['urls_found']}",
            f"  - 成功迁移的URL: {self.migration_stats['urls_migrated']}",
            "",
        ]
        
        if self.migration_stats['errors']:
            report_lines.extend([
                "错误列表:",
                *[f"  - {error}" for error in self.migration_stats['errors']],
                ""
            ])
        
        # 验证Redis数据
        validation_results = await self.validate_redis_data_integrity()
        
        report_lines.extend([
            "Redis数据验证:",
            f"  - 总任务数: {validation_results.get('total_tasks', 0)}",
            f"  - 有效任务: {validation_results.get('valid_tasks', 0)}",
            f"  - 无效任务: {validation_results.get('invalid_tasks', 0)}",
            f"  - 任务-URL关联: {validation_results.get('task_url_associations', 0)}",
            ""
        ])
        
        if validation_results.get('missing_fields'):
            report_lines.extend([
                "缺失字段:",
                *[f"  - {field}" for field in validation_results['missing_fields'][:10]],
                ""
            ])
        
        # 清理孤立数据
        cleanup_results = await self.cleanup_orphaned_data()
        
        report_lines.extend([
            "数据清理:",
            f"  - 清理的孤立task_urls: {cleanup_results.get('orphaned_task_urls', 0)}",
            f"  - 清理的孤立url_tasks: {cleanup_results.get('orphaned_url_tasks', 0)}",
            "",
            "=" * 60,
            "迁移完成！",
            "=" * 60
        ])
        
        return "\n".join(report_lines)
    
    async def run_migration(self) -> bool:
        """运行完整的迁移流程"""
        try:
            logger.info("开始内存任务存储迁移...")
            
            # 检查内存存储
            has_memory_storage = await self.check_memory_storage_exists()
            
            if not has_memory_storage:
                logger.info("未发现内存存储数据，无需迁移")
                self.migration_stats['tasks_found'] = 0
            else:
                logger.info("发现内存存储数据，开始迁移...")
                # 这里可以添加实际的迁移逻辑
                # 由于我们已经完全移除了内存存储，这部分主要是验证
            
            # 生成报告
            report = await self.generate_migration_report()
            
            # 保存报告
            report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"迁移报告已保存到: {report_file}")
            print(report)
            
            return True
            
        except Exception as e:
            logger.error(f"迁移失败: {e}")
            self.migration_stats['errors'].append(str(e))
            return False
        finally:
            await self.task_storage.close()


async def main():
    """主函数"""
    print("内存任务存储迁移工具")
    print("=" * 40)
    
    migrator = MemoryToRedisMigrator()
    
    try:
        success = await migrator.run_migration()
        
        if success:
            print("\n✅ 迁移成功完成！")
            return 0
        else:
            print("\n❌ 迁移失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 迁移被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 迁移过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
