import { useState, useCallback } from 'react';

interface SystemStatus {
  celery: {
    status: string;
    workers: number;
    worker_names: string[];
  };
  redis: {
    connected: boolean;
    memory_usage?: string;
    connected_clients?: number;
  };
  queues: {
    [key: string]: number;
  };
  tasks: {
    active: number;
    failed: number;
    pending: number;
    processed: number;
  };
}

export const useSystemStatus = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshSystemStatus = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 并行获取多个状态信息
      const [healthRes, statsRes, queuesRes] = await Promise.all([
        fetch('/api/v1/celery/health'),
        fetch('/api/v1/celery/stats'),
        fetch('/api/v1/celery/queues')
      ]);

      const [health, stats, queues] = await Promise.all([
        healthRes.json(),
        statsRes.json(),
        queuesRes.json()
      ]);

      // 组合系统状态数据
      const combinedStatus: SystemStatus = {
        celery: {
          status: health.status || 'unknown',
          workers: health.workers || 0,
          worker_names: health.worker_names || []
        },
        redis: {
          connected: health.status === 'healthy',
          memory_usage: stats.redis_info?.memory_usage,
          connected_clients: stats.redis_info?.connected_clients
        },
        queues: queues.queues || {},
        tasks: {
          active: stats.active_tasks || 0,
          failed: stats.failed_tasks || 0,
          pending: stats.pending_tasks || 0,
          processed: stats.processed_tasks || 0
        }
      };

      setSystemStatus(combinedStatus);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch system status';
      setError(errorMessage);
      console.error('Failed to fetch system status:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    systemStatus,
    loading,
    error,
    refreshSystemStatus
  };
};
