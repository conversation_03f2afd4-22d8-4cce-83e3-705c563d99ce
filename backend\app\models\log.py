"""
系统日志数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import DateTime, String, Text, Integer, JSON, CheckConstraint, func, PrimaryKeyConstraint
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TimestampMixin


class SystemLog(Base, TimestampMixin):
    """系统日志表 (TimescaleDB hypertable)"""
    
    __tablename__ = "system_logs"
    __table_args__ = (
        PrimaryKeyConstraint('id', 'logged_at'),  # 复合主键，包含分区列
        CheckConstraint(
            "level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')",
            name="system_logs_level_check"
        ),
    )
    
    # 主键ID
    id: Mapped[int] = mapped_column(
        Integer,
        autoincrement=True,
        comment="日志ID"
    )
    
    # 时间戳 (用于TimescaleDB分区，现在是主键的一部分)
    logged_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.current_timestamp(),
        comment="日志时间戳"
    )
    
    # 日志基础信息
    level: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        comment="日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL"
    )
    logger: Mapped[str] = mapped_column(String(100), nullable=False, comment="日志记录器名称")
    message: Mapped[str] = mapped_column(Text, nullable=False, comment="日志消息")
    
    # 代码位置信息
    module: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="模块名称")
    function: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="函数名称")
    line_number: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="行号")
    
    # 关联信息
    task_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="关联任务ID")
    product_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="关联商品ID")
    
    # 额外数据
    extra_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="额外的结构化数据"
    )
    
    def __repr__(self) -> str:
        message_preview = self.message[:50] + "..." if len(self.message) > 50 else self.message
        return f"<SystemLog(id={self.id}, level='{self.level}', message='{message_preview}')>"
    
    @property
    def is_error(self) -> bool:
        """是否为错误级别"""
        return self.level in ("ERROR", "CRITICAL") 