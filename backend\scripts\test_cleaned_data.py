#!/usr/bin/env python3
"""
测试清理后的数据格式

验证前端数据清理功能是否能解决422错误
"""

import requests
import json

def test_cleaned_data():
    """测试清理后的数据"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试清理后的数据格式")
    print("=" * 50)
    
    # 模拟前端清理后的数据（移除了空字符串）
    test_cases = [
        {
            "name": "测试1: 清理后的daily格式",
            "data": {
                "name": "测试任务1",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai",
                    "enable_random_delay": False,
                    "random_delay_min": 0,
                    "random_delay_max": 180
                }
            }
        },
        {
            "name": "测试2: 最小化字段",
            "data": {
                "name": "测试任务2",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai"
                }
            }
        },
        {
            "name": "测试3: 包含有效的可选字段",
            "data": {
                "name": "测试任务3",
                "url_ids": ["url1", "url2"],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": "09:00",
                    "timezone": "Asia/Shanghai",
                    "end_time": "18:00",
                    "max_runs": 100,
                    "enable_random_delay": True,
                    "random_delay_min": 5,
                    "random_delay_max": 30
                }
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 {test_case['name']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/v1/tasks/validate",
                json=test_case['data'],
                timeout=10
            )
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ 验证成功")
                print(f"  验证结果: success={result.get('success')}, errors={len(result.get('errors', []))}")
            else:
                print(f"  ❌ 验证失败")
                try:
                    error_data = response.json()
                    print(f"  错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  错误文本: {response.text}")
                    
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    test_cleaned_data()
