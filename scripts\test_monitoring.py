#!/usr/bin/env python3
"""
MonIt监控系统测试脚本
验证监控栈的部署和功能
"""

import requests
import time
import json
import sys
from typing import Dict, Any

class MonitoringTester:
    """监控系统测试器"""
    
    def __init__(self):
        self.services = {
            'prometheus': 'http://localhost:9090',
            'grafana': 'http://localhost:3001',
            'elasticsearch': 'http://localhost:9200',
            'kibana': 'http://localhost:5601'
        }
        self.results = {}
    
    def test_service_health(self, service_name: str, url: str) -> Dict[str, Any]:
        """测试服务健康状态"""
        print(f"🔍 测试 {service_name} 服务...")
        
        try:
            response = requests.get(f"{url}/api/health", timeout=10)
            if response.status_code == 200:
                print(f"✅ {service_name} 服务正常")
                return {"status": "healthy", "response_time": response.elapsed.total_seconds()}
            else:
                print(f"⚠️  {service_name} 服务响应异常: {response.status_code}")
                return {"status": "unhealthy", "status_code": response.status_code}
        except requests.exceptions.RequestException as e:
            print(f"❌ {service_name} 服务不可访问: {e}")
            return {"status": "unreachable", "error": str(e)}
    
    def test_prometheus(self) -> Dict[str, Any]:
        """测试Prometheus"""
        print("🔍 测试Prometheus...")
        
        try:
            # 测试基本连接
            response = requests.get(f"{self.services['prometheus']}/api/v1/query?query=up", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    targets = len(data.get('data', {}).get('result', []))
                    print(f"✅ Prometheus正常，监控目标数: {targets}")
                    return {"status": "healthy", "targets": targets}
            
            print(f"⚠️  Prometheus响应异常: {response.status_code}")
            return {"status": "unhealthy", "status_code": response.status_code}
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Prometheus不可访问: {e}")
            return {"status": "unreachable", "error": str(e)}
    
    def test_grafana(self) -> Dict[str, Any]:
        """测试Grafana"""
        print("🔍 测试Grafana...")
        
        try:
            # 测试登录页面
            response = requests.get(f"{self.services['grafana']}/login", timeout=10)
            if response.status_code == 200:
                print("✅ Grafana正常")
                return {"status": "healthy"}
            else:
                print(f"⚠️  Grafana响应异常: {response.status_code}")
                return {"status": "unhealthy", "status_code": response.status_code}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Grafana不可访问: {e}")
            return {"status": "unreachable", "error": str(e)}
    
    def test_elasticsearch(self) -> Dict[str, Any]:
        """测试Elasticsearch"""
        print("🔍 测试Elasticsearch...")
        
        try:
            # 测试集群健康状态
            response = requests.get(f"{self.services['elasticsearch']}/_cluster/health", timeout=10)
            if response.status_code == 200:
                health = response.json()
                status = health.get('status', 'unknown')
                nodes = health.get('number_of_nodes', 0)
                print(f"✅ Elasticsearch正常，状态: {status}，节点数: {nodes}")
                return {"status": "healthy", "cluster_status": status, "nodes": nodes}
            else:
                print(f"⚠️  Elasticsearch响应异常: {response.status_code}")
                return {"status": "unhealthy", "status_code": response.status_code}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Elasticsearch不可访问: {e}")
            return {"status": "unreachable", "error": str(e)}
    
    def test_kibana(self) -> Dict[str, Any]:
        """测试Kibana"""
        print("🔍 测试Kibana...")
        
        try:
            # 测试状态API
            response = requests.get(f"{self.services['kibana']}/api/status", timeout=10)
            if response.status_code == 200:
                status = response.json()
                overall_status = status.get('status', {}).get('overall', {}).get('state', 'unknown')
                print(f"✅ Kibana正常，状态: {overall_status}")
                return {"status": "healthy", "overall_status": overall_status}
            else:
                print(f"⚠️  Kibana响应异常: {response.status_code}")
                return {"status": "unhealthy", "status_code": response.status_code}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Kibana不可访问: {e}")
            return {"status": "unreachable", "error": str(e)}
    
    def test_docker_services(self) -> Dict[str, Any]:
        """测试Docker服务状态"""
        print("🔍 测试Docker服务状态...")
        
        try:
            import subprocess
            result = subprocess.run(
                ['docker-compose', '-f', 'docker-compose.monitoring.yml', 'ps'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                services = []
                for line in lines[2:]:  # 跳过标题行
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 4:
                            service_name = parts[0]
                            status = parts[-1]
                            services.append({"name": service_name, "status": status})
                
                print(f"✅ Docker服务状态获取成功，服务数: {len(services)}")
                return {"status": "success", "services": services}
            else:
                print(f"⚠️  Docker命令执行失败: {result.stderr}")
                return {"status": "failed", "error": result.stderr}
                
        except Exception as e:
            print(f"❌ Docker服务状态检查失败: {e}")
            return {"status": "error", "error": str(e)}
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始监控系统测试...")
        print("=" * 50)
        
        # 测试Docker服务
        self.results['docker'] = self.test_docker_services()
        
        # 等待服务启动
        print("\n⏳ 等待服务完全启动...")
        time.sleep(10)
        
        # 测试各个服务
        self.results['prometheus'] = self.test_prometheus()
        self.results['grafana'] = self.test_grafana()
        self.results['elasticsearch'] = self.test_elasticsearch()
        self.results['kibana'] = self.test_kibana()
        
        return self.results
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 50)
        print("📊 监控系统测试总结")
        print("=" * 50)
        
        healthy_services = 0
        total_services = 0
        
        for service, result in self.results.items():
            total_services += 1
            status = result.get('status', 'unknown')
            
            if status in ['healthy', 'success']:
                healthy_services += 1
                print(f"✅ {service.capitalize()}: 正常")
            elif status in ['unhealthy', 'failed']:
                print(f"⚠️  {service.capitalize()}: 异常")
            else:
                print(f"❌ {service.capitalize()}: 不可访问")
        
        print(f"\n📈 总体状态: {healthy_services}/{total_services} 服务正常")
        
        if healthy_services == total_services:
            print("🎉 所有监控服务运行正常！")
        elif healthy_services > 0:
            print("⚠️  部分监控服务存在问题，请检查日志")
        else:
            print("❌ 监控系统启动失败，请检查配置和依赖")
        
        print("\n🔗 访问地址:")
        print("  - Grafana:      http://localhost:3001 (admin/admin123)")
        print("  - Prometheus:   http://localhost:9090")
        print("  - Kibana:       http://localhost:5601")
        print("  - Elasticsearch: http://localhost:9200")


def main():
    """主函数"""
    tester = MonitoringTester()
    
    try:
        results = tester.run_all_tests()
        tester.print_summary()
        
        # 保存测试结果
        with open('logs/monitoring_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📝 测试结果已保存到: logs/monitoring_test_results.json")
        
        # 根据测试结果设置退出码
        healthy_count = sum(1 for r in results.values() if r.get('status') in ['healthy', 'success'])
        if healthy_count == len(results):
            sys.exit(0)  # 所有服务正常
        else:
            sys.exit(1)  # 存在问题
            
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
