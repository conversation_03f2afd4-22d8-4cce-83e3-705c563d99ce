services:
  # Prometheus - 指标收集
  prometheus:
    image: prom/prometheus:latest
    container_name: monit-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monit-network
      - monitoring-network
    restart: unless-stopped

  # Grafana - 监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: monit-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - monitoring-network
    restart: unless-stopped

  # Elasticsearch - 日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: monit-elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Logstash - 日志处理
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: monit-logstash
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: "-Xmx512m -Xms512m"
    volumes:
      - ./monitoring/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - monitoring-network
    restart: unless-stopped

  # Kibana - 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: monit-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=
    volumes:
      - ./monitoring/kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - monitoring-network
    restart: unless-stopped

  # Filebeat - 日志收集
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: monit-filebeat
    user: root
    volumes:
      - ./monitoring/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/monit:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - monitoring-network
    restart: unless-stopped

  # Node Exporter - 系统指标
  node-exporter:
    image: prom/node-exporter:latest
    container_name: monit-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring-network
    restart: unless-stopped

  # Redis Exporter - Redis指标 (需要先启动主应用的Redis)
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: monit-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://host.docker.internal:6379
    networks:
      - monitoring-network
    restart: unless-stopped
    profiles:
      - with-redis

volumes:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  monitoring-network:
    driver: bridge
  monit-network:
    external: true
