#!/usr/bin/env python3
"""
测试修正后的后端配置
验证只包含系统级配置的新模型
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.services.backend_config_service import backend_config_service
from app.schemas.backend_config import BackendConfigCreate

async def test_create_system_config():
    """测试创建只包含系统级配置的后端配置"""
    
    config_data = {
        "backend_name": "系统级配置测试v2",
        "description": "只包含系统级配置的后端配置测试v2",
        "api_endpoint": "http://system-test-v2:8080",
        "auth_config": {
            "auth_type": "api_key",
            "api_key": "system-test-key"
        },
        "performance_config": {
            "max_concurrent_tasks": 5,
            "batch_timeout": 300,
            "api_timeout": 30000,
            "api_max_retries": 3,
            "api_retry_delay": 1000,
            "memory_limit_mb": 1024,
            "cpu_limit_percent": 80,
            "rate_limit_requests": 100,
            "rate_limit_period": 60
        },
        "callback_config": {
            "default_callback_url": "http://backend:8000/api/v1/crawler/callback",
            "callback_timeout": 30000,
            "callback_retries": 3,
            "callback_verify_ssl": True
        },
        "monitoring_config": {
            "health_check_enabled": True,
            "health_check_interval": 60,
            "health_check_timeout": 10000,
            "response_time_threshold": 30000,
            "error_rate_threshold": 0.1,
            "alert_on_system_failure": True,
            "system_alert_webhook": "http://monitoring:9093/api/v1/alerts",
            "failure_threshold": 3,
            "recovery_threshold": 2
        },
        "weight": 5,
        "priority": 2,
        "performance_level": "medium"
    }
    
    try:
        config = BackendConfigCreate(**config_data)
        result = await backend_config_service.create_config(config)
        print(f"✅ 创建系统级配置成功: {result.backend_name}")
        print(f"   - 配置ID: {result.backend_id}")
        print(f"   - 最大并发任务数: {result.performance_config.max_concurrent_tasks}")
        print(f"   - 批次超时: {result.performance_config.batch_timeout}秒")
        print(f"   - API超时: {result.performance_config.api_timeout}ms")
        print(f"   - 默认回调URL: {result.callback_config.default_callback_url}")
        print(f"   - 健康检查启用: {result.monitoring_config.health_check_enabled}")
        print(f"   - 系统告警Webhook: {result.monitoring_config.system_alert_webhook}")
        
        # 测试获取配置详情
        try:
            config_detail = await backend_config_service.get_config(result.backend_id)
            if config_detail:
                print(f"\n📊 配置详情验证:")
                print(f"   - 最大并发任务数: {config_detail.performance_config.max_concurrent_tasks}")
                print(f"   - 有回调配置: {bool(config_detail.callback_config.default_callback_url)}")
                print(f"   - 健康检查启用: {config_detail.monitoring_config.health_check_enabled}")
                print(f"   - API超时: {config_detail.performance_config.api_timeout}ms")
                print(f"   - 批次超时: {config_detail.performance_config.batch_timeout}秒")
        except Exception as e:
            print(f"⚠️ 获取配置详情失败: {e}")
        
        return result
        
    except Exception as e:
        print(f"❌ 创建系统级配置失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主函数"""
    print("🧪 开始测试修正后的系统级配置...")
    
    result = await test_create_system_config()
    
    if result:
        print(f"\n✅ 测试完成！系统级配置工作正常。")
        print(f"\n📋 配置说明:")
        print(f"   - performance_config: 系统性能参数（并发数、超时等）")
        print(f"   - callback_config: 系统级回调配置")
        print(f"   - monitoring_config: 系统级监控配置")
        print(f"\n🎯 这些配置属于'系统配置'范畴，用于CrawlerRequestBuilder的系统级参数")
    else:
        print(f"\n❌ 测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
