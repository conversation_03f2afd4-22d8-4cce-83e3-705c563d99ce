import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Descriptions,
  Alert,
  Typography,
  Select,
  DatePicker,
  Row,
  Col,
  Collapse,
  message,
  Badge,
  Tooltip
} from 'antd';
import {
  BugOutlined,
  EyeOutlined,
  CopyOutlined,
  ReloadOutlined,
  PlayCircleOutlined,

  FilterOutlined,
  ClearOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface CrawlerRequest {
  id: string;
  taskId: string;
  url: string;
  workerId: string;
  celeryWorker?: string;
  timestamp: string;
  method: string;
  headers: Record<string, string>;
  payload: any;
  response?: {
    status: number;
    responseTime: number;
    data: any;
  };
  error?: string;
  status: 'pending' | 'sent' | 'success' | 'failed';
}

interface CrawlerWorkerInfo {
  worker_id: string;
  status: 'online' | 'offline' | 'busy';
  capabilities: string[];
  lastSeen: string;
}

const CrawlerRequestPanel: React.FC = React.memo(() => {
  console.log('🔍 [DEBUG] CrawlerRequestPanel: Component rendering/re-rendering');

  const [requests, setRequests] = useState<CrawlerRequest[]>([]);
  const [workers, setWorkers] = useState<CrawlerWorkerInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<CrawlerRequest | null>(null);
  // const [isLiveMode, setIsLiveMode] = useState(true); // 默认开启实时监听 - 暂时不使用
  const [filters, setFilters] = useState({
    workerId: '',
    status: '',
    timeRange: null as [any, any] | null
  });



  // 智能合并请求数据，避免列表重载
  const mergeRequestData = (newRequests: CrawlerRequest[], isLiveUpdate: boolean = false) => {
    console.log('🔍 [DEBUG] mergeRequestData called:', {
      newRequestsCount: newRequests?.length || 0,
      isLiveUpdate,
      currentRequestsCount: requests.length
    });

    setRequests(prevRequests => {
      console.log('🔍 [DEBUG] setRequests callback:', {
        prevRequestsCount: prevRequests.length,
        newRequestsCount: newRequests?.length || 0,
        isLiveUpdate
      });

      // 如果没有新数据，保持原有数据不变
      if (!newRequests || newRequests.length === 0) {
        console.log('🔍 [DEBUG] No new requests, keeping previous data');
        return prevRequests;
      }

      // 对于实时更新，只添加真正的新请求
      if (isLiveUpdate && prevRequests.length > 0) {
        const existingIds = new Set(prevRequests.map(req => req.id));
        const newRequestsOnly = newRequests.filter(req => !existingIds.has(req.id));

        if (newRequestsOnly.length === 0) {
          console.log('🔍 [DEBUG] Live update: No new requests found, keeping previous data');
          return prevRequests; // 没有新请求，保持原有数据
        }

        console.log('🔍 [DEBUG] Live update: Adding', newRequestsOnly.length, 'new requests');
        // 只添加新请求到列表顶部
        const updatedRequests = [...newRequestsOnly, ...prevRequests]
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          .slice(0, 100);

        return updatedRequests;
      }

      // 对于手动刷新，进行完整的数据替换
      console.log('🔍 [DEBUG] Manual refresh: Replacing all data with', newRequests.length, 'requests');
      const sortedRequests = newRequests
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 100);

      return sortedRequests;
    });
  };

  // 获取请求历史
  const fetchRequestHistory = useCallback(async (isLiveUpdate: boolean = false) => {
    console.log('🔍 [DEBUG] fetchRequestHistory called:', { isLiveUpdate });

    try {
      // 手动刷新时显示loading，自动刷新时不显示
      if (!isLiveUpdate) {
        console.log('🔍 [DEBUG] Setting loading to true for manual refresh');
        setLoading(true);
      }



      const params = new URLSearchParams();
      if (filters.workerId) params.append('worker_id', filters.workerId);
      if (filters.status) params.append('status', filters.status);
      if (filters.timeRange && filters.timeRange[0]) {
        params.append('start_time', filters.timeRange[0].toISOString());
      }
      if (filters.timeRange && filters.timeRange[1]) {
        params.append('end_time', filters.timeRange[1].toISOString());
      }
      params.append('limit', '100');

      const response = await fetch(`/api/v1/crawler-debug/requests?${params.toString()}`);
      if (response.ok) {
        const rawData = await response.json();
        
        const data = rawData.map((item: any) => ({
          id: item.id,
          taskId: item.task_id,
          url: item.url,
          workerId: item.worker_id,
          celeryWorker: item.celery_worker,
          timestamp: item.timestamp,
          method: item.method,
          headers: item.headers,
          payload: item.payload,
          response: item.response,
          error: item.error,
          status: item.status
        }));
        
        mergeRequestData(data, isLiveUpdate);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to fetch request history:', error);
      if (!isLiveUpdate) {
        message.error('获取请求历史失败');
        mergeRequestData([], isLiveUpdate);
      }
    } finally {
      if (!isLiveUpdate) {
        setLoading(false);
      }
      if (isFirstLoad) {
        setIsFirstLoad(false);
      }
    }
  }, [filters, isFirstLoad]);

  // 获取Worker信息
  const fetchWorkers = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/crawler-debug/workers');
      if (response.ok) {
        const data = await response.json();
        setWorkers(data);
      } else {
        setWorkers([]);
      }
    } catch (error) {
      console.error('Failed to fetch workers:', error);
      setWorkers([]);
    }
  }, []);

  // 实时监听功能已集成到useEffect中，无需手动控制

  useEffect(() => {
    return () => {
      if ((window as any).crawlerRequestInterval) {
        clearInterval((window as any).crawlerRequestInterval);
        (window as any).crawlerRequestInterval = null;
      }
    };
  }, []);

  // 复制请求信息
  const copyRequest = (request: CrawlerRequest) => {
    const requestInfo = {
      url: request.url,
      method: request.method,
      headers: request.headers,
      payload: request.payload
    };
    navigator.clipboard.writeText(JSON.stringify(requestInfo, null, 2));
    message.success('请求信息已复制到剪贴板');
  };

  // 初始化数据
  useEffect(() => {
    console.log('🔍 [DEBUG] CrawlerRequestPanel: Initial data loading useEffect triggered');
    fetchWorkers();
    fetchRequestHistory();
  }, [fetchWorkers, fetchRequestHistory]);

  // 自动刷新逻辑（独立的useEffect）
  useEffect(() => {
    console.log('🔍 [DEBUG] CrawlerRequestPanel: Auto-refresh useEffect setup');
    const interval = setInterval(() => {
      if (!selectedRequest) {
        console.log('🔍 [DEBUG] Auto-refresh: Fetching request history (live update)');
        fetchRequestHistory(true);
      }
    }, 3000);

    (window as any).crawlerRequestInterval = interval;

    return () => {
      console.log('🔍 [DEBUG] CrawlerRequestPanel: Auto-refresh interval cleared');
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [selectedRequest, fetchRequestHistory]);

  // 请求列表表格列定义
  const requestColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (timestamp: string) => new Date(timestamp).toLocaleTimeString()
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 200,
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code style={{ fontSize: '11px' }}>
            {url.length > 30 ? `${url.substring(0, 30)}...` : url}
          </Text>
        </Tooltip>
      )
    },
    {
      title: 'Celery Worker',
      dataIndex: 'celeryWorker',
      key: 'celeryWorker',
      width: 120,
      render: (celeryWorker: string) => (
        <Tag color="purple">{celeryWorker || '未知'}</Tag>
      )
    },
    {
      title: '指派爬虫Worker',
      dataIndex: 'workerId',
      key: 'workerId',
      width: 140,
      render: (workerId: string) => (
        <Tooltip title={`指派给监控任务的爬虫Worker: ${workerId}`}>
          <Tag color="blue">{workerId || '未分配'}</Tag>
        </Tooltip>
      )
    },
    {
      title: '监控任务ID',
      dataIndex: 'taskId',
      key: 'taskId',
      width: 120,
      render: (taskId: string) => {
        // 如果是UUID格式，显示前8位；如果是数字，显示监控-数字格式
        const displayId = taskId && taskId.includes('-')
          ? taskId.split('-')[0] // UUID的第一部分
          : taskId ? `监控-${taskId.slice(-8)}` : '-';

        return (
          <Tooltip title={`完整监控任务ID: ${taskId}\n这是监控任务的内部ID，用于关联爬虫请求与具体的监控任务`}>
            <Text code style={{ fontSize: '11px', color: '#1890ff' }}>
              {displayId}
            </Text>
          </Tooltip>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colorMap = {
          pending: 'orange',
          sent: 'blue',
          success: 'green',
          failed: 'red'
        };
        return <Tag color={colorMap[status as keyof typeof colorMap]}>{status}</Tag>;
      }
    },
    {
      title: '响应时间',
      dataIndex: ['response', 'responseTime'],
      key: 'responseTime',
      width: 80,
      render: (time: number) => time ? `${time}ms` : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: CrawlerRequest) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => setSelectedRequest(record)}
          />
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyRequest(record)}
          />
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 功能说明 */}
      <Alert
        message="爬虫请求调试"
        description="监控Celery Worker发送给爬虫后端Worker的请求详情，便于调试不同版本爬虫Worker的兼容性问题。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={18}>
          <Card title="过滤条件" size="small">
            <Row gutter={8}>
              <Col span={6}>
                <Select
                  placeholder="选择Worker"
                  value={filters.workerId}
                  onChange={(value) => setFilters(prev => ({ ...prev, workerId: value }))}
                  allowClear
                  size="small"
                  style={{ width: '100%' }}
                >
                  {workers.map(worker => (
                    <Option key={worker.worker_id} value={worker.worker_id}>
                      {worker.worker_id}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <Select
                  placeholder="状态"
                  value={filters.status}
                  onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                  allowClear
                  size="small"
                  style={{ width: '100%' }}
                >
                  <Option value="pending">Pending</Option>
                  <Option value="sent">Sent</Option>
                  <Option value="success">Success</Option>
                  <Option value="failed">Failed</Option>
                </Select>
              </Col>
              <Col span={6}>
                <RangePicker
                  showTime
                  value={filters.timeRange}
                  onChange={(dates) => setFilters(prev => ({ ...prev, timeRange: dates }))}
                  size="small"
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={4}>
                <Button
                  icon={<FilterOutlined />}
                  onClick={() => fetchRequestHistory(false)}
                  loading={loading}
                  size="small"
                  block
                >
                  应用
                </Button>
              </Col>
              <Col span={4}>
                <Button
                  icon={<ClearOutlined />}
                  onClick={() => {
                    setFilters({
                      workerId: '',
                      status: '',
                      timeRange: null
                    });
                    setTimeout(() => fetchRequestHistory(false), 100);
                  }}
                  size="small"
                  block
                >
                  清空
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={6}>
          <Card title="实时监听" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Tag color="green" icon={<PlayCircleOutlined />}>
                  自动监听中
                </Tag>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchRequestHistory(false)}
                  loading={loading}
                  size="small"
                >
                  手动刷新
                </Button>
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                每3秒自动更新数据
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 请求列表 */}
      <Card
        key="crawler-requests-card"
        title="爬虫请求记录"
        size="small"
      >
        <Table
          columns={requestColumns}
          dataSource={requests}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 8,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 800 }}
          size="small"
          style={{ backgroundColor: 'white' }}
        />
      </Card>

      {/* 请求详情模态框 */}
      <Modal
        title={
          <Space>
            <BugOutlined />
            <span>请求详情</span>
            {selectedRequest && (
              <Tag color={
                selectedRequest.status === 'success' ? 'green' :
                selectedRequest.status === 'failed' ? 'red' :
                selectedRequest.status === 'sent' ? 'blue' : 'orange'
              }>
                {selectedRequest.status}
              </Tag>
            )}
          </Space>
        }
        open={!!selectedRequest}
        onCancel={() => setSelectedRequest(null)}
        footer={[
          <Button key="copy" icon={<CopyOutlined />} onClick={() => selectedRequest && copyRequest(selectedRequest)}>
            复制请求信息
          </Button>,
          <Button key="close" onClick={() => setSelectedRequest(null)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedRequest && (
          <Collapse defaultActiveKey={['request', 'debug']}>
            <Panel header="基本信息" key="request">
              <Descriptions column={2} size="small">
                <Descriptions.Item label="请求URL">{selectedRequest.url}</Descriptions.Item>
                <Descriptions.Item label="HTTP方法">{selectedRequest.method}</Descriptions.Item>
                <Descriptions.Item label="指派爬虫Worker">{selectedRequest.workerId || '未分配'}</Descriptions.Item>
                <Descriptions.Item label="Celery Worker">{selectedRequest.celeryWorker || '未知'}</Descriptions.Item>
                <Descriptions.Item label="监控任务ID">
                  <Tooltip title="这是监控任务的内部ID，用于关联爬虫请求与具体的监控任务">
                    <Text code style={{ color: '#1890ff' }}>{selectedRequest.taskId || '未知'}</Text>
                  </Tooltip>
                </Descriptions.Item>
                <Descriptions.Item label="请求时间">{new Date(selectedRequest.timestamp).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="请求状态">
                  <Badge 
                    color={
                      selectedRequest.status === 'success' ? 'green' :
                      selectedRequest.status === 'failed' ? 'red' :
                      selectedRequest.status === 'sent' ? 'blue' : 'orange'
                    } 
                    text={selectedRequest.status} 
                  />
                </Descriptions.Item>
                {selectedRequest.response?.responseTime && (
                  <Descriptions.Item label="响应时间">{selectedRequest.response.responseTime}ms</Descriptions.Item>
                )}
              </Descriptions>
            </Panel>
            
            <Panel header="调试信息" key="debug">
              <Alert
                message="请求链路跟踪"
                description={
                  <div style={{ marginTop: 8 }}>
                    <Text>
                      <strong>Celery Worker:</strong> {selectedRequest.celeryWorker || '未知'} → 
                      <strong> 爬虫Worker:</strong> {selectedRequest.workerId || '未知'} → 
                      <strong> 目标URL:</strong> {selectedRequest.url}
                    </Text>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              {selectedRequest.error && (
                <Alert
                  message="错误信息"
                  description={selectedRequest.error}
                  type="error"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}
            </Panel>
          </Collapse>
        )}
      </Modal>
    </div>
  );
}, () => {
  // 永远返回true，表示props没有变化，防止父组件状态变化导致重新渲染
  // 因为CrawlerRequestPanel不接收任何props，所以可以安全地这样做
  console.log('🔍 [DEBUG] CrawlerRequestPanel React.memo comparison function called');
  console.log('🔍 [DEBUG] Returning true to prevent re-render');
  return true;
});

export default CrawlerRequestPanel;
