#!/usr/bin/env python3
"""
演示增强的监控和日志功能
展示爬虫请求构建过程的详细监控和日志记录
"""

import asyncio
import json
import time
from datetime import datetime
from unittest.mock import patch, MagicMock, AsyncMock

# 设置路径以便导入
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.services.crawler_request_monitor import CrawlerRequestMonitor, RequestBuildMetrics
from app.core.enhanced_logging import setup_enhanced_logging, StructuredLogger


async def demo_monitoring_features():
    """演示监控功能"""
    
    print("🔍 爬虫请求构建监控功能演示")
    print("=" * 60)
    
    # 1. 设置增强日志
    print("\n📝 1. 设置增强日志配置")
    setup_enhanced_logging("DEBUG", "logs/crawler_request_demo.log")
    print("  ✅ 增强日志配置完成")
    
    # 2. 创建监控器
    print("\n📊 2. 创建监控器实例")
    monitor = CrawlerRequestMonitor()
    print(f"  ✅ 监控器创建完成")
    print(f"  📋 配置: 指标保留 {monitor.metrics_retention_days} 天")
    print(f"  ⚠️ 性能阈值: {monitor.performance_thresholds}")
    
    # 3. 演示监控上下文管理器
    print("\n🔄 3. 演示监控上下文管理器")
    
    task_id = "demo-task-12345"
    execution_id = f"demo-exec-{int(time.time())}"
    context = {
        "demo": True,
        "source": "monitoring_demo",
        "client_ip": "*************"
    }
    
    try:
        async with monitor.monitor_request_building(task_id, execution_id, context) as metrics:
            print(f"  🚀 开始监控任务: {task_id}")
            
            # 模拟URL获取过程
            print("  🌐 模拟URL获取...")
            await asyncio.sleep(0.5)  # 模拟0.5秒的URL获取时间
            metrics.url_fetch_time = 0.5
            metrics.urls_count = 15
            
            await monitor.log_data_flow_step(
                "获取活跃URL", task_id, 
                ["https://example.com/1", "https://example.com/2"], 
                0.5
            )
            
            # 模拟配置获取过程
            print("  ⚙️ 模拟配置获取...")
            await asyncio.sleep(0.3)  # 模拟0.3秒的配置获取时间
            metrics.config_fetch_time = 0.3
            metrics.config_completeness = 0.9
            metrics.config_source = "任务配置 (4/4 字段)"
            
            await monitor.log_data_flow_step(
                "获取爬取配置", task_id,
                {"llm_query": "提取商品信息", "priority": "high"},
                0.3
            )
            
            # 模拟请求构建过程
            print("  🔨 模拟请求构建...")
            await asyncio.sleep(0.2)  # 模拟0.2秒的请求构建时间
            metrics.request_build_time = 0.2
            
            # 模拟构建的请求
            demo_request = {
                "request": {
                    "urls": ["https://example.com/1", "https://example.com/2"],
                    "q": "提取商品标题和价格",
                    "priority": "high",
                    "cache": True
                },
                "auth_context": {
                    "user_id": "monit_system",
                    "auth_method": "api_key"
                }
            }
            
            await monitor.log_request_structure(demo_request, task_id)
            
            print(f"  ✅ 监控完成，总耗时: {time.time() - metrics.start_time:.3f}s")
            
    except Exception as e:
        print(f"  ❌ 监控过程出错: {e}")
    
    # 4. 演示性能阈值检查
    print("\n⚠️ 4. 演示性能阈值检查")
    
    # 创建超过阈值的指标
    slow_metrics = RequestBuildMetrics(
        task_id="slow-task-demo",
        execution_id="slow-exec-demo",
        start_time=time.time()
    )
    
    # 设置超过阈值的值
    slow_metrics.url_fetch_time = 3.5  # 超过2.0秒阈值
    slow_metrics.config_fetch_time = 1.8  # 超过1.0秒阈值
    slow_metrics.duration = 6.2  # 超过5.0秒阈值
    slow_metrics.urls_count = 120  # 超过100个阈值
    
    print("  📊 模拟性能指标:")
    print(f"    • URL获取时间: {slow_metrics.url_fetch_time}s (阈值: 2.0s)")
    print(f"    • 配置获取时间: {slow_metrics.config_fetch_time}s (阈值: 1.0s)")
    print(f"    • 总构建时间: {slow_metrics.duration}s (阈值: 5.0s)")
    print(f"    • URL数量: {slow_metrics.urls_count} (阈值: 100)")
    
    await monitor._check_performance_thresholds(slow_metrics)
    print("  ✅ 性能阈值检查完成（查看上方的警告日志）")
    
    # 5. 演示错误建议
    print("\n💡 5. 演示错误建议功能")
    
    error_cases = [
        ("ValueError", "Task has no active URLs"),
        ("ValueError", "Task has no crawl config"),
        ("ConnectionError", "Redis connection timeout"),
        ("TimeoutError", "Database query timeout")
    ]
    
    for error_type, error_message in error_cases:
        suggestions = monitor._get_error_suggestions(error_type, error_message)
        print(f"  🐛 错误: {error_type} - {error_message}")
        print(f"  💡 建议解决方案:")
        for suggestion in suggestions:
            print(f"    • {suggestion}")
        print()
    
    # 6. 演示结构化日志
    print("\n📋 6. 演示结构化日志功能")
    
    structured_logger = StructuredLogger("demo.crawler_request")
    
    # 记录请求构建开始
    structured_logger.log_request_building_start(
        task_id="demo-structured-task",
        execution_id="demo-structured-exec",
        context={"demo": True, "user": "admin"}
    )
    
    # 记录数据源访问
    structured_logger.log_data_source_access(
        source_type="url_pool",
        task_id="demo-structured-task",
        data_info={"urls_found": 25, "active_urls": 20},
        duration=1.2
    )
    
    # 记录性能警告
    structured_logger.log_performance_warning(
        task_id="demo-structured-task",
        warning_type="url_fetch_slow",
        threshold=2.0,
        actual_value=3.5
    )
    
    # 记录请求构建完成
    structured_logger.log_request_building_complete(
        task_id="demo-structured-task",
        execution_id="demo-structured-exec",
        urls_count=20,
        duration=2.8,
        status="success"
    )
    
    print("  ✅ 结构化日志演示完成")
    
    # 7. 生成监控报告
    print("\n📊 7. 生成监控报告")
    
    # 模拟一些历史指标数据
    with patch.object(monitor, 'get_task_metrics') as mock_get_metrics:
        mock_metrics = [
            {
                "task_id": "demo-task-12345",
                "execution_id": "exec-1",
                "start_time": time.time() - 3600,
                "duration": 2.5,
                "status": "success",
                "urls_count": 15
            },
            {
                "task_id": "demo-task-12345", 
                "execution_id": "exec-2",
                "start_time": time.time() - 1800,
                "duration": 3.2,
                "status": "success",
                "urls_count": 20
            },
            {
                "task_id": "demo-task-12345",
                "execution_id": "exec-3", 
                "start_time": time.time() - 900,
                "duration": 1.8,
                "status": "validation_error",
                "urls_count": 0,
                "error_message": "Task has no active URLs"
            }
        ]
        
        mock_get_metrics.return_value = mock_metrics
        
        # 生成报告
        report = await monitor.generate_monitoring_report("demo-task-12345")
        print("📋 监控报告:")
        print(report)
    
    # 8. 演示指标摘要
    print("\n📈 8. 演示指标摘要")
    
    with patch.object(monitor, 'get_metrics_summary') as mock_get_summary:
        mock_summary = {
            "period": "最近 1 天",
            "total_requests": 10,
            "success_rate": "80.0%",
            "status_breakdown": {
                "successful": 8,
                "validation_errors": 1,
                "api_errors": 1,
                "failed": 0
            },
            "performance": {
                "avg_duration": "2.450s",
                "max_duration": "4.200s", 
                "min_duration": "1.100s"
            },
            "url_statistics": {
                "avg_urls_per_request": "18.5",
                "max_urls_per_request": 35
            }
        }
        
        mock_get_summary.return_value = mock_summary
        
        summary = await monitor.get_metrics_summary(1)
        print("📊 指标摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))
    
    # 清理
    await monitor.close()
    
    print("\n🎉 监控功能演示完成！")
    print("\n✨ 监控功能特点:")
    print("  • 详细的性能指标追踪")
    print("  • 智能的性能阈值检查")
    print("  • 结构化的日志记录")
    print("  • 完整的数据流可视化")
    print("  • 错误分析和建议")
    print("  • 历史指标统计和报告")


async def demo_api_endpoints():
    """演示监控API端点"""
    
    print("\n🌐 监控API端点演示")
    print("=" * 40)
    
    # 模拟API调用
    api_endpoints = [
        {
            "method": "GET",
            "path": "/api/v1/monitoring/crawler-request-metrics/summary?days=7",
            "description": "获取7天的爬虫请求指标摘要"
        },
        {
            "method": "GET", 
            "path": "/api/v1/monitoring/crawler-request-metrics/task/task-123",
            "description": "获取特定任务的指标历史"
        },
        {
            "method": "GET",
            "path": "/api/v1/monitoring/crawler-request-metrics/report/task-123",
            "description": "获取任务的监控报告"
        },
        {
            "method": "GET",
            "path": "/api/v1/monitoring/crawler-request-metrics/performance-analysis?days=7",
            "description": "获取性能分析报告"
        },
        {
            "method": "GET",
            "path": "/api/v1/monitoring/system-health",
            "description": "获取系统健康状态"
        }
    ]
    
    print("📋 可用的监控API端点:")
    for endpoint in api_endpoints:
        print(f"  {endpoint['method']} {endpoint['path']}")
        print(f"    📝 {endpoint['description']}")
        print()
    
    print("💡 使用示例:")
    print("""
# 获取指标摘要
curl "http://localhost:8000/api/v1/monitoring/crawler-request-metrics/summary?days=7"

# 获取任务指标
curl "http://localhost:8000/api/v1/monitoring/crawler-request-metrics/task/monitoring-task-123"

# 获取监控报告
curl "http://localhost:8000/api/v1/monitoring/crawler-request-metrics/report/monitoring-task-123"

# 获取性能分析
curl "http://localhost:8000/api/v1/monitoring/crawler-request-metrics/performance-analysis?days=7"

# 检查系统健康
curl "http://localhost:8000/api/v1/monitoring/system-health"
""")


async def demo_integration_with_crawler_request_builder():
    """演示与CrawlerRequestBuilder的集成"""
    
    print("\n🔗 与CrawlerRequestBuilder集成演示")
    print("=" * 50)
    
    # 模拟CrawlerRequestBuilder的调用
    task_id = "integration-demo-task"
    execution_context = {
        "execution_id": f"integration-demo-{int(time.time())}",
        "source": "demo_integration",
        "client_ip": "*************"
    }
    
    print(f"📋 任务ID: {task_id}")
    print(f"📋 执行上下文: {json.dumps(execution_context, indent=2)}")
    
    # 模拟监控过程
    monitor = CrawlerRequestMonitor()
    
    with patch('app.services.crawler_request_monitor.redis') as mock_redis:
        # 模拟Redis客户端
        mock_redis_client = AsyncMock()
        mock_redis.from_url.return_value = mock_redis_client
        
        try:
            async with monitor.monitor_request_building(
                task_id, execution_context["execution_id"], execution_context
            ) as metrics:
                
                print("  🔄 模拟数据流步骤...")
                
                # 步骤1: 获取URL
                await monitor.log_data_flow_step(
                    "获取活跃URL", task_id,
                    ["https://articulo.mercadolibre.com.mx/MLM-1234567890-demo"],
                    0.8
                )
                metrics.url_fetch_time = 0.8
                metrics.urls_count = 1
                
                # 步骤2: 获取配置
                await monitor.log_data_flow_step(
                    "获取爬取配置", task_id,
                    {"llm_query": "提取商品信息", "priority": "high"},
                    0.4
                )
                metrics.config_fetch_time = 0.4
                metrics.config_completeness = 1.0
                
                # 步骤3: 构建请求
                demo_request = {
                    "request": {
                        "urls": ["https://articulo.mercadolibre.com.mx/MLM-1234567890-demo"],
                        "q": "提取商品标题、价格、库存信息",
                        "priority": "high",
                        "cache": True
                    },
                    "auth_context": {
                        "user_id": "monit_system",
                        "auth_method": "api_key"
                    }
                }
                
                await monitor.log_request_structure(demo_request, task_id)
                metrics.request_build_time = 0.3
                
                print("  ✅ 集成演示成功完成")
                
        except Exception as e:
            print(f"  ❌ 集成演示失败: {e}")
    
    await monitor.close()


async def main():
    """主函数"""
    
    print("🎯 爬虫请求构建增强监控和日志演示")
    print("🕐 开始时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    try:
        # 演示监控功能
        await demo_monitoring_features()
        
        # 演示API端点
        await demo_api_endpoints()
        
        # 演示集成功能
        await demo_integration_with_crawler_request_builder()
        
        print("\n🎉 所有演示完成！")
        print("\n📚 相关文档:")
        print("  • docs/crawler_request_generation_analysis.md - 技术分析")
        print("  • docs/crawler_request_builder_usage_guide.md - 使用指南")
        print("  • backend/app/services/crawler_request_monitor.py - 监控服务")
        print("  • backend/app/core/enhanced_logging.py - 增强日志")
        print("  • backend/app/api/monitoring_routes.py - 监控API")
        
    except Exception as e:
        print(f"\n❌ 演示过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
