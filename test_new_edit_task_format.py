#!/usr/bin/env python3
"""
测试新的编辑任务页面格式
"""

import asyncio
import aiohttp

API_BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

async def test_new_edit_task_format():
    """测试新的编辑任务页面格式"""
    
    async with aiohttp.ClientSession() as session:
        print("✏️ 测试新的编辑任务页面格式...")
        
        # 1. 获取任务列表，找到一个任务进行测试
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        task_status = tasks[0]['status']
                        is_running = tasks[0].get('is_running', False)
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                        print(f"   当前状态: {task_status}, 运行中: {is_running}")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 测试前端编辑页面是否可访问
        print(f"\n2. 测试编辑页面访问...")
        edit_url = f"{FRONTEND_URL}/monitoring/tasks/{task_id}/edit"
        try:
            async with session.get(edit_url) as response:
                if response.status == 200:
                    content = await response.text()
                    print(f"✅ 编辑页面可访问: {response.status}")
                    
                    # 检查页面内容是否包含关键元素
                    if "编辑任务" in content or "基础配置" in content:
                        print(f"✅ 页面包含编辑任务相关内容")
                    else:
                        print(f"⚠️ 页面内容可能有问题")
                else:
                    print(f"❌ 编辑页面访问失败: {response.status}")
        except Exception as e:
            print(f"❌ 编辑页面访问异常: {e}")
        
        # 3. 验证后端API支持
        print(f"\n3. 验证后端API支持...")
        try:
            # 测试获取任务详情API
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    task_detail = data.get('data', {})
                    print(f"✅ 任务详情API正常")
                    print(f"   任务名称: {task_detail.get('name')}")
                    print(f"   任务描述: {task_detail.get('description')}")
                    print(f"   调度类型: {task_detail.get('schedule', {}).get('type')}")
                    print(f"   执行时间: {task_detail.get('schedule', {}).get('time')}")
                    print(f"   平台: {task_detail.get('config', {}).get('platform')}")
                    print(f"   优先级: {task_detail.get('config', {}).get('priority')}")
                else:
                    print(f"❌ 任务详情API失败: {response.status}")
        except Exception as e:
            print(f"❌ 任务详情API异常: {e}")
        
        # 4. 检查前端编译状态
        print(f"\n4. 检查前端编译状态...")
        print(f"   请检查Docker日志确认编译状态:")
        print(f"   命令: docker logs monit-frontend --tail=5")
        
        print(f"\n🎉 新编辑任务格式测试完成!")
        print(f"\n📋 新格式特点:")
        print(f"   ✅ 使用步骤向导界面 - 与URL池创建任务一致")
        print(f"   ✅ 分步骤编辑 - 基础配置、调度配置、确认修改")
        print(f"   ✅ 页面跳转方式 - 不再使用弹窗")
        print(f"   ✅ 完整的编辑体验 - 包含变更对比和确认")
        print(f"   ✅ 运行状态感知 - 运行中任务的编辑限制")
        
        print(f"\n🌐 用户体验改进:")
        print(f"   ✅ 界面一致性 - 与创建任务使用相同格式")
        print(f"   ✅ 操作空间充足 - 不受弹窗尺寸限制")
        print(f"   ✅ 步骤清晰 - 分步骤引导用户完成编辑")
        print(f"   ✅ 变更可视化 - 清楚显示修改前后对比")
        print(f"   ✅ 安全确认 - 最后一步确认所有变更")
        
        print(f"\n📍 测试建议:")
        print(f"   1. 访问: {FRONTEND_URL}/monitoring")
        print(f"   2. 点击任务的编辑按钮")
        print(f"   3. 验证跳转到编辑页面: {edit_url}")
        print(f"   4. 测试步骤向导的各个步骤")
        print(f"   5. 验证编辑功能的完整性")
        
        print(f"\n🔄 编辑流程对比:")
        print(f"   修改前: 任务列表 → 编辑弹窗 → 简单表单")
        print(f"   修改后: 任务列表 → 编辑页面 → 步骤向导 → 变更确认")

if __name__ == "__main__":
    asyncio.run(test_new_edit_task_format())
