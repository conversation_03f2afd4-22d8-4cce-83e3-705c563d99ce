"""
测试爬虫请求监控服务
"""

import pytest
import asyncio
import time
from unittest.mock import patch, MagicMock, AsyncMock
from app.services.crawler_request_monitor import CrawlerRequestMonitor, RequestBuildMetrics


class TestCrawlerRequestMonitor:
    """测试爬虫请求监控器"""
    
    @pytest.mark.asyncio
    async def test_request_build_metrics(self):
        """测试请求构建指标"""
        
        # 创建指标对象
        metrics = RequestBuildMetrics(
            task_id="test-task-123",
            execution_id="exec-456",
            start_time=time.time()
        )
        
        # 测试初始状态
        assert metrics.task_id == "test-task-123"
        assert metrics.execution_id == "exec-456"
        assert metrics.status == "building"
        assert metrics.duration is None
        
        # 模拟设置性能指标
        metrics.url_fetch_time = 1.5
        metrics.config_fetch_time = 0.8
        metrics.urls_count = 25
        metrics.config_completeness = 0.9
        
        # 标记完成
        time.sleep(0.1)  # 模拟一些处理时间
        metrics.mark_completed("success")
        
        # 验证完成状态
        assert metrics.status == "success"
        assert metrics.end_time is not None
        assert metrics.duration is not None
        assert metrics.duration > 0
        
        # 测试转换为字典
        metrics_dict = metrics.to_dict()
        assert metrics_dict["task_id"] == "test-task-123"
        assert metrics_dict["status"] == "success"
        assert metrics_dict["urls_count"] == 25
    
    @pytest.mark.asyncio
    async def test_monitor_request_building_success(self):
        """测试监控请求构建成功场景"""
        
        monitor = CrawlerRequestMonitor()
        
        with patch.object(monitor, '_save_metrics') as mock_save, \
             patch.object(monitor, '_log_performance_analysis') as mock_log_perf, \
             patch.object(monitor, '_check_performance_thresholds') as mock_check_perf:
            
            mock_save.return_value = None
            mock_log_perf.return_value = None
            mock_check_perf.return_value = None
            
            task_id = "test-monitor-task"
            execution_id = "test-exec-123"
            context = {"test": "context"}
            
            # 使用监控上下文管理器
            async with monitor.monitor_request_building(task_id, execution_id, context) as metrics:
                # 模拟请求构建过程
                metrics.urls_count = 10
                metrics.url_fetch_time = 1.2
                metrics.config_fetch_time = 0.5
                metrics.config_completeness = 1.0
                
                # 模拟一些处理时间
                await asyncio.sleep(0.1)
            
            # 验证指标
            assert metrics.status == "success"
            assert metrics.urls_count == 10
            assert metrics.duration is not None
            assert metrics.duration > 0
            
            # 验证调用
            mock_save.assert_called_once_with(metrics)
            mock_log_perf.assert_called_once_with(metrics)
            mock_check_perf.assert_called_once_with(metrics)
    
    @pytest.mark.asyncio
    async def test_monitor_request_building_validation_error(self):
        """测试监控请求构建验证错误场景"""
        
        monitor = CrawlerRequestMonitor()
        
        with patch.object(monitor, '_save_metrics') as mock_save, \
             patch.object(monitor, '_log_performance_analysis') as mock_log_perf:
            
            mock_save.return_value = None
            mock_log_perf.return_value = None
            
            task_id = "test-validation-error"
            execution_id = "test-exec-456"
            
            # 使用监控上下文管理器，模拟验证错误
            with pytest.raises(ValueError, match="no active URLs"):
                async with monitor.monitor_request_building(task_id, execution_id) as metrics:
                    # 模拟验证错误
                    raise ValueError("Task has no active URLs")
            
            # 验证指标状态
            assert metrics.status == "validation_error"
            assert "no active URLs" in metrics.error_message
            
            # 验证调用
            mock_save.assert_called_once_with(metrics)
            mock_log_perf.assert_called_once_with(metrics)
    
    @pytest.mark.asyncio
    async def test_performance_threshold_checking(self):
        """测试性能阈值检查"""
        
        monitor = CrawlerRequestMonitor()
        
        # 创建超过阈值的指标
        metrics = RequestBuildMetrics(
            task_id="test-performance",
            execution_id="exec-perf",
            start_time=time.time()
        )
        
        # 设置超过阈值的值
        metrics.url_fetch_time = 3.0  # 超过2.0秒阈值
        metrics.config_fetch_time = 1.5  # 超过1.0秒阈值
        metrics.duration = 6.0  # 超过5.0秒阈值
        metrics.urls_count = 150  # 超过100个阈值
        
        # 测试阈值检查
        with patch('app.services.crawler_request_monitor.logger') as mock_logger:
            await monitor._check_performance_thresholds(metrics)
            
            # 验证警告日志被调用
            assert mock_logger.warning.call_count >= 2  # 至少有2个警告
            
            # 检查警告内容
            warning_calls = [call.args[0] for call in mock_logger.warning.call_args_list]
            assert any("性能警告" in call for call in warning_calls)
    
    @pytest.mark.asyncio
    async def test_log_data_flow_step(self):
        """测试数据流步骤日志"""
        
        monitor = CrawlerRequestMonitor()
        
        with patch('app.services.crawler_request_monitor.logger') as mock_logger:
            # 测试记录数据流步骤
            await monitor.log_data_flow_step(
                step_name="测试步骤",
                task_id="test-task",
                data=["url1", "url2", "url3"],
                duration=1.5
            )
            
            # 验证日志调用
            mock_logger.info.assert_called()
            info_call = mock_logger.info.call_args[0][0]
            assert "🔄 数据流步骤: 测试步骤" in info_call
            assert "test-task" in info_call
            assert "1.500s" in info_call
            
            # 验证调试日志
            mock_logger.debug.assert_called()
            debug_calls = [call.args[0] for call in mock_logger.debug.call_args_list]
            # 检查是否有包含数据信息的调试日志
            assert any("📊 数据:" in call for call in debug_calls)
    
    @pytest.mark.asyncio
    async def test_log_request_structure(self):
        """测试请求结构日志"""
        
        monitor = CrawlerRequestMonitor()
        
        # 模拟爬虫请求
        crawler_request = {
            "request": {
                "urls": ["url1", "url2"],
                "q": "测试查询",
                "schema_str": '{"type": "object"}',
                "cache": True,
                "priority": "high",
                "batch_name": "test-batch",
                "max_concurrent_tasks": 2,
                "batch_timeout": 300
            },
            "auth_context": {
                "user_id": "monit_system",
                "role": "user",
                "auth_method": "api_key",
                "api_key_prefix": "test_key_123",
                "client_ip": "*************",
                "user_agent": "MonIt-Test/1.0"
            }
        }
        
        with patch('app.services.crawler_request_monitor.logger') as mock_logger:
            await monitor.log_request_structure(crawler_request, "test-task")
            
            # 验证日志调用
            assert mock_logger.info.call_count >= 3  # 至少3个info调用
            assert mock_logger.debug.call_count >= 1  # 至少1个debug调用
            
            # 检查日志内容
            info_calls = [call.args[0] for call in mock_logger.info.call_args_list]
            assert any("📤 爬虫请求结构分析" in call for call in info_calls)
            assert any("🔍 request部分" in call for call in info_calls)
            assert any("🔐 auth_context部分" in call for call in info_calls)
    
    @pytest.mark.asyncio
    async def test_error_suggestions(self):
        """测试错误建议功能"""
        
        monitor = CrawlerRequestMonitor()
        
        # 测试不同类型的错误建议
        test_cases = [
            ("ValueError", "Task has no active URLs", ["检查URL池", "验证监控任务", "检查URL的启用"]),
            ("ValueError", "Task has no crawl config", ["确保监控任务包含", "验证爬取配置", "检查config_id"]),
            ("ConnectionError", "Redis connection failed", ["检查Redis连接", "验证网络连接", "检查服务依赖"]),
            ("TimeoutError", "Request timeout", ["增加超时时间", "检查系统负载", "优化数据库查询"])
        ]
        
        for error_type, error_message, expected_keywords in test_cases:
            suggestions = monitor._get_error_suggestions(error_type, error_message)
            
            # 验证建议不为空
            assert len(suggestions) > 0
            
            # 验证建议包含预期的关键词
            suggestions_text = " ".join(suggestions)
            for keyword in expected_keywords:
                assert any(keyword in suggestion for suggestion in suggestions), \
                    f"Expected keyword '{keyword}' not found in suggestions: {suggestions}"
    
    @pytest.mark.asyncio
    async def test_metrics_summary_empty(self):
        """测试空指标摘要"""
        
        monitor = CrawlerRequestMonitor()
        
        with patch.object(monitor, 'get_redis_client') as mock_get_redis:
            # 模拟Redis客户端
            mock_redis_client = AsyncMock()
            mock_redis_client.lrange.return_value = []  # 没有数据
            mock_get_redis.return_value = mock_redis_client
            
            # 获取指标摘要
            summary = await monitor.get_metrics_summary(1)
            
            # 验证结果
            assert "message" in summary
            assert "没有找到指标数据" in summary["message"]


if __name__ == "__main__":
    pytest.main([__file__])
