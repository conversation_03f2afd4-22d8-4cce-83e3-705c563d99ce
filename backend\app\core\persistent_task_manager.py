"""
持久化任务管理器

在TaskManager基础上增加Redis持久化功能，防止任务丢失
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

import redis.asyncio as redis

from app.core.task_manager import TaskManager, TaskManagerConfig, BatchExecution
from app.core.task_splitter import TaskBatch, TaskPriority

logger = logging.getLogger(__name__)


class PersistentTaskManager(TaskManager):
    """持久化任务管理器
    
    在原有TaskManager基础上增加Redis持久化功能：
    1. 任务队列持久化到Redis
    2. 服务重启时自动恢复状态
    3. 防止任务丢失和重复执行
    """
    
    def __init__(self, config: TaskManagerConfig, redis_url: str = "redis://redis:6379/0"):
        """初始化持久化任务管理器
        
        Args:
            config: 任务管理器配置
            redis_url: Redis连接URL
        """
        super().__init__(config)
        
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        
        # Redis键名定义
        self.pending_queue_key = "taskmanager:pending_batches"
        self.running_hash_key = "taskmanager:running_batches"
        self.completed_set_key = "taskmanager:completed_batches"
        self.failed_set_key = "taskmanager:failed_batches"
        self.manager_state_key = "taskmanager:state"
        
        # 持久化标志
        self.persistence_enabled = True
        
        self.logger = logging.getLogger(__name__)
    
    async def start(self) -> None:
        """启动持久化任务管理器"""
        try:
            # 连接Redis
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            self.logger.info("Connected to Redis for persistence")
            
            # 恢复状态
            await self._restore_state()
            
            # 启动基础TaskManager
            await super().start()
            
            self.logger.info("Persistent task manager started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start persistent task manager: {e}")
            # 如果Redis连接失败，降级为非持久化模式
            self.persistence_enabled = False
            self.logger.warning("Running in non-persistent mode due to Redis connection failure")
            await super().start()
    
    async def stop(self) -> None:
        """停止持久化任务管理器"""
        try:
            # 保存当前状态
            if self.persistence_enabled:
                await self._save_state()
            
            # 停止基础TaskManager
            await super().stop()
            
            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.close()
                self.logger.info("Redis connection closed")
            
        except Exception as e:
            self.logger.error(f"Error stopping persistent task manager: {e}")
            raise
    
    async def submit_task(
        self,
        task_id: int,
        urls: List[str],
        platform: str = "mercadolibre",
        priority: TaskPriority = TaskPriority.NORMAL,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """提交任务并持久化"""
        # 调用基础方法
        submission_id = await super().submit_task(task_id, urls, platform, priority, options)
        
        # 持久化新增的批次
        if self.persistence_enabled:
            await self._persist_pending_batches()
        
        return submission_id
    
    async def _start_batch(self, batch: TaskBatch) -> None:
        """启动批次并持久化状态"""
        # 调用基础方法
        await super()._start_batch(batch)
        
        # 持久化运行状态
        if self.persistence_enabled:
            await self._persist_running_batch(batch.batch_id)
            await self._remove_from_pending_queue(batch)
    
    async def _check_running_batches(self) -> None:
        """检查运行批次并持久化状态变化"""
        # 记录变化前的状态
        before_running = set(self.running_batches.keys())
        before_completed = len(self.completed_batches)
        before_failed = len(self.failed_batches)
        
        # 调用基础方法
        await super()._check_running_batches()
        
        # 持久化状态变化
        if self.persistence_enabled:
            after_running = set(self.running_batches.keys())
            after_completed = len(self.completed_batches)
            after_failed = len(self.failed_batches)
            
            # 处理完成的批次
            completed_batches = before_running - after_running
            for batch_id in completed_batches:
                await self._remove_from_running_hash(batch_id)
            
            # 持久化完成和失败的批次
            if after_completed > before_completed:
                new_completed = self.completed_batches[before_completed:]
                await self._persist_completed_batches(new_completed)
            
            if after_failed > before_failed:
                new_failed = self.failed_batches[before_failed:]
                await self._persist_failed_batches(new_failed)
    
    async def _restore_state(self) -> None:
        """从Redis恢复状态"""
        if not self.persistence_enabled or not self.redis_client:
            return
        
        try:
            # 恢复待处理队列
            pending_count = await self._restore_pending_batches()
            
            # 恢复运行状态
            running_count = await self._restore_running_batches()
            
            # 恢复完成和失败记录
            completed_count = await self._restore_completed_batches()
            failed_count = await self._restore_failed_batches()
            
            # 检查孤儿任务（Redis中有但Celery中已不存在的任务）
            orphaned_count = await self._check_orphaned_tasks()
            
            self.logger.info(
                f"State restored: {pending_count} pending, {running_count} running, "
                f"{completed_count} completed, {failed_count} failed, {orphaned_count} orphaned"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to restore state: {e}")
            # 清理可能损坏的状态
            await self._clear_persistent_state()
    
    async def _restore_pending_batches(self) -> int:
        """恢复待处理队列"""
        try:
            batch_data_list = await self.redis_client.lrange(self.pending_queue_key, 0, -1)
            
            for batch_data in batch_data_list:
                try:
                    batch_dict = json.loads(batch_data)
                    batch = self._dict_to_task_batch(batch_dict)
                    self.pending_batches.append(batch)
                except Exception as e:
                    self.logger.error(f"Failed to restore batch: {e}")
            
            return len(batch_data_list)
            
        except Exception as e:
            self.logger.error(f"Failed to restore pending batches: {e}")
            return 0
    
    async def _restore_running_batches(self) -> int:
        """恢复运行状态"""
        try:
            running_data = await self.redis_client.hgetall(self.running_hash_key)
            
            for batch_id, execution_data in running_data.items():
                try:
                    execution_dict = json.loads(execution_data)
                    execution = self._dict_to_batch_execution(execution_dict)
                    self.running_batches[batch_id] = execution
                except Exception as e:
                    self.logger.error(f"Failed to restore running batch {batch_id}: {e}")
            
            return len(running_data)
            
        except Exception as e:
            self.logger.error(f"Failed to restore running batches: {e}")
            return 0
    
    async def _restore_completed_batches(self) -> int:
        """恢复完成记录"""
        try:
            completed_batches = await self.redis_client.smembers(self.completed_set_key)
            self.completed_batches.extend([batch_id.decode() for batch_id in completed_batches])
            return len(completed_batches)
        except Exception as e:
            self.logger.error(f"Failed to restore completed batches: {e}")
            return 0
    
    async def _restore_failed_batches(self) -> int:
        """恢复失败记录"""
        try:
            failed_batches = await self.redis_client.smembers(self.failed_set_key)
            self.failed_batches.extend([batch_id.decode() for batch_id in failed_batches])
            return len(failed_batches)
        except Exception as e:
            self.logger.error(f"Failed to restore failed batches: {e}")
            return 0
    
    async def _check_orphaned_tasks(self) -> int:
        """检查孤儿任务"""
        try:
            orphaned_count = 0
            
            # 检查运行中的任务是否在Celery中还存在
            for batch_id, execution in list(self.running_batches.items()):
                from celery.result import AsyncResult
                celery_result = AsyncResult(execution.celery_task_id)
                
                # 如果Celery任务不存在或已完成，但我们的状态还是运行中
                if celery_result.state in ['PENDING', 'RETRY'] and not celery_result.ready():
                    continue  # 任务还在运行
                
                # 任务已完成但状态未更新，标记为孤儿
                self.logger.warning(f"Found orphaned task: {batch_id}")
                
                # 移动到失败队列
                del self.running_batches[batch_id]
                self.failed_batches.append(batch_id)
                
                # 更新持久化状态
                await self._remove_from_running_hash(batch_id)
                await self._persist_failed_batches([batch_id])
                
                orphaned_count += 1
            
            return orphaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to check orphaned tasks: {e}")
            return 0
    
    async def _persist_pending_batches(self) -> None:
        """持久化待处理队列"""
        try:
            # 清空现有队列
            await self.redis_client.delete(self.pending_queue_key)
            
            # 重新添加所有批次
            if self.pending_batches:
                batch_data_list = [
                    json.dumps(self._task_batch_to_dict(batch))
                    for batch in self.pending_batches
                ]
                await self.redis_client.lpush(self.pending_queue_key, *batch_data_list)
            
        except Exception as e:
            self.logger.error(f"Failed to persist pending batches: {e}")
    
    async def _persist_running_batch(self, batch_id: str) -> None:
        """持久化运行状态"""
        try:
            if batch_id in self.running_batches:
                execution = self.running_batches[batch_id]
                execution_data = json.dumps(self._batch_execution_to_dict(execution))
                await self.redis_client.hset(self.running_hash_key, batch_id, execution_data)
        except Exception as e:
            self.logger.error(f"Failed to persist running batch {batch_id}: {e}")
    
    async def _remove_from_pending_queue(self, batch: TaskBatch) -> None:
        """从待处理队列中移除批次"""
        try:
            batch_data = json.dumps(self._task_batch_to_dict(batch))
            await self.redis_client.lrem(self.pending_queue_key, 1, batch_data)
        except Exception as e:
            self.logger.error(f"Failed to remove batch from pending queue: {e}")
    
    async def _remove_from_running_hash(self, batch_id: str) -> None:
        """从运行哈希中移除批次"""
        try:
            await self.redis_client.hdel(self.running_hash_key, batch_id)
        except Exception as e:
            self.logger.error(f"Failed to remove running batch {batch_id}: {e}")
    
    async def _persist_completed_batches(self, batch_ids: List[str]) -> None:
        """持久化完成批次"""
        try:
            if batch_ids:
                await self.redis_client.sadd(self.completed_set_key, *batch_ids)
        except Exception as e:
            self.logger.error(f"Failed to persist completed batches: {e}")
    
    async def _persist_failed_batches(self, batch_ids: List[str]) -> None:
        """持久化失败批次"""
        try:
            if batch_ids:
                await self.redis_client.sadd(self.failed_set_key, *batch_ids)
        except Exception as e:
            self.logger.error(f"Failed to persist failed batches: {e}")
    
    async def _save_state(self) -> None:
        """保存当前状态"""
        try:
            state_data = {
                "status": self.status.value,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "stats": self.stats,
                "saved_at": datetime.now().isoformat()
            }
            
            await self.redis_client.set(
                self.manager_state_key,
                json.dumps(state_data),
                ex=3600  # 1小时过期
            )
            
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
    
    async def _clear_persistent_state(self) -> None:
        """清理持久化状态"""
        try:
            keys_to_delete = [
                self.pending_queue_key,
                self.running_hash_key,
                self.completed_set_key,
                self.failed_set_key,
                self.manager_state_key
            ]
            
            await self.redis_client.delete(*keys_to_delete)
            self.logger.info("Cleared persistent state")
            
        except Exception as e:
            self.logger.error(f"Failed to clear persistent state: {e}")
    
    def _task_batch_to_dict(self, batch: TaskBatch) -> Dict[str, Any]:
        """将TaskBatch转换为字典"""
        return {
            "batch_id": batch.batch_id,
            "urls": batch.urls,
            "platform": batch.platform,
            "priority": batch.priority.value,
            "estimated_duration": batch.estimated_duration,
            "created_at": batch.created_at.isoformat(),
            "metadata": batch.metadata
        }
    
    def _dict_to_task_batch(self, data: Dict[str, Any]) -> TaskBatch:
        """将字典转换为TaskBatch"""
        return TaskBatch(
            batch_id=data["batch_id"],
            urls=data["urls"],
            platform=data["platform"],
            priority=TaskPriority(data["priority"]),
            estimated_duration=data["estimated_duration"],
            created_at=datetime.fromisoformat(data["created_at"]),
            metadata=data["metadata"]
        )
    
    def _batch_execution_to_dict(self, execution: BatchExecution) -> Dict[str, Any]:
        """将BatchExecution转换为字典"""
        return {
            "batch_id": execution.batch_id,
            "celery_task_id": execution.celery_task_id,
            "batch": self._task_batch_to_dict(execution.batch),
            "started_at": execution.started_at.isoformat(),
            "status": execution.status,
            "result": execution.result,
            "error": execution.error
        }
    
    def _dict_to_batch_execution(self, data: Dict[str, Any]) -> BatchExecution:
        """将字典转换为BatchExecution"""
        return BatchExecution(
            batch_id=data["batch_id"],
            celery_task_id=data["celery_task_id"],
            batch=self._dict_to_task_batch(data["batch"]),
            started_at=datetime.fromisoformat(data["started_at"]),
            status=data.get("status"),
            result=data.get("result"),
            error=data.get("error")
        )
    
    def get_persistence_status(self) -> Dict[str, Any]:
        """获取持久化状态"""
        return {
            "persistence_enabled": self.persistence_enabled,
            "redis_connected": self.redis_client is not None,
            "redis_url": self.redis_url,
            "persistent_keys": {
                "pending_queue": self.pending_queue_key,
                "running_hash": self.running_hash_key,
                "completed_set": self.completed_set_key,
                "failed_set": self.failed_set_key,
                "manager_state": self.manager_state_key
            }
        }
