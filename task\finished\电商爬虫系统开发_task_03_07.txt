# 子任务03-07: 任务调度和管理系统

## 任务描述
开发高效的任务调度和管理系统，实现爬取任务的队列管理、优先级调度、并发控制、状态跟踪和进度报告功能。

## 具体任务内容

### 1. 任务队列管理
- 实现基于优先级的任务队列
- 支持任务的添加、暂停、恢复、取消
- 实现任务去重和防重复机制
- 支持任务的序列化和持久化

### 2. 任务调度器设计
- 实现多线程/协程任务调度
- 支持定时任务和周期性任务
- 实现任务依赖关系管理
- 支持任务优先级和权重调度

### 3. 并发控制系统
- 实现任务并发数量控制
- 支持动态调整并发数
- 实现资源使用限制和监控
- 防止系统过载和资源耗尽

### 4. 任务状态跟踪
- 实现任务生命周期状态管理
- 支持任务进度实时更新
- 提供任务执行历史记录
- 实现任务执行统计和分析

### 5. 失败任务处理
- 实现智能重试机制（指数退避）
- 支持失败任务的分类和标记
- 提供失败原因分析和报告
- 实现死信队列管理

### 6. 任务监控和报告
- 提供实时任务执行监控
- 生成任务执行报告和统计
- 实现任务性能指标收集
- 支持告警和通知机制

## 技术实现

### 任务调度器架构
```python
from asyncio import Queue, Event, Lock
from typing import Dict, List, Optional, Callable
from enum import Enum
import asyncio
import time
from datetime import datetime, timedelta

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class CrawlTask:
    """爬取任务模型"""
    def __init__(self, task_id: str, url: str, priority: int = 0, **kwargs):
        self.task_id = task_id
        self.url = url
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.retry_count = 0
        self.max_retries = kwargs.get('max_retries', 3)
        self.metadata = kwargs
        self.result = None
        self.error = None

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self.task_queue = Queue()
        self.running_tasks: Dict[str, CrawlTask] = {}
        self.completed_tasks: Dict[str, CrawlTask] = {}
        self.failed_tasks: Dict[str, CrawlTask] = {}
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.running = False
        self.stats = TaskStats()
    
    async def start(self) -> bool:
        """启动任务调度器"""
        self.running = True
        await self._start_workers()
        return True
    
    async def stop(self) -> bool:
        """停止任务调度器"""
        self.running = False
        await self._stop_workers()
        return True
    
    async def add_task(self, task: CrawlTask) -> bool:
        """添加任务到队列"""
        if await self._is_duplicate(task):
            return False
        await self.task_queue.put(task)
        self.stats.total_tasks += 1
        return True
    
    async def _start_workers(self):
        """启动工作协程"""
        self.workers = []
        for i in range(self.max_concurrent):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
    
    async def _worker(self, worker_name: str):
        """工作协程"""
        while self.running:
            task = await self.task_queue.get()
            if task is None:
                break
            
            async with self.semaphore:
                await self._execute_task(task)
    
    async def _execute_task(self, task: CrawlTask):
        """执行单个任务"""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        self.running_tasks[task.task_id] = task
        
        try:
            # 执行爬取任务
            result = await self._crawl_url(task.url, task.metadata)
            
            task.result = result
            task.status = TaskStatus.SUCCESS
            task.completed_at = datetime.now()
            
            self.completed_tasks[task.task_id] = task
            self.stats.successful_tasks += 1
            
        except Exception as e:
            await self._handle_task_failure(task, e)
        finally:
            self.running_tasks.pop(task.task_id, None)

class TaskStats:
    """任务统计"""
    def __init__(self):
        self.total_tasks = 0
        self.successful_tasks = 0
        self.failed_tasks = 0
        self.running_tasks = 0
        self.average_execution_time = 0.0
        self.start_time = datetime.now()
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return {
            "total_tasks": self.total_tasks,
            "successful_tasks": self.successful_tasks,
            "failed_tasks": self.failed_tasks,
            "running_tasks": self.running_tasks,
            "success_rate": self.successful_tasks / max(self.total_tasks, 1) * 100,
            "average_execution_time": self.average_execution_time,
            "uptime": (datetime.now() - self.start_time).total_seconds()
        }

class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    async def should_retry(self, task: CrawlTask, error: Exception) -> bool:
        """判断是否应该重试"""
        if task.retry_count >= task.max_retries:
            return False
        
        # 根据错误类型判断是否值得重试
        retryable_errors = [
            "ConnectionError", 
            "TimeoutError", 
            "HTTPError"
        ]
        
        return any(err in str(type(error)) for err in retryable_errors)
    
    async def calculate_delay(self, retry_count: int) -> float:
        """计算重试延迟（指数退避）"""
        return self.base_delay * (2 ** retry_count)
```

### 配置文件结构
```yaml
# task_scheduler_config.yaml
scheduler:
  max_concurrent_tasks: 10
  default_priority: 0
  queue_size: 1000
  worker_timeout: 300  # 5分钟
  
  # 重试配置
  retry:
    max_retries: 3
    base_delay: 1.0
    max_delay: 60.0
    exponential_backoff: true
  
  # 任务去重
  deduplication:
    enabled: true
    ttl: 3600  # 1小时
    key_fields: ["url", "method"]
  
  # 监控配置
  monitoring:
    stats_interval: 60  # 统计间隔(秒)
    log_progress: true
    alert_on_failure_rate: 0.1  # 失败率超过10%告警
```

## 预期产出物
1. **任务调度器核心** (`task_scheduler.py`)
2. **任务队列管理** (`task_queue.py`)
3. **任务状态管理** (`task_status.py`)
4. **重试机制实现** (`retry_manager.py`)
5. **任务统计模块** (`task_stats.py`)
6. **并发控制器** (`concurrency_controller.py`)
7. **任务监控器** (`task_monitor.py`)
8. **配置文件** (`task_scheduler_config.yaml`)
9. **单元测试文件** (`tests/test_task_scheduler.py`)
10. **集成测试用例** (`tests/integration/test_scheduling.py`)

## 验收标准
- [ ] 任务调度器可以正常启动和停止
- [ ] 任务队列管理功能正常
- [ ] 优先级调度机制有效
- [ ] 并发控制准确无误
- [ ] 任务状态跟踪完整
- [ ] 失败重试机制正常工作
- [ ] 任务去重功能有效
- [ ] 监控和统计数据准确
- [ ] 通过所有单元测试和集成测试
- [ ] 性能测试达到预期指标

## 性能指标
- **任务处理速度**: ≥100任务/分钟
- **并发任务数**: 支持10-50个并发任务
- **内存使用**: 队列1000任务≤100MB
- **响应时间**: 任务分发延迟≤100ms
- **可靠性**: 99.9%任务执行成功率

## 技术要点
- 使用asyncio异步编程
- 实现优先级队列算法
- 采用指数退避重试策略
- 使用信号量控制并发
- 实现任务持久化和恢复

## 预估工时
1天

## 依赖关系
- 前置任务: Task 03-01 (爬虫引擎架构)
- 后置任务: Task 03-05 (错误处理机制)

## 状态
待开始

## 备注
任务调度系统是爬虫引擎的核心组件，需要确保高可靠性和良好的性能，为整个爬虫系统提供稳定的任务执行基础。 