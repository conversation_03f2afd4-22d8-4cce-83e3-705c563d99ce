"""
爬虫任务执行API
基于外部爬虫API的任务执行管理
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.services.crawler_task_executor import (
    CrawlerTaskExecutor,
    TaskExecutionRequest,
    ExecutionMode,
    get_task_executor
)
from app.services.crawler_api_service import TaskPriority
from app.services.task_storage_service import TaskStorageService

router = APIRouter()


class ExecuteTaskRequest(BaseModel):
    """执行任务请求"""
    monitoring_task_id: str = Field(..., description="监控任务ID")
    urls: List[str] = Field(..., min_items=1, max_items=1000, description="URL列表")
    config_id: str = Field(..., description="爬虫配置ID")
    execution_mode: ExecutionMode = Field(ExecutionMode.BATCH, description="执行模式")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="回调URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class ExecuteTaskResponse(BaseModel):
    """执行任务响应"""
    success: bool
    data: Dict[str, Any]
    message: str


class BatchExecuteRequest(BaseModel):
    """批量执行请求"""
    tasks: List[ExecuteTaskRequest] = Field(..., min_items=1, max_items=10, description="任务列表")


@router.post("/execute", response_model=ExecuteTaskResponse)
async def execute_crawler_task(
    request: ExecuteTaskRequest,
    executor: CrawlerTaskExecutor = Depends(get_task_executor)
):
    """执行爬虫任务"""
    try:
        # 构建任务执行请求
        execution_request = TaskExecutionRequest(
            monitoring_task_id=request.monitoring_task_id,
            urls=request.urls,
            config_id=request.config_id,
            execution_mode=request.execution_mode,
            priority=request.priority,
            callback_url=request.callback_url,
            metadata=request.metadata
        )
        
        # 执行任务
        result = await executor.execute_monitoring_task(execution_request)
        
        return ExecuteTaskResponse(
            success=result.status != "failed",
            data={
                "execution_id": result.execution_id,
                "monitoring_task_id": result.monitoring_task_id,
                "batch_id": result.batch_id,
                "task_ids": result.task_ids,
                "status": result.status,
                "total_urls": result.total_urls,
                "submitted_at": result.submitted_at.isoformat() if result.submitted_at else None,
                "external_result": result.external_result
            },
            message=result.message
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"任务执行失败: {str(e)}")


@router.post("/execute/batch", response_model=Dict[str, Any])
async def execute_crawler_tasks_batch(
    request: BatchExecuteRequest,
    background_tasks: BackgroundTasks,
    executor: CrawlerTaskExecutor = Depends(get_task_executor)
):
    """批量执行爬虫任务"""
    try:
        results = []
        
        for task_request in request.tasks:
            try:
                # 构建任务执行请求
                execution_request = TaskExecutionRequest(
                    monitoring_task_id=task_request.monitoring_task_id,
                    urls=task_request.urls,
                    config_id=task_request.config_id,
                    execution_mode=task_request.execution_mode,
                    priority=task_request.priority,
                    callback_url=task_request.callback_url,
                    metadata=task_request.metadata
                )
                
                # 执行任务
                result = await executor.execute_monitoring_task(execution_request)
                
                results.append({
                    "monitoring_task_id": task_request.monitoring_task_id,
                    "execution_id": result.execution_id,
                    "status": result.status,
                    "message": result.message,
                    "success": result.status != "failed"
                })
                
            except Exception as e:
                results.append({
                    "monitoring_task_id": task_request.monitoring_task_id,
                    "execution_id": None,
                    "status": "failed",
                    "message": f"执行失败: {str(e)}",
                    "success": False
                })
        
        # 统计结果
        total_tasks = len(results)
        successful_tasks = sum(1 for r in results if r["success"])
        failed_tasks = total_tasks - successful_tasks
        
        return {
            "success": True,
            "data": {
                "results": results,
                "summary": {
                    "total_tasks": total_tasks,
                    "successful_tasks": successful_tasks,
                    "failed_tasks": failed_tasks,
                    "success_rate": successful_tasks / total_tasks if total_tasks > 0 else 0
                }
            },
            "message": f"批量执行完成: 成功{successful_tasks}, 失败{failed_tasks}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量执行失败: {str(e)}")


@router.get("/executions/{execution_id}", response_model=Dict[str, Any])
async def get_execution_status(
    execution_id: str,
    executor: CrawlerTaskExecutor = Depends(get_task_executor)
):
    """获取执行状态"""
    try:
        execution_data = await executor.get_execution_status(execution_id)
        
        if not execution_data:
            raise HTTPException(status_code=404, detail="执行记录不存在")
        
        return {
            "success": True,
            "data": execution_data,
            "message": "获取执行状态成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行状态失败: {str(e)}")


@router.get("/tasks/{monitoring_task_id}/executions", response_model=Dict[str, Any])
async def get_task_executions(
    monitoring_task_id: str,
    limit: int = Query(10, ge=1, le=100, description="返回数量限制"),
    executor: CrawlerTaskExecutor = Depends(get_task_executor)
):
    """获取监控任务的执行历史"""
    try:
        executions = await executor.get_task_executions(monitoring_task_id, limit)
        
        return {
            "success": True,
            "data": {
                "monitoring_task_id": monitoring_task_id,
                "executions": executions,
                "total": len(executions)
            },
            "message": "获取执行历史成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行历史失败: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_executor_stats(
    executor: CrawlerTaskExecutor = Depends(get_task_executor)
):
    """获取执行器统计信息"""
    try:
        stats = executor.get_executor_stats()
        
        return {
            "success": True,
            "data": stats,
            "message": "获取统计信息成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/test", response_model=Dict[str, Any])
async def test_crawler_api_connection():
    """测试爬虫API连接"""
    try:
        from app.services.crawler_api_service import get_crawler_api_service
        
        crawler_api = await get_crawler_api_service()
        
        # 测试系统状态
        system_status = await crawler_api.get_system_status()
        
        # 测试系统指标
        try:
            system_metrics = await crawler_api.get_system_metrics()
        except Exception as e:
            system_metrics = {"error": str(e)}
        
        # 获取客户端统计
        client_stats = crawler_api.get_stats()
        
        return {
            "success": True,
            "data": {
                "connection_status": "connected",
                "system_status": system_status,
                "system_metrics": system_metrics,
                "client_stats": client_stats,
                "test_time": datetime.now().isoformat()
            },
            "message": "爬虫API连接测试成功"
        }
        
    except Exception as e:
        return {
            "success": False,
            "data": {
                "connection_status": "failed",
                "error": str(e),
                "test_time": datetime.now().isoformat()
            },
            "message": f"爬虫API连接测试失败: {str(e)}"
        }


@router.post("/executions/{execution_id}/cancel", response_model=Dict[str, Any])
async def cancel_execution(
    execution_id: str,
    executor: CrawlerTaskExecutor = Depends(get_task_executor)
):
    """取消执行（如果支持）"""
    try:
        # 获取执行记录
        execution_data = await executor.get_execution_status(execution_id)
        
        if not execution_data:
            raise HTTPException(status_code=404, detail="执行记录不存在")
        
        # 注意：外部爬虫API可能不支持取消操作
        # 这里只是更新本地状态
        redis_client = executor.task_storage.redis_client
        execution_key = f"crawler_executions:{execution_id}"
        
        redis_client.hset(execution_key, "status", "cancelled")
        redis_client.hset(execution_key, "cancelled_at", datetime.now().isoformat())
        
        return {
            "success": True,
            "data": {
                "execution_id": execution_id,
                "status": "cancelled",
                "cancelled_at": datetime.now().isoformat()
            },
            "message": "执行已标记为取消"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消执行失败: {str(e)}")


@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """健康检查"""
    try:
        from app.services.crawler_api_service import get_crawler_api_service
        
        # 检查爬虫API连接
        try:
            crawler_api = await get_crawler_api_service()
            api_status = await crawler_api.get_system_status()
            api_healthy = True
        except Exception as e:
            api_status = {"error": str(e)}
            api_healthy = False
        
        # 检查Redis连接
        try:
            task_storage = TaskStorageService()
            redis_info = task_storage.redis_client.info()
            redis_healthy = True
        except Exception as e:
            redis_info = {"error": str(e)}
            redis_healthy = False
        
        overall_healthy = api_healthy and redis_healthy
        
        return {
            "success": True,
            "data": {
                "status": "healthy" if overall_healthy else "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "crawler_api": {
                        "status": "healthy" if api_healthy else "unhealthy",
                        "details": api_status
                    },
                    "redis": {
                        "status": "healthy" if redis_healthy else "unhealthy",
                        "details": {"connected_clients": redis_info.get("connected_clients", "unknown")}
                    }
                }
            },
            "message": "健康检查完成"
        }
        
    except Exception as e:
        return {
            "success": False,
            "data": {
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            },
            "message": f"健康检查失败: {str(e)}"
        }
