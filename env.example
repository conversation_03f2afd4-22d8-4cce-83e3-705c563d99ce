# 基础配置
APP_NAME=MonIt Crawler System
VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=postgresql://crawler_user:crawler_password@localhost:5432/monit_crawler_dev

# Redis配置
REDIS_URL=redis://localhost:6379/0

# API配置
API_PREFIX=/api/v1
HOST=0.0.0.0
PORT=8000

# 安全配置 (生产环境请更换)
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# 爬虫配置
CRAWLER_MAX_CONCURRENT=5
CRAWLER_DELAY_MIN=1.0
CRAWLER_DELAY_MAX=3.0
CRAWLER_TIMEOUT=30
CRAWLER_RETRY_TIMES=3
CRAWLER_RETRY_DELAY=5.0

# 文件配置
UPLOAD_DIR=data/uploads
EXPORT_DIR=data/exports
LOG_DIR=logs
MAX_UPLOAD_SIZE=52428800

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# TimescaleDB配置
TIMESCALE_CHUNK_TIME_INTERVAL=1 day
TIMESCALE_COMPRESS_AFTER=7 days
TIMESCALE_DROP_AFTER=365 days

# 监控配置
METRICS_ENABLED=True
LOG_LEVEL=INFO 