# 重置配置功能修复文档

## 问题描述

用户反馈前端页面的"重置默认"按钮点击后，各项配置没有恢复成API请求示例中的样例配置，而是恢复到了不完整的默认配置。

## 问题分析

### 根本原因

1. **前端使用本地默认配置**：
   - 重置功能调用的是前端本地的`getDefaultConfig()`方法
   - 前端的默认配置中`schema_extraction.extraction_schema`是空对象`{}`
   - 前端的默认配置中`instructions`是空字符串`''`
   - 没有从后端获取包含完整API示例配置的默认值

2. **前后端配置不同步**：
   - 后端包含完整的电商商品信息提取Schema（6746字符）
   - 后端包含详细的提取指令（1193字符）
   - 后端使用API请求示例中的API密钥
   - 前端的默认配置缺少这些内容

### 数据流问题

```
用户点击重置 → 前端getDefaultConfig() → 空的Schema和指令 → 表单显示不完整配置
                     ↑
                 应该调用后端API获取完整默认配置
```

## 解决方案

### 1. 添加后端API端点

在`backend/app/api/v1/crawler_config.py`中添加获取默认配置的API：

```python
@router.get("/config/default", response_model=CrawlerFullConfig)
async def get_default_crawler_config():
    """获取默认爬虫配置"""
    try:
        config = config_service._get_default_config()
        # 不隐藏API密钥，因为这是默认配置
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取默认配置失败: {str(e)}")
```

### 2. 添加前端服务方法

在`frontend/src/services/crawlerConfigService.ts`中添加获取默认配置的方法：

```typescript
/**
 * 获取默认爬虫配置
 */
async getDefaultConfigFromServer(): Promise<CrawlerFullConfig> {
  const response = await this.client.get(`${this.baseUrl}/config/default`);
  return response.data;
}
```

### 3. 更新前端重置功能

修改`frontend/src/pages/CrawlerSettings/index.tsx`中的重置函数：

**修改前**：
```typescript
const resetConfig = () => {
  const defaultConfig = crawlerConfigService.getDefaultConfig();
  // 使用本地默认配置
};
```

**修改后**：
```typescript
const resetConfig = async () => {
  try {
    setLoading(true);
    // 从服务器获取默认配置
    const defaultConfig = await crawlerConfigService.getDefaultConfigFromServer();
    
    // 处理JSON序列化
    const defaultFormValues = {
      ...defaultConfig,
      schema_extraction: {
        ...defaultConfig.schema_extraction,
        extraction_schema: JSON.stringify(defaultConfig.schema_extraction.extraction_schema, null, 2)
      }
    };
    
    form.setFieldsValue(defaultFormValues);
    setConfig(defaultConfig);
    message.success('已重置为默认配置');
  } catch (error) {
    // 备用方案：使用本地默认配置
    const localDefaultConfig = crawlerConfigService.getDefaultConfig();
    // ... 处理本地配置
    message.info('已重置为本地默认配置');
  } finally {
    setLoading(false);
  }
};
```

### 4. 更新前端本地默认配置

同时更新前端的本地默认配置，确保与后端保持一致：

- 添加完整的电商商品信息提取Schema
- 添加详细的提取指令
- 使用正确的API密钥
- 包含所有新增的配置项

## 配置内容对比

### 修复前（前端本地默认配置）

```typescript
schema_extraction: {
  extraction_schema: {},  // 空对象
  instructions: '',       // 空字符串
  validate_schema: true,
  return_raw: false
}
```

### 修复后（从后端获取的默认配置）

```typescript
schema_extraction: {
  extraction_schema: {
    "type": "object",
    "description": "电商商品完整信息提取结构",
    "properties": {
      "task_info": { /* 任务信息 */ },
      "product_basic_info": { /* 商品基础信息 */ },
      "pricing_info": { /* 价格信息 */ },
      "category_info": { /* 分类信息 */ },
      "seller_info": { /* 销售商信息 */ },
      "media_info": { /* 媒体信息 */ },
      "qa_section": { /* 问答信息 */ },
      "rating_info": { /* 评分信息 */ },
      "reviews_section": { /* 评论信息 */ }
    },
    "required": ["product_basic_info", "category_info", "pricing_info", "media_info", "rating_info"]
  },
  instructions: "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息...",
  validate_schema: true,
  return_raw: false
}
```

## 测试验证

### 测试结果

通过完整的测试验证了修复效果：

1. **后端默认配置验证**：
   - ✅ Schema包含9个主要属性
   - ✅ 指令长度1193字符
   - ✅ API密钥正确：`sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi`
   - ✅ 包含所有API请求示例中的配置

2. **序列化测试**：
   - ✅ JSON序列化成功（6746字符）
   - ✅ 反序列化数据完整性验证通过
   - ✅ 前端显示格式正确

3. **API端点测试**：
   - ✅ `/api/v1/crawler/config/default`端点正常工作
   - ✅ 返回完整的默认配置
   - ✅ 不隐藏API密钥（因为是默认配置）

## 错误处理

### 网络错误处理

```typescript
try {
  const defaultConfig = await crawlerConfigService.getDefaultConfigFromServer();
  // 使用服务器配置
} catch (error) {
  message.error('重置配置失败');
  // 备用方案：使用本地默认配置
  const localDefaultConfig = crawlerConfigService.getDefaultConfig();
  // ...
  message.info('已重置为本地默认配置');
}
```

### 加载状态管理

- 重置过程中显示加载状态
- 防止用户重复点击
- 提供明确的成功/失败反馈

## 用户体验改进

### 1. 反馈信息

- **成功**：`message.success('已重置为默认配置')`
- **网络失败**：`message.error('重置配置失败')`
- **备用方案**：`message.info('已重置为本地默认配置')`

### 2. 加载状态

- 重置按钮显示加载状态
- 防止操作期间的其他交互

### 3. 数据完整性

- 重置后立即更新组件状态
- 确保表单显示与内部状态一致

## 配置项完整性

重置后用户可以看到：

### LLM配置
- **查询指令**：`提取页面中所有文章的标题、作者和发布时间`
- **API密钥**：完整的有效密钥
- **模型**：`deepseek-v3-0324`
- **基础URL**：`https://api.lkeap.cloud.tencent.com/v1`

### Schema提取配置
- **完整Schema**：包含9个主要数据结构的电商商品信息提取Schema
- **详细指令**：1193字符的完整提取指令
- **验证设置**：启用Schema验证

### 其他配置
- **内容处理**：词数阈值200，排除导航等标签
- **链接过滤**：排除广告和社交媒体域名
- **爬虫设置**：LLM提取策略，绕过缓存等

## 总结

通过这次修复：

- ✅ **解决了重置问题**：现在重置功能正确恢复到API请求示例配置
- ✅ **提供了完整配置**：包含电商商品信息提取的完整Schema和指令
- ✅ **增强了错误处理**：网络失败时有备用方案
- ✅ **改善了用户体验**：明确的反馈信息和加载状态
- ✅ **保证了数据一致性**：前后端配置完全同步

现在用户点击"重置默认"按钮后，可以看到完整的API请求示例配置，包括详细的电商商品信息提取Schema、完整的提取指令和正确的API密钥。
