"""
测试监控功能的完整集成
验证CrawlerRequestBuilder与监控系统的集成
"""

import pytest
import asyncio
import time
from unittest.mock import patch, AsyncMock, MagicMock
from app.services.crawler_request_builder import CrawlerRequestBuilder
from app.services.crawler_request_monitor import get_crawler_request_monitor


class TestMonitoringIntegration:
    """测试监控集成功能"""
    
    @pytest.mark.asyncio
    async def test_crawler_request_builder_with_monitoring(self):
        """测试CrawlerRequestBuilder与监控的集成"""
        
        task_id = "integration-test-task"
        execution_context = {
            "execution_id": "integration-exec-123",
            "source": "test_integration",
            "client_ip": "*************"
        }
        
        # 模拟数据
        mock_urls = [
            "https://articulo.mercadolibre.com.mx/MLM-1234567890-test1",
            "https://articulo.mercadolibre.com.mx/MLM-1234567891-test2"
        ]
        
        mock_config = {
            "llm_query": "提取商品信息",
            "llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}}}',
            "priority": "high",
            "cache_enabled": True,
            "auth_method": "api_key",
            "api_key_prefix": "test_key",
            "user_agent": "MonIt-Test/1.0"
        }
        
        # 模拟所有依赖服务
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config, \
             patch('app.services.crawler_request_builder.get_crawler_request_monitor') as mock_get_monitor:
            
            # 设置模拟返回值
            mock_get_urls.return_value = mock_urls
            mock_get_config.return_value = mock_config
            
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.log_data_flow_step = AsyncMock()
            mock_monitor.log_request_structure = AsyncMock()

            # 模拟监控上下文管理器
            mock_metrics = MagicMock()
            mock_metrics.start_time = time.time()
            mock_metrics.url_fetch_time = None
            mock_metrics.config_fetch_time = None
            mock_metrics.request_build_time = None
            mock_metrics.urls_count = 0
            mock_metrics.config_completeness = None
            mock_metrics.config_source = ""

            # 创建一个真正的异步上下文管理器
            from contextlib import asynccontextmanager

            @asynccontextmanager
            async def mock_context_manager(task_id, execution_id, context):
                yield mock_metrics

            mock_monitor.monitor_request_building = mock_context_manager
            
            mock_get_monitor.return_value = mock_monitor
            
            # 调用被测试的方法
            result = await CrawlerRequestBuilder.build_request_for_task(
                task_id=task_id,
                execution_context=execution_context
            )
            
            # 验证结果
            assert result is not None
            assert "request" in result
            assert "auth_context" in result
            
            # 验证request部分
            request_data = result["request"]
            assert request_data["urls"] == mock_urls
            assert request_data["q"] == mock_config["llm_query"]
            assert request_data["schema_str"] == mock_config["llm_schema"]
            assert request_data["priority"] == mock_config["priority"]
            assert request_data["cache"] == mock_config["cache_enabled"]
            
            # 验证auth_context部分
            auth_context = result["auth_context"]
            assert auth_context["user_id"] == "monit_system"
            assert auth_context["auth_method"] == mock_config["auth_method"]
            assert auth_context["api_key_prefix"] == mock_config["api_key_prefix"]
            assert auth_context["user_agent"] == mock_config["user_agent"]
            assert auth_context["client_ip"] == execution_context["client_ip"]
            
            # 验证监控调用
            mock_monitor.monitor_request_building.assert_called_once_with(
                task_id, execution_context["execution_id"], execution_context
            )
            
            # 验证数据流日志调用
            assert mock_monitor.log_data_flow_step.call_count >= 3  # 至少3个步骤
            
            # 验证请求结构日志调用
            mock_monitor.log_request_structure.assert_called_once()
            
            # 验证指标设置
            assert mock_metrics.urls_count == len(mock_urls)
            assert mock_metrics.url_fetch_time is not None
            assert mock_metrics.config_fetch_time is not None
            assert mock_metrics.request_build_time is not None
            assert mock_metrics.config_completeness is not None
    
    @pytest.mark.asyncio
    async def test_monitoring_error_handling(self):
        """测试监控系统的错误处理"""
        
        task_id = "error-test-task"
        execution_context = {"execution_id": "error-exec-123"}
        
        # 模拟URL获取失败
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch('app.services.crawler_request_builder.get_crawler_request_monitor') as mock_get_monitor:
            
            # 设置URL获取失败
            mock_get_urls.return_value = []  # 没有活跃URL
            
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.log_data_flow_step = AsyncMock()

            # 模拟监控上下文管理器
            mock_metrics = MagicMock()
            mock_metrics.start_time = time.time()
            mock_metrics.url_fetch_time = None
            mock_metrics.config_fetch_time = None
            mock_metrics.urls_count = 0

            # 创建一个真正的异步上下文管理器
            from contextlib import asynccontextmanager

            @asynccontextmanager
            async def mock_context_manager(task_id, execution_id, context=None):
                yield mock_metrics

            mock_monitor.monitor_request_building = mock_context_manager
            
            mock_get_monitor.return_value = mock_monitor
            
            # 调用应该失败
            with pytest.raises(ValueError, match="no active URLs"):
                await CrawlerRequestBuilder.build_request_for_task(
                    task_id=task_id,
                    execution_context=execution_context
                )
            
            # 验证监控仍然被调用
            mock_monitor.monitor_request_building.assert_called_once()
            mock_monitor.log_data_flow_step.assert_called()
            
            # 验证指标被设置
            assert mock_metrics.urls_count == 0
            assert mock_metrics.url_fetch_time is not None
    
    @pytest.mark.asyncio
    async def test_performance_metrics_collection(self):
        """测试性能指标收集"""
        
        task_id = "performance-test-task"
        execution_context = {"execution_id": "perf-exec-123"}
        
        # 模拟数据
        mock_urls = ["https://example.com/test"]
        mock_config = {
            "llm_query": "test query",
            "llm_schema": "{}",
            "priority": "normal",
            "cache_enabled": False,
            "auth_method": "none"
        }
        
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config, \
             patch('app.services.crawler_request_builder.get_crawler_request_monitor') as mock_get_monitor:
            
            # 设置模拟返回值
            mock_get_urls.return_value = mock_urls
            mock_get_config.return_value = mock_config
            
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.log_data_flow_step = AsyncMock()
            mock_monitor.log_request_structure = AsyncMock()

            # 模拟监控上下文管理器
            mock_metrics = MagicMock()
            mock_metrics.start_time = time.time()
            mock_metrics.url_fetch_time = None
            mock_metrics.config_fetch_time = None
            mock_metrics.request_build_time = None
            mock_metrics.urls_count = 0
            mock_metrics.config_completeness = None
            mock_metrics.config_source = ""

            # 创建一个真正的异步上下文管理器
            from contextlib import asynccontextmanager

            @asynccontextmanager
            async def mock_context_manager(task_id, execution_id, context=None):
                yield mock_metrics

            mock_monitor.monitor_request_building = mock_context_manager
            
            mock_get_monitor.return_value = mock_monitor
            
            # 调用方法
            result = await CrawlerRequestBuilder.build_request_for_task(
                task_id=task_id,
                execution_context=execution_context
            )
            
            # 验证性能指标被收集
            assert mock_metrics.url_fetch_time is not None
            assert mock_metrics.config_fetch_time is not None
            assert mock_metrics.request_build_time is not None
            assert mock_metrics.urls_count == len(mock_urls)
            assert mock_metrics.config_completeness is not None
            assert mock_metrics.config_source != ""
            
            # 验证所有性能指标都是合理的数值
            assert isinstance(mock_metrics.url_fetch_time, (int, float))
            assert isinstance(mock_metrics.config_fetch_time, (int, float))
            assert isinstance(mock_metrics.request_build_time, (int, float))
            assert 0 <= mock_metrics.config_completeness <= 1
    
    @pytest.mark.asyncio
    async def test_monitoring_data_flow_logging(self):
        """测试监控数据流日志记录"""
        
        task_id = "dataflow-test-task"
        execution_context = {"execution_id": "dataflow-exec-123"}
        
        # 模拟数据
        mock_urls = ["https://test.com/1", "https://test.com/2"]
        mock_config = {"llm_query": "test", "priority": "low"}
        
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config, \
             patch('app.services.crawler_request_builder.get_crawler_request_monitor') as mock_get_monitor:
            
            # 设置模拟返回值
            mock_get_urls.return_value = mock_urls
            mock_get_config.return_value = mock_config
            
            # 模拟监控器
            mock_monitor = AsyncMock()
            mock_monitor.log_data_flow_step = AsyncMock()
            mock_monitor.log_request_structure = AsyncMock()

            # 模拟监控上下文管理器
            mock_metrics = MagicMock()
            mock_metrics.start_time = time.time()
            mock_metrics.url_fetch_time = None
            mock_metrics.config_fetch_time = None
            mock_metrics.request_build_time = None
            mock_metrics.urls_count = 0
            mock_metrics.config_completeness = None
            mock_metrics.config_source = ""

            # 创建一个真正的异步上下文管理器
            from contextlib import asynccontextmanager

            @asynccontextmanager
            async def mock_context_manager(task_id, execution_id, context=None):
                yield mock_metrics

            mock_monitor.monitor_request_building = mock_context_manager
            
            mock_get_monitor.return_value = mock_monitor
            
            # 调用方法
            await CrawlerRequestBuilder.build_request_for_task(
                task_id=task_id,
                execution_context=execution_context
            )
            
            # 验证数据流日志调用
            data_flow_calls = mock_monitor.log_data_flow_step.call_args_list
            
            # 应该有至少4个数据流步骤
            assert len(data_flow_calls) >= 4
            
            # 验证步骤名称
            step_names = [call[1]['step_name'] for call in data_flow_calls]
            expected_steps = ["开始获取活跃URL", "获取活跃URL完成", "开始获取爬取配置", "获取爬取配置完成"]
            
            for expected_step in expected_steps:
                assert any(expected_step in step for step in step_names), \
                    f"Expected step '{expected_step}' not found in {step_names}"
            
            # 验证请求结构日志
            mock_monitor.log_request_structure.assert_called_once()
            
            # 获取请求结构日志的参数
            structure_call = mock_monitor.log_request_structure.call_args
            logged_request = structure_call[0][0]  # 第一个参数是请求对象
            logged_task_id = structure_call[0][1]  # 第二个参数是任务ID
            
            assert logged_task_id == task_id
            assert "request" in logged_request
            assert "auth_context" in logged_request
    
    @pytest.mark.asyncio
    async def test_monitoring_metrics_persistence(self):
        """测试监控指标持久化"""
        
        # 创建真实的监控器实例
        monitor = await get_crawler_request_monitor()
        
        # 模拟Redis客户端
        with patch.object(monitor, 'get_redis_client') as mock_get_redis:
            mock_redis_client = AsyncMock()
            mock_redis_client.lpush = AsyncMock()
            mock_redis_client.expire = AsyncMock()
            mock_get_redis.return_value = mock_redis_client
            
            # 创建测试指标
            from app.services.crawler_request_monitor import RequestBuildMetrics
            
            metrics = RequestBuildMetrics(
                task_id="persistence-test-task",
                execution_id="persistence-exec-123",
                start_time=time.time()
            )
            
            metrics.urls_count = 5
            metrics.url_fetch_time = 1.2
            metrics.config_fetch_time = 0.8
            metrics.request_build_time = 0.5
            metrics.config_completeness = 0.9
            metrics.mark_completed("success")
            
            # 保存指标
            await monitor._save_metrics(metrics)
            
            # 验证Redis调用
            assert mock_redis_client.lpush.call_count == 2  # 两个键：日期键和任务键
            assert mock_redis_client.expire.call_count == 2  # 两个过期设置
            
            # 验证保存的数据格式
            lpush_calls = mock_redis_client.lpush.call_args_list
            
            # 检查第一个调用（日期键）
            date_key_call = lpush_calls[0]
            assert "crawler_request_metrics:" in date_key_call[0][0]
            
            # 检查第二个调用（任务键）
            task_key_call = lpush_calls[1]
            assert f"task_metrics:{metrics.task_id}" == task_key_call[0][0]
            
            # 验证保存的数据是JSON格式
            import json
            saved_data = json.loads(date_key_call[0][1])
            assert saved_data["task_id"] == metrics.task_id
            assert saved_data["status"] == "success"
            assert saved_data["urls_count"] == 5
        
        await monitor.close()
    
    @pytest.mark.asyncio
    async def test_end_to_end_monitoring_flow(self):
        """测试端到端的监控流程"""
        
        task_id = "e2e-monitoring-test"
        execution_context = {
            "execution_id": "e2e-exec-456",
            "source": "e2e_test",
            "celery_task_id": "celery-e2e-456"
        }
        
        # 模拟完整的数据流
        mock_urls = [
            "https://articulo.mercadolibre.com.mx/MLM-1111111111-e2e-test1",
            "https://articulo.mercadolibre.com.mx/MLM-2222222222-e2e-test2",
            "https://articulo.mercadolibre.com.mx/MLM-3333333333-e2e-test3"
        ]
        
        mock_config = {
            "llm_query": "提取商品标题、价格、库存状态",
            "llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}, "price": {"type": "string"}}}',
            "priority": "high",
            "cache_enabled": True,
            "auth_method": "api_key",
            "api_key_prefix": "e2e_test_key",
            "user_agent": "MonIt-E2E-Test/1.0"
        }
        
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config:
            
            # 设置模拟返回值
            mock_get_urls.return_value = mock_urls
            mock_get_config.return_value = mock_config
            
            # 模拟Redis连接（用于监控器）
            with patch('app.services.crawler_request_monitor.redis') as mock_redis:
                mock_redis_client = AsyncMock()
                mock_redis_client.lpush = AsyncMock()
                mock_redis_client.expire = AsyncMock()
                mock_redis.from_url.return_value = mock_redis_client
                
                # 执行完整流程
                start_time = time.time()
                
                result = await CrawlerRequestBuilder.build_request_for_task(
                    task_id=task_id,
                    execution_context=execution_context
                )
                
                end_time = time.time()
                total_duration = end_time - start_time
                
                # 验证结果完整性
                assert result is not None
                assert "request" in result
                assert "auth_context" in result
                
                # 验证请求内容
                request_data = result["request"]
                assert len(request_data["urls"]) == 3
                assert request_data["q"] == mock_config["llm_query"]
                assert request_data["cache"] == True
                assert request_data["priority"] == "high"
                
                # 验证认证上下文
                auth_context = result["auth_context"]
                assert auth_context["execution_id"] == execution_context["execution_id"]
                assert auth_context["celery_task_id"] == execution_context["celery_task_id"]
                
                # 验证性能
                assert total_duration < 5.0  # 整个流程应该在5秒内完成
                
                # 验证监控数据被保存
                assert mock_redis_client.lpush.call_count >= 2
                
                print(f"✅ 端到端监控测试完成 - 耗时: {total_duration:.3f}s")


if __name__ == "__main__":
    pytest.main([__file__])
