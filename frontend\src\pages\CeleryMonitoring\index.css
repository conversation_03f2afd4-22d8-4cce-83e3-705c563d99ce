.celery-monitoring {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.celery-monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.celery-monitoring-header h2 {
  margin: 0;
  color: #1890ff;
}

/* 统计卡片样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.ant-table-tbody > tr:hover > td {
  background: #f0f9ff;
}

/* 状态标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 代码文本样式 */
.ant-typography code {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 12px;
  color: #24292e;
}

/* 操作按钮样式 */
.ant-btn-sm {
  height: 24px;
  padding: 0 7px;
  font-size: 12px;
  border-radius: 4px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 刷新指示器 */
.refresh-indicator {
  position: fixed;
  top: 80px;
  right: 24px;
  z-index: 1000;
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .celery-monitoring {
    padding: 16px;
  }
  
  .celery-monitoring-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .celery-monitoring {
    padding: 12px;
  }
  
  .ant-statistic-content {
    font-size: 20px;
  }
  
  .ant-table {
    font-size: 12px;
  }
}

/* 任务详情模态框样式 */
.task-detail-modal .ant-modal-body {
  padding: 24px;
}

.task-detail-section {
  margin-bottom: 24px;
}

.task-detail-section h4 {
  margin-bottom: 12px;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.task-args-display {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.task-error-display {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  color: #c53030;
}

/* 队列健康状态指示器 */
.queue-health-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.queue-health-indicator.healthy {
  background-color: #52c41a;
}

.queue-health-indicator.warning {
  background-color: #faad14;
}

.queue-health-indicator.critical {
  background-color: #ff4d4f;
}

/* Worker负载进度条 */
.worker-load-progress {
  margin-top: 8px;
}

.worker-load-progress .ant-progress-text {
  font-size: 12px;
}

/* 实时更新动画 */
.data-updating {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #999;
}

.empty-state .anticon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  font-size: 12px;
  max-width: 300px;
}

/* 标签页样式 */
.ant-tabs-tab {
  font-weight: 500;
}

.ant-tabs-tab-active {
  color: #1890ff !important;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #333;
}

/* 统计数值颜色 */
.stat-success {
  color: #52c41a;
}

.stat-warning {
  color: #faad14;
}

.stat-error {
  color: #ff4d4f;
}

.stat-info {
  color: #1890ff;
}

.stat-default {
  color: #666;
}
