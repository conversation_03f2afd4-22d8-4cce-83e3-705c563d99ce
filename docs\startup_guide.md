# MonIt 项目完整启动指南

## 🎯 概述

MonIt项目包含多个服务组件，本指南将帮您了解完整的启动流程和各种启动方式。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │    │ 数据库(TimescaleDB)│
│   Port: 3000    │◄──►│   Port: 8000    │◄──►│   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Celery Worker   │◄──►│  Redis (队列)    │    │ 任务调度管理器    │
│  (任务执行)      │    │   Port: 6379    │    │  (Task Manager) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 必需服务清单

### 🔧 基础设施服务
1. **TimescaleDB** - 时间序列数据库 (Port: 5432)
2. **Redis** - 消息队列和缓存 (Port: 6379)

### 🚀 应用服务
3. **FastAPI Backend** - 后端API服务 (Port: 8000)
4. **React Frontend** - 前端界面 (Port: 3000) [可选]
5. **Celery Worker** - 任务执行器
6. **Celery Beat** - 定时任务调度器 [可选]
7. **Task Manager** - 任务调度管理器

## 🚀 启动方式

### 方式1: 一键启动 (推荐)

#### Windows用户
```bash
# 运行一键启动脚本
scripts/start_dev.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x scripts/start_dev.sh

# 运行一键启动脚本
./scripts/start_dev.sh
```

### 方式2: Docker Compose (完整环境)

```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f
```

### 方式3: 混合启动 (推荐开发)

#### 步骤1: 启动基础设施
```bash
# 只启动数据库和Redis
docker-compose -f docker-compose.dev.yml up timescaledb redis -d
```

#### 步骤2: 激活conda环境
```bash
conda activate monit
```

#### 步骤3: 启动应用服务
```bash
# 启动后端API (终端1)
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动Celery Worker (终端2)
cd backend
celery -A app.celery_app worker --loglevel=info

# 启动任务管理器 (终端3)
python backend/scripts/start_task_manager.py

# 启动前端 (终端4) [可选]
cd frontend
npm start
```

### 方式4: 最小启动 (仅测试任务调度)

```bash
# 1. 启动Redis
docker run -d --name monit-redis -p 6379:6379 redis:7-alpine

# 2. 激活环境
conda activate monit

# 3. 运行测试
python backend/scripts/quick_test.py
```

## 📊 服务启动顺序

### 必需的启动顺序
```
1. TimescaleDB (数据库)
2. Redis (消息队列)
3. FastAPI Backend (API服务)
4. Celery Worker (任务执行)
5. Task Manager (任务调度)
6. Frontend (可选)
```

### 依赖关系
```
TimescaleDB ← Backend ← Celery Worker ← Task Manager
Redis ← Backend ← Celery Worker ← Task Manager
Backend ← Frontend
```

## 🔍 服务验证

### 检查服务状态
```bash
# 检查Docker容器
docker ps

# 检查端口占用
netstat -an | grep -E "(3000|5432|6379|8000)"

# 检查Redis连接
redis-cli ping

# 检查数据库连接
psql -h localhost -U crawler_user -d monit_crawler_dev
```

### 健康检查URL
- **后端API**: http://localhost:8000/health
- **API文档**: http://localhost:8000/docs
- **前端界面**: http://localhost:3000
- **Redis**: `redis-cli ping`

## 🧪 测试启动

### 快速功能测试
```bash
# 激活环境
conda activate monit

# 运行快速测试 (不需要数据库)
python backend/scripts/quick_test.py
```

### 完整系统测试
```bash
# 确保所有服务运行后
python backend/scripts/run_real_test.py
```

## 📝 环境配置

### 环境变量文件
```bash
# 复制环境变量模板
cp env.example .env

# 编辑配置 (如果需要)
nano .env
```

### 关键配置项
```bash
# 数据库连接
DATABASE_URL=postgresql://crawler_user:crawler_password@localhost:5432/monit_crawler_dev

# Redis连接
REDIS_URL=redis://localhost:6379/0

# API配置
HOST=0.0.0.0
PORT=8000
```

## 🚨 常见问题

### 问题1: Docker容器启动失败
```bash
# 解决方案: 检查Docker是否运行
docker --version
docker ps

# 重启Docker服务
# Windows: 重启Docker Desktop
# Linux: sudo systemctl restart docker
```

### 问题2: 端口被占用
```bash
# 查看端口占用
netstat -an | grep :8000

# 杀死占用进程 (Windows)
taskkill /F /PID <PID>

# 杀死占用进程 (Linux/Mac)
kill -9 <PID>
```

### 问题3: conda环境未激活
```bash
# 激活环境
conda activate monit

# 验证环境
python -c "import pandas; print('环境正常')"
```

### 问题4: 数据库连接失败
```bash
# 等待数据库完全启动
sleep 10

# 检查数据库日志
docker logs monit-timescaledb

# 手动连接测试
psql -h localhost -U crawler_user -d monit_crawler_dev
```

## 🔄 停止服务

### 停止Docker服务
```bash
# 停止所有服务
docker-compose -f docker-compose.dev.yml down

# 停止并删除数据卷
docker-compose -f docker-compose.dev.yml down -v
```

### 停止本地服务
```bash
# 在各个终端中按 Ctrl+C 停止服务
# 或者关闭对应的终端窗口
```

## 📈 性能监控

### 资源使用监控
```bash
# 查看Docker容器资源使用
docker stats

# 查看系统资源
htop  # Linux/Mac
taskmgr  # Windows
```

### 应用监控
```bash
# 查看Celery任务状态
celery -A app.celery_app inspect active

# 查看Redis队列长度
redis-cli llen crawler_queue
```

## 🎯 开发建议

### 日常开发流程
1. **启动基础设施**: `docker-compose up timescaledb redis -d`
2. **激活环境**: `conda activate monit`
3. **启动后端**: `uvicorn app.main:app --reload`
4. **启动Worker**: `celery -A app.celery_app worker`
5. **运行测试**: `python backend/scripts/quick_test.py`

### 调试模式
```bash
# 启动调试模式的后端
python -m uvicorn app.main:app --reload --log-level debug

# 启动详细日志的Celery
celery -A app.celery_app worker --loglevel=debug
```

## 📋 启动检查清单

- [ ] Docker已安装并运行
- [ ] Conda环境已创建并激活
- [ ] 依赖包已安装
- [ ] 环境变量已配置
- [ ] TimescaleDB容器运行正常
- [ ] Redis容器运行正常
- [ ] 后端API服务启动
- [ ] Celery Worker启动
- [ ] 任务管理器启动
- [ ] 健康检查通过
- [ ] 测试运行成功

---

**恭喜！** 按照本指南，您应该能够成功启动完整的MonIt系统！🎉
