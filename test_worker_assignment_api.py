#!/usr/bin/env python3
"""
测试Worker任务分配API的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_worker_assignment_api():
    """测试Worker任务分配API"""
    
    print("🧪 测试Worker任务分配API...")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    worker_id = "f1798516-0d34-4acb-b8dd-2118c15690a6"  # 11235e Worker
    
    # 1. 创建测试分配
    print(f"\n🔧 为Worker {worker_id} 创建测试分配...")
    assignment_data = {
        "task_id": task_id,
        "worker_ids": [worker_id],
        "assignment_name": "测试Worker任务分配API",
        "description": "用于测试Worker任务分配信息显示功能",
        "assignment_strategy": "health_based",
        "auto_failover": True,
        "total_urls": 49
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/task-assignments/",
            json=assignment_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"分配创建响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            assignment = response.json()
            print("✅ 分配创建成功!")
            print(f"  分配ID: {assignment['assignment_id']}")
            
            # 2. 测试Worker任务分配API
            print(f"\n📋 测试Worker任务分配API...")
            assignments_response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}/assignments")
            
            print(f"任务分配响应状态码: {assignments_response.status_code}")
            
            if assignments_response.status_code == 200:
                assignments = assignments_response.json()
                print(f"✅ 获取到 {len(assignments)} 个任务分配:")
                
                for assignment in assignments:
                    print(f"\n  📄 分配: {assignment['assignment_name']}")
                    print(f"    分配ID: {assignment['assignment_id']}")
                    print(f"    任务名称: {assignment['task_name']}")
                    print(f"    任务ID: {assignment['task_id']}")
                    print(f"    任务状态: {assignment['task_status']}")
                    print(f"    分配状态: {assignment['assignment_status']}")
                    print(f"    是否主要Worker: {assignment['is_primary_worker']}")
                    print(f"    是否当前Worker: {assignment['is_current_worker']}")
                    print(f"    Worker数量: {assignment['worker_count']}")
                    print(f"    进度: {assignment['processed_urls']}/{assignment['total_urls']}")
                    print(f"    创建时间: {assignment['created_at']}")
                
                return True
            else:
                print(f"❌ 获取Worker任务分配失败: {assignments_response.text}")
                return False
        else:
            print(f"❌ 分配创建失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试Worker任务分配API\n")
    
    result = test_worker_assignment_api()
    
    print(f"\n🎯 测试结果:")
    print(f"  Worker任务分配API: {'✅ 成功' if result else '❌ 失败'}")
    
    if result:
        print(f"\n🎉 Worker任务分配API测试通过！")
    else:
        print(f"\n❌ Worker任务分配API测试失败")

if __name__ == "__main__":
    main()
