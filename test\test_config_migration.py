"""
配置迁移测试用例
测试传统爬虫配置的迁移功能
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

from backend.app.services.config_migration_service import ConfigMigrationService
from backend.app.api.v1.crawler_config import CrawlerFullConfig, APIConfig, BrowserConfig, CrawlerConfig, LLMConfig
from backend.app.api.v1.crawler_config import SchemaExtractionConfig, ContentProcessingConfig, LinkFilteringConfig
from backend.app.api.v1.crawler_config import SchedulerConfig, MonitorConfig


class TestConfigMigration:
    """配置迁移测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.migration_service = ConfigMigrationService()
        self.sample_legacy_config = self._create_sample_legacy_config()
    
    def _create_sample_legacy_config(self) -> CrawlerFullConfig:
        """创建示例传统配置"""
        return CrawlerFullConfig(
            api=APIConfig(
                base_url="http://localhost:11234",
                timeout=30000,
                max_retries=3
            ),
            browser=BrowserConfig(
                headless=True,
                user_agent="Mozilla/5.0 Test Agent",
                viewport_width=1920,
                viewport_height=1080
            ),
            crawler=CrawlerConfig(
                method="arun_many",
                verbose=True,
                simulate_user=True,
                magic=True
            ),
            llm=LLMConfig(
                model="deepseek-v3-0324",
                api_key="sk-test-key-12345",
                temperature=0.7,
                max_tokens=4000
            ),
            schema_extraction=SchemaExtractionConfig(
                enabled=True,
                schema_type="auto",
                extraction_schema={
                    "title": "string",
                    "price": "number",
                    "availability": "boolean"
                },
                instructions="Extract product information",
                validate_schema=True,
                return_raw=False
            ),
            content_processing=ContentProcessingConfig(
                word_count_threshold=200,
                excluded_tags=["nav", "footer"],
                parser_type="lxml",
                css_selector=".content",
                remove_forms=False,
                only_text=False,
                prettify=False
            ),
            link_filtering=LinkFilteringConfig(
                exclude_external_links=False,
                exclude_internal_links=False,
                exclude_social_media_links=True,
                exclude_domains=["ads.google.com"],
                social_media_domains=["facebook.com", "twitter.com"]
            ),
            scheduler=SchedulerConfig(
                semaphore_count=2,
                mean_delay=0.1,
                max_range=3,
                pool_size=10,
                memory_threshold=4096
            ),
            monitor=MonitorConfig(
                display_mode="detailed",
                show_progress=True,
                log_errors=True
            )
        )
    
    def test_convert_legacy_to_new_config(self):
        """测试传统配置转换为新架构配置"""
        # 执行转换
        new_config = self.migration_service.convert_legacy_to_new_config(self.sample_legacy_config)
        
        # 验证转换结果
        assert new_config.config_name.startswith("迁移配置_")
        assert new_config.description == "从传统配置迁移而来"
        assert new_config.browser.headless == self.sample_legacy_config.browser.headless
        assert new_config.llm.model == self.sample_legacy_config.llm.model
        assert new_config.schema_extraction.enabled == self.sample_legacy_config.schema_extraction.enabled
    
    def test_convert_legacy_to_instance_config(self):
        """测试传统配置转换为实例配置"""
        # 执行转换
        instance_config = self.migration_service.convert_legacy_to_instance_config(self.sample_legacy_config)
        
        # 验证转换结果
        assert instance_config.config_name.startswith("实例配置_")
        assert instance_config.api_endpoint == self.sample_legacy_config.api.base_url
        assert instance_config.timeout == self.sample_legacy_config.api.timeout
        assert instance_config.max_retries == self.sample_legacy_config.api.max_retries
        assert instance_config.max_concurrent == 2  # 默认值
        assert instance_config.weight == 1  # 默认值
    
    def test_convert_legacy_to_backend_config(self):
        """测试传统配置转换为后端配置"""
        # 执行转换
        backend_config = self.migration_service.convert_legacy_to_backend_config(self.sample_legacy_config)
        
        # 验证转换结果
        assert backend_config.backend_name.startswith("后端配置_")
        assert backend_config.api_endpoint == self.sample_legacy_config.api.base_url
        assert backend_config.timeout == self.sample_legacy_config.api.timeout
        assert backend_config.auth_config["auth_type"] == "api_key"
    
    def test_create_compatibility_mapping(self):
        """测试创建兼容性映射"""
        # 执行映射创建
        mapping = self.migration_service.create_compatibility_mapping(self.sample_legacy_config)
        
        # 验证映射结果
        assert mapping["legacy_format"] is True
        assert "migration_timestamp" in mapping
        assert mapping["api_endpoint"] == self.sample_legacy_config.api.base_url
        assert mapping["has_api_key"] is True
        assert mapping["browser_headless"] is True
        assert mapping["extraction_enabled"] is True
    
    def test_validate_migration_compatibility(self):
        """测试迁移兼容性验证"""
        # 测试正常配置
        result = self.migration_service.validate_migration_compatibility(self.sample_legacy_config)
        assert result["compatible"] is True
        assert len(result["issues"]) == 0
        assert result["migration_recommended"] is True
        
        # 测试有问题的配置
        problematic_config = self.sample_legacy_config.copy()
        problematic_config.api.base_url = ""  # 空URL
        
        result = self.migration_service.validate_migration_compatibility(problematic_config)
        assert result["compatible"] is False
        assert len(result["issues"]) > 0
        assert "API基础URL不能为空" in result["issues"]
    
    def test_conversion_error_handling(self):
        """测试转换错误处理"""
        # 创建无效配置
        invalid_config = Mock()
        invalid_config.browser = None  # 缺少必需字段
        
        # 测试错误处理
        with pytest.raises(ValueError, match="配置转换失败"):
            self.migration_service.convert_legacy_to_new_config(invalid_config)
    
    def test_migration_data_integrity(self):
        """测试迁移数据完整性"""
        # 转换为新配置
        new_config = self.migration_service.convert_legacy_to_new_config(self.sample_legacy_config)
        
        # 验证关键数据完整性
        assert new_config.llm.model == self.sample_legacy_config.llm.model
        assert new_config.llm.temperature == self.sample_legacy_config.llm.temperature
        assert new_config.browser.viewport_width == self.sample_legacy_config.browser.viewport_width
        assert new_config.schema_extraction.extraction_schema == self.sample_legacy_config.schema_extraction.extraction_schema
        assert new_config.content_processing.word_count_threshold == self.sample_legacy_config.content_processing.word_count_threshold


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
