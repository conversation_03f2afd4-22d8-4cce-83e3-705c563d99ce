#!/usr/bin/env python3
"""
测试React Key重复错误修复
验证前端不再出现重复key警告
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_celery_monitoring_page():
    """测试Celery监控页面是否有React key错误"""
    
    print("🔍 测试Celery监控页面React Key修复...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 访问Celery监控页面
        print("📱 访问Celery监控页面...")
        driver.get("http://localhost:3000/celery-monitoring")
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        
        # 等待一段时间让React渲染完成
        time.sleep(3)
        
        # 切换到实时任务标签页
        print("🔄 切换到实时任务标签页...")
        try:
            real_time_tab = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '实时任务')]")))
            real_time_tab.click()
            time.sleep(2)
        except:
            print("⚠️  无法找到实时任务标签页，可能页面结构已变化")
        
        # 检查控制台错误
        print("🔍 检查浏览器控制台错误...")
        logs = driver.get_log('browser')
        
        # 过滤React key相关的错误
        key_errors = []
        for log in logs:
            message = log.get('message', '')
            if 'same key' in message.lower() or 'duplicate key' in message.lower():
                key_errors.append(message)
        
        if key_errors:
            print("❌ 发现React Key重复错误:")
            for error in key_errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ 没有发现React Key重复错误")
            
        # 检查页面是否正常显示
        print("🔍 检查页面内容...")
        
        # 检查是否有任务表格
        try:
            tables = driver.find_elements(By.TAG_NAME, "table")
            if tables:
                print(f"✅ 找到 {len(tables)} 个表格")
                
                # 检查表格行数
                for i, table in enumerate(tables):
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    print(f"   表格 {i+1}: {len(rows)} 行")
            else:
                print("⚠️  没有找到任务表格")
        except Exception as e:
            print(f"⚠️  检查表格时出错: {e}")
        
        # 检查是否有错误提示
        try:
            error_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'error') or contains(text(), '错误')]")
            if error_elements:
                print("⚠️  页面上发现错误提示:")
                for elem in error_elements[:3]:  # 只显示前3个
                    print(f"   - {elem.text}")
        except:
            pass
        
        print("✅ Celery监控页面测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
        
    finally:
        try:
            driver.quit()
        except:
            pass

def test_api_endpoints():
    """测试API端点是否正常"""
    print("\n🔍 测试API端点...")
    
    try:
        # 测试实时任务流API
        response = requests.get("http://localhost:8000/api/v1/celery/tasks/live-stream", timeout=5)
        if response.status_code == 200:
            data = response.json()
            task_count = len(data.get('executing_tasks', []))
            print(f"✅ 实时任务流API正常，返回 {task_count} 个任务")
            return True
        else:
            print(f"❌ 实时任务流API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试React Key重复错误修复\n")
    
    # 1. 测试API端点
    api_success = test_api_endpoints()
    
    # 2. 测试前端页面
    if api_success:
        page_success = test_celery_monitoring_page()
    else:
        print("⚠️  API测试失败，跳过前端页面测试")
        page_success = False
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    if api_success and page_success:
        print("🎉 所有测试通过！React Key重复错误已修复！")
        print("\n💡 修复内容:")
        print("   - 将Table的rowKey从简单的'id'改为复合key")
        print("   - 确保每个URL执行记录都有唯一标识")
        print("   - 格式: `${record.id}_${record.current_url}_${index}`")
        return True
    else:
        print("❌ 部分测试失败，可能还有问题需要修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
