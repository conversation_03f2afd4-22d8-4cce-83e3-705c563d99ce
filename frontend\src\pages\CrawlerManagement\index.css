/* 爬虫管理页面样式 */

.crawler-management {
  padding: 0;
}

.crawler-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.crawler-management .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.crawler-management .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.crawler-management .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.crawler-management .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.crawler-management .ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

.crawler-management .ant-badge-status-text {
  font-size: 13px;
}

/* 配置表单样式 */
.crawler-config-form .ant-form-item-label > label {
  font-weight: 500;
}

.crawler-config-form .ant-tabs-tab {
  font-weight: 500;
}

.crawler-config-form .ant-alert {
  border-radius: 6px;
}

.crawler-config-form .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
}

/* 执行模态框样式 */
.crawler-execution-modal .ant-form-item-label > label {
  font-weight: 500;
}

.crawler-execution-modal .ant-progress-text {
  font-size: 12px;
}

.crawler-execution-modal .ant-divider {
  margin: 12px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crawler-management .ant-col {
    margin-bottom: 16px;
  }
  
  .crawler-management .ant-space {
    flex-wrap: wrap;
  }
  
  .crawler-management .ant-table {
    font-size: 12px;
  }
  
  .crawler-management .ant-btn {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .crawler-management .ant-statistic {
    text-align: center;
  }
  
  .crawler-management .ant-card-body {
    padding: 16px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .crawler-management .ant-card {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .crawler-management .ant-table-thead > tr > th {
    background-color: #262626;
    color: #fff;
  }
  
  .crawler-management .ant-table-tbody > tr:hover > td {
    background-color: #262626;
  }
}
