apiVersion: 1

datasources:
  # Prometheus数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true

  # Elasticsearch数据源
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "logstash-*"
    jsonData:
      interval: Daily
      timeField: "@timestamp"
      esVersion: "8.0.0"
    editable: true

  # TimescaleDB数据源
  - name: TimescaleDB
    type: postgres
    access: proxy
    url: timescaledb:5432
    database: monit_crawler_dev
    user: crawler_user
    secureJsonData:
      password: crawler_password
    jsonData:
      sslmode: disable
      postgresVersion: 1400
      timescaledb: true
    editable: true
