#!/usr/bin/env python3
"""
数据迁移脚本：从旧架构迁移到新架构
将现有的CrawlerInstanceConfig拆分为CrawlerConfig + BackendConfig + CrawlerWorker
"""

import json
import asyncio
import sys
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.app.services.crawler_instance_config_service import crawler_instance_config_service
from backend.app.services.crawler_config_service import crawler_config_service
from backend.app.services.backend_config_service import backend_config_service
from backend.app.services.crawler_worker_service import crawler_worker_service

from backend.app.schemas.crawler_config_new import CrawlerConfigCreate
from backend.app.schemas.backend_config import BackendConfigCreate
from backend.app.schemas.crawler_worker import CrawlerWorkerCreate
from backend.app.schemas.crawler_instance_config import AuthConfig, AuthType


async def load_migration_config():
    """加载迁移配置"""
    config_file = Path("config/exported_config_defaults.json")
    if not config_file.exists():
        print("❌ 未找到导出的配置文件，请先运行 export_current_config_defaults.py")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def extract_crawler_config_data(instance_config: Dict[str, Any]) -> Dict[str, Any]:
    """从实例配置中提取爬取配置数据"""
    crawler_config_fields = [
        "browser", "crawler", "llm", "schema_extraction",
        "content_processing", "link_filtering", "monitor"
    ]
    
    crawler_data = {}
    for field in crawler_config_fields:
        if field in instance_config:
            crawler_data[field] = instance_config[field]
    
    return crawler_data


def extract_backend_config_data(instance_config: Dict[str, Any]) -> Dict[str, Any]:
    """从实例配置中提取后端配置数据"""
    backend_data = {
        "api_endpoint": instance_config.get("api_endpoint", "http://localhost:11234"),
        "timeout": instance_config.get("timeout", 30000),
        "max_retries": instance_config.get("max_retries", 3),
        "max_concurrent": instance_config.get("max_concurrent", 2),
        "weight": instance_config.get("weight", 1),
        "priority": instance_config.get("priority", 1),
        
        # 新增的调度配置（使用默认值）
        "mean_delay": 1000,  # 1秒平均延迟
        "max_range": 2000,   # 最大2秒范围
        "pool_size": 10,     # 线程池大小
        "memory_threshold": 512,  # 512MB内存阈值
        
        # 认证配置
        "auth_config": instance_config.get("auth_config", {
            "auth_type": "none"
        })
    }
    
    return backend_data


async def migrate_single_config(instance_config: Dict[str, Any], migration_stats: Dict[str, int]) -> Dict[str, str]:
    """迁移单个配置"""
    try:
        config_id = instance_config.get("config_id")
        config_name = instance_config.get("config_name", f"迁移配置_{config_id[:8]}")
        
        print(f"🔄 迁移配置: {config_name} ({config_id})")
        
        # 1. 创建爬取配置
        crawler_data = extract_crawler_config_data(instance_config)
        crawler_config_create = CrawlerConfigCreate(
            config_name=f"{config_name}_爬取配置",
            description=f"从 {config_name} 迁移的爬取配置",
            **crawler_data
        )
        
        crawler_config = await crawler_config_service.create_config(crawler_config_create)
        print(f"  ✅ 创建爬取配置: {crawler_config.config_id}")
        migration_stats["crawler_configs"] += 1
        
        # 2. 创建后端配置
        backend_data = extract_backend_config_data(instance_config)
        
        # 检查是否已存在相同端点的后端配置
        existing_backend = await backend_config_service.get_config_by_endpoint(backend_data["api_endpoint"])
        if existing_backend:
            print(f"  ⚠️ 后端配置已存在: {existing_backend.backend_name}")
            backend_config = existing_backend
        else:
            backend_config_create = BackendConfigCreate(
                backend_name=f"{config_name}_后端配置",
                description=f"从 {config_name} 迁移的后端配置",
                **backend_data
            )
            
            backend_config = await backend_config_service.create_config(backend_config_create)
            print(f"  ✅ 创建后端配置: {backend_config.backend_id}")
            migration_stats["backend_configs"] += 1
        
        # 3. 创建Worker
        worker_create = CrawlerWorkerCreate(
            worker_name=f"{config_name}_Worker",
            description=f"从 {config_name} 迁移的Worker",
            crawler_config_id=crawler_config.config_id,
            backend_config_id=backend_config.backend_id,
            allocated_concurrent=backend_config.max_concurrent
        )
        
        worker = await crawler_worker_service.create_worker(worker_create)
        print(f"  ✅ 创建Worker: {worker.worker_id}")
        migration_stats["workers"] += 1
        
        return {
            "original_config_id": config_id,
            "crawler_config_id": crawler_config.config_id,
            "backend_config_id": backend_config.backend_id,
            "worker_id": worker.worker_id
        }
        
    except Exception as e:
        print(f"  ❌ 迁移失败: {e}")
        migration_stats["errors"] += 1
        return None


async def create_default_configs_from_templates(migration_config: Dict[str, Any]) -> Dict[str, str]:
    """从模板创建默认配置"""
    try:
        print("🔧 创建默认配置...")
        
        templates = migration_config.get("default_templates", {})
        if not templates:
            print("⚠️ 未找到默认配置模板")
            return {}
        
        # 创建默认爬取配置
        crawler_template = templates.get("crawler_config_template", {})
        if crawler_template:
            crawler_config_create = CrawlerConfigCreate(**crawler_template)
            crawler_config = await crawler_config_service.create_config(crawler_config_create)
            print(f"  ✅ 创建默认爬取配置: {crawler_config.config_id}")
        
        # 创建默认后端配置
        backend_template = templates.get("backend_config_template", {})
        if backend_template:
            backend_config_create = BackendConfigCreate(**backend_template)
            backend_config = await backend_config_service.create_config(backend_config_create)
            print(f"  ✅ 创建默认后端配置: {backend_config.backend_id}")
        
        # 创建默认Worker
        if crawler_template and backend_template:
            worker_create = CrawlerWorkerCreate(
                worker_name="默认Worker",
                description="从旧架构迁移的默认Worker",
                crawler_config_id=crawler_config.config_id,
                backend_config_id=backend_config.backend_id,
                allocated_concurrent=backend_config.max_concurrent
            )
            worker = await crawler_worker_service.create_worker(worker_create)
            print(f"  ✅ 创建默认Worker: {worker.worker_id}")
        
        return {
            "default_crawler_config_id": crawler_config.config_id if crawler_template else None,
            "default_backend_config_id": backend_config.backend_id if backend_template else None,
            "default_worker_id": worker.worker_id if crawler_template and backend_template else None
        }
        
    except Exception as e:
        print(f"❌ 创建默认配置失败: {e}")
        return {}


async def migrate_instance_configs(migration_config: Dict[str, Any]) -> List[Dict[str, str]]:
    """迁移所有实例配置"""
    try:
        print("🔄 开始迁移实例配置...")
        
        instance_configs_data = migration_config.get("configs", {}).get("crawler_instance_configs", {})
        instance_configs = instance_configs_data.get("configs", [])
        
        if not instance_configs:
            print("⚠️ 未找到需要迁移的实例配置")
            return []
        
        migration_results = []
        migration_stats = {
            "crawler_configs": 0,
            "backend_configs": 0,
            "workers": 0,
            "errors": 0
        }
        
        for instance_config in instance_configs:
            result = await migrate_single_config(instance_config, migration_stats)
            if result:
                migration_results.append(result)
        
        print(f"\n📊 迁移统计:")
        print(f"  - 爬取配置: {migration_stats['crawler_configs']}")
        print(f"  - 后端配置: {migration_stats['backend_configs']}")
        print(f"  - Worker: {migration_stats['workers']}")
        print(f"  - 错误: {migration_stats['errors']}")
        
        return migration_results
        
    except Exception as e:
        print(f"❌ 迁移实例配置失败: {e}")
        return []


async def save_migration_report(migration_results: List[Dict[str, str]], default_configs: Dict[str, str]):
    """保存迁移报告"""
    try:
        report = {
            "migration_time": datetime.now().isoformat(),
            "migration_summary": {
                "total_migrated": len(migration_results),
                "default_configs_created": len([v for v in default_configs.values() if v])
            },
            "default_configs": default_configs,
            "migration_results": migration_results,
            "migration_mapping": {
                result["original_config_id"]: {
                    "crawler_config_id": result["crawler_config_id"],
                    "backend_config_id": result["backend_config_id"],
                    "worker_id": result["worker_id"]
                }
                for result in migration_results if result
            }
        }
        
        report_file = Path("config/migration_results.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 迁移报告已保存: {report_file}")
        
        # 生成Markdown报告
        markdown_report = generate_markdown_report(report)
        markdown_file = Path("config/migration_results.md")
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(markdown_report)
        
        print(f"✅ Markdown报告已保存: {markdown_file}")
        
    except Exception as e:
        print(f"❌ 保存迁移报告失败: {e}")


def generate_markdown_report(report: Dict[str, Any]) -> str:
    """生成Markdown格式的迁移报告"""
    markdown = f"""# 架构迁移报告

## 迁移概要

- **迁移时间**: {report['migration_time']}
- **迁移配置数**: {report['migration_summary']['total_migrated']}
- **默认配置数**: {report['migration_summary']['default_configs_created']}

## 默认配置

| 类型 | 配置ID |
|------|--------|
| 默认爬取配置 | {report['default_configs'].get('default_crawler_config_id', 'N/A')} |
| 默认后端配置 | {report['default_configs'].get('default_backend_config_id', 'N/A')} |
| 默认Worker | {report['default_configs'].get('default_worker_id', 'N/A')} |

## 迁移映射

| 原配置ID | 爬取配置ID | 后端配置ID | Worker ID |
|----------|------------|------------|-----------|
"""
    
    for result in report['migration_results']:
        if result:
            markdown += f"| {result['original_config_id'][:8]}... | {result['crawler_config_id'][:8]}... | {result['backend_config_id'][:8]}... | {result['worker_id'][:8]}... |\n"
    
    markdown += f"""
## 迁移状态

✅ **迁移完成**

新架构已成功部署，包含以下组件：
- **爬取配置**: 管理纯爬取相关配置
- **后端配置**: 管理API连接和性能配置  
- **Worker**: 配置组合，支持互斥检查
- **任务分配**: 智能任务分配和冲突检测

## 下一步

1. 验证迁移结果的正确性
2. 测试新架构的功能
3. 更新前端界面以支持新架构
4. 清理旧架构代码（可选）

---
**报告生成时间**: {datetime.now().isoformat()}
"""
    
    return markdown


async def main():
    """主迁移流程"""
    print("🚀 开始架构迁移...")
    
    try:
        # 1. 加载迁移配置
        migration_config = await load_migration_config()
        if not migration_config:
            return 1
        
        print("✅ 迁移配置加载成功")
        
        # 2. 创建默认配置
        default_configs = await create_default_configs_from_templates(migration_config)
        
        # 3. 迁移实例配置
        migration_results = await migrate_instance_configs(migration_config)
        
        # 4. 保存迁移报告
        await save_migration_report(migration_results, default_configs)
        
        print("\n" + "="*50)
        print("🎉 架构迁移完成！")
        print("\n📁 生成的文件:")
        print("   - config/migration_results.json")
        print("   - config/migration_results.md")
        print(f"\n📊 迁移结果:")
        print(f"   - 成功迁移: {len(migration_results)} 个配置")
        print(f"   - 默认配置: {len([v for v in default_configs.values() if v])} 个")
        
        print("\n🔄 新架构组件:")
        print("   - 爬取配置 (CrawlerConfig)")
        print("   - 后端配置 (BackendConfig)")
        print("   - Worker (CrawlerWorker)")
        print("   - 任务分配 (TaskAssignment)")
        
        print("\n🎯 下一步:")
        print("   1. 验证迁移结果")
        print("   2. 测试新架构功能")
        print("   3. 更新前端界面")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
