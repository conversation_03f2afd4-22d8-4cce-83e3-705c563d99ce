#!/usr/bin/env python3
"""
环境测试脚本 - 验证开发环境是否正确配置
"""

import sys
import importlib
import subprocess
import os
from typing import List, <PERSON><PERSON>

def check_python_version() -> bool:
    """检查Python版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本不符合要求: {version.major}.{version.minor}.{version.micro} (需要3.10+)")
        return False

def check_required_packages() -> List[Tuple[str, bool]]:
    """检查必需的Python包"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'alembic',
        'psycopg2',
        'redis',
        'celery',
        'playwright',
        'requests',
        'bs4',  # beautifulsoup4
        'pandas',
        'openpyxl',
        'pydantic'
    ]
    
    results = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}: 已安装")
            results.append((package, True))
        except ImportError:
            print(f"❌ {package}: 未安装")
            results.append((package, False))
    
    return results

def check_docker() -> bool:
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Docker: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker: 未安装或不可用")
        return False

def check_node() -> bool:
    """检查Node.js是否可用"""
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        version_num = int(version.replace('v', '').split('.')[0])
        if version_num >= 18:
            print(f"✅ Node.js: {version}")
            return True
        else:
            print(f"❌ Node.js版本不符合要求: {version} (需要18+)")
            return False
    except (subprocess.CalledProcessError, FileNotFoundError, ValueError):
        print("❌ Node.js: 未安装或不可用")
        return False

def check_directories() -> bool:
    """检查项目目录结构"""
    required_dirs = [
        'backend',
        'frontend',
        'config',
        'database',
        'data/uploads',
        'data/exports',
        'logs',
        'task'
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            all_exist = False
    
    return all_exist

def check_config_files() -> bool:
    """检查配置文件"""
    required_files = [
        'environment.yml',
        'docker-compose.dev.yml',
        'backend/requirements.txt',
        'config/settings.py',
        'env.example'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 配置文件存在: {file_path}")
        else:
            print(f"❌ 配置文件缺失: {file_path}")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("=" * 50)
    print("MonIt 爬虫系统 - 环境检查")
    print("=" * 50)
    
    # 检查各项环境配置
    checks = [
        ("Python版本", check_python_version()),
        ("Docker", check_docker()),
        ("Node.js", check_node()),
        ("项目目录", check_directories()),
        ("配置文件", check_config_files()),
    ]
    
    # 检查Python包
    print("\n📦 Python包检查:")
    package_results = check_required_packages()
    packages_ok = all(result[1] for result in package_results)
    checks.append(("Python包", packages_ok))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("环境检查总结:")
    print("=" * 50)
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{check_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 环境检查通过！可以开始开发。")
        print("\n📝 下一步:")
        print("1. 创建conda环境: conda env create -f environment.yml")
        print("2. 激活环境: conda activate monit-crawler")
        print("3. 启动服务: docker-compose -f docker-compose.dev.yml up -d")
    else:
        print("⚠️  环境检查未完全通过，请根据上述提示修复问题。")
    print("=" * 50)

if __name__ == "__main__":
    main() 