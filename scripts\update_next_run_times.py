#!/usr/bin/env python3
"""
更新任务下次运行时间脚本

目的：
1. 重新计算所有任务的下次运行时间
2. 使用正确的时区和用户配置的时间
3. 应用随机延迟配置
4. 修复之前硬编码的时间问题
"""

import redis
import json
import pytz
from datetime import datetime, timezone, timedelta
import random


class NextRunTimeUpdater:
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """初始化Redis连接"""
        self.redis = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
    
    def calculate_next_run_time(self, schedule_config: dict) -> datetime:
        """计算下次运行时间"""
        # 获取用户设置的时区
        user_timezone = pytz.timezone(schedule_config.get('timezone', 'Asia/Shanghai'))
        now_utc = datetime.now(timezone.utc)
        now_local = now_utc.astimezone(user_timezone)
        
        schedule_type = schedule_config.get('type', 'daily')
        
        if schedule_type == 'daily':
            # 解析用户设置的执行时间
            time_str = schedule_config.get('time')
            if time_str:
                try:
                    time_parts = time_str.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                except (ValueError, IndexError):
                    # 如果时间格式错误，使用默认时间
                    hour, minute = 9, 0
            else:
                hour, minute = 9, 0
            
            # 在用户时区中计算下次执行时间
            next_run_local = now_local.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # 如果今天的执行时间已过，安排到明天
            if next_run_local <= now_local:
                next_run_local = next_run_local + timedelta(days=1)
            
            # 应用随机延迟
            if schedule_config.get('enable_random_delay', False):
                delay_min = schedule_config.get('random_delay_min', 0)
                delay_max = schedule_config.get('random_delay_max', 180)
                random_delay = random.randint(delay_min, delay_max)
                next_run_local = next_run_local + timedelta(minutes=random_delay)
            
            # 转换回UTC时间
            next_run = next_run_local.astimezone(timezone.utc)
        else:
            # 其他调度类型的处理
            next_run = now_utc + timedelta(hours=1)  # 默认1小时后
        
        return next_run
    
    def update_task_next_run(self, task_id: str) -> dict:
        """更新单个任务的下次运行时间"""
        try:
            # 获取任务数据
            task_key = f'monitoring_tasks:{task_id}'
            task_data = self.redis.hgetall(task_key)
            
            if not task_data:
                return {'success': False, 'error': 'Task not found'}
            
            task_name = task_data.get('name', '未知')
            
            # 解析调度配置
            schedule_raw = task_data.get('schedule', '{}')
            try:
                schedule_config = json.loads(schedule_raw)
            except json.JSONDecodeError:
                return {'success': False, 'error': 'Invalid schedule config'}
            
            # 计算新的下次运行时间
            next_run = self.calculate_next_run_time(schedule_config)
            
            # 更新Redis中的下次运行时间
            self.redis.hset(task_key, 'next_run', next_run.isoformat())
            
            # 获取用户时区用于显示
            user_timezone = pytz.timezone(schedule_config.get('timezone', 'Asia/Shanghai'))
            next_run_local = next_run.astimezone(user_timezone)
            
            return {
                'success': True,
                'task_name': task_name,
                'next_run_utc': next_run.isoformat(),
                'next_run_local': next_run_local.strftime('%Y-%m-%d %H:%M:%S %Z'),
                'schedule_time': schedule_config.get('time', '未设置'),
                'timezone': schedule_config.get('timezone', 'Asia/Shanghai'),
                'random_delay': schedule_config.get('enable_random_delay', False)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def update_all_tasks(self) -> dict:
        """更新所有任务的下次运行时间"""
        print("🚀 开始更新所有任务的下次运行时间...")
        print("=" * 60)
        
        # 获取所有任务
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        total_tasks = len(task_ids)
        
        print(f"📊 找到 {total_tasks} 个任务需要更新")
        print()
        
        results = {
            'total_tasks': total_tasks,
            'updated_tasks': 0,
            'failed_tasks': 0,
            'task_results': []
        }
        
        for i, task_id in enumerate(task_ids, 1):
            print(f"[{i}/{total_tasks}] 更新任务: {task_id[:8]}...")
            
            result = self.update_task_next_run(task_id)
            results['task_results'].append({
                'task_id': task_id,
                **result
            })
            
            if result['success']:
                results['updated_tasks'] += 1
                print(f"  ✅ 成功: {result['task_name']}")
                print(f"     执行时间: {result['schedule_time']}")
                print(f"     时区: {result['timezone']}")
                print(f"     下次运行: {result['next_run_local']}")
                print(f"     随机延迟: {'启用' if result['random_delay'] else '禁用'}")
            else:
                results['failed_tasks'] += 1
                print(f"  ❌ 失败: {result['error']}")
            print()
        
        print("=" * 60)
        print(f"📊 更新结果:")
        print(f"   - 总任务数: {results['total_tasks']}")
        print(f"   - 成功更新: {results['updated_tasks']}")
        print(f"   - 更新失败: {results['failed_tasks']}")
        
        if results['failed_tasks'] == 0:
            print(f"\n🎉 所有任务的下次运行时间更新完成！")
        else:
            print(f"\n⚠️  部分任务更新失败，需要检查。")
        
        return results
    
    def verify_updates(self) -> dict:
        """验证更新结果"""
        print("\n✅ 验证更新结果...")
        
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        verification = {
            'total_tasks': len(task_ids),
            'tasks_with_valid_next_run': 0,
            'tasks_with_invalid_next_run': 0,
            'invalid_tasks': []
        }
        
        for task_id in task_ids:
            task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
            task_name = task_data.get('name', '未知')
            next_run = task_data.get('next_run')
            
            if next_run:
                try:
                    # 尝试解析时间
                    next_run_dt = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                    now = datetime.now(timezone.utc)
                    
                    # 检查时间是否合理（在未来且不超过7天）
                    if next_run_dt > now and (next_run_dt - now).days <= 7:
                        verification['tasks_with_valid_next_run'] += 1
                    else:
                        verification['tasks_with_invalid_next_run'] += 1
                        verification['invalid_tasks'].append({
                            'task_id': task_id,
                            'task_name': task_name,
                            'next_run': next_run,
                            'issue': 'Time out of reasonable range'
                        })
                except Exception as e:
                    verification['tasks_with_invalid_next_run'] += 1
                    verification['invalid_tasks'].append({
                        'task_id': task_id,
                        'task_name': task_name,
                        'next_run': next_run,
                        'issue': f'Parse error: {e}'
                    })
            else:
                verification['tasks_with_invalid_next_run'] += 1
                verification['invalid_tasks'].append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'next_run': None,
                    'issue': 'Missing next_run time'
                })
        
        print(f"   - 有效的下次运行时间: {verification['tasks_with_valid_next_run']}")
        print(f"   - 无效的下次运行时间: {verification['tasks_with_invalid_next_run']}")
        
        if verification['invalid_tasks']:
            print(f"   无效任务详情:")
            for task in verification['invalid_tasks']:
                print(f"     - {task['task_name']}: {task['issue']}")
        
        return verification


if __name__ == "__main__":
    updater = NextRunTimeUpdater()
    
    # 更新所有任务
    update_results = updater.update_all_tasks()
    
    # 验证更新结果
    verification_results = updater.verify_updates()
    
    print(f"\n🎯 总结:")
    print(f"   更新成功率: {update_results['updated_tasks']}/{update_results['total_tasks']} ({update_results['updated_tasks']/update_results['total_tasks']*100:.1f}%)")
    print(f"   时间有效率: {verification_results['tasks_with_valid_next_run']}/{verification_results['total_tasks']} ({verification_results['tasks_with_valid_next_run']/verification_results['total_tasks']*100:.1f}%)")
