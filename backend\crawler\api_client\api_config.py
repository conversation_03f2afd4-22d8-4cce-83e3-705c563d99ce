"""
API配置管理

管理外部爬虫API的配置信息。
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from urllib.parse import urljoin


@dataclass
class APIConfig:
    """API配置类"""
    
    # 基础配置
    base_url: str = "http://localhost:8080"
    api_version: str = "v1"
    timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 认证配置
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    auth_type: str = "api_key"  # api_key, bearer_token, basic_auth
    
    # 请求配置
    headers: Dict[str, str] = field(default_factory=dict)
    verify_ssl: bool = True
    
    # 限流配置
    rate_limit_requests: int = 100
    rate_limit_period: int = 60  # seconds
    
    # 爬虫特定配置
    default_platform: str = "mercadolibre"
    supported_platforms: list = field(default_factory=lambda: ["mercadolibre", "amazon", "ebay"])
    
    def __post_init__(self):
        """初始化后处理"""
        # 从环境变量读取配置
        self.base_url = os.getenv("CRAWLER_API_BASE_URL", self.base_url)
        self.api_key = os.getenv("CRAWLER_API_KEY", self.api_key)
        self.api_secret = os.getenv("CRAWLER_API_SECRET", self.api_secret)
        self.auth_type = os.getenv("CRAWLER_API_AUTH_TYPE", self.auth_type)
        self.timeout = float(os.getenv("CRAWLER_API_TIMEOUT", self.timeout))
        
        # 设置默认请求头
        if not self.headers:
            self.headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "MonIt-Crawler-Client/1.0.0"
            }
        
        # 添加认证头
        if self.api_key and self.auth_type == "api_key":
            self.headers["X-API-Key"] = self.api_key
        elif self.api_key and self.auth_type == "bearer_token":
            self.headers["Authorization"] = f"Bearer {self.api_key}"
    
    def get_endpoint_url(self, endpoint: str) -> str:
        """获取完整的端点URL
        
        Args:
            endpoint: API端点路径
            
        Returns:
            str: 完整的URL
        """
        api_base = f"/api/{self.api_version}"
        full_endpoint = f"{api_base}/{endpoint.lstrip('/')}"
        return urljoin(self.base_url, full_endpoint)
    
    def get_crawl_url(self) -> str:
        """获取爬取API的URL"""
        return self.get_endpoint_url("crawl/job")

    def get_batch_crawl_url(self) -> str:
        """获取批量爬取API的URL"""
        return self.get_endpoint_url("tasks/batch")
    
    def get_status_url(self, task_id: str) -> str:
        """获取任务状态API的URL"""
        return self.get_endpoint_url(f"tasks/{task_id}/status")
    
    def get_result_url(self, task_id: str) -> str:
        """获取任务结果API的URL"""
        return self.get_endpoint_url(f"tasks/{task_id}/result")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "base_url": self.base_url,
            "api_version": self.api_version,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "auth_type": self.auth_type,
            "headers": self.headers.copy(),
            "verify_ssl": self.verify_ssl,
            "rate_limit_requests": self.rate_limit_requests,
            "rate_limit_period": self.rate_limit_period,
            "default_platform": self.default_platform,
            "supported_platforms": self.supported_platforms.copy()
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "APIConfig":
        """从字典创建配置"""
        return cls(**config_dict)
    
    @classmethod
    def from_env(cls) -> "APIConfig":
        """从环境变量创建配置"""
        return cls()
    
    def validate(self) -> bool:
        """验证配置"""
        if not self.base_url:
            raise ValueError("base_url is required")
        
        if self.timeout <= 0:
            raise ValueError("timeout must be positive")
        
        if self.max_retries < 0:
            raise ValueError("max_retries must be non-negative")
        
        if self.auth_type not in ["api_key", "bearer_token", "basic_auth", "none"]:
            raise ValueError("Invalid auth_type")
        
        if self.auth_type in ["api_key", "bearer_token"] and not self.api_key:
            raise ValueError(f"api_key is required for auth_type: {self.auth_type}")
        
        return True
