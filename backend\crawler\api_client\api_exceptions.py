"""
API客户端异常类

定义API调用过程中可能出现的各种异常。
"""

from typing import Optional, Dict, Any


class APIException(Exception):
    """API调用基础异常"""
    
    def __init__(
        self, 
        message: str, 
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.response_data = response_data or {}
    
    def __str__(self) -> str:
        if self.status_code:
            return f"API Error {self.status_code}: {self.message}"
        return f"API Error: {self.message}"


class APITimeoutException(APIException):
    """API调用超时异常"""
    
    def __init__(self, message: str = "API request timeout", timeout: Optional[float] = None):
        super().__init__(message)
        self.timeout = timeout
    
    def __str__(self) -> str:
        if self.timeout:
            return f"API Timeout ({self.timeout}s): {self.message}"
        return f"API Timeout: {self.message}"


class APIAuthException(APIException):
    """API认证异常"""
    
    def __init__(self, message: str = "API authentication failed"):
        super().__init__(message, status_code=401)
    
    def __str__(self) -> str:
        return f"API Auth Error: {self.message}"


class APIRateLimitException(APIException):
    """API限流异常"""
    
    def __init__(
        self, 
        message: str = "API rate limit exceeded",
        retry_after: Optional[int] = None
    ):
        super().__init__(message, status_code=429)
        self.retry_after = retry_after
    
    def __str__(self) -> str:
        if self.retry_after:
            return f"API Rate Limit: {self.message} (retry after {self.retry_after}s)"
        return f"API Rate Limit: {self.message}"


class APIServerException(APIException):
    """API服务器异常"""
    
    def __init__(self, message: str = "API server error", status_code: int = 500):
        super().__init__(message, status_code=status_code)
    
    def __str__(self) -> str:
        return f"API Server Error {self.status_code}: {self.message}"


class APIValidationException(APIException):
    """API参数验证异常"""
    
    def __init__(
        self, 
        message: str = "API request validation failed",
        validation_errors: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code=400)
        self.validation_errors = validation_errors or {}
    
    def __str__(self) -> str:
        if self.validation_errors:
            return f"API Validation Error: {self.message} - {self.validation_errors}"
        return f"API Validation Error: {self.message}"
