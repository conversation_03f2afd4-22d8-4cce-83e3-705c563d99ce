import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Progress,
  Alert,
  Select,
  Button,
  Spin,
  Typography,
  Divider,
  Tooltip
} from 'antd';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  ApiOutlined,
  BarChartOutlined
} from '@ant-design/icons';
// import { Line } from '@ant-design/plots'; // 暂时注释掉，使用简化版本

const { Title, Text } = Typography;
const { Option } = Select;

interface MetricsSummary {
  period: string;
  total_requests: number;
  success_rate: string;
  status_breakdown: {
    successful: number;
    validation_errors: number;
    api_errors: number;
    failed: number;
  };
  performance: {
    avg_duration: string;
    max_duration: string;
    min_duration: string;
  };
  url_statistics: {
    avg_urls_per_request: string;
    max_urls_per_request: number;
  };
}

interface TaskMetrics {
  task_id: string;
  execution_id: string;
  start_time: number;
  duration: number;
  status: string;
  urls_count: number;
  error_message?: string;
  url_fetch_time?: number;
  config_fetch_time?: number;
  request_build_time?: number;
}

const CrawlerRequestMonitoring: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [summaryData, setSummaryData] = useState<MetricsSummary | null>(null);
  const [taskMetrics, setTaskMetrics] = useState<TaskMetrics[]>([]);
  const [selectedTaskId, setSelectedTaskId] = useState<string>('');
  const [selectedDays, setSelectedDays] = useState<number>(1);
  const [error, setError] = useState<string>('');

  // 获取指标摘要
  const fetchMetricsSummary = useCallback(async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetch(
        `/api/v1/monitoring/crawler-request-metrics/summary?days=${selectedDays}`
      );
      const result = await response.json();

      if (result.success) {
        setSummaryData(result.data);
      } else {
        const errorMsg = `获取指标摘要失败: ${result.error || '未知错误'}`;
        setError(errorMsg);
        console.error(errorMsg, result);
      }
    } catch (error) {
      const errorMsg = `获取指标摘要异常: ${error}`;
      setError(errorMsg);
      console.error(errorMsg, error);
    } finally {
      setLoading(false);
    }
  }, [selectedDays]);

  // 获取任务指标
  const fetchTaskMetrics = async (taskId: string) => {
    if (!taskId) return;

    try {
      setLoading(true);
      setError('');
      const response = await fetch(
        `/api/v1/monitoring/crawler-request-metrics/task/${taskId}`
      );
      const result = await response.json();

      if (result.success) {
        setTaskMetrics(result.data.metrics || []);
      } else {
        const errorMsg = `获取任务指标失败: ${result.error || '未知错误'}`;
        setError(errorMsg);
        console.error(errorMsg, result);
      }
    } catch (error) {
      const errorMsg = `获取任务指标异常: ${error}`;
      setError(errorMsg);
      console.error(errorMsg, error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetricsSummary();
  }, [fetchMetricsSummary]);

  useEffect(() => {
    if (selectedTaskId) {
      fetchTaskMetrics(selectedTaskId);
    }
  }, [selectedTaskId]);



  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      success: { color: 'green', icon: <CheckCircleOutlined />, text: '成功' },
      validation_error: { color: 'orange', icon: <ExclamationCircleOutlined />, text: '验证错误' },
      api_error: { color: 'red', icon: <ApiOutlined />, text: 'API错误' },
      failed: { color: 'red', icon: <ExclamationCircleOutlined />, text: '失败' },
      building: { color: 'blue', icon: <ClockCircleOutlined />, text: '构建中' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 性能进度条
  const renderPerformanceBar = (duration: number, label: string) => {
    const maxDuration = 10; // 10秒为最大值
    const percent = Math.min((duration / maxDuration) * 100, 100);
    
    let status: "success" | "normal" | "exception" = "success";
    if (duration > 5) status = "exception";
    else if (duration > 3) status = "normal";
    
    return (
      <Tooltip title={`${label}: ${duration.toFixed(3)}s`}>
        <Progress 
          percent={percent} 
          status={status}
          size="small"
          format={() => `${duration.toFixed(3)}s`}
        />
      </Tooltip>
    );
  };

  // 任务指标表格列定义
  const taskMetricsColumns = [
    {
      title: '执行ID',
      dataIndex: 'execution_id',
      key: 'execution_id',
      width: 200,
      render: (text: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {text.slice(-12)}
        </Text>
      )
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 150,
      render: (timestamp: number) => 
        new Date(timestamp * 1000).toLocaleString('zh-CN')
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: renderStatusTag
    },
    {
      title: 'URL数量',
      dataIndex: 'urls_count',
      key: 'urls_count',
      width: 80,
      render: (count: number) => (
        <Tag color={count > 50 ? 'orange' : 'blue'}>
          {count}
        </Tag>
      )
    },
    {
      title: '总耗时',
      dataIndex: 'duration',
      key: 'duration',
      width: 120,
      render: (duration: number) => renderPerformanceBar(duration, '总耗时')
    },
    {
      title: 'URL获取',
      dataIndex: 'url_fetch_time',
      key: 'url_fetch_time',
      width: 120,
      render: (time: number) => time ? renderPerformanceBar(time, 'URL获取') : '-'
    },
    {
      title: '配置获取',
      dataIndex: 'config_fetch_time', 
      key: 'config_fetch_time',
      width: 120,
      render: (time: number) => time ? renderPerformanceBar(time, '配置获取') : '-'
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      render: (error: string) => error ? (
        <Tooltip title={error}>
          <Text type="danger" style={{ fontSize: '12px' }}>
            {error.slice(0, 30)}...
          </Text>
        </Tooltip>
      ) : '-'
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <BarChartOutlined /> 爬虫请求构建监控
      </Title>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="加载错误"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError('')}
          style={{ marginBottom: '16px' }}
        />
      )}
      
      {/* 控制面板 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col>
            <Text strong>查询天数:</Text>
          </Col>
          <Col>
            <Select
              value={selectedDays}
              onChange={setSelectedDays}
              style={{ width: 120 }}
            >
              <Option value={1}>1天</Option>
              <Option value={3}>3天</Option>
              <Option value={7}>7天</Option>
              <Option value={30}>30天</Option>
            </Select>
          </Col>
          <Col>
            <Text strong>任务ID:</Text>
          </Col>
          <Col flex={1}>
            <Select
              value={selectedTaskId}
              onChange={setSelectedTaskId}
              placeholder="选择要查看的任务"
              style={{ width: '100%', maxWidth: '300px' }}
              showSearch
              allowClear
            >
              {/* TODO: 从API获取真实的任务列表 */}
              {/* 暂时为空，等待任务管理API实现 */}
            </Select>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={fetchMetricsSummary}
              loading={loading}
            >
              刷新
            </Button>
          </Col>

        </Row>
      </Card>

      {/* 指标摘要 */}
      <Spin spinning={loading && !summaryData}>
        {summaryData ? (
          <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总请求数"
                value={summaryData?.total_requests || 0}
                prefix={<ApiOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="成功率"
                value={summaryData?.success_rate ? parseFloat(summaryData.success_rate) : 0}
                suffix="%"
                precision={1}
                valueStyle={{
                  color: (summaryData?.success_rate ? parseFloat(summaryData.success_rate) : 0) > 90 ? '#3f8600' : '#cf1322'
                }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均耗时"
                value={summaryData?.performance?.avg_duration || '0.000s'}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均URL数"
                value={summaryData?.url_statistics?.avg_urls_per_request ? parseFloat(summaryData.url_statistics.avg_urls_per_request) : 0}
                precision={1}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
        </Row>
        ) : !loading && !error && (
          <Card style={{ marginBottom: '16px' }}>
            <Alert
              message="暂无监控数据"
              description="系统正在收集监控数据，请稍后刷新查看。"
              type="info"
              showIcon
            />
          </Card>
        )}
      </Spin>

      {/* 状态分布 */}
      <Spin spinning={loading && !summaryData}>
        {summaryData ? (
        <Card title="状态分布" style={{ marginBottom: '16px' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="成功"
                value={summaryData?.status_breakdown?.successful || 0}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="验证错误"
                value={summaryData?.status_breakdown?.validation_errors || 0}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="API错误"
                value={summaryData?.status_breakdown?.api_errors || 0}
                valueStyle={{ color: '#cf1322' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="失败"
                value={summaryData?.status_breakdown?.failed || 0}
                valueStyle={{ color: '#cf1322' }}
              />
            </Col>
          </Row>
        </Card>
        ) : !loading && !error && (
          <Card title="状态分布" style={{ marginBottom: '16px' }}>
            <Alert
              message="暂无状态数据"
              description="等待监控数据收集完成。"
              type="info"
              showIcon
            />
          </Card>
        )}
      </Spin>

      {/* 任务详细指标 */}
      {selectedTaskId && (
        <Card 
          title={`任务指标详情 - ${selectedTaskId}`}
          extra={
            <Button 
              size="small" 
              onClick={() => fetchTaskMetrics(selectedTaskId)}
              loading={loading}
            >
              刷新
            </Button>
          }
        >
          <Spin spinning={loading}>
            <Table
              columns={taskMetricsColumns}
              dataSource={taskMetrics}
              rowKey="execution_id"
              size="small"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
              scroll={{ x: 1000 }}
            />
          </Spin>
        </Card>
      )}

      {/* 性能趋势图 - 简化版本 */}
      {taskMetrics.length > 0 && (
        <Card title="性能趋势" style={{ marginTop: '16px' }}>
          <div style={{ padding: '16px' }}>
            <Alert
              message="性能趋势分析"
              description={
                <div>
                  <p><strong>最近执行统计:</strong></p>
                  <ul>
                    <li>总执行次数: {taskMetrics.length}</li>
                    <li>成功次数: {taskMetrics.filter(m => m.status === 'success').length}</li>
                    <li>平均耗时: {
                      taskMetrics.filter(m => m.duration).length > 0
                        ? (taskMetrics.filter(m => m.duration).reduce((sum, m) => sum + m.duration, 0) / taskMetrics.filter(m => m.duration).length).toFixed(3)
                        : '0.000'
                    }s</li>
                    <li>平均URL数: {
                      taskMetrics.length > 0
                        ? (taskMetrics.reduce((sum, m) => sum + m.urls_count, 0) / taskMetrics.length).toFixed(1)
                        : '0'
                    }</li>
                  </ul>
                  <Text type="secondary">
                    💡 提示: 未来版本将提供更详细的图表分析功能
                  </Text>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        </Card>
      )}

      {/* 使用说明 */}
      <Card title="监控说明" style={{ marginTop: '16px' }}>
        <Alert
          message="爬虫请求构建监控"
          description={
            <div>
              <p>此页面展示爬虫请求构建过程的详细监控数据，包括：</p>
              <ul>
                <li><strong>性能指标</strong>: URL获取时间、配置获取时间、总构建时间</li>
                <li><strong>数据质量</strong>: URL数量、配置完整性、成功率</li>
                <li><strong>错误分析</strong>: 验证错误、API错误、系统错误</li>
                <li><strong>趋势分析</strong>: 性能趋势图和历史数据对比</li>
              </ul>
              <Divider />
              <Text type="secondary">
                💡 提示: 选择特定任务ID可以查看该任务的详细执行历史和性能分析
              </Text>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default CrawlerRequestMonitoring;
