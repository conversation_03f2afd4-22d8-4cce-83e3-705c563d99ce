@echo off
chcp 65001 >nul
echo ====================================================
echo 🔍 Task 01 验证 - 项目初始化与环境设置检查
echo ====================================================
echo.

cd /d "%~dp0\.."

set "error_count=0"

echo 📁 项目结构验证...
if exist "backend" (
    echo ✅ backend目录存在
) else (
    echo ❌ backend目录缺失
    set /a error_count+=1
)

if exist "frontend" (
    echo ✅ frontend目录存在
) else (
    echo ❌ frontend目录缺失
    set /a error_count+=1
)

if exist "config" (
    echo ✅ config目录存在
) else (
    echo ❌ config目录缺失
    set /a error_count+=1
)

if exist "database" (
    echo ✅ database目录存在
) else (
    echo ❌ database目录缺失
    set /a error_count+=1
)

echo.
echo 📦 配置文件验证...
if exist "environment.yml" (
    echo ✅ environment.yml存在
) else (
    echo ❌ environment.yml缺失
    set /a error_count+=1
)

if exist "docker-compose.dev.yml" (
    echo ✅ docker-compose.dev.yml存在
) else (
    echo ❌ docker-compose.dev.yml缺失
    set /a error_count+=1
)

if exist "backend/requirements.txt" (
    echo ✅ backend/requirements.txt存在
) else (
    echo ❌ backend/requirements.txt缺失
    set /a error_count+=1
)

if exist "frontend/package.json" (
    echo ✅ frontend/package.json存在
) else (
    echo ❌ frontend/package.json缺失
    set /a error_count+=1
)

echo.
echo 🔧 系统环境验证...
where conda >nul 2>&1
if errorlevel 1 (
    echo ❌ Conda未安装或不在PATH中
    set /a error_count+=1
) else (
    echo ✅ Conda可用
)

where docker >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装或不在PATH中
    set /a error_count+=1
) else (
    echo ✅ Docker可用
)

where node >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或不在PATH中
    set /a error_count+=1
) else (
    echo ✅ Node.js可用
)

where npm >nul 2>&1
if errorlevel 1 (
    echo ❌ NPM未安装或不在PATH中
    set /a error_count+=1
) else (
    echo ✅ NPM可用
)

echo.
echo 🧪 启动脚本验证...
if exist "scripts/start_dev.bat" (
    echo ✅ 一键启动脚本存在
) else (
    echo ❌ 一键启动脚本缺失
    set /a error_count+=1
)

echo.
echo ====================================================
if %error_count% equ 0 (
    echo 🎉 Task 01 验证完成！环境设置正确！
    echo ✅ 所有检查项都通过
) else (
    echo ❌ Task 01 验证失败！发现 %error_count% 个问题
    echo 请检查上述错误并修复
)
echo ====================================================

pause 