"""
测试完整数据流：从监控任务到爬虫API请求
验证整个系统的数据流是否正常工作
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from app.services.crawler_request_builder import CrawlerRequestBuilder
from app.tasks.enhanced_crawl_task import _enhanced_crawl_async
from app.tasks.monitoring_executor import MonitoringTaskExecutor


class TestCompleteDataFlow:
    """测试完整数据流"""
    
    @pytest.mark.asyncio
    async def test_monitoring_task_to_crawler_api_flow(self):
        """测试从监控任务到爬虫API的完整数据流"""
        
        # 1. 模拟监控任务数据
        task_id = "monitoring-task-12345"
        
        # 模拟任务存储中的任务数据
        mock_task_data = {
            "id": task_id,
            "name": "测试监控任务",
            "platform": "mercadolibre",
            "config": {
                "config_id": "config-67890",
                "llm_query": "提取商品标题、价格、库存信息",
                "llm_schema": '{"type": "object", "properties": {"title": {"type": "string"}, "price": {"type": "string"}, "stock": {"type": "number"}}}',
                "cache_enabled": True,
                "priority": "high",
                "auth_method": "api_key",
                "api_key_prefix": "prod_key_123",
                "user_agent": "MonIt-Production/2.0"
            }
        }
        
        # 模拟URL池中的活跃URL
        mock_url_ids = ["url-001", "url-002", "url-003"]
        mock_active_urls = [
            "https://articulo.mercadolibre.com.mx/MLM-1234567890-producto-test-1",
            "https://articulo.mercadolibre.com.mx/MLM-1234567891-producto-test-2",
            "https://articulo.mercadolibre.com.mx/MLM-1234567892-producto-test-3"
        ]
        
        # 模拟系统配置
        mock_system_config = MagicMock()
        mock_system_config.default_callback_url = "http://backend:8000/api/v1/crawler/callback"
        mock_system_config.max_concurrent_tasks = 2
        mock_system_config.batch_timeout = 600
        
        # 2. 模拟各个服务的响应
        with patch('app.services.crawler_request_builder.TaskStorageService') as mock_task_storage, \
             patch('app.services.crawler_request_builder.UrlPoolService') as mock_url_pool, \
             patch('app.services.crawler_request_builder.SystemConfigService') as mock_system_service, \
             patch('app.services.crawler_request_builder.redis') as mock_redis:
            
            # 设置TaskStorageService模拟
            mock_storage_instance = AsyncMock()
            mock_storage_instance.get_task.return_value = mock_task_data
            mock_storage_instance.get_task_urls.return_value = mock_url_ids
            mock_task_storage.return_value = mock_storage_instance
            
            # 设置UrlPoolService模拟
            mock_pool_instance = AsyncMock()
            
            # 创建模拟的URL项目
            mock_url_items = []
            for i, url in enumerate(mock_active_urls):
                mock_url_item = MagicMock()
                mock_url_item.status = "active"
                mock_url_item.url = url
                mock_url_items.append(mock_url_item)
            
            mock_pool_instance.get_url_by_id.side_effect = mock_url_items
            mock_url_pool.return_value = mock_pool_instance
            
            # 设置SystemConfigService模拟
            mock_system_service.get_crawler_config.return_value = mock_system_config
            
            # 设置Redis客户端模拟
            mock_redis_client = AsyncMock()
            mock_redis.from_url.return_value = mock_redis_client
            
            # 3. 测试完整的请求构建流程
            execution_context = {
                "execution_id": "exec-test-123",
                "client_ip": "*************",
                "celery_task_id": "celery-test-456"
            }
            
            # 构建爬虫请求
            crawler_request = await CrawlerRequestBuilder.build_request_for_task(
                task_id=task_id,
                execution_context=execution_context
            )
            
            # 4. 验证请求结构的完整性
            assert "request" in crawler_request
            assert "auth_context" in crawler_request
            
            # 验证request部分
            request_data = crawler_request["request"]
            assert request_data["urls"] == mock_active_urls
            assert request_data["q"] == "提取商品标题、价格、库存信息"
            assert request_data["schema_str"] == '{"type": "object", "properties": {"title": {"type": "string"}, "price": {"type": "string"}, "stock": {"type": "number"}}}'
            assert request_data["cache"] is True
            assert request_data["priority"] == "high"
            assert "Task_monitoring-task-12345_" in request_data["batch_name"]
            assert "监控任务 monitoring-task-12345 的批次执行" in request_data["batch_description"]
            assert request_data["callback_url"] == "http://backend:8000/api/v1/crawler/callback"
            assert request_data["max_concurrent_tasks"] == 2
            assert request_data["batch_timeout"] == 600
            
            # 验证auth_context部分
            auth_context = crawler_request["auth_context"]
            assert auth_context["user_id"] == "monit_system"
            assert auth_context["role"] == "user"
            assert auth_context["permissions"] == []
            assert auth_context["auth_method"] == "api_key"
            assert auth_context["api_key_prefix"] == "prod_key_123"
            assert auth_context["client_ip"] == "*************"
            assert auth_context["user_agent"] == "MonIt-Production/2.0"
            assert auth_context["celery_task_id"] == "celery-test-456"
            assert auth_context["execution_id"] == "exec-test-123"
            assert "authenticated_at" in auth_context
            
            # 5. 验证数据来源的正确性
            # URL来源：监控任务的活跃URL
            assert len(request_data["urls"]) == 3
            assert all("mercadolibre.com.mx" in url for url in request_data["urls"])
            
            # 配置来源：爬取配置
            config = mock_task_data["config"]
            assert request_data["q"] == config["llm_query"]
            assert request_data["schema_str"] == config["llm_schema"]
            assert request_data["cache"] == config["cache_enabled"]
            assert request_data["priority"] == config["priority"]
            assert auth_context["auth_method"] == config["auth_method"]
            assert auth_context["api_key_prefix"] == config["api_key_prefix"]
            assert auth_context["user_agent"] == config["user_agent"]
            
            # 系统配置来源：环境变量
            assert request_data["callback_url"] == mock_system_config.default_callback_url
            assert request_data["max_concurrent_tasks"] == mock_system_config.max_concurrent_tasks
            assert request_data["batch_timeout"] == mock_system_config.batch_timeout
            
            # 自动生成字段
            assert request_data["batch_name"].startswith("Task_")
            assert request_data["batch_description"].startswith("监控任务")
            assert auth_context["user_id"] == "monit_system"
            assert auth_context["role"] == "user"
            
            # 6. 验证服务调用
            mock_storage_instance.get_task.assert_called_once_with(task_id)
            mock_storage_instance.get_task_urls.assert_called_once_with(task_id)
            assert mock_pool_instance.get_url_by_id.call_count == 3
            mock_system_service.get_crawler_config.assert_called_once()
            mock_redis_client.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_enhanced_crawl_task_integration(self):
        """测试增强爬取任务的集成"""
        
        # 模拟Celery任务
        mock_celery_task = MagicMock()
        mock_celery_task.request.id = "celery-integration-test"
        
        task_id = "integration-test-task"
        execution_context = {"integration_test": True}
        
        # 模拟完整的爬虫请求
        mock_crawler_request = {
            "request": {
                "urls": [
                    "https://articulo.mercadolibre.com.mx/MLM-1111111111-integration-test-1",
                    "https://articulo.mercadolibre.com.mx/MLM-2222222222-integration-test-2"
                ],
                "q": "提取商品完整信息用于集成测试",
                "schema_str": '{"type": "object", "properties": {"title": {"type": "string"}, "price": {"type": "string"}, "description": {"type": "string"}}}',
                "cache": True,
                "priority": "medium",
                "batch_name": "Task_integration-test-task_20250809_150000",
                "batch_description": "监控任务 integration-test-task 的批次执行 - 2 URLs",
                "callback_url": "http://backend:8000/api/v1/crawler/callback",
                "max_concurrent_tasks": 2,
                "batch_timeout": 600
            },
            "auth_context": {
                "user_id": "monit_system",
                "role": "user",
                "permissions": [],
                "auth_method": "api_key",
                "api_key_prefix": "integration_test_key",
                "client_ip": "**********",
                "user_agent": "MonIt-Integration-Test/1.0",
                "celery_task_id": "celery-integration-test",
                "authenticated_at": "2025-08-09T15:00:00"
            }
        }
        
        # 模拟爬虫API成功响应
        mock_api_result = {
            "status": "success",
            "batch_id": "integration-batch-789",
            "submitted_urls": 2,
            "estimated_completion": "2025-08-09T15:30:00",
            "crawler_worker": "worker-node-1"
        }
        
        with patch('app.tasks.enhanced_crawl_task.CrawlerRequestBuilder') as mock_builder, \
             patch('app.tasks.enhanced_crawl_task.CrawlerAPIService') as mock_api_service:
            
            # 设置模拟返回值
            mock_builder.build_request_for_task = AsyncMock(return_value=mock_crawler_request)
            
            mock_api_instance = AsyncMock()
            mock_api_instance.submit_batch_request.return_value = mock_api_result
            mock_api_service.return_value = mock_api_instance
            
            # 执行增强爬取任务
            result = await _enhanced_crawl_async(
                mock_celery_task, task_id, execution_context
            )
            
            # 验证完整数据流的结果
            assert result["status"] == "success"
            assert result["task_id"] == task_id
            assert result["celery_task_id"] == "celery-integration-test"
            assert result["urls_count"] == 2
            assert result["priority"] == "medium"
            assert result["batch_name"] == "Task_integration-test-task_20250809_150000"
            assert result["crawler_api_result"] == mock_api_result
            assert result["method"] == "enhanced_crawl_task"
            
            # 验证数据流的各个环节
            # 1. 请求构建调用
            mock_builder.build_request_for_task.assert_called_once()
            call_args = mock_builder.build_request_for_task.call_args
            assert call_args[1]["task_id"] == task_id
            assert "celery_task_id" in call_args[1]["execution_context"]
            
            # 2. 爬虫API调用
            mock_api_instance.submit_batch_request.assert_called_once_with(mock_crawler_request)
            
            # 3. 验证请求内容的数据来源正确性
            submitted_request = mock_api_instance.submit_batch_request.call_args[0][0]
            
            # URL来源验证
            assert len(submitted_request["request"]["urls"]) == 2
            assert all("mercadolibre.com.mx" in url for url in submitted_request["request"]["urls"])
            
            # 配置来源验证
            assert submitted_request["request"]["q"] == "提取商品完整信息用于集成测试"
            assert submitted_request["request"]["cache"] is True
            assert submitted_request["request"]["priority"] == "medium"
            
            # 系统配置验证
            assert submitted_request["request"]["callback_url"] == "http://backend:8000/api/v1/crawler/callback"
            assert submitted_request["request"]["max_concurrent_tasks"] == 2
            assert submitted_request["request"]["batch_timeout"] == 600
            
            # 认证配置验证
            assert submitted_request["auth_context"]["user_id"] == "monit_system"
            assert submitted_request["auth_context"]["auth_method"] == "api_key"
            assert submitted_request["auth_context"]["api_key_prefix"] == "integration_test_key"
            
            print("✅ 完整数据流验证成功：监控任务 → CrawlerRequestBuilder → 爬虫API")
    
    @pytest.mark.asyncio
    async def test_data_source_mapping(self):
        """测试数据来源映射的正确性"""
        
        task_id = "data-mapping-test"
        
        # 定义各个数据源
        monitoring_task_config = {
            "llm_query": "用户级配置：LLM查询指令",
            "llm_schema": '{"用户级配置": "数据提取Schema"}',
            "cache_enabled": True,
            "priority": "high",
            "auth_method": "api_key",
            "api_key_prefix": "user_config_key",
            "user_agent": "用户级配置UserAgent"
        }
        
        system_config = {
            "default_callback_url": "系统级配置：回调URL",
            "max_concurrent_tasks": 4,
            "batch_timeout": 900
        }
        
        execution_context = {
            "client_ip": "运行时信息：客户端IP",
            "celery_task_id": "运行时信息：Celery任务ID",
            "execution_id": "运行时信息：执行ID"
        }
        
        active_urls = [
            "数据源：监控任务的活跃URL1",
            "数据源：监控任务的活跃URL2"
        ]
        
        # 模拟CrawlerRequestBuilder的各个方法
        with patch.object(CrawlerRequestBuilder, 'get_active_urls_for_task') as mock_get_urls, \
             patch.object(CrawlerRequestBuilder, 'get_crawl_config_for_task') as mock_get_config, \
             patch('app.services.crawler_request_builder.SystemConfigService') as mock_system_service:
            
            # 设置模拟返回值
            mock_get_urls.return_value = active_urls
            mock_get_config.return_value = monitoring_task_config
            
            mock_system_config_obj = MagicMock()
            mock_system_config_obj.default_callback_url = system_config["default_callback_url"]
            mock_system_config_obj.max_concurrent_tasks = system_config["max_concurrent_tasks"]
            mock_system_config_obj.batch_timeout = system_config["batch_timeout"]
            mock_system_service.get_crawler_config.return_value = mock_system_config_obj
            
            # 构建请求
            crawler_request = await CrawlerRequestBuilder.build_crawler_request(
                task_id=task_id,
                urls=active_urls,
                crawl_config=monitoring_task_config,
                execution_context=execution_context
            )
            
            # 验证数据来源映射
            request_data = crawler_request["request"]
            auth_context = crawler_request["auth_context"]
            
            # 验证URL来源：监控任务的活跃URL
            assert request_data["urls"] == active_urls
            
            # 验证用户级配置来源：爬取配置
            assert request_data["q"] == monitoring_task_config["llm_query"]
            assert request_data["schema_str"] == monitoring_task_config["llm_schema"]
            assert request_data["cache"] == monitoring_task_config["cache_enabled"]
            assert request_data["priority"] == monitoring_task_config["priority"]
            assert auth_context["auth_method"] == monitoring_task_config["auth_method"]
            assert auth_context["api_key_prefix"] == monitoring_task_config["api_key_prefix"]
            assert auth_context["user_agent"] == monitoring_task_config["user_agent"]
            
            # 验证系统级配置来源：环境变量
            assert request_data["callback_url"] == system_config["default_callback_url"]
            assert request_data["max_concurrent_tasks"] == system_config["max_concurrent_tasks"]
            assert request_data["batch_timeout"] == system_config["batch_timeout"]
            
            # 验证运行时信息来源：执行上下文
            assert auth_context["client_ip"] == execution_context["client_ip"]
            assert auth_context["celery_task_id"] == execution_context["celery_task_id"]
            assert auth_context["execution_id"] == execution_context["execution_id"]
            
            # 验证自动生成字段
            assert request_data["batch_name"].startswith("Task_")
            assert request_data["batch_description"].startswith("监控任务")
            assert auth_context["user_id"] == "monit_system"
            assert auth_context["role"] == "user"
            assert auth_context["permissions"] == []
            assert "authenticated_at" in auth_context
            
            print("✅ 数据来源映射验证成功：各个字段的数据来源正确")
    
    def test_data_flow_documentation(self):
        """验证数据流文档的准确性"""
        
        # 这个测试验证我们的文档分析是否准确
        expected_data_sources = {
            # request部分
            "urls": "监控任务的活跃URL",
            "q": "爬取配置.llm_query",
            "schema_str": "爬取配置.llm_schema", 
            "cache": "爬取配置.cache_enabled",
            "priority": "爬取配置.priority",
            "batch_name": "自动生成",
            "batch_description": "自动生成",
            "callback_url": "系统配置.default_callback_url",
            "max_concurrent_tasks": "系统配置.max_concurrent_tasks",
            "batch_timeout": "系统配置.batch_timeout",
            
            # auth_context部分
            "user_id": "固定值(monit_system)",
            "role": "固定值(user)",
            "permissions": "固定值([])",
            "auth_method": "爬取配置.auth_method",
            "api_key_prefix": "爬取配置.api_key_prefix",
            "client_ip": "执行上下文.client_ip",
            "user_agent": "爬取配置.user_agent",
            "authenticated_at": "自动生成"
        }
        
        # 验证文档的完整性
        assert len(expected_data_sources) == 18  # 确保覆盖了所有字段
        
        # 验证分类的正确性
        user_config_fields = ["q", "schema_str", "cache", "priority", "auth_method", "api_key_prefix", "user_agent"]
        system_config_fields = ["callback_url", "max_concurrent_tasks", "batch_timeout"]
        auto_generated_fields = ["batch_name", "batch_description", "authenticated_at"]
        fixed_value_fields = ["user_id", "role", "permissions"]
        runtime_fields = ["urls", "client_ip"]
        
        total_fields = len(user_config_fields) + len(system_config_fields) + len(auto_generated_fields) + len(fixed_value_fields) + len(runtime_fields)
        assert total_fields == len(expected_data_sources)
        
        print("✅ 数据流文档验证成功：字段分类和来源描述准确")


if __name__ == "__main__":
    pytest.main([__file__])
