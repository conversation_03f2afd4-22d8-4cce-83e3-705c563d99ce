#!/usr/bin/env python3
"""
测试Worker API格式修复
验证系统是否发送了正确格式的请求到Worker后端
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_worker_backend_request():
    """测试Worker后端是否收到正确格式的请求"""
    
    # 检查Worker后端健康状态
    logger.info("1. 检查Worker后端健康状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:11235/health", timeout=5) as response:
                if response.status == 200:
                    logger.info("✅ Worker后端健康检查通过")
                else:
                    logger.warning(f"⚠️ Worker后端健康检查失败: {response.status}")
        except Exception as e:
            logger.error(f"❌ Worker后端不可访问: {e}")
            return False
    
    # 检查Worker后端API端点
    logger.info("2. 检查Worker后端API端点...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试正确的端点
            async with session.get("http://localhost:11235/api/crawl/job", timeout=5) as response:
                logger.info(f"GET /api/crawl/job 响应状态: {response.status}")
                
            # 测试POST端点（不发送数据，只检查端点是否存在）
            async with session.post("http://localhost:11235/api/crawl/job", 
                                   json={}, timeout=5) as response:
                logger.info(f"POST /api/crawl/job 响应状态: {response.status}")
                if response.status != 404:
                    logger.info("✅ Worker后端API端点存在")
                else:
                    logger.warning("⚠️ Worker后端API端点不存在")
                    
        except Exception as e:
            logger.error(f"❌ Worker后端API端点检查失败: {e}")
    
    # 检查任务执行状态
    logger.info("3. 检查任务执行状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8000/api/v1/tasks/status") as response:
                if response.status == 200:
                    status = await response.json()
                    logger.info(f"任务管理器状态: {status['status']}")
                    logger.info(f"运行中批次: {status['running_batches']}")
                    logger.info(f"待处理批次: {status['pending_batches']}")
                    
                    if status['running_batches'] > 0:
                        logger.info("✅ 有任务正在执行")
                        return True
                    else:
                        logger.info("ℹ️ 没有任务正在执行")
                        
        except Exception as e:
            logger.error(f"❌ 检查任务状态失败: {e}")
    
    return False

async def test_direct_worker_api():
    """直接测试Worker API格式"""
    
    logger.info("4. 直接测试Worker API格式...")
    
    # 创建测试payload
    test_payload = {
        "urls": [
            "https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM"
        ],
        "browser_config": {
            "headless": True,
            "verbose": False,
            "viewport_width": 1920,
            "viewport_height": 1080,
            "wait_for": 2,
            "timeout": 30,
            "ignore_https_errors": True,
            "extra_args": [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation"
            ]
        },
        "crawler_config": {
            "method": "arun_many",
            "verbose": True,
            "check_robots_txt": False,
            "fetch_ssl_certificate": False,
            "simulate_user": True,
            "magic": True,
            "override_navigator": True,
            "remove_overlay_elements": True,
            "ignore_body_visibility": True,
            "adjust_viewport_to_content": True,
            "wait_until": "domcontentloaded",
            "wait_for_images": False,
            "page_timeout": 60000,
            "delay_before_return_html": 0.1,
            "js_only": False,
            "scan_full_page": True,
            "process_iframes": True,
            "scroll_delay": 0.2,
            "cache_mode": "BYPASS",
            "screenshot": False,
            "pdf": False,
            "capture_mhtml": False,
            "exclude_external_images": False,
            "exclude_all_images": False,
            "image_score_threshold": 50,
            "image_description_min_word_threshold": 50,
            "table_score_threshold": 7,
            "capture_network_requests": False,
            "capture_console_messages": False,
            "log_console": False,
            "extraction_strategy": "LLMExtractionStrategy",
            "chunking_strategy": "IdentityChunking",
            "markdown_generator": "DefaultMarkdownGenerator",
            "bypass_cache": True,
            "semaphore_count": 2,
            "stream": True,
            "mean_delay": 0.1,
            "max_range": 3,
            "task_type": "batch",
            "scheduler": {
                "type": "AsyncQueueManager",
                "pool_size": 10,
                "memory_threshold": 4096
            },
            "monitor": {
                "display_mode": "detailed",
                "show_progress": True,
                "log_errors": True
            }
        }
    }
    
    logger.info("发送测试请求到Worker后端...")
    logger.info(f"请求格式预览: {len(json.dumps(test_payload))} 字符")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                "http://localhost:11235/api/crawl/job",
                json=test_payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            ) as response:
                logger.info(f"Worker后端响应状态: {response.status}")
                
                if response.status == 200:
                    logger.info("✅ Worker后端接受了请求格式")
                    result = await response.json()
                    logger.info(f"响应数据: {json.dumps(result, indent=2)[:200]}...")
                    return True
                else:
                    logger.warning(f"⚠️ Worker后端拒绝了请求: {response.status}")
                    error_text = await response.text()
                    logger.warning(f"错误信息: {error_text[:200]}...")
                    
        except Exception as e:
            logger.error(f"❌ 直接测试Worker API失败: {e}")
    
    return False

async def main():
    """主函数"""
    logger.info("🚀 开始测试Worker API格式修复...")
    logger.info("=" * 60)
    
    try:
        # 测试Worker后端请求
        backend_ok = await test_worker_backend_request()
        
        # 直接测试Worker API
        api_ok = await test_direct_worker_api()
        
        logger.info("\n" + "=" * 60)
        logger.info("📋 测试总结:")
        
        if backend_ok:
            logger.info("1. ✅ Worker后端连接正常")
        else:
            logger.info("1. ⚠️ Worker后端连接有问题")
            
        if api_ok:
            logger.info("2. ✅ Worker API格式正确")
        else:
            logger.info("2. ⚠️ Worker API格式需要调整")
        
        if backend_ok and api_ok:
            logger.info("\n🎉 Worker API格式修复成功！")
            logger.info("现在系统应该能正确向Worker后端发送请求了。")
        else:
            logger.info("\n💡 需要进一步调试:")
            logger.info("   - 检查Worker后端是否正在运行")
            logger.info("   - 验证API端点是否正确")
            logger.info("   - 确认请求格式是否匹配")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
