#!/usr/bin/env python3
"""
测试Worker创建422错误
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_worker_creation_422_error():
    """测试Worker创建422错误"""
    
    print("🧪 测试Worker创建422错误...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 1. 获取可用的配置
            print("\n1. 获取可用的配置...")
            
            # 获取爬取配置
            crawl_response = await client.get("http://localhost:8000/api/v1/crawl-configs/")
            if crawl_response.status_code != 200:
                print(f"❌ 获取爬取配置失败: {crawl_response.status_code}")
                return
            
            crawl_result = crawl_response.json()
            if not crawl_result.get('success') or not crawl_result['data']['configs']:
                print(f"❌ 没有可用的爬取配置")
                return
            
            crawl_config = crawl_result['data']['configs'][0]
            print(f"✅ 爬取配置: {crawl_config['name']} ({crawl_config['id']})")
            
            # 获取后端配置
            backend_response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            if backend_response.status_code != 200:
                print(f"❌ 获取后端配置失败: {backend_response.status_code}")
                return
            
            backend_configs = backend_response.json()
            if not backend_configs:
                print(f"❌ 没有可用的后端配置")
                return
            
            backend_config = backend_configs[0]
            print(f"✅ 后端配置: {backend_config['backend_name']} ({backend_config['backend_id']})")
            
            # 2. 测试不同的Worker创建数据
            test_cases = [
                {
                    "name": "基础数据",
                    "data": {
                        "worker_name": "test-worker-basic",
                        "description": "基础测试Worker",
                        "crawler_config_id": crawl_config['id'],
                        "backend_config_id": backend_config['backend_id']
                    }
                },
                {
                    "name": "完整数据",
                    "data": {
                        "worker_name": "test-worker-full",
                        "description": "完整测试Worker",
                        "crawler_config_id": crawl_config['id'],
                        "backend_config_id": backend_config['backend_id'],
                        "priority": "normal",
                        "max_concurrent_tasks": 5,
                        "allocated_concurrent": 3
                    }
                },
                {
                    "name": "前端表单数据",
                    "data": {
                        "worker_name": "test-worker-frontend",
                        "description": "前端表单测试Worker",
                        "crawler_config_id": crawl_config['id'],
                        "backend_config_id": backend_config['backend_id'],
                        "priority": "normal",
                        "max_concurrent_tasks": 100,  # 从后端同步的值
                        "allocated_concurrent": 80,   # 从后端同步的值
                        "tags": []
                    }
                }
            ]
            
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n{i}. 测试{test_case['name']}...")
                print(f"   数据: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
                
                response = await client.post(
                    "http://localhost:8000/api/v1/crawler-workers/",
                    json=test_case['data']
                )
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ 创建成功: {result['worker_name']} ({result['worker_id']})")
                else:
                    error_text = response.text
                    print(f"   ❌ 创建失败: {error_text}")
                    
                    # 尝试解析错误详情
                    try:
                        error_json = response.json()
                        if 'detail' in error_json:
                            if isinstance(error_json['detail'], str):
                                print(f"   错误详情: {error_json['detail']}")
                            elif isinstance(error_json['detail'], list):
                                print(f"   验证错误:")
                                for error in error_json['detail']:
                                    if isinstance(error, dict):
                                        loc = " -> ".join(str(x) for x in error.get('loc', []))
                                        msg = error.get('msg', '')
                                        input_val = error.get('input', '')
                                        print(f"     - {loc}: {msg} (输入: {input_val})")
                                    else:
                                        print(f"     - {error}")
                        
                        if 'errors' in error_json:
                            print(f"   错误列表: {error_json['errors']}")
                            
                    except:
                        print(f"   原始错误: {error_text}")
                
                print()
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_worker_creation_422_error()
    print(f"\n🏁 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
