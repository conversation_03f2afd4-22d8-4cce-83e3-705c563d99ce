# 任务01：Excel上传与URL解析功能实现

## 📋 任务概述

**任务目标**: 实现Excel文件上传、解析URL并存储到Redis的完整功能
**预计工期**: 3-4天
**优先级**: 高
**状态**: 待开始

## 🎯 具体需求

### 功能需求
1. **前端Excel上传组件**
   - 支持.xlsx, .xls格式文件上传
   - 文件大小限制：最大50MB
   - 上传进度显示
   - 文件格式验证
   - 上传成功后显示解析结果

2. **后端Excel解析API**
   - 解析Excel文件中的URL
   - 自动识别URL列
   - 平台类型自动检测
   - URL去重处理
   - 数据验证和清洗

3. **Redis数据存储**
   - URL池数据结构设计
   - 高效的存储和查询
   - 数据过期策略
   - 索引优化

### 技术需求
- 前端：React + Ant Design Upload组件
- 后端：FastAPI + pandas + openpyxl
- 存储：Redis Hash + Set数据结构
- 文件处理：临时文件管理

## 🏗️ 技术实现方案

### 前端实现
```typescript
// Excel上传组件
interface ExcelUploadProps {
  onUploadSuccess: (urls: UrlPoolItem[]) => void;
  onUploadError: (error: string) => void;
}

// 上传处理逻辑
const handleUpload = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await uploadAndParseExcel(formData);
    onUploadSuccess(response.data.urls);
  } catch (error) {
    onUploadError(error.message);
  }
};
```

### 后端API设计
```python
@router.post("/excel/upload-and-parse")
async def upload_and_parse_excel(
    file: UploadFile = File(...),
    platform_filter: Optional[str] = None
):
    """上传Excel文件并解析URL"""
    
    # 1. 文件验证
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(400, "不支持的文件格式")
    
    # 2. 解析Excel
    urls = await parse_excel_urls(file)
    
    # 3. 数据清洗和去重
    cleaned_urls = clean_and_deduplicate_urls(urls)
    
    # 4. 存储到Redis
    url_items = await store_urls_to_redis(cleaned_urls, file.filename)
    
    return {
        "success": True,
        "total_urls": len(url_items),
        "new_urls": len([u for u in url_items if u.is_new]),
        "duplicate_urls": len(urls) - len(url_items),
        "urls": url_items
    }
```

### Redis数据结构
```python
# URL池主数据 - Hash结构
url_pool:{url_id} = {
    "id": "uuid",
    "url": "https://example.com",
    "platform": "mercadolibre",
    "source_file": "products_2025.xlsx",
    "added_at": "2025-07-04T10:30:00Z",
    "status": "active",
    "last_check": null,
    "check_count": 0,
    "success_count": 0,
    "metadata": "{}"
}

# URL索引 - Set结构
url_pool:all_ids = {url_id1, url_id2, ...}
url_pool:platform:mercadolibre = {url_id1, url_id3, ...}
url_pool:source:products_2025.xlsx = {url_id1, url_id2, ...}
url_pool:status:active = {url_id1, url_id2, ...}

# URL去重 - Set结构
url_pool:url_hashes = {hash1, hash2, ...}
```

## 📝 实施步骤

### 步骤1：后端Excel解析功能开发
1. **创建Excel解析服务**
   ```python
   # backend/app/services/excel_parser.py
   class ExcelUrlParser:
       def parse_urls(self, file_path: str) -> List[Dict]
       def detect_url_columns(self, df: pd.DataFrame) -> List[str]
       def extract_platform(self, url: str) -> str
       def validate_url(self, url: str) -> bool
   ```

2. **实现URL存储服务**
   ```python
   # backend/app/services/url_pool_service.py
   class UrlPoolService:
       def store_urls(self, urls: List[Dict], source: str) -> List[UrlPoolItem]
       def get_url_by_id(self, url_id: str) -> UrlPoolItem
       def check_url_exists(self, url: str) -> bool
       def update_url_status(self, url_id: str, status: str) -> bool
   ```

3. **创建API路由**
   ```python
   # backend/app/api/excel_routes.py
   @router.post("/upload-and-parse")
   async def upload_and_parse_excel(...)
   ```

### 步骤2：前端上传组件开发
1. **创建Excel上传组件**
   ```typescript
   // frontend/src/components/ExcelUpload/index.tsx
   const ExcelUpload: React.FC<ExcelUploadProps> = ({ onSuccess, onError }) => {
     // 上传逻辑实现
   };
   ```

2. **集成到URL池管理页面**
   ```typescript
   // frontend/src/pages/UrlPool/index.tsx
   const UrlPoolPage: React.FC = () => {
     // 页面逻辑实现
   };
   ```

3. **创建API服务**
   ```typescript
   // frontend/src/services/urlPoolApi.ts
   export const uploadAndParseExcel = (formData: FormData) => {
     return api.post('/api/v1/excel/upload-and-parse', formData);
   };
   ```

### 步骤3：数据存储优化
1. **Redis连接池配置**
2. **数据序列化优化**
3. **索引策略实现**
4. **缓存策略设计**

### 步骤4：测试和验证
1. **单元测试**
   - Excel解析功能测试
   - URL存储功能测试
   - API接口测试

2. **集成测试**
   - 端到端上传流程测试
   - 大文件处理测试
   - 并发上传测试

3. **性能测试**
   - 文件解析性能测试
   - Redis存储性能测试
   - 内存使用监控

## 📊 验收标准

### 功能验收
- [ ] 支持Excel文件上传（.xlsx, .xls格式）
- [ ] 自动识别和解析URL列
- [ ] 平台类型自动检测准确率>90%
- [ ] URL去重功能正常
- [ ] 数据成功存储到Redis
- [ ] 前端显示解析结果

### 性能验收
- [ ] 1000行Excel文件解析时间<10秒
- [ ] 10MB文件上传成功率>99%
- [ ] Redis存储响应时间<100ms
- [ ] 内存使用合理（<500MB）

### 用户体验验收
- [ ] 上传进度实时显示
- [ ] 错误信息清晰明确
- [ ] 解析结果展示友好
- [ ] 操作流程直观简单

## 🔧 开发环境准备

### 后端依赖
```python
# requirements.txt 新增
pandas>=1.5.0
openpyxl>=3.0.0
xlrd>=2.0.0
redis>=4.0.0
```

### 前端依赖
```json
// package.json 可能需要的新依赖
{
  "dependencies": {
    "antd": "^5.0.0",
    "@ant-design/icons": "^5.0.0"
  }
}
```

### Redis配置
```yaml
# docker-compose.dev.yml 确保Redis配置正确
redis:
  image: redis:7-alpine
  ports:
    - "6379:6379"
  volumes:
    - redis_data:/data
```

## 🚀 部署注意事项

1. **文件上传限制**
   - Nginx上传大小限制配置
   - FastAPI文件大小限制配置

2. **临时文件清理**
   - 定期清理上传的临时文件
   - 异常情况下的文件清理

3. **Redis内存管理**
   - 设置合理的内存限制
   - 配置数据过期策略

4. **错误处理**
   - 文件格式错误处理
   - 网络中断恢复机制
   - 数据一致性保证

## 📈 后续优化方向

1. **功能扩展**
   - 支持CSV格式文件
   - 批量文件上传
   - 文件模板下载

2. **性能优化**
   - 异步文件处理
   - 分块上传支持
   - 缓存优化

3. **用户体验**
   - 拖拽上传支持
   - 预览功能
   - 历史记录

---

**负责人**: AI Assistant  
**创建时间**: 2025-07-04  
**预计开始**: 2025-07-04  
**预计完成**: 2025-07-07  
**当前状态**: 待开始
