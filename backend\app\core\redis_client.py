"""
Redis客户端管理模块
提供统一的Redis连接和操作接口
"""

import logging
from typing import Optional
import redis.asyncio as redis

logger = logging.getLogger(__name__)

# 全局Redis客户端实例
_redis_client: Optional[redis.Redis] = None


async def get_redis_client(redis_url: str = "redis://redis:6379/0") -> redis.Redis:
    """获取Redis客户端实例
    
    Args:
        redis_url: Redis连接URL
        
    Returns:
        Redis客户端实例
    """
    global _redis_client
    
    if _redis_client is None:
        try:
            _redis_client = redis.from_url(redis_url)
            await _redis_client.ping()
            logger.info(f"Connected to Redis: {redis_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    return _redis_client


async def close_redis_client():
    """关闭Redis客户端连接"""
    global _redis_client
    
    if _redis_client:
        try:
            await _redis_client.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
        finally:
            _redis_client = None


class RedisManager:
    """Redis管理器类"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.redis_url = redis_url
        self.client: Optional[redis.Redis] = None
    
    async def connect(self):
        """连接到Redis"""
        try:
            self.client = redis.from_url(self.redis_url)
            await self.client.ping()
            logger.info(f"Redis manager connected: {self.redis_url}")
        except Exception as e:
            logger.error(f"Redis manager connection failed: {e}")
            raise
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.client:
            try:
                await self.client.close()
                logger.info("Redis manager disconnected")
            except Exception as e:
                logger.error(f"Error disconnecting Redis manager: {e}")
            finally:
                self.client = None
    
    async def ping(self) -> bool:
        """检查Redis连接状态"""
        if not self.client:
            return False
        
        try:
            await self.client.ping()
            return True
        except Exception:
            return False
    
    async def get(self, key: str) -> Optional[str]:
        """获取键值"""
        if not self.client:
            return None
        
        try:
            result = await self.client.get(key)
            return result.decode() if result else None
        except Exception as e:
            logger.error(f"Error getting key {key}: {e}")
            return None
    
    async def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置键值"""
        if not self.client:
            return False
        
        try:
            await self.client.set(key, value, ex=ex)
            return True
        except Exception as e:
            logger.error(f"Error setting key {key}: {e}")
            return False
    
    async def delete(self, *keys: str) -> int:
        """删除键"""
        if not self.client or not keys:
            return 0
        
        try:
            return await self.client.delete(*keys)
        except Exception as e:
            logger.error(f"Error deleting keys {keys}: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self.client:
            return False
        
        try:
            return bool(await self.client.exists(key))
        except Exception as e:
            logger.error(f"Error checking key existence {key}: {e}")
            return False
    
    async def keys(self, pattern: str = "*") -> list:
        """获取匹配模式的键列表"""
        if not self.client:
            return []
        
        try:
            keys = await self.client.keys(pattern)
            return [key.decode() for key in keys]
        except Exception as e:
            logger.error(f"Error getting keys with pattern {pattern}: {e}")
            return []
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """获取哈希字段值"""
        if not self.client:
            return None
        
        try:
            result = await self.client.hget(name, key)
            return result.decode() if result else None
        except Exception as e:
            logger.error(f"Error getting hash field {name}.{key}: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: str) -> bool:
        """设置哈希字段值"""
        if not self.client:
            return False
        
        try:
            await self.client.hset(name, key, value)
            return True
        except Exception as e:
            logger.error(f"Error setting hash field {name}.{key}: {e}")
            return False
    
    async def hgetall(self, name: str) -> dict:
        """获取哈希所有字段"""
        if not self.client:
            return {}
        
        try:
            result = await self.client.hgetall(name)
            return {k.decode(): v.decode() for k, v in result.items()}
        except Exception as e:
            logger.error(f"Error getting all hash fields {name}: {e}")
            return {}
    
    async def llen(self, name: str) -> int:
        """获取列表长度"""
        if not self.client:
            return 0
        
        try:
            return await self.client.llen(name)
        except Exception as e:
            logger.error(f"Error getting list length {name}: {e}")
            return 0
    
    async def lrange(self, name: str, start: int, end: int) -> list:
        """获取列表范围"""
        if not self.client:
            return []
        
        try:
            result = await self.client.lrange(name, start, end)
            return [item.decode() for item in result]
        except Exception as e:
            logger.error(f"Error getting list range {name}[{start}:{end}]: {e}")
            return []
    
    async def lpush(self, name: str, *values: str) -> int:
        """向列表左侧推入值"""
        if not self.client or not values:
            return 0
        
        try:
            return await self.client.lpush(name, *values)
        except Exception as e:
            logger.error(f"Error pushing to list {name}: {e}")
            return 0
    
    async def rpop(self, name: str) -> Optional[str]:
        """从列表右侧弹出值"""
        if not self.client:
            return None
        
        try:
            result = await self.client.rpop(name)
            return result.decode() if result else None
        except Exception as e:
            logger.error(f"Error popping from list {name}: {e}")
            return None
    
    async def sadd(self, name: str, *values: str) -> int:
        """向集合添加成员"""
        if not self.client or not values:
            return 0
        
        try:
            return await self.client.sadd(name, *values)
        except Exception as e:
            logger.error(f"Error adding to set {name}: {e}")
            return 0
    
    async def smembers(self, name: str) -> set:
        """获取集合所有成员"""
        if not self.client:
            return set()
        
        try:
            result = await self.client.smembers(name)
            return {member.decode() for member in result}
        except Exception as e:
            logger.error(f"Error getting set members {name}: {e}")
            return set()
    
    async def scard(self, name: str) -> int:
        """获取集合成员数量"""
        if not self.client:
            return 0
        
        try:
            return await self.client.scard(name)
        except Exception as e:
            logger.error(f"Error getting set cardinality {name}: {e}")
            return 0
