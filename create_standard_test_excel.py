#!/usr/bin/env python3
"""
创建标准格式的测试Excel文件
"""

import pandas as pd
import requests

def create_standard_excel():
    """创建标准的3列Excel文件"""
    print("📝 创建标准格式的测试Excel文件")
    
    # 创建标准的3列数据
    data = [
        {
            'chinese_name': '苹果手机',
            'spanish_name': 'Teléfono Apple',
            'product_url': 'https://www.amazon.com/dp/B08N5WRWNW'
        },
        {
            'chinese_name': '三星手机',
            'spanish_name': 'Teléfono Samsung',
            'product_url': 'https://articulo.mercadolibre.com.mx/MLM-123456789'
        },
        {
            'chinese_name': '华为手机',
            'spanish_name': 'Teléfono Huawei',
            'product_url': 'https://www.ebay.com/itm/123456789'
        },
        {
            'chinese_name': '小米手机',
            'spanish_name': 'Teléfono Xiaomi',
            'product_url': 'https://www.aliexpress.com/item/123456789.html'
        },
        {
            'chinese_name': 'OPPO手机',
            'spanish_name': 'Teléfono OPPO',
            'product_url': 'https://shopee.com.mx/product/123456789'
        }
    ]
    
    df = pd.DataFrame(data)
    filename = 'standard_test.xlsx'
    df.to_excel(filename, index=False)
    
    print(f"✅ 标准测试文件创建成功: {filename}")
    print(f"   包含 {len(data)} 个URL")
    print("   列结构: chinese_name, spanish_name, product_url")
    
    return filename

def test_upload(filename):
    """测试上传文件"""
    print(f"\n📤 测试上传: {filename}")
    
    try:
        with open(filename, 'rb') as f:
            files = {
                'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            
            response = requests.post("http://localhost:8000/api/v1/excel/upload-and-parse", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 上传成功:")
            print(f"   - 总URL数: {result['total_urls']}")
            print(f"   - 新增URL: {result['new_urls']}")
            print(f"   - 重复URL: {result['duplicate_urls']}")
            print(f"   - 无效URL: {result['invalid_urls']}")
            
            if result.get('urls'):
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['urls'][:3]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')}")
            
            return True
        else:
            print(f"❌ 上传失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return False

def test_url_pool_list():
    """测试URL池列表"""
    print(f"\n📋 测试URL池列表")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/urls/pool")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取列表成功:")
            print(f"   - 总数: {result['total']}")
            print(f"   - 当前页数据: {len(result['data'])}")
            
            if result['data']:
                print(f"   - URL样本:")
                for i, url_item in enumerate(result['data'][:3]):
                    print(f"     {i+1}. [{url_item.get('platform', 'unknown')}] {url_item.get('url', 'N/A')[:50]}...")
            
            return True
        else:
            print(f"❌ 获取列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取列表异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试标准Excel上传")
    print("=" * 40)
    
    # 创建标准Excel文件
    filename = create_standard_excel()
    
    try:
        # 测试上传
        if test_upload(filename):
            # 测试列表
            test_url_pool_list()
        
    finally:
        # 清理文件
        import os
        if os.path.exists(filename):
            os.remove(filename)
            print(f"\n🧹 已清理测试文件: {filename}")

if __name__ == "__main__":
    main()
