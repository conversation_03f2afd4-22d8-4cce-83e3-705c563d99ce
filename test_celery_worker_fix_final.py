#!/usr/bin/env python3
"""
最终测试Celery Worker显示修复
"""

import requests
import json
import time

def create_test_task():
    """创建测试任务"""
    print("1. 创建测试任务...")
    
    # 直接调用Celery任务
    try:
        # 使用现有的监控任务执行API
        response = requests.get("http://localhost:8000/api/v1/monitoring-tasks")
        if response.status_code == 200:
            tasks = response.json()
            if tasks:
                task_id = tasks[0]['id']
                print(f"找到任务: {task_id}")
                
                # 尝试执行任务
                exec_response = requests.post(f"http://localhost:8000/api/v1/monitoring-tasks/{task_id}/execute")
                if exec_response.status_code == 200:
                    print("✅ 任务执行成功")
                    return True
                else:
                    print(f"任务执行失败: {exec_response.status_code} - {exec_response.text}")
            else:
                print("没有找到可用的监控任务")
        else:
            print(f"获取监控任务失败: {response.status_code}")
    except Exception as e:
        print(f"创建任务失败: {e}")
    
    return False

def wait_for_new_requests():
    """等待新的爬虫请求"""
    print("2. 等待新的爬虫请求生成...")
    
    # 获取当前最新请求的时间戳
    try:
        response = requests.get("http://localhost:8000/api/v1/crawler-debug/requests?limit=1")
        if response.status_code == 200:
            requests_data = response.json()
            if requests_data:
                latest_timestamp = requests_data[0]['timestamp']
                print(f"当前最新请求时间: {latest_timestamp}")
                
                # 等待新请求
                for i in range(30):  # 等待30秒
                    time.sleep(1)
                    response = requests.get("http://localhost:8000/api/v1/crawler-debug/requests?limit=1")
                    if response.status_code == 200:
                        new_requests = response.json()
                        if new_requests and new_requests[0]['timestamp'] != latest_timestamp:
                            print("✅ 检测到新的爬虫请求")
                            return new_requests[0]
                    
                    if i % 5 == 0:
                        print(f"等待中... ({i}/30秒)")
                
                print("⚠️ 30秒内没有检测到新请求")
            else:
                print("没有找到现有请求")
    except Exception as e:
        print(f"等待新请求失败: {e}")
    
    return None

def check_celery_worker_fix():
    """检查Celery Worker修复"""
    print("3. 检查Celery Worker修复...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/crawler-debug/requests?limit=5")
        if response.status_code == 200:
            requests_data = response.json()
            print(f"获取到 {len(requests_data)} 条记录")
            
            for i, req in enumerate(requests_data):
                print(f"\n记录 {i+1}:")
                print(f"  - 请求ID: {req['id']}")
                print(f"  - 时间戳: {req['timestamp']}")
                print(f"  - Celery Worker: {req['celery_worker']}")
                print(f"  - 状态: {req['status']}")
                
                if req['celery_worker'] and req['celery_worker'] != 'null':
                    print(f"  ✅ Celery Worker显示正常: {req['celery_worker']}")
                    return True
                else:
                    print(f"  ❌ Celery Worker仍然为空")
            
            print("\n❌ 所有记录的Celery Worker都为空")
        else:
            print(f"获取请求记录失败: {response.status_code}")
    except Exception as e:
        print(f"检查失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🔧 最终测试Celery Worker显示修复...\n")
    
    # 创建测试任务
    if create_test_task():
        # 等待新请求
        new_request = wait_for_new_requests()
        if new_request:
            print(f"\n新请求详情:")
            print(f"  - 请求ID: {new_request['id']}")
            print(f"  - Celery Worker: {new_request['celery_worker']}")
            
            if new_request['celery_worker'] and new_request['celery_worker'] != 'null':
                print("🎉 Celery Worker修复成功!")
            else:
                print("❌ Celery Worker修复失败")
    
    # 检查所有记录
    check_celery_worker_fix()

if __name__ == "__main__":
    main()
