/**
 * Worker详情模态框组件
 * 显示Worker的详细信息和统计数据
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Tag,
  Progress,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Table,
  Space,
  Button
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  <PERSON>boltOutlined,
  TeamOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

import { 
  workerApi,
  CrawlerWorker,
  TaskAssignment,
  WorkerStatus,
  WorkerPriority
} from '../../../services';

interface WorkerDetailModalProps {
  workerId: string;
  onClose: () => void;
}

const WorkerDetailModal: React.FC<WorkerDetailModalProps> = ({ workerId, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [worker, setWorker] = useState<CrawlerWorker | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [assignments, setAssignments] = useState<TaskAssignment[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWorkerData();
  }, [workerId]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchWorkerData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [workerData, assignmentsData] = await Promise.all([
        workerApi.getWorker(workerId),
        workerApi.getWorkerAssignments(workerId)
      ]);

      // 暂时设置空的统计数据
      const statsData = null;
      
      setWorker(workerData);
      setStats(statsData);
      setAssignments(assignmentsData);
    } catch (err) {
      setError(`获取Worker数据失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={fetchWorkerData}>
            重试
          </Button>
        }
      />
    );
  }

  if (!worker) {
    return (
      <Alert
        message="Worker不存在"
        description="找不到指定的Worker"
        type="warning"
        showIcon
      />
    );
  }

  // 确保worker对象有必要的字段
  const safeWorker = {
    ...worker,
    tags: worker.tags || [],
    max_concurrent_tasks: worker.max_concurrent_tasks || worker.allocated_concurrent || 0,
    current_tasks: worker.current_tasks || 0,
    health_score: worker.health_score || 0,
    created_at: worker.created_at || null,
    last_heartbeat: worker.last_heartbeat || null,
    description: worker.description || '',
    crawler_config_id: worker.crawler_config_id || '',
    backend_config_id: worker.backend_config_id || ''
  };

  // 确保stats对象有必要的字段并处理NaN值
  const safeStats = stats ? {
    total_tasks: stats.total_tasks || 0,
    completed_tasks: stats.completed_tasks || 0,
    failed_tasks: stats.failed_tasks || 0,
    success_rate: isNaN(stats.success_rate) ? 0 : (stats.success_rate || 0),
    avg_execution_time: isNaN(stats.avg_execution_time) ? 0 : (stats.avg_execution_time || 0),
    current_load: isNaN(stats.current_load) ? 0 : (stats.current_load || 0),
    peak_load: isNaN(stats.peak_load) ? 0 : (stats.peak_load || 0),
    uptime_hours: isNaN(stats.uptime_hours) ? 0 : (stats.uptime_hours || 0),
    error_rate: isNaN(stats.error_rate) ? 0 : (stats.error_rate || 0),
    throughput_per_hour: isNaN(stats.throughput_per_hour) ? 0 : (stats.throughput_per_hour || 0),
    memory_usage_mb: isNaN(stats.memory_usage_mb) ? 0 : (stats.memory_usage_mb || 0),
    cpu_usage_percent: isNaN(stats.cpu_usage_percent) ? 0 : (stats.cpu_usage_percent || 0)
  } : null;

  // 状态标签渲染
  const renderWorkerStatus = (status: WorkerStatus) => {
    const statusConfig = {
      active: { color: 'green', text: '活跃' },
      idle: { color: 'default', text: '空闲' },
      running: { color: 'green', text: '运行中' },
      busy: { color: 'orange', text: '忙碌' },
      error: { color: 'red', text: '错误' },
      maintenance: { color: 'blue', text: '维护中' }
    };

    const config = statusConfig[status] || statusConfig.idle;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderWorkerPriority = (priority: WorkerPriority) => {
    const priorityConfig = {
      low: { color: 'default', text: '低' },
      normal: { color: 'blue', text: '普通' },
      high: { color: 'orange', text: '高' },
      urgent: { color: 'red', text: '紧急' }
    };
    
    const config = priorityConfig[priority] || priorityConfig.normal;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 任务分配表格列定义
  const assignmentColumns: TableColumnsType<any> = [
    {
      title: '任务信息',
      key: 'task_info',
      width: 250,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.task_name}</div>
          <div style={{ fontSize: '12px', color: '#666', fontFamily: 'monospace' }}>
            ID: {record.task_id.slice(0, 8)}...
          </div>
          <Tag color={record.task_status === 'active' ? 'green' : 'orange'}>
            {record.task_status}
          </Tag>
        </div>
      ),
    },
    {
      title: '分配状态',
      dataIndex: 'assignment_status',
      key: 'assignment_status',
      width: 120,
      render: (status: string) => {
        const statusConfig: Record<string, { color: string; text: string }> = {
          pending: { color: 'default', text: '待处理' },
          assigned: { color: 'blue', text: '已分配' },
          running: { color: 'orange', text: '运行中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' },
          cancelled: { color: 'default', text: '已取消' }
        };
        const config = statusConfig[status] || statusConfig.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: 'Worker角色',
      key: 'worker_role',
      width: 120,
      render: (_, record) => (
        <div>
          {record.is_primary_worker && <Tag color="blue">主要</Tag>}
          {record.is_current_worker && <Tag color="green">当前</Tag>}
          {!record.is_primary_worker && !record.is_current_worker && <Tag>普通</Tag>}
        </div>
      ),
    },
    {
      title: '进度',
      key: 'progress',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            {record.processed_urls || 0} / {record.total_urls || 0}
          </div>
          <Progress
            percent={record.total_urls > 0 ? (record.processed_urls / record.total_urls * 100) : 0}
            size="small"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '分配时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
  ];

  return (
    <div>
      {/* 基本信息 */}
      <Card title="基本信息" style={{ marginBottom: 16 }}>
        <Descriptions column={2}>
          <Descriptions.Item label="Worker名称">{safeWorker.worker_name}</Descriptions.Item>
          <Descriptions.Item label="状态">{renderWorkerStatus(safeWorker.status)}</Descriptions.Item>
          <Descriptions.Item label="优先级">{renderWorkerPriority(safeWorker.priority)}</Descriptions.Item>
          <Descriptions.Item label="健康评分">
            <Progress
              percent={safeWorker.health_score * 100}
              size="small"
              status={safeWorker.health_score >= 0.8 ? 'success' : safeWorker.health_score >= 0.5 ? 'normal' : 'exception'}
              format={() => `${(safeWorker.health_score * 100).toFixed(0)}%`}
            />
          </Descriptions.Item>
          <Descriptions.Item label="当前任务">
            {safeWorker.current_tasks}/{safeWorker.max_concurrent_tasks}
          </Descriptions.Item>
          <Descriptions.Item label="分配并发">{safeWorker.allocated_concurrent}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {safeWorker.created_at ? new Date(safeWorker.created_at).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="最后心跳">
            {safeWorker.last_heartbeat ? new Date(safeWorker.last_heartbeat).toLocaleString() : '从未'}
          </Descriptions.Item>
          <Descriptions.Item label="前端配置">
            {safeWorker.crawler_config_name || safeWorker.crawler_config_id || '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="后端配置">
            {safeWorker.backend_config_name || safeWorker.backend_config_id || '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="描述" span={2}>
            {safeWorker.description || '无描述'}
          </Descriptions.Item>
          <Descriptions.Item label="标签" span={2}>
            <Space>
              {safeWorker.tags.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
              {safeWorker.tags.length === 0 && <span style={{ color: '#999' }}>无标签</span>}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 统计信息 */}
      {safeStats && (
        <Card title="统计信息" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="总任务数"
                value={safeStats.total_tasks}
                prefix={<TeamOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="完成任务"
                value={safeStats.completed_tasks}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="失败任务"
                value={safeStats.failed_tasks}
                prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="成功率"
                value={safeStats.success_rate * 100}
                precision={1}
                suffix="%"
                prefix={<ThunderboltOutlined />}
              />
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={8}>
              <Statistic
                title="平均任务时长"
                value={safeStats.avg_execution_time}
                precision={1}
                suffix="秒"
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="当前负载"
                value={safeStats.current_load}
                suffix={`/${safeWorker.max_concurrent_tasks}`}
              />
              <Progress
                percent={safeWorker.max_concurrent_tasks > 0 ? (safeStats.current_load / safeWorker.max_concurrent_tasks) * 100 : 0}
                size="small"
                style={{ marginTop: 8 }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="运行时间"
                value={safeStats.uptime_hours}
                precision={1}
                suffix="小时"
              />
            </Col>
          </Row>
        </Card>
      )}

      {/* 最近任务分配 */}
      <Card 
        title="最近任务分配" 
        style={{ marginBottom: 16 }}
        extra={
          <Button 
            size="small" 
            icon={<ReloadOutlined />} 
            onClick={fetchWorkerData}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={assignmentColumns}
          dataSource={assignments}
          rowKey="assignment_id"
          size="small"
          pagination={false}
          locale={{
            emptyText: '暂无任务分配记录'
          }}
        />
      </Card>

      {/* 配置信息 */}
      <Card title="配置信息">
        <Descriptions column={1}>
          <Descriptions.Item label="爬取配置ID">
            <span style={{ fontFamily: 'monospace' }}>{safeWorker.crawler_config_id}</span>
          </Descriptions.Item>
          <Descriptions.Item label="后端配置ID">
            <span style={{ fontFamily: 'monospace' }}>{safeWorker.backend_config_id}</span>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default WorkerDetailModal;
