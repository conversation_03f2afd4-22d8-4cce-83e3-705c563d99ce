"""
动态调度管理器
管理监控任务的Celery Beat动态注册和调度
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from celery.schedules import crontab
from celery import current_app

from app.celery_app import celery_app
from app.models.monitoring_task import ScheduleConfig
from app.core.redis_client import get_redis_client

logger = logging.getLogger(__name__)


class ScheduleManager:
    """动态调度管理器"""
    
    def __init__(self):
        self.redis_client = None
        self.schedule_key_prefix = "celery:beat:schedule"
    
    async def initialize(self):
        """初始化调度管理器"""
        try:
            self.redis_client = await get_redis_client()
            logger.info("ScheduleManager initialized")
        except Exception as e:
            logger.error(f"Failed to initialize ScheduleManager: {e}")
            raise
    
    async def register_monitoring_task(self, task_id: str, schedule_config: ScheduleConfig) -> bool:
        """注册监控任务到Celery Beat调度"""
        try:
            if not self.redis_client:
                await self.initialize()
            
            # 创建Celery调度配置
            schedule = self._create_celery_schedule(schedule_config)
            if not schedule:
                logger.error(f"Failed to create schedule for task {task_id}")
                return False
            
            # 创建任务配置
            task_name = f"monitoring_task_{task_id}"
            task_config = {
                'task': 'app.tasks.monitoring_executor.execute_monitoring_task',
                'schedule': schedule,
                'args': [task_id],
                'options': {
                    'queue': 'monitor_queue',
                    'routing_key': 'monitor_queue'
                },
                'enabled': True
            }
            
            # 动态添加到Celery Beat调度
            celery_app.conf.beat_schedule[task_name] = task_config
            
            # 保存调度配置到Redis
            await self._save_schedule_config(task_id, task_config, schedule_config)
            
            logger.info(f"Successfully registered monitoring task {task_id} to Celery Beat")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register monitoring task {task_id}: {e}")
            return False
    
    async def unregister_monitoring_task(self, task_id: str) -> bool:
        """从Celery Beat调度中移除监控任务"""
        try:
            if not self.redis_client:
                await self.initialize()
            
            task_name = f"monitoring_task_{task_id}"
            
            # 从Celery Beat调度中移除
            if task_name in celery_app.conf.beat_schedule:
                del celery_app.conf.beat_schedule[task_name]
            
            # 从Redis中移除调度配置
            await self._remove_schedule_config(task_id)
            
            logger.info(f"Successfully unregistered monitoring task {task_id} from Celery Beat")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister monitoring task {task_id}: {e}")
            return False
    
    async def update_monitoring_task_schedule(self, task_id: str, schedule_config: ScheduleConfig) -> bool:
        """更新监控任务的调度配置"""
        try:
            # 先移除旧的调度
            await self.unregister_monitoring_task(task_id)
            
            # 注册新的调度
            return await self.register_monitoring_task(task_id, schedule_config)
            
        except Exception as e:
            logger.error(f"Failed to update monitoring task schedule {task_id}: {e}")
            return False
    
    async def pause_monitoring_task(self, task_id: str) -> bool:
        """暂停监控任务调度"""
        try:
            if not self.redis_client:
                await self.initialize()
            
            task_name = f"monitoring_task_{task_id}"
            
            # 禁用调度但不删除
            if task_name in celery_app.conf.beat_schedule:
                celery_app.conf.beat_schedule[task_name]['enabled'] = False
            
            # 更新Redis中的状态
            schedule_key = f"{self.schedule_key_prefix}:{task_id}"
            schedule_data = await self.redis_client.get(schedule_key)
            if schedule_data:
                import json
                data = json.loads(schedule_data)
                data['enabled'] = False
                await self.redis_client.set(schedule_key, json.dumps(data))
            
            logger.info(f"Successfully paused monitoring task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause monitoring task {task_id}: {e}")
            return False
    
    async def resume_monitoring_task(self, task_id: str) -> bool:
        """恢复监控任务调度"""
        try:
            if not self.redis_client:
                await self.initialize()
            
            task_name = f"monitoring_task_{task_id}"
            
            # 启用调度
            if task_name in celery_app.conf.beat_schedule:
                celery_app.conf.beat_schedule[task_name]['enabled'] = True
            
            # 更新Redis中的状态
            schedule_key = f"{self.schedule_key_prefix}:{task_id}"
            schedule_data = await self.redis_client.get(schedule_key)
            if schedule_data:
                import json
                data = json.loads(schedule_data)
                data['enabled'] = True
                await self.redis_client.set(schedule_key, json.dumps(data))
            
            logger.info(f"Successfully resumed monitoring task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume monitoring task {task_id}: {e}")
            return False
    
    async def get_task_schedule_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务调度状态"""
        try:
            if not self.redis_client:
                await self.initialize()
            
            schedule_key = f"{self.schedule_key_prefix}:{task_id}"
            schedule_data = await self.redis_client.get(schedule_key)
            
            if schedule_data:
                import json
                return json.loads(schedule_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get task schedule status {task_id}: {e}")
            return None
    
    async def list_scheduled_tasks(self) -> Dict[str, Any]:
        """列出所有已调度的监控任务"""
        try:
            if not self.redis_client:
                await self.initialize()
            
            pattern = f"{self.schedule_key_prefix}:*"
            keys = await self.redis_client.keys(pattern)
            
            scheduled_tasks = {}
            for key in keys:
                task_id = key.split(':')[-1]
                schedule_data = await self.redis_client.get(key)
                if schedule_data:
                    import json
                    scheduled_tasks[task_id] = json.loads(schedule_data)
            
            return scheduled_tasks
            
        except Exception as e:
            logger.error(f"Failed to list scheduled tasks: {e}")
            return {}
    
    def _create_celery_schedule(self, schedule_config: ScheduleConfig):
        """创建Celery调度配置"""
        try:
            schedule_type = schedule_config.type
            
            if schedule_type == "daily":
                # 每日执行
                time_parts = schedule_config.time.split(':')
                hour = int(time_parts[0])
                minute = int(time_parts[1])
                
                return crontab(hour=hour, minute=minute)
            
            elif schedule_type == "weekly":
                # 每周执行
                time_parts = schedule_config.time.split(':')
                hour = int(time_parts[0])
                minute = int(time_parts[1])
                days = schedule_config.days or [1]  # 默认周一
                
                return crontab(hour=hour, minute=minute, day_of_week=','.join(map(str, days)))
            
            elif schedule_type == "hourly":
                # 每小时执行
                minute = schedule_config.interval or 0
                return crontab(minute=minute)
            
            elif schedule_type == "custom":
                # 自定义间隔（分钟）
                interval = schedule_config.interval or 60
                return interval * 60  # 转换为秒
            
            else:
                logger.error(f"Unsupported schedule type: {schedule_type}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to create Celery schedule: {e}")
            return None
    
    async def _save_schedule_config(self, task_id: str, task_config: Dict[str, Any], schedule_config: ScheduleConfig):
        """保存调度配置到Redis"""
        try:
            schedule_key = f"{self.schedule_key_prefix}:{task_id}"
            
            config_data = {
                "task_id": task_id,
                "task_config": task_config,
                "schedule_config": schedule_config.dict(),
                "created_at": datetime.now().isoformat(),
                "enabled": True
            }
            
            import json
            await self.redis_client.set(schedule_key, json.dumps(config_data, default=str))
            
        except Exception as e:
            logger.error(f"Failed to save schedule config for task {task_id}: {e}")
    
    async def _remove_schedule_config(self, task_id: str):
        """从Redis中移除调度配置"""
        try:
            schedule_key = f"{self.schedule_key_prefix}:{task_id}"
            await self.redis_client.delete(schedule_key)
            
        except Exception as e:
            logger.error(f"Failed to remove schedule config for task {task_id}: {e}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.redis_client:
                await self.redis_client.close()
        except Exception as e:
            logger.error(f"Failed to cleanup ScheduleManager: {e}")


# 全局调度管理器实例
_schedule_manager = None


async def get_schedule_manager() -> ScheduleManager:
    """获取调度管理器实例"""
    global _schedule_manager
    
    if _schedule_manager is None:
        _schedule_manager = ScheduleManager()
        await _schedule_manager.initialize()
    
    return _schedule_manager


async def cleanup_schedule_manager():
    """清理调度管理器"""
    global _schedule_manager
    
    if _schedule_manager:
        await _schedule_manager.cleanup()
        _schedule_manager = None
