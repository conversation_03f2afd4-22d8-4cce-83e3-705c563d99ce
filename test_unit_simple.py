#!/usr/bin/env python3
"""
简单的单元测试运行器

不依赖pytest，直接运行各个组件的单元测试。
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_unit_tests():
    """运行单元测试"""
    
    print("=" * 60)
    print("🧪 Task 03-03: 单元测试开始")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 导入所有模块
        from backend.crawler.anti_detection.user_agent_manager import UserAgentManager, UserAgentInfo
        from backend.crawler.anti_detection.header_manager import RequestHeaderManager
        from backend.crawler.anti_detection.behavior_simulator import BehaviorSimulator
        from backend.crawler.anti_detection.session_manager import SessionManager
        from backend.crawler.anti_detection.fingerprint_faker import FingerprintFaker
        from backend.crawler.anti_detection.mercadolibre_anti_detection import MercadoLibreAntiDetection
        
        print("✅ 模块导入成功")
        
        # 测试1: UserAgentManager
        print("\n📱 测试UserAgentManager...")
        try:
            ua_manager = UserAgentManager()
            
            # 测试初始化
            assert len(ua_manager.user_agents) > 0, "用户代理列表不能为空"
            assert ua_manager.config['weights']['chrome'] == 0.6, "Chrome权重应为0.6"
            
            # 测试获取随机UA
            ua_string, ua_info = ua_manager.get_random_ua(device_type="desktop")
            assert ua_string is not None, "UA字符串不能为空"
            assert len(ua_string) > 50, "UA字符串长度应大于50"
            assert isinstance(ua_info, UserAgentInfo), "应返回UserAgentInfo对象"
            assert ua_info.device_type == "desktop", "设备类型应为desktop"
            
            # 测试UA验证
            valid_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            assert ua_manager.validate_ua(valid_ua) == True, "有效UA应通过验证"
            
            invalid_ua = "short"
            assert ua_manager.validate_ua(invalid_ua) == False, "无效UA应不通过验证"
            
            print("   ✅ UserAgentManager测试通过")
            test_results.append(("UserAgentManager", True, None))
            
        except Exception as e:
            print(f"   ❌ UserAgentManager测试失败: {e}")
            test_results.append(("UserAgentManager", False, str(e)))
        
        # 测试2: RequestHeaderManager
        print("\n🌐 测试RequestHeaderManager...")
        try:
            header_manager = RequestHeaderManager()
            
            # 测试初始化
            assert hasattr(header_manager, 'header_templates'), "应有header_templates属性"
            assert hasattr(header_manager, 'language_preferences'), "应有language_preferences属性"
            
            # 测试生成请求头
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            headers = header_manager.generate_headers(user_agent)
            
            assert 'User-Agent' in headers, "应包含User-Agent头"
            assert 'Accept' in headers, "应包含Accept头"
            assert 'Accept-Language' in headers, "应包含Accept-Language头"
            assert headers['User-Agent'] == user_agent, "User-Agent应匹配输入"
            
            # 测试ML特定头部
            ml_headers = header_manager.get_mercadolibre_headers(user_agent, country_code="AR")
            assert 'X-Client-ID' in ml_headers, "应包含X-Client-ID头"
            assert 'X-Api-Client' in ml_headers, "应包含X-Api-Client头"
            assert 'es-AR' in ml_headers['Accept-Language'], "应包含阿根廷语言设置"
            
            # 测试头部验证
            valid_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml'
            }
            assert header_manager.validate_headers(valid_headers) == True, "有效头部应通过验证"
            
            print("   ✅ RequestHeaderManager测试通过")
            test_results.append(("RequestHeaderManager", True, None))
            
        except Exception as e:
            print(f"   ❌ RequestHeaderManager测试失败: {e}")
            test_results.append(("RequestHeaderManager", False, str(e)))
        
        # 测试3: BehaviorSimulator (异步测试)
        print("\n🎭 测试BehaviorSimulator...")
        try:
            async def test_behavior_simulator():
                simulator = BehaviorSimulator()
                
                # 测试初始化
                assert hasattr(simulator, 'config'), "应有config属性"
                assert hasattr(simulator, 'stats'), "应有stats属性"
                
                # 测试随机延迟
                from datetime import datetime
                start_time = datetime.now()
                delay = await simulator.random_delay(0.1, 0.2)
                end_time = datetime.now()
                
                actual_delay = (end_time - start_time).total_seconds()
                assert 0.1 <= delay <= 0.2, f"延迟应在0.1-0.2秒之间，实际: {delay}"
                assert actual_delay >= 0.1, f"实际延迟应>=0.1秒，实际: {actual_delay}"
                
                # 测试阅读模拟
                start_time = datetime.now()
                reading_time = await simulator.simulate_reading(1000)
                end_time = datetime.now()
                
                actual_time = (end_time - start_time).total_seconds()
                assert reading_time > 0, "阅读时间应大于0"
                assert actual_time >= reading_time * 0.8, "实际时间应接近模拟时间"
                
                # 测试访问模式
                pattern = simulator.get_visit_pattern()
                assert isinstance(pattern, str), "访问模式应为字符串"
                assert '->' in pattern, "访问模式应包含箭头"
                
                return True
            
            # 运行异步测试
            result = asyncio.run(test_behavior_simulator())
            if result:
                print("   ✅ BehaviorSimulator测试通过")
                test_results.append(("BehaviorSimulator", True, None))
            
        except Exception as e:
            print(f"   ❌ BehaviorSimulator测试失败: {e}")
            test_results.append(("BehaviorSimulator", False, str(e)))
        
        # 测试4: SessionManager
        print("\n🍪 测试SessionManager...")
        try:
            session_manager = SessionManager()
            
            # 测试初始化
            assert hasattr(session_manager, 'sessions'), "应有sessions属性"
            assert hasattr(session_manager, 'config'), "应有config属性"
            
            # 测试创建会话
            session = session_manager.create_session()
            assert session.session_id is not None, "会话ID不能为空"
            assert len(session.session_id) > 0, "会话ID长度应大于0"
            assert session.session_id in session_manager.sessions, "会话应被存储"
            
            # 测试创建ML会话
            ml_session = session_manager.create_session(platform="mercadolibre")
            assert len(ml_session.cookies) > 0, "ML会话应有Cookie"
            
            cookie_names = [cookie.name for cookie in ml_session.cookies]
            assert "_device_id" in cookie_names, "应包含设备ID Cookie"
            assert "currency" in cookie_names, "应包含货币Cookie"
            
            # 测试添加Cookie
            cookie_data = {
                'name': 'test_cookie',
                'value': 'test_value',
                'domain': '.example.com'
            }
            
            result = session_manager.add_cookie(session.session_id, cookie_data)
            assert result == True, "添加Cookie应成功"
            
            cookies = session_manager.get_cookies_for_domain(session.session_id, 'example.com')
            assert len(cookies) > 0, "应能获取到Cookie"
            assert cookies[0]['name'] == 'test_cookie', "Cookie名称应匹配"
            
            print("   ✅ SessionManager测试通过")
            test_results.append(("SessionManager", True, None))
            
        except Exception as e:
            print(f"   ❌ SessionManager测试失败: {e}")
            test_results.append(("SessionManager", False, str(e)))
        
        # 测试5: FingerprintFaker
        print("\n🔍 测试FingerprintFaker...")
        try:
            faker = FingerprintFaker()
            
            # 测试初始化
            assert hasattr(faker, 'config'), "应有config属性"
            assert hasattr(faker, 'current_fingerprint'), "应有current_fingerprint属性"
            
            # 测试生成指纹
            profile = faker.generate_fingerprint_profile()
            assert isinstance(profile, dict), "指纹配置应为字典"
            assert 'screen' in profile, "应包含屏幕信息"
            assert 'locale' in profile, "应包含地区信息"
            
            # 测试阿根廷指纹
            ar_profile = faker.generate_fingerprint_profile(platform="argentina")
            assert ar_profile['locale']['language'] == 'es-AR', "阿根廷指纹语言应为es-AR"
            assert ar_profile['locale']['currency'] == 'ARS', "阿根廷指纹货币应为ARS"
            
            # 测试指纹验证
            is_valid = faker.validate_fingerprint(ar_profile)
            assert is_valid == True, "生成的指纹应通过验证"
            
            print("   ✅ FingerprintFaker测试通过")
            test_results.append(("FingerprintFaker", True, None))
            
        except Exception as e:
            print(f"   ❌ FingerprintFaker测试失败: {e}")
            test_results.append(("FingerprintFaker", False, str(e)))
        
        # 测试6: MercadoLibreAntiDetection
        print("\n🛡️ 测试MercadoLibreAntiDetection...")
        try:
            ml_anti_detection = MercadoLibreAntiDetection()
            
            # 测试初始化
            assert hasattr(ml_anti_detection, 'config'), "应有config属性"
            
            # 测试获取头部
            headers = ml_anti_detection.get_headers(country_code="AR")
            assert isinstance(headers, dict), "头部应为字典"
            assert 'X-Client-ID' in headers, "应包含客户端ID"
            assert 'X-Country' in headers, "应包含国家代码"
            assert headers['X-Country'] == 'AR', "国家代码应为AR"
            
            # 测试生成New Relic ID
            newrelic_id = ml_anti_detection._generate_newrelic_id()
            assert isinstance(newrelic_id, str), "New Relic ID应为字符串"
            assert len(newrelic_id) > 0, "New Relic ID不能为空"
            
            print("   ✅ MercadoLibreAntiDetection测试通过")
            test_results.append(("MercadoLibreAntiDetection", True, None))
            
        except Exception as e:
            print(f"   ❌ MercadoLibreAntiDetection测试失败: {e}")
            test_results.append(("MercadoLibreAntiDetection", False, str(e)))
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("📊 单元测试结果汇总")
        print("=" * 60)
        
        passed_count = 0
        failed_count = 0
        
        for test_name, passed, error in test_results:
            if passed:
                print(f"✅ {test_name}: 通过")
                passed_count += 1
            else:
                print(f"❌ {test_name}: 失败 - {error}")
                failed_count += 1
        
        print(f"\n总计: {len(test_results)} 个测试")
        print(f"通过: {passed_count} 个")
        print(f"失败: {failed_count} 个")
        print(f"成功率: {passed_count/len(test_results)*100:.1f}%")
        
        if failed_count == 0:
            print("\n🎉 所有单元测试通过！")
            return True
        else:
            print(f"\n⚠️ 有 {failed_count} 个测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        return False

if __name__ == "__main__":
    success = run_unit_tests()
    sys.exit(0 if success else 1) 