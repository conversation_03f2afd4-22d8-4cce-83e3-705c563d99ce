"""
任务创建API路由

提供从URL池创建监控任务的API接口
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
import redis.asyncio as redis

from app.models.task_create import (
    TaskCreateFromUrlsRequest, TaskCreateResponse,
    UrlPreviewRequest, UrlPreviewResponse,
    TaskValidationRequest, TaskValidationResponse
)
from app.services.task_create_service import TaskCreateService
from app.services.url_pool_service import UrlPoolService

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/tasks", tags=["task-create"])

# Redis连接
_redis_client: Optional[redis.Redis] = None


async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global _redis_client
    if _redis_client is None:
        _redis_client = redis.from_url("redis://redis:6379/0")
    return _redis_client


async def get_task_create_service() -> TaskCreateService:
    """获取任务创建服务"""
    redis_client = await get_redis_client()
    url_pool_service = UrlPoolService(redis_client)
    return TaskCreateService(redis_client, url_pool_service)


@router.post("/preview-urls", response_model=UrlPreviewResponse)
async def preview_selected_urls(
    request: UrlPreviewRequest,
    task_service: TaskCreateService = Depends(get_task_create_service)
):
    """
    预览选中的URL信息
    
    用于在创建任务前预览选中的URL，包括平台分布、有效性等信息
    """
    try:
        logger.info(f"预览URL请求: {len(request.url_ids)}个URL")
        
        response = await task_service.preview_urls(request)
        
        logger.info(f"URL预览完成: 有效{response.valid_count}个，无效{response.invalid_count}个")
        return response
        
    except Exception as e:
        logger.error(f"预览URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"预览失败: {str(e)}")


@router.post("/validate", response_model=TaskValidationResponse)
async def validate_task_creation(
    request: TaskValidationRequest,
    task_service: TaskCreateService = Depends(get_task_create_service)
):
    """
    验证任务创建请求
    
    在实际创建任务前验证配置的有效性，返回错误、警告和建议
    """
    try:
        logger.info(f"验证任务创建: {request.name}")
        
        response = await task_service.validate_task_creation(request)
        
        if response.success:
            logger.info(f"任务验证通过: {request.name}")
        else:
            logger.warning(f"任务验证失败: {request.name}, 错误: {response.errors}")
        
        return response
        
    except Exception as e:
        logger.error(f"验证任务创建失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")


@router.post("/create-from-urls", response_model=TaskCreateResponse)
async def create_task_from_urls(
    request: TaskCreateFromUrlsRequest,
    task_service: TaskCreateService = Depends(get_task_create_service)
):
    """
    从URL池创建监控任务
    
    根据选中的URL和配置创建新的监控任务，支持定时调度
    """
    try:
        logger.info(f"创建任务请求: {request.name}, URL数量: {len(request.url_ids)}")

        # 验证请求数据
        if not request.name.strip():
            raise HTTPException(status_code=400, detail="任务名称不能为空")
        
        if not request.url_ids:
            raise HTTPException(status_code=400, detail="必须选择至少一个URL")
        
        if len(request.url_ids) > 1000:
            raise HTTPException(status_code=400, detail="单个任务最多支持1000个URL")
        
        # 创建任务
        response = await task_service.create_task_from_urls(request)
        
        logger.info(f"任务创建成功: {response.task_id}")
        return response
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"任务创建参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.get("/templates")
async def get_task_templates():
    """
    获取任务模板列表
    
    返回预定义的任务模板，用于快速创建常用类型的任务
    """
    try:
        # 返回一些预定义的模板
        templates = [
            {
                "id": "daily_price_monitor",
                "name": "每日价格监控",
                "description": "每日监控商品价格变化",
                "platform": "all",
                "default_schedule": {
                    "type": "daily",
                    "enabled": True,
                    "start_time": None,
                    "interval": None,
                    "cron_expression": None,
                    "timezone": "Asia/Shanghai",
                    "enable_random_delay": False,
                    "random_delay_min": 0,
                    "random_delay_max": 180
                },
                "default_config": {
                    "platform": "mercadolibre",
                    "priority": "normal",
                    "retry_count": 3,
                    "timeout": 300,
                    "concurrent_limit": 5,
                    "batch_size": 10,
                    "enable_notifications": True
                },
                "usage_count": 0
            },
            {
                "id": "hourly_stock_check",
                "name": "每小时库存检查",
                "description": "每小时检查商品库存状态",
                "platform": "all",
                "default_schedule": {
                    "type": "hourly",
                    "enabled": True,
                    "start_time": None,
                    "interval": 60,
                    "cron_expression": None,
                    "timezone": "Asia/Shanghai"
                },
                "default_config": {
                    "platform": "amazon",
                    "priority": "high",
                    "retry_count": 2,
                    "timeout": 180,
                    "concurrent_limit": 10,
                    "batch_size": 20,
                    "enable_notifications": True
                },
                "usage_count": 0
            },
            {
                "id": "weekly_report",
                "name": "周报监控",
                "description": "每周生成监控报告",
                "platform": "all",
                "default_schedule": {
                    "type": "weekly",
                    "enabled": True,
                    "start_time": None,
                    "interval": None,
                    "cron_expression": None,
                    "timezone": "Asia/Shanghai"
                },
                "default_config": {
                    "platform": "all",
                    "priority": "low",
                    "retry_count": 1,
                    "timeout": 600,
                    "concurrent_limit": 3,
                    "batch_size": 50,
                    "enable_notifications": True
                },
                "usage_count": 0
            }
        ]
        
        return {
            "success": True,
            "templates": templates,
            "total": len(templates)
        }
        
    except Exception as e:
        logger.error(f"获取任务模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")


@router.get("/stats")
async def get_task_creation_stats(
    task_service: TaskCreateService = Depends(get_task_create_service)
):
    """
    获取任务创建统计信息
    
    返回任务创建的统计数据，用于仪表板展示
    """
    try:
        redis_client = await get_redis_client()
        
        # 获取基本统计（使用新的分层结构）
        total_tasks = await redis_client.scard("monitoring_tasks:indexes:all_task_ids")
        active_tasks = await redis_client.scard("monitoring_tasks:indexes:active_tasks")
        scheduled_tasks = await redis_client.scard("monitoring_tasks:indexes:scheduled_tasks")
        
        # 获取最近创建的任务（使用新的分层结构）
        recent_task_ids = await redis_client.smembers("monitoring_tasks:indexes:all_task_ids")
        recent_tasks = []
        
        for task_id_bytes in list(recent_task_ids)[:5]:  # 只取前5个
            task_id = task_id_bytes.decode()
            task_data = await task_service.get_task_by_id(task_id)
            if task_data:
                recent_tasks.append({
                    "id": task_data["id"],
                    "name": task_data["name"],
                    "url_count": task_data["url_count"],
                    "status": task_data["status"],
                    "created_at": task_data["created_at"]
                })
        
        # 平台分布（模拟数据）
        platform_distribution = {
            "mercadolibre": int(total_tasks * 0.4),
            "amazon": int(total_tasks * 0.3),
            "ebay": int(total_tasks * 0.2),
            "others": int(total_tasks * 0.1)
        }
        
        return {
            "success": True,
            "data": {
                "total_tasks": total_tasks,
                "active_tasks": active_tasks,
                "scheduled_tasks": scheduled_tasks,
                "platform_distribution": platform_distribution,
                "recent_tasks": recent_tasks
            }
        }
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/{task_id}")
async def get_task_details(
    task_id: str,
    task_service: TaskCreateService = Depends(get_task_create_service)
):
    """
    获取任务详细信息
    
    根据任务ID获取完整的任务信息，包括关联的URL列表
    """
    try:
        logger.info(f"获取任务详情: {task_id}")
        
        # 获取任务基本信息
        task_data = await task_service.get_task_by_id(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 获取关联的URL
        redis_client = await get_redis_client()
        url_pool_service = UrlPoolService(redis_client)
        
        task_urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
        url_ids = await redis_client.smembers(task_urls_key)
        
        urls = []
        for url_id_bytes in url_ids:
            url_id = url_id_bytes.decode()
            url_item = await url_pool_service.get_url_by_id(url_id)
            if url_item:
                urls.append({
                    "id": url_item.id,
                    "url": url_item.url,
                    "platform": url_item.platform,
                    "status": url_item.status,
                    "source_file": url_item.source_file,
                    "check_count": url_item.check_count,
                    "success_count": url_item.success_count
                })
        
        # 组合返回数据
        task_data["urls"] = urls
        task_data["url_count"] = len(urls)
        
        return {
            "success": True,
            "data": task_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取详情失败: {str(e)}")


@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    task_service: TaskCreateService = Depends(get_task_create_service)
):
    """
    删除任务
    
    删除指定的监控任务及其所有关联数据
    """
    try:
        logger.info(f"删除任务: {task_id}")
        
        # 检查任务是否存在
        task_data = await task_service.get_task_by_id(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        redis_client = await get_redis_client()
        
        # 删除任务数据
        pipe = redis_client.pipeline()
        
        # 使用TaskStorageService删除任务（包含完整的清理逻辑）
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        success = await task_storage.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=500, detail="删除任务失败")

        
        logger.info(f"任务删除成功: {task_id}")
        
        return {
            "success": True,
            "message": f"任务 '{task_data['name']}' 删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")
