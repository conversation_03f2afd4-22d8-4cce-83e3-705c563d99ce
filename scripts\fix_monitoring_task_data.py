#!/usr/bin/env python3
"""
监控任务数据修复脚本

修复内容：
1. 重建任务-URL关联关系
2. 清理孤立的关联数据
3. 确保数据一致性
4. 验证修复结果
"""

import redis
import json
import sys
from datetime import datetime
from typing import List, Dict, Set


class MonitoringTaskDataFixer:
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """初始化Redis连接"""
        self.redis = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
        
    def analyze_data_issues(self) -> Dict:
        """分析数据一致性问题"""
        print("🔍 分析数据一致性问题...")
        
        issues = {
            'tasks_with_missing_urls': [],
            'orphaned_url_associations': [],
            'inconsistent_url_counts': [],
            'total_tasks': 0,
            'total_urls': 0
        }
        
        # 获取所有任务
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        issues['total_tasks'] = len(task_ids)
        
        # 获取URL池总数
        issues['total_urls'] = self.redis.scard('url_pool:all_ids')
        
        print(f"   总任务数: {issues['total_tasks']}")
        print(f"   URL池总数: {issues['total_urls']}")
        
        # 检查每个任务的URL关联
        for task_id in task_ids:
            task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
            if not task_data:
                continue
                
            task_name = task_data.get('name', '未知')
            declared_url_count = int(task_data.get('url_count', 0))
            actual_url_count = self.redis.scard(f'monitoring_tasks:task_urls:{task_id}')
            
            if declared_url_count != actual_url_count:
                issues['inconsistent_url_counts'].append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'declared': declared_url_count,
                    'actual': actual_url_count
                })
                
                if actual_url_count == 0 and declared_url_count > 0:
                    issues['tasks_with_missing_urls'].append({
                        'task_id': task_id,
                        'task_name': task_name,
                        'expected_urls': declared_url_count
                    })
        
        # 检查孤立的URL关联
        url_task_keys = self.redis.keys('monitoring_tasks:url_tasks:*')
        for key in url_task_keys:
            url_id = key.split(':')[-1]
            task_ids_for_url = self.redis.smembers(key)
            
            for task_id in task_ids_for_url:
                if not self.redis.exists(f'monitoring_tasks:{task_id}'):
                    issues['orphaned_url_associations'].append({
                        'url_id': url_id,
                        'orphaned_task_id': task_id
                    })
        
        return issues
    
    def rebuild_task_url_associations(self, task_id: str, expected_url_count: int) -> int:
        """重建任务的URL关联关系"""
        print(f"   🔧 重建任务 {task_id} 的URL关联...")
        
        # 获取URL池中的URL，按添加时间排序
        url_pool_ids = list(self.redis.smembers('url_pool:all_ids'))
        
        # 获取URL的详细信息并排序
        url_candidates = []
        for url_id in url_pool_ids:
            url_data = self.redis.hgetall(f'url_pool:items:{url_id}')
            if url_data and url_data.get('status') == 'active':
                # 检查这个URL是否已经被其他任务使用
                existing_tasks = self.redis.smembers(f'monitoring_tasks:url_tasks:{url_id}')
                if not existing_tasks:  # 只选择未被使用的URL
                    url_candidates.append({
                        'id': url_id,
                        'added_at': url_data.get('added_at', ''),
                        'platform': url_data.get('platform', ''),
                        'url': url_data.get('url', '')
                    })
        
        # 按添加时间排序，选择最新的URL
        url_candidates.sort(key=lambda x: x['added_at'], reverse=True)
        
        # 选择指定数量的URL
        selected_urls = url_candidates[:expected_url_count]
        
        if len(selected_urls) < expected_url_count:
            print(f"      ⚠️  只找到 {len(selected_urls)} 个可用URL，少于期望的 {expected_url_count} 个")
        
        # 建立关联关系
        task_urls_key = f'monitoring_tasks:task_urls:{task_id}'
        
        for url_info in selected_urls:
            url_id = url_info['id']
            
            # 任务 -> URL 关联
            self.redis.sadd(task_urls_key, url_id)
            
            # URL -> 任务 反向关联
            url_tasks_key = f'monitoring_tasks:url_tasks:{url_id}'
            self.redis.sadd(url_tasks_key, task_id)
        
        print(f"      ✅ 成功关联 {len(selected_urls)} 个URL")
        return len(selected_urls)
    
    def clean_orphaned_associations(self, orphaned_associations: List[Dict]) -> int:
        """清理孤立的关联数据"""
        print(f"🧹 清理 {len(orphaned_associations)} 个孤立的关联...")
        
        cleaned_count = 0
        for assoc in orphaned_associations:
            url_id = assoc['url_id']
            orphaned_task_id = assoc['orphaned_task_id']
            
            # 从URL的任务关联中移除孤立的任务ID
            url_tasks_key = f'monitoring_tasks:url_tasks:{url_id}'
            removed = self.redis.srem(url_tasks_key, orphaned_task_id)
            
            if removed:
                cleaned_count += 1
                
            # 如果URL没有关联任何任务了，删除整个关联键
            if self.redis.scard(url_tasks_key) == 0:
                self.redis.delete(url_tasks_key)
        
        print(f"   ✅ 清理了 {cleaned_count} 个孤立关联")
        return cleaned_count
    
    def update_task_url_counts(self, task_ids: List[str]) -> int:
        """更新任务的URL计数"""
        print(f"📊 更新 {len(task_ids)} 个任务的URL计数...")
        
        updated_count = 0
        for task_id in task_ids:
            actual_url_count = self.redis.scard(f'monitoring_tasks:task_urls:{task_id}')
            
            # 更新任务的url_count字段
            self.redis.hset(f'monitoring_tasks:{task_id}', 'url_count', actual_url_count)
            updated_count += 1
        
        print(f"   ✅ 更新了 {updated_count} 个任务的URL计数")
        return updated_count
    
    def verify_fix_results(self) -> Dict:
        """验证修复结果"""
        print("✅ 验证修复结果...")
        
        results = {
            'total_tasks': 0,
            'consistent_tasks': 0,
            'total_associations': 0,
            'orphaned_associations': 0
        }
        
        # 检查任务一致性
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        results['total_tasks'] = len(task_ids)
        
        for task_id in task_ids:
            task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
            declared_count = int(task_data.get('url_count', 0))
            actual_count = self.redis.scard(f'monitoring_tasks:task_urls:{task_id}')
            
            if declared_count == actual_count:
                results['consistent_tasks'] += 1
        
        # 检查关联总数
        url_task_keys = self.redis.keys('monitoring_tasks:url_tasks:*')
        results['total_associations'] = len(url_task_keys)
        
        # 检查孤立关联
        for key in url_task_keys:
            task_ids_for_url = self.redis.smembers(key)
            for task_id in task_ids_for_url:
                if not self.redis.exists(f'monitoring_tasks:{task_id}'):
                    results['orphaned_associations'] += 1
        
        return results
    
    def run_fix(self) -> Dict:
        """执行完整的数据修复流程"""
        print("🚀 开始监控任务数据修复...")
        print("=" * 50)
        
        # 1. 分析问题
        issues = self.analyze_data_issues()
        
        print(f"\n📋 发现的问题:")
        print(f"   - URL计数不一致的任务: {len(issues['inconsistent_url_counts'])}")
        print(f"   - 缺少URL关联的任务: {len(issues['tasks_with_missing_urls'])}")
        print(f"   - 孤立的URL关联: {len(issues['orphaned_url_associations'])}")
        
        if not any([
            issues['inconsistent_url_counts'],
            issues['tasks_with_missing_urls'], 
            issues['orphaned_url_associations']
        ]):
            print("\n✅ 数据一致性良好，无需修复！")
            return issues
        
        print(f"\n🔧 开始修复...")
        
        # 2. 重建缺失的URL关联
        for task_info in issues['tasks_with_missing_urls']:
            self.rebuild_task_url_associations(
                task_info['task_id'], 
                task_info['expected_urls']
            )
        
        # 3. 清理孤立的关联
        if issues['orphaned_url_associations']:
            self.clean_orphaned_associations(issues['orphaned_url_associations'])
        
        # 4. 更新URL计数
        all_task_ids = list(self.redis.smembers('monitoring_tasks:all_ids'))
        self.update_task_url_counts(all_task_ids)
        
        # 5. 验证结果
        print(f"\n" + "=" * 50)
        results = self.verify_fix_results()
        
        print(f"📊 修复结果:")
        print(f"   - 总任务数: {results['total_tasks']}")
        print(f"   - 数据一致的任务: {results['consistent_tasks']}")
        print(f"   - 一致性比例: {results['consistent_tasks']/results['total_tasks']*100:.1f}%")
        print(f"   - 总URL关联数: {results['total_associations']}")
        print(f"   - 剩余孤立关联: {results['orphaned_associations']}")
        
        if results['consistent_tasks'] == results['total_tasks'] and results['orphaned_associations'] == 0:
            print(f"\n🎉 数据修复完成！所有任务数据现在都是一致的。")
        else:
            print(f"\n⚠️  修复部分完成，可能需要进一步检查。")
        
        return results


if __name__ == "__main__":
    fixer = MonitoringTaskDataFixer()
    results = fixer.run_fix()
