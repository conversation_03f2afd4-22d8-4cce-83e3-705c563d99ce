/**
 * 爬取配置API服务
 * 提供爬取配置的CRUD操作和相关功能
 */

import {
  CrawlerConfigData,
  CrawlerConfigCreate,
  CrawlerConfigUpdate,
  CrawlerConfigSummary,
  CrawlerConfigValidationResult,
  ApiError
} from '../types/newArchitecture';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const API_BASE = `${API_BASE_URL}/api/v1/crawler-configs`;

class CrawlerConfigApiService {
  /**
   * 获取爬取配置列表
   */
  async getConfigs(params?: {
    status?: string;
    tags?: string[];
    search?: string;
    page?: number;
    size?: number;
  }): Promise<CrawlerConfigSummary[]> {
    const searchParams = new URLSearchParams();
    
    if (params?.status) searchParams.append('status', params.status);
    if (params?.search) searchParams.append('search', params.search);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.size) searchParams.append('size', params.size.toString());
    if (params?.tags) {
      params.tags.forEach(tag => searchParams.append('tags', tag));
    }

    const response = await fetch(`${API_BASE}/?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取配置列表失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取单个爬取配置详情
   */
  async getConfig(configId: string): Promise<CrawlerConfigData> {
    const response = await fetch(`${API_BASE}/${configId}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('配置不存在');
      }
      throw new Error(`获取配置详情失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 创建新的爬取配置
   */
  async createConfig(configData: CrawlerConfigCreate): Promise<CrawlerConfigData> {
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(configData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '创建配置失败');
    }

    return response.json();
  }

  /**
   * 更新爬取配置
   */
  async updateConfig(configId: string, updateData: CrawlerConfigUpdate): Promise<CrawlerConfigData> {
    const response = await fetch(`${API_BASE}/${configId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '更新配置失败');
    }

    return response.json();
  }

  /**
   * 删除爬取配置
   */
  async deleteConfig(configId: string): Promise<void> {
    const response = await fetch(`${API_BASE}/${configId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '删除配置失败');
    }
  }

  /**
   * 验证爬取配置
   */
  async validateConfig(configId: string): Promise<CrawlerConfigValidationResult> {
    const response = await fetch(`${API_BASE}/${configId}/validate`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '验证配置失败');
    }

    return response.json();
  }

  /**
   * 复制爬取配置
   */
  async duplicateConfig(configId: string, newName: string): Promise<CrawlerConfigData> {
    const response = await fetch(`${API_BASE}/${configId}/duplicate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ new_name: newName }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '复制配置失败');
    }

    return response.json();
  }

  /**
   * 获取配置模板
   */
  async getTemplates(): Promise<CrawlerConfigData[]> {
    const response = await fetch(`${API_BASE}/templates`);
    if (!response.ok) {
      throw new Error(`获取配置模板失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 从模板创建配置
   */
  async createFromTemplate(templateId: string, configName: string): Promise<CrawlerConfigData> {
    const response = await fetch(`${API_BASE}/templates/${templateId}/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ config_name: configName }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '从模板创建配置失败');
    }

    return response.json();
  }

  /**
   * 获取配置使用统计
   */
  async getConfigStats(configId: string): Promise<{
    usage_count: number;
    last_used: string;
    success_rate: number;
    avg_execution_time: number;
    recent_tasks: any[];
  }> {
    const response = await fetch(`${API_BASE}/${configId}/stats`);
    if (!response.ok) {
      throw new Error(`获取配置统计失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 导出配置
   */
  async exportConfig(configId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE}/${configId}/export`);
    if (!response.ok) {
      throw new Error(`导出配置失败: ${response.statusText}`);
    }
    
    return response.blob();
  }

  /**
   * 导入配置
   */
  async importConfig(file: File): Promise<CrawlerConfigData> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE}/import`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '导入配置失败');
    }

    return response.json();
  }

  /**
   * 批量操作
   */
  async batchOperation(operation: 'delete' | 'activate' | 'deactivate', configIds: string[]): Promise<{
    success: string[];
    failed: { id: string; error: string }[];
  }> {
    const response = await fetch(`${API_BASE}/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        operation,
        config_ids: configIds,
      }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '批量操作失败');
    }

    return response.json();
  }

  /**
   * 搜索配置
   */
  async searchConfigs(query: string, filters?: {
    status?: string[];
    tags?: string[];
    created_after?: string;
    created_before?: string;
  }): Promise<CrawlerConfigSummary[]> {
    const searchParams = new URLSearchParams();
    searchParams.append('q', query);
    
    if (filters?.status) {
      filters.status.forEach(status => searchParams.append('status', status));
    }
    if (filters?.tags) {
      filters.tags.forEach(tag => searchParams.append('tags', tag));
    }
    if (filters?.created_after) {
      searchParams.append('created_after', filters.created_after);
    }
    if (filters?.created_before) {
      searchParams.append('created_before', filters.created_before);
    }

    const response = await fetch(`${API_BASE}/search?${searchParams}`);
    if (!response.ok) {
      throw new Error(`搜索配置失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取所有可用标签
   */
  async getTags(): Promise<string[]> {
    const response = await fetch(`${API_BASE}/tags`);
    if (!response.ok) {
      throw new Error(`获取标签失败: ${response.statusText}`);
    }
    
    return response.json();
  }
}

// 导出单例实例
export const crawlerConfigApi = new CrawlerConfigApiService();
export default crawlerConfigApi;
