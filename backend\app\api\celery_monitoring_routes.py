"""
Celery监控API路由
提供Celery任务、Worker、队列的实时监控功能
"""

import logging
import asyncio
import json
import subprocess
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, WebSocket, WebSocketDisconnect
from pydantic import BaseModel

from app.celery_app import celery_app
from app.core.redis_client import get_redis_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/celery", tags=["Celery监控"])

# 缓存配置
CACHE_TTL = 10  # 缓存10秒
INSPECT_TIMEOUT = 2  # Celery inspect超时2秒
FALLBACK_CACHE_TTL = 60  # 降级缓存60秒


async def safe_inspect_with_timeout(inspect_func, timeout=INSPECT_TIMEOUT):
    """带超时的安全inspect调用"""
    try:
        # 使用asyncio.wait_for添加超时
        result = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(None, inspect_func),
            timeout=timeout
        )
        return result
    except asyncio.TimeoutError:
        logger.warning(f"Celery inspect operation timed out after {timeout}s")
        return None
    except Exception as e:
        logger.error(f"Celery inspect operation failed: {e}")
        return None


async def get_cached_data(redis_client, cache_key: str):
    """获取缓存数据"""
    try:
        cached = await redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
    except Exception as e:
        logger.warning(f"Failed to get cached data: {e}")
    return None


async def set_cached_data(redis_client, cache_key: str, data: Any, ttl: int = CACHE_TTL):
    """设置缓存数据"""
    try:
        await redis_client.setex(cache_key, ttl, json.dumps(data, default=str))
        # 同时设置降级缓存，TTL更长
        fallback_key = f"{cache_key}:fallback"
        await redis_client.setex(fallback_key, FALLBACK_CACHE_TTL, json.dumps(data, default=str))
    except Exception as e:
        logger.warning(f"Failed to set cached data: {e}")


async def get_fallback_data(redis_client, cache_key: str):
    """获取降级缓存数据"""
    try:
        fallback_key = f"{cache_key}:fallback"
        cached = await redis_client.get(fallback_key)
        if cached:
            return json.loads(cached)
    except Exception as e:
        logger.warning(f"Failed to get fallback data: {e}")
    return None


# 响应模型
class CeleryTaskInfo(BaseModel):
    id: str
    name: str
    state: str
    args: List[Any]
    kwargs: Dict[str, Any]
    result: Optional[Any] = None
    traceback: Optional[str] = None
    timestamp: str
    runtime: Optional[float] = None
    worker: Optional[str] = None
    queue: Optional[str] = None
    retries: Optional[int] = None
    eta: Optional[str] = None


class CeleryWorkerInfo(BaseModel):
    name: str
    status: str
    active_tasks: int
    processed_tasks: int
    load_avg: List[float]
    pool_size: int
    pool_writes: int
    clock: int


class QueueInfo(BaseModel):
    name: str
    length: int
    consumers: int
    messages: int
    memory: int


class CeleryStats(BaseModel):
    total_tasks: int
    active_tasks: int
    pending_tasks: int
    success_tasks: int
    failed_tasks: int
    workers_online: int
    total_queues: int


@router.get("/stats", response_model=CeleryStats)
async def get_celery_stats():
    """获取Celery统计信息"""
    try:
        redis_client = await get_redis_client()
        cache_key = "celery:cache:stats"

        # 尝试从缓存获取
        cached_stats = await get_cached_data(redis_client, cache_key)
        if cached_stats:
            return CeleryStats(**cached_stats)

        inspect = celery_app.control.inspect()

        # 使用超时机制获取基本统计
        stats = await safe_inspect_with_timeout(inspect.stats)
        active_tasks = await safe_inspect_with_timeout(inspect.active)
        scheduled_tasks = await safe_inspect_with_timeout(inspect.scheduled)

        # 如果inspect失败，尝试使用降级缓存
        if stats is None and active_tasks is None and scheduled_tasks is None:
            fallback_stats = await get_fallback_data(redis_client, cache_key)
            if fallback_stats:
                logger.info("Using fallback cache for Celery stats")
                return CeleryStats(**fallback_stats)

        # 计算统计数据
        workers_online = len(stats or {})
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_scheduled = sum(len(tasks) for tasks in (scheduled_tasks or {}).values())

        # 从统一执行服务获取额外的活跃任务
        try:
            from ..services.unified_execution_service import unified_execution_service

            # 获取正在运行和等待中的URL执行记录
            running_executions = await redis_client.smembers("url_executions:by_status:running") or set()
            pending_executions = await redis_client.smembers("url_executions:by_status:pending") or set()

            # 添加到活跃任务计数
            unified_active = len(running_executions)
            unified_pending = len(pending_executions)

            total_active += unified_active
            total_scheduled += unified_pending

            logger.debug(f"Added {unified_active} running and {unified_pending} pending tasks from unified execution service")

        except Exception as e:
            logger.warning(f"Failed to get unified execution tasks: {e}")

        # 从Redis获取历史统计
        total_tasks = await redis_client.get("celery:stats:total_tasks") or 0
        success_tasks = await redis_client.get("celery:stats:success_tasks") or 0
        failed_tasks = await redis_client.get("celery:stats:failed_tasks") or 0

        result = {
            "total_tasks": int(total_tasks),
            "active_tasks": total_active,
            "pending_tasks": total_scheduled,
            "success_tasks": int(success_tasks),
            "failed_tasks": int(failed_tasks),
            "workers_online": workers_online,
            "total_queues": 1  # 默认队列数量
        }

        # 缓存结果
        await set_cached_data(redis_client, cache_key, result)

        return CeleryStats(**result)

    except Exception as e:
        logger.error(f"Failed to get Celery stats: {e}")
        # 返回默认值而不是抛出异常
        return CeleryStats(
            total_tasks=0,
            active_tasks=0,
            pending_tasks=0,
            success_tasks=0,
            failed_tasks=0,
            workers_online=0,
            total_queues=0
        )


@router.get("/stats/fast", response_model=CeleryStats)
async def get_celery_stats_fast():
    """获取Celery统计信息 - 快速版本，只返回基本信息"""
    try:
        redis_client = await get_redis_client()

        # 只从Redis获取基本统计，不调用inspect
        total_tasks = await redis_client.get("celery:stats:total_tasks") or 0
        success_tasks = await redis_client.get("celery:stats:success_tasks") or 0
        failed_tasks = await redis_client.get("celery:stats:failed_tasks") or 0

        # 检查队列长度作为pending_tasks的估算
        queue_length = await redis_client.llen("celery")

        return CeleryStats(
            total_tasks=int(total_tasks),
            active_tasks=0,  # 快速版本不检查活跃任务
            pending_tasks=queue_length,
            success_tasks=int(success_tasks),
            failed_tasks=int(failed_tasks),
            workers_online=1,  # 假设有一个worker在线
            total_queues=1
        )

    except Exception as e:
        logger.error(f"Failed to get fast Celery stats: {e}")
        return CeleryStats(
            total_tasks=0,
            active_tasks=0,
            pending_tasks=0,
            success_tasks=0,
            failed_tasks=0,
            workers_online=0,
            total_queues=0
        )


def _process_task_data(task: Dict, worker: str, state: str) -> Dict:
    """处理单个任务数据"""
    task_id = task.get('id', task.get('uuid', ''))
    task_name = task.get('name', task.get('type', ''))

    # 简化时间戳处理
    timestamp = task.get('time_start') or task.get('timestamp') or task.get('eta')
    if isinstance(timestamp, (int, float)):
        timestamp = datetime.fromtimestamp(timestamp).isoformat()
    elif not timestamp:
        timestamp = datetime.now().isoformat()

    # 简化队列信息处理
    delivery_info = task.get('delivery_info', {})
    queue = delivery_info.get('routing_key', 'default') if isinstance(delivery_info, dict) else 'default'

    return {
        "id": task_id,
        "name": task_name,
        "state": state,
        "args": task.get('args', []),
        "kwargs": task.get('kwargs', {}),
        "timestamp": timestamp,
        "worker": worker,
        "queue": queue,
        "eta": task.get('eta') if state == 'PENDING' else None
    }


@router.get("/tasks/active", response_model=List[CeleryTaskInfo])
async def get_active_tasks():
    """获取活跃任务列表"""
    try:
        redis_client = await get_redis_client()
        cache_key = "celery:cache:active_tasks"

        # 尝试从缓存获取
        cached_tasks = await get_cached_data(redis_client, cache_key)
        if cached_tasks:
            return [CeleryTaskInfo(**task) for task in cached_tasks]

        inspect = celery_app.control.inspect()

        # 使用超时机制获取任务
        active_tasks = await safe_inspect_with_timeout(inspect.active)
        scheduled_tasks = await safe_inspect_with_timeout(inspect.scheduled)

        tasks = []

        # 处理活跃任务
        if active_tasks:
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    task_data = _process_task_data(task, worker, 'STARTED')
                    tasks.append(task_data)

        # 处理计划任务
        if scheduled_tasks:
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    task_data = _process_task_data(task, worker, 'PENDING')
                    tasks.append(task_data)

        # 从统一执行服务获取额外的活跃任务
        try:
            # 获取正在运行的URL执行记录
            running_execution_ids = await redis_client.smembers("url_executions:by_status:running") or set()
            pending_execution_ids = await redis_client.smembers("url_executions:by_status:pending") or set()

            # 处理正在运行的执行记录
            for execution_id in running_execution_ids:
                # 处理bytes类型的execution_id
                if isinstance(execution_id, bytes):
                    execution_id = execution_id.decode('utf-8')

                execution_data = await redis_client.hgetall(f"url_executions:{execution_id}")

                # 处理bytes类型的值
                if execution_data:
                    execution_data = {
                        k.decode('utf-8') if isinstance(k, bytes) else k:
                        v.decode('utf-8') if isinstance(v, bytes) else v
                        for k, v in execution_data.items()
                    }

                if execution_data:
                    task_data = {
                        "id": execution_data.get("celery_task_id", execution_id),
                        "name": "crawl_batch_task",
                        "state": "STARTED",
                        "args": [execution_data.get("url", "")],
                        "kwargs": {
                            "platform": "mercadolibre",
                            "monitoring_task_id": execution_data.get("monitoring_task_id"),
                            "worker_id": execution_data.get("assigned_worker_id")
                        },
                        "timestamp": execution_data.get("started_at", datetime.now().isoformat()),
                        "worker": execution_data.get("assigned_worker_id", "unknown"),
                        "queue": "default"
                    }
                    tasks.append(task_data)

            # 处理等待中的执行记录
            for execution_id in pending_execution_ids:
                # 处理bytes类型的execution_id
                if isinstance(execution_id, bytes):
                    execution_id = execution_id.decode('utf-8')

                execution_data = await redis_client.hgetall(f"url_executions:{execution_id}")

                # 处理bytes类型的值
                if execution_data:
                    execution_data = {
                        k.decode('utf-8') if isinstance(k, bytes) else k:
                        v.decode('utf-8') if isinstance(v, bytes) else v
                        for k, v in execution_data.items()
                    }

                if execution_data:
                    task_data = {
                        "id": execution_data.get("celery_task_id", execution_id),
                        "name": "crawl_batch_task",
                        "state": "PENDING",
                        "args": [execution_data.get("url", "")],
                        "kwargs": {
                            "platform": "mercadolibre",
                            "monitoring_task_id": execution_data.get("monitoring_task_id"),
                            "worker_id": execution_data.get("assigned_worker_id")
                        },
                        "timestamp": execution_data.get("started_at", datetime.now().isoformat()),
                        "worker": execution_data.get("assigned_worker_id", "unknown"),
                        "queue": "default"
                    }
                    tasks.append(task_data)

            logger.debug(f"Added {len(running_execution_ids)} running and {len(pending_execution_ids)} pending tasks from unified execution service")

        except Exception as e:
            logger.warning(f"Failed to get unified execution tasks for active list: {e}")

        # 缓存结果
        await set_cached_data(redis_client, cache_key, tasks)

        return [CeleryTaskInfo(**task) for task in tasks]

    except Exception as e:
        logger.error(f"Failed to get active tasks: {e}")
        # 返回空列表而不是抛出异常
        return []


@router.get("/tasks/history", response_model=List[CeleryTaskInfo])
async def get_task_history(
    limit: int = Query(100, description="返回任务数量限制"),
    state: Optional[str] = Query(None, description="任务状态筛选"),
    worker: Optional[str] = Query(None, description="Worker筛选")
):
    """获取任务历史记录"""
    try:
        redis_client = await get_redis_client()
        
        # 从Redis获取任务历史
        task_keys = await redis_client.keys("celery:task:*")
        tasks = []
        
        for key in task_keys[-limit:]:  # 限制数量
            task_data = await redis_client.hgetall(key)
            if task_data:
                # 应用筛选条件
                if state and task_data.get('state') != state:
                    continue
                if worker and task_data.get('worker') != worker:
                    continue
                
                tasks.append(CeleryTaskInfo(
                    id=task_data.get('id', ''),
                    name=task_data.get('name', ''),
                    state=task_data.get('state', 'UNKNOWN'),
                    args=eval(task_data.get('args', '[]')),
                    kwargs=eval(task_data.get('kwargs', '{}')),
                    result=task_data.get('result'),
                    traceback=task_data.get('traceback'),
                    timestamp=task_data.get('timestamp', ''),
                    runtime=float(task_data.get('runtime', 0)) if task_data.get('runtime') else None,
                    worker=task_data.get('worker'),
                    queue=task_data.get('queue'),
                    retries=int(task_data.get('retries', 0)) if task_data.get('retries') else None
                ))
        
        # 按时间戳排序
        tasks.sort(key=lambda x: x.timestamp, reverse=True)
        
        return tasks
        
    except Exception as e:
        logger.error(f"Failed to get task history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workers", response_model=List[CeleryWorkerInfo])
async def get_workers():
    """获取Worker状态"""
    try:
        redis_client = await get_redis_client()
        cache_key = "celery:cache:workers"

        # 尝试从缓存获取
        cached_workers = await get_cached_data(redis_client, cache_key)
        if cached_workers:
            return [CeleryWorkerInfo(**worker) for worker in cached_workers]

        inspect = celery_app.control.inspect()

        # 使用超时机制获取Worker统计信息
        stats = await safe_inspect_with_timeout(inspect.stats)
        active_tasks = await safe_inspect_with_timeout(inspect.active)

        workers = []
        
        worker_data = []
        if stats:
            for worker_name, worker_stats in stats.items():
                # 简化数据处理
                worker_active_tasks = len((active_tasks or {}).get(worker_name, []))

                # 简化统计数据获取
                total_stats = worker_stats.get('total', {})
                processed_tasks = sum(v for k, v in total_stats.items()
                                    if isinstance(v, int) and 'crawl' in k)

                # 简化load_avg处理
                rusage_stats = worker_stats.get('rusage', {})
                load_avg = rusage_stats.get('load_avg', [0.0, 0.0, 0.0])
                if not isinstance(load_avg, list) or len(load_avg) < 3:
                    load_avg = [0.0, 0.0, 0.0]

                # 简化pool数据处理
                pool_stats = worker_stats.get('pool', {})
                pool_size = int(pool_stats.get('max-concurrency', 4))
                pool_writes = int(pool_stats.get('writes', {}).get('total', 0)
                                if isinstance(pool_stats.get('writes'), dict)
                                else pool_stats.get('writes', 0))

                # 从统一执行服务获取额外的活跃任务数
                unified_active_tasks = 0
                try:
                    # 获取该Worker的当前执行任务
                    worker_executions = await redis_client.hgetall(f"current_executions:by_worker:{worker_name}")
                    unified_active_tasks = len(worker_executions) if worker_executions else 0
                except Exception as e:
                    logger.debug(f"Failed to get unified tasks for worker {worker_name}: {e}")

                worker_info = {
                    "name": worker_name,
                    "status": "online",
                    "active_tasks": worker_active_tasks + unified_active_tasks,
                    "processed_tasks": processed_tasks,
                    "load_avg": load_avg[:3],
                    "pool_size": pool_size,
                    "pool_writes": pool_writes,
                    "clock": int(worker_stats.get('clock', 0))
                }
                worker_data.append(worker_info)

        # 缓存结果
        await set_cached_data(redis_client, cache_key, worker_data)

        return [CeleryWorkerInfo(**worker) for worker in worker_data]

    except Exception as e:
        logger.error(f"Failed to get workers: {e}")
        # 返回空列表而不是抛出异常
        return []


@router.get("/queues", response_model=List[QueueInfo])
async def get_queues():
    """获取队列信息"""
    try:
        redis_client = await get_redis_client()
        
        # 获取队列信息
        queues = []
        
        # 检查默认队列
        queue_length = await redis_client.llen("celery")
        
        queues.append(QueueInfo(
            name="celery",
            length=queue_length,
            consumers=1,  # 假设有一个消费者
            messages=queue_length,
            memory=queue_length * 1024  # 估算内存使用
        ))
        
        # 检查其他可能的队列
        for queue_name in ["crawl", "monitor", "high", "low"]:
            queue_length = await redis_client.llen(queue_name)
            if queue_length > 0:
                queues.append(QueueInfo(
                    name=queue_name,
                    length=queue_length,
                    consumers=1,
                    messages=queue_length,
                    memory=queue_length * 1024
                ))
        
        return queues
        
    except Exception as e:
        logger.error(f"Failed to get queues: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/revoke")
async def revoke_task(task_id: str):
    """撤销任务"""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        # 记录撤销操作
        redis_client = await get_redis_client()
        await redis_client.hset(
            f"celery:task:{task_id}",
            mapping={
                "state": "REVOKED",
                "revoked_at": datetime.now().isoformat(),
                "revoked_by": "admin"
            }
        )
        
        return {"success": True, "message": f"Task {task_id} revoked successfully"}
        
    except Exception as e:
        logger.error(f"Failed to revoke task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/detail", response_model=CeleryTaskInfo)
async def get_task_detail(task_id: str):
    """获取任务详细信息"""
    try:
        redis_client = await get_redis_client()
        
        # 从Redis获取任务详情
        task_data = await redis_client.hgetall(f"celery:task:{task_id}")
        
        if not task_data:
            # 尝试从活跃任务中查找
            inspect = celery_app.control.inspect()
            active_tasks = inspect.active()
            
            if active_tasks:
                for worker, worker_tasks in active_tasks.items():
                    for task in worker_tasks:
                        if task['id'] == task_id:
                            return CeleryTaskInfo(
                                id=task['id'],
                                name=task['name'],
                                state='STARTED',
                                args=task.get('args', []),
                                kwargs=task.get('kwargs', {}),
                                timestamp=task.get('time_start', datetime.now().isoformat()),
                                worker=worker,
                                queue=task.get('delivery_info', {}).get('routing_key', 'default')
                            )
            
            raise HTTPException(status_code=404, detail="Task not found")
        
        return CeleryTaskInfo(
            id=task_data.get('id', task_id),
            name=task_data.get('name', ''),
            state=task_data.get('state', 'UNKNOWN'),
            args=eval(task_data.get('args', '[]')),
            kwargs=eval(task_data.get('kwargs', '{}')),
            result=task_data.get('result'),
            traceback=task_data.get('traceback'),
            timestamp=task_data.get('timestamp', ''),
            runtime=float(task_data.get('runtime', 0)) if task_data.get('runtime') else None,
            worker=task_data.get('worker'),
            queue=task_data.get('queue'),
            retries=int(task_data.get('retries', 0)) if task_data.get('retries') else None
        )
        
    except Exception as e:
        logger.error(f"Failed to get task detail {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def celery_health_check():
    """Celery健康检查"""
    try:
        inspect = celery_app.control.inspect()
        stats = inspect.stats()

        if not stats:
            return {
                "status": "unhealthy",
                "message": "No workers available",
                "workers": 0,
                "timestamp": datetime.now().isoformat()
            }

        return {
            "status": "healthy",
            "message": "Celery is running normally",
            "workers": len(stats),
            "worker_names": list(stats.keys()),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": str(e),
            "workers": 0,
            "timestamp": datetime.now().isoformat()
        }


# Celery深度监控相关API
class WorkerDetailedStatus(BaseModel):
    name: str
    status: str
    active_tasks: int
    processed_tasks: int
    failed_tasks: int
    load_avg: List[float]
    memory_usage: str
    uptime: str
    last_heartbeat: str


class LiveTaskInfo(BaseModel):
    id: str
    name: str
    worker: str
    started_at: str
    estimated_duration: str
    progress: int
    args: List[Any]
    # 队列信息
    queue: Optional[str] = None
    # 新增调试信息字段
    batch_id: Optional[str] = None
    total_urls: Optional[int] = None
    platform: Optional[str] = None
    task_id: Optional[str] = None  # 改为字符串类型，支持UUID
    elapsed_time: Optional[float] = None
    avg_url_time: Optional[float] = None
    current_status: Optional[str] = None
    db_progress: Optional[int] = None
    # 当前处理的URL
    current_url: Optional[str] = None


class BeatExecutionHistory(BaseModel):
    task_name: str
    scheduled_time: str
    actual_execution_time: str
    status: str
    duration: str
    result: str


class SystemDiagnostic(BaseModel):
    type: str
    category: str
    message: str
    suggestion: str


class DockerContainerStatus(BaseModel):
    name: str
    status: str
    state: str
    uptime: str
    restart_count: int


@router.get("/docker/containers", response_model=List[DockerContainerStatus])
async def get_docker_containers():
    """获取Celery相关Docker容器状态"""
    try:
        # 检查是否在Docker环境中
        import os
        if os.path.exists('/.dockerenv'):
            # 在Docker容器内部，无法直接访问Docker daemon
            # 返回模拟数据或从其他方式获取
            logger.warning("Running inside Docker container, cannot access Docker daemon directly")
            return [
                DockerContainerStatus(
                    name="monit-celery-worker",
                    status="Up (simulated)",
                    state="running",
                    uptime="unknown",
                    restart_count=0
                ),
                DockerContainerStatus(
                    name="monit-celery-beat",
                    status="Up (simulated)",
                    state="running",
                    uptime="unknown",
                    restart_count=0
                )
            ]

        # 尝试获取容器状态
        try:
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", "name=celery", "--format",
                 "{{.Names}}\t{{.Status}}\t{{.State}}\t{{.RunningFor}}\t{{.RestartCount}}"],
                capture_output=True, text=True, timeout=10
            )

            containers = []
            if result.returncode == 0 and result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) >= 4:
                            containers.append(DockerContainerStatus(
                                name=parts[0],
                                status=parts[1],
                                state=parts[2],
                                uptime=parts[3],
                                restart_count=int(parts[4]) if len(parts) > 4 and parts[4].isdigit() else 0
                            ))

            # 如果没有找到容器，返回默认状态
            if not containers:
                containers = [
                    DockerContainerStatus(
                        name="celery-worker",
                        status="Not found",
                        state="unknown",
                        uptime="unknown",
                        restart_count=0
                    ),
                    DockerContainerStatus(
                        name="celery-beat",
                        status="Not found",
                        state="unknown",
                        uptime="unknown",
                        restart_count=0
                    )
                ]

            return containers

        except subprocess.TimeoutExpired:
            logger.warning("Docker command timed out")
            raise HTTPException(status_code=408, detail="Docker command timed out")
        except FileNotFoundError:
            logger.warning("Docker command not found")
            # Docker不可用，返回模拟数据
            return [
                DockerContainerStatus(
                    name="celery-worker",
                    status="Docker unavailable",
                    state="unknown",
                    uptime="unknown",
                    restart_count=0
                ),
                DockerContainerStatus(
                    name="celery-beat",
                    status="Docker unavailable",
                    state="unknown",
                    uptime="unknown",
                    restart_count=0
                )
            ]

    except Exception as e:
        logger.error(f"Failed to get Docker containers: {e}")
        # 返回错误状态而不是抛出异常
        return [
            DockerContainerStatus(
                name="celery-worker",
                status=f"Error: {str(e)[:50]}",
                state="error",
                uptime="unknown",
                restart_count=0
            ),
            DockerContainerStatus(
                name="celery-beat",
                status=f"Error: {str(e)[:50]}",
                state="error",
                uptime="unknown",
                restart_count=0
            )
        ]


@router.get("/docker/logs/{container}")
async def get_docker_logs(
    container: str,
    lines: int = Query(100, description="日志行数"),
    since: str = Query("1h", description="时间范围"),
    filter_keywords: Optional[str] = Query(None, description="过滤关键词")
):
    """获取Docker容器日志"""
    try:
        # 验证容器名称，防止注入攻击
        allowed_containers = ["monit-celery-worker", "monit-celery-beat", "celery-worker", "celery-beat"]
        if container not in allowed_containers:
            raise HTTPException(status_code=400, detail="Invalid container name")

        # 构建docker logs命令
        cmd = ["docker", "logs", container, "--tail", str(lines), "--since", since, "--timestamps"]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode != 0:
            raise HTTPException(status_code=404, detail=f"Container {container} not found or not accessible")

        logs = result.stdout + result.stderr  # 合并stdout和stderr
        log_lines = logs.strip().split('\n') if logs.strip() else []

        # 解析日志条目
        parsed_logs = []
        for line in log_lines:
            if not line.strip():
                continue

            # 应用关键词过滤
            if filter_keywords and filter_keywords.lower() not in line.lower():
                continue

            # 解析时间戳和日志级别
            timestamp_match = re.match(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z)\s+(.*)$', line)
            if timestamp_match:
                timestamp = timestamp_match.group(1)
                message = timestamp_match.group(2)
            else:
                timestamp = datetime.now().isoformat()
                message = line

            # 检测日志级别
            level = "INFO"
            message_upper = message.upper()
            if "ERROR" in message_upper or "EXCEPTION" in message_upper:
                level = "ERROR"
            elif "WARNING" in message_upper or "WARN" in message_upper:
                level = "WARNING"
            elif "DEBUG" in message_upper:
                level = "DEBUG"

            parsed_logs.append(DockerLogEntry(
                timestamp=timestamp,
                level=level,
                message=message,
                container=container
            ))

        return {
            "container": container,
            "total_lines": len(parsed_logs),
            "logs": parsed_logs
        }

    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="Docker logs request timed out")
    except Exception as e:
        logger.error(f"Failed to get Docker logs for {container}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Redis信息API
@router.get("/redis/info")
async def get_redis_info():
    """获取Redis信息"""
    try:
        redis_client = await get_redis_client()

        # 获取Redis基本信息
        info = await redis_client.info()

        # 提取关键信息
        redis_info = {
            "connected": True,
            "version": info.get("redis_version", "unknown"),
            "uptime_seconds": info.get("uptime_in_seconds", 0),
            "connected_clients": info.get("connected_clients", 0),
            "used_memory": info.get("used_memory", 0),
            "used_memory_human": info.get("used_memory_human", "0B"),
            "memory_usage": info.get("used_memory_human", "0B"),
            "total_commands_processed": info.get("total_commands_processed", 0),
            "keyspace_hits": info.get("keyspace_hits", 0),
            "keyspace_misses": info.get("keyspace_misses", 0)
        }

        return redis_info

    except Exception as e:
        logger.error(f"Failed to get Redis info: {e}")
        return {
            "connected": False,
            "error": str(e),
            "version": "unknown",
            "uptime_seconds": 0,
            "connected_clients": 0,
            "used_memory": 0,
            "used_memory_human": "0B",
            "memory_usage": "0B",
            "total_commands_processed": 0,
            "keyspace_hits": 0,
            "keyspace_misses": 0
        }


# Beat调度管理API
class BeatScheduleEntry(BaseModel):
    name: str
    task: str
    schedule: str
    args: List[Any]
    kwargs: Dict[str, Any]
    enabled: bool
    last_run: Optional[str] = None
    next_run: Optional[str] = None
    total_runs: int = 0


@router.get("/beat/schedule", response_model=List[BeatScheduleEntry])
async def get_beat_schedule():
    """获取Beat调度配置"""
    try:
        redis_client = await get_redis_client()

        # 从Redis获取调度配置
        schedule_keys = await redis_client.keys("celery:beat:schedule:*")
        schedules = []

        for key in schedule_keys:
            schedule_data = await redis_client.hgetall(key)
            if schedule_data:
                # 解码数据
                schedule_data = {k.decode(): v.decode() for k, v in schedule_data.items()}

                # 解析调度名称
                schedule_name = key.decode().split(":")[-1]

                schedules.append(BeatScheduleEntry(
                    name=schedule_name,
                    task=schedule_data.get('task', ''),
                    schedule=schedule_data.get('schedule', ''),
                    args=json.loads(schedule_data.get('args', '[]')),
                    kwargs=json.loads(schedule_data.get('kwargs', '{}')),
                    enabled=schedule_data.get('enabled', 'true').lower() == 'true',
                    last_run=schedule_data.get('last_run'),
                    next_run=schedule_data.get('next_run'),
                    total_runs=int(schedule_data.get('total_runs', 0))
                ))

        return schedules

    except Exception as e:
        logger.error(f"Failed to get Beat schedule: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/beat/schedule/{task_name}/toggle")
async def toggle_beat_task(task_name: str, enabled: bool):
    """启用/禁用Beat任务"""
    try:
        redis_client = await get_redis_client()
        schedule_key = f"celery:beat:schedule:{task_name}"

        # 检查任务是否存在
        exists = await redis_client.exists(schedule_key)
        if not exists:
            raise HTTPException(status_code=404, detail=f"Beat task {task_name} not found")

        # 更新启用状态
        await redis_client.hset(schedule_key, "enabled", "true" if enabled else "false")

        # 如果是监控任务，同时更新ScheduleManager
        if task_name.startswith("monitoring_task_"):
            try:
                from ..core.schedule_manager import get_schedule_manager
                schedule_manager = await get_schedule_manager()

                task_id = task_name.replace("monitoring_task_", "")
                if enabled:
                    # 这里需要获取任务的调度配置来重新注册
                    # 简化处理：只更新Redis状态，实际注册在任务启动时处理
                    logger.info(f"Beat task {task_name} enabled")
                else:
                    await schedule_manager.pause_monitoring_task(task_id)
                    logger.info(f"Beat task {task_name} disabled")
            except Exception as e:
                logger.warning(f"Failed to update ScheduleManager for {task_name}: {e}")

        return {
            "success": True,
            "message": f"Beat task {task_name} {'enabled' if enabled else 'disabled'} successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to toggle Beat task {task_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/beat/schedule/{task_name}")
async def remove_beat_task(task_name: str):
    """移除Beat任务"""
    try:
        redis_client = await get_redis_client()
        schedule_key = f"celery:beat:schedule:{task_name}"

        # 检查任务是否存在
        exists = await redis_client.exists(schedule_key)
        if not exists:
            raise HTTPException(status_code=404, detail=f"Beat task {task_name} not found")

        # 删除调度配置
        await redis_client.delete(schedule_key)

        # 如果是监控任务，同时从ScheduleManager移除
        if task_name.startswith("monitoring_task_"):
            try:
                from ..core.schedule_manager import get_schedule_manager
                schedule_manager = await get_schedule_manager()

                task_id = task_name.replace("monitoring_task_", "")
                await schedule_manager.unregister_monitoring_task(task_id)
                logger.info(f"Beat task {task_name} removed from ScheduleManager")
            except Exception as e:
                logger.warning(f"Failed to remove from ScheduleManager for {task_name}: {e}")

        return {
            "success": True,
            "message": f"Beat task {task_name} removed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove Beat task {task_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 增强的任务管理API
@router.post("/tasks/{task_id}/retry")
async def retry_failed_task(task_id: str):
    """重试失败的任务"""
    try:
        redis_client = await get_redis_client()

        # 获取任务信息
        task_key = f"celery:task:{task_id}"
        task_data = await redis_client.hgetall(task_key)

        if not task_data:
            raise HTTPException(status_code=404, detail="Task not found")

        # 检查任务状态
        task_state = task_data.get(b'state', b'').decode()
        if task_state not in ['FAILURE', 'REVOKED']:
            raise HTTPException(status_code=400, detail=f"Task is not in a retryable state: {task_state}")

        # 获取任务信息
        task_name = task_data.get(b'name', b'').decode()
        task_args = json.loads(task_data.get(b'args', b'[]').decode())
        task_kwargs = json.loads(task_data.get(b'kwargs', b'{}').decode())

        # 重新提交任务
        if task_name == "app.tasks.monitoring_executor.execute_monitoring_task":
            from ..tasks.monitoring_executor import execute_monitoring_task
            new_task = execute_monitoring_task.delay(*task_args, **task_kwargs)
        else:
            # 通用任务重试
            celery_app.send_task(task_name, args=task_args, kwargs=task_kwargs)
            new_task = None

        # 更新原任务状态
        await redis_client.hset(task_key, mapping={
            "retried_at": datetime.now().isoformat(),
            "retry_task_id": new_task.id if new_task else "unknown"
        })

        return {
            "success": True,
            "message": f"Task {task_id} retried successfully",
            "new_task_id": new_task.id if new_task else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retry task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/batch/revoke")
async def batch_revoke_tasks(task_ids: List[str]):
    """批量撤销任务"""
    try:
        results = []

        for task_id in task_ids:
            try:
                celery_app.control.revoke(task_id, terminate=True)

                # 更新Redis中的任务状态
                redis_client = await get_redis_client()
                await redis_client.hset(
                    f"celery:task:{task_id}",
                    mapping={
                        "state": "REVOKED",
                        "revoked_at": datetime.now().isoformat(),
                        "revoked_by": "admin_batch"
                    }
                )

                results.append({"task_id": task_id, "success": True})
            except Exception as e:
                results.append({"task_id": task_id, "success": False, "error": str(e)})

        success_count = sum(1 for r in results if r["success"])

        return {
            "success": True,
            "message": f"Batch revoke completed: {success_count}/{len(task_ids)} successful",
            "results": results
        }

    except Exception as e:
        logger.error(f"Failed to batch revoke tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/failed", response_model=List[CeleryTaskInfo])
async def get_failed_tasks(limit: int = Query(50, description="返回任务数量限制")):
    """获取失败的任务列表"""
    try:
        redis_client = await get_redis_client()

        # 从Redis获取失败的任务
        task_keys = await redis_client.keys("celery:task:*")
        failed_tasks = []

        for key in task_keys:
            task_data = await redis_client.hgetall(key)
            if task_data:
                task_data = {k.decode(): v.decode() for k, v in task_data.items()}

                if task_data.get('state') == 'FAILURE':
                    task_id = key.decode().split(":")[-1]
                    failed_tasks.append(CeleryTaskInfo(
                        id=task_id,
                        name=task_data.get('name', ''),
                        state=task_data.get('state', ''),
                        args=json.loads(task_data.get('args', '[]')),
                        kwargs=json.loads(task_data.get('kwargs', '{}')),
                        result=task_data.get('result'),
                        traceback=task_data.get('traceback'),
                        timestamp=task_data.get('timestamp', ''),
                        worker=task_data.get('worker'),
                        queue=task_data.get('queue')
                    ))

        # 按时间排序，最新的在前
        failed_tasks.sort(key=lambda x: x.timestamp, reverse=True)

        return failed_tasks[:limit]

    except Exception as e:
        logger.error(f"Failed to get failed tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Celery深度监控API
@router.get("/workers/detailed", response_model=List[WorkerDetailedStatus])
async def get_worker_detailed_status():
    """获取Worker详细状态"""
    try:
        inspect = celery_app.control.inspect()

        # 获取Worker统计信息
        stats = await safe_inspect_with_timeout(inspect.stats)
        active_tasks = await safe_inspect_with_timeout(inspect.active)

        workers = []

        if stats:
            for worker_name, worker_stats in stats.items():
                # 获取该Worker的活跃任务数
                worker_active_tasks = len(active_tasks.get(worker_name, [])) if active_tasks else 0

                # 计算处理的任务数
                total_tasks = worker_stats.get('total', {})
                processed_tasks = sum(total_tasks.values()) if isinstance(total_tasks, dict) else 0

                # 获取负载信息
                rusage = worker_stats.get('rusage', {})

                # 安全地获取数值
                def safe_float(value, default=0.0):
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return default

                maxrss = safe_float(rusage.get('maxrss', 0))
                utime = safe_float(rusage.get('utime', 0))
                stime = safe_float(rusage.get('stime', 0))
                clock = safe_float(worker_stats.get('clock', 0))

                load_avg = [utime, stime, maxrss / 1024]  # 转换为MB

                workers.append(WorkerDetailedStatus(
                    name=worker_name,
                    status="online",
                    active_tasks=worker_active_tasks,
                    processed_tasks=processed_tasks,
                    failed_tasks=0,  # 需要从Redis获取
                    load_avg=load_avg,
                    memory_usage=f"{maxrss / 1024:.1f}MB",
                    uptime=f"{clock:.0f}s",
                    last_heartbeat=datetime.now().isoformat()
                ))

        return workers

    except Exception as e:
        logger.error(f"Failed to get detailed worker status: {e}")
        # 返回默认状态
        return [
            WorkerDetailedStatus(
                name="celery@unknown",
                status="unknown",
                active_tasks=0,
                processed_tasks=0,
                failed_tasks=0,
                load_avg=[0.0, 0.0, 0.0],
                memory_usage="0MB",
                uptime="unknown",
                last_heartbeat=datetime.now().isoformat()
            )
        ]


@router.get("/tasks/live-stream")
async def get_live_task_stream():
    """获取实时任务执行流"""
    try:
        inspect = celery_app.control.inspect()

        # 获取正在执行的任务
        active_tasks = await safe_inspect_with_timeout(inspect.active)

        executing_tasks = []

        if active_tasks:
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    # 解析任务参数获取详细信息
                    task_args = task.get('args', [])
                    batch_id = task_args[0] if len(task_args) > 0 else None
                    urls = task_args[1] if len(task_args) > 1 else []
                    platform = task_args[2] if len(task_args) > 2 else "unknown"
                    db_task_id_raw = task_args[3] if len(task_args) > 3 else None

                    # 处理task_id字段 - 保持为字符串类型
                    db_task_id = None
                    if db_task_id_raw:
                        if isinstance(db_task_id_raw, (int, str)):
                            db_task_id = str(db_task_id_raw)
                        # 支持UUID字符串和数字ID

                    total_urls = len(urls) if isinstance(urls, list) else 0

                    # 处理时间戳格式
                    time_start = task.get('time_start')
                    if isinstance(time_start, (int, float)):
                        started_at = datetime.fromtimestamp(time_start).isoformat()
                        start_time = datetime.fromtimestamp(time_start)
                    elif isinstance(time_start, str):
                        started_at = time_start
                        try:
                            start_time = datetime.fromisoformat(time_start.replace('Z', '+00:00'))
                        except:
                            start_time = datetime.now()
                    else:
                        started_at = datetime.now().isoformat()
                        start_time = datetime.now()

                    # 计算已运行时间
                    elapsed_time = (datetime.now(start_time.tzinfo) - start_time).total_seconds()

                    # 获取队列信息
                    delivery_info = task.get('delivery_info', {})
                    queue = delivery_info.get('routing_key', 'default') if isinstance(delivery_info, dict) else 'default'

                    # 从Redis获取任务的实时状态和当前处理URL
                    db_progress = None
                    current_status = "running"
                    current_url = None

                    # 尝试多种方式获取当前URL
                    try:
                        redis_client = await get_redis_client()

                        # 方式1: 如果有db_task_id，从任务进度中获取
                        if db_task_id:
                            progress_key = f"task_progress:{db_task_id}"
                            progress_data = await redis_client.get(progress_key)
                            if progress_data:
                                progress_info = json.loads(progress_data)
                                db_progress = progress_info.get("progress")
                                current_url = progress_info.get("current_url")

                            # 如果没有进度信息，尝试从WebSocket消息中获取当前URL
                            if not current_url:
                                recent_progress_key = f"task_recent_progress:{db_task_id}"
                                recent_data = await redis_client.get(recent_progress_key)
                                if recent_data:
                                    recent_info = json.loads(recent_data)
                                    current_url = recent_info.get("current_url")

                        # 方式2: 如果还是没有URL，尝试从batch_id获取
                        if not current_url and batch_id:
                            batch_progress_key = f"batch_progress:{batch_id}"
                            batch_data = await redis_client.get(batch_progress_key)
                            if batch_data:
                                batch_info = json.loads(batch_data)
                                current_url = batch_info.get("current_url")

                        # 方式3: 从Celery任务ID获取（使用任务ID作为键）
                        if not current_url:
                            celery_task_key = f"celery_task_progress:{task['id']}"
                            celery_data = await redis_client.get(celery_task_key)
                            if celery_data:
                                celery_info = json.loads(celery_data)
                                current_url = celery_info.get("current_url")

                    except Exception as e:
                        logger.warning(f"Failed to get task progress from Redis: {e}")

                    # 调试日志
                    if current_url:
                        logger.debug(f"Found current URL for task {task['id']}: {current_url}")
                    else:
                        logger.debug(f"No current URL found for task {task['id']} (db_task_id: {db_task_id}, batch_id: {batch_id})")

                    # 计算进度和预估时间
                    if db_progress is not None:
                        progress = db_progress
                        if progress > 0 and total_urls > 0:
                            # 基于实际进度计算预估剩余时间
                            avg_time_per_url = elapsed_time / (progress / 100 * total_urls) if progress > 0 else 2.0
                            remaining_urls = total_urls * (100 - progress) / 100
                            estimated_remaining = remaining_urls * avg_time_per_url
                        else:
                            estimated_remaining = total_urls * 2.0  # 假设每个URL 2秒
                    else:
                        # 回退到基于时间的估算
                        if total_urls > 0:
                            avg_time_per_url = 2.0  # 假设每个URL 2秒
                            total_estimated_time = total_urls * avg_time_per_url
                            progress = min(int(elapsed_time / total_estimated_time * 100), 95)
                            estimated_remaining = max(total_estimated_time - elapsed_time, 5)
                        else:
                            progress = 50
                            estimated_remaining = 15

                    task_info = LiveTaskInfo(
                        id=task['id'],
                        name=task['name'],
                        worker=worker,
                        started_at=started_at,
                        estimated_duration=f"{estimated_remaining:.0f}s",
                        progress=progress,
                        args=task.get('args', []),
                        # 队列信息
                        queue=queue,
                        # 调试信息
                        batch_id=batch_id,
                        total_urls=total_urls,
                        platform=platform,
                        task_id=db_task_id,
                        elapsed_time=round(elapsed_time, 1),
                        avg_url_time=round(elapsed_time / max(progress / 100 * total_urls, 1), 2) if progress > 0 and total_urls > 0 else None,
                        current_status=current_status,
                        db_progress=db_progress,
                        # 当前处理的URL
                        current_url=current_url
                    )
                    executing_tasks.append(task_info)

        # 从统一执行服务获取额外的实时任务
        try:
            redis_client = await get_redis_client()

            # 获取正在运行的URL执行记录
            running_execution_ids = await redis_client.smembers("url_executions:by_status:running") or set()

            logger.info(f"Found {len(running_execution_ids)} running execution IDs: {list(running_execution_ids)}")

            for execution_id in running_execution_ids:
                # 处理bytes类型的execution_id
                if isinstance(execution_id, bytes):
                    execution_id = execution_id.decode('utf-8')

                execution_data = await redis_client.hgetall(f"url_executions:{execution_id}")

                # 处理bytes类型的值
                if execution_data:
                    execution_data = {
                        k.decode('utf-8') if isinstance(k, bytes) else k:
                        v.decode('utf-8') if isinstance(v, bytes) else v
                        for k, v in execution_data.items()
                    }

                logger.info(f"Execution data for {execution_id}: {execution_data}")
                if execution_data:
                    # 计算执行时间
                    started_at = execution_data.get("started_at", "")
                    if started_at:
                        try:
                            start_time = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                            elapsed_time = (datetime.now() - start_time.replace(tzinfo=None)).total_seconds()
                        except:
                            elapsed_time = 0
                    else:
                        elapsed_time = 0

                    # 创建实时任务信息
                    # 直接使用monitoring_task_id作为字符串
                    monitoring_task_id = execution_data.get("monitoring_task_id", "")

                    live_task = LiveTaskInfo(
                        id=execution_data.get("celery_task_id", execution_id),
                        name="crawl_batch_task",
                        worker=execution_data.get("assigned_worker_id", "unknown"),
                        started_at=started_at,
                        estimated_duration="unknown",
                        progress=50,  # 默认进度
                        args=[execution_data.get("url", "")],
                        queue="default",
                        batch_id=execution_data.get("session_id", ""),
                        total_urls=1,
                        platform="mercadolibre",
                        task_id=monitoring_task_id if monitoring_task_id else None,  # 直接使用字符串
                        elapsed_time=round(elapsed_time, 1),
                        avg_url_time=round(elapsed_time, 2) if elapsed_time > 0 else None,
                        current_status="processing",
                        db_progress=50,
                        current_url=execution_data.get("url", "")
                    )
                    executing_tasks.append(live_task)

            logger.debug(f"Added {len(running_execution_ids)} running tasks from unified execution service to live stream")

        except Exception as e:
            logger.warning(f"Failed to get unified execution tasks for live stream: {e}")

        return {
            "executing_tasks": executing_tasks,
            "recent_completed": [],  # 可以从Redis获取
            "recent_failed": [],     # 可以从Redis获取
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get live task stream: {e}")
        return {
            "executing_tasks": [],
            "recent_completed": [],
            "recent_failed": [],
            "timestamp": datetime.now().isoformat()
        }


@router.get("/health/diagnostics")
async def get_system_diagnostics():
    """获取系统诊断信息"""
    try:
        redis_client = await get_redis_client()
        inspect = celery_app.control.inspect()

        issues = []
        recommendations = []

        # 检查队列积压
        queue_lengths = {}
        for queue_name in ["celery", "monitor_queue", "high_priority", "low_priority"]:
            try:
                length = await redis_client.llen(queue_name)
                if length > 0:
                    queue_lengths[queue_name] = length

                    if length > 10:
                        issues.append({
                            "type": "warning",
                            "category": "performance",
                            "message": f"队列{queue_name}积压任务较多({length}个)",
                            "suggestion": "考虑增加Worker数量或检查任务执行效率"
                        })
            except:
                pass

        # 检查Worker状态
        stats = await safe_inspect_with_timeout(inspect.stats)
        if not stats:
            issues.append({
                "type": "error",
                "category": "availability",
                "message": "没有可用的Worker",
                "suggestion": "检查Celery Worker服务是否正常运行"
            })
        elif len(stats) < 2:
            issues.append({
                "type": "warning",
                "category": "availability",
                "message": f"只有{len(stats)}个Worker在线",
                "suggestion": "考虑启动更多Worker以提高处理能力"
            })

        # 检查失败任务
        failed_count = 0
        try:
            failed_count = int(await redis_client.get("celery:stats:failed_tasks") or 0)
        except:
            pass

        if failed_count > 5:
            issues.append({
                "type": "warning",
                "category": "reliability",
                "message": f"最近有{failed_count}个任务失败",
                "suggestion": "检查失败任务的错误原因，可能需要修复配置或代码"
            })

        return {
            "issues": issues,
            "recommendations": recommendations,
            "queue_lengths": queue_lengths,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get system diagnostics: {e}")
        return {
            "issues": [],
            "recommendations": [],
            "queue_lengths": {},
            "timestamp": datetime.now().isoformat()
        }
