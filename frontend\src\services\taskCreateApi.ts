import axios from 'axios';
import type {
  TaskCreateFromUrlsRequest,
  TaskCreateResponse,
  UrlPreviewRequest,
  UrlPreviewResponse,
  TaskValidationRequest,
  TaskValidationResponse,
  TaskTemplate,
  TaskCreateStats
} from '../types/taskCreate';

// 创建API客户端
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 300000, // 5分钟超时
});

/**
 * 预览选中的URL信息
 */
export const previewSelectedUrls = async (
  request: UrlPreviewRequest
): Promise<UrlPreviewResponse> => {
  const response = await api.post('/api/v1/tasks/preview-urls', request);
  return response.data;
};

/**
 * 验证任务创建请求
 */
/**
 * 清理请求数据，移除空字符串和无效值
 */
const cleanRequestData = (data: any): any => {
  if (data === null || data === undefined) {
    return undefined;
  }

  if (typeof data === 'string') {
    return data.trim() === '' ? undefined : data;
  }

  if (Array.isArray(data)) {
    return data.map(cleanRequestData).filter(item => item !== undefined);
  }

  if (typeof data === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(data)) {
      const cleanedValue = cleanRequestData(value);
      if (cleanedValue !== undefined) {
        cleaned[key] = cleanedValue;
      }
    }
    return cleaned;
  }

  return data;
};

export const validateTaskCreation = async (
  request: TaskValidationRequest
): Promise<TaskValidationResponse> => {
  // 清理请求数据
  const cleanedRequest = cleanRequestData(request) as TaskValidationRequest;

  console.log('🚀 taskCreateApi - 发送验证请求:', {
    url: '/api/v1/tasks/validate',
    originalRequest: JSON.stringify(request, null, 2),
    cleanedRequest: JSON.stringify(cleanedRequest, null, 2)
  });

  try {
    const response = await api.post('/api/v1/tasks/validate', cleanedRequest);
    console.log('✅ taskCreateApi - 验证响应:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ taskCreateApi - 验证失败:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      originalRequest: request,
      cleanedRequest: cleanedRequest
    });
    throw error;
  }
};

/**
 * 从URL池创建监控任务
 */
export const createTaskFromUrls = async (
  request: TaskCreateFromUrlsRequest
): Promise<TaskCreateResponse> => {
  const response = await api.post('/api/v1/tasks/create-from-urls', request);
  return response.data;
};

/**
 * 获取任务模板列表
 */
export const getTaskTemplates = async (): Promise<{
  success: boolean;
  templates: TaskTemplate[];
  total: number;
}> => {
  const response = await api.get('/api/v1/tasks/templates');
  return response.data;
};

/**
 * 获取任务创建统计信息
 */
export const getTaskCreateStats = async (): Promise<{
  success: boolean;
  data: TaskCreateStats;
}> => {
  const response = await api.get('/api/v1/tasks/stats');
  return response.data;
};

/**
 * 获取任务详细信息
 */
export const getTaskDetails = async (taskId: string): Promise<{
  success: boolean;
  data: any;
}> => {
  const response = await api.get(`/api/v1/tasks/${taskId}`);
  return response.data;
};

/**
 * 删除任务
 */
export const deleteTask = async (taskId: string): Promise<{
  success: boolean;
  message: string;
}> => {
  const response = await api.delete(`/api/v1/tasks/${taskId}`);
  return response.data;
};

/**
 * 生成调度描述
 */
export const generateScheduleDescription = (schedule: any): string => {
  if (!schedule.enabled) {
    return '调度已禁用';
  }

  switch (schedule.type) {
    case 'once':
      if (schedule.start_time) {
        return `一次性执行，时间：${new Date(schedule.start_time).toLocaleString()}`;
      }
      return '一次性执行';

    case 'daily':
      if (schedule.time) {
        return `每日执行，时间：${schedule.time}`;
      } else if (schedule.start_time) {
        // 兼容旧格式
        const time = new Date(schedule.start_time).toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        });
        return `每日执行，时间：${time}`;
      }
      return '每日执行';

    case 'weekly':
      if (schedule.start_time) {
        const date = new Date(schedule.start_time);
        const weekday = date.toLocaleDateString('zh-CN', { weekday: 'long' });
        const time = date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        });
        return `每周执行，时间：${weekday} ${time}`;
      }
      return '每周执行';

    case 'hourly':
      if (schedule.interval) {
        return `每${schedule.interval}分钟执行一次`;
      }
      return '每小时执行';

    case 'custom':
      if (schedule.cron_expression) {
        return `自定义调度：${schedule.cron_expression}`;
      }
      return '自定义调度';

    default:
      return '未知调度类型';
  }
};

/**
 * 验证任务名称
 */
export const validateTaskName = (name: string): string | undefined => {
  if (!name || name.trim().length === 0) {
    return '任务名称不能为空';
  }
  if (name.length > 100) {
    return '任务名称不能超过100个字符';
  }
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/.test(name)) {
    return '任务名称只能包含中文、英文、数字、空格、横线和下划线';
  }
  return undefined;
};

/**
 * 验证URL选择
 */
export const validateUrlSelection = (urlIds: string[]): string | undefined => {
  if (!urlIds || urlIds.length === 0) {
    return '必须选择至少一个URL';
  }
  if (urlIds.length > 1000) {
    return '单个任务最多支持1000个URL';
  }
  return undefined;
};

/**
 * 验证调度配置
 */
export const validateScheduleConfig = (schedule: any): string[] => {
  const errors: string[] = [];

  if (schedule.enabled) {
    // 验证开始时间
    if (schedule.start_time) {
      const startTime = new Date(schedule.start_time);
      const now = new Date();
      if (startTime <= now) {
        errors.push('开始时间必须晚于当前时间');
      }
    }

    // 验证结束时间
    if (schedule.end_time && schedule.start_time) {
      const endTime = new Date(schedule.end_time);
      const startTime = new Date(schedule.start_time);
      if (endTime <= startTime) {
        errors.push('结束时间必须晚于开始时间');
      }
    }

    // 验证间隔时间
    if (schedule.type === 'hourly') {
      if (!schedule.interval || schedule.interval < 1) {
        errors.push('执行间隔必须大于0分钟');
      }
      if (schedule.interval > 1440) {
        errors.push('执行间隔不能超过1440分钟（24小时）');
      }
    }

    // 验证cron表达式
    if (schedule.type === 'custom') {
      if (!schedule.cron_expression) {
        errors.push('自定义调度必须提供cron表达式');
      } else {
        // 简单的cron表达式验证
        const cronParts = schedule.cron_expression.split(' ');
        if (cronParts.length !== 5 && cronParts.length !== 6) {
          errors.push('cron表达式格式不正确');
        }
      }
    }

    // 验证最大执行次数
    if (schedule.max_runs && schedule.max_runs < 1) {
      errors.push('最大执行次数必须大于0');
    }
  }

  return errors;
};

/**
 * 验证任务配置
 */
export const validateTaskConfig = (config: any): string[] => {
  const errors: string[] = [];

  // 验证重试次数
  if (config.retry_count < 0 || config.retry_count > 10) {
    errors.push('重试次数必须在0-10之间');
  }

  // 验证超时时间
  if (config.timeout < 30 || config.timeout > 3600) {
    errors.push('超时时间必须在30-3600秒之间');
  }

  // 验证并发限制
  if (config.concurrent_limit < 1 || config.concurrent_limit > 50) {
    errors.push('并发限制必须在1-50之间');
  }

  // 验证批次大小
  if (config.batch_size < 1 || config.batch_size > 100) {
    errors.push('批次大小必须在1-100之间');
  }

  return errors;
};

/**
 * 格式化平台分布数据
 */
export const formatPlatformDistribution = (distribution: Record<string, number>) => {
  const platformNames: Record<string, string> = {
    'mercadolibre': 'MercadoLibre',
    'amazon': 'Amazon',
    'ebay': 'eBay',
    'aliexpress': 'AliExpress',
    'shopee': 'Shopee',
    'unknown': '未知平台'
  };

  return Object.entries(distribution).map(([platform, count]) => ({
    platform,
    name: platformNames[platform] || platform,
    count,
    percentage: 0 // 将在组件中计算
  }));
};

/**
 * 获取优先级显示信息
 */
export const getPriorityDisplay = (priority: string) => {
  const priorityMap: Record<string, { name: string; color: string }> = {
    'low': { name: '低优先级', color: '#52c41a' },
    'normal': { name: '普通优先级', color: '#1890ff' },
    'high': { name: '高优先级', color: '#fa8c16' },
    'urgent': { name: '紧急优先级', color: '#f5222d' }
  };

  return priorityMap[priority] || { name: priority, color: '#666666' };
};

/**
 * 获取调度类型显示信息
 */
export const getScheduleTypeDisplay = (type: string) => {
  const typeMap: Record<string, string> = {
    'once': '一次性执行',
    'daily': '每日执行',
    'weekly': '每周执行',
    'hourly': '每小时执行',
    'custom': '自定义调度'
  };

  return typeMap[type] || type;
};

/**
 * 计算下次执行时间
 */
export const calculateNextRun = (schedule: any): Date | null => {
  if (!schedule.enabled) {
    return null;
  }

  const now = new Date();

  switch (schedule.type) {
    case 'once':
      return schedule.start_time ? new Date(schedule.start_time) : now;

    case 'daily':
      if (schedule.start_time) {
        const startTime = new Date(schedule.start_time);
        const nextRun = new Date(now);
        nextRun.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);
        
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        
        return nextRun;
      }
      break;

    case 'hourly':
      if (schedule.interval) {
        const nextRun = new Date(now);
        nextRun.setMinutes(nextRun.getMinutes() + schedule.interval);
        return nextRun;
      }
      break;

    // 其他类型的计算逻辑...
  }

  return null;
};
