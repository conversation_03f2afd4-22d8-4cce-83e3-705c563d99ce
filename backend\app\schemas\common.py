"""
通用Pydantic数据模型
"""

from datetime import datetime
from typing import Optional, List, TypeVar, Generic
from pydantic import BaseModel, ConfigDict

# 泛型类型变量
T = TypeVar('T')


class BaseSchema(BaseModel):
    """基础Schema类"""
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        use_enum_values=True
    )


class TimestampSchema(BaseSchema):
    """时间戳Schema混入"""
    
    created_at: datetime
    updated_at: datetime


class ResponseStatus(BaseSchema):
    """响应状态模型"""
    
    success: bool = True
    message: str = "操作成功"
    code: int = 200


class PaginatedResponse(BaseSchema, Generic[T]):
    """分页响应通用模型"""
    
    items: List[T]
    total: int
    page: int = 1
    size: int = 20
    pages: int
    has_next: bool = False
    has_prev: bool = False
    
    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        page: int = 1,
        size: int = 20
    ) -> "PaginatedResponse[T]":
        """创建分页响应"""
        pages = (total + size - 1) // size  # 向上取整
        
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )


class IDResponse(BaseSchema):
    """ID响应模型"""
    
    id: int
    message: str = "操作成功"


class BulkResponse(BaseSchema):
    """批量操作响应模型"""
    
    success_count: int
    failed_count: int
    total_count: int
    errors: Optional[List[str]] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_count == 0:
            return 0.0
        return (self.success_count / self.total_count) * 100


class HealthCheckResponse(BaseSchema):
    """健康检查响应模型"""
    
    status: str = "healthy"
    timestamp: datetime
    app_name: str
    version: str
    environment: str
    
    # 数据库状态
    database_status: str = "unknown"
    redis_status: str = "unknown"
    
    # 系统信息
    uptime_seconds: Optional[int] = None
    memory_usage: Optional[dict] = None
    
    @classmethod
    def create_healthy(
        cls,
        app_name: str,
        version: str,
        environment: str,
        **kwargs
    ) -> "HealthCheckResponse":
        """创建健康状态响应"""
        return cls(
            status="healthy",
            timestamp=datetime.utcnow(),
            app_name=app_name,
            version=version,
            environment=environment,
            **kwargs
        )
    
    @classmethod
    def create_unhealthy(
        cls,
        app_name: str,
        version: str,
        environment: str,
        **kwargs
    ) -> "HealthCheckResponse":
        """创建不健康状态响应"""
        return cls(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            app_name=app_name,
            version=version,
            environment=environment,
            **kwargs
        )


class ErrorResponse(BaseSchema):
    """错误响应模型"""
    
    error: str
    detail: Optional[str] = None
    code: int = 400
    timestamp: datetime = datetime.utcnow()
    
    @classmethod
    def create(
        cls,
        error: str,
        detail: Optional[str] = None,
        code: int = 400
    ) -> "ErrorResponse":
        """创建错误响应"""
        return cls(
            error=error,
            detail=detail,
            code=code,
            timestamp=datetime.utcnow()
        ) 