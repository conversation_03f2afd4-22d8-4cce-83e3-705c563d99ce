import React from 'react';
import { Drawer, List, Button, Typography, Space, Tag, Empty } from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../store';
import {
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearNotifications,
} from '../../store/slices/uiSlice';
import { formatRelativeTime } from '../../utils';
import './index.css';

const { Text, Title } = Typography;

interface NotificationPanelProps {
  visible: boolean;
  onClose: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ visible, onClose }) => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector(state => state.ui);

  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'info':
      default:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'processing';
    }
  };

  const handleMarkAsRead = (id: string) => {
    dispatch(markNotificationAsRead(id));
  };

  const handleRemove = (id: string) => {
    dispatch(removeNotification(id));
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllNotificationsAsRead());
  };

  const handleClearAll = () => {
    dispatch(clearNotifications());
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Drawer
      title={
        <div className="notification-header">
          <Title level={4} style={{ margin: 0 }}>
            通知中心
          </Title>
          {unreadCount > 0 && (
            <Tag color="blue">{unreadCount} 条未读</Tag>
          )}
        </div>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      extra={
        <Space>
          {unreadCount > 0 && (
            <Button
              type="text"
              size="small"
              icon={<CheckOutlined />}
              onClick={handleMarkAllAsRead}
            >
              全部已读
            </Button>
          )}
          {notifications.length > 0 && (
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={handleClearAll}
              danger
            >
              清空
            </Button>
          )}
        </Space>
      }
    >
      {notifications.length === 0 ? (
        <Empty
          description="暂无通知"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <List
          className="notification-list"
          dataSource={notifications}
          renderItem={(notification) => (
            <List.Item
              className={`notification-item ${notification.read ? 'read' : 'unread'}`}
              data-type={notification.type}
              actions={[
                !notification.read && (
                  <Button
                    type="text"
                    size="small"
                    icon={<CheckOutlined />}
                    onClick={() => handleMarkAsRead(notification.id)}
                  >
                    标记已读
                  </Button>
                ),
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => handleRemove(notification.id)}
                  danger
                >
                  删除
                </Button>,
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={getIcon(notification.type)}
                title={
                  <div className="notification-title">
                    <Text strong={!notification.read}>
                      {notification.title}
                    </Text>
                    <Tag
                      color={getTypeColor(notification.type)}
                    >
                      {notification.type}
                    </Tag>
                  </div>
                }
                description={
                  <div className="notification-content">
                    <Text type="secondary">
                      {notification.message}
                    </Text>
                    <Text type="secondary" className="notification-time">
                      {formatRelativeTime(notification.timestamp)}
                    </Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Drawer>
  );
};

export default NotificationPanel;
