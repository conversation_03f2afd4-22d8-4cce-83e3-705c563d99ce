const { spawn } = require('child_process');
const path = require('path');

// 设置环境变量
process.env.GENERATE_SOURCEMAP = 'false';
process.env.FAST_REFRESH = 'true';
process.env.WDS_SOCKET_HOST = 'localhost';
process.env.WDS_SOCKET_PORT = '3000';
process.env.WDS_SOCKET_PATH = '/ws';
process.env.HTTPS = 'false';
process.env.CHOKIDAR_USEPOLLING = 'false';
process.env.BROWSER = 'none';
process.env.REACT_APP_API_URL = 'http://localhost:8000';
process.env.REACT_APP_WS_URL = 'ws://localhost:8000/ws';
process.env.HOST = 'localhost';
process.env.PORT = '3000';
process.env.ESLINT_NO_DEV_ERRORS = 'true';

console.log('🚀 启动React开发服务器...');
console.log('📡 WebSocket配置: localhost:3000/ws');
console.log('🔧 API地址: http://localhost:8000');

// 启动React开发服务器
const reactScripts = spawn('npm', ['run', 'start'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

reactScripts.on('close', (code) => {
  console.log(`React开发服务器退出，代码: ${code}`);
});

reactScripts.on('error', (err) => {
  console.error('启动React开发服务器时出错:', err);
});
