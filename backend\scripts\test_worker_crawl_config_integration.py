#!/usr/bin/env python3
"""
测试Worker管理页面与新爬取配置的集成
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_worker_crawl_config_integration():
    """测试Worker管理页面与新爬取配置的集成"""
    
    print("🧪 测试Worker管理页面与新爬取配置的集成...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 1. 测试新的爬取配置API
            print("\n1. 测试新的爬取配置API...")
            response = await client.get("http://localhost:8000/api/v1/crawl-configs/")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    configs = result['data']['configs']
                    print(f"   ✅ 获取到 {len(configs)} 个爬取配置:")
                    
                    for config in configs:
                        print(f"      - ID: {config['id']}")
                        print(f"        名称: {config['name']}")
                        print(f"        平台: {config['platform']}")
                        print(f"        类型: {config['config_type']}")
                        print(f"        状态: {'活跃' if config['is_active'] else '非活跃'}")
                        print(f"        使用次数: {config['usage_count']}")
                        print()
                    
                    # 检查是否有merc-middleware配置
                    merc_config = next((c for c in configs if c['name'] == 'merc-middleware'), None)
                    if merc_config:
                        print(f"   ✅ 找到merc-middleware配置:")
                        print(f"      - ID: {merc_config['id']}")
                        print(f"      - 状态: {'活跃' if merc_config['is_active'] else '非活跃'}")
                        return merc_config
                    else:
                        print(f"   ❌ 未找到merc-middleware配置")
                        return None
                else:
                    print(f"   ❌ API返回失败: {result.get('message', '未知错误')}")
                    return None
            else:
                print(f"   ❌ API调用失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_worker_creation_with_new_config():
    """测试使用新配置创建Worker"""
    
    print("\n2. 测试使用新配置创建Worker...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 获取merc-middleware配置
            response = await client.get("http://localhost:8000/api/v1/crawl-configs/")
            result = response.json()
            configs = result['data']['configs']
            merc_config = next((c for c in configs if c['name'] == 'merc-middleware'), None)
            
            if not merc_config:
                print("   ❌ 未找到merc-middleware配置，跳过Worker创建测试")
                return False
            
            # 获取后端配置
            backend_response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            if backend_response.status_code != 200:
                print("   ❌ 获取后端配置失败")
                return False
            
            backend_configs = backend_response.json()
            if not backend_configs:
                print("   ❌ 没有可用的后端配置")
                return False
            
            backend_config = backend_configs[0]
            
            # 创建测试Worker
            worker_data = {
                "worker_name": "test-worker-with-merc-config",
                "description": "测试使用merc-middleware配置的Worker",
                "crawler_config_id": merc_config['id'],
                "backend_config_id": backend_config['backend_id'],
                "max_concurrent_tasks": 2,
                "allocated_concurrent": 1,
                "priority": "normal",  # 修复优先级值
                "status": "idle"
            }
            
            print(f"   📝 创建Worker数据:")
            print(f"      - Worker名称: {worker_data['worker_name']}")
            print(f"      - 爬取配置ID: {worker_data['crawler_config_id']}")
            print(f"      - 爬取配置名称: {merc_config['name']}")
            print(f"      - 后端配置ID: {worker_data['backend_config_id']}")
            
            # 发送创建请求
            create_response = await client.post(
                "http://localhost:8000/api/v1/crawler-workers/",
                json=worker_data
            )
            
            if create_response.status_code == 200:
                created_worker = create_response.json()
                print(f"   ✅ Worker创建成功:")
                print(f"      - Worker ID: {created_worker['worker_id']}")
                print(f"      - Worker名称: {created_worker['worker_name']}")
                print(f"      - 爬取配置名称: {created_worker.get('crawler_config_name', '未知')}")
                
                # 验证Worker列表中是否正确显示配置名称
                workers_response = await client.get("http://localhost:8000/api/v1/crawler-workers/")
                if workers_response.status_code == 200:
                    workers = workers_response.json()
                    test_worker = next((w for w in workers if w['worker_id'] == created_worker['worker_id']), None)
                    
                    if test_worker:
                        print(f"   ✅ Worker列表验证:")
                        print(f"      - 显示的爬取配置名称: {test_worker.get('crawler_config_name', '未知')}")
                        print(f"      - 是否正确: {'是' if test_worker.get('crawler_config_name') == merc_config['name'] else '否'}")
                        
                        return test_worker.get('crawler_config_name') == merc_config['name']
                    else:
                        print(f"   ❌ 在Worker列表中未找到创建的Worker")
                        return False
                else:
                    print(f"   ❌ 获取Worker列表失败")
                    return False
                    
            else:
                print(f"   ❌ Worker创建失败: {create_response.status_code}")
                print(f"   错误: {create_response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始测试Worker管理页面与新爬取配置的集成...")
    
    # 测试1: 新爬取配置API
    merc_config = await test_worker_crawl_config_integration()
    
    if merc_config:
        # 测试2: 使用新配置创建Worker
        success = await test_worker_creation_with_new_config()
        
        if success:
            print(f"\n✅ 所有测试通过！Worker管理页面可以正确识别和使用新的爬取配置")
        else:
            print(f"\n❌ Worker创建或配置名称显示测试失败")
    else:
        print(f"\n❌ 新爬取配置API测试失败")
    
    print(f"\n🏁 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
