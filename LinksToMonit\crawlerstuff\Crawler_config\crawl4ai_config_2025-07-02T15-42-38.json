{"server": {"url": "http://localhost:11234", "timeout": "30", "api_key": ""}, "crawler": {"method": "GET", "verbose": false, "check_robots_txt": false, "fetch_ssl_certificate": false, "simulate_user": true, "magic": true, "override_navigator": true, "remove_overlay_elements": true, "ignore_body_visibility": true, "adjust_viewport_to_content": true, "wait_until": "domcontentloaded", "wait_for_images": false, "page_timeout": 60000, "delay_before_return_html": 0.1, "js_only": false, "scan_full_page": true, "process_iframes": true, "scroll_delay": 0.2, "cache_mode": "BYPASS", "screenshot": false, "pdf": false, "capture_mhtml": false, "exclude_external_images": false, "exclude_all_images": false, "image_score_threshold": 50, "image_description_min_word_threshold": 50, "table_score_threshold": 7, "capture_network_requests": false, "capture_console_messages": false, "log_console": false, "browser_config": {"headless": true, "verbose": false, "viewport_width": 1920, "viewport_height": 1080, "wait_for": 2, "timeout": 30, "ignore_https_errors": true, "extra_args": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--exclude-switches=enable-automation"]}}, "extraction": {"extraction_strategy": "LLMExtractionStrategy", "chunking_strategy": "IdentityChunking", "markdown_generator": "DefaultMarkdownGenerator", "verbose": true, "bypass_cache": true, "llm_extraction": {"query": "提取页面中所有文章的标题、作者和发布时间", "provider": "openai", "model": "deepseek-v3-0324", "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi", "temperature": 0, "max_tokens": 16384, "top_p": 0.9, "base_url": "https://api.lkeap.cloud.tencent.com/v1"}, "schema_extraction": {"schema": {"type": "object", "description": "电商商品完整信息提取结构", "properties": {"task_info": {"type": "object", "description": "任务执行信息", "properties": {"start_timestamp": {"type": "string", "pattern": "^[0-9]{14}$", "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"}}, "required": ["start_timestamp"]}, "product_basic_info": {"type": "object", "description": "商品基础信息", "properties": {"product_url": {"type": "string", "format": "uri", "description": "商品链接"}, "product_name": {"type": "string", "description": "商品名称"}, "mlm_id": {"type": "number", "description": "商品MLM-ID中的数值部分"}}, "required": ["product_url", "product_name", "mlm_id"]}, "pricing_info": {"type": "object", "description": "价格和库存信息", "properties": {"sales_count": {"type": "number", "description": "商品销量（格式：+xxx vendidos中的数值）"}, "current_price": {"type": "number", "description": "商品现价（数值）"}, "original_price": {"type": "number", "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"}, "discount_rate": {"type": "number", "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"}, "stock_quantity": {"type": "number", "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"}, "stocktype_IsFull": {"type": "number", "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"}}, "required": ["current_price"]}, "category_info": {"type": "object", "description": "商品目录分类信息", "properties": {"category_breadcrumb": {"type": "string", "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"}, "category_hierarchy": {"type": "array", "description": "商品各级目录详细信息", "items": {"type": "object", "properties": {"category_name": {"type": "string", "description": "目录名称"}, "category_url": {"type": "string", "format": "uri", "description": "目录链接"}, "level": {"type": "integer", "description": "目录层级（1为顶级，2为二级，以此类推）"}}, "required": ["category_name", "level"]}}}, "required": ["category_breadcrumb"]}, "seller_info": {"type": "object", "description": "销售商信息", "properties": {"seller_name": {"type": "string", "description": "商品销售商名称"}, "seller_url": {"type": "string", "format": "uri", "description": "商品销售商链接"}}, "required": ["seller_name"]}, "media_info": {"type": "object", "description": "商品媒体信息", "properties": {"main_image_url": {"type": "string", "format": "uri", "description": "商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）"}}, "required": ["main_image_url"]}, "qa_section": {"type": "object", "description": "问答区域信息", "properties": {"questions": {"type": "array", "description": "商品页面的问题列表", "items": {"type": "object", "properties": {"question_content": {"type": "string", "description": "问题内容"}, "question_time": {"type": "string", "description": "问题时间"}, "answer_content": {"type": "string", "description": "问题回答"}}, "required": ["question_content", "answer_content"]}}, "all_questions_url": {"type": "string", "format": "uri", "description": "商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接"}}}, "rating_info": {"type": "object", "description": "评分信息", "properties": {"rating_score": {"type": "number", "minimum": 0, "maximum": 5, "description": "商品评分数（0-5分）"}, "rating_count": {"type": "integer", "description": "商品评分数量"}}, "required": ["rating_score", "rating_count"]}, "reviews_section": {"type": "object", "description": "评论区域信息", "properties": {"reviews": {"type": "array", "description": "商品评论列表", "items": {"type": "object", "properties": {"review_content": {"type": "string", "description": "评论内容"}, "review_rating": {"type": "number", "minimum": 0, "maximum": 5, "description": "评论评分（0-5分）"}, "review_time": {"type": "string", "description": "评论时间"}}, "required": ["review_content", "review_time"]}}, "all_reviews_url": {"type": "string", "format": "uri", "description": "Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接"}}}}, "required": ["product_basic_info", "category_info", "pricing_info", "media_info", "rating_info"]}, "instructions": "请严格按照以下条件提取商品信息：\n1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品\n2. 如果页面有多个商品，只提取最突出显示的主商品\n3. 重点关注页面标题中提到的商品\n4. 忽略广告推荐和次要商品信息\n5. 确保提取的商品名称与页面URL或页面标题相匹配\n6. 确保提取的商品mlmid与页面URL的mlmid相匹配\n\n需要提取的信息如下：\n商品链接、商品名称、商品MLM-ID；\n商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\\":\\\"Full\\\"，商品有就是1，没有就是0)；\n获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；\n商品销售商名称、商品销售商链接；\n商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；\n商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；\n商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；\n商品评分数、商品评分数量、\n商品的评论内容、评论评分、评论时间；\n商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；\n任务开始的时间戳YYYYMMDDHHMMSS", "validate_schema": true, "return_raw": false}, "content_processing": {"word_count_threshold": 200, "css_selector": "", "target_elements": [], "excluded_tags": ["nav", "footer", "aside"], "excluded_selector": "", "remove_forms": false, "only_text": false, "prettify": false, "parser_type": "lxml", "keep_data_attributes": false, "keep_attrs": []}, "link_filtering": {"exclude_external_links": false, "exclude_internal_links": false, "exclude_social_media_links": false, "exclude_domains": ["example.com", "ads.google.com", "facebook.com", "twitter.com"], "social_media_domains": ["facebook.com", "twitter.com", "instagram.com", "linkedin.com", "youtube.com", "tiktok.com", "pinterest.com", "reddit.com"], "exclude_external_images": false, "exclude_all_images": false, "image_score_threshold": 3, "image_description_min_word_threshold": 50, "table_score_threshold": 7}, "_config_summary": {"enabled_features": {"llm_extraction": true, "css_extraction": false, "schema_extraction": true, "content_processing": true, "link_filtering": true}, "strategy_used": "LLMExtractionStrategy"}}, "task": {"task_type": "batch", "urls": ["https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM#polycard_client=recommendations_pdp-v2p&reco_backend=ranker_retrieval_system_vpp_v2p_coldstart&reco_model=ranker_entity_v2_retrieval_system_vpp_v2p&reco_client=pdp-v2p&reco_item_pos=0&reco_backend_type=low_level&reco_id=b455fd04-c241-4472-add9-422c3ea1bf2b", "https://www.mercadolibre.com.mx/mini-platillo-para-auto-cymbal-bateria-tambor-crash-auto/up/MLMU3190246156#polycard_client=search_best-seller&wid=MLM3703411410&sid=search", "https://articulo.mercadolibre.com.mx/MLM-3699672552-car-crash-cymbal-platillo-de-ventilacion-de-aire-para-auto-_JM#polycard_client=recommendations_vip-v2p&reco_backend=recomm-platform_coldstart&reco_model=coldstart_low_exposition%2C+ranker_entity_v2_retrieval_system_vpp_v2p%2C+coldstart_high_exposition&reco_client=vip-v2p&reco_item_pos=5&reco_backend_type=low_level&reco_id=71ca2090-c8ed-48de-afd0-e1e7555f6b7a", "https://articulo.mercadolibre.com.mx/MLM-3699703058-ventilacion-de-aire-para-platillos-de-coche-2025-divertido-_JM#polycard_client=search_best-seller", "https://www.mercadolibre.com.mx/car-crash-cymbal-air-vent-haz-tu-conduccion-mas-divertida/up/MLMU3162357664#polycard_client=search_best-seller&wid=MLM3674104316&sid=search"], "method": "arun_many", "semaphore_count": 2, "stream": true, "mean_delay": 0.1, "max_range": 3, "scheduler": {"type": "AsyncQueueManager", "pool_size": 10, "memory_threshold": 4096}, "monitor": {"display_mode": "detailed", "show_progress": true, "log_errors": true}}, "html": {"input_method": "direct", "base_url": "", "processing": {"remove_scripts": true, "remove_styles": false, "remove_comments": true, "remove_empty_tags": false, "normalize_whitespace": true, "decode_entities": true, "fix_broken_tags": false, "convert_relative_urls": false, "min_word_count": 10, "max_word_count": 10000, "preserve_formatting": false, "extract_tables": true, "extract_lists": true, "exclude_tags": ["script", "style", "nav", "footer", "aside"]}, "output": {"format": "markdown", "markdown": {"include_links": true, "include_images": true, "include_tables": true, "preserve_formatting": false}, "json": {"pretty_print": true, "include_raw": false, "include_stats": true}, "custom_template": "标题: {{title}}\n作者: {{author}}\n日期: {{date}}\n\n内容:\n{{content}}\n\n链接数量: {{links.length}}\n图片数量: {{images.length}}"}, "html_content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>示例页面</title>\n</head>\n<body>\n    <header>\n        <h1>网站标题</h1>\n        <nav>\n            <a href=\"/home\">首页</a>\n            <a href=\"/about\">关于</a>\n        </nav>\n    </header>\n    <main>\n        <article>\n            <h2>文章标题</h2>\n            <p class=\"author\">作者：张三</p>\n            <p class=\"date\">发布时间：2024-01-01</p>\n            <div class=\"content\">\n                <p>这是文章的主要内容...</p>\n                <img src=\"image.jpg\" alt=\"示例图片\">\n            </div>\n        </article>\n    </main>\n    <footer>\n        <p>© 2024 示例网站</p>\n    </footer>\n</body>\n</html>", "_config_summary": {"enabled_features": {"html_processing": true, "content_extraction": false}, "input_method": "direct", "output_format": "markdown"}}, "_export_info": {"timestamp": "2025-07-02T15:42:38.666Z", "version": "1.0", "description": "Crawl4AI配置文件"}}