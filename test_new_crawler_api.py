"""
测试新的爬虫API设计
验证基于外部爬虫API的集成功能
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_crawler_api_service():
    """测试爬虫API服务"""
    logger.info("=== 测试爬虫API服务 ===")
    
    try:
        from backend.app.services.crawler_api_service import (
            CrawlerAPIService, 
            BatchTaskRequest, 
            LlmJobRequest,
            TaskPriority
        )
        
        # 创建API服务实例
        async with CrawlerAPIService() as api_service:
            
            # 1. 测试系统状态
            logger.info("1. 测试系统状态...")
            try:
                system_status = await api_service.get_system_status()
                logger.info(f"系统状态: {system_status.get('status', 'unknown')}")
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
            
            # 2. 测试系统指标
            logger.info("2. 测试系统指标...")
            try:
                system_metrics = await api_service.get_system_metrics()
                logger.info(f"系统指标获取成功，包含 {len(system_metrics)} 个指标")
            except Exception as e:
                logger.error(f"获取系统指标失败: {e}")
            
            # 3. 测试单个任务创建
            logger.info("3. 测试单个任务创建...")
            try:
                single_request = LlmJobRequest(
                    url="https://example.com",
                    q="提取页面标题和主要内容",
                    cache=False
                )
                
                single_result = await api_service.create_llm_job(single_request)
                logger.info(f"单个任务创建成功: {single_result}")
            except Exception as e:
                logger.error(f"单个任务创建失败: {e}")
            
            # 4. 测试批量任务创建
            logger.info("4. 测试批量任务创建...")
            try:
                batch_request = BatchTaskRequest(
                    urls=[
                        "https://example.com/page1",
                        "https://example.com/page2"
                    ],
                    q="提取页面标题、内容和链接",
                    priority=TaskPriority.HIGH,
                    batch_name="测试批次",
                    batch_description="API集成测试批次",
                    cache=False
                )
                
                batch_result = await api_service.create_batch_tasks(batch_request)
                logger.info(f"批量任务创建成功: batch_id={batch_result.get('batch_id')}")
                
                # 如果有batch_id，测试获取批次状态
                if batch_result.get('batch_id'):
                    batch_status = await api_service.get_batch_tasks(batch_result['batch_id'])
                    logger.info(f"批次状态: {batch_status.get('batch_info', {}).get('status', 'unknown')}")
                
            except Exception as e:
                logger.error(f"批量任务创建失败: {e}")
            
            # 5. 获取客户端统计
            stats = api_service.get_stats()
            logger.info(f"API客户端统计: {stats}")
            
    except Exception as e:
        logger.error(f"爬虫API服务测试失败: {e}")


async def test_crawler_config_service():
    """测试爬虫配置服务"""
    logger.info("=== 测试爬虫配置服务 ===")
    
    try:
        from backend.app.api.v1.crawler_config import (
            CrawlerConfigService,
            CrawlerConfigCreate,
            CrawlerConfigUpdate,
            PlatformType,
            CrawlerConfigType
        )
        from backend.app.services.crawler_api_service import TaskPriority
        
        config_service = CrawlerConfigService()
        
        # 1. 创建配置
        logger.info("1. 创建爬虫配置...")
        config_data = CrawlerConfigCreate(
            name="测试配置",
            description="API集成测试配置",
            config_type=CrawlerConfigType.BATCH_TASK,
            platform=PlatformType.GENERAL,
            llm_query="提取页面标题、内容和元数据",
            llm_schema='{"title": "string", "content": "string", "metadata": "object"}',
            priority=TaskPriority.MEDIUM,
            cache_enabled=True,
            max_concurrent_tasks=5,
            batch_timeout=300
        )
        
        config = await config_service.create_config(config_data, "test_user")
        logger.info(f"配置创建成功: {config.id}")
        
        # 2. 获取配置
        logger.info("2. 获取爬虫配置...")
        retrieved_config = await config_service.get_config(config.id)
        if retrieved_config:
            logger.info(f"配置获取成功: {retrieved_config.name}")
        else:
            logger.error("配置获取失败")
        
        # 3. 更新配置
        logger.info("3. 更新爬虫配置...")
        update_data = CrawlerConfigUpdate(
            description="更新后的测试配置",
            priority=TaskPriority.HIGH
        )
        
        updated_config = await config_service.update_config(config.id, update_data)
        if updated_config:
            logger.info(f"配置更新成功: priority={updated_config.priority}")
        else:
            logger.error("配置更新失败")
        
        # 4. 列出配置
        logger.info("4. 列出爬虫配置...")
        config_list = await config_service.list_configs(page=1, page_size=10)
        logger.info(f"配置列表获取成功: 总数={config_list['total']}")
        
        # 5. 删除配置
        logger.info("5. 删除爬虫配置...")
        delete_success = await config_service.delete_config(config.id)
        if delete_success:
            logger.info("配置删除成功")
        else:
            logger.error("配置删除失败")
            
    except Exception as e:
        logger.error(f"爬虫配置服务测试失败: {e}")


async def test_crawler_task_executor():
    """测试爬虫任务执行器"""
    logger.info("=== 测试爬虫任务执行器 ===")
    
    try:
        from backend.app.services.crawler_task_executor import (
            CrawlerTaskExecutor,
            TaskExecutionRequest,
            ExecutionMode
        )
        from backend.app.services.crawler_api_service import TaskPriority
        from backend.app.api.v1.crawler_config import (
            CrawlerConfigService,
            CrawlerConfigCreate,
            PlatformType,
            CrawlerConfigType
        )
        
        # 初始化服务
        executor = CrawlerTaskExecutor()
        await executor.initialize()
        
        config_service = CrawlerConfigService()
        
        # 1. 创建测试配置
        logger.info("1. 创建测试配置...")
        config_data = CrawlerConfigCreate(
            name="执行器测试配置",
            description="任务执行器测试配置",
            config_type=CrawlerConfigType.BATCH_TASK,
            platform=PlatformType.GENERAL,
            llm_query="提取页面的主要信息",
            priority=TaskPriority.MEDIUM,
            cache_enabled=False
        )
        
        config = await config_service.create_config(config_data, "test_executor")
        logger.info(f"测试配置创建成功: {config.id}")
        
        # 2. 执行批量任务
        logger.info("2. 执行批量任务...")
        execution_request = TaskExecutionRequest(
            monitoring_task_id="test_monitoring_task_001",
            urls=[
                "https://example.com/test1",
                "https://example.com/test2"
            ],
            config_id=config.id,
            execution_mode=ExecutionMode.BATCH,
            priority=TaskPriority.HIGH,
            metadata={"test": True, "source": "api_test"}
        )
        
        execution_result = await executor.execute_monitoring_task(execution_request)
        logger.info(f"任务执行结果: {execution_result.status}, execution_id={execution_result.execution_id}")
        
        # 3. 获取执行状态
        logger.info("3. 获取执行状态...")
        execution_status = await executor.get_execution_status(execution_result.execution_id)
        if execution_status:
            logger.info(f"执行状态获取成功: status={execution_status.get('status')}")
        else:
            logger.error("执行状态获取失败")
        
        # 4. 获取任务执行历史
        logger.info("4. 获取任务执行历史...")
        executions = await executor.get_task_executions("test_monitoring_task_001", limit=5)
        logger.info(f"执行历史获取成功: 共{len(executions)}条记录")
        
        # 5. 获取执行器统计
        stats = executor.get_executor_stats()
        logger.info(f"执行器统计: {stats}")
        
        # 清理测试配置
        await config_service.delete_config(config.id)
        logger.info("测试配置已清理")
        
    except Exception as e:
        logger.error(f"爬虫任务执行器测试失败: {e}")


async def test_integration():
    """集成测试"""
    logger.info("=== 开始集成测试 ===")
    
    try:
        # 测试各个组件
        await test_crawler_api_service()
        await test_crawler_config_service()
        await test_crawler_task_executor()
        
        logger.info("=== 集成测试完成 ===")
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")


def print_test_summary():
    """打印测试总结"""
    print("\n" + "="*60)
    print("新爬虫API设计测试总结")
    print("="*60)
    print("测试组件:")
    print("1. CrawlerAPIService - 外部爬虫API客户端")
    print("2. CrawlerConfigService - 爬虫配置管理")
    print("3. CrawlerTaskExecutor - 任务执行器")
    print("\n主要功能:")
    print("- 基于外部爬虫API (localhost:11238) 的任务提交")
    print("- 支持批量和单个任务执行模式")
    print("- 完整的配置管理和模板系统")
    print("- 执行历史和状态跟踪")
    print("- 统计信息和性能监控")
    print("\nAPI端点:")
    print("- /api/v1/crawler/configs/ - 配置管理")
    print("- /api/v1/crawler/execute - 任务执行")
    print("- /api/v1/crawler/executions/{id} - 执行状态")
    print("- /api/v1/crawler/test - 连接测试")
    print("="*60)


if __name__ == "__main__":
    print_test_summary()
    
    # 运行测试
    try:
        asyncio.run(test_integration())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
    
    print("\n测试完成！请检查日志输出以了解详细结果。")
