filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/monit/*.log
  fields:
    service: monit-backend
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/monit/celery/*.log
  fields:
    service: monit-celery
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/monit/crawler/*.log
  fields:
    service: monit-crawler
  fields_under_root: true

- type: container
  enabled: true
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"

output.logstash:
  hosts: ["logstash:5044"]

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
