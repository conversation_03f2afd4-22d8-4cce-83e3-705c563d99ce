#!/usr/bin/env python3
"""
测试任务详情相关的API接口
"""

import asyncio
import aiohttp
import json
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

async def test_monitoring_task_apis():
    """测试监控任务相关的API接口"""
    
    async with aiohttp.ClientSession() as session:
        print("🚀 开始测试监控任务API接口...")
        
        # 1. 测试获取任务列表
        print("\n1. 测试获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务列表成功: {len(data.get('data', []))} 个任务")
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        print(f"📝 使用第一个任务进行后续测试: {task_id}")
                    else:
                        print("⚠️ 没有找到任务，创建一个测试任务...")
                        task_id = await create_test_task(session)
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        if not task_id:
            print("❌ 无法获取有效的任务ID")
            return
        
        # 2. 测试获取任务详情
        print(f"\n2. 测试获取任务详情 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务详情成功: {data['data']['name']}")
                else:
                    print(f"❌ 获取任务详情失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务详情异常: {e}")
        
        # 3. 测试获取任务URL列表
        print(f"\n3. 测试获取任务URL列表 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务URL列表成功: {len(data.get('data', []))} 个URL")
                else:
                    print(f"❌ 获取任务URL列表失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务URL列表异常: {e}")
        
        # 4. 测试获取任务执行历史
        print(f"\n4. 测试获取任务执行历史 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/history") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务执行历史成功: {len(data.get('data', []))} 条记录")
                else:
                    print(f"❌ 获取任务执行历史失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务执行历史异常: {e}")
        
        # 5. 测试获取任务统计信息
        print(f"\n5. 测试获取任务统计信息 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务统计信息成功")
                    stats = data.get('data', {})
                    print(f"   URL统计: {stats.get('url_stats', {})}")
                    print(f"   执行统计: {stats.get('execution_stats', {})}")
                else:
                    print(f"❌ 获取任务统计信息失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务统计信息异常: {e}")
        
        # 6. 测试获取任务执行状态
        print(f"\n6. 测试获取任务执行状态 (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execution-status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取任务执行状态成功")
                    status_info = data.get('data', {})
                    print(f"   任务状态: {status_info.get('status')}")
                    print(f"   是否运行中: {status_info.get('is_running')}")
                else:
                    print(f"❌ 获取任务执行状态失败: {response.status}")
        except Exception as e:
            print(f"❌ 获取任务执行状态异常: {e}")
        
        # 7. 测试手动执行任务
        print(f"\n7. 测试手动执行任务 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/execute") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 手动执行任务成功: {data.get('message')}")
                    execution_id = data.get('execution_id')
                    print(f"   执行ID: {execution_id}")
                else:
                    error_data = await response.json()
                    print(f"❌ 手动执行任务失败: {response.status} - {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 手动执行任务异常: {e}")
        
        # 8. 测试终止任务执行
        print(f"\n8. 测试终止任务执行 (ID: {task_id})...")
        try:
            async with session.post(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/terminate") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 终止任务执行成功: {data.get('message')}")
                else:
                    error_data = await response.json()
                    print(f"❌ 终止任务执行失败: {response.status} - {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 终止任务执行异常: {e}")
        
        print("\n🎉 API接口测试完成!")

async def create_test_task(session):
    """创建一个测试任务"""
    task_data = {
        "name": "测试任务",
        "description": "用于API测试的任务",
        "schedule": {
            "type": "daily",
            "time": "09:00",
            "timezone": "Asia/Shanghai"
        },
        "config": {
            "platform": "taobao",
            "priority": "medium",
            "retry_count": 3,
            "timeout": 30,
            "batch_size": 10
        },
        "urls": [
            "https://example.com/product1",
            "https://example.com/product2"
        ]
    }
    
    try:
        async with session.post(
            f"{API_BASE_URL}/api/v1/monitoring-tasks/",
            json=task_data
        ) as response:
            if response.status == 200:
                data = await response.json()
                task_id = data['data']['id']
                print(f"✅ 创建测试任务成功: {task_id}")
                return task_id
            else:
                print(f"❌ 创建测试任务失败: {response.status}")
                return None
    except Exception as e:
        print(f"❌ 创建测试任务异常: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(test_monitoring_task_apis())
