import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  <PERSON>,
  Tabs,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Badge,
  Tooltip,
  Progress,
  Statistic,
  List,
  Tag,
  Table,
  Select,
  DatePicker,
  Modal,
  Descriptions,
  message
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  BarsOutlined,
  TrophyOutlined,
  BugOutlined,
  FilterOutlined,
  ClearOutlined,
  EyeOutlined,
  CopyOutlined
} from '@ant-design/icons';
// 移除外部组件导入，改为内联实现
// import CrawlerRequestPanel from './components/CrawlerRequestPanel';

// 爬虫请求调试相关的类型定义
interface CrawlerRequest {
  id: string;
  taskId: string;
  url: string;
  workerId: string;
  celeryWorker?: string;
  timestamp: string;
  method: string;
  headers: Record<string, string>;
  payload: any;
  response?: {
    status: number;
    responseTime: number;
    data: any;
  };
  error?: string;
  status: 'pending' | 'sent' | 'success' | 'failed';
}

interface CrawlerWorkerInfo {
  worker_id: string;
  status: 'online' | 'offline' | 'busy';
  capabilities: string[];
  lastSeen: string;
}





const { Title, Text } = Typography;

interface WorkerStatus {
  name: string;
  status: string;
  active_tasks: number;
  processed_tasks: number;
  failed_tasks: number;
  load_avg: number[];
  memory_usage: string;
  uptime: string;
  last_heartbeat: string;
}

interface LiveTask {
  id: string;
  name: string;
  worker: string;
  started_at: string;
  estimated_duration: string;
  progress: number;
  args: any[];
  // 队列信息
  queue?: string;
  // 新增调试信息字段
  batch_id?: string;
  total_urls?: number;
  platform?: string;
  task_id?: string;  // 改为字符串类型，支持UUID
  elapsed_time?: number;
  avg_url_time?: number;
  current_status?: string;
  db_progress?: number;
  // 当前处理的URL
  current_url?: string;
}

interface SystemIssue {
  type: string;
  category: string;
  message: string;
  suggestion: string;
}

const CeleryMonitoringV3: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [workers, setWorkers] = useState<WorkerStatus[]>([]);
  const [liveTasks, setLiveTasks] = useState<LiveTask[]>([]);
  // 添加子tab状态管理
  const [taskSubTab, setTaskSubTab] = useState('celery-tasks');
  const [diagnostics, setDiagnostics] = useState<{
    issues: SystemIssue[];
    queue_lengths: Record<string, number>;
  }>({ issues: [], queue_lengths: {} });
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 获取Worker详细状态
  const fetchWorkerStatus = async () => {
    try {
      const response = await fetch('/api/v1/celery/workers/detailed');
      if (response.ok) {
        const data = await response.json();
        setWorkers(data);
      }
    } catch (error) {
      console.error('Failed to fetch worker status:', error);
    }
  };

  // 获取实时任务流
  const fetchLiveTasks = async () => {
    try {
      const response = await fetch('/api/v1/celery/tasks/live-stream');
      if (response.ok) {
        const data = await response.json();
        const tasks = data.executing_tasks || [];

        // 可选：调试信息（生产环境可移除）
        if (process.env.NODE_ENV === 'development') {
          console.log('Live tasks data:', tasks);
          tasks.forEach((task: LiveTask) => {
            if (task.current_url) {
              console.log(`Task ${task.id} current URL: ${task.current_url}`);
            }
          });
        }

        setLiveTasks(tasks);
      }
    } catch (error) {
      console.error('Failed to fetch live tasks:', error);
    }
  };

  // 获取系统诊断
  const fetchDiagnostics = async () => {
    try {
      const response = await fetch('/api/v1/celery/health/diagnostics');
      if (response.ok) {
        const data = await response.json();
        setDiagnostics(data);
      }
    } catch (error) {
      console.error('Failed to fetch diagnostics:', error);
    }
  };

  // 区域刷新状态管理
  const [refreshingStates, setRefreshingStates] = useState({
    workers: false,
    tasks: false,
    diagnostics: false
  });

  // 区域刷新函数
  const refreshWorkers = useCallback(async () => {
    setRefreshingStates(prev => ({ ...prev, workers: true }));
    try {
      await fetchWorkerStatus();
    } catch (error) {
      console.error('Failed to refresh workers:', error);
    } finally {
      setRefreshingStates(prev => ({ ...prev, workers: false }));
    }
  }, []);

  const refreshTasks = useCallback(async () => {
    setRefreshingStates(prev => ({ ...prev, tasks: true }));
    try {
      await fetchLiveTasks();
    } catch (error) {
      console.error('Failed to refresh tasks:', error);
    } finally {
      setRefreshingStates(prev => ({ ...prev, tasks: false }));
    }
  }, []);

  const refreshDiagnostics = useCallback(async () => {
    setRefreshingStates(prev => ({ ...prev, diagnostics: true }));
    try {
      await fetchDiagnostics();
    } catch (error) {
      console.error('Failed to refresh diagnostics:', error);
    } finally {
      setRefreshingStates(prev => ({ ...prev, diagnostics: false }));
    }
  }, []);

  // 智能全量刷新 - 根据当前标签页决定刷新范围
  const handleRefresh = useCallback(async () => {

    setLoading(true);
    try {
      switch (activeTab) {
        case 'overview':

          // 概览页面刷新所有数据
          await Promise.all([
            refreshWorkers(),
            refreshTasks(),
            refreshDiagnostics()
          ]);
          break;
        case 'workers':

          // Worker页面只刷新Worker相关数据
          await Promise.all([
            refreshWorkers(),
            refreshDiagnostics()
          ]);
          break;
        case 'tasks':

          // 任务页面只刷新Celery任务，不影响爬虫请求调试面板
          await Promise.all([
            refreshTasks(),
            refreshDiagnostics()
          ]);
          break;
        case 'diagnostics':

          // 诊断页面只刷新诊断数据
          await refreshDiagnostics();
          break;
        default:

          // 默认刷新概览数据（不包括任务）
          await Promise.all([
            refreshWorkers(),
            refreshDiagnostics()
          ]);
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setLoading(false);
    }
  }, [activeTab, refreshWorkers, refreshTasks, refreshDiagnostics]);

  // 智能自动刷新 - 根据当前活跃标签页只刷新相关数据
  // 注意：爬虫请求调试面板有自己的自动刷新机制，这里不需要干扰
  const smartAutoRefresh = useCallback(async () => {
    switch (activeTab) {
      case 'overview':
        // 概览页面需要所有数据，但可以并行刷新
        await Promise.all([refreshWorkers(), refreshDiagnostics()]);
        break;
      case 'workers':
        await refreshWorkers();
        break;
      case 'tasks':
        // 只刷新Celery任务，不影响爬虫请求调试面板
        await refreshTasks();
        break;
      case 'diagnostics':
        await refreshDiagnostics();
        break;
      default:
        // 默认刷新概览数据
        await Promise.all([refreshWorkers(), refreshDiagnostics()]);
    }
  }, [activeTab, refreshWorkers, refreshTasks, refreshDiagnostics]);

  // 初始化
  useEffect(() => {
    handleRefresh();
  }, [handleRefresh]);

  // 智能自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(smartAutoRefresh, 5000); // 每5秒智能刷新
      return () => clearInterval(interval);
    }
  }, [autoRefresh, smartAutoRefresh]);

  // 计算系统健康度
  const calculateSystemHealth = () => {
    const onlineWorkers = workers.filter(w => w.status === 'online').length;
    const totalWorkers = workers.length || 1;
    const issueCount = diagnostics.issues.length;
    
    let health = (onlineWorkers / totalWorkers) * 100;
    health -= issueCount * 10; // 每个问题扣10分
    
    return Math.max(0, Math.min(100, health));
  };

  const systemHealth = calculateSystemHealth();

  // 系统概览面板
  const OverviewPanel = () => (
    <div>
      {/* 系统健康度 */}
      <Row gutter={16} style={{ marginBottom: 24 }} key="system-health-row">
        <Col span={24} key="system-health-col">
          <Card
            key="system-health-card"
            title="系统健康度"
            extra={
              <Button
                size="small"
                icon={<ReloadOutlined />}
                loading={refreshingStates.diagnostics}
                onClick={refreshDiagnostics}
              >
                刷新诊断
              </Button>
            }
          >
            <Row align="middle" key="health-content-row">
              <Col span={18} key="health-progress-col">
                <Progress
                  key="health-progress"
                  percent={systemHealth}
                  status={systemHealth >= 80 ? 'success' : systemHealth >= 60 ? 'normal' : 'exception'}
                  strokeColor={systemHealth >= 80 ? '#52c41a' : systemHealth >= 60 ? '#1890ff' : '#ff4d4f'}
                />
              </Col>
              <Col span={6} style={{ textAlign: 'right' }} key="health-statistic-col">
                <Statistic
                  key="health-statistic"
                  value={systemHealth}
                  suffix="%"
                  valueStyle={{ color: systemHealth >= 80 ? '#52c41a' : systemHealth >= 60 ? '#1890ff' : '#ff4d4f' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 关键指标 */}
      <Row gutter={16} style={{ marginBottom: 24 }} key="key-metrics-row">
        <Col span={6} key="worker-status-col">
          <Card
            key="worker-status-card"
            title="Worker状态"
            extra={
              <Button
                size="small"
                icon={<ReloadOutlined />}
                loading={refreshingStates.workers}
                onClick={refreshWorkers}
              />
            }
          >
            <Statistic
              key="worker-status-statistic"
              title="在线Worker"
              value={workers.filter(w => w.status === 'online').length}
              suffix={`/ ${workers.length}`}
              prefix={<TeamOutlined />}
              valueStyle={{ color: workers.every(w => w.status === 'online') ? '#52c41a' : '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6} key="task-status-col">
          <Card
            key="task-status-card"
            title="任务状态"
            extra={
              <Button
                size="small"
                icon={<ReloadOutlined />}
                loading={refreshingStates.tasks}
                onClick={refreshTasks}
              />
            }
          >
            <Statistic
              key="task-status-statistic"
              title="执行中任务"
              value={liveTasks.length}
              prefix={<BarsOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6} key="queue-backlog-col">
          <Card key="queue-backlog-card">
            <Statistic
              key="queue-backlog-statistic"
              title="队列积压"
              value={Object.values(diagnostics.queue_lengths).reduce((a, b) => a + b, 0)}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: Object.values(diagnostics.queue_lengths).some(v => v > 10) ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6} key="system-issues-col">
          <Card key="system-issues-card">
            <Statistic
              key="system-issues-statistic"
              title="系统问题"
              value={diagnostics.issues.length}
              prefix={<WarningOutlined />}
              valueStyle={{ color: diagnostics.issues.length > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统问题警告 */}
      {diagnostics.issues.length > 0 && (
        <Alert
          key="system-issues-alert"
          message="发现系统问题"
          description={
            <List
              key="system-issues-list"
              size="small"
              dataSource={diagnostics.issues}
              renderItem={(issue, index) => (
                <List.Item key={`issue-${index}-${issue.type}-${issue.message.slice(0, 10)}`}>
                  <Space>
                    <Tag color={issue.type === 'error' ? 'red' : 'orange'}>
                      {issue.type.toUpperCase()}
                    </Tag>
                    <Text>{issue.message}</Text>
                    <Text type="secondary">建议: {issue.suggestion}</Text>
                  </Space>
                </List.Item>
              )}
            />
          }
          type={diagnostics.issues.some(i => i.type === 'error') ? 'error' : 'warning'}
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
    </div>
  );

  // Worker状态面板
  const WorkerPanel = () => (
    <div>
      {/* Worker面板头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={4} style={{ margin: 0 }}>Worker状态监控</Title>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            loading={refreshingStates.workers}
            onClick={refreshWorkers}
            type="primary"
            size="small"
          >
            刷新Worker状态
          </Button>
        </Col>
      </Row>

      <Row gutter={16}>
        {workers.map((worker) => (
          <Col span={12} key={worker.name} style={{ marginBottom: 16 }}>
            <Card
              title={
                <Space>
                  <TeamOutlined />
                  <Text strong>{worker.name}</Text>
                  <Tag color={worker.status === 'online' ? 'green' : 'red'}>
                    {worker.status}
                  </Tag>
                </Space>
              }
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="活跃任务"
                    value={worker.active_tasks}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="已处理"
                    value={worker.processed_tasks}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="内存使用"
                    value={worker.memory_usage}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
              </Row>
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">运行时间: {worker.uptime}</Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  // Celery任务面板
  const CeleryTaskPanel = () => {
    const columns = [
      {
        title: '任务信息',
        key: 'task_info',
        width: 350,
        render: (record: LiveTask) => (
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div>
              <Text strong style={{ fontSize: '13px' }}>{record.name}</Text>
              <Tag color="blue" style={{ marginLeft: 8, fontSize: '11px' }}>
                {record.platform || 'unknown'}
              </Tag>
            </div>
            <Tooltip title={record.id}>
              <Text type="secondary" style={{ fontSize: '11px' }}>
                完整ID: {record.id}
              </Text>
            </Tooltip>
            {record.batch_id && (
              <Text type="secondary" style={{ fontSize: '11px' }}>
                Batch: {record.batch_id.slice(-8)}
              </Text>
            )}
            {/* 当前处理的URL */}
            {record.current_url && (
              <div style={{ marginTop: 4 }}>
                <Text type="secondary" style={{ fontSize: '10px' }}>当前URL:</Text>
                <Tooltip title={record.current_url}>
                  <Text code style={{ fontSize: '10px', display: 'block', marginTop: 2 }}>
                    {record.current_url.length > 50 ?
                      `${record.current_url.substring(0, 50)}...` :
                      record.current_url
                    }
                  </Text>
                </Tooltip>
              </div>
            )}
          </Space>
        ),
      },
      {
        title: 'Worker',
        dataIndex: 'worker',
        key: 'worker',
        width: 150,
        render: (worker: string) => (
          <Text style={{ fontSize: '12px' }}>{worker.split('@')[1] || worker}</Text>
        ),
      },
      {
        title: '队列',
        dataIndex: 'queue',
        key: 'queue',
        width: 120,
        render: (queue: string) => (
          <Tag color={getQueueColor(queue)} style={{ fontSize: '11px' }}>
            {queue || 'default'}
          </Tag>
        ),
      },
      {
        title: '进度',
        key: 'progress',
        width: 200,
        render: (record: LiveTask) => (
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Progress
              percent={record.progress}
              size="small"
              status={record.progress >= 95 ? 'success' : 'active'}
              format={(percent) => `${percent}%`}
            />
            {record.total_urls && (
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {Math.round(record.progress / 100 * record.total_urls)}/{record.total_urls} URLs
              </Text>
            )}
            {record.db_progress !== null && record.db_progress !== record.progress && (
              <Tag color="orange" style={{ fontSize: '10px' }}>
                DB: {record.db_progress}%
              </Tag>
            )}
          </Space>
        ),
      },
      {
        title: '时间信息',
        key: 'time_info',
        width: 180,
        render: (record: LiveTask) => (
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              已运行: {record.elapsed_time ? `${record.elapsed_time}s` : '-'}
            </Text>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              预计剩余: {record.estimated_duration}
            </Text>
            {record.avg_url_time && (
              <Text type="secondary" style={{ fontSize: '11px' }}>
                平均: {record.avg_url_time}s/URL
              </Text>
            )}
          </Space>
        ),
      },
      {
        title: '状态',
        key: 'status',
        width: 100,
        render: (record: LiveTask) => (
          <Space direction="vertical" size="small">
            <Badge
              status={record.current_status === 'running' ? 'processing' : 'default'}
              text={record.current_status || 'running'}
            />
            <Text type="secondary" style={{ fontSize: '10px' }}>
              {new Date(record.started_at).toLocaleTimeString()}
            </Text>
          </Space>
        ),
      },
    ];

    // 队列颜色映射
    const getQueueColor = (queue: string) => {
      const colorMap: { [key: string]: string } = {
        'crawler_queue': 'blue',
        'monitor_queue': 'green',
        'cleanup_queue_low': 'orange',
        'crawler_queue_high': 'purple',
        'monitor_queue_high': 'cyan',
        'default': 'default',
      };
      return colorMap[queue] || 'default';
    };

    return (
      <div>
        {/* 任务面板头部 */}
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Title level={4} style={{ margin: 0 }}>实时任务监控</Title>
              {liveTasks.length > 0 && (
                <Badge count={liveTasks.length} size="small" style={{ backgroundColor: '#1890ff' }} />
              )}
            </Space>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              loading={refreshingStates.tasks}
              onClick={refreshTasks}
              type="primary"
              size="small"
            >
              刷新任务列表
            </Button>
          </Col>
        </Row>

        {liveTasks.length === 0 ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
              <Title level={4}>当前没有执行中的任务</Title>
              <Text type="secondary">系统空闲中</Text>
            </div>
          </Card>
        ) : (
          <Table
            dataSource={liveTasks}
            columns={columns}
            rowKey={(record) => `${record.id}_${record.current_url || 'no-url'}_${record.worker || 'no-worker'}_${record.started_at || Date.now()}`}
            pagination={false}
            size="small"
            scroll={{ x: 1200 }}
            style={{ backgroundColor: 'white' }}
          />
        )}
      </div>
    );
  };

  // 爬虫请求调试面板的状态管理
  const [crawlerRequests, setCrawlerRequests] = useState<CrawlerRequest[]>([]);
  const [crawlerWorkers, setCrawlerWorkers] = useState<CrawlerWorkerInfo[]>([]);
  const [crawlerLoading, setCrawlerLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<CrawlerRequest | null>(null);
  const [crawlerFilters, setCrawlerFilters] = useState({
    workerId: '',
    status: '',
    timeRange: null as [any, any] | null
  });

  // 分页状态管理，从localStorage恢复用户设置
  const [crawlerPagination, setCrawlerPagination] = useState(() => {
    const saved = localStorage.getItem('celery-monitoring-crawler-pagination');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        return {
          current: 1, // 页面刷新时总是从第一页开始
          pageSize: parsed.pageSize || 20,
          total: 0
        };
      } catch (e) {
        console.warn('Failed to parse saved pagination settings:', e);
      }
    }
    return {
      current: 1,
      pageSize: 20,
      total: 0
    };
  });

  // 保存分页设置到localStorage
  useEffect(() => {
    localStorage.setItem('celery-monitoring-crawler-pagination', JSON.stringify({
      pageSize: crawlerPagination.pageSize
    }));
  }, [crawlerPagination.pageSize]);



  // 获取爬虫请求历史数据
  const fetchCrawlerRequestHistory = useCallback(async (isLiveUpdate: boolean = false) => {
    try {
      if (!isLiveUpdate) {
        setCrawlerLoading(true);
      }

      const response = await fetch('/api/v1/crawler-debug/requests?limit=100');

      if (response.ok) {
        const data = await response.json();

        // 处理直接返回数组的情况（根据后端API实现）
        let requestsArray = [];
        if (Array.isArray(data)) {
          requestsArray = data;
        } else if (data.success && data.data) {
          requestsArray = data.data;
        } else if (data.data) {
          requestsArray = data.data;
        }

        const newRequests = requestsArray.map((item: any) => ({
          id: item.id,
          taskId: item.task_id,
          url: item.url,
          workerId: item.worker_id,
          celeryWorker: item.celery_worker,
          timestamp: item.timestamp,
          method: item.method,
          headers: item.headers || {},
          payload: item.payload || {},
          response: item.response,
          error: item.error,
          status: item.status
        }));

        setCrawlerRequests(newRequests);
      } else {
        setCrawlerRequests([]);
      }
    } catch (error) {
      console.error('🔍 [ERROR] Failed to fetch crawler requests:', error);
      // 发生错误时也设置空数组
      setCrawlerRequests([]);
    } finally {
      if (!isLiveUpdate) {
        setCrawlerLoading(false);
      }
    }
  }, []);

  // 获取爬虫Worker信息
  const fetchCrawlerWorkers = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/crawler-debug/workers');

      if (response.ok) {
        const data = await response.json();

        // 处理直接返回数组的情况
        let workersArray = [];
        if (Array.isArray(data)) {
          workersArray = data;
        } else if (data.success && data.data) {
          workersArray = data.data;
        } else if (data.data) {
          workersArray = data.data;
        }

        // 设置Worker数据（如果没有数据则为空数组）
        setCrawlerWorkers(workersArray);
      } else {
        setCrawlerWorkers([]);
      }
    } catch (error) {
      console.error('🔍 [ERROR] Failed to fetch crawler workers:', error);
      setCrawlerWorkers([]);
    }
  }, []);

  // 内联爬虫请求调试面板，使用useCallback确保能访问最新状态
  const InlineCrawlerRequestPanel = useCallback(() => {

    return (
      <div>
        {/* 功能说明 */}
        <Alert
          message="爬虫请求调试"
          description="监控Celery Worker发送给爬虫后端Worker的请求详情，便于调试不同版本爬虫Worker的兼容性问题。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 控制面板 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={18}>
            <Card title="过滤条件" size="small">
              <Row gutter={8}>
                <Col span={6}>
                  <Select
                    placeholder="选择Worker"
                    value={crawlerFilters.workerId}
                    onChange={(value) => setCrawlerFilters(prev => ({ ...prev, workerId: value }))}
                    allowClear
                    size="small"
                    style={{ width: '100%' }}
                  >
                    {crawlerWorkers.map(worker => (
                      <Select.Option key={worker.worker_id} value={worker.worker_id}>
                        {worker.worker_id}
                      </Select.Option>
                    ))}
                  </Select>
                </Col>
                <Col span={4}>
                  <Select
                    placeholder="状态"
                    value={crawlerFilters.status}
                    onChange={(value) => setCrawlerFilters(prev => ({ ...prev, status: value }))}
                    allowClear
                    size="small"
                    style={{ width: '100%' }}
                  >
                    <Select.Option value="pending">Pending</Select.Option>
                    <Select.Option value="sent">Sent</Select.Option>
                    <Select.Option value="success">Success</Select.Option>
                    <Select.Option value="failed">Failed</Select.Option>
                  </Select>
                </Col>
                <Col span={6}>
                  <DatePicker.RangePicker
                    size="small"
                    style={{ width: '100%' }}
                    value={crawlerFilters.timeRange}
                    onChange={(dates) => setCrawlerFilters(prev => ({ ...prev, timeRange: dates }))}
                  />
                </Col>
                <Col span={8}>
                  <Space>
                    <Button
                      size="small"
                      icon={<FilterOutlined />}
                      onClick={() => {/* 应用过滤器 */}}
                    >
                      应用过滤
                    </Button>
                    <Button
                      size="small"
                      icon={<ClearOutlined />}
                      onClick={() => setCrawlerFilters({ workerId: '', status: '', timeRange: null })}
                    >
                      清除
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col span={6}>
            <Card title="实时监听" size="small">
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Tag color="green" icon={<PlayCircleOutlined />}>
                  自动监听中
                </Tag>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchCrawlerRequestHistory(false)}
                  loading={crawlerLoading}
                  size="small"
                >
                  手动刷新
                </Button>
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                每3秒自动更新数据
              </div>
            </Card>
          </Col>
        </Row>

        {/* 请求列表表格 */}
        <Card title={`爬虫请求记录 (${crawlerRequests.length})`} size="small">
          <Table
            dataSource={crawlerRequests}
            columns={[
              {
                title: '时间',
                dataIndex: 'timestamp',
                key: 'timestamp',
                width: 120,
                render: (timestamp: string) => new Date(timestamp).toLocaleTimeString()
              },
              {
                title: '监控任务ID',
                dataIndex: 'taskId',
                key: 'taskId',
                width: 120,
                render: (taskId: string) => {
                  // UUID格式显示前8位，数字格式显示监控-数字
                  if (taskId && taskId.includes('-')) {
                    return taskId.substring(0, 8);
                  }
                  return taskId ? `监控-${taskId}` : '-';
                }
              },
              {
                title: 'URL',
                dataIndex: 'url',
                key: 'url',
                width: 300,
                render: (url: string) => (
                  <Tooltip title={url}>
                    <Text ellipsis style={{ maxWidth: 280 }}>
                      {url}
                    </Text>
                  </Tooltip>
                )
              },
              {
                title: '指派爬虫Worker',
                dataIndex: 'workerId',
                key: 'workerId',
                width: 150,
                render: (workerId: string) => workerId || 'unknown'
              },
              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                width: 80,
                render: (status: string) => {
                  const colorMap = {
                    pending: 'orange',
                    sent: 'blue',
                    success: 'green',
                    failed: 'red'
                  };
                  return <Tag color={colorMap[status as keyof typeof colorMap]}>{status}</Tag>;
                }
              },
              {
                title: '响应时间',
                dataIndex: ['response', 'responseTime'],
                key: 'responseTime',
                width: 80,
                render: (time: number) => time ? `${time}ms` : '-'
              },
              {
                title: '操作',
                key: 'actions',
                width: 120,
                render: (_: any, record: CrawlerRequest) => (
                  <Space>
                    <Button
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => setSelectedRequest(record)}
                    />
                    <Button
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(record, null, 2));
                        message.success('请求详情已复制到剪贴板');
                      }}
                    />
                  </Space>
                )
              }
            ]}
            rowKey="id"
            size="small"
            pagination={{
              current: crawlerPagination.current,
              pageSize: crawlerPagination.pageSize,
              total: crawlerRequests.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100', '200'],
              onChange: (page, pageSize) => {
                setCrawlerPagination(prev => ({
                  ...prev,
                  current: page,
                  pageSize: pageSize || prev.pageSize
                }));
              },
              onShowSizeChange: (_, size) => {
                setCrawlerPagination(prev => ({
                  ...prev,
                  current: 1, // 改变页面大小时重置到第一页
                  pageSize: size
                }));
              }
            }}
            scroll={{ x: 1200 }}
            loading={crawlerLoading}
          />
        </Card>
      </div>
    );
  }, [crawlerRequests]);

  // 初始化爬虫请求调试数据
  useEffect(() => {
    fetchCrawlerWorkers();
    fetchCrawlerRequestHistory();
  }, [fetchCrawlerWorkers, fetchCrawlerRequestHistory]);

  // 自动刷新爬虫请求数据
  useEffect(() => {
    const interval = setInterval(() => {
      if (!selectedRequest) {
        fetchCrawlerRequestHistory(true);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [selectedRequest, fetchCrawlerRequestHistory]);

  // 请求详情模态框
  const requestDetailModal = useMemo(() => (
    <Modal
      title="请求详情"
      open={!!selectedRequest}
      onCancel={() => setSelectedRequest(null)}
      footer={[
        <Button key="copy" icon={<CopyOutlined />} onClick={() => {
          if (selectedRequest) {
            navigator.clipboard.writeText(JSON.stringify(selectedRequest, null, 2));
            message.success('请求详情已复制到剪贴板');
          }
        }}>
          复制详情
        </Button>,
        <Button key="close" onClick={() => setSelectedRequest(null)}>
          关闭
        </Button>
      ]}
      width={800}
    >
      {selectedRequest && (
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label="请求ID">{selectedRequest.id}</Descriptions.Item>
          <Descriptions.Item label="监控任务ID">
            <div style={{
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              maxWidth: '100%',
              fontSize: '12px',
              fontFamily: 'monospace',
              backgroundColor: '#f5f5f5',
              padding: '4px 8px',
              borderRadius: '4px',
              cursor: 'text',
              userSelect: 'all'
            }}>
              {selectedRequest.taskId}
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="URL">
            <div style={{
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              maxWidth: '100%',
              fontSize: '12px'
            }}>
              {selectedRequest.url}
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="指派Worker">{selectedRequest.workerId}</Descriptions.Item>
          <Descriptions.Item label="Celery Worker">{selectedRequest.celeryWorker || '-'}</Descriptions.Item>
          <Descriptions.Item label="时间">{new Date(selectedRequest.timestamp).toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="方法">{selectedRequest.method}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={
              selectedRequest.status === 'success' ? 'green' :
              selectedRequest.status === 'failed' ? 'red' :
              selectedRequest.status === 'sent' ? 'blue' : 'orange'
            }>
              {selectedRequest.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="请求头">
            <pre style={{
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto',
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              maxWidth: '100%'
            }}>
              {JSON.stringify(selectedRequest.headers, null, 2)}
            </pre>
          </Descriptions.Item>
          <Descriptions.Item label="请求载荷">
            <pre style={{
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto',
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              maxWidth: '100%'
            }}>
              {JSON.stringify(selectedRequest.payload, null, 2)}
            </pre>
          </Descriptions.Item>
          {selectedRequest.response && (
            <Descriptions.Item label="响应">
              <div>
                <p><strong>状态码:</strong> {selectedRequest.response.status}</p>
                <p><strong>响应时间:</strong> {selectedRequest.response.responseTime}ms</p>
                <p><strong>响应数据:</strong></p>
                <pre style={{
                  fontSize: '12px',
                  maxHeight: '300px',
                  overflow: 'auto',
                  wordBreak: 'break-all',
                  whiteSpace: 'pre-wrap',
                  maxWidth: '100%'
                }}>
                  {JSON.stringify(selectedRequest.response.data, null, 2)}
                </pre>
              </div>
            </Descriptions.Item>
          )}
          {selectedRequest.error && (
            <Descriptions.Item label="错误信息">
              <Text type="danger">{selectedRequest.error}</Text>
            </Descriptions.Item>
          )}
        </Descriptions>
      )}
    </Modal>
  ), [selectedRequest]);

  // 内联爬虫请求调试标签项，需要依赖crawlerRequests以便数据更新时重新渲染
  const crawlerRequestTabItem = useMemo(() => {
    return {
      key: 'crawler-requests',
      label: (
        <Space>
          <BugOutlined />
          爬虫请求调试
          <Badge count="🔧" size="small" style={{ backgroundColor: '#faad14' }} />
        </Space>
      ),
      children: <InlineCrawlerRequestPanel />
    };
  }, [InlineCrawlerRequestPanel, crawlerRequests.length]); // 依赖组件和数据长度，确保数据更新时重新创建

  // Celery任务标签项，只有这个会随liveTasks变化
  const celeryTaskTabItem = useMemo(() => ({
    key: 'celery-tasks',
    label: (
      <Space>
        <BarsOutlined />
        Celery任务
        {liveTasks.length > 0 && (
          <Badge count={liveTasks.length} size="small" style={{ backgroundColor: '#1890ff' }} />
        )}
      </Space>
    ),
    children: <CeleryTaskPanel />
  }), [liveTasks.length]);

  // 使用useMemo缓存taskSubTabItems，但CrawlerRequestPanel完全隔离
  const taskSubTabItems = useMemo(() => {
    return [
      celeryTaskTabItem, // 只有Celery任务标签会变化
      crawlerRequestTabItem // 完全隔离的爬虫请求调试标签
    ];
  }, [celeryTaskTabItem, crawlerRequestTabItem]); // crawlerRequestTabItem永远不会变化

  // 实时任务面板（包含子Tab）
  const LiveTaskPanel = () => {

    return (
      <Tabs
        activeKey={taskSubTab}
        onChange={setTaskSubTab}
        items={taskSubTabItems}
        size="small"
      />
    );
  };

  const tabItems = [
    {
      key: 'overview',
      label: (
        <Space>
          <TrophyOutlined />
          系统概览
          {diagnostics.issues.length > 0 && (
            <Badge count={diagnostics.issues.length} size="small" />
          )}
        </Space>
      ),
      children: <OverviewPanel />
    },
    {
      key: 'workers',
      label: (
        <Space>
          <TeamOutlined />
          Worker状态
          <Badge count={workers.filter(w => w.status === 'online').length} size="small" style={{ backgroundColor: '#52c41a' }} />
        </Space>
      ),
      children: <WorkerPanel />
    },
    {
      key: 'tasks',
      label: (
        <Space>
          <BarsOutlined />
          实时任务
          {liveTasks.length > 0 && (
            <Badge count={liveTasks.length} size="small" style={{ backgroundColor: '#1890ff' }} />
          )}
        </Space>
      ),
      children: <LiveTaskPanel />
    }
  ];

  return (
    <div style={{ padding: 24, background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            Celery监控与管理中心
          </Title>
          <Text type="secondary">
            专注于Celery服务状态监控和任务管理
          </Text>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
              type="primary"
            >
              刷新
            </Button>
            <Tooltip title={autoRefresh ? '点击关闭自动刷新' : '点击开启自动刷新'}>
              <Button
                icon={autoRefresh ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => setAutoRefresh(!autoRefresh)}
                type={autoRefresh ? 'default' : 'dashed'}
              >
                {autoRefresh ? '自动刷新' : '手动模式'}
              </Button>
            </Tooltip>
          </Space>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>

      {/* 请求详情模态框 */}
      {requestDetailModal}
    </div>
  );
};

export default CeleryMonitoringV3;
