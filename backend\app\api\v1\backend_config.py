"""
新架构：后端配置API接口
提供后端配置的CRUD操作和健康检查功能
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from datetime import datetime

from ...schemas.backend_config import (
    BackendConfig, BackendConfigCreate, BackendConfigUpdate,
    BackendConfigSummary, BackendConfigStats, BackendHealthCheck,
    BackendStatus, PerformanceLevel
)
from ...services.backend_config_service import backend_config_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/backend-configs", tags=["后端配置管理"])


@router.post("/", response_model=BackendConfig, summary="创建后端配置")
async def create_backend_config(config_data: BackendConfigCreate):
    """
    创建新的后端配置
    
    - **backend_name**: 后端名称（必须唯一）
    - **api_endpoint**: API端点地址（必须唯一）
    - **auth_config**: 认证配置
    - **max_concurrent**: 最大并发数
    - **mean_delay**: 平均延迟(ms)
    - **max_range**: 延迟最大范围(ms)
    - **pool_size**: 线程池大小
    - **memory_threshold**: 内存阈值(MB)
    """
    try:
        logger.info(f"Creating backend config with data: {config_data.dict()}")
        config = await backend_config_service.create_config(config_data)
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create backend config: {e}")
        logger.error(f"Config data that failed: {config_data.dict() if hasattr(config_data, 'dict') else str(config_data)}")
        raise HTTPException(status_code=500, detail=f"创建后端配置失败: {str(e)}")


@router.get("/", response_model=List[BackendConfigSummary], summary="获取后端配置列表")
async def list_backend_configs(
    status: Optional[BackendStatus] = Query(None, description="状态过滤"),
    performance_level: Optional[PerformanceLevel] = Query(None, description="性能级别过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取后端配置列表
    
    支持按状态和性能级别过滤，支持分页
    """
    try:
        configs = await backend_config_service.list_configs(
            status=status,
            performance_level=performance_level,
            limit=limit,
            offset=offset
        )
        return configs
    except Exception as e:
        logger.error(f"Failed to list backend configs: {e}")
        raise HTTPException(status_code=500, detail=f"获取后端配置列表失败: {str(e)}")


@router.get("/available", response_model=List[BackendConfig], summary="获取可用后端")
async def get_available_backends():
    """
    获取所有可用的后端配置
    
    只返回状态为活跃且健康的后端
    """
    try:
        configs = await backend_config_service.get_available_backends()
        return configs
    except Exception as e:
        logger.error(f"Failed to get available backends: {e}")
        raise HTTPException(status_code=500, detail=f"获取可用后端失败: {str(e)}")


@router.get("/{backend_id}", response_model=BackendConfig, summary="获取后端配置详情")
async def get_backend_config(backend_id: str):
    """
    根据ID获取后端配置详情
    """
    try:
        config = await backend_config_service.get_config(backend_id)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get backend config {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取后端配置失败: {str(e)}")


@router.get("/{backend_id}/stats", response_model=BackendConfigStats, summary="获取后端配置统计信息")
async def get_backend_config_stats(backend_id: str):
    """
    获取指定后端配置的统计信息

    返回后端的性能统计、请求统计和健康状态等信息
    """
    try:
        config = await backend_config_service.get_config(backend_id)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")

        # 获取统计信息
        stats = await backend_config_service.get_config_stats(backend_id)
        return stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get backend config stats {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/by-name/{backend_name}", response_model=BackendConfig, summary="根据名称获取后端配置")
async def get_backend_config_by_name(backend_name: str):
    """
    根据名称获取后端配置
    """
    try:
        config = await backend_config_service.get_config_by_name(backend_name)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get backend config by name {backend_name}: {e}")
        raise HTTPException(status_code=500, detail=f"获取后端配置失败: {str(e)}")


@router.get("/by-endpoint/{api_endpoint:path}", response_model=BackendConfig, summary="根据端点获取后端配置")
async def get_backend_config_by_endpoint(api_endpoint: str):
    """
    根据API端点获取后端配置
    """
    try:
        config = await backend_config_service.get_config_by_endpoint(api_endpoint)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get backend config by endpoint {api_endpoint}: {e}")
        raise HTTPException(status_code=500, detail=f"获取后端配置失败: {str(e)}")


@router.put("/{backend_id}", response_model=BackendConfig, summary="更新后端配置")
async def update_backend_config(backend_id: str, update_data: BackendConfigUpdate):
    """
    更新后端配置
    
    只更新提供的字段，其他字段保持不变
    """
    try:
        config = await backend_config_service.update_config(backend_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update backend config {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新后端配置失败: {str(e)}")


@router.delete("/{backend_id}", summary="删除后端配置")
async def delete_backend_config(backend_id: str):
    """
    删除后端配置
    
    注意：如果配置正在被Worker使用，删除会失败
    """
    try:
        success = await backend_config_service.delete_config(backend_id)
        if not success:
            raise HTTPException(status_code=400, detail="删除后端配置失败")
        return {"message": "后端配置删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete backend config {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除后端配置失败: {str(e)}")


@router.post("/{backend_id}/health-check", response_model=BackendHealthCheck, summary="执行健康检查")
async def check_backend_health(backend_id: str, background_tasks: BackgroundTasks):
    """
    执行后端健康检查
    
    检查后端API的可用性和响应时间
    """
    try:
        health_check = await backend_config_service.check_backend_health(backend_id)
        if not health_check:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        return health_check
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to check backend health {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.patch("/{backend_id}/status", response_model=BackendConfig, summary="更新后端状态")
async def update_backend_status(backend_id: str, status: BackendStatus):
    """
    更新后端状态
    
    - **active**: 活跃状态，可以接收任务
    - **inactive**: 非活跃状态，暂停接收任务
    - **maintenance**: 维护状态，正在维护
    - **error**: 错误状态，出现故障
    """
    try:
        update_data = BackendConfigUpdate(status=status)
        config = await backend_config_service.update_config(backend_id, update_data)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update backend status {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")


@router.post("/{backend_id}/test-connection", summary="测试连接")
async def test_backend_connection(backend_id: str):
    """
    测试后端连接
    
    发送测试请求验证连接是否正常
    """
    try:
        config = await backend_config_service.get_config(backend_id)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        
        # 执行健康检查作为连接测试
        health_check = await backend_config_service.check_backend_health(backend_id)
        
        if health_check and health_check.is_healthy:
            return {
                "success": True,
                "message": "连接测试成功",
                "response_time": health_check.response_time,
                "api_version": health_check.api_version
            }
        else:
            return {
                "success": False,
                "message": "连接测试失败",
                "error": health_check.error_message if health_check else "未知错误"
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test backend connection {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"连接测试失败: {str(e)}")


@router.post("/test-connection", summary="测试连接（无需保存配置）")
async def test_connection_without_save(config_data: BackendConfigCreate):
    """
    测试后端连接（无需先保存配置）

    直接使用提供的配置数据测试连接
    """
    try:
        # 创建临时配置对象进行测试
        temp_config = BackendConfig(
            backend_id="temp-test",
            backend_name=config_data.backend_name,
            description=config_data.description,
            api_endpoint=config_data.api_endpoint,
            timeout=config_data.timeout,
            max_retries=config_data.max_retries,
            auth_config=config_data.auth_config,

            # 使用新的配置结构
            performance_config=config_data.performance_config,
            system_auth_config=config_data.system_auth_config,
            callback_config=config_data.callback_config,
            monitoring_config=config_data.monitoring_config,

            weight=config_data.weight,
            priority=config_data.priority,
            performance_level=config_data.performance_level
        )

        # 执行健康检查
        health_check = await backend_config_service._perform_health_check(temp_config)

        if health_check and health_check.is_healthy:
            return {
                "success": True,
                "message": "连接测试成功",
                "response_time": health_check.response_time,
                "api_version": health_check.api_version
            }
        else:
            return {
                "success": False,
                "message": "连接测试失败",
                "error": health_check.error_message if health_check else "未知错误"
            }
    except Exception as e:
        logger.error(f"Failed to test connection: {e}")
        raise HTTPException(status_code=500, detail=f"连接测试失败: {str(e)}")


@router.post("/{backend_id}/duplicate", response_model=BackendConfig, summary="复制后端配置")
async def duplicate_backend_config(
    backend_id: str, 
    new_name: str = Query(..., description="新配置名称"),
    new_endpoint: str = Query(..., description="新API端点")
):
    """
    复制现有后端配置创建新配置
    """
    try:
        # 获取原配置
        original_config = await backend_config_service.get_config(backend_id)
        if not original_config:
            raise HTTPException(status_code=404, detail="原配置不存在")
        
        # 创建复制的配置数据
        duplicate_data = BackendConfigCreate(
            backend_name=new_name,
            description=f"复制自 {original_config.backend_name}",
            api_endpoint=new_endpoint,
            timeout=original_config.timeout,
            max_retries=original_config.max_retries,
            auth_config=original_config.auth_config,
            max_concurrent=original_config.max_concurrent,
            mean_delay=original_config.mean_delay,
            max_range=original_config.max_range,
            pool_size=original_config.pool_size,
            memory_threshold=original_config.memory_threshold,
            weight=original_config.weight,
            priority=original_config.priority,
            performance_level=original_config.performance_level,
            health_check_interval=original_config.health_check_interval,
            health_check_timeout=original_config.health_check_timeout,
            failure_threshold=original_config.failure_threshold,
            recovery_threshold=original_config.recovery_threshold
        )
        
        # 创建新配置
        new_config = await backend_config_service.create_config(duplicate_data)
        return new_config
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to duplicate backend config {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"复制配置失败: {str(e)}")


@router.get("/{backend_id}/performance-metrics", summary="获取性能指标")
async def get_backend_performance_metrics(backend_id: str):
    """
    获取后端性能指标
    
    包括响应时间、成功率、负载等信息
    """
    try:
        config = await backend_config_service.get_config(backend_id)
        if not config:
            raise HTTPException(status_code=404, detail="后端配置不存在")
        
        metrics = {
            "backend_id": backend_id,
            "backend_name": config.backend_name,
            "status": config.status,
            "health_score": config.health_score,
            "success_rate": config.success_rate,
            "avg_response_time": config.avg_response_time,
            "current_load": config.current_load,
            "max_concurrent": config.max_concurrent,
            "available_capacity": config.available_capacity,
            "total_requests": config.total_requests,
            "success_requests": config.success_requests,
            "failed_requests": config.failed_requests,
            "last_health_check": config.last_health_check,
            "performance_level": config.performance_level,
            "delay_range": config.delay_range
        }
        
        return metrics
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get performance metrics {backend_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")
