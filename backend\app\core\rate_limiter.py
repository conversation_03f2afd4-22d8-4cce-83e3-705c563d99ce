"""
限流控制器

严格控制对外部爬虫API的调用频率和并发数量：
- 最多2个URL并行处理
- 控制API调用频率
- 熔断机制和降级策略
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import redis.asyncio as redis

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class RateLimitConfig:
    """限流配置"""
    max_concurrent_requests: int = 2      # 最大并发请求数
    requests_per_minute: int = 60         # 每分钟最大请求数
    requests_per_hour: int = 1000         # 每小时最大请求数
    burst_size: int = 5                   # 突发请求大小
    
    # 熔断器配置
    failure_threshold: int = 5            # 失败阈值
    failure_rate_threshold: float = 0.5   # 失败率阈值
    recovery_timeout: int = 60            # 恢复超时时间（秒）
    half_open_max_calls: int = 3          # 半开状态最大调用数
    
    # Redis配置
    redis_url: str = "redis://redis:6379/0"
    key_prefix: str = "rate_limiter"


@dataclass
class RequestMetrics:
    """请求指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    current_concurrent: int = 0


class RateLimiter:
    """限流控制器"""
    
    def __init__(self, config: Optional[RateLimitConfig] = None):
        """初始化限流控制器
        
        Args:
            config: 限流配置
        """
        self.config = config or RateLimitConfig()
        self.logger = logging.getLogger(__name__)
        
        # Redis连接
        self.redis: Optional[redis.Redis] = None
        
        # 本地状态
        self.current_concurrent = 0
        self.concurrent_lock = asyncio.Lock()
        
        # 熔断器状态
        self.circuit_state = CircuitState.CLOSED
        self.circuit_opened_at: Optional[datetime] = None
        self.half_open_calls = 0
        
        # 请求指标
        self.metrics = RequestMetrics()
        
        # 请求历史（用于计算失败率）
        self.request_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
    async def start(self) -> None:
        """启动限流器"""
        try:
            self.redis = redis.from_url(self.config.redis_url)
            await self.redis.ping()
            self.logger.info("Rate limiter started")
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def stop(self) -> None:
        """停止限流器"""
        if self.redis:
            await self.redis.close()
            self.logger.info("Rate limiter stopped")
    
    async def acquire(self, request_id: str) -> bool:
        """获取请求许可
        
        Args:
            request_id: 请求ID
            
        Returns:
            bool: 是否获得许可
        """
        # 检查熔断器状态
        if not await self._check_circuit_breaker():
            self.logger.warning(f"Request {request_id} rejected by circuit breaker")
            return False
        
        # 检查并发限制
        async with self.concurrent_lock:
            if self.current_concurrent >= self.config.max_concurrent_requests:
                self.logger.warning(
                    f"Request {request_id} rejected: concurrent limit reached "
                    f"({self.current_concurrent}/{self.config.max_concurrent_requests})"
                )
                return False
            
            # 检查频率限制
            if not await self._check_rate_limits(request_id):
                return False
            
            # 获得许可
            self.current_concurrent += 1
            await self._record_request_start(request_id)
            
            self.logger.debug(
                f"Request {request_id} acquired permit "
                f"(concurrent: {self.current_concurrent}/{self.config.max_concurrent_requests})"
            )
            
            return True
    
    async def release(self, request_id: str, success: bool = True, response_time: float = 0.0) -> None:
        """释放请求许可
        
        Args:
            request_id: 请求ID
            success: 是否成功
            response_time: 响应时间
        """
        async with self.concurrent_lock:
            if self.current_concurrent > 0:
                self.current_concurrent -= 1
            
            await self._record_request_end(request_id, success, response_time)
            
            self.logger.debug(
                f"Request {request_id} released permit "
                f"(concurrent: {self.current_concurrent}/{self.config.max_concurrent_requests})"
            )
    
    async def _check_circuit_breaker(self) -> bool:
        """检查熔断器状态
        
        Returns:
            bool: 是否允许请求
        """
        now = datetime.now()
        
        if self.circuit_state == CircuitState.OPEN:
            # 检查是否可以进入半开状态
            if (self.circuit_opened_at and 
                (now - self.circuit_opened_at).total_seconds() >= self.config.recovery_timeout):
                self.circuit_state = CircuitState.HALF_OPEN
                self.half_open_calls = 0
                self.logger.info("Circuit breaker entering HALF_OPEN state")
            else:
                return False
        
        elif self.circuit_state == CircuitState.HALF_OPEN:
            # 半开状态限制调用数量
            if self.half_open_calls >= self.config.half_open_max_calls:
                return False
        
        return True
    
    async def _check_rate_limits(self, request_id: str) -> bool:
        """检查频率限制
        
        Args:
            request_id: 请求ID
            
        Returns:
            bool: 是否通过频率限制
        """
        if not self.redis:
            return True
        
        now = int(time.time())
        
        try:
            # 检查每分钟限制
            minute_key = f"{self.config.key_prefix}:minute:{now // 60}"
            minute_count = await self.redis.incr(minute_key)
            await self.redis.expire(minute_key, 60)
            
            if minute_count > self.config.requests_per_minute:
                self.logger.warning(
                    f"Request {request_id} rejected: minute rate limit exceeded "
                    f"({minute_count}/{self.config.requests_per_minute})"
                )
                return False
            
            # 检查每小时限制
            hour_key = f"{self.config.key_prefix}:hour:{now // 3600}"
            hour_count = await self.redis.incr(hour_key)
            await self.redis.expire(hour_key, 3600)
            
            if hour_count > self.config.requests_per_hour:
                self.logger.warning(
                    f"Request {request_id} rejected: hour rate limit exceeded "
                    f"({hour_count}/{self.config.requests_per_hour})"
                )
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to check rate limits: {e}")
            # Redis故障时允许请求通过，但记录错误
            return True
    
    async def _record_request_start(self, request_id: str) -> None:
        """记录请求开始
        
        Args:
            request_id: 请求ID
        """
        self.metrics.total_requests += 1
        self.metrics.current_concurrent = self.current_concurrent
        self.metrics.last_request_time = datetime.now()
        
        # 记录到Redis
        if self.redis:
            try:
                await self.redis.hset(
                    f"{self.config.key_prefix}:requests:{request_id}",
                    mapping={
                        "start_time": time.time(),
                        "status": "running"
                    }
                )
                await self.redis.expire(
                    f"{self.config.key_prefix}:requests:{request_id}",
                    3600  # 1小时过期
                )
            except Exception as e:
                self.logger.error(f"Failed to record request start: {e}")
    
    async def _record_request_end(
        self,
        request_id: str,
        success: bool,
        response_time: float
    ) -> None:
        """记录请求结束
        
        Args:
            request_id: 请求ID
            success: 是否成功
            response_time: 响应时间
        """
        # 更新本地指标
        if success:
            self.metrics.successful_requests += 1
        else:
            self.metrics.failed_requests += 1
        
        # 更新平均响应时间
        total_successful = self.metrics.successful_requests
        if total_successful > 0:
            self.metrics.avg_response_time = (
                (self.metrics.avg_response_time * (total_successful - 1) + response_time) /
                total_successful
            )
        
        # 记录请求历史
        request_record = {
            "request_id": request_id,
            "success": success,
            "response_time": response_time,
            "timestamp": datetime.now()
        }
        
        self.request_history.append(request_record)
        if len(self.request_history) > self.max_history_size:
            self.request_history.pop(0)
        
        # 更新熔断器状态
        await self._update_circuit_breaker(success)
        
        # 记录到Redis
        if self.redis:
            try:
                await self.redis.hset(
                    f"{self.config.key_prefix}:requests:{request_id}",
                    mapping={
                        "end_time": time.time(),
                        "success": success,
                        "response_time": response_time,
                        "status": "completed"
                    }
                )
            except Exception as e:
                self.logger.error(f"Failed to record request end: {e}")
    
    async def _update_circuit_breaker(self, success: bool) -> None:
        """更新熔断器状态
        
        Args:
            success: 请求是否成功
        """
        if self.circuit_state == CircuitState.HALF_OPEN:
            self.half_open_calls += 1
            
            if success:
                # 半开状态下成功，检查是否可以关闭熔断器
                if self.half_open_calls >= self.config.half_open_max_calls:
                    self.circuit_state = CircuitState.CLOSED
                    self.logger.info("Circuit breaker closed")
            else:
                # 半开状态下失败，重新打开熔断器
                self.circuit_state = CircuitState.OPEN
                self.circuit_opened_at = datetime.now()
                self.logger.warning("Circuit breaker re-opened")
        
        elif self.circuit_state == CircuitState.CLOSED:
            # 检查是否需要打开熔断器
            if await self._should_open_circuit():
                self.circuit_state = CircuitState.OPEN
                self.circuit_opened_at = datetime.now()
                self.logger.warning("Circuit breaker opened")
    
    async def _should_open_circuit(self) -> bool:
        """检查是否应该打开熔断器
        
        Returns:
            bool: 是否应该打开熔断器
        """
        if len(self.request_history) < self.config.failure_threshold:
            return False
        
        # 检查最近的请求
        recent_requests = self.request_history[-self.config.failure_threshold:]
        failed_count = sum(1 for req in recent_requests if not req["success"])
        
        failure_rate = failed_count / len(recent_requests)
        
        return failure_rate >= self.config.failure_rate_threshold
    
    async def get_current_limits(self) -> Dict[str, Any]:
        """获取当前限制状态
        
        Returns:
            Dict[str, Any]: 当前限制状态
        """
        if not self.redis:
            return {}
        
        now = int(time.time())
        
        try:
            minute_key = f"{self.config.key_prefix}:minute:{now // 60}"
            hour_key = f"{self.config.key_prefix}:hour:{now // 3600}"
            
            minute_count = await self.redis.get(minute_key) or 0
            hour_count = await self.redis.get(hour_key) or 0
            
            return {
                "concurrent": {
                    "current": self.current_concurrent,
                    "max": self.config.max_concurrent_requests
                },
                "minute": {
                    "current": int(minute_count),
                    "max": self.config.requests_per_minute
                },
                "hour": {
                    "current": int(hour_count),
                    "max": self.config.requests_per_hour
                },
                "circuit_breaker": {
                    "state": self.circuit_state.value,
                    "opened_at": self.circuit_opened_at.isoformat() if self.circuit_opened_at else None
                }
            }
        except Exception as e:
            self.logger.error(f"Failed to get current limits: {e}")
            return {}
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标信息
        
        Returns:
            Dict[str, Any]: 指标信息
        """
        success_rate = 0.0
        if self.metrics.total_requests > 0:
            success_rate = self.metrics.successful_requests / self.metrics.total_requests
        
        return {
            "total_requests": self.metrics.total_requests,
            "successful_requests": self.metrics.successful_requests,
            "failed_requests": self.metrics.failed_requests,
            "success_rate": success_rate,
            "avg_response_time": self.metrics.avg_response_time,
            "current_concurrent": self.current_concurrent,
            "circuit_state": self.circuit_state.value,
            "last_request_time": (
                self.metrics.last_request_time.isoformat() 
                if self.metrics.last_request_time else None
            )
        }
