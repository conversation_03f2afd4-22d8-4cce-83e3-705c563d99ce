#!/usr/bin/env python3
"""
测试前端API调用兼容性

验证前端调用的所有API端点都正常工作
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_frontend_api_compatibility():
    """测试前端API调用兼容性"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试前端API调用兼容性")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/frontend-api-test",
            "platform": "mercadolibre",
            "title": "前端API测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'frontend_api_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'frontend_api_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:frontend_api_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 测试任务创建API (前端taskCreateApi.ts使用)
        print(f"\n📋 步骤2: 测试任务创建API (/api/v1/tasks/create-from-urls)")
        
        create_data = {
            "name": "前端API兼容性测试任务",
            "description": "用于测试前端API兼容性的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "13:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建API正常: {task_id}")
        else:
            print(f"  ❌ 任务创建API失败: {create_response.text}")
            return
        
        # 步骤3: 测试监控任务API (前端monitoringTaskApi.ts使用)
        print(f"\n📋 步骤3: 测试监控任务API")
        
        # 3.1 任务列表API
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        if list_response.status_code == 200:
            list_result = list_response.json()
            tasks = list_result.get('data', [])
            found_task = any(t.get('id') == task_id for t in tasks)
            print(f"  ✅ 任务列表API (/api/v1/monitoring-tasks/): {'找到任务' if found_task else '未找到任务'}")
        else:
            print(f"  ❌ 任务列表API失败: {list_response.text}")
        
        # 3.2 任务详情API
        detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
        if detail_response.status_code == 200:
            detail_result = detail_response.json()
            task_data = detail_result.get('data', {})
            print(f"  ✅ 任务详情API (/api/v1/monitoring-tasks/{{task_id}}): URL数量 {task_data.get('total_urls', 0)}")
        else:
            print(f"  ❌ 任务详情API失败: {detail_response.text}")
        
        # 3.3 任务URL列表API
        urls_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}/urls", timeout=10)
        if urls_response.status_code == 200:
            urls_result = urls_response.json()
            task_urls = urls_result.get('data', [])
            print(f"  ✅ 任务URL列表API (/api/v1/monitoring-tasks/{{task_id}}/urls): 返回 {len(task_urls)} 个URL")
        else:
            print(f"  ❌ 任务URL列表API失败: {urls_response.text}")
        
        # 步骤4: 测试任务删除API (前端taskCreateApi.ts使用)
        print(f"\n📋 步骤4: 测试任务删除API")
        
        # 4.1 使用taskCreateApi.ts的删除端点
        delete_response1 = requests.delete(f"{base_url}/api/v1/tasks/{task_id}", timeout=10)
        if delete_response1.status_code in [200, 204]:
            print(f"  ✅ taskCreateApi删除API (/api/v1/tasks/{{task_id}}): 正常")
            task_ids.remove(task_id)  # 已删除，从列表中移除
        else:
            print(f"  ❌ taskCreateApi删除API失败: {delete_response1.text}")
            
            # 如果第一个删除失败，尝试使用监控任务删除API
            delete_response2 = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if delete_response2.status_code in [200, 204]:
                print(f"  ✅ monitoringTaskApi删除API (/api/v1/monitoring-tasks/{{task_id}}): 正常")
                task_ids.remove(task_id)
            else:
                print(f"  ❌ monitoringTaskApi删除API也失败: {delete_response2.text}")
        
        # 步骤5: 测试URL预览API (前端taskCreateApi.ts使用)
        print(f"\n📋 步骤5: 测试URL预览API")
        
        preview_data = {
            "url_ids": [url_id]
        }
        
        preview_response = requests.post(
            f"{base_url}/api/v1/tasks/preview-urls",
            json=preview_data,
            timeout=10
        )
        
        if preview_response.status_code == 200:
            preview_result = preview_response.json()
            print(f"  ✅ URL预览API (/api/v1/tasks/preview-urls): 返回 {preview_result.get('valid_count', 0)} 个有效URL")
        else:
            print(f"  ❌ URL预览API失败: {preview_response.text}")
        
        # 步骤6: 测试任务验证API (前端taskCreateApi.ts使用)
        print(f"\n📋 步骤6: 测试任务验证API")
        
        validation_data = {
            "name": "验证测试任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "14:00",
                "timezone": "Asia/Shanghai"
            }
        }
        
        validation_response = requests.post(
            f"{base_url}/api/v1/tasks/validate",
            json=validation_data,
            timeout=10
        )
        
        if validation_response.status_code == 200:
            validation_result = validation_response.json()
            print(f"  ✅ 任务验证API (/api/v1/tasks/validate): 验证{'通过' if validation_result.get('success') else '失败'}")
        else:
            print(f"  ❌ 任务验证API失败: {validation_response.text}")
        
        print(f"\n🎉 前端API兼容性测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理剩余的测试任务
        for task_id in task_ids:
            try:
                # 尝试两个删除端点
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:frontend_api_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_frontend_api_compatibility()
