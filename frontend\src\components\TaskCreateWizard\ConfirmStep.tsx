import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Typography,
  Tag,
  Alert,
  Divider,
  Row,
  Col,
  Statistic,
  List,
  Spin
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

import type { TaskCreateWizardData, TaskValidationResponse } from '../../types/taskCreate';
import { validateTaskCreation, generateScheduleDescription, getPriorityDisplay } from '../../services/taskCreateApi';

const { Title, Text } = Typography;

interface ConfirmStepProps {
  wizardData: TaskCreateWizardData;
  loading: boolean;
}

const ConfirmStep: React.FC<ConfirmStepProps> = ({
  wizardData,
  loading
}) => {
  const [validationResult, setValidationResult] = useState<TaskValidationResponse | null>(null);
  const [validating, setValidating] = useState(false);

  // 安全地转换项目为字符串
  const safeStringify = (item: any): string => {
    if (typeof item === 'string') {
      return item;
    }
    if (typeof item === 'object' && item !== null) {
      // 如果是Pydantic验证错误对象
      if (item.msg && item.type) {
        return `${item.msg} (${item.type})`;
      }
      // 其他对象，尝试JSON序列化
      try {
        return JSON.stringify(item);
      } catch {
        return String(item);
      }
    }
    return String(item);
  };

  // 验证任务配置
  useEffect(() => {
    const validateConfig = async () => {
      setValidating(true);
      try {
        const requestData = {
          name: wizardData.basicConfig.name,
          url_ids: wizardData.selectedUrls,
          schedule: wizardData.scheduleConfig
        };

        // 添加详细的调试日志
        console.log('🔍 ConfirmStep - 发送验证请求:', {
          requestData: JSON.stringify(requestData, null, 2),
          basicConfig: wizardData.basicConfig,
          selectedUrls: wizardData.selectedUrls,
          scheduleConfig: wizardData.scheduleConfig
        });

        const result = await validateTaskCreation(requestData);
        setValidationResult(result);
      } catch (error: any) {
        console.error('验证失败:', error);
        console.error('错误详情:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          config: error.config
        });
        // 处理验证错误，创建一个失败的验证结果
        const errorMessage = error.response?.data?.detail || error.message || '验证请求失败';
        setValidationResult({
          success: false,
          errors: [errorMessage],
          warnings: [],
          suggestions: []
        });
      } finally {
        setValidating(false);
      }
    };

    validateConfig();
  }, [wizardData]);

  // 渲染验证结果
  const renderValidationResult = () => {
    if (validating) {
      return (
        <Card size="small" style={{ marginBottom: 16 }}>
          <Spin>
            <div style={{ padding: 20, textAlign: 'center' }}>
              正在验证配置...
            </div>
          </Spin>
        </Card>
      );
    }

    if (!validationResult) {
      return null;
    }

    const { success, errors, warnings, suggestions } = validationResult;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={5}>配置验证</Title>
        
        {success ? (
          <Alert
            message="配置验证通过"
            description="所有配置项都符合要求，可以创建任务"
            type="success"
            showIcon
            icon={<CheckCircleOutlined />}
            style={{ marginBottom: 16 }}
          />
        ) : (
          <Alert
            message="配置验证失败"
            description="发现配置错误，请返回修改"
            type="error"
            showIcon
            icon={<ExclamationCircleOutlined />}
            style={{ marginBottom: 16 }}
          />
        )}

        {errors.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Text strong style={{ color: '#f5222d' }}>错误:</Text>
            <List
              size="small"
              dataSource={errors}
              renderItem={item => (
                <List.Item>
                  <ExclamationCircleOutlined style={{ color: '#f5222d', marginRight: 8 }} />
                  {safeStringify(item)}
                </List.Item>
              )}
            />
          </div>
        )}

        {warnings.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Text strong style={{ color: '#fa8c16' }}>警告:</Text>
            <List
              size="small"
              dataSource={warnings}
              renderItem={item => (
                <List.Item>
                  <ExclamationCircleOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                  {safeStringify(item)}
                </List.Item>
              )}
            />
          </div>
        )}

        {suggestions.length > 0 && (
          <div>
            <Text strong style={{ color: '#1890ff' }}>建议:</Text>
            <List
              size="small"
              dataSource={suggestions}
              renderItem={item => (
                <List.Item>
                  <InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                  {safeStringify(item)}
                </List.Item>
              )}
            />
          </div>
        )}
      </Card>
    );
  };

  // 渲染任务概览
  const renderTaskOverview = () => {
    const priorityDisplay = getPriorityDisplay(wizardData.basicConfig.priority);
    const scheduleDescription = generateScheduleDescription(wizardData.scheduleConfig);

    return (
      <Card title="任务概览" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="选择的URL"
              value={wizardData.selectedUrls.length}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="目标平台"
              value={wizardData.basicConfig.platform === 'all' ? '所有平台' : wizardData.basicConfig.platform}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="任务优先级"
              value={priorityDisplay.name}
              valueStyle={{ color: priorityDisplay.color }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="调度状态"
              value={wizardData.scheduleConfig.enabled ? '启用' : '禁用'}
              valueStyle={{ 
                color: wizardData.scheduleConfig.enabled ? '#3f8600' : '#cf1322' 
              }}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染配置详情
  const renderConfigDetails = () => (
    <Card title="配置详情" size="small" style={{ marginBottom: 16 }}>
      <Descriptions column={2} size="small">
        <Descriptions.Item label="任务名称">
          {wizardData.basicConfig.name}
        </Descriptions.Item>
        <Descriptions.Item label="任务描述">
          {wizardData.basicConfig.description || '无'}
        </Descriptions.Item>
        <Descriptions.Item label="目标平台">
          <Tag color="blue">
            {wizardData.basicConfig.platform === 'all' ? '所有平台' : wizardData.basicConfig.platform}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="优先级">
          <Tag color={getPriorityDisplay(wizardData.basicConfig.priority).color}>
            {getPriorityDisplay(wizardData.basicConfig.priority).name}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="标签">
          {wizardData.basicConfig.tags.length > 0 ? (
            wizardData.basicConfig.tags.map(tag => (
              <Tag key={tag}>{tag}</Tag>
            ))
          ) : '无'}
        </Descriptions.Item>

        <Descriptions.Item label="启用通知">
          {wizardData.taskConfig.enable_notifications ? '是' : '否'}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <Title level={5}>
        <ClockCircleOutlined /> 调度配置
      </Title>
      <Descriptions column={2} size="small">
        <Descriptions.Item label="调度类型">
          {wizardData.scheduleConfig.type}
        </Descriptions.Item>
        <Descriptions.Item label="调度状态">
          <Tag color={wizardData.scheduleConfig.enabled ? 'green' : 'red'}>
            {wizardData.scheduleConfig.enabled ? '启用' : '禁用'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="调度描述" span={2}>
          {generateScheduleDescription(wizardData.scheduleConfig)}
        </Descriptions.Item>
        {process.env.NODE_ENV === 'development' && (
          <Descriptions.Item label="调试信息" span={2}>
            <div style={{ fontSize: '12px', fontFamily: 'monospace', color: '#666' }}>
              <div>time: "{wizardData.scheduleConfig.time}"</div>
              <div>start_time: "{wizardData.scheduleConfig.start_time}"</div>
              <div>end_time: "{wizardData.scheduleConfig.end_time}"</div>
              <div>type: "{wizardData.scheduleConfig.type}"</div>
              <div>enable_random_delay: {String(wizardData.scheduleConfig.enable_random_delay)}</div>
              <div>random_delay_min: {wizardData.scheduleConfig.random_delay_min}</div>
              <div>random_delay_max: {wizardData.scheduleConfig.random_delay_max}</div>
            </div>
          </Descriptions.Item>
        )}
        {(() => {
          // 获取执行时间，优先使用time字段（daily类型），其次使用start_time字段
          const executionTime = wizardData.scheduleConfig.time || wizardData.scheduleConfig.start_time;

          if (!executionTime) return null;

          return (
            <Descriptions.Item label="执行时间">
              {wizardData.scheduleConfig.time
                ? wizardData.scheduleConfig.time
                : (wizardData.scheduleConfig.start_time &&
                   !isNaN(new Date(wizardData.scheduleConfig.start_time).getTime())
                     ? new Date(wizardData.scheduleConfig.start_time).toLocaleString()
                     : wizardData.scheduleConfig.start_time)}
            </Descriptions.Item>
          );
        })()}
        {wizardData.scheduleConfig.end_time && (
          <Descriptions.Item label="结束时间">
            {(() => {
              const endTime = wizardData.scheduleConfig.end_time;
              // 如果是HH:mm格式，直接显示
              if (typeof endTime === 'string' && /^\d{2}:\d{2}$/.test(endTime)) {
                return endTime;
              }
              // 如果是完整的日期时间，格式化显示
              if (!isNaN(new Date(endTime).getTime())) {
                return new Date(endTime).toLocaleString();
              }
              // 其他情况直接显示
              return endTime;
            })()}
          </Descriptions.Item>
        )}
        {wizardData.scheduleConfig.interval && (
          <Descriptions.Item label="执行间隔">
            {wizardData.scheduleConfig.interval}分钟
          </Descriptions.Item>
        )}
        {wizardData.scheduleConfig.max_runs && (
          <Descriptions.Item label="最大执行次数">
            {wizardData.scheduleConfig.max_runs}
          </Descriptions.Item>
        )}
        <Descriptions.Item label="时区">
          {wizardData.scheduleConfig.timezone}
        </Descriptions.Item>

        {/* 反爬虫配置显示 */}
        <Descriptions.Item label="随机延迟">
          <Tag color={wizardData.scheduleConfig.enable_random_delay ? 'green' : 'default'}>
            {wizardData.scheduleConfig.enable_random_delay ? '已启用' : '已禁用'}
          </Tag>
        </Descriptions.Item>
        {wizardData.scheduleConfig.enable_random_delay && (
          <Descriptions.Item label="延迟范围">
            {wizardData.scheduleConfig.random_delay_min || 0}-{wizardData.scheduleConfig.random_delay_max || 180}分钟
            {(wizardData.scheduleConfig.random_delay_max || 180) > 60 && (
              <span style={{ color: '#666', marginLeft: 8 }}>
                (约{Math.floor((wizardData.scheduleConfig.random_delay_min || 0)/60)}-{Math.floor((wizardData.scheduleConfig.random_delay_max || 180)/60)}小时)
              </span>
            )}
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );

  const canConfirm = validationResult?.success && !validating;

  return (
    <div>
      <Title level={4}>确认创建</Title>
      <Text type="secondary">请确认以下配置信息，确认无误后点击创建任务</Text>

      <div style={{ marginTop: 24 }}>
        {renderValidationResult()}
        {renderTaskOverview()}
        {renderConfigDetails()}
      </div>
    </div>
  );
};

export default ConfirmStep;
