# 爬虫配置更新文档

## 概述

根据 `LinksToMonit/crawlerstuff/API_request/` 目录下的API请求示例，已将爬虫配置的默认选项更新为更完整和实用的配置。

## 更新内容

### 1. 后端配置更新 (`backend/app/api/v1/crawler_config.py`)

#### 新增配置类

1. **SchemaExtractionConfig** - Schema提取配置
   - `extraction_schema`: 提取数据的JSON Schema定义
   - `instructions`: 提取指令
   - `validate_schema`: 是否验证Schema
   - `return_raw`: 是否返回原始数据

2. **ContentProcessingConfig** - 内容处理配置
   - `word_count_threshold`: 词数阈值
   - `css_selector`: CSS选择器
   - `target_elements`: 目标元素列表
   - `excluded_tags`: 排除的HTML标签
   - `excluded_selector`: 排除的CSS选择器
   - `remove_forms`: 是否移除表单
   - `only_text`: 是否只提取文本
   - `prettify`: 是否美化HTML
   - `parser_type`: 解析器类型
   - `keep_data_attributes`: 是否保留数据属性
   - `keep_attrs`: 保留的属性列表

3. **LinkFilteringConfig** - 链接过滤配置
   - `exclude_external_links`: 是否排除外部链接
   - `exclude_internal_links`: 是否排除内部链接
   - `exclude_social_media_links`: 是否排除社交媒体链接
   - `exclude_domains`: 排除的域名列表
   - `social_media_domains`: 社交媒体域名列表
   - `exclude_external_images`: 是否排除外部图片
   - `exclude_all_images`: 是否排除所有图片
   - `image_score_threshold`: 图片评分阈值
   - `image_description_min_word_threshold`: 图片描述最小词数阈值
   - `table_score_threshold`: 表格评分阈值

#### 更新现有配置类

1. **CrawlerConfig** - 新增字段
   - `check_robots_txt`: 是否检查robots.txt
   - `fetch_ssl_certificate`: 是否获取SSL证书
   - `extraction_strategy`: 提取策略
   - `chunking_strategy`: 分块策略
   - `markdown_generator`: Markdown生成器
   - `bypass_cache`: 是否绕过缓存

2. **LLMConfig** - 新增字段
   - `query`: LLM查询语句

#### 默认配置更新

- **API密钥**: 使用示例中的有效API密钥
- **Schema配置**: 包含完整的电商商品信息提取Schema
- **提取指令**: 详细的商品信息提取指令
- **内容处理**: 合理的默认处理参数
- **链接过滤**: 常见的过滤规则

### 2. 前端配置更新 (`frontend/src/services/crawlerConfigService.ts`)

#### 更新TypeScript接口

1. 新增 `SchemaExtractionConfig` 接口
2. 新增 `ContentProcessingConfig` 接口  
3. 新增 `LinkFilteringConfig` 接口
4. 更新 `CrawlerConfig` 接口，添加新字段
5. 更新 `LLMConfig` 接口，添加query字段
6. 更新 `CrawlerFullConfig` 接口，包含新的配置项

#### 更新默认配置

- 同步后端的默认配置值
- 确保前后端配置一致性

### 3. API请求构建更新

更新 `build_crawl4ai_request` 方法，包含所有新的配置项：

- `schema_extraction`: Schema提取配置
- `content_processing`: 内容处理配置
- `link_filtering`: 链接过滤配置
- `_config_summary`: 配置摘要信息

## 主要特性

### 1. 电商商品信息提取

默认Schema包含完整的电商商品信息结构：

- **任务信息**: 时间戳等执行信息
- **商品基础信息**: URL、名称、ID等
- **价格信息**: 现价、原价、折扣、库存等
- **分类信息**: 商品目录层级结构
- **销售商信息**: 销售商名称和链接
- **媒体信息**: 商品图片链接
- **问答区域**: 商品相关问答
- **评分信息**: 商品评分和评价数量
- **评论区域**: 用户评论内容

### 2. 智能内容处理

- 自动排除导航、页脚等无关内容
- 支持CSS选择器精确定位
- 可配置的词数和评分阈值
- 灵活的HTML解析选项

### 3. 链接过滤

- 自动过滤广告和社交媒体链接
- 可配置的域名黑名单
- 图片和表格内容的智能评分

## 配置兼容性

- 保持向后兼容性
- 新配置项都有合理的默认值
- 支持渐进式配置更新

## 使用方法

### 后端

```python
from backend.app.api.v1.crawler_config import config_service

# 获取默认配置
config = await config_service.get_config()

# 构建API请求
request_data = config_service.build_crawl4ai_request(config, urls)
```

### 前端

```typescript
import { CrawlerConfigService } from '@/services/crawlerConfigService';

const configService = new CrawlerConfigService();

// 获取配置
const config = await configService.getConfig();

// 更新配置
await configService.updateConfig(newConfig);
```

## 测试验证

已通过完整的测试验证：

- ✅ 配置类创建和验证
- ✅ API请求构建
- ✅ 前后端配置一致性
- ✅ 默认值正确性
- ✅ Schema验证通过

## 注意事项

1. **API密钥安全**: 生产环境中请更换为安全的API密钥
2. **Schema定制**: 可根据具体需求调整提取Schema
3. **性能优化**: 根据实际使用情况调整并发和超时参数
4. **域名过滤**: 根据目标网站调整过滤规则

## 后续优化建议

1. 添加配置预设模板（高性能、高质量等）
2. 支持动态Schema生成
3. 添加配置验证和错误提示
4. 实现配置版本管理
5. 添加配置导入导出功能
