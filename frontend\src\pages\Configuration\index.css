.configuration {
  padding: 24px;
}

.configuration-header {
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.configuration-header h2 {
  margin-bottom: 8px;
  color: #1a1a1a;
  font-weight: 600;
}

.configuration-header .ant-typography {
  color: #666666;
  font-size: 16px;
}

/* 配置卡片样式 */
.config-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.config-card .ant-card-head {
  border-bottom: 1px solid #f5f5f5;
}

.config-card .ant-card-head-title {
  color: #1a1a1a;
  font-weight: 600;
}

/* 爬虫配置区域 */
.crawler-config-section {
  margin-bottom: 32px;
}

.config-form {
  max-width: 800px;
}

.config-form .ant-form-item-label > label {
  font-weight: 500;
  color: #333333;
}

.config-form .ant-form-item-explain {
  color: #666666;
  font-size: 12px;
}

/* 选择器配置编辑器 */
.selector-editor {
  margin-bottom: 24px;
}

.selector-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.selector-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.selector-item.active {
  border-color: #1890ff;
  background: #e6f7ff;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selector-title {
  font-weight: 600;
  color: #1a1a1a;
}

.selector-actions {
  display: flex;
  gap: 8px;
}

.selector-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.selector-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.selector-label {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

.selector-input {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 代理池管理 */
.proxy-management {
  margin-bottom: 32px;
}

.proxy-list {
  max-height: 400px;
  overflow-y: auto;
}

.proxy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.proxy-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.proxy-info {
  flex: 1;
}

.proxy-url {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.proxy-meta {
  font-size: 12px;
  color: #666666;
}

.proxy-status {
  margin-right: 12px;
}

.proxy-status.online {
  color: #52c41a;
}

.proxy-status.offline {
  color: #ff4d4f;
}

.proxy-status.testing {
  color: #faad14;
}

.proxy-actions {
  display: flex;
  gap: 8px;
}

/* 系统参数设置 */
.system-params {
  margin-bottom: 32px;
}

.param-section {
  margin-bottom: 24px;
}

.param-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.param-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-label {
  font-weight: 500;
  color: #333333;
}

.param-description {
  font-size: 12px;
  color: #666666;
  margin-top: 4px;
}

/* 配置模板管理 */
.template-management {
  margin-bottom: 32px;
}

.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.template-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background: #ffffff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.template-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.template-card.selected {
  border-color: #1890ff;
  background: #e6f7ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-name {
  font-weight: 600;
  color: #1a1a1a;
}

.template-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background: #f0f0f0;
  color: #666666;
}

.template-description {
  font-size: 14px;
  color: #666666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999999;
}

.template-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

/* JSON编辑器样式 */
.json-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.json-editor .ant-input {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

/* 测试结果显示 */
.test-result {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.test-result.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #389e0d;
}

.test-result.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #cf1322;
}

.test-result.warning {
  background: #fffbe6;
  border: 1px solid #ffe58f;
  color: #d48806;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .configuration {
    padding: 16px;
  }
  
  .selector-content {
    grid-template-columns: 1fr;
  }
  
  .param-grid {
    grid-template-columns: 1fr;
  }
  
  .template-list {
    grid-template-columns: 1fr;
  }
  
  .config-form {
    max-width: 100%;
  }
}

/* 动画效果 */
.config-card {
  animation: fadeInUp 0.3s ease-out;
}

.selector-item {
  animation: slideInLeft 0.3s ease-out;
}

.template-card {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.success {
  background: #52c41a;
}

.status-dot.error {
  background: #ff4d4f;
}

.status-dot.warning {
  background: #faad14;
}

.status-dot.info {
  background: #1890ff;
}

/* 夜间模式适配 */
.app-layout.dark .configuration-header {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .configuration-header h2 {
  color: #ffffff;
}

.app-layout.dark .configuration-header .ant-typography {
  color: #d9d9d9;
}

.app-layout.dark .config-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .config-card .ant-card-head {
  border-bottom-color: #303030;
}

.app-layout.dark .config-card .ant-card-head-title {
  color: #ffffff;
}

.app-layout.dark .config-section {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .config-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .config-item:hover {
  background: #303030;
}

.app-layout.dark .config-label {
  color: #ffffff;
}

.app-layout.dark .config-description {
  color: #d9d9d9;
}

.app-layout.dark .config-value {
  color: #d9d9d9;
}

.app-layout.dark .selector-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .selector-item:hover {
  background: #303030;
}

.app-layout.dark .selector-name {
  color: #ffffff;
}

.app-layout.dark .selector-path {
  color: #40a9ff;
}

.app-layout.dark .selector-description {
  color: #d9d9d9;
}

.app-layout.dark .proxy-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .proxy-item:hover {
  background: #303030;
}

.app-layout.dark .proxy-url {
  color: #40a9ff;
}

.app-layout.dark .proxy-type {
  color: #d9d9d9;
}

.app-layout.dark .proxy-status {
  color: #d9d9d9;
}

.app-layout.dark .template-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .template-item:hover {
  background: #303030;
}

.app-layout.dark .template-name {
  color: #ffffff;
}

.app-layout.dark .template-description {
  color: #d9d9d9;
}

.app-layout.dark .template-tags {
  color: #8c8c8c;
}

.app-layout.dark .empty-state {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-icon {
  color: #595959;
}

.app-layout.dark .empty-state-text {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-hint {
  color: #8c8c8c;
}

.app-layout.dark .config-form {
  background: transparent;
}

.app-layout.dark .config-form .ant-form-item-label > label {
  color: #d9d9d9 !important;
}

.app-layout.dark .config-form .ant-input {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .config-form .ant-input:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .config-form .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .config-form .ant-input::placeholder {
  color: #8c8c8c !important;
}

.app-layout.dark .config-form .ant-input-number {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .config-form .ant-input-number:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .config-form .ant-input-number:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .config-form .ant-input-number-input {
  color: #ffffff !important;
}

.app-layout.dark .config-form .ant-input-password {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .config-form .ant-input-password:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .config-form .ant-input-password:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .config-form .ant-input-password-icon {
  color: #8c8c8c !important;
}

.app-layout.dark .config-form .ant-input-password-icon:hover {
  color: #d9d9d9 !important;
}

.app-layout.dark .selector-input {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .selector-label {
  color: #d9d9d9 !important;
}

.app-layout.dark .selector-field {
  border-bottom-color: #303030;
}

.app-layout.dark .config-section {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .config-section .ant-card-head {
  border-bottom-color: #303030;
}

.app-layout.dark .config-section .ant-card-head-title {
  color: #ffffff;
}

.app-layout.dark .config-section .ant-card-body {
  background: #262626 !important;
}

/* 强制修复配置页面的黑色方块问题 */
.app-layout.dark .configuration * {
  background: transparent !important;
}

.app-layout.dark .configuration .ant-card {
  background: #262626 !important;
  border-color: #303030 !important;
}

.app-layout.dark .configuration .ant-card-head {
  background: #262626 !important;
  border-bottom-color: #303030 !important;
}

.app-layout.dark .configuration .ant-card-body {
  background: #262626 !important;
}

.app-layout.dark .configuration .ant-form-item {
  background: transparent !important;
}

.app-layout.dark .configuration .ant-form-item-label {
  background: transparent !important;
}

.app-layout.dark .configuration .ant-form-item-control {
  background: transparent !important;
}

.app-layout.dark .configuration .ant-row {
  background: transparent !important;
}

.app-layout.dark .configuration .ant-col {
  background: transparent !important;
}

.app-layout.dark .configuration .system-params {
  background: transparent !important;
}

.app-layout.dark .configuration .system-params * {
  background: transparent !important;
}

.app-layout.dark .configuration .system-params .ant-card {
  background: #262626 !important;
}

.app-layout.dark .configuration .system-params .ant-card-head {
  background: #262626 !important;
}

.app-layout.dark .configuration .system-params .ant-card-body {
  background: #262626 !important;
}
