import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import TaskDetail from '../TaskDetail';

// Mock the API calls
jest.mock('../../../services/monitoringTaskApi', () => ({
  getMonitoringTask: jest.fn(),
  getTaskUrls: jest.fn(),
  getTaskExecutionHistory: jest.fn(),
  getTaskStats: jest.fn(),
}));

// Mock useParams hook
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ taskId: 'test-task-id' }),
  useNavigate: () => jest.fn(),
}));

const mockTask = {
  id: 'test-task-id',
  name: '测试监控任务',
  description: '这是一个测试任务',
  status: 'active',
  is_running: false,
  tags: ['测试', '监控'],
  created_at: '2024-01-01T10:00:00Z',
  updated_at: '2024-01-01T12:00:00Z',
  last_run: '2024-01-01T11:00:00Z',
  next_run: '2024-01-02T10:00:00Z',
  schedule: {
    type: 'daily',
    enabled: true,
    time: '10:00',
    timezone: 'Asia/Shanghai',
    enable_random_delay: true,
    random_delay_min: 0,
    random_delay_max: 180,
  },
  config: {
    platform: 'mercadolibre',
    priority: 'high',
    retry_count: 3,
    timeout: 300,
    concurrent_limit: 5,
    batch_size: 10,
    enable_notifications: true,
    notification_config: {
      email: '<EMAIL>'
    }
  }
};

const mockTaskStats = {
  url_stats: {
    total_urls: 100,
    active_urls: 95,
  },
  execution_stats: {
    total_executions: 50,
    successful_executions: 45,
    failed_executions: 5,
    success_rate: 90,
    avg_response_time: 1500,
    last_success_time: '2024-01-01T11:00:00Z',
  }
};

describe('TaskDetail Component', () => {
  const mockApi = require('../../../services/monitoringTaskApi');

  beforeEach(() => {
    jest.clearAllMocks();
    mockApi.getMonitoringTask.mockResolvedValue({
      success: true,
      data: mockTask
    });
    mockApi.getTaskUrls.mockResolvedValue({
      success: true,
      data: []
    });
    mockApi.getTaskExecutionHistory.mockResolvedValue({
      success: true,
      data: []
    });
    mockApi.getTaskStats.mockResolvedValue({
      success: true,
      data: mockTaskStats
    });
  });

  const renderComponent = () => {
    return render(
      <MemoryRouter initialEntries={['/monitoring/test-task-id']}>
        <TaskDetail />
      </MemoryRouter>
    );
  };

  it('应该正确渲染任务基本信息', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('测试监控任务')).toBeInTheDocument();
    });

    // 检查基本信息
    expect(screen.getByText('test-task-id')).toBeInTheDocument();
    expect(screen.getByText('这是一个测试任务')).toBeInTheDocument();
    expect(screen.getByText('运行中')).toBeInTheDocument();
  });

  it('应该正确显示调度配置信息', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('每日执行')).toBeInTheDocument();
    });

    expect(screen.getByText('已启用')).toBeInTheDocument();
    expect(screen.getByText('10:00')).toBeInTheDocument();
    expect(screen.getByText('Asia/Shanghai')).toBeInTheDocument();
  });

  it('应该正确显示任务配置信息', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('mercadolibre')).toBeInTheDocument();
    });

    expect(screen.getByText('高优先级')).toBeInTheDocument();
    expect(screen.getByText('3 次')).toBeInTheDocument();
    expect(screen.getByText('300 秒')).toBeInTheDocument();
    expect(screen.getByText('5 个')).toBeInTheDocument();
    expect(screen.getByText('10 个/批次')).toBeInTheDocument();
  });

  it('应该正确显示任务统计信息', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('100')).toBeInTheDocument(); // 总URL数
    });

    expect(screen.getByText('95')).toBeInTheDocument(); // 活跃URL
    expect(screen.getByText('50')).toBeInTheDocument(); // 总执行次数
    expect(screen.getByText('90')).toBeInTheDocument(); // 成功率
  });

  it('应该正确显示任务标签', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('测试')).toBeInTheDocument();
    });

    expect(screen.getByText('监控')).toBeInTheDocument();
  });

  it('应该正确显示随机延迟配置', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('已启用')).toBeInTheDocument();
    });

    expect(screen.getByText(/延迟范围: 0 - 180 分钟/)).toBeInTheDocument();
  });

  it('当API调用失败时应该显示错误信息', async () => {
    mockApi.getMonitoringTask.mockRejectedValue(new Error('API Error'));

    renderComponent();

    // 简单检查组件是否渲染，不检查具体的错误消息
    await waitFor(() => {
      expect(screen.getByText(/加载任务详情中/)).toBeInTheDocument();
    }, { timeout: 3000 });
  });
});
