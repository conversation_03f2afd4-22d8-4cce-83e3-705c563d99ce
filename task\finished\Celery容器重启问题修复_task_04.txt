# Celery容器重启问题修复任务

**任务编号**: Task 04  
**任务名称**: 修复Docker中Celery容器一直重启的问题  
**创建时间**: 2025年7月2日  
**状态**: ✅ 已完成

## 问题描述

用户报告Docker中的celery-worker和celery-beat容器一直重启，退出码为1，导致任务队列系统无法正常工作。

## 问题分析

### 🔍 根本原因
通过分析容器日志，发现了一系列导入和依赖问题：

1. **模块导入路径错误**: `ModuleNotFoundError: No module named 'backend'`
2. **接口定义缺失**: `NameError: name 'IDataExtractor' is not defined`
3. **导入路径不匹配**: `cannot import name 'ProductSnapshot' from 'app.models.product'`
4. **依赖包缺失**: `ModuleNotFoundError: No module named 'psutil'`

### 📋 具体错误序列

#### 错误1: 模块路径问题
```
File "/app/app/tasks/crawl_batch.py", line 14, in <module>
    from backend.app.celery_app import celery_app
ModuleNotFoundError: No module named 'backend'
```

**原因**: Docker容器内工作目录为`/app`，代码被复制到`/app`，但导入语句使用`backend.`前缀，在容器内不存在。

#### 错误2: 接口定义缺失
```
File "/app/crawler/engine/base.py", line 134, in <module>
    class BaseDataExtractor(IDataExtractor):
NameError: name 'IDataExtractor' is not defined
```

**原因**: `IDataExtractor`接口在`interfaces.py`中不存在，但`BaseDataExtractor`类尝试继承它。

#### 错误3: 导入路径错误
```
ImportError: cannot import name 'ProductSnapshot' from 'app.models.product'
```

**原因**: `ProductSnapshot`类在`app.models.snapshot`中定义，不在`app.models.product`中。

#### 错误4: 依赖包缺失
```
File "/app/app/tasks/monitor_tasks.py", line 9, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
```

**原因**: `psutil`包未在`requirements.txt`中声明。

## 修复方案

### ✅ 1. 修复导入路径问题
**文件**: `backend/app/tasks/crawl_batch.py`

**修改前**:
```python
from backend.app.celery_app import celery_app
from backend.app.core.rate_limiter import RateLimiter, RateLimitConfig
from backend.crawler.api_client import CrawlerAPIClient, APIConfig
from backend.app.database import get_async_session
from backend.app.models.task import CrawlTask
from backend.app.models.product import ProductSnapshot
```

**修改后**:
```python
from app.celery_app import celery_app
from app.core.rate_limiter import RateLimiter, RateLimitConfig
from crawler.api_client import CrawlerAPIClient, APIConfig
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.snapshot import ProductSnapshot
```

### ✅ 2. 修复接口继承问题
**文件**: `backend/crawler/engine/base.py`

**修改前**:
```python
class BaseDataExtractor(IDataExtractor):
```

**修改后**:
```python
class BaseDataExtractor(ABC):
```

### ✅ 3. 修复main.py导入问题
**文件**: `backend/app/main.py`

**修改前**:
```python
from backend.app.api.task_routes import router as task_router
from backend.app.api.monitoring_routes import router as monitoring_router
```

**修改后**:
```python
from app.api.task_routes import router as task_router
from app.api.monitoring_routes import router as monitoring_router
```

### ✅ 4. 添加缺失依赖
**文件**: `backend/requirements.txt`

**添加**:
```
# System monitoring
psutil>=5.9.0
```

### 🔄 5. 重新构建Docker镜像
由于修改了`requirements.txt`，需要重新构建Docker镜像：
```bash
docker-compose -f docker-compose.dev.yml build celery-worker celery-beat
```

## 技术分析

### 🔧 Docker容器环境差异
- **宿主机环境**: 项目根目录包含`backend/`文件夹
- **容器环境**: 代码被复制到`/app`，没有`backend`模块路径
- **PYTHONPATH**: 设置为`/app`，所以导入应该从`app.`开始

### 🔧 模块结构理解
```
宿主机:
MonIt/
  backend/
    app/
      celery_app.py
      tasks/
        crawl_batch.py

容器内:
/app/
  app/
    celery_app.py
    tasks/
      crawl_batch.py
```

### 🔧 依赖管理
- 系统监控任务需要`psutil`包来获取系统资源信息
- 必须在`requirements.txt`中声明所有Python依赖

## 修复进度

### ✅ 已完成
- [x] 修复`crawl_batch.py`中的导入路径
- [x] 修复`BaseDataExtractor`接口继承问题
- [x] 修复`main.py`中的导入路径
- [x] 修复`ProductSnapshot`导入路径
- [x] 添加`psutil`依赖到requirements.txt

### ✅ 已完成
- [x] 重新构建Docker镜像
- [x] 测试Celery容器启动
- [x] 验证任务队列功能
- [x] 修复Redis连接配置

### ✅ 验证结果
- [x] celery-worker容器正常启动
- [x] celery-beat容器正常启动
- [x] 任务队列连接正常 (redis://redis:6379/0)
- [x] 监控任务正常执行
- [x] 所有任务正确注册

## 预期结果

修复完成后，应该实现：
1. ✅ Celery容器不再重启
2. ✅ 任务队列系统正常工作
3. ✅ 监控任务能够执行
4. ✅ 批量爬取任务能够调度

## 后续优化建议

1. **统一导入规范**: 建立明确的导入路径规范，避免混用绝对和相对导入
2. **依赖管理**: 建立完整的依赖检查流程
3. **容器测试**: 增加容器启动测试，及早发现导入问题
4. **文档更新**: 更新开发文档，说明容器内外的模块路径差异

### ✅ 5. 修复Redis连接配置
**文件**: `backend/app/celery_app.py`

**问题**: Celery配置使用localhost:6379，在Docker容器内无法连接
**解决**: 直接从环境变量读取Redis URL

**修改**:
```python
import os
redis_url = os.getenv('REDIS_URL', settings.REDIS_URL)

celery_app = Celery(
    "monit_crawler",
    broker=redis_url,
    backend=redis_url,
)
```

## 最终验证结果

### ✅ 容器状态
```bash
$ docker ps | grep celery
cf1fbf50d879   monit-celery-worker   Up About a minute
b78872e4c7a3   monit-celery-beat     Up About a minute
```

### ✅ Worker连接状态
```
Connected to redis://redis:6379/0
celery@cf1fbf50d879 ready.
```

### ✅ Beat调度状态
```
broker -> redis://redis:6379/0
beat: Starting...
Scheduler: Sending due task collect-system-metrics
```

### ✅ 注册的任务
- app.tasks.cleanup_tasks.cleanup_failed_tasks
- app.tasks.cleanup_tasks.generate_cleanup_report
- app.tasks.cleanup_tasks.optimize_database
- app.tasks.crawl_batch.crawl_batch_task
- app.tasks.crawl_batch.crawl_single_url
- app.tasks.monitor_tasks.check_queue_health
- app.tasks.monitor_tasks.collect_system_metrics
- app.tasks.monitor_tasks.urgent_monitor_task
- app.tasks.result_processor.aggregate_daily_statistics
- app.tasks.result_processor.process_results

## 总结

通过系统性地修复以下问题，成功解决了Celery容器的重启问题：

1. ✅ **模块导入路径**: 修复了backend.前缀导入问题
2. ✅ **接口定义**: 修复了不存在的IDataExtractor接口
3. ✅ **依赖管理**: 添加了缺失的psutil包
4. ✅ **文件路径**: 修复了错误的模块导入路径
5. ✅ **Redis连接**: 修复了容器内Redis连接配置

现在Celery任务队列系统完全正常工作，可以处理爬取任务和监控任务。
