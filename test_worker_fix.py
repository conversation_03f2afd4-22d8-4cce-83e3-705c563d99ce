#!/usr/bin/env python3
"""
测试Worker分配修复效果
"""

import asyncio
import aiohttp
import json

async def test_worker_assignment():
    """测试有Worker分配的任务"""
    base_url = "http://localhost:8000"
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    async with aiohttp.ClientSession() as session:
        # 检查Worker分配
        print(f"🔍 检查任务 {task_id} 的Worker分配...")
        async with session.get(f"{base_url}/api/v1/crawler-workers/task/{task_id}/assigned-workers") as response:
            if response.status == 200:
                workers = await response.json()
                if workers:
                    print(f"✅ 任务有 {len(workers)} 个分配的Worker:")
                    for worker in workers:
                        print(f"   - Worker名称: {worker.get('worker_name', 'N/A')}")
                        print(f"   - Worker ID: {worker.get('worker_id', 'N/A')}")
                    
                    # 尝试执行任务
                    print(f"\n🚀 尝试执行任务...")
                    async with session.post(f"{base_url}/api/v1/monitoring-tasks/{task_id}/execute") as exec_response:
                        if exec_response.status == 200:
                            result = await exec_response.json()
                            print(f"✅ 任务执行成功")
                            
                            # 等待任务开始
                            print("⏳ 等待10秒...")
                            await asyncio.sleep(10)
                            
                        elif exec_response.status == 400:
                            error = await exec_response.json()
                            if "already running" in error.get("detail", "").lower():
                                print("⚠️  任务已经在运行中")
                            else:
                                print(f"❌ 任务执行失败: {error}")
                        else:
                            print(f"❌ 任务执行失败: {exec_response.status}")
                    
                    # 检查爬虫请求记录
                    print(f"\n🔍 检查爬虫请求记录...")
                    async with session.get(f"{base_url}/api/v1/crawler-debug/requests?task_id={task_id}&limit=3") as req_response:
                        if req_response.status == 200:
                            requests = await req_response.json()
                            if requests:
                                print(f"✅ 找到 {len(requests)} 条记录")
                                for i, req in enumerate(requests):
                                    worker_id = req.get('worker_id', '')
                                    print(f"   记录 {i+1}: Worker ID = '{worker_id}'")
                                    if worker_id == '11235w':
                                        print("   ✅ 修复生效！显示实际Worker名称")
                                    elif worker_id == 'unknown':
                                        print("   ⚠️  Worker分配获取失败")
                                    elif worker_id.startswith('worker_batch_'):
                                        print("   ❌ 仍然是旧格式")
                            else:
                                print("⚠️  没有找到该任务的爬虫请求记录")
                        else:
                            print(f"❌ 获取爬虫请求记录失败: {req_response.status}")
                else:
                    print("⚠️  任务没有分配Worker")
            else:
                print(f"❌ 获取Worker分配失败: {response.status}")

if __name__ == "__main__":
    asyncio.run(test_worker_assignment())
