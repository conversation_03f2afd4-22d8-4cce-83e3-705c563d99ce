import React from 'react';
import {
  Card,
  Table,
  Tag,
  Timeline,
  Row,
  Col,
  Statistic,
  Progress,
  Empty,
  Tooltip
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface ExecutionHistory {
  id: string;
  start_time: string;
  end_time?: string;
  status: string;
  trigger: string;
  url_count: number;
  success_count: number;
  error_count: number;
  duration: number;
  error_message?: string;
}

interface TaskStats {
  url_stats: {
    total_urls: number;
    active_urls: number;
    disabled_urls: number;
    error_urls: number;
  };
  execution_stats: {
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    success_rate: number;
    avg_duration: number;
  };
  task_info: {
    created_at: string;
    last_run?: string;
    next_run?: string;
    status: string;
    is_running: boolean;
  };
}

// 执行历史Tab组件
export const ExecutionHistoryTab: React.FC<{ 
  executionHistory: ExecutionHistory[]; 
  onRefresh: () => void; 
}> = ({ executionHistory, onRefresh }) => {
  const historyColumns: ColumnsType<ExecutionHistory> = [
    {
      title: '执行ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          running: { color: 'blue', icon: <ClockCircleOutlined />, text: '执行中' },
          success: { color: 'green', icon: <CheckCircleOutlined />, text: '成功' },
          failed: { color: 'red', icon: <CloseCircleOutlined />, text: '失败' },
          terminated: { color: 'orange', icon: <StopOutlined />, text: '已终止' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '触发方式',
      dataIndex: 'trigger',
      key: 'trigger',
      width: 100,
      render: (trigger: string) => (
        <Tag color={trigger === 'manual' ? 'blue' : 'green'}>
          {trigger === 'manual' ? '手动' : '自动'}
        </Tag>
      ),
    },
    {
      title: 'URL数量',
      dataIndex: 'url_count',
      key: 'url_count',
      width: 100,
    },
    {
      title: '成功/失败',
      key: 'success_rate',
      width: 120,
      render: (_, record) => (
        <span>
          <span style={{ color: '#3f8600' }}>{record.success_count}</span>
          {' / '}
          <span style={{ color: '#cf1322' }}>{record.error_count}</span>
        </span>
      ),
    },
    {
      title: '执行时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => `${duration}s`,
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 180,
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: 'end_time',
      width: 180,
      render: (time: string) => time || '未结束',
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      ellipsis: true,
      render: (message: string) => message ? (
        <Tooltip title={message}>
          <span style={{ color: '#cf1322' }}>{message}</span>
        </Tooltip>
      ) : '-',
    },
  ];

  if (executionHistory.length === 0) {
    return (
      <Card title="执行历史">
        <Empty description="暂无执行历史" />
      </Card>
    );
  }

  return (
    <Card title="执行历史">
      <Table
        columns={historyColumns}
        dataSource={executionHistory}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条执行记录`,
        }}
        scroll={{ x: 1200 }}
      />
    </Card>
  );
};

// 统计图表Tab组件
export const StatsTab: React.FC<{ 
  taskStats: TaskStats | null; 
}> = ({ taskStats }) => {
  if (!taskStats) {
    return (
      <Card title="统计信息">
        <Empty description="暂无统计数据" />
      </Card>
    );
  }

  const { url_stats, execution_stats } = taskStats;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* URL统计 */}
        <Col span={12}>
          <Card title="URL统计">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic 
                  title="总URL数" 
                  value={url_stats.total_urls} 
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="活跃URL" 
                  value={url_stats.active_urls} 
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="禁用URL" 
                  value={url_stats.disabled_urls} 
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="错误URL" 
                  value={url_stats.error_urls} 
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 8 }}>URL状态分布</div>
              <Progress 
                percent={url_stats.total_urls > 0 ? (url_stats.active_urls / url_stats.total_urls * 100) : 0}
                success={{ 
                  percent: url_stats.total_urls > 0 ? (url_stats.active_urls / url_stats.total_urls * 100) : 0 
                }}
                format={() => `${url_stats.active_urls}/${url_stats.total_urls}`}
              />
            </div>
          </Card>
        </Col>

        {/* 执行统计 */}
        <Col span={12}>
          <Card title="执行统计">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic 
                  title="总执行次数" 
                  value={execution_stats.total_executions} 
                  prefix={<PlayCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="成功次数" 
                  value={execution_stats.successful_executions} 
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="失败次数" 
                  value={execution_stats.failed_executions} 
                  valueStyle={{ color: '#cf1322' }}
                  prefix={<CloseCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="平均时长" 
                  value={execution_stats.avg_duration} 
                  suffix="s"
                  precision={1}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 8 }}>成功率</div>
              <Progress 
                percent={execution_stats.success_rate}
                status={execution_stats.success_rate > 80 ? 'success' : execution_stats.success_rate > 60 ? 'normal' : 'exception'}
                format={(percent) => `${percent}%`}
              />
            </div>
          </Card>
        </Col>

        {/* 执行趋势时间线 */}
        <Col span={24}>
          <Card title="最近执行记录">
            <Timeline
              items={[
                {
                  color: "green",
                  dot: <CheckCircleOutlined />,
                  children: (
                    <div>
                      <strong>任务创建</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {taskStats.task_info.created_at}
                      </div>
                    </div>
                  )
                },
                ...(taskStats.task_info.last_run ? [{
                  color: "blue" as const,
                  dot: <PlayCircleOutlined />,
                  children: (
                    <div>
                      <strong>最后执行</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {taskStats.task_info.last_run}
                      </div>
                    </div>
                  )
                }] : []),
                ...(taskStats.task_info.next_run ? [{
                  color: "orange" as const,
                  dot: <ClockCircleOutlined />,
                  children: (
                    <div>
                      <strong>下次执行</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {taskStats.task_info.next_run}
                      </div>
                    </div>
                  )
                }] : [])
              ]}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};


