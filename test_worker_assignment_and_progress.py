#!/usr/bin/env python3
"""
测试Worker分配和进度跟踪功能
"""

import asyncio
import aiohttp
import json
import logging
import websockets
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"
WS_URL = "ws://localhost:8000/ws"

async def test_worker_assignment_and_progress():
    """测试Worker分配和进度跟踪功能"""
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 获取监控任务
        logger.info("1. 获取监控任务...")
        async with session.get(f"{API_BASE}/monitoring-tasks") as response:
            if response.status == 200:
                data = await response.json()
                tasks = data.get("data", [])
                if not tasks:
                    logger.error("没有找到监控任务")
                    return False
                
                task = tasks[0]
                task_id = task["id"]
                logger.info(f"选择任务: {task_id} - {task.get('name', 'Unknown')}")
            else:
                logger.error(f"获取任务列表失败: {response.status}")
                return False
        
        # 2. 确保任务未运行
        logger.info("2. 检查任务状态...")
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}") as response:
            if response.status == 200:
                task_detail = await response.json()
                if task_detail.get("is_running"):
                    logger.info("任务正在运行，先终止...")
                    async with session.post(f"{API_BASE}/monitoring-tasks/{task_id}/terminate") as term_response:
                        if term_response.status == 200:
                            logger.info("任务已终止")
                            await asyncio.sleep(2)
                        else:
                            logger.warning("终止任务失败")
        
        # 3. 建立WebSocket连接监听进度更新
        logger.info("3. 建立WebSocket连接...")
        websocket_messages = []
        
        async def websocket_listener():
            try:
                async with websockets.connect(WS_URL) as websocket:
                    # 订阅任务更新
                    subscribe_message = {
                        "action": "subscribe_task",
                        "task_id": task_id
                    }
                    await websocket.send(json.dumps(subscribe_message))
                    logger.info(f"已订阅任务 {task_id} 的更新")
                    
                    # 监听消息
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            websocket_messages.append(data)
                            
                            msg_type = data.get("type")
                            if msg_type == "task_progress":
                                progress_data = data.get("data", {})
                                logger.info(f"📊 进度更新: {progress_data.get('progress', 0)}% "
                                          f"({progress_data.get('processed_urls', 0)}/{progress_data.get('total_urls', 0)})")
                            elif msg_type == "url_status_change":
                                url_data = data.get("data", {})
                                logger.info(f"🔗 URL状态: {url_data.get('url', 'N/A')[:50]}... -> {url_data.get('status')}")
                            elif msg_type == "worker_assignment_update":
                                assign_data = data.get("data", {})
                                logger.info(f"👷 Worker分配: {assign_data.get('url', 'N/A')[:30]}... -> {assign_data.get('worker_id')}")
                            
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析WebSocket消息: {message}")
                            
            except Exception as e:
                logger.error(f"WebSocket连接错误: {e}")
        
        # 启动WebSocket监听器
        websocket_task = asyncio.create_task(websocket_listener())
        
        # 等待WebSocket连接建立
        await asyncio.sleep(2)
        
        # 4. 执行任务
        logger.info("4. 执行任务...")
        async with session.post(f"{API_BASE}/monitoring-tasks/{task_id}/execute") as response:
            if response.status == 200:
                result = await response.json()
                logger.info("✅ 任务执行已启动")
                logger.info(f"执行ID: {result.get('execution_id')}")
                logger.info(f"提交ID: {result.get('submission_id')}")
                logger.info(f"URL数量: {result.get('url_count')}")
            else:
                error_text = await response.text()
                logger.error(f"❌ 任务执行失败: {response.status} - {error_text}")
                websocket_task.cancel()
                return False
        
        # 5. 检查Worker分配
        logger.info("5. 检查Worker分配...")
        await asyncio.sleep(1)  # 等待分配完成
        
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}/assignments") as response:
            if response.status == 200:
                assignments = await response.json()
                assignment_data = assignments.get("data", {})
                
                if assignment_data:
                    logger.info("✅ Worker分配信息:")
                    logger.info(f"  总URL数: {assignment_data.get('total_urls', 0)}")
                    logger.info(f"  Worker数: {assignment_data.get('worker_count', 0)}")
                    
                    worker_assignments = assignment_data.get("assignments", {})
                    for worker_id, urls in worker_assignments.items():
                        logger.info(f"  {worker_id}: {len(urls)} 个URL")
                else:
                    logger.warning("⚠️ 没有找到Worker分配信息")
            else:
                logger.warning(f"获取Worker分配失败: {response.status}")
        
        # 6. 监听进度更新
        logger.info("6. 监听进度更新 (30秒)...")
        
        start_time = asyncio.get_event_loop().time()
        progress_updates = 0
        url_updates = 0
        worker_updates = 0
        
        while asyncio.get_event_loop().time() - start_time < 30:
            await asyncio.sleep(1)
            
            # 统计收到的消息
            for msg in websocket_messages:
                msg_type = msg.get("type")
                if msg_type == "task_progress":
                    progress_updates += 1
                elif msg_type == "url_status_change":
                    url_updates += 1
                elif msg_type == "worker_assignment_update":
                    worker_updates += 1
            
            # 清空已统计的消息
            websocket_messages.clear()
        
        # 7. 检查最终状态
        logger.info("7. 检查最终状态...")
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}/history") as response:
            if response.status == 200:
                history = await response.json()
                executions = history.get("data", [])
                if executions:
                    latest = executions[0]
                    logger.info(f"最新执行状态: {latest.get('status')}")
                    logger.info(f"处理URL数: {latest.get('processed_count', 0)}")
                    logger.info(f"成功数: {latest.get('success_count', 0)}")
                    logger.info(f"失败数: {latest.get('error_count', 0)}")
        
        # 停止WebSocket监听
        websocket_task.cancel()
        
        # 8. 总结测试结果
        logger.info("8. 测试结果总结:")
        logger.info(f"📊 进度更新消息: {progress_updates}")
        logger.info(f"🔗 URL状态更新: {url_updates}")
        logger.info(f"👷 Worker分配更新: {worker_updates}")
        
        success = progress_updates > 0 or url_updates > 0
        
        if success:
            logger.info("✅ 测试成功: 收到了实时更新消息")
        else:
            logger.warning("⚠️ 测试部分成功: 没有收到实时更新消息，但基本功能正常")
        
        return True

async def main():
    """主函数"""
    logger.info("🚀 开始测试Worker分配和进度跟踪功能...")
    logger.info("=" * 60)
    
    try:
        success = await test_worker_assignment_and_progress()
        
        if success:
            logger.info("✅ 测试完成!")
        else:
            logger.error("❌ 测试失败!")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
