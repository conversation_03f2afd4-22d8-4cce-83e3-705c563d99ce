/**
 * 爬取配置相关的类型定义
 */

// 优先级类型
export type CrawlPriority = 'low' | 'medium' | 'high';

// 认证方法类型
export type AuthMethod = 'none' | 'api_key' | 'bearer_token' | 'basic_auth';

// 用户角色类型
export type UserRole = 'user' | 'admin' | 'system';

// 爬取请求配置
export interface CrawlRequestConfig {
  // 基础配置
  urls: string[];
  q: string;                          // 查询参数
  schema_str: string;                 // 数据提取模式
  cache: boolean;                     // 缓存设置
  priority: CrawlPriority;            // 优先级
  batch_name: string;                 // 批次名称
  batch_description: string;          // 批次描述
  callback_url: string;               // 回调URL
  max_concurrent_tasks: number;       // 最大并发任务数
  batch_timeout: number;              // 批次超时时间(秒)
}

// 认证上下文
export interface AuthContext {
  user_id: string;
  role: UserRole;
  permissions: string[];
  auth_method: AuthMethod;
  api_key_prefix: string;
  client_ip: string;
  user_agent: string;
  authenticated_at: string;
}

// 完整的API请求格式
export interface CrawlerAPIRequest {
  request: CrawlRequestConfig;
  auth_context: AuthContext;
}

// 爬取配置模板
export interface CrawlConfigTemplate {
  id: string;
  name: string;
  description: string;
  platform: string;
  config: Partial<CrawlRequestConfig>;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  usage_count: number;
  tags: string[];
}

// 爬取配置表单数据
export interface CrawlConfigFormData {
  name: string;
  description: string;
  platform: string;

  // 基础配置
  query: string;
  schema_str: string;
  cache: boolean;
  priority: CrawlPriority;

  // 认证配置
  auth_method: AuthMethod;
  api_key_prefix: string;
  user_agent: string;

  // 标签和状态
  tags: string[];
  is_active: boolean;
}

// 默认的Schema模板
export const DEFAULT_SCHEMAS = {
  mercadolibre: {
    type: "object",
    description: "MercadoLibre商品信息提取",
    properties: {
      product_basic_info: {
        type: "object",
        properties: {
          product_url: { type: "string", format: "uri" },
          product_name: { type: "string" },
          mlm_id: { type: "number" }
        },
        required: ["product_url", "product_name", "mlm_id"]
      },
      price_info: {
        type: "object",
        properties: {
          current_price: { type: "number" },
          currency: { type: "string" },
          discount_percentage: { type: "number" }
        }
      }
    }
  },
  amazon: {
    type: "object",
    description: "Amazon商品信息提取",
    properties: {
      product_basic_info: {
        type: "object",
        properties: {
          product_url: { type: "string", format: "uri" },
          product_name: { type: "string" },
          asin: { type: "string" }
        },
        required: ["product_url", "product_name", "asin"]
      }
    }
  }
};

// 默认配置值
export const DEFAULT_CRAWL_CONFIG: Partial<CrawlRequestConfig> = {
  q: "",
  schema_str: "",
  cache: false,
  priority: "medium",
  callback_url: "",
  max_concurrent_tasks: 2,
  batch_timeout: 300
};

// 默认认证上下文
export const DEFAULT_AUTH_CONTEXT: Partial<AuthContext> = {
  user_id: "monit_system",
  role: "user",
  permissions: [],
  auth_method: "api_key",
  api_key_prefix: "",
  client_ip: "127.0.0.1",
  user_agent: "MonIt-Crawler/1.0"
};
