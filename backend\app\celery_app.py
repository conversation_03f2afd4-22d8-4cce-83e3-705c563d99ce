"""
Celery应用配置
"""

import os
from celery import Celery
from config.settings import get_settings

settings = get_settings()

# 从环境变量获取Redis URL，如果没有则使用设置中的默认值
redis_url = os.getenv('REDIS_URL', settings.REDIS_URL)

# 创建Celery应用
celery_app = Celery(
    "monit_crawler",
    broker=redis_url,
    backend=redis_url,
    include=[
        "app.tasks.crawl_batch",
        "app.tasks.enhanced_crawl_task",  # 新的增强爬取任务
        "app.tasks.monitor_tasks",
        "app.tasks.monitoring_executor",
        "app.tasks.result_processor",
        "app.tasks.cleanup_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 序列化配置
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,
    
    # 时区配置
    timezone=settings.CELERY_TIMEZONE,
    enable_utc=settings.CELERY_ENABLE_UTC,
    
    # 任务路由配置
    task_routes={
        'app.tasks.crawl_batch.crawl_batch_task': {'queue': 'crawler_queue'},
        'app.tasks.crawl_batch.crawl_batch_task_enhanced': {'queue': 'crawler_queue'},
        'app.tasks.crawl_batch.crawl_single_url': {'queue': 'crawler_queue'},
        'app.tasks.enhanced_crawl_task.enhanced_crawl_task': {'queue': 'crawler_queue'},
        'app.tasks.enhanced_crawl_task.enhanced_single_url_crawl': {'queue': 'crawler_queue'},
        'app.tasks.monitor_tasks.monitor_task': {'queue': 'monitor_queue'},
        'app.tasks.monitoring_executor.execute_monitoring_task': {'queue': 'monitor_queue'},
        'app.tasks.monitoring_executor.execute_monitoring_task_enhanced': {'queue': 'monitor_queue'},
        'app.tasks.result_processor.process_results': {'queue': 'processor_queue'},
        'app.tasks.cleanup_tasks.cleanup_expired_tasks': {'queue': 'cleanup_queue'},
    },
    
    # 队列配置
    task_default_queue='default',
    task_create_missing_queues=True,
    
    # 工作进程配置
    worker_concurrency=4,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # 任务执行配置
    task_soft_time_limit=300,  # 5分钟软限制
    task_time_limit=600,       # 10分钟硬限制
    task_reject_on_worker_lost=True,
    
    # 结果配置
    result_expires=3600,  # 结果保存1小时
    result_persistent=True,
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # 重试配置
    task_default_retry_delay=60,  # 默认重试延迟60秒
    task_max_retries=3,           # 默认最大重试3次
    
    # 批量任务配置
    task_always_eager=False,
    task_eager_propagates=True,
    
    # 安全配置
    worker_hijack_root_logger=False,
    worker_log_color=False,
)

# 任务优先级队列配置
celery_app.conf.task_routes.update({
    # 高优先级队列
    'app.tasks.crawl_batch.urgent_crawl_task': {'queue': 'crawler_queue_high'},
    'app.tasks.monitor_tasks.urgent_monitor_task': {'queue': 'monitor_queue_high'},
    
    # 低优先级队列  
    'app.tasks.cleanup_tasks.*': {'queue': 'cleanup_queue_low'},
})

# 队列优先级配置
celery_app.conf.broker_transport_options = {
    'priority_steps': list(range(10)),  # 0-9优先级
    'sep': ':',
    'queue_order_strategy': 'priority',
}

# 定时任务配置 (Celery Beat)
celery_app.conf.beat_schedule = {
    # 每5分钟检查一次过期任务
    'cleanup-expired-tasks': {
        'task': 'app.tasks.cleanup_tasks.cleanup_expired_tasks',
        'schedule': 300.0,  # 5分钟
        'options': {'queue': 'cleanup_queue_low'}
    },
    
    # 每分钟收集系统指标
    'collect-system-metrics': {
        'task': 'app.tasks.monitor_tasks.collect_system_metrics',
        'schedule': 60.0,   # 1分钟
        'options': {'queue': 'monitor_queue'}
    },
    
    # 每10分钟检查任务队列健康状态
    'check-queue-health': {
        'task': 'app.tasks.monitor_tasks.check_queue_health',
        'schedule': 600.0,  # 10分钟
        'options': {'queue': 'monitor_queue'}
    },
}

# 错误处理配置
celery_app.conf.task_annotations = {
    '*': {
        'rate_limit': '100/m',  # 默认限流每分钟100个任务
    },
    'app.tasks.crawl_batch.crawl_batch_task': {
        'rate_limit': '10/m',   # 批量爬取任务限流更严格
        'time_limit': 1800,     # 30分钟超时
        'soft_time_limit': 1500, # 25分钟软超时
    },
    'app.tasks.crawl_batch.crawl_single_url': {
        'rate_limit': '60/m',   # 单URL爬取限流
        'time_limit': 300,      # 5分钟超时
        'soft_time_limit': 240, # 4分钟软超时
    }
}

# 日志配置
celery_app.conf.worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
celery_app.conf.worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

if __name__ == '__main__':
    celery_app.start()
