.task-manager {
  padding: 24px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.task-manager-header {
  margin-bottom: 32px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.task-manager-header h2 {
  margin-bottom: 8px;
  color: #1a1a1a;
  font-weight: 600;
}

.task-manager-header .ant-typography {
  color: #666666;
  font-size: 16px;
}

/* 任务创建区域 */
.task-creation-section {
  margin-bottom: 32px;
}

.task-creation-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.task-creation-card .ant-card-head {
  border-bottom: 1px solid #f5f5f5;
}

.task-creation-card .ant-card-head-title {
  color: #1a1a1a;
  font-weight: 600;
}

/* 文件上传区域 - 禁用外层边框 */
.upload-area {
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  text-align: center;
  background: transparent !important;
  transition: none !important;
  cursor: pointer;
}

.upload-area:hover {
  border: none !important;
  background: transparent !important;
}

.upload-area.dragover {
  border: none !important;
  background: transparent !important;
  transform: none !important;
}

/* 彻底禁用外层Dragger容器的边框 */
.upload-area.ant-upload-drag {
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  padding: 0 !important;
}

.upload-area.ant-upload-drag:hover {
  border: none !important;
  background: transparent !important;
}

.upload-area.ant-upload-drag.ant-upload-drag-hover {
  border: none !important;
  background: transparent !important;
}

/* 移除可能的左侧边框和伪元素 */
.upload-area::before,
.upload-area::after {
  display: none !important;
}

.ant-upload-drag::before,
.ant-upload-drag::after {
  display: none !important;
}

/* 确保外层容器没有边框 */
.upload-area,
.upload-area.ant-upload-drag {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 移除任何可能的左侧装饰线 */
.upload-area *::before,
.upload-area *::after {
  display: none !important;
}

/* 重置所有可能影响边框的样式 */
.ant-upload.ant-upload-drag {
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  position: relative !important;
}

.ant-upload.ant-upload-drag:hover {
  border: none !important;
  background: transparent !important;
}

/* 彻底移除所有可能的装饰元素 */
.upload-area,
.upload-area *,
.ant-upload-drag,
.ant-upload-drag * {
  box-shadow: none !important;
  outline: none !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
}

/* 确保主容器没有边框 */
.upload-area.ant-upload-drag {
  border: none !important;
  border-radius: 0 !important;
}

.upload-area.ant-upload-drag:hover,
.upload-area.ant-upload-drag.ant-upload-drag-hover {
  border: none !important;
  border-radius: 0 !important;
}

/* 移除任何可能的伪元素和装饰线 */
.upload-area *,
.ant-upload-drag *,
.ant-upload-drag-container *,
.ant-upload-drag-icon *,
.ant-upload-text *,
.ant-upload-hint * {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.upload-area *::before,
.upload-area *::after,
.ant-upload-drag *::before,
.ant-upload-drag *::after {
  display: none !important;
  content: none !important;
}

/* 额外的重置规则 - 针对可能的特定Ant Design类 */
.ant-upload-drag-container,
.ant-upload-drag-container::before,
.ant-upload-drag-container::after {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  display: block !important;
}

.ant-upload-drag-container::before,
.ant-upload-drag-container::after {
  display: none !important;
}

/* 只为内层容器设置边框 */
.upload-area .ant-upload-drag-container {
  border: 2px dashed #d9d9d9 !important;
  border-radius: 8px !important;
  background: #fafafa !important;
  padding: 24px !important;
  box-sizing: border-box !important;
  transition: all 0.3s ease !important;
}

.upload-area:hover .ant-upload-drag-container,
.upload-area .ant-upload-drag-container:hover {
  border-color: #1890ff !important;
  background: #f0f8ff !important;
}

/* 确保外层容器完全透明 */
.upload-area,
.upload-area.ant-upload-drag {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.upload-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #333333;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 14px;
  color: #666666;
}

/* 链接预览区域 */
.links-preview {
  margin-top: 24px;
}

.links-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.links-preview-stats {
  display: flex;
  gap: 24px;
}

.links-preview-stat {
  text-align: center;
}

.links-preview-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.links-preview-stat-label {
  font-size: 12px;
  color: #666666;
  margin-top: 4px;
}

.links-table {
  margin-top: 16px;
}

.link-status-valid {
  color: #52c41a;
}

.link-status-invalid {
  color: #ff4d4f;
}

.link-status-pending {
  color: #faad14;
}

/* 任务配置区域 */
.task-config {
  margin-top: 24px;
}

.config-section {
  margin-bottom: 24px;
}

.config-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

/* 任务列表区域 */
.task-list-section {
  margin-bottom: 32px;
}

.task-list-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.task-search {
  width: 300px;
}

/* 任务状态标签 */
.task-status-pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.task-status-running {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.task-status-completed {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.task-status-failed {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.task-status-paused {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

/* 进度监控区域 */
.progress-monitor {
  margin-top: 24px;
}

.progress-item {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-weight: 600;
  color: #1a1a1a;
}

.progress-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666666;
}

.progress-bar {
  margin-bottom: 12px;
}

.progress-logs {
  max-height: 200px;
  overflow-y: auto;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}

.progress-logs::-webkit-scrollbar {
  width: 6px;
}

.progress-logs::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.progress-logs::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.progress-logs::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.log-entry {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
  padding: 2px 0;
}

.log-entry-info {
  color: #1890ff;
}

.log-entry-warning {
  color: #faad14;
}

.log-entry-error {
  color: #ff4d4f;
}

.log-entry-success {
  color: #52c41a;
}

/* 表格操作列样式 */
.task-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.task-actions .ant-btn {
  border-radius: 6px;
  font-size: 12px;
  height: 28px;
  padding: 0 8px;
}

.task-actions .ant-dropdown-trigger {
  border: 1px solid #d9d9d9;
  background: #ffffff;
}

.task-actions .ant-dropdown-trigger:hover {
  border-color: #40a9ff;
  background: #f0f8ff;
}

/* 移动端任务卡片样式 */
.mobile-task-list {
  padding: 0;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.mobile-task-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.mobile-task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.mobile-task-card .ant-card-body {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

.mobile-task-header {
  margin-bottom: 12px;
}

.mobile-task-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.mobile-task-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  word-break: break-word;
  overflow-wrap: break-word;
}

.mobile-task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  width: 100%;
}

.mobile-task-platform {
  font-size: 12px;
  color: #666666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.mobile-task-time {
  font-size: 12px;
  color: #999999;
}

.mobile-task-progress {
  margin-bottom: 12px;
  width: 100%;
}

.mobile-task-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.mobile-action-row {
  display: flex;
  gap: 8px;
  width: 100%;
}

.mobile-action-btn {
  flex: 1;
  min-width: 0;
  font-size: 12px;
  height: 32px;
  border-radius: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-action-delete {
  width: 100%;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  /* 中等屏幕：保持按钮但减少间距 */
  .task-actions {
    gap: 2px;
  }

  .task-actions .ant-btn {
    padding: 0 6px;
    font-size: 11px;
  }

  .task-manager {
    padding: 20px;
  }
}

@media (max-width: 1024px) {
  /* 平板屏幕：使用移动端布局 */
  .task-manager {
    padding: 16px;
  }

  .mobile-task-card .ant-card-body {
    padding: 14px;
  }

  .mobile-task-name {
    font-size: 15px;
  }

  .mobile-action-btn {
    height: 30px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .task-manager {
    padding: 8px;
    margin: 0;
    width: 100%;
    max-width: 100%;
    /* 确保不会侵占侧边栏区域 */
    box-sizing: border-box;
  }

  .task-manager-header {
    padding: 12px;
    margin-bottom: 12px;
  }

  .task-list-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .task-filters {
    flex-direction: column;
    gap: 8px;
  }

  .task-search {
    width: 100%;
  }

  .config-grid {
    grid-template-columns: 1fr;
  }

  .links-preview-stats {
    flex-direction: column;
    gap: 12px;
  }

  /* 移动端任务卡片优化 */
  .mobile-task-card {
    margin-bottom: 8px;
    width: 100%;
    max-width: 100%;
  }

  .mobile-task-card .ant-card-body {
    padding: 12px;
  }

  .mobile-task-name {
    font-size: 14px;
  }

  .mobile-action-row {
    gap: 6px;
  }

  .mobile-action-btn {
    height: 28px;
    font-size: 11px;
    padding: 0 8px;
  }

  /* 确保表格在小屏幕下正确显示 */
  .ant-table-wrapper {
    overflow-x: auto;
    width: 100%;
    max-width: 100%;
  }

  .ant-table {
    min-width: 600px;
  }
}

@media (max-width: 576px) {
  /* 超小屏幕：进一步优化 */
  .task-manager {
    padding: 4px;
    margin: 0;
    width: 100%;
    max-width: 100%;
    /* 确保在超小屏幕下也不侵占侧边栏 */
    box-sizing: border-box;
  }

  .task-manager-header {
    padding: 8px;
    margin-bottom: 8px;
  }

  .task-manager-header h2 {
    font-size: 16px;
  }

  .mobile-task-card {
    margin-bottom: 6px;
  }

  .mobile-task-card .ant-card-body {
    padding: 8px;
  }

  .mobile-task-name {
    font-size: 13px;
  }

  .mobile-task-actions {
    gap: 4px;
  }

  .mobile-action-row {
    gap: 4px;
  }

  .mobile-action-btn {
    height: 28px;
    font-size: 10px;
    padding: 0 6px;
  }

  .mobile-action-delete {
    width: 100%;
    margin-top: 4px;
  }

  /* 桌面端表格操作列优化 */
  .task-actions .ant-btn {
    padding: 0 4px;
    font-size: 10px;
    height: 24px;
  }

  .task-actions .ant-btn span {
    display: none;
  }

  .task-actions .ant-btn .anticon {
    margin: 0;
  }

  /* 确保内容不超出边界 */
  .ant-card,
  .ant-table-wrapper,
  .mobile-task-list {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }
}

/* 动画效果 */
.task-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.upload-area {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 夜间模式适配 */
.app-layout.dark .task-manager-header {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .task-manager-header h2 {
  color: #ffffff;
}

.app-layout.dark .task-manager-header .ant-typography {
  color: #d9d9d9;
}

.app-layout.dark .task-creation-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .task-creation-card .ant-card-head {
  border-bottom-color: #303030;
}

.app-layout.dark .task-creation-card .ant-card-head-title {
  color: #ffffff;
}

.app-layout.dark .upload-area {
  background: transparent;
}

.app-layout.dark .upload-area .ant-upload-drag-container {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .upload-area:hover .ant-upload-drag-container,
.app-layout.dark .upload-area .ant-upload-drag-container:hover {
  border-color: #1890ff !important;
  background: #303030 !important;
}

.app-layout.dark .upload-hint {
  color: #d9d9d9;
}

.app-layout.dark .upload-description {
  color: #8c8c8c;
}

.app-layout.dark .task-list-card {
  background: #262626;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-layout.dark .task-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .task-item:hover {
  background: #303030;
}

.app-layout.dark .task-title {
  color: #ffffff;
}

.app-layout.dark .task-description {
  color: #d9d9d9;
}

.app-layout.dark .task-meta {
  color: #8c8c8c;
}

.app-layout.dark .batch-info {
  background: #303030;
  border-color: #434343;
}

.app-layout.dark .batch-title {
  color: #ffffff;
}

.app-layout.dark .batch-stats {
  color: #d9d9d9;
}

.app-layout.dark .link-item {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .link-item:hover {
  background: #303030;
}

.app-layout.dark .link-url {
  color: #40a9ff;
}

.app-layout.dark .link-status {
  color: #d9d9d9;
}

.app-layout.dark .progress-section {
  background: #262626;
  border-color: #303030;
}

.app-layout.dark .progress-title {
  color: #ffffff;
}

.app-layout.dark .progress-stats {
  color: #d9d9d9;
}

.app-layout.dark .empty-state {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-icon {
  color: #595959;
}

.app-layout.dark .empty-state-text {
  color: #d9d9d9;
}

.app-layout.dark .empty-state-hint {
  color: #8c8c8c;
}
