#!/usr/bin/env python3
"""
测试Worker管理相关的API端点
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_worker_apis():
    """测试Worker管理相关的API"""
    
    print("🧪 测试Worker管理相关的API...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 1. 测试获取爬取配置列表
            print("\n1. 测试获取爬取配置列表...")
            response = await client.get("http://localhost:8000/api/v1/crawler-configs/")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                configs = response.json()
                print(f"   ✅ 获取到 {len(configs)} 个爬取配置")
                if configs:
                    config = configs[0]
                    print(f"   - 配置ID: {config.get('config_id')}")
                    print(f"   - 配置名称: {config.get('config_name')}")
                    print(f"   - 状态: {config.get('status')}")
                    print(f"   - 版本: {config.get('version')}")
                    print(f"   - 标签: {config.get('tags')}")
            else:
                print(f"   ❌ 获取爬取配置失败: {response.text}")
            
            # 2. 测试获取后端配置列表
            print("\n2. 测试获取后端配置列表...")
            response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                configs = response.json()
                print(f"   ✅ 获取到 {len(configs)} 个后端配置")
                if configs:
                    config = configs[0]
                    print(f"   - 配置ID: {config.get('backend_id')}")
                    print(f"   - 配置名称: {config.get('backend_name')}")
                    print(f"   - 状态: {config.get('status')}")
                    print(f"   - 性能级别: {config.get('performance_level')}")
                    print(f"   - 最大并发任务数: {config.get('max_concurrent_tasks')}")
                    print(f"   - 健康评分: {config.get('health_score')}")
            else:
                print(f"   ❌ 获取后端配置失败: {response.text}")
            
            # 3. 测试获取Worker列表
            print("\n3. 测试获取Worker列表...")
            response = await client.get("http://localhost:8000/api/v1/crawler-workers/")
            print(f"   状态码: {response.status_code}")

            if response.status_code == 200:
                workers = response.json()
                print(f"   ✅ 获取到 {len(workers)} 个Worker")
                if workers:
                    worker = workers[0]
                    print(f"   - Worker ID: {worker.get('worker_id')}")
                    print(f"   - Worker名称: {worker.get('worker_name')}")
                    print(f"   - 状态: {worker.get('status')}")
                    print(f"   - 爬取配置ID: {worker.get('crawler_config_id')}")
                    print(f"   - 后端配置ID: {worker.get('backend_config_id')}")
            else:
                print(f"   ❌ 获取Worker列表失败: {response.text}")
            
            # 4. 测试创建Worker（如果有可用的配置）
            print("\n4. 测试创建Worker...")
            
            # 先获取可用的配置
            crawler_configs_response = await client.get("http://localhost:8000/api/v1/crawler-configs/")
            backend_configs_response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            
            if (crawler_configs_response.status_code == 200 and 
                backend_configs_response.status_code == 200):
                
                crawler_configs = crawler_configs_response.json()
                backend_configs = backend_configs_response.json()
                
                active_crawler_configs = [c for c in crawler_configs if c.get('status') == 'active']
                active_backend_configs = [c for c in backend_configs if c.get('status') == 'active']
                
                if active_crawler_configs and active_backend_configs:
                    worker_data = {
                        "worker_name": "测试Worker",
                        "description": "API测试创建的Worker",
                        "crawler_config_id": active_crawler_configs[0]['config_id'],
                        "backend_config_id": active_backend_configs[0]['backend_id'],
                        "max_concurrent_tasks": 2,
                        "allocated_concurrent": 1,
                        "priority": "normal",
                        "tags": ["测试"]
                    }
                    
                    response = await client.post(
                        "http://localhost:8000/api/v1/crawler-workers/",
                        json=worker_data
                    )
                    
                    print(f"   状态码: {response.status_code}")
                    if response.status_code == 200:
                        worker = response.json()
                        print(f"   ✅ 创建Worker成功: {worker.get('worker_name')}")
                        print(f"   - Worker ID: {worker.get('worker_id')}")
                        
                        # 清理：删除测试Worker
                        delete_response = await client.delete(
                            f"http://localhost:8000/api/v1/crawler-workers/{worker.get('worker_id')}"
                        )
                        if delete_response.status_code == 200:
                            print(f"   ✅ 测试Worker已清理")
                        
                    else:
                        print(f"   ❌ 创建Worker失败: {response.text}")
                else:
                    print(f"   ⚠️  没有可用的活跃配置，跳过Worker创建测试")
                    print(f"      - 活跃爬取配置: {len(active_crawler_configs)}")
                    print(f"      - 活跃后端配置: {len(active_backend_configs)}")
            else:
                print(f"   ❌ 无法获取配置信息，跳过Worker创建测试")
                
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_worker_apis()
    print(f"\n✅ Worker管理API测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
