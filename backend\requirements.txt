# Core web framework
fastapi>=0.110.0
uvicorn[standard]>=0.27.0

# Database
sqlalchemy>=2.0.25
alembic>=1.13.0
psycopg2-binary>=2.9.9
asyncpg>=0.29.0

# Task queue and caching
redis>=5.0.1
celery[redis]>=5.3.4

# HTTP client for API calls (replaces playwright, requests, beautifulsoup4)
httpx>=0.26.0
aiohttp>=3.8.5

# Data processing
pandas>=2.1.4
openpyxl>=3.1.2
xlrd>=2.0.1

# Configuration and validation
pydantic-settings==2.9.1

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# Authentication (if needed)
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# WebSocket support
websockets==12.0

# System monitoring
psutil>=5.9.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1