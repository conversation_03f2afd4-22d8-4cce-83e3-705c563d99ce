# MonIt 系统工作流与数据流详细分析 v2.0

## 文档信息
- **文档版本**: v2.0
- **创建日期**: 2025-08-09
- **最后更新**: 2025-08-09
- **作者**: MonIt技术团队
- **状态**: 已发布

## 1. 系统整体工作流概览

### 1.1 核心工作流
```
URL池管理 → 任务创建 → 任务调度 → Worker执行 → 结果存储 → 数据展示
    ↓         ↓         ↓         ↓         ↓         ↓
  Redis   → Redis   → Celery  → Crawl4AI → TimescaleDB → React UI
```

### 1.2 数据流向图
```mermaid
graph TB
    %% 数据输入层
    subgraph "📤 数据输入层"
        Excel[Excel文件上传]
        Manual[手动URL输入]
    end

    %% 数据处理层
    subgraph "🔄 数据处理层"
        Parse[URL解析服务]
        Pool[URL池存储]
        Filter[筛选过滤]
    end

    %% 任务管理层
    subgraph "⚙️ 任务管理层"
        TaskCreate[任务创建]
        TaskConfig[任务配置]
        TaskSchedule[任务调度]
    end

    %% 执行层
    subgraph "🚀 执行层"
        CeleryBeat[Celery Beat]
        CeleryWorker[Celery Worker]
        CrawlEngine[爬虫引擎]
    end

    %% 存储层
    subgraph "💾 存储层"
        Redis[(Redis统一存储)]
        TimescaleDB[(TimescaleDB时序数据)]
    end

    %% 展示层
    subgraph "📊 展示层"
        Dashboard[仪表板]
        TaskMgmt[任务管理]
        DataAnalysis[数据分析]
    end

    %% 连接关系
    Excel --> Parse
    Manual --> Parse
    Parse --> Pool
    Pool --> Redis
    Pool --> TaskCreate
    TaskCreate --> TaskConfig
    TaskConfig --> TaskSchedule
    TaskSchedule --> CeleryBeat
    CeleryBeat --> CeleryWorker
    CeleryWorker --> CrawlEngine
    CrawlEngine --> TimescaleDB
    Redis --> Dashboard
    Redis --> TaskMgmt
    TimescaleDB --> DataAnalysis
```

## 2. 前端页面与API交互详细分析

### 2.1 导航菜单与页面映射

#### 2.1.1 主导航结构
```typescript
// 来源: frontend/src/components/Layout/index.tsx
const menuItems = [
  { key: '/dashboard', label: '仪表板' },           // Dashboard页面
  { key: '/url-pool', label: 'URL池管理' },         // URL池管理页面
  { key: '/monitoring', label: '任务管理' },         // 监控任务管理页面
  { key: '/config', label: '配置管理' },             // 配置管理页面
  { key: '/worker-management', label: 'Worker管理' }, // Worker管理页面
  { key: '/celery-monitoring', label: 'Celery监控' }, // Celery监控页面
  { key: '/alerts', label: '告警管理' },             // 告警管理页面
  { key: '/data-analysis', label: '数据分析' },      // 数据分析页面
  { key: '/logs', label: '日志监控' }                // 日志监控页面
];
```

### 2.2 URL池管理页面详细分析

#### 2.2.1 页面功能与API调用
**页面路径**: `/url-pool`
**组件文件**: `frontend/src/pages/UrlPool/index.tsx`

##### 主要功能按钮与API调用
| 功能按钮 | API接口 | Redis操作 | 返回数据 |
|---------|---------|-----------|----------|
| **Excel上传** | `POST /api/v1/excel/upload-and-parse` | 写入`url_pool:urls:{url_id}` | 解析的URL列表 |
| **获取URL列表** | `GET /api/v1/url-pool/` | 读取`url_pool:indexes:all_url_ids` | URL分页列表 |
| **筛选URL** | `GET /api/v1/url-pool/?platform=xxx&status=xxx` | 读取`url_pool:indexes:by_platform:{platform}` | 筛选后的URL列表 |
| **批量选择** | `POST /api/v1/url-pool/batch-select` | 读取多个`url_pool:urls:{url_id}` | 选中的URL详情 |
| **批量更新状态** | `PUT /api/v1/url-pool/batch-update` | 更新多个`url_pool:urls:{url_id}:basic` | 更新结果 |
| **删除URL** | `DELETE /api/v1/url-pool/{url_id}` | 删除`url_pool:urls:{url_id}` | 删除确认 |

##### Excel上传详细流程
```typescript
// 前端调用 (frontend/src/services/urlPoolApi.ts)
const uploadExcel = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/v1/excel/upload-and-parse', {
    method: 'POST',
    body: formData
  });

  return response.json();
};
```

```python
# 后端处理 (backend/app/api/url_pool_routes.py)
@router.post("/excel/upload-and-parse")
async def upload_and_parse_excel(file: UploadFile):
    # 1. 解析Excel文件
    excel_processor = ExcelProcessor()
    products, stats = excel_processor.read_product_links(temp_file_path)

    # 2. 存储到Redis
    for product in products:
        url_hash = hashlib.md5(product['product_url'].encode()).hexdigest()
        await url_pool_service.add_url({
            'url': product['product_url'],
            'platform': product.get('platform', 'unknown'),
            'source_file': file.filename,
            'url_hash': url_hash
        })
```

##### Redis存储结构
```redis
# URL基本信息
url_pool:urls:{url_id}:basic -> Hash {
    "url": "https://example.com/product/123",
    "platform": "mercadolibre",
    "status": "active",
    "created_at": "2025-08-09T10:00:00Z",
    "source_file": "products.xlsx"
}

# URL元数据
url_pool:urls:{url_id}:metadata -> Hash {
    "url_hash": "abc123def456",
    "original_row": "5",
    "original_column": "product_url",
    "validation_status": "valid"
}

# URL统计信息
url_pool:urls:{url_id}:stats -> Hash {
    "total_crawls": "10",
    "success_crawls": "8",
    "last_crawl_time": "2025-08-09T09:30:00Z",
    "avg_response_time": "1.5"
}

# 索引结构
url_pool:indexes:all_url_ids -> Set ["url_001", "url_002", "url_003"]
url_pool:indexes:by_platform:mercadolibre -> Set ["url_001", "url_003"]
url_pool:indexes:by_status:active -> Set ["url_001", "url_002"]
```

### 2.3 任务管理页面详细分析

#### 2.3.1 页面功能与API调用
**页面路径**: `/monitoring`
**组件文件**: `frontend/src/pages/MonitoringTasks/index.tsx`

##### 主要功能按钮与API调用
| 功能按钮 | API接口 | Redis操作 | 返回数据 |
|---------|---------|-----------|----------|
| **获取任务列表** | `GET /api/v1/monitoring-tasks/` | 读取`monitoring_tasks:indexes:all_task_ids` | 任务分页列表 |
| **创建任务** | `POST /api/v1/monitoring-tasks/` | 写入`monitoring_tasks:tasks:{task_id}` | 新创建的任务 |
| **启动任务** | `POST /api/v1/monitoring-tasks/{task_id}/start` | 更新`monitoring_tasks:tasks:{task_id}:basic` | 启动结果 |
| **停止任务** | `POST /api/v1/monitoring-tasks/{task_id}/stop` | 更新`monitoring_tasks:tasks:{task_id}:basic` | 停止结果 |
| **删除任务** | `DELETE /api/v1/monitoring-tasks/{task_id}` | 删除`monitoring_tasks:tasks:{task_id}` | 删除确认 |
| **获取任务统计** | `GET /api/v1/monitoring-tasks/{task_id}/stats` | 读取`monitoring_tasks:tasks:{task_id}:stats` | 任务统计数据 |

##### 任务创建详细流程
```typescript
// 前端任务创建向导 (frontend/src/services/monitoringTaskApi.ts)
const createTask = async (taskData: TaskCreateData) => {
  const response = await fetch('/api/v1/monitoring-tasks/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: taskData.name,
      description: taskData.description,
      url_ids: taskData.selectedUrls,
      schedule_config: taskData.scheduleConfig,
      crawler_config: taskData.crawlerConfig
    })
  });

  return response.json();
};
```

```python
# 后端任务创建处理 (backend/app/api/monitoring_task_routes.py)
@router.post("/monitoring-tasks/")
async def create_monitoring_task(task_data: MonitoringTaskCreate):
    # 1. 生成任务ID
    task_id = str(uuid.uuid4())

    # 2. 存储任务基本信息
    await storage_service.store_task_basic(task_id, {
        "name": task_data.name,
        "description": task_data.description,
        "status": "created",
        "created_at": datetime.now().isoformat()
    })

    # 3. 存储任务配置
    await storage_service.store_task_config(task_id, task_data.config)

    # 4. 存储调度配置
    await storage_service.store_task_schedule(task_id, task_data.schedule_config)

    # 5. 关联URL
    await storage_service.associate_urls_with_task(task_id, task_data.url_ids)

    return {"task_id": task_id, "status": "created"}
```

##### Redis任务存储结构
```redis
# 任务基本信息
monitoring_tasks:tasks:{task_id}:basic -> Hash {
    "name": "MercadoLibre商品监控",
    "description": "监控电子产品价格变化",
    "status": "active",
    "created_at": "2025-08-09T10:00:00Z",
    "updated_at": "2025-08-09T10:30:00Z"
}

# 任务配置
monitoring_tasks:tasks:{task_id}:config -> Hash {
    "platform": "mercadolibre",
    "concurrent_limit": "2",
    "timeout": "30",
    "retry_count": "3",
    "batch_size": "10"
}

# 调度配置
monitoring_tasks:tasks:{task_id}:schedule -> Hash {
    "schedule_type": "daily",
    "execution_time": "09:00",
    "timezone": "Asia/Shanghai",
    "is_active": "true"
}

# 任务统计
monitoring_tasks:tasks:{task_id}:stats -> Hash {
    "total_runs": "25",
    "success_runs": "23",
    "failed_runs": "2",
    "avg_duration": "45.6",
    "last_run_time": "2025-08-09T09:00:00Z"
}

# 关联的URL
monitoring_tasks:tasks:{task_id}:urls -> Set ["url_001", "url_002", "url_003"]

# 任务索引
monitoring_tasks:indexes:all_task_ids -> Set ["task_001", "task_002"]
monitoring_tasks:indexes:active_tasks -> Set ["task_001"]
monitoring_tasks:indexes:by_platform:mercadolibre -> Set ["task_001"]

# URL反向关联
monitoring_tasks:url_tasks:url_001 -> Set ["task_001", "task_002"]
```

### 2.4 配置管理页面详细分析

#### 2.4.1 爬虫配置页面
**页面路径**: `/config/crawler`
**组件文件**: `frontend/src/pages/Configuration/CrawlerConfiguration.tsx`

##### 主要功能与API调用
| 功能按钮 | API接口 | Redis操作 | 返回数据 |
|---------|---------|-----------|----------|
| **获取配置列表** | `GET /api/v1/crawler-configs/` | 读取`crawler_configs:index` | 爬虫配置列表 |
| **创建配置** | `POST /api/v1/crawler-configs/` | 写入`crawler_configs:{config_id}` | 新配置信息 |
| **更新配置** | `PUT /api/v1/crawler-configs/{id}` | 更新`crawler_configs:{config_id}` | 更新结果 |
| **验证配置** | `POST /api/v1/crawler-configs/{id}/validate` | 读取配置并验证 | 验证结果 |

##### Redis配置存储结构
```redis
# 爬虫配置
crawler_configs:{config_id} -> JSON {
    "name": "MercadoLibre标准配置",
    "browser_config": {
        "headless": true,
        "window_size": "1920x1080",
        "timeout": 30000
    },
    "llm_config": {
        "provider": "openai",
        "model": "gpt-4",
        "api_key": "encrypted_key"
    },
    "crawler_config": {
        "max_depth": 3,
        "delay": 1000,
        "concurrent": 2
    }
}

# 配置索引
crawler_configs:index -> Set ["config_001", "config_002"]
```

#### 2.4.2 后端配置页面
**页面路径**: `/config/backend`
**组件文件**: `frontend/src/pages/Configuration/BackendConfiguration.tsx`

##### 主要功能与API调用
| 功能按钮 | API接口 | Redis操作 | 返回数据 |
|---------|---------|-----------|----------|
| **获取后端配置** | `GET /api/v1/backend-configs/` | 读取`backend_configs:index` | 后端配置列表 |
| **创建后端配置** | `POST /api/v1/backend-configs/` | 写入`backend_configs:{backend_id}` | 新后端配置 |
| **测试连接** | `POST /api/v1/backend-configs/{id}/test-connection` | 读取配置并测试 | 连接测试结果 |
| **获取统计信息** | `GET /api/v1/backend-configs/{id}/stats` | 读取配置统计 | 配置使用统计 |

##### Redis后端配置存储结构
```redis
# 后端配置
backend_configs:{backend_id} -> JSON {
    "name": "外部爬虫API",
    "connection": {
        "host": "api.crawler.com",
        "port": 443,
        "protocol": "https",
        "timeout": 30
    },
    "performance": {
        "max_retries": 3,
        "performance_level": "high"
    },
    "auth": {
        "type": "api_key",
        "credentials": "encrypted_credentials"
    }
}

# 后端配置索引
backend_configs:index -> Set ["backend_001", "backend_002"]
```

### 2.5 Worker管理页面详细分析

#### 2.5.1 页面功能与API调用
**页面路径**: `/worker-management`
**组件文件**: `frontend/src/pages/WorkerManagement/index.tsx`

##### 主要功能与API调用
| 功能按钮 | API接口 | Redis操作 | 返回数据 |
|---------|---------|-----------|----------|
| **获取Worker列表** | `GET /api/v1/crawler-workers/` | 读取`crawler_workers:index` | Worker列表 |
| **创建Worker** | `POST /api/v1/crawler-workers/` | 写入`crawler_workers:{worker_id}` | 新Worker信息 |
| **启动Worker** | `POST /api/v1/crawler-workers/{id}/start` | 更新Worker状态 | 启动结果 |
| **停止Worker** | `POST /api/v1/crawler-workers/{id}/stop` | 更新Worker状态 | 停止结果 |
| **检查兼容性** | `POST /api/v1/crawler-workers/check-compatibility` | 读取配置信息 | 兼容性检查结果 |

##### Worker创建流程
```typescript
// 前端Worker创建 (frontend/src/services/workerApi.ts)
const createWorker = async (workerData: WorkerCreateData) => {
  const response = await fetch('/api/v1/crawler-workers/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: workerData.name,
      crawler_config_id: workerData.crawlerConfigId,
      backend_config_id: workerData.backendConfigId,
      priority: workerData.priority,
      max_concurrent_tasks: workerData.maxConcurrentTasks
    })
  });

  return response.json();
};
```

```python
# 后端Worker创建处理 (backend/app/api/v1/crawler_worker.py)
@router.post("/crawler-workers/")
async def create_crawler_worker(worker_data: CrawlerWorkerCreate):
    # 1. 检查配置兼容性
    compatibility = await check_config_compatibility(
        worker_data.crawler_config_id,
        worker_data.backend_config_id
    )

    if not compatibility.is_compatible:
        raise HTTPException(400, detail="配置不兼容")

    # 2. 创建Worker
    worker_id = str(uuid.uuid4())
    worker = CrawlerWorker(
        id=worker_id,
        name=worker_data.name,
        crawler_config_id=worker_data.crawler_config_id,
        backend_config_id=worker_data.backend_config_id,
        status="inactive",
        priority=worker_data.priority,
        max_concurrent_tasks=worker_data.max_concurrent_tasks
    )

    # 3. 存储到Redis
    await worker_service.create_worker(worker)

    return worker
```

##### Redis Worker存储结构
```redis
# Worker信息
crawler_workers:{worker_id} -> JSON {
    "name": "Worker-001",
    "crawler_config_id": "config_001",
    "backend_config_id": "backend_001",
    "status": "active",
    "priority": 1,
    "max_concurrent_tasks": 5,
    "current_tasks": 2,
    "total_tasks_completed": 150,
    "total_tasks_failed": 5,
    "created_at": "2025-08-09T10:00:00Z",
    "last_active": "2025-08-09T11:30:00Z"
}

# Worker索引
crawler_workers:index -> Set ["worker_001", "worker_002"]
crawler_workers:by_status:active -> Set ["worker_001"]
crawler_workers:by_status:inactive -> Set ["worker_002"]
```

## 3. 任务执行流程详细分析

### 3.1 任务调度流程
```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API
    participant R as Redis
    participant C as Celery
    participant W as Worker
    participant T as TimescaleDB

    F->>A: 启动任务
    A->>R: 更新任务状态为running
    A->>C: 发送Celery任务
    C->>W: 分配给Worker执行
    W->>W: 爬取数据
    W->>T: 存储结果数据
    W->>R: 更新执行统计
    W->>C: 返回执行结果
    C->>A: 通知任务完成
    A->>F: WebSocket推送状态更新
```

### 3.2 任务执行详细步骤

#### 3.2.1 任务启动流程
```python
# 1. 前端触发任务启动
# frontend/src/pages/MonitoringTasks/TaskActions.tsx
const startTask = async (taskId: string) => {
  const response = await fetch(`/api/v1/monitoring-tasks/${taskId}/start`, {
    method: 'POST'
  });
  return response.json();
};

# 2. 后端处理任务启动
# backend/app/api/monitoring_task_routes.py
@router.post("/monitoring-tasks/{task_id}/start")
async def start_monitoring_task(task_id: str):
    # 2.1 更新Redis中的任务状态
    await storage_service.update_task_status(task_id, "running")

    # 2.2 获取任务配置和URL列表
    task_config = await storage_service.get_task_config(task_id)
    url_list = await storage_service.get_task_urls(task_id)

    # 2.3 创建Celery任务
    from app.tasks.crawl_tasks import execute_monitoring_task
    celery_task = execute_monitoring_task.delay(task_id, url_list, task_config)

    # 2.4 记录Celery任务ID
    await storage_service.update_task_celery_id(task_id, celery_task.id)

    return {"status": "started", "celery_task_id": celery_task.id}
```

#### 3.2.2 Celery任务执行流程
```python
# backend/app/tasks/crawl_tasks.py
@celery_app.task(bind=True)
def execute_monitoring_task(self, task_id: str, url_list: List[str], config: dict):
    """执行监控任务"""

    # 3.1 初始化任务执行记录
    execution_id = str(uuid.uuid4())
    start_time = datetime.now()

    # 3.2 更新Redis执行状态
    redis_client.hset(
        f"monitoring_tasks:executions:{execution_id}",
        mapping={
            "task_id": task_id,
            "status": "running",
            "start_time": start_time.isoformat(),
            "total_urls": len(url_list),
            "completed_urls": 0,
            "failed_urls": 0
        }
    )

    # 3.3 批量处理URL
    batch_size = config.get("batch_size", 10)
    for i in range(0, len(url_list), batch_size):
        batch_urls = url_list[i:i + batch_size]

        # 3.4 调用爬虫引擎
        results = crawl_batch_urls(batch_urls, config)

        # 3.5 存储结果到TimescaleDB
        for result in results:
            store_crawl_result(result)

        # 3.6 更新执行进度
        completed_count = i + len(batch_urls)
        redis_client.hset(
            f"monitoring_tasks:executions:{execution_id}",
            "completed_urls", completed_count
        )

        # 3.7 发送WebSocket更新
        send_websocket_update(task_id, {
            "type": "progress_update",
            "completed": completed_count,
            "total": len(url_list)
        })

    # 3.8 完成任务执行
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    # 3.9 更新最终状态
    redis_client.hset(
        f"monitoring_tasks:executions:{execution_id}",
        mapping={
            "status": "completed",
            "end_time": end_time.isoformat(),
            "duration": duration
        }
    )

    # 3.10 更新任务统计
    update_task_statistics(task_id, len(url_list), duration)

    return {"execution_id": execution_id, "status": "completed"}
```

### 3.3 数据存储流程

#### 3.3.1 TimescaleDB数据存储
```python
# backend/app/services/data_storage_service.py
def store_crawl_result(result: CrawlResult):
    """存储爬取结果到TimescaleDB"""

    # 3.3.1 构建产品快照数据
    snapshot_data = {
        "timestamp": result.crawl_time,
        "product_id": result.product_id,
        "url": result.url,
        "name_cn": result.name_cn,
        "name_original": result.name_original,
        "price_current": result.price_current,
        "price_original": result.price_original,
        "discount_percentage": result.discount_percentage,
        "rating": result.rating,
        "reviews_count": result.reviews_count,
        "stock_status": result.stock_status,
        "main_image_url": result.main_image_url,
        "images": json.dumps(result.images),
        "extra_data": json.dumps(result.extra_data),
        "crawl_duration_ms": result.crawl_duration_ms,
        "success": result.success,
        "error_message": result.error_message
    }

    # 3.3.2 插入TimescaleDB
    insert_query = """
    INSERT INTO product_snapshots (
        timestamp, product_id, url, name_cn, name_original,
        price_current, price_original, discount_percentage,
        rating, reviews_count, stock_status, main_image_url,
        images, extra_data, crawl_duration_ms, success, error_message
    ) VALUES (
        %(timestamp)s, %(product_id)s, %(url)s, %(name_cn)s, %(name_original)s,
        %(price_current)s, %(price_original)s, %(discount_percentage)s,
        %(rating)s, %(reviews_count)s, %(stock_status)s, %(main_image_url)s,
        %(images)s, %(extra_data)s, %(crawl_duration_ms)s, %(success)s, %(error_message)s
    )
    """

    with get_db_connection() as conn:
        conn.execute(insert_query, snapshot_data)
        conn.commit()
```

#### 3.3.2 Redis统计更新
```python
# 更新任务统计信息
def update_task_statistics(task_id: str, url_count: int, duration: float):
    """更新任务统计信息"""

    # 获取当前统计
    current_stats = redis_client.hgetall(f"monitoring_tasks:tasks:{task_id}:stats")

    # 计算新的统计值
    total_runs = int(current_stats.get("total_runs", 0)) + 1
    success_runs = int(current_stats.get("success_runs", 0)) + 1
    total_duration = float(current_stats.get("total_duration", 0)) + duration
    avg_duration = total_duration / total_runs

    # 更新统计信息
    redis_client.hset(
        f"monitoring_tasks:tasks:{task_id}:stats",
        mapping={
            "total_runs": total_runs,
            "success_runs": success_runs,
            "failed_runs": int(current_stats.get("failed_runs", 0)),
            "avg_duration": round(avg_duration, 2),
            "last_run_time": datetime.now().isoformat(),
            "total_duration": total_duration
        }
    )
```

## 4. 实时状态更新机制

### 4.1 WebSocket连接管理
```typescript
// frontend/src/services/websocket.ts
class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect() {
    this.ws = new WebSocket('ws://localhost:8000/ws');

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.attemptReconnect();
    };
  }

  handleMessage(data: any) {
    switch (data.type) {
      case 'task_status_update':
        store.dispatch(updateTaskStatus(data.payload));
        break;
      case 'progress_update':
        store.dispatch(updateTaskProgress(data.payload));
        break;
      case 'system_alert':
        store.dispatch(addAlert(data.payload));
        break;
    }
  }
}
```

### 4.2 后端WebSocket处理
```python
# backend/app/websocket/connection_manager.py
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        """广播消息给所有连接的客户端"""
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                # 连接已断开，移除
                self.active_connections.remove(connection)

# 发送任务状态更新
async def send_task_update(task_id: str, status: str, progress: dict = None):
    message = {
        "type": "task_status_update",
        "payload": {
            "task_id": task_id,
            "status": status,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
    }
    await connection_manager.broadcast(message)
```

## 5. 系统监控与告警

### 5.1 系统指标收集
```python
# backend/app/api/monitoring_routes.py
@router.get("/metrics")
async def get_system_metrics():
    """获取系统指标"""

    # 5.1.1 Redis指标收集
    redis_info = await redis_client.info()
    redis_metrics = {
        "memory_usage": redis_info.get("used_memory", 0),
        "memory_peak": redis_info.get("used_memory_peak", 0),
        "connected_clients": redis_info.get("connected_clients", 0),
        "total_commands": redis_info.get("total_commands_processed", 0),
        "keyspace_hits": redis_info.get("keyspace_hits", 0),
        "keyspace_misses": redis_info.get("keyspace_misses", 0)
    }

    # 5.1.2 任务管理器指标
    active_tasks = await redis_client.scard("monitoring_tasks:indexes:active_tasks")
    total_tasks = await redis_client.scard("monitoring_tasks:indexes:all_task_ids")

    taskmanager_metrics = {
        "active_tasks": active_tasks,
        "total_tasks": total_tasks,
        "queue_size": await get_celery_queue_size(),
        "worker_count": await get_active_worker_count()
    }

    # 5.1.3 系统资源指标
    import psutil
    system_metrics = {
        "cpu_usage": psutil.cpu_percent(),
        "memory_usage": psutil.virtual_memory().percent,
        "disk_usage": psutil.disk_usage('/').percent
    }

    return {
        "timestamp": datetime.now().isoformat(),
        "redis_metrics": redis_metrics,
        "taskmanager_metrics": taskmanager_metrics,
        "system_metrics": system_metrics
    }
```

### 5.2 告警规则配置
```redis
# 告警规则存储
alerts:rules -> JSON {
    "high_cpu": {
        "threshold": 80,
        "enabled": true,
        "severity": "warning",
        "message": "CPU使用率过高"
    },
    "high_memory": {
        "threshold": 85,
        "enabled": true,
        "severity": "critical",
        "message": "内存使用率过高"
    },
    "task_failure_rate": {
        "threshold": 5,
        "enabled": true,
        "severity": "warning",
        "message": "任务失败率过高"
    },
    "redis_memory": {
        "threshold": 1073741824,
        "enabled": true,
        "severity": "warning",
        "message": "Redis内存使用过高"
    }
}

# 告警历史记录
alerts:history:{alert_id} -> Hash {
    "rule_name": "high_cpu",
    "triggered_at": "2025-08-09T11:30:00Z",
    "severity": "warning",
    "message": "CPU使用率达到85%",
    "current_value": "85.2",
    "threshold": "80",
    "status": "active"
}
```

### 5.3 前端监控面板
```typescript
// frontend/src/pages/Dashboard/SystemMetrics.tsx
const SystemMetrics: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      const response = await fetch('/api/v1/monitoring/metrics');
      const data = await response.json();
      setMetrics(data);
    };

    // 每30秒更新一次指标
    const interval = setInterval(fetchMetrics, 30000);
    fetchMetrics(); // 立即获取一次

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="system-metrics">
      <Row gutter={16}>
        <Col span={6}>
          <Card title="CPU使用率">
            <Progress
              percent={metrics?.system_metrics.cpu_usage}
              status={metrics?.system_metrics.cpu_usage > 80 ? 'exception' : 'normal'}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card title="内存使用率">
            <Progress
              percent={metrics?.system_metrics.memory_usage}
              status={metrics?.system_metrics.memory_usage > 85 ? 'exception' : 'normal'}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card title="活跃任务">
            <Statistic
              value={metrics?.taskmanager_metrics.active_tasks}
              suffix={`/ ${metrics?.taskmanager_metrics.total_tasks}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card title="队列大小">
            <Statistic value={metrics?.taskmanager_metrics.queue_size} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
```

## 6. 数据分析页面详细分析

### 6.1 页面功能与API调用
**页面路径**: `/data-analysis`
**组件文件**: `frontend/src/pages/DataAnalysis/index.tsx`

##### 主要功能与API调用
| 功能按钮 | API接口 | 数据源 | 返回数据 |
|---------|---------|--------|----------|
| **获取产品快照** | `GET /api/v1/product-snapshots/` | TimescaleDB | 产品历史数据 |
| **价格趋势分析** | `GET /api/v1/product-trends/` | TimescaleDB | 价格变化趋势 |
| **数据导出** | `GET /api/v1/export/` | TimescaleDB | Excel/CSV文件 |
| **统计报表** | `GET /api/v1/analytics/summary/` | TimescaleDB聚合查询 | 统计摘要 |

### 6.2 TimescaleDB查询优化
```sql
-- 6.2.1 价格趋势查询（优化版）
SELECT
    time_bucket('1 hour', timestamp) AS hour,
    url,
    avg(price_current) as avg_price,
    min(price_current) as min_price,
    max(price_current) as max_price,
    count(*) as sample_count
FROM product_snapshots
WHERE url = $1
    AND timestamp >= $2
    AND timestamp <= $3
    AND success = true
GROUP BY hour, url
ORDER BY hour;

-- 6.2.2 商品排行查询
SELECT
    url,
    name_cn,
    avg(price_current) as avg_price,
    avg(rating) as avg_rating,
    count(*) as crawl_count,
    max(timestamp) as last_crawl
FROM product_snapshots
WHERE timestamp >= NOW() - INTERVAL '7 days'
    AND success = true
GROUP BY url, name_cn
ORDER BY avg_rating DESC, crawl_count DESC
LIMIT 50;
```

## 7. 系统性能优化策略

### 7.1 Redis性能优化
```python
# 7.1.1 批量操作优化
async def batch_update_urls(url_updates: List[dict]):
    """批量更新URL状态"""
    pipe = redis_client.pipeline()

    for update in url_updates:
        url_id = update['url_id']
        # 批量更新基本信息
        pipe.hset(f"url_pool:urls:{url_id}:basic", mapping=update['basic'])
        # 批量更新统计信息
        pipe.hset(f"url_pool:urls:{url_id}:stats", mapping=update['stats'])

    # 一次性执行所有操作
    await pipe.execute()

# 7.1.2 缓存策略优化
class CacheManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.cache_ttl = {
            'task_list': 300,      # 任务列表缓存5分钟
            'url_list': 600,       # URL列表缓存10分钟
            'config_list': 1800,   # 配置列表缓存30分钟
            'system_metrics': 60   # 系统指标缓存1分钟
        }

    async def get_cached_data(self, key: str, data_type: str):
        """获取缓存数据"""
        cached = await self.redis.get(f"cache:{key}")
        if cached:
            return json.loads(cached)
        return None

    async def set_cached_data(self, key: str, data: any, data_type: str):
        """设置缓存数据"""
        ttl = self.cache_ttl.get(data_type, 300)
        await self.redis.setex(
            f"cache:{key}",
            ttl,
            json.dumps(data, default=str)
        )
```

### 7.2 API响应优化
```python
# 7.2.1 分页查询优化
@router.get("/url-pool/")
async def get_url_pool(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    platform: Optional[str] = None,
    status: Optional[str] = None
):
    # 构建缓存键
    cache_key = f"url_pool:page:{page}:size:{page_size}:platform:{platform}:status:{status}"

    # 尝试从缓存获取
    cached_result = await cache_manager.get_cached_data(cache_key, 'url_list')
    if cached_result:
        return cached_result

    # 查询数据
    result = await url_pool_service.get_paginated_urls(
        page=page,
        page_size=page_size,
        platform=platform,
        status=status
    )

    # 缓存结果
    await cache_manager.set_cached_data(cache_key, result, 'url_list')

    return result
```

## 8. 总结

### 8.1 系统架构优势
MonIt系统通过统一的Redis存储架构和清晰的数据流设计，实现了以下核心优势：

1. **数据一致性**: 统一Redis存储消除了数据不一致问题
2. **高性能**: 分层存储结构和缓存策略保证了系统响应速度
3. **实时性**: WebSocket机制实现了前端状态的实时更新
4. **可扩展性**: 模块化设计支持系统的水平和垂直扩展
5. **可维护性**: 清晰的API接口和数据结构便于系统维护

### 8.2 关键技术特点
- **统一数据源**: Redis作为唯一的任务和配置存储
- **分层存储**: 基本信息、配置、统计、索引的分层管理
- **异步处理**: FastAPI + Celery的异步任务处理架构
- **实时同步**: WebSocket实现的实时状态更新机制
- **时序存储**: TimescaleDB支持的历史数据分析

### 8.3 数据流特征
- **单向数据流**: 从输入到存储到展示的清晰流向
- **事件驱动**: 基于事件的异步处理和状态更新
- **批量处理**: 支持大规模URL的批量操作和处理
- **智能缓存**: 多层缓存机制提升系统性能
- **完整监控**: 全方位的系统监控和告警机制

这份详细的工作流和数据流分析文档为MonIt系统的开发、维护和优化提供了完整的技术参考。

## 9. Celery任务执行与监控任务关联详细分析

### 9.1 任务执行关联的Redis存储结构

您提到的问题非常重要！确实，我之前遗漏了Celery任务执行和监控任务关联的关键部分。让我详细补充：

#### 9.1.1 任务执行记录存储
```redis
# 任务执行记录 (短期存储，1-24小时)
task_execution:{execution_id} -> JSON {
    "execution_id": "exec_task_001_1691568000",
    "task_id": "monitoring_task_001",
    "status": "running",
    "started_at": "2025-08-09T09:00:00Z",
    "completed_at": null,
    "execution_type": "scheduled",
    "celery_task_ids": ["celery_001", "celery_002"],
    "submission_id": "submission_123",
    "urls_count": 50,
    "platform": "mercadolibre"
}

# Celery任务与监控任务的关联
celery_task_mapping:{celery_task_id} -> JSON {
    "celery_task_id": "celery_001",
    "monitoring_task_id": "monitoring_task_001",
    "execution_id": "exec_task_001_1691568000",
    "batch_id": "batch_001",
    "urls": ["url_001", "url_002", "url_003"],
    "status": "PENDING",
    "created_at": "2025-08-09T09:00:00Z",
    "started_at": null,
    "completed_at": null
}

# 监控任务的活跃执行
monitoring_tasks:active_executions:{task_id} -> Set ["exec_task_001_1691568000", "exec_task_001_1691571600"]

# TaskManager提交记录
task_manager:submissions:{submission_id} -> JSON {
    "submission_id": "submission_123",
    "monitoring_task_id": "monitoring_task_001",
    "urls": ["url_001", "url_002", "url_003"],
    "platform": "mercadolibre",
    "priority": "NORMAL",
    "submitted_at": "2025-08-09T09:00:00Z",
    "batch_ids": ["batch_001", "batch_002"],
    "status": "processing"
}

# 批次执行状态
task_batches:{batch_id} -> JSON {
    "batch_id": "batch_001",
    "monitoring_task_id": "monitoring_task_001",
    "execution_id": "exec_task_001_1691568000",
    "celery_task_id": "celery_001",
    "urls": ["url_001", "url_002", "url_003"],
    "status": "running",
    "started_at": "2025-08-09T09:00:15Z",
    "platform": "mercadolibre",
    "batch_size": 3
}
```

### 9.2 任务执行流程中的Redis操作详细分析

#### 9.2.1 监控任务启动时的Redis操作
```python
# backend/app/tasks/monitoring_executor.py
async def _update_task_execution_start(self, execution_id: str):
    """更新任务执行开始状态"""

    # 1. 更新监控任务状态
    await self.task_storage.update_task(self.task_id, {
        "is_running": True,
        "last_run": now,
        "updated_at": now
    })
    # Redis操作: HSET monitoring_tasks:tasks:{task_id}:basic is_running true

    # 2. 创建执行记录
    execution_record = {
        "execution_id": execution_id,
        "task_id": self.task_id,
        "status": "running",
        "started_at": now,
        "execution_type": "scheduled"
    }
    await self.redis_client.setex(f"task_execution:{execution_id}", 3600, str(execution_record))

    # 3. 添加到活跃执行集合
    await self.redis_client.sadd(f"monitoring_tasks:active_executions:{self.task_id}", execution_id)
```

#### 9.2.2 TaskManager提交任务时的Redis操作
```python
# backend/app/core/task_manager.py
async def submit_task(self, task_id: str, urls: List[str], platform: str, priority: TaskPriority, options: Dict[str, Any]):
    """提交任务到TaskManager"""

    submission_id = str(uuid.uuid4())

    # 1. 记录提交信息
    submission_record = {
        "submission_id": submission_id,
        "monitoring_task_id": task_id,
        "urls": urls,
        "platform": platform,
        "priority": priority.value,
        "submitted_at": datetime.now().isoformat(),
        "status": "submitted",
        "options": options
    }
    await self.redis_client.setex(f"task_manager:submissions:{submission_id}", 7200, json.dumps(submission_record))

    # 2. 任务分片处理
    batches = self.task_splitter.split_task(urls, platform, priority)
    batch_ids = []

    for batch in batches:
        batch_id = str(uuid.uuid4())
        batch_ids.append(batch_id)

        # 3. 提交Celery任务
        celery_task = crawl_batch_task.delay(
            batch_id=batch_id,
            urls=batch.urls,
            platform=platform,
            task_id=task_id,  # 监控任务ID
            options={
                **options,
                "submission_id": submission_id,
                "batch_id": batch_id
            }
        )

        # 4. 记录批次信息
        batch_record = {
            "batch_id": batch_id,
            "monitoring_task_id": task_id,
            "execution_id": options.get("execution_id"),
            "celery_task_id": celery_task.id,
            "urls": batch.urls,
            "status": "submitted",
            "submitted_at": datetime.now().isoformat(),
            "platform": platform,
            "batch_size": len(batch.urls)
        }
        await self.redis_client.setex(f"task_batches:{batch_id}", 7200, json.dumps(batch_record))

        # 5. 记录Celery任务映射
        celery_mapping = {
            "celery_task_id": celery_task.id,
            "monitoring_task_id": task_id,
            "execution_id": options.get("execution_id"),
            "batch_id": batch_id,
            "urls": batch.urls,
            "status": "PENDING",
            "created_at": datetime.now().isoformat()
        }
        await self.redis_client.setex(f"celery_task_mapping:{celery_task.id}", 7200, json.dumps(celery_mapping))

    # 6. 更新提交记录
    submission_record["batch_ids"] = batch_ids
    submission_record["status"] = "processing"
    await self.redis_client.setex(f"task_manager:submissions:{submission_id}", 7200, json.dumps(submission_record))

    return submission_id
```

#### 9.2.3 Celery任务执行时的Redis操作
```python
# backend/app/tasks/crawl_batch.py
@celery_app.task(bind=True)
def crawl_batch_task(self, batch_id: str, urls: List[str], platform: str, task_id: str, options: Dict[str, Any]):
    """批量爬取任务"""

    celery_task_id = self.request.id

    # 1. 更新Celery任务状态为STARTED
    celery_mapping = {
        "celery_task_id": celery_task_id,
        "monitoring_task_id": task_id,
        "execution_id": options.get("execution_id"),
        "batch_id": batch_id,
        "urls": urls,
        "status": "STARTED",
        "started_at": datetime.now().isoformat()
    }
    redis_client.setex(f"celery_task_mapping:{celery_task_id}", 7200, json.dumps(celery_mapping))

    # 2. 更新批次状态
    batch_record = json.loads(redis_client.get(f"task_batches:{batch_id}"))
    batch_record["status"] = "running"
    batch_record["started_at"] = datetime.now().isoformat()
    redis_client.setex(f"task_batches:{batch_id}", 7200, json.dumps(batch_record))

    try:
        # 3. 执行爬取逻辑
        results = perform_crawling(urls, platform, options)

        # 4. 更新成功状态
        celery_mapping["status"] = "SUCCESS"
        celery_mapping["completed_at"] = datetime.now().isoformat()
        celery_mapping["results"] = results
        redis_client.setex(f"celery_task_mapping:{celery_task_id}", 7200, json.dumps(celery_mapping))

        batch_record["status"] = "completed"
        batch_record["completed_at"] = datetime.now().isoformat()
        batch_record["results"] = results
        redis_client.setex(f"task_batches:{batch_id}", 7200, json.dumps(batch_record))

        return results

    except Exception as e:
        # 5. 更新失败状态
        celery_mapping["status"] = "FAILURE"
        celery_mapping["completed_at"] = datetime.now().isoformat()
        celery_mapping["error"] = str(e)
        redis_client.setex(f"celery_task_mapping:{celery_task_id}", 7200, json.dumps(celery_mapping))

        batch_record["status"] = "failed"
        batch_record["completed_at"] = datetime.now().isoformat()
        batch_record["error"] = str(e)
        redis_client.setex(f"task_batches:{batch_id}", 7200, json.dumps(batch_record))

        raise
```

### 9.3 任务状态查询API

#### 9.3.1 获取任务执行状态
```python
# backend/app/api/monitoring_task_routes.py
@router.get("/monitoring-tasks/{task_id}/executions")
async def get_task_executions(task_id: str):
    """获取任务执行历史"""

    # 1. 获取活跃执行
    active_executions = await redis_client.smembers(f"monitoring_tasks:active_executions:{task_id}")

    executions = []
    for execution_id in active_executions:
        execution_data = await redis_client.get(f"task_execution:{execution_id}")
        if execution_data:
            execution = json.loads(execution_data)

            # 2. 获取关联的Celery任务状态
            celery_tasks = []
            # 查找所有相关的批次
            batch_keys = await redis_client.keys(f"task_batches:*")
            for batch_key in batch_keys:
                batch_data = json.loads(await redis_client.get(batch_key))
                if batch_data.get("execution_id") == execution_id:
                    celery_task_id = batch_data.get("celery_task_id")
                    if celery_task_id:
                        celery_mapping = await redis_client.get(f"celery_task_mapping:{celery_task_id}")
                        if celery_mapping:
                            celery_tasks.append(json.loads(celery_mapping))

            execution["celery_tasks"] = celery_tasks
            executions.append(execution)

    return {"executions": executions}

@router.get("/monitoring-tasks/{task_id}/current-status")
async def get_task_current_status(task_id: str):
    """获取任务当前状态"""

    # 1. 获取基本任务信息
    task_data = await task_storage.get_task(task_id)

    # 2. 获取当前执行状态
    active_executions = await redis_client.smembers(f"monitoring_tasks:active_executions:{task_id}")

    current_execution = None
    if active_executions:
        # 获取最新的执行
        latest_execution_id = max(active_executions)
        execution_data = await redis_client.get(f"task_execution:{latest_execution_id}")
        if execution_data:
            current_execution = json.loads(execution_data)

            # 获取执行进度
            progress = await get_execution_progress(latest_execution_id)
            current_execution["progress"] = progress

    return {
        "task_id": task_id,
        "task_data": task_data,
        "current_execution": current_execution,
        "is_running": task_data.get("is_running", False)
    }

async def get_execution_progress(execution_id: str):
    """获取执行进度"""

    # 查找所有相关批次
    batch_keys = await redis_client.keys(f"task_batches:*")
    total_batches = 0
    completed_batches = 0
    failed_batches = 0
    total_urls = 0
    processed_urls = 0

    for batch_key in batch_keys:
        batch_data = json.loads(await redis_client.get(batch_key))
        if batch_data.get("execution_id") == execution_id:
            total_batches += 1
            total_urls += batch_data.get("batch_size", 0)

            status = batch_data.get("status")
            if status == "completed":
                completed_batches += 1
                processed_urls += batch_data.get("batch_size", 0)
            elif status == "failed":
                failed_batches += 1

    return {
        "total_batches": total_batches,
        "completed_batches": completed_batches,
        "failed_batches": failed_batches,
        "running_batches": total_batches - completed_batches - failed_batches,
        "total_urls": total_urls,
        "processed_urls": processed_urls,
        "progress_percentage": (processed_urls / total_urls * 100) if total_urls > 0 else 0
    }
```

### 9.4 前端实时状态显示

#### 9.4.1 任务执行状态组件
```typescript
// frontend/src/components/TaskExecutionStatus.tsx
const TaskExecutionStatus: React.FC<{taskId: string}> = ({ taskId }) => {
  const [executionStatus, setExecutionStatus] = useState<any>(null);

  useEffect(() => {
    const fetchStatus = async () => {
      const response = await fetch(`/api/v1/monitoring-tasks/${taskId}/current-status`);
      const data = await response.json();
      setExecutionStatus(data);
    };

    // 每5秒更新一次状态
    const interval = setInterval(fetchStatus, 5000);
    fetchStatus();

    return () => clearInterval(interval);
  }, [taskId]);

  if (!executionStatus?.current_execution) {
    return <Tag color="default">未运行</Tag>;
  }

  const { current_execution } = executionStatus;
  const progress = current_execution.progress || {};

  return (
    <div className="task-execution-status">
      <div className="execution-info">
        <Tag color={current_execution.status === 'running' ? 'processing' : 'success'}>
          {current_execution.status === 'running' ? '执行中' : '已完成'}
        </Tag>
        <span>执行ID: {current_execution.execution_id}</span>
      </div>

      {current_execution.status === 'running' && (
        <div className="progress-info">
          <Progress
            percent={progress.progress_percentage || 0}
            format={() => `${progress.processed_urls || 0}/${progress.total_urls || 0}`}
          />
          <div className="batch-status">
            <span>批次进度: {progress.completed_batches || 0}/{progress.total_batches || 0}</span>
            {progress.failed_batches > 0 && (
              <span style={{color: 'red'}}>失败: {progress.failed_batches}</span>
            )}
          </div>
        </div>
      )}

      {/* Celery任务详情 */}
      {current_execution.celery_tasks && (
        <Collapse size="small">
          <Panel header="Celery任务详情" key="celery">
            {current_execution.celery_tasks.map((celeryTask: any) => (
              <div key={celeryTask.celery_task_id} className="celery-task-item">
                <Tag color={getStatusColor(celeryTask.status)}>{celeryTask.status}</Tag>
                <span>任务ID: {celeryTask.celery_task_id}</span>
                <span>批次: {celeryTask.batch_id}</span>
                <span>URL数量: {celeryTask.urls?.length || 0}</span>
              </div>
            ))}
          </Panel>
        </Collapse>
      )}
    </div>
  );
};
```

这样就完整地建立了监控任务、TaskManager提交、Celery任务执行之间的关联关系，通过Redis实现了完整的状态跟踪和进度监控。
