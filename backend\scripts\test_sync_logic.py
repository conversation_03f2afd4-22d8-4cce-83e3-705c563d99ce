#!/usr/bin/env python3
"""
测试前端同步逻辑
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_sync_logic():
    """测试同步逻辑"""
    
    print("🧪 测试前端同步逻辑...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 获取后端配置详情
            response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            
            if response.status_code == 200:
                configs = response.json()
                if configs:
                    config_summary = configs[0]
                    backend_id = config_summary['backend_id']
                    
                    # 获取详细配置
                    detail_response = await client.get(f"http://localhost:8000/api/v1/backend-configs/{backend_id}")
                    
                    if detail_response.status_code == 200:
                        config_detail = detail_response.json()
                        print(f"✅ 获取到配置详情: {config_detail['backend_name']}")
                        
                        # 模拟前端同步逻辑
                        performance_level = config_detail.get('performance_level', 'medium')
                        perf_config = config_detail.get('performance_config', {})
                        
                        print(f"📋 配置信息:")
                        print(f"   - 性能级别: {performance_level}")
                        print(f"   - 性能配置: {json.dumps(perf_config, indent=2, ensure_ascii=False)}")
                        
                        # 基于性能级别设置基础值
                        if performance_level == 'high':
                            base_max_concurrent = 10
                            base_allocated_concurrent = 8
                        elif performance_level == 'medium':
                            base_max_concurrent = 5
                            base_allocated_concurrent = 3
                        else:  # low
                            base_max_concurrent = 2
                            base_allocated_concurrent = 1
                        
                        print(f"\n🎯 同步计算过程:")
                        print(f"   1. 基于性能级别 '{performance_level}' 的基础值:")
                        print(f"      - 基础最大并发: {base_max_concurrent}")
                        print(f"      - 基础分配并发: {base_allocated_concurrent}")
                        
                        # 考虑后端配置的实际限制
                        backend_max_concurrent = perf_config.get('max_concurrent_tasks')
                        if backend_max_concurrent and backend_max_concurrent > 0:
                            final_max_concurrent = min(base_max_concurrent, backend_max_concurrent)
                            print(f"   2. 考虑后端限制 ({backend_max_concurrent}) 后:")
                            print(f"      - 调整后最大并发: {final_max_concurrent}")
                        else:
                            final_max_concurrent = base_max_concurrent
                            print(f"   2. 没有后端限制，使用基础值: {final_max_concurrent}")
                        
                        # 考虑内存限制
                        memory_limit = perf_config.get('memory_limit_mb')
                        if memory_limit and memory_limit > 0:
                            if memory_limit < 512:
                                adjusted_max_concurrent = max(1, int(final_max_concurrent * 0.6))
                                print(f"   3. 低内存环境 ({memory_limit}MB)，调整为: {adjusted_max_concurrent}")
                            elif memory_limit > 2048:
                                adjusted_max_concurrent = int(final_max_concurrent * 1.2)
                                print(f"   3. 高内存环境 ({memory_limit}MB)，调整为: {adjusted_max_concurrent}")
                            else:
                                adjusted_max_concurrent = final_max_concurrent
                                print(f"   3. 中等内存环境 ({memory_limit}MB)，保持: {adjusted_max_concurrent}")
                        else:
                            adjusted_max_concurrent = final_max_concurrent
                            print(f"   3. 没有内存限制信息，保持: {adjusted_max_concurrent}")
                        
                        # 最终分配并发数
                        final_allocated_concurrent = min(base_allocated_concurrent, max(1, adjusted_max_concurrent - 1))
                        
                        print(f"\n✅ 最终同步结果:")
                        print(f"   - 最大并发任务数: {adjusted_max_concurrent}")
                        print(f"   - 分配并发数: {final_allocated_concurrent}")
                        
                        print(f"\n📝 前端应该设置的表单值:")
                        print(f"   form.setFieldsValue({{")
                        print(f"     max_concurrent_tasks: {adjusted_max_concurrent},")
                        print(f"     allocated_concurrent: {final_allocated_concurrent},")
                        print(f"   }});")
                        
                    else:
                        print(f"❌ 获取配置详情失败: {detail_response.status_code}")
                        
                else:
                    print(f"⚠️  没有后端配置数据")
            else:
                print(f"❌ 获取配置列表失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_sync_logic()

if __name__ == "__main__":
    asyncio.run(main())
