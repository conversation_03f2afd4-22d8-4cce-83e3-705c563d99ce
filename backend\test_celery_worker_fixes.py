#!/usr/bin/env python3
"""
测试Celery Worker修复效果
验证监控任务UUID显示、Worker信息获取、请求载荷格式、任务关联信息等修复
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any

# 测试配置
TEST_MONITORING_TASK_ID = str(uuid.uuid4())
TEST_URL = "https://articulo.mercadolibre.com.mx/MLM-3406662550-test-product"
TEST_PLATFORM = "mercadolibre"
TEST_WORKER_ID = "crawler_worker_test"
TEST_BATCH_ID = "test_batch_001"


async def test_task_relation_service():
    """测试任务关联服务"""
    print("🔍 测试任务关联服务...")

    try:
        # 使用本地Redis连接
        from app.services.task_relation_service import TaskRelationService
        task_relation_service = TaskRelationService(redis_url="redis://localhost:6379/0")
        
        # 创建测试关联
        celery_task_id = f"celery_test_{int(datetime.now().timestamp())}"
        
        relation = await task_relation_service.create_task_relation(
            monitoring_task_id=TEST_MONITORING_TASK_ID,
            celery_task_id=celery_task_id,
            worker_id=TEST_WORKER_ID,
            url=TEST_URL,
            batch_id=TEST_BATCH_ID
        )
        
        print(f"✅ 创建任务关联成功: {relation.monitoring_task_id} -> {relation.celery_task_id}")
        
        # 测试查询功能
        retrieved_relation = await task_relation_service.get_task_relation_by_celery_id(celery_task_id)
        if retrieved_relation:
            print(f"✅ 通过Celery任务ID查询成功: {retrieved_relation.monitoring_task_id}")
        else:
            print("❌ 通过Celery任务ID查询失败")
        
        # 测试监控任务关联查询
        monitoring_relations = await task_relation_service.get_monitoring_task_relations(TEST_MONITORING_TASK_ID)
        if monitoring_relations:
            print(f"✅ 监控任务关联查询成功: {len(monitoring_relations.celery_task_ids)} 个Celery任务")
        else:
            print("❌ 监控任务关联查询失败")
        
        # 测试Worker任务查询
        worker_tasks = await task_relation_service.get_worker_tasks(TEST_WORKER_ID)
        print(f"✅ Worker任务查询: {len(worker_tasks)} 个任务")
        
        # 测试URL任务查询
        url_tasks = await task_relation_service.get_url_tasks(TEST_URL)
        print(f"✅ URL任务查询: {len(url_tasks)} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务关联服务测试失败: {e}")
        return False


def test_crawler_api_payload_format():
    """测试爬虫API载荷格式"""
    print("\n🔍 测试爬虫API载荷格式...")

    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        from crawler.api_client.crawler_api_client import CrawlerAPIClient
        from crawler.config.api_config import APIConfig
        
        # 创建API客户端
        config = APIConfig(base_url="http://test.example.com")
        client = CrawlerAPIClient(config)
        
        # 测试载荷生成
        test_urls = [TEST_URL]
        test_options = {
            "monitoring_task_id": TEST_MONITORING_TASK_ID,
            "assigned_worker": TEST_WORKER_ID
        }
        
        payload = client._create_worker_api_payload(test_urls, TEST_PLATFORM, test_options)
        
        # 验证载荷格式
        required_fields = ["urls", "browser_config", "crawler_config"]
        for field in required_fields:
            if field not in payload:
                print(f"❌ 载荷缺少必需字段: {field}")
                return False
        
        # 验证browser_config
        browser_config = payload["browser_config"]
        if not isinstance(browser_config, dict) or "headless" not in browser_config:
            print("❌ browser_config格式不正确")
            return False
        
        # 验证crawler_config
        crawler_config = payload["crawler_config"]
        required_crawler_fields = ["extraction_strategy", "llm_extraction", "schema_extraction"]
        for field in required_crawler_fields:
            if field not in crawler_config:
                print(f"❌ crawler_config缺少字段: {field}")
                return False
        
        # 验证schema_extraction
        schema_extraction = crawler_config["schema_extraction"]
        if "schema" not in schema_extraction or "instructions" not in schema_extraction:
            print("❌ schema_extraction配置不完整")
            return False
        
        print("✅ 载荷格式验证通过")
        print(f"✅ URLs: {len(payload['urls'])} 个")
        print(f"✅ Browser配置: {len(browser_config)} 个字段")
        print(f"✅ Crawler配置: {len(crawler_config)} 个字段")
        print(f"✅ Schema类型: {schema_extraction['schema'].get('type', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 载荷格式测试失败: {e}")
        return False


def test_monitoring_task_id_logic():
    """测试监控任务ID获取逻辑"""
    print("\n🔍 测试监控任务ID获取逻辑...")
    
    try:
        # 模拟不同的options情况
        test_cases = [
            {
                "name": "有monitoring_task_id的options",
                "options": {"monitoring_task_id": TEST_MONITORING_TASK_ID, "other": "value"},
                "task_id": "12345",
                "expected": TEST_MONITORING_TASK_ID
            },
            {
                "name": "没有monitoring_task_id的options",
                "options": {"other": "value"},
                "task_id": TEST_MONITORING_TASK_ID,
                "expected": TEST_MONITORING_TASK_ID
            },
            {
                "name": "空options",
                "options": None,
                "task_id": TEST_MONITORING_TASK_ID,
                "expected": TEST_MONITORING_TASK_ID
            },
            {
                "name": "空task_id和options",
                "options": None,
                "task_id": None,
                "expected": "batch_test_batch_001"
            }
        ]
        
        for case in test_cases:
            # 模拟crawl_batch.py中的逻辑
            options = case["options"]
            task_id = case["task_id"]
            batch_id = "test_batch_001"
            
            # 复制修复后的逻辑
            monitoring_task_id = None
            if options and "monitoring_task_id" in options:
                monitoring_task_id = options["monitoring_task_id"]
            elif task_id:
                monitoring_task_id = str(task_id)
            else:
                monitoring_task_id = f"batch_{batch_id}"
            
            if monitoring_task_id == case["expected"]:
                print(f"✅ {case['name']}: {monitoring_task_id}")
            else:
                print(f"❌ {case['name']}: 期望 {case['expected']}, 实际 {monitoring_task_id}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 监控任务ID逻辑测试失败: {e}")
        return False


def test_worker_info_fallback_logic():
    """测试Worker信息获取fallback逻辑"""
    print("\n🔍 测试Worker信息获取fallback逻辑...")
    
    try:
        # 模拟不同的Worker信息获取情况
        test_cases = [
            {
                "name": "从options获取assigned_worker",
                "options": {"assigned_worker": "specific_worker"},
                "expected": "specific_worker"
            },
            {
                "name": "从options获取crawler_backend",
                "options": {"crawler_backend": {"name": "backend_worker"}},
                "expected": "backend_worker"
            },
            {
                "name": "使用默认Worker命名",
                "options": {},
                "expected": f"crawler_worker_{TEST_BATCH_ID}"
            }
        ]
        
        for case in test_cases:
            options = case["options"]
            batch_id = TEST_BATCH_ID
            
            # 模拟修复后的Worker获取逻辑
            actual_worker_name = "unknown"
            
            if options and "assigned_worker" in options:
                actual_worker_name = options["assigned_worker"]
            elif options and "crawler_backend" in options:
                backend = options["crawler_backend"]
                actual_worker_name = backend.get("name", "crawler_backend")
            else:
                actual_worker_name = f"crawler_worker_{batch_id}"
            
            if actual_worker_name == case["expected"]:
                print(f"✅ {case['name']}: {actual_worker_name}")
            else:
                print(f"❌ {case['name']}: 期望 {case['expected']}, 实际 {actual_worker_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Worker信息获取逻辑测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试Celery Worker修复效果\n")
    
    test_results = []
    
    # 测试1: 监控任务ID获取逻辑
    result1 = test_monitoring_task_id_logic()
    test_results.append(("监控任务ID获取逻辑", result1))
    
    # 测试2: Worker信息获取逻辑
    result2 = test_worker_info_fallback_logic()
    test_results.append(("Worker信息获取逻辑", result2))
    
    # 测试3: 载荷格式
    result3 = test_crawler_api_payload_format()
    test_results.append(("载荷格式", result3))
    
    # 测试4: 任务关联服务
    result4 = await test_task_relation_service()
    test_results.append(("任务关联服务", result4))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Celery Worker修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
