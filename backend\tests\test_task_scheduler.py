"""
任务调度系统测试

测试任务分片、限流控制和任务管理器的功能
"""

import asyncio
import pytest
import logging
from typing import List, Dict, Any
from datetime import datetime

from app.core.task_splitter import TaskSplitter, TaskPriority, SplitterConfig
from app.core.rate_limiter import RateLimiter, RateLimitConfig
from app.core.task_manager import TaskManager, TaskManagerConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestTaskSplitter:
    """测试任务分片器"""
    
    def test_basic_splitting(self):
        """测试基础分片功能"""
        splitter = TaskSplitter()
        
        # 测试100个URL分片
        urls = [f"https://example.com/product/{i}" for i in range(100)]
        batches = splitter.split_urls(urls, platform="mercadolibre")
        
        assert len(batches) == 1  # 100个URL应该分成1个批次
        assert len(batches[0].urls) == 100
        assert batches[0].platform == "mercadolibre"
        assert batches[0].priority == TaskPriority.NORMAL
    
    def test_large_task_splitting(self):
        """测试大任务分片"""
        config = SplitterConfig(max_batch_size=50)
        splitter = TaskSplitter(config)
        
        # 测试1000个URL分片
        urls = [f"https://example.com/product/{i}" for i in range(1000)]
        batches = splitter.split_urls(urls, platform="mercadolibre", priority=TaskPriority.HIGH)
        
        assert len(batches) == 20  # 1000个URL应该分成20个批次
        assert all(len(batch.urls) == 50 for batch in batches)
        assert all(batch.priority == TaskPriority.HIGH for batch in batches)
    
    def test_priority_calculation(self):
        """测试优先级计算"""
        splitter = TaskSplitter()
        
        # 测试不同优先级的分数
        now = datetime.now()
        
        urgent_score = splitter.calculate_priority_score(TaskPriority.URGENT, now, 50)
        high_score = splitter.calculate_priority_score(TaskPriority.HIGH, now, 50)
        normal_score = splitter.calculate_priority_score(TaskPriority.NORMAL, now, 50)
        low_score = splitter.calculate_priority_score(TaskPriority.LOW, now, 50)
        
        assert urgent_score > high_score > normal_score > low_score


class TestRateLimiter:
    """测试限流控制器"""
    
    @pytest.mark.asyncio
    async def test_concurrent_limit(self):
        """测试并发限制"""
        config = RateLimitConfig(max_concurrent_requests=2)
        limiter = RateLimiter(config)
        
        try:
            await limiter.start()
            
            # 获取2个许可应该成功
            assert await limiter.acquire("req1") == True
            assert await limiter.acquire("req2") == True
            
            # 第3个许可应该失败
            assert await limiter.acquire("req3") == False
            
            # 释放一个许可后应该可以获取新的
            await limiter.release("req1", True, 1.0)
            assert await limiter.acquire("req4") == True
            
        finally:
            await limiter.stop()
    
    @pytest.mark.asyncio
    async def test_circuit_breaker(self):
        """测试熔断器"""
        config = RateLimitConfig(
            max_concurrent_requests=10,
            failure_threshold=3,
            failure_rate_threshold=0.5
        )
        limiter = RateLimiter(config)
        
        try:
            await limiter.start()
            
            # 模拟一些失败请求
            for i in range(5):
                await limiter.acquire(f"req{i}")
                await limiter.release(f"req{i}", False, 1.0)  # 失败
            
            # 检查熔断器状态
            metrics = limiter.get_metrics()
            logger.info(f"Circuit breaker state: {metrics['circuit_state']}")
            
        finally:
            await limiter.stop()


async def test_task_manager_integration():
    """测试任务管理器集成"""
    
    # 配置较小的批次大小以便测试
    splitter_config = SplitterConfig(max_batch_size=10, min_batch_size=5)
    rate_limiter_config = RateLimitConfig(max_concurrent_requests=2)
    manager_config = TaskManagerConfig(
        max_concurrent_batches=2,
        batch_check_interval=1.0,
        splitter_config=splitter_config,
        rate_limiter_config=rate_limiter_config
    )
    
    manager = TaskManager(manager_config)
    
    try:
        await manager.start()
        logger.info("Task manager started")
        
        # 提交一个测试任务
        test_urls = [f"https://mercadolibre.com.ar/product/{i}" for i in range(25)]
        
        submission_id = await manager.submit_task(
            task_id=1,
            urls=test_urls,
            platform="mercadolibre",
            priority=TaskPriority.NORMAL
        )
        
        logger.info(f"Task submitted with ID: {submission_id}")
        
        # 等待一段时间让任务处理
        await asyncio.sleep(10)
        
        # 检查状态
        status = manager.get_status()
        logger.info(f"Manager status: {status}")
        
        # 提交单URL任务
        single_task_id = await manager.submit_single_url(
            url="https://mercadolibre.com.ar/product/test",
            platform="mercadolibre",
            priority=TaskPriority.HIGH
        )
        
        logger.info(f"Single URL task submitted: {single_task_id}")
        
        # 再等待一段时间
        await asyncio.sleep(5)
        
        final_status = manager.get_status()
        logger.info(f"Final status: {final_status}")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise
    finally:
        await manager.stop()
        logger.info("Task manager stopped")


async def test_stress_scenario():
    """压力测试场景：模拟1000+URL监控任务"""
    
    logger.info("Starting stress test with 1000+ URLs")
    
    # 配置适合压力测试的参数
    splitter_config = SplitterConfig(
        max_batch_size=100,
        min_batch_size=50,
        adaptive_sizing=True
    )
    rate_limiter_config = RateLimitConfig(
        max_concurrent_requests=2,  # 模拟外部API限制
        requests_per_minute=60,
        failure_threshold=5
    )
    manager_config = TaskManagerConfig(
        max_concurrent_batches=2,   # 模拟外部API限制
        batch_check_interval=2.0,
        splitter_config=splitter_config,
        rate_limiter_config=rate_limiter_config
    )
    
    manager = TaskManager(manager_config)
    
    try:
        await manager.start()
        
        # 生成1500个测试URL
        test_urls = [
            f"https://mercadolibre.com.ar/product/stress_test_{i}" 
            for i in range(1500)
        ]
        
        logger.info(f"Submitting {len(test_urls)} URLs for processing")
        
        # 提交大任务
        submission_id = await manager.submit_task(
            task_id=999,
            urls=test_urls,
            platform="mercadolibre",
            priority=TaskPriority.NORMAL
        )
        
        logger.info(f"Stress test task submitted: {submission_id}")
        
        # 监控处理进度
        for i in range(30):  # 监控30次，每次间隔10秒
            await asyncio.sleep(10)
            
            status = manager.get_status()
            logger.info(
                f"Progress check {i+1}: "
                f"Pending: {status['pending_batches']}, "
                f"Running: {status['running_batches']}, "
                f"Completed: {status['completed_batches']}, "
                f"Failed: {status['failed_batches']}, "
                f"URLs processed: {status['stats']['total_urls_processed']}"
            )
            
            # 如果所有批次都处理完了，提前退出
            if (status['pending_batches'] == 0 and 
                status['running_batches'] == 0):
                logger.info("All batches completed!")
                break
        
        final_status = manager.get_status()
        logger.info(f"Stress test final status: {final_status}")
        
        # 验证结果
        total_batches = (final_status['completed_batches'] + 
                        final_status['failed_batches'])
        expected_batches = len(test_urls) // splitter_config.max_batch_size
        if len(test_urls) % splitter_config.max_batch_size > 0:
            expected_batches += 1
        
        logger.info(
            f"Expected {expected_batches} batches, "
            f"processed {total_batches} batches"
        )
        
    except Exception as e:
        logger.error(f"Stress test failed: {e}")
        raise
    finally:
        await manager.stop()


async def main():
    """运行所有测试"""
    logger.info("Starting Task Scheduler System Tests")
    
    # 运行单元测试
    logger.info("=== Running Unit Tests ===")
    
    test_splitter = TestTaskSplitter()
    test_splitter.test_basic_splitting()
    test_splitter.test_large_task_splitting()
    test_splitter.test_priority_calculation()
    logger.info("✓ Task Splitter tests passed")
    
    # 运行集成测试
    logger.info("=== Running Integration Tests ===")
    
    try:
        await test_task_manager_integration()
        logger.info("✓ Task Manager integration test passed")
    except Exception as e:
        logger.error(f"✗ Task Manager integration test failed: {e}")
    
    # 运行压力测试
    logger.info("=== Running Stress Tests ===")
    
    try:
        await test_stress_scenario()
        logger.info("✓ Stress test completed")
    except Exception as e:
        logger.error(f"✗ Stress test failed: {e}")
    
    logger.info("All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
