#!/usr/bin/env python3
"""
测试反爬虫配置在确认步骤中的显示

创建测试环境并验证反爬虫配置的显示效果
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_anti_crawler_config_display():
    """测试反爬虫配置在确认步骤中的显示"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试反爬虫配置在确认步骤中的显示")
    print("=" * 60)
    
    created_url_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_urls = [
            {
                "url": "https://www.mercadolibre.com.ar/anti-crawler-test-1",
                "platform": "mercadolibre",
                "title": "反爬虫配置测试URL 1",
            },
            {
                "url": "https://www.mercadolibre.com.ar/anti-crawler-test-2", 
                "platform": "mercadolibre",
                "title": "反爬虫配置测试URL 2",
            }
        ]
        
        for i, test_url in enumerate(test_urls, 1):
            # 生成URL ID和哈希
            url_id = str(uuid4())
            url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
            now = datetime.now().isoformat()
            
            # 创建URL项目数据
            url_item_data = {
                'id': url_id,
                'url': test_url['url'],
                'platform': test_url['platform'],
                'source_file': 'anti_crawler_test',
                'added_at': now,
                'status': 'active',
                'last_check': '',
                'check_count': '0',
                'success_count': '0',
                'error_count': '0',
                'metadata': json.dumps({
                    'url_hash': url_hash,
                    'title': test_url.get('title', ''),
                    'added_via': 'anti_crawler_test'
                })
            }
            
            # 存储到Redis
            url_key = f"url_pool:items:{url_id}"
            r.hset(url_key, mapping=url_item_data)
            
            # 添加到索引
            r.sadd("url_pool:all_ids", url_id)
            r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
            r.sadd("url_pool:status:active", url_id)
            r.sadd("url_pool:source:anti_crawler_test", url_id)
            r.sadd("url_pool:url_hashes", url_hash)
            
            created_url_ids.append(url_id)
            print(f"  ✅ URL {i} 创建成功: {url_id}")
        
        # 步骤2: 提供前端测试信息
        print(f"\n📋 步骤2: 反爬虫配置测试指南")
        
        print(f"  🌐 前端URL池页面: http://localhost:3000/url-pool")
        print(f"  📝 测试URL数量: {len(created_url_ids)}")
        
        print(f"\n  📋 详细测试步骤:")
        print(f"    1. 访问 http://localhost:3000/url-pool")
        print(f"    2. 搜索'anti-crawler-test'找到测试URL")
        print(f"    3. 选择测试URL并点击'创建任务'")
        print(f"    4. 在任务创建向导中:")
        print(f"       - 基础配置: 设置任务名称等基本信息")
        print(f"       - 调度配置: 重点测试反爬虫配置")
        print(f"       - 确认步骤: 检查反爬虫配置显示")
        
        print(f"\n  🔧 调度配置 - 反爬虫配置测试:")
        print(f"    📍 在调度配置步骤中:")
        print(f"      1. 设置基本调度信息（每日执行、时间等）")
        print(f"      2. 找到'反爬虫配置'卡片")
        print(f"      3. 启用'随机启动延迟'开关")
        print(f"      4. 设置延迟范围（如：最小5分钟，最大30分钟）")
        print(f"      5. 查看随机延迟说明和调度预览")
        
        print(f"\n  ✅ 确认步骤 - 应该显示的反爬虫配置:")
        print(f"    📊 调度配置部分应该包含:")
        print(f"      - 随机延迟: 已启用 (绿色标签)")
        print(f"      - 延迟范围: 5-30分钟")
        print(f"      - 如果延迟超过60分钟，还会显示小时数")
        
        # 步骤3: 提供测试用例
        print(f"\n📋 步骤3: 测试用例")
        
        test_cases = [
            {
                "name": "基础反爬虫配置",
                "config": {
                    "enable_random_delay": True,
                    "random_delay_min": 5,
                    "random_delay_max": 30
                },
                "expected": {
                    "status": "已启用 (绿色标签)",
                    "range": "5-30分钟"
                }
            },
            {
                "name": "长时间延迟配置",
                "config": {
                    "enable_random_delay": True,
                    "random_delay_min": 30,
                    "random_delay_max": 180
                },
                "expected": {
                    "status": "已启用 (绿色标签)",
                    "range": "30-180分钟 (约0-3小时)"
                }
            },
            {
                "name": "禁用反爬虫配置",
                "config": {
                    "enable_random_delay": False,
                    "random_delay_min": 0,
                    "random_delay_max": 0
                },
                "expected": {
                    "status": "已禁用 (默认标签)",
                    "range": "不显示延迟范围"
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  测试用例 {i}: {test_case['name']}")
            print(f"    配置:")
            print(f"      启用随机延迟: {test_case['config']['enable_random_delay']}")
            print(f"      最小延迟: {test_case['config']['random_delay_min']}分钟")
            print(f"      最大延迟: {test_case['config']['random_delay_max']}分钟")
            print(f"    期望显示:")
            print(f"      随机延迟状态: {test_case['expected']['status']}")
            print(f"      延迟范围: {test_case['expected']['range']}")
        
        # 步骤4: 数据结构说明
        print(f"\n📋 步骤4: 期望的数据结构")
        
        expected_schedule_config = {
            "type": "daily",
            "enabled": True,
            "time": "16:00",
            "end_time": "23:30",
            "timezone": "Asia/Shanghai",
            "enable_random_delay": True,  # ← 反爬虫配置
            "random_delay_min": 5,        # ← 最小延迟
            "random_delay_max": 30,       # ← 最大延迟
            "max_runs": None
        }
        
        print(f"  📊 完整的调度配置数据结构:")
        print(f"    {json.dumps(expected_schedule_config, indent=2, ensure_ascii=False)}")
        
        # 步骤5: 问题排查
        print(f"\n📋 步骤5: 问题排查指南")
        
        print(f"  🔍 如果反爬虫配置没有显示，检查:")
        print(f"    1. 确认步骤的调度配置部分是否包含'随机延迟'字段")
        print(f"    2. 开发者工具Console是否有错误")
        print(f"    3. 调试信息中的enable_random_delay值")
        print(f"    4. 数据是否正确从调度配置步骤传递到确认步骤")
        
        print(f"  ✅ 修复后应该看到:")
        print(f"    - 随机延迟: 已启用/已禁用 标签")
        print(f"    - 延迟范围: X-Y分钟 (如果启用)")
        print(f"    - 小时数显示: (约X-Y小时) (如果超过60分钟)")
        
        print(f"  🐛 常见问题:")
        print(f"    1. 随机延迟字段不显示 → 检查条件判断逻辑")
        print(f"    2. 延迟范围显示错误 → 检查数值计算")
        print(f"    3. 标签颜色不对 → 检查enable_random_delay值")
        
        # 等待用户测试
        print(f"\n💡 请按照上述步骤进行前端测试")
        print(f"🎯 重点关注确认步骤中的反爬虫配置显示")
        input("测试完成后按回车键继续清理测试数据...")
        
        print(f"\n🎉 反爬虫配置显示测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:anti_crawler_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_anti_crawler_config_display()
