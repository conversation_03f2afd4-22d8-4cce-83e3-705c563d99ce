/* 监控任务管理页面样式 */

.monitoring-tasks-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.stats-card {
  margin-bottom: 24px;
}

.stats-card .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.stats-card .ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

.task-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-name-cell {
  font-weight: bold;
}

.task-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.schedule-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.schedule-main {
  font-weight: 500;
}

.schedule-next {
  font-size: 12px;
  color: #666;
}

.url-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.url-count {
  font-weight: 500;
}

.success-rate-circle {
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .ant-btn {
  border-radius: 4px;
}

.create-task-modal .ant-modal-body {
  padding: 24px;
}

.create-task-form .ant-form-item {
  margin-bottom: 16px;
}

.status-tag-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.running-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitoring-tasks-container {
    padding: 16px;
  }
  
  .stats-card {
    margin-bottom: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
  }
}

/* 表格行悬停效果 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* 状态标签样式 */
.ant-tag.status-active {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag.status-paused {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.ant-tag.status-stopped {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.ant-tag.status-draft {
  background-color: #fafafa;
  border-color: #d9d9d9;
  color: #666;
}

/* 进度条样式 */
.url-progress .ant-progress-line {
  margin: 0;
}

.url-progress .ant-progress-bg {
  height: 4px !important;
}

/* 统计卡片悬停效果 */
.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* 创建按钮样式 */
.create-task-button {
  height: 40px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.create-task-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.2s ease;
}

/* 模态框样式 */
.create-task-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.create-task-modal .ant-modal-title {
  font-size: 18px;
  font-weight: 600;
}

/* 表单样式 */
.create-task-form .ant-form-item-label > label {
  font-weight: 500;
}

.create-task-form .ant-input,
.create-task-form .ant-select-selector,
.create-task-form .ant-picker {
  border-radius: 6px;
}

.create-task-form .ant-input:focus,
.create-task-form .ant-select-focused .ant-select-selector,
.create-task-form .ant-picker-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
