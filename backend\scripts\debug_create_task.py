#!/usr/bin/env python3
"""
调试创建任务问题

直接测试TaskStorageService的创建和转换功能
"""

import asyncio
import sys
import os
import json
import uuid
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.services.task_storage_service import TaskStorageService


async def debug_create_task():
    """调试创建任务"""
    print("🔍 调试创建任务问题")
    print("=" * 50)
    
    try:
        # 创建存储服务
        storage = TaskStorageService()
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        print(f"📝 生成任务ID: {task_id}")
        
        # 准备任务数据
        now = datetime.now()
        task_data = {
            "id": task_id,
            "name": "调试测试任务",
            "description": "用于调试的测试任务",
            "status": "draft",
            "platform": "mercadolibre",
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "max_retries": 3,
                "timeout": 300,
                "priority": "medium"
            },
            "next_run": "",
            "last_run": "",
            "is_running": False,
            "total_runs": 0,
            "success_count": 0,
            "failed_count": 0,
            "success_rate": 0.0,
            "avg_duration": 0.0
        }
        
        print(f"📊 任务数据准备完成")
        
        # 保存任务
        print(f"💾 保存任务...")
        success = await storage.save_task(task_id, task_data)
        print(f"保存结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if not success:
            print("❌ 任务保存失败，停止调试")
            return
        
        # 读取任务
        print(f"📖 读取任务...")
        retrieved_task = await storage.get_task(task_id)
        
        if retrieved_task:
            print(f"✅ 任务读取成功")
            print(f"任务数据结构:")
            for key, value in retrieved_task.items():
                print(f"  {key}: {type(value).__name__} = {value}")
        else:
            print(f"❌ 任务读取失败")
            return
        
        # 测试转换函数
        print(f"\n🔄 测试数据转换...")
        
        try:
            # 导入转换函数
            sys.path.insert(0, str(project_root / "backend"))
            from app.api.monitoring_task_routes import convert_redis_task_to_monitoring_task
            
            print(f"📥 调用转换函数...")
            converted_task = await convert_redis_task_to_monitoring_task(retrieved_task)
            
            if converted_task:
                print(f"✅ 转换成功")
                print(f"转换后的数据:")
                print(json.dumps(converted_task, indent=2, default=str, ensure_ascii=False))
            else:
                print(f"❌ 转换失败")
                
        except Exception as e:
            print(f"❌ 转换过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        
        # 清理测试任务
        print(f"\n🧹 清理测试任务...")
        delete_success = await storage.delete_task(task_id)
        print(f"删除结果: {'✅ 成功' if delete_success else '❌ 失败'}")
        
        await storage.close()
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    await debug_create_task()


if __name__ == "__main__":
    asyncio.run(main())
