import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Select,
  Input,
  Button,
  Space,
  Switch,
  Slider,
  Row,
  Col,
  Tag,
  Tooltip,
  message,
  Typography
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClearOutlined,
  DownloadOutlined,
  SearchOutlined,
  FilterOutlined,
  VerticalAlignBottomOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Search } = Input;
const { Text } = Typography;

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  container: string;
}

interface LogStreamPanelProps {}

const LogStreamPanel: React.FC<LogStreamPanelProps> = () => {
  const [selectedContainer, setSelectedContainer] = useState<string>('monit-celery-worker');
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [levelFilter, setLevelFilter] = useState<string[]>(['INFO', 'WARNING', 'ERROR', 'DEBUG']);
  const [maxLines, setMaxLines] = useState(500);
  const [loading, setLoading] = useState(false);
  
  const logContainerRef = useRef<HTMLDivElement>(null);
  const streamIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 可用的容器列表
  const availableContainers = [
    { value: 'monit-celery-worker', label: 'Celery Worker' },
    { value: 'monit-celery-beat', label: 'Celery Beat' },
    { value: 'celery-worker', label: 'Celery Worker (Legacy)' },
    { value: 'celery-beat', label: 'Celery Beat (Legacy)' }
  ];

  // 日志级别配置
  const logLevels = [
    { value: 'DEBUG', color: 'purple' },
    { value: 'INFO', color: 'blue' },
    { value: 'WARNING', color: 'orange' },
    { value: 'ERROR', color: 'red' }
  ];

  // 获取日志
  const fetchLogs = async (container: string, lines: number = 100) => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/v1/celery/docker/logs/${container}?lines=${lines}&since=1h${searchKeyword ? `&filter_keywords=${encodeURIComponent(searchKeyword)}` : ''}`
      );
      
      if (response.ok) {
        const data = await response.json();
        return data.logs || [];
      } else {
        message.error(`获取${container}日志失败`);
        return [];
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      message.error('获取日志失败');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // 开始日志流
  const startLogStream = async () => {
    setIsStreaming(true);
    
    // 首次获取日志
    const initialLogs = await fetchLogs(selectedContainer, maxLines);
    setLogs(initialLogs);

    // 设置定时获取新日志
    streamIntervalRef.current = setInterval(async () => {
      const newLogs = await fetchLogs(selectedContainer, 50); // 每次获取最新50条
      
      setLogs(prevLogs => {
        // 合并新日志，避免重复
        const combined = [...prevLogs, ...newLogs];
        const unique = combined.filter((log, index, self) => 
          index === self.findIndex(l => l.timestamp === log.timestamp && l.message === log.message)
        );
        
        // 保持最大行数限制
        return unique.slice(-maxLines);
      });
    }, 3000); // 每3秒获取一次新日志
  };

  // 停止日志流
  const stopLogStream = () => {
    setIsStreaming(false);
    if (streamIntervalRef.current) {
      clearInterval(streamIntervalRef.current);
      streamIntervalRef.current = null;
    }
  };

  // 清空日志
  const clearLogs = () => {
    setLogs([]);
    setFilteredLogs([]);
  };

  // 导出日志
  const exportLogs = () => {
    const logText = filteredLogs.map(log => 
      `[${log.timestamp}] [${log.level}] ${log.message}`
    ).join('\n');
    
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `celery-logs-${selectedContainer}-${new Date().toISOString().slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success('日志导出成功');
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  };

  // 过滤日志
  useEffect(() => {
    let filtered = logs;

    // 按级别过滤
    if (levelFilter.length > 0) {
      filtered = filtered.filter(log => levelFilter.includes(log.level));
    }

    // 按关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(keyword) ||
        log.level.toLowerCase().includes(keyword)
      );
    }

    setFilteredLogs(filtered);
  }, [logs, levelFilter, searchKeyword]);

  // 自动滚动
  useEffect(() => {
    if (autoScroll && filteredLogs.length > 0) {
      setTimeout(scrollToBottom, 100);
    }
  }, [filteredLogs, autoScroll]);

  // 容器切换时重新获取日志
  useEffect(() => {
    if (isStreaming) {
      stopLogStream();
      setTimeout(() => startLogStream(), 500);
    }
  }, [selectedContainer]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (streamIntervalRef.current) {
        clearInterval(streamIntervalRef.current);
      }
    };
  }, []);

  // 获取日志级别颜色
  const getLevelColor = (level: string) => {
    const levelConfig = logLevels.find(l => l.value === level);
    return levelConfig?.color || 'default';
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString();
    } catch {
      return timestamp.slice(-12); // 取最后12个字符作为时间
    }
  };

  return (
    <div className="log-stream-panel">
      {/* 控制面板 */}
      <div className="log-controls">
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              value={selectedContainer}
              onChange={setSelectedContainer}
              style={{ width: '100%' }}
              placeholder="选择容器"
            >
              {availableContainers.map(container => (
                <Option key={container.value} value={container.value}>
                  {container.label}
                </Option>
              ))}
            </Select>
          </Col>
          
          <Col span={6}>
            <Search
              placeholder="搜索日志内容"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              allowClear
              prefix={<SearchOutlined />}
            />
          </Col>
          
          <Col span={4}>
            <Select
              mode="multiple"
              placeholder="日志级别"
              value={levelFilter}
              onChange={setLevelFilter}
              style={{ width: '100%' }}
            >
              {logLevels.map(level => (
                <Option key={level.value} value={level.value}>
                  <Tag color={level.color}>{level.value}</Tag>
                </Option>
              ))}
            </Select>
          </Col>
          
          <Col span={6}>
            <Space>
              <Button
                type={isStreaming ? 'default' : 'primary'}
                icon={isStreaming ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={isStreaming ? stopLogStream : startLogStream}
                loading={loading}
              >
                {isStreaming ? '暂停' : '开始'}
              </Button>
              
              <Button
                icon={<ClearOutlined />}
                onClick={clearLogs}
                disabled={logs.length === 0}
              >
                清空
              </Button>
              
              <Button
                icon={<DownloadOutlined />}
                onClick={exportLogs}
                disabled={filteredLogs.length === 0}
              >
                导出
              </Button>
            </Space>
          </Col>
          
          <Col span={4}>
            <Space>
              <Tooltip title="自动滚动到底部">
                <Switch
                  checked={autoScroll}
                  onChange={setAutoScroll}
                  checkedChildren="自动滚动"
                  unCheckedChildren="手动滚动"
                />
              </Tooltip>
              
              <Button
                icon={<VerticalAlignBottomOutlined />}
                onClick={scrollToBottom}
                size="small"
              >
                到底部
              </Button>
            </Space>
          </Col>
        </Row>
        
        <Row style={{ marginTop: 8 }}>
          <Col span={12}>
            <Space>
              <Text type="secondary">最大行数:</Text>
              <Slider
                min={100}
                max={2000}
                step={100}
                value={maxLines}
                onChange={setMaxLines}
                style={{ width: 120 }}
                tooltip={{ formatter: (value) => `${value} 行` }}
              />
              <Text type="secondary">{maxLines} 行</Text>
            </Space>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Space>
              <Text type="secondary">
                显示: {filteredLogs.length} / {logs.length} 条日志
              </Text>
              {isStreaming && (
                <Tag color="green" icon={<PlayCircleOutlined />}>
                  实时流
                </Tag>
              )}
            </Space>
          </Col>
        </Row>
      </div>

      {/* 日志内容 */}
      <div className="log-container">
        <div className="log-content" ref={logContainerRef}>
          {filteredLogs.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
              {logs.length === 0 ? '暂无日志数据' : '没有匹配的日志'}
            </div>
          ) : (
            filteredLogs.map((log, index) => (
              <div key={index} className={`log-entry ${log.level.toLowerCase()}`}>
                <span className="log-timestamp">
                  {formatTimestamp(log.timestamp)}
                </span>
                <span className="log-level">
                  [{log.level}]
                </span>
                <span className="log-message">
                  {log.message}
                </span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default LogStreamPanel;
