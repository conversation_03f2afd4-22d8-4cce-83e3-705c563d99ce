{"dashboard": {"id": null, "title": "MonIt System Dashboard", "tags": ["monit", "system", "monitoring"], "timezone": "browser", "editable": true, "graphTooltip": 1, "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "System Health Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "{{job}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}}, "type": "value"}, {"options": {"1": {"text": "UP", "color": "green"}}, "type": "value"}], "displayName": "${__field.labels.job}"}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background"}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU Usage %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "options": {"tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 4}}, {"id": 3, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "Memory Usage %", "refId": "A"}, {"expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / 1024 / 1024 / 1024", "legendFormat": "Used Memory (GB)", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Used Memory (GB)"}, "properties": [{"id": "unit", "value": "decgbytes"}, {"id": "max", "value": null}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 4}}, {"id": 4, "title": "System Load", "type": "timeseries", "targets": [{"expr": "node_load1", "legendFormat": "1m load", "refId": "A"}, {"expr": "node_load5", "legendFormat": "5m load", "refId": "B"}, {"expr": "node_load15", "legendFormat": "15m load", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 4}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}