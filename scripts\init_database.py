#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有必要的数据库表
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.database import engine
from backend.app.models.base import Base
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def init_database():
    """初始化数据库表"""
    try:
        logger.info("🔧 开始初始化数据库...")
        
        # 使用导入的数据库引擎
        # engine 已经从 backend.app.database 导入
        
        # 创建所有表
        async with engine.begin() as conn:
            logger.info("📊 创建数据库表...")
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("✅ 数据库初始化完成！")
        
        # 显示创建的表
        from sqlalchemy.sql import text
        async with engine.begin() as conn:
            result = await conn.execute(
                text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
            )
            tables = [row[0] for row in result.fetchall()]
            logger.info(f"📋 创建的表: {', '.join(tables)}")
        
        await engine.dispose()
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(init_database())
