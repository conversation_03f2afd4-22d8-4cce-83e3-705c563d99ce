#!/usr/bin/env python3
"""
测试URL执行状态API
验证前端能否正确获取URL执行信息
"""

import asyncio
import requests
import json
from datetime import datetime

# 测试配置
BACKEND_URL = "http://localhost:8000"
TEST_URLS = [
    "https://articulo.mercadolibre.com.mx/MLM-test-api-1",
    "https://articulo.mercadolibre.com.mx/MLM-test-api-2"
]


async def create_test_execution_data():
    """创建测试执行数据"""
    print("🔍 创建测试执行数据...")
    
    try:
        from app.services.unified_execution_service import UnifiedExecutionService
        service = UnifiedExecutionService(redis_url="redis://localhost:6379/0")
        
        # 创建执行会话
        session = await service.create_execution_session(
            monitoring_task_id="test_task_api_demo",
            celery_task_id="celery_test_api_demo",
            execution_type="manual",
            urls=TEST_URLS
        )
        
        print(f"✅ 创建执行会话: {session.session_id}")
        
        # 创建URL执行记录
        for i, url in enumerate(TEST_URLS):
            execution = await service.create_url_execution(
                session_id=session.session_id,
                monitoring_task_id="test_task_api_demo",
                celery_task_id="celery_test_api_demo",
                url=url,
                assigned_worker_id=f"test_worker_{i+1}",
                crawler_endpoint=f"http://localhost:1123{i+5}/crawl"
            )
            
            # 更新状态
            if i == 0:
                await service.update_url_execution_status(
                    execution.execution_id,
                    "completed",
                    response_time=2.5
                )
                print(f"✅ URL执行记录: {url} -> completed")
            else:
                await service.update_url_execution_status(
                    execution.execution_id,
                    "running",
                    response_time=1.2
                )
                print(f"✅ URL执行记录: {url} -> running")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False


def test_url_execution_status_api():
    """测试URL执行状态API"""
    print("\n🔍 测试URL执行状态API...")
    
    results = []
    
    for url in TEST_URLS:
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/crawler-debug/execution/url-status",
                params={"url": url}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ URL状态API成功: {url}")
                print(f"   - 监控任务ID: {data.get('monitoring_task_id', 'N/A')}")
                print(f"   - Celery任务ID: {data.get('celery_task_id', 'N/A')}")
                print(f"   - 分配Worker: {data.get('assigned_worker_id', 'N/A')}")
                print(f"   - 状态: {data.get('status', 'N/A')}")
                print(f"   - 端点: {data.get('crawler_endpoint', 'N/A')}")
                results.append(True)
            elif response.status_code == 404:
                print(f"⚠️  URL状态未找到: {url}")
                results.append(False)
            else:
                print(f"❌ URL状态API失败: {response.status_code} - {response.text}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ URL状态API异常: {e}")
            results.append(False)
    
    return all(results)


def test_monitoring_task_execution_chain_api():
    """测试监控任务执行链路API"""
    print("\n🔍 测试监控任务执行链路API...")
    
    try:
        response = requests.get(
            f"{BACKEND_URL}/api/v1/crawler-debug/execution/monitoring-task/test_task_api_demo"
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 监控任务执行链路API成功")
            print(f"   - 监控任务ID: {data.get('monitoring_task_id', 'N/A')}")
            print(f"   - 总会话数: {data.get('total_sessions', 0)}")
            
            execution_chain = data.get('execution_chain', [])
            for i, session in enumerate(execution_chain):
                session_data = session.get('session', {})
                url_executions = session.get('url_executions', [])
                print(f"   - 会话 {i+1}: {session_data.get('session_id', 'N/A')}")
                print(f"     状态: {session_data.get('status', 'N/A')}")
                print(f"     URL执行数: {len(url_executions)}")
            
            return True
        else:
            print(f"❌ 监控任务执行链路API失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 监控任务执行链路API异常: {e}")
        return False


def test_worker_load_api():
    """测试Worker负载API"""
    print("\n🔍 测试Worker负载API...")
    
    results = []
    
    for i in range(2):
        worker_id = f"test_worker_{i+1}"
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/crawler-debug/execution/worker-load/{worker_id}"
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Worker负载API成功: {worker_id}")
                print(f"   - 当前任务数: {data.get('current_tasks', 0)}")
                print(f"   - 最大任务数: {data.get('max_tasks', 10)}")
                print(f"   - 当前URL: {len(data.get('current_urls', []))}")
                results.append(True)
            else:
                print(f"❌ Worker负载API失败: {response.status_code} - {response.text}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Worker负载API异常: {e}")
            results.append(False)
    
    return all(results)


def test_frontend_api_integration():
    """测试前端API集成"""
    print("\n🔍 测试前端API集成...")
    
    try:
        # 模拟前端调用
        test_url = TEST_URLS[0]
        
        # 测试前端会调用的API格式
        response = requests.get(
            f"{BACKEND_URL}/api/v1/crawler-debug/execution/url-status",
            params={"url": test_url},
            headers={"Accept": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            
            # 验证前端需要的字段
            required_fields = [
                'monitoring_task_id',
                'celery_task_id', 
                'assigned_worker_id',
                'status',
                'crawler_endpoint'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if not missing_fields:
                print("✅ 前端API集成验证成功")
                print("   - 所有必需字段都存在")
                return True
            else:
                print(f"❌ 前端API集成验证失败: 缺少字段 {missing_fields}")
                return False
        else:
            print(f"❌ 前端API集成验证失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端API集成验证异常: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试URL执行状态API\n")
    
    # 1. 创建测试数据
    data_created = await create_test_execution_data()
    if not data_created:
        print("❌ 无法创建测试数据，跳过API测试")
        return False
    
    # 等待数据写入
    await asyncio.sleep(1)
    
    test_results = []
    
    # 2. 测试URL执行状态API
    result1 = test_url_execution_status_api()
    test_results.append(("URL执行状态API", result1))
    
    # 3. 测试监控任务执行链路API
    result2 = test_monitoring_task_execution_chain_api()
    test_results.append(("监控任务执行链路API", result2))
    
    # 4. 测试Worker负载API
    result3 = test_worker_load_api()
    test_results.append(("Worker负载API", result3))
    
    # 5. 测试前端API集成
    result4 = test_frontend_api_integration()
    test_results.append(("前端API集成", result4))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！URL执行状态API工作正常！")
        print("\n💡 现在前端应该能够正确显示:")
        print("   - 爬虫Worker信息")
        print("   - Celery Worker信息") 
        print("   - 最后处理时间")
        print("   - 响应时间")
        print("   - 执行状态")
        return True
    else:
        print("⚠️  部分测试失败，前端显示可能不完整")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
