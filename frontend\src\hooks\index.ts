import { useState, useEffect, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '../store';
import { updateCurrentMetrics } from '../store/slices/systemSlice';
import { showSuccess, showError, showWarning, showInfo } from '../store/slices/uiSlice';
import { useWebSocket } from '../services/websocket';
import { WebSocketMessage, SystemMetrics } from '../types';

// 通知Hook
export const useNotification = () => {
  const dispatch = useAppDispatch();

  return {
    success: useCallback((title: string, message: string) => {
      dispatch(showSuccess({ title, message }));
    }, [dispatch]),
    
    error: useCallback((title: string, message: string) => {
      dispatch(showError({ title, message }));
    }, [dispatch]),
    
    warning: useCallback((title: string, message: string) => {
      dispatch(showWarning({ title, message }));
    }, [dispatch]),
    
    info: useCallback((title: string, message: string) => {
      dispatch(showInfo({ title, message }));
    }, [dispatch]),
  };
};

// 实时任务更新Hook (暂时禁用，等待与新监控任务系统集成)
export const useTaskUpdates = () => {
  // TODO: 重新实现以支持新的监控任务系统
  // const dispatch = useAppDispatch();
  // const { subscribe } = useWebSocket();

  useEffect(() => {
    // 暂时禁用实时更新，避免与旧的tasksSlice冲突
    // const unsubscribe = subscribe('task_update', (message: WebSocketMessage) => {
    //   const { task_id, status, progress, error_message } = message.data;
    //
    //   dispatch(updateTaskStatus({
    //     id: task_id,
    //     status: status as TaskStatus,
    //     progress,
    //   }));

    //   // 如果任务失败，显示错误通知
    //   if (status === TaskStatus.FAILED && error_message) {
    //     dispatch(showError({
    //       title: '任务执行失败',
    //       message: error_message,
    //     }));
    //   }
    //
    //   // 如果任务完成，显示成功通知
    //   if (status === TaskStatus.COMPLETED) {
    //     dispatch(showSuccess({
    //       title: '任务执行完成',
    //       message: `任务 ${task_id} 已成功完成`,
    //     }));
    //   }
    // });

    // return unsubscribe;
  }, []);
};

// 实时系统监控Hook
export const useSystemMonitoring = () => {
  const dispatch = useAppDispatch();
  const { subscribe } = useWebSocket();

  useEffect(() => {
    const unsubscribe = subscribe('system_metrics', (message: WebSocketMessage) => {
      const metrics: SystemMetrics = message.data;
      dispatch(updateCurrentMetrics(metrics));
    });

    return unsubscribe;
  }, [dispatch, subscribe]);
};

// 本地存储Hook
export const useLocalStorage = <T>(key: string, initialValue: T) => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  return [storedValue, setValue] as const;
};

// 防抖Hook
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// 异步操作Hook
export const useAsync = <T, E = string>(
  asyncFunction: () => Promise<T>,
  immediate = true
) => {
  const [status, setStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<E | null>(null);

  const execute = useCallback(async () => {
    setStatus('pending');
    setData(null);
    setError(null);

    try {
      const response = await asyncFunction();
      setData(response);
      setStatus('success');
      return response;
    } catch (error) {
      setError(error as E);
      setStatus('error');
      throw error;
    }
  }, [asyncFunction]);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return { execute, status, data, error };
};

// 分页Hook
export const usePagination = (initialPage = 1, initialPageSize = 10) => {
  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const handlePageChange = useCallback((newPage: number, newPageSize?: number) => {
    setPage(newPage);
    if (newPageSize && newPageSize !== pageSize) {
      setPageSize(newPageSize);
    }
  }, [pageSize]);

  const reset = useCallback(() => {
    setPage(initialPage);
    setPageSize(initialPageSize);
  }, [initialPage, initialPageSize]);

  return {
    page,
    pageSize,
    setPage,
    setPageSize,
    handlePageChange,
    reset,
  };
};

// 表格选择Hook
export const useTableSelection = <T extends { id: string }>(items: T[]) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const handleSelectChange = useCallback((keys: string[]) => {
    setSelectedRowKeys(keys);
  }, []);

  const handleSelectAll = useCallback(() => {
    setSelectedRowKeys(items.map(item => item.id));
  }, [items]);

  const handleClearSelection = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  const selectedItems = items.filter(item => selectedRowKeys.includes(item.id));

  return {
    selectedRowKeys,
    selectedItems,
    handleSelectChange,
    handleSelectAll,
    handleClearSelection,
    hasSelected: selectedRowKeys.length > 0,
    selectedCount: selectedRowKeys.length,
  };
};

// 窗口大小Hook
export const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
};

// 定时器Hook
export const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef<() => void>(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    const tick = () => {
      savedCallback.current?.();
    };

    if (delay !== null) {
      const id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
  }, [delay]);
};

// 前一个值Hook
export const usePrevious = <T>(value: T): T | undefined => {
  const ref = useRef<T | undefined>(undefined);
  
  useEffect(() => {
    ref.current = value;
  });
  
  return ref.current;
};

// 点击外部Hook
export const useClickOutside = (
  ref: React.RefObject<HTMLElement>,
  handler: () => void
) => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler]);
};
