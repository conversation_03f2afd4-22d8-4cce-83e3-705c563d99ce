#!/usr/bin/env python3
"""
检查指定任务的激活URL数量
"""

import asyncio
import redis.asyncio as redis
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from app.services.task_storage_service import TaskStorageService
from app.services.url_pool_service import UrlPoolService


async def check_task_active_urls(task_id: str):
    """检查任务的激活URL数量"""
    
    # 连接Redis (使用本地端口)
    redis_client = redis.from_url("redis://localhost:6379/0")

    try:
        await redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ 无法连接到Redis服务器: {e}")
        return
    
    try:
        print(f"=== 检查任务 {task_id} 的激活URL数量 ===\n")
        
        # 1. 初始化服务
        task_storage = TaskStorageService()
        url_pool_service = UrlPoolService(redis_client)
        
        # 2. 检查任务是否存在
        task_exists = await task_storage.task_exists(task_id)
        if not task_exists:
            print(f"❌ 任务 {task_id} 不存在")
            return
        
        print(f"✅ 任务存在")
        
        # 3. 获取任务基本信息
        task_data = await task_storage.get_task(task_id)
        if task_data:
            print(f"📋 任务信息:")
            print(f"   名称: {task_data.get('name', 'N/A')}")
            print(f"   状态: {task_data.get('status', 'N/A')}")
            print(f"   总URL数: {task_data.get('total_urls', 'N/A')}")
        print()
        
        # 4. 获取任务关联的URL ID列表
        url_ids = await task_storage.get_task_urls(task_id)
        print(f"🔗 任务关联的URL:")
        print(f"   URL ID数量: {len(url_ids)}")
        
        if not url_ids:
            print("   ⚠️ 任务没有关联任何URL")
            return
        
        # 5. 检查每个URL的状态
        print(f"\n📊 URL状态统计:")
        
        status_counts = {
            'active': 0,
            'disabled': 0,
            'deleted': 0,
            'other': 0,
            'not_found': 0
        }
        
        active_urls = []
        
        for i, url_id in enumerate(url_ids, 1):
            url_item = await url_pool_service.get_url_by_id(url_id)
            
            if url_item:
                status = url_item.status
                if status == 'active':
                    status_counts['active'] += 1
                    active_urls.append({
                        'id': url_id,
                        'url': url_item.url,
                        'platform': url_item.platform
                    })
                elif status == 'disabled':
                    status_counts['disabled'] += 1
                elif status == 'deleted':
                    status_counts['deleted'] += 1
                else:
                    status_counts['other'] += 1
                
                print(f"   {i:2d}. {url_id[:8]}... - {status} - {url_item.platform}")
            else:
                status_counts['not_found'] += 1
                print(f"   {i:2d}. {url_id[:8]}... - NOT_FOUND")
        
        print(f"\n📈 状态汇总:")
        print(f"   ✅ 激活 (active):   {status_counts['active']}")
        print(f"   ⏸️  禁用 (disabled): {status_counts['disabled']}")
        print(f"   🗑️  删除 (deleted):  {status_counts['deleted']}")
        print(f"   ❓ 其他状态:        {status_counts['other']}")
        print(f"   ❌ 未找到:          {status_counts['not_found']}")
        print(f"   📊 总计:            {len(url_ids)}")
        
        # 6. 显示激活的URL详情
        if active_urls:
            print(f"\n🎯 激活的URL详情:")
            for i, url_info in enumerate(active_urls, 1):
                print(f"   {i}. [{url_info['platform']}] {url_info['url']}")
                print(f"      ID: {url_info['id']}")
        
        # 7. 模拟监控任务执行器的URL获取逻辑
        print(f"\n🔄 模拟监控任务执行器获取URL:")
        executor_urls = []
        for url_id in url_ids:
            url_item = await url_pool_service.get_url_by_id(url_id)
            if url_item and url_item.status == "active":
                executor_urls.append(url_item.url)
        
        print(f"   监控任务执行器会获取到 {len(executor_urls)} 个激活URL")
        print(f"   这些URL将被提交给TaskManager进行处理")
        
        # 8. 结论
        print(f"\n🎉 检查结果:")
        print(f"   任务 {task_id} 有 {status_counts['active']} 个激活的URL")
        print(f"   监控任务执行时将处理这 {status_counts['active']} 个URL")
        
        if status_counts['active'] == 0:
            print(f"   ⚠️ 警告: 没有激活的URL，监控任务将不会执行任何操作")
        elif status_counts['disabled'] > 0 or status_counts['deleted'] > 0:
            print(f"   ℹ️ 信息: 有 {status_counts['disabled'] + status_counts['deleted']} 个非激活URL被正确过滤")
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await redis_client.close()


async def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_task_active_urls.py <task_id>")
        print("示例: python check_task_active_urls.py 20baf174-bb0d-4d45-b931-d1580bac02da")
        sys.exit(1)
    
    task_id = sys.argv[1]
    await check_task_active_urls(task_id)


if __name__ == "__main__":
    asyncio.run(main())
