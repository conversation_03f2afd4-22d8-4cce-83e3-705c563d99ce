@echo off
chcp 65001 >nul

echo 🚀 启动MonIt监控系统...

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 创建日志目录
echo 📁 创建日志目录...
if not exist logs mkdir logs
if not exist logs\backend mkdir logs\backend
if not exist logs\celery mkdir logs\celery
if not exist logs\crawler mkdir logs\crawler
if not exist logs\frontend mkdir logs\frontend

REM 创建网络（如果不存在）
echo 🌐 创建Docker网络...
docker network create monit-network >nul 2>&1

REM 启动监控服务
echo 📊 启动监控服务...
docker-compose -f docker-compose.monitoring.yml up -d

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose -f docker-compose.monitoring.yml ps

REM 显示访问地址
echo.
echo ✅ 监控系统启动完成！
echo.
echo 📊 监控面板访问地址：
echo   - Grafana:      http://localhost:3001 (admin/admin123)
echo   - Prometheus:   http://localhost:9090
echo   - Kibana:       http://localhost:5601
echo   - Elasticsearch: http://localhost:9200
echo.
echo 📝 日志目录: .\logs\
echo ⚙️  配置目录: .\monitoring\
echo.
echo 🔧 使用以下命令管理监控系统：
echo   启动: docker-compose -f docker-compose.monitoring.yml up -d
echo   停止: docker-compose -f docker-compose.monitoring.yml down
echo   查看日志: docker-compose -f docker-compose.monitoring.yml logs -f [service_name]
echo.
pause
