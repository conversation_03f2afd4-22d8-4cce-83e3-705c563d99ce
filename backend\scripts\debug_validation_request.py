#!/usr/bin/env python3
"""
调试验证请求

捕获和分析前端发送的验证请求数据
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 创建调试应用
debug_app = FastAPI()

@debug_app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有请求"""
    if request.url.path == "/api/v1/tasks/validate":
        print(f"\n🔍 捕获验证请求")
        print(f"方法: {request.method}")
        print(f"URL: {request.url}")
        print(f"Headers: {dict(request.headers)}")
        
        # 读取请求体
        body = await request.body()
        if body:
            try:
                json_data = json.loads(body)
                print(f"请求体:")
                print(json.dumps(json_data, indent=2, ensure_ascii=False))
                
                # 分析数据结构
                print(f"\n📊 数据结构分析:")
                print(f"name: {type(json_data.get('name'))} = {json_data.get('name')}")
                print(f"url_ids: {type(json_data.get('url_ids'))} = {json_data.get('url_ids')}")
                
                schedule = json_data.get('schedule', {})
                print(f"schedule: {type(schedule)}")
                for key, value in schedule.items():
                    print(f"  {key}: {type(value)} = {value}")
                
            except Exception as e:
                print(f"解析请求体失败: {e}")
                print(f"原始数据: {body}")
    
    response = await call_next(request)
    return response

@debug_app.post("/api/v1/tasks/validate")
async def debug_validate(request: Request):
    """调试验证端点"""
    try:
        body = await request.body()
        json_data = json.loads(body)
        
        print(f"\n✅ 成功接收验证请求")
        
        # 模拟验证逻辑
        errors = []
        warnings = []
        suggestions = []
        
        # 检查必需字段
        if not json_data.get('name'):
            errors.append("缺少任务名称")
        
        if not json_data.get('url_ids'):
            errors.append("缺少URL列表")
        elif not isinstance(json_data.get('url_ids'), list):
            errors.append("URL列表格式错误")
        
        schedule = json_data.get('schedule', {})
        if not schedule:
            errors.append("缺少调度配置")
        else:
            # 检查调度配置
            if not schedule.get('type'):
                errors.append("缺少调度类型")
            
            if schedule.get('type') == 'daily':
                if not schedule.get('time') and not schedule.get('start_time'):
                    errors.append("daily类型缺少时间配置")
            
            if 'enabled' not in schedule:
                warnings.append("未指定调度启用状态，默认为启用")
            
            if not schedule.get('timezone'):
                warnings.append("未指定时区，默认为Asia/Shanghai")
        
        # 返回验证结果
        result = {
            "success": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "suggestions": suggestions
        }
        
        print(f"\n📤 返回验证结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return JSONResponse(content=result)
        
    except Exception as e:
        print(f"❌ 处理验证请求时发生错误: {e}")
        import traceback
        traceback.print_exc()
        
        return JSONResponse(
            status_code=422,
            content={"detail": f"验证请求处理失败: {str(e)}"}
        )

@debug_app.get("/health")
async def health():
    """健康检查"""
    return {"status": "ok", "message": "调试服务运行中"}

def main():
    """启动调试服务"""
    print("🚀 启动验证请求调试服务")
    print("=" * 50)
    print("服务地址: http://localhost:8001")
    print("验证端点: http://localhost:8001/api/v1/tasks/validate")
    print("健康检查: http://localhost:8001/health")
    print("")
    print("请将前端API地址临时改为 http://localhost:8001 来捕获请求")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    uvicorn.run(debug_app, host="0.0.0.0", port=8001, log_level="info")

if __name__ == "__main__":
    main()
