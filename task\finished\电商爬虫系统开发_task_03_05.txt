# 子任务03-05: 错误处理和重试机制

## 任务描述
建立完善的错误处理和重试机制，确保爬虫系统的稳定性和可靠性，能够优雅地处理各种异常情况并自动恢复。

## 具体任务内容

### 1. 异常分类和处理
- 网络错误处理(超时、连接失败、DNS错误)
- 浏览器错误处理(崩溃、页面加载失败)
- 数据提取错误处理(选择器失效、数据格式异常)
- 系统资源错误处理(内存不足、磁盘空间)

### 2. 智能重试机制
- 指数退避重试策略
- 基于错误类型的重试决策
- 重试次数和时间间隔配置
- 重试状态跟踪和统计

### 3. 错误记录和分析
- 结构化错误日志记录
- 错误统计和趋势分析
- 错误报告和告警机制
- 错误模式识别和预警

### 4. 自动恢复机制
- 浏览器实例自动重启
- 网络连接自动重建
- 代理切换和恢复
- 任务状态自动恢复

### 5. 故障转移和降级
- 多级故障转移策略
- 服务降级和限流
- 紧急停止和安全模式
- 数据一致性保障

## 技术实现

### 错误处理框架
```python
import asyncio
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
import random

class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network"
    BROWSER_ERROR = "browser"
    EXTRACTION_ERROR = "extraction"
    SYSTEM_ERROR = "system"
    UNKNOWN_ERROR = "unknown"

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ErrorInfo:
    """错误信息"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    timestamp: float
    context: Dict[str, Any]
    retry_count: int = 0

class RetryStrategy:
    """重试策略"""
    
    def __init__(self, config: Dict):
        self.max_retries = config.get('max_retries', 3)
        self.base_delay = config.get('base_delay', 1.0)
        self.max_delay = config.get('max_delay', 60.0)
        self.exponential_base = config.get('exponential_base', 2)
        self.jitter = config.get('jitter', True)
    
    def should_retry(self, error_info: ErrorInfo) -> bool:
        """判断是否应该重试"""
        if error_info.retry_count >= self.max_retries:
            return False
        
        # 根据错误类型决定是否重试
        retryable_errors = {
            ErrorType.NETWORK_ERROR,
            ErrorType.BROWSER_ERROR
        }
        
        return error_info.error_type in retryable_errors
    
    def get_delay(self, retry_count: int) -> float:
        """计算重试延迟时间"""
        delay = self.base_delay * (self.exponential_base ** retry_count)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            delay *= (0.5 + random.random() * 0.5)
        
        return delay

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.retry_strategy = RetryStrategy(config.get('retry', {}))
        self.error_stats = {}
        self.logger = logging.getLogger(__name__)
    
    async def handle_error(self, error: Exception, context: Dict[str, Any]) -> ErrorInfo:
        """处理错误"""
        error_info = self._classify_error(error, context)
        await self._log_error(error_info)
        await self._update_stats(error_info)
        
        return error_info
    
    def _classify_error(self, error: Exception, context: Dict) -> ErrorInfo:
        """分类错误"""
        error_type = ErrorType.UNKNOWN_ERROR
        severity = ErrorSeverity.MEDIUM
        
        if isinstance(error, (ConnectionError, TimeoutError)):
            error_type = ErrorType.NETWORK_ERROR
            severity = ErrorSeverity.MEDIUM
        elif "browser" in str(error).lower():
            error_type = ErrorType.BROWSER_ERROR
            severity = ErrorSeverity.HIGH
        elif "extraction" in str(error).lower():
            error_type = ErrorType.EXTRACTION_ERROR
            severity = ErrorSeverity.LOW
        
        return ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=str(error),
            timestamp=time.time(),
            context=context
        )
    
    async def retry_with_backoff(self, func: Callable, *args, **kwargs) -> Any:
        """带退避的重试执行"""
        last_error = None
        
        for attempt in range(self.retry_strategy.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_error = e
                error_info = await self.handle_error(e, {'attempt': attempt})
                
                if not self.retry_strategy.should_retry(error_info):
                    break
                
                if attempt < self.retry_strategy.max_retries:
                    delay = self.retry_strategy.get_delay(attempt)
                    await asyncio.sleep(delay)
        
        raise last_error
```

## 配置文件结构
```yaml
# error_handling_config.yaml
retry:
  max_retries: 3
  base_delay: 1.0
  max_delay: 60.0
  exponential_base: 2
  jitter: true

error_classification:
  network_errors:
    - "ConnectionError"
    - "TimeoutError"
    - "DNSError"
  
  browser_errors:
    - "BrowserDisconnectedError"
    - "PageCrashedError"
    - "NavigationError"
  
  extraction_errors:
    - "SelectorNotFoundError"
    - "DataValidationError"

recovery:
  browser_restart_threshold: 5
  network_retry_interval: 30
  max_consecutive_failures: 10

logging:
  error_log_level: "ERROR"
  error_log_format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  error_log_file: "logs/crawler_errors.log"
  error_log_rotation: "1 day"
  error_log_retention: "30 days"
```

## 预期产出物
1. **错误处理器** (`error_handler.py`)
2. **重试机制** (`retry_manager.py`)
3. **错误分类器** (`error_classifier.py`)
4. **恢复管理器** (`recovery_manager.py`)
5. **错误统计分析** (`error_statistics.py`)
6. **错误配置管理** (`error_config.py`)
7. **单元测试文件** (`tests/test_error_handling.py`)
8. **集成测试用例** (`tests/integration/test_recovery.py`)

## 验收标准
- [ ] 错误分类准确有效
- [ ] 重试机制工作正常
- [ ] 自动恢复功能稳定
- [ ] 错误日志记录完整
- [ ] 故障转移机制有效
- [ ] 系统稳定性提升明显
- [ ] 通过所有单元测试
- [ ] 压力测试下表现良好

## 性能指标
- **错误检测时间**: ≤1秒
- **自动恢复时间**: ≤30秒
- **系统可用性**: ≥99%
- **错误处理开销**: ≤5%
- **数据一致性**: 100%

## 预估工时
1天

## 依赖关系
- 前置任务: Task 03-04 (数据提取和解析器)
- 为Task 03-06提供稳定性基础

## 状态
待开始 