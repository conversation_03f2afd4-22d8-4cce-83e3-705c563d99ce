#!/usr/bin/env python3
"""
测试WebSocket连接
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_websocket():
    """测试WebSocket连接"""
    uri = "ws://localhost:8000/ws"
    
    try:
        print(f"🔌 连接到 {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 等待欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📨 收到欢迎消息: {welcome_msg}")
            
            # 发送ping消息
            ping_msg = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(ping_msg))
            print(f"📤 发送ping消息: {ping_msg}")
            
            # 等待pong响应
            pong_response = await websocket.recv()
            print(f"📨 收到pong响应: {pong_response}")
            
            # 发送测试消息
            test_msg = {
                "type": "test",
                "data": {"message": "Hello from test script!"},
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_msg))
            print(f"📤 发送测试消息: {test_msg}")
            
            # 等待回显
            echo_response = await websocket.recv()
            print(f"📨 收到回显: {echo_response}")
            
            print("🎉 WebSocket测试成功!")
            
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_websocket())
    if success:
        print("\n✅ WebSocket连接测试通过!")
    else:
        print("\n❌ WebSocket连接测试失败!")
