"""
重试管理器

增强的任务重试和失败处理系统
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import redis.asyncio as redis

from app.core.task_splitter import TaskBatch, TaskPriority
from app.database import get_async_session
from app.models.task import CrawlTask
from sqlalchemy import select, update

logger = logging.getLogger(__name__)


class FailureType(Enum):
    """失败类型"""
    NETWORK_ERROR = "network_error"        # 网络错误
    TIMEOUT_ERROR = "timeout_error"        # 超时错误
    API_LIMIT_ERROR = "api_limit_error"    # API限制错误
    PARSE_ERROR = "parse_error"            # 解析错误
    UNKNOWN_ERROR = "unknown_error"        # 未知错误


class RetryStrategy(Enum):
    """重试策略"""
    IMMEDIATE = "immediate"                # 立即重试
    EXPONENTIAL_BACKOFF = "exponential"    # 指数退避
    LINEAR_BACKOFF = "linear"              # 线性退避
    FIXED_DELAY = "fixed"                  # 固定延迟
    NO_RETRY = "no_retry"                  # 不重试


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    base_delay: float = 60.0               # 基础延迟（秒）
    max_delay: float = 3600.0              # 最大延迟（秒）
    backoff_multiplier: float = 2.0        # 退避倍数
    jitter: bool = True                    # 是否添加随机抖动
    
    # 不同错误类型的重试策略
    error_strategies: Dict[FailureType, RetryStrategy] = None
    
    def __post_init__(self):
        if self.error_strategies is None:
            self.error_strategies = {
                FailureType.NETWORK_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
                FailureType.TIMEOUT_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF,
                FailureType.API_LIMIT_ERROR: RetryStrategy.FIXED_DELAY,
                FailureType.PARSE_ERROR: RetryStrategy.LINEAR_BACKOFF,
                FailureType.UNKNOWN_ERROR: RetryStrategy.EXPONENTIAL_BACKOFF
            }


@dataclass
class FailedTask:
    """失败任务信息"""
    batch_id: str
    task_id: int
    urls: List[str]
    failure_type: FailureType
    error_message: str
    retry_count: int
    last_attempt: datetime
    next_retry_time: Optional[datetime]
    priority: TaskPriority


class RetryManager:
    """重试管理器"""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        """初始化重试管理器
        
        Args:
            config: 重试配置
        """
        self.config = config or RetryConfig()
        self.logger = logging.getLogger(__name__)
        
        # Redis连接
        self.redis: Optional[redis.Redis] = None
        
        # 失败任务队列
        self.failed_tasks: Dict[str, FailedTask] = {}
        self.retry_queue: List[FailedTask] = []
        
        # 死信队列
        self.dead_letter_queue: List[FailedTask] = []
        
        # 统计信息
        self.stats = {
            "total_failures": 0,
            "total_retries": 0,
            "successful_retries": 0,
            "abandoned_tasks": 0,
            "failure_types": {ft.value: 0 for ft in FailureType}
        }
    
    async def start(self, redis_url: str = "redis://redis:6379/0"):
        """启动重试管理器"""
        try:
            self.redis = redis.from_url(redis_url)
            await self.redis.ping()
            self.logger.info("Retry manager started")
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def stop(self):
        """停止重试管理器"""
        if self.redis:
            await self.redis.close()
            self.logger.info("Retry manager stopped")
    
    async def handle_batch_failure(
        self,
        batch_id: str,
        task_id: int,
        urls: List[str],
        error: Exception,
        priority: TaskPriority = TaskPriority.NORMAL
    ) -> bool:
        """处理批次失败
        
        Args:
            batch_id: 批次ID
            task_id: 任务ID
            urls: 失败的URL列表
            error: 错误信息
            priority: 任务优先级
            
        Returns:
            bool: 是否会重试
        """
        try:
            # 分析失败类型
            failure_type = self._analyze_failure_type(error)
            
            # 获取当前重试次数
            retry_count = await self._get_retry_count(batch_id)
            
            # 创建失败任务记录
            failed_task = FailedTask(
                batch_id=batch_id,
                task_id=task_id,
                urls=urls,
                failure_type=failure_type,
                error_message=str(error),
                retry_count=retry_count,
                last_attempt=datetime.now(),
                next_retry_time=None,
                priority=priority
            )
            
            # 更新统计
            self.stats["total_failures"] += 1
            self.stats["failure_types"][failure_type.value] += 1
            
            # 判断是否应该重试
            if await self._should_retry(failed_task):
                return await self._schedule_retry(failed_task)
            else:
                return await self._move_to_dead_letter_queue(failed_task)
                
        except Exception as e:
            self.logger.error(f"Failed to handle batch failure: {e}")
            return False
    
    def _analyze_failure_type(self, error: Exception) -> FailureType:
        """分析失败类型
        
        Args:
            error: 错误对象
            
        Returns:
            FailureType: 失败类型
        """
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        if "timeout" in error_str or "timeout" in error_type:
            return FailureType.TIMEOUT_ERROR
        elif "connection" in error_str or "network" in error_str:
            return FailureType.NETWORK_ERROR
        elif "rate limit" in error_str or "429" in error_str:
            return FailureType.API_LIMIT_ERROR
        elif "parse" in error_str or "json" in error_str:
            return FailureType.PARSE_ERROR
        else:
            return FailureType.UNKNOWN_ERROR
    
    async def _get_retry_count(self, batch_id: str) -> int:
        """获取重试次数
        
        Args:
            batch_id: 批次ID
            
        Returns:
            int: 重试次数
        """
        try:
            if self.redis:
                count = await self.redis.get(f"retry_count:{batch_id}")
                return int(count) if count else 0
            return 0
        except Exception:
            return 0
    
    async def _should_retry(self, failed_task: FailedTask) -> bool:
        """判断是否应该重试
        
        Args:
            failed_task: 失败任务
            
        Returns:
            bool: 是否应该重试
        """
        # 检查重试次数
        if failed_task.retry_count >= self.config.max_retries:
            self.logger.info(f"Task {failed_task.batch_id} exceeded max retries")
            return False
        
        # 检查重试策略
        strategy = self.config.error_strategies.get(
            failed_task.failure_type, 
            RetryStrategy.NO_RETRY
        )
        
        if strategy == RetryStrategy.NO_RETRY:
            self.logger.info(f"Task {failed_task.batch_id} marked as no retry")
            return False
        
        return True
    
    async def _schedule_retry(self, failed_task: FailedTask) -> bool:
        """调度重试任务
        
        Args:
            failed_task: 失败任务
            
        Returns:
            bool: 是否成功调度
        """
        try:
            # 计算重试延迟
            delay = self._calculate_retry_delay(failed_task)
            failed_task.next_retry_time = datetime.now() + timedelta(seconds=delay)
            
            # 降低优先级
            if failed_task.retry_count > 0:
                failed_task.priority = self._downgrade_priority(failed_task.priority)
            
            # 增加重试次数
            failed_task.retry_count += 1
            
            # 存储到Redis
            if self.redis:
                await self.redis.setex(
                    f"retry_count:{failed_task.batch_id}",
                    3600,  # 1小时过期
                    failed_task.retry_count
                )
            
            # 添加到重试队列
            self.retry_queue.append(failed_task)
            self.failed_tasks[failed_task.batch_id] = failed_task
            
            # 更新统计
            self.stats["total_retries"] += 1
            
            self.logger.info(
                f"Scheduled retry for task {failed_task.batch_id} "
                f"(attempt {failed_task.retry_count}/{self.config.max_retries}) "
                f"in {delay:.1f} seconds"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to schedule retry: {e}")
            return False
    
    def _calculate_retry_delay(self, failed_task: FailedTask) -> float:
        """计算重试延迟
        
        Args:
            failed_task: 失败任务
            
        Returns:
            float: 延迟时间（秒）
        """
        strategy = self.config.error_strategies.get(
            failed_task.failure_type,
            RetryStrategy.EXPONENTIAL_BACKOFF
        )
        
        if strategy == RetryStrategy.IMMEDIATE:
            delay = 0
        elif strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
        elif strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * failed_task.retry_count
        elif strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay * (
                self.config.backoff_multiplier ** failed_task.retry_count
            )
        else:
            delay = self.config.base_delay
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        # 添加随机抖动
        if self.config.jitter:
            import random
            jitter = random.uniform(0.8, 1.2)
            delay *= jitter
        
        return delay
    
    def _downgrade_priority(self, priority: TaskPriority) -> TaskPriority:
        """降低任务优先级
        
        Args:
            priority: 当前优先级
            
        Returns:
            TaskPriority: 降级后的优先级
        """
        if priority == TaskPriority.URGENT:
            return TaskPriority.HIGH
        elif priority == TaskPriority.HIGH:
            return TaskPriority.NORMAL
        elif priority == TaskPriority.NORMAL:
            return TaskPriority.LOW
        else:
            return TaskPriority.LOW
    
    async def _move_to_dead_letter_queue(self, failed_task: FailedTask) -> bool:
        """移动到死信队列
        
        Args:
            failed_task: 失败任务
            
        Returns:
            bool: 是否成功移动
        """
        try:
            self.dead_letter_queue.append(failed_task)
            self.stats["abandoned_tasks"] += 1
            
            # 更新数据库状态
            await self._update_task_status(failed_task.task_id, "abandoned")
            
            self.logger.warning(
                f"Task {failed_task.batch_id} moved to dead letter queue "
                f"after {failed_task.retry_count} retries"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to move task to dead letter queue: {e}")
            return False
    
    async def get_ready_retries(self) -> List[FailedTask]:
        """获取准备重试的任务
        
        Returns:
            List[FailedTask]: 准备重试的任务列表
        """
        now = datetime.now()
        ready_tasks = []
        
        for task in self.retry_queue[:]:
            if task.next_retry_time and task.next_retry_time <= now:
                ready_tasks.append(task)
                self.retry_queue.remove(task)
        
        return ready_tasks
    
    async def _update_task_status(self, task_id: int, status: str):
        """更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
        """
        try:
            async with get_async_session() as session:
                await session.execute(
                    update(CrawlTask)
                    .where(CrawlTask.id == task_id)
                    .values(status=status)
                )
                await session.commit()
        except Exception as e:
            self.logger.error(f"Failed to update task status: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.stats,
            "retry_queue_size": len(self.retry_queue),
            "dead_letter_queue_size": len(self.dead_letter_queue),
            "failed_tasks_count": len(self.failed_tasks)
        }
