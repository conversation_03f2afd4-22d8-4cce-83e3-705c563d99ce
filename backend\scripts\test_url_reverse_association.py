#!/usr/bin/env python3
"""
测试URL反向关联功能

验证URL反向关联的正确性
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_url_reverse_association():
    """测试URL反向关联功能"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试URL反向关联功能")
    print("=" * 50)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        test_urls = [
            {
                "url": "https://www.mercadolibre.com.ar/shared-url-1",
                "platform": "mercadolibre",
                "title": "共享URL 1",
            },
            {
                "url": "https://www.mercadolibre.com.ar/shared-url-2", 
                "platform": "mercadolibre",
                "title": "共享URL 2",
            },
            {
                "url": "https://www.mercadolibre.com.ar/unique-url-1", 
                "platform": "mercadolibre",
                "title": "独占URL 1",
            }
        ]
        
        for i, url_data in enumerate(test_urls, 1):
            print(f"  创建URL {i}...")
            
            # 生成URL ID和哈希
            url_id = str(uuid4())
            url_hash = hashlib.md5(url_data['url'].encode()).hexdigest()
            now = datetime.now().isoformat()
            
            # 创建URL项目数据
            url_item_data = {
                'id': url_id,
                'url': url_data['url'],
                'platform': url_data['platform'],
                'source_file': 'reverse_test',
                'added_at': now,
                'status': 'active',
                'last_check': '',
                'check_count': '0',
                'success_count': '0',
                'error_count': '0',
                'metadata': json.dumps({
                    'url_hash': url_hash,
                    'title': url_data.get('title', ''),
                    'added_via': 'reverse_test'
                })
            }
            
            # 存储到Redis
            url_key = f"url_pool:items:{url_id}"
            r.hset(url_key, mapping=url_item_data)
            
            # 添加到索引
            r.sadd("url_pool:all_ids", url_id)
            r.sadd(f"url_pool:platform:{url_data['platform']}", url_id)
            r.sadd("url_pool:status:active", url_id)
            r.sadd("url_pool:source:reverse_test", url_id)
            r.sadd("url_pool:url_hashes", url_hash)
            
            created_url_ids.append(url_id)
            print(f"    ✅ URL创建成功: {url_id}")
        
        print(f"  ✅ 成功创建 {len(created_url_ids)} 个URL")
        
        # 步骤2: 创建多个任务，使用重叠的URL
        print(f"\n📋 步骤2: 创建多个任务测试URL反向关联")
        
        task_configs = [
            {
                "name": "任务A - 使用共享URL",
                "url_ids": [created_url_ids[0], created_url_ids[1]],  # 共享URL 1, 2
            },
            {
                "name": "任务B - 使用部分共享URL",
                "url_ids": [created_url_ids[1], created_url_ids[2]],  # 共享URL 2, 独占URL 1
            },
            {
                "name": "任务C - 只使用共享URL",
                "url_ids": [created_url_ids[0]],  # 只有共享URL 1
            }
        ]
        
        for i, task_config in enumerate(task_configs, 1):
            print(f"  创建任务 {i}: {task_config['name']}")
            
            create_data = {
                "name": task_config['name'],
                "description": f"用于测试URL反向关联的任务 {i}",
                "url_ids": task_config['url_ids'],
                "schedule": {
                    "type": "daily",
                    "enabled": True,
                    "time": f"{8+i}:00",
                    "timezone": "Asia/Shanghai"
                },
                "config": {
                    "platform": "mercadolibre",
                    "batch_size": 10,
                    "retry_count": 3,
                    "timeout": 300,
                    "priority": "normal",
                    "concurrent_limit": 5,
                    "enable_notifications": True
                }
            }
            
            create_response = requests.post(
                f"{base_url}/api/v1/tasks/create-from-urls",
                json=create_data,
                timeout=10
            )
            
            if create_response.status_code == 200:
                create_result = create_response.json()
                task_id = create_result.get('task_id')
                task_ids.append(task_id)
                print(f"    ✅ 任务创建成功: {task_id}")
                print(f"    URL数量: {len(task_config['url_ids'])}")
            else:
                print(f"    ❌ 任务创建失败: {create_response.text}")
                return
        
        # 步骤3: 验证URL反向关联
        print(f"\n📋 步骤3: 验证URL反向关联")
        
        for i, url_id in enumerate(created_url_ids):
            print(f"  URL {i+1} ({url_id[:8]}...):")
            
            # 检查反向关联键是否存在
            url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
            if r.exists(url_tasks_key):
                associated_tasks = r.smembers(url_tasks_key)
                print(f"    ✅ 关联的任务数量: {len(associated_tasks)}")
                for task_id in associated_tasks:
                    print(f"      - {task_id[:8]}...")
            else:
                print(f"    ❌ 没有找到反向关联")
        
        # 步骤4: 验证预期的关联关系
        print(f"\n📋 步骤4: 验证预期的关联关系")
        
        expected_associations = {
            created_url_ids[0]: 2,  # 共享URL 1 应该被任务A和任务C使用
            created_url_ids[1]: 2,  # 共享URL 2 应该被任务A和任务B使用
            created_url_ids[2]: 1,  # 独占URL 1 应该只被任务B使用
        }
        
        all_correct = True
        for url_id, expected_count in expected_associations.items():
            url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
            actual_count = r.scard(url_tasks_key)
            
            if actual_count == expected_count:
                print(f"  ✅ URL {url_id[:8]}... 关联数量正确: {actual_count}")
            else:
                print(f"  ❌ URL {url_id[:8]}... 关联数量错误: 期望{expected_count}, 实际{actual_count}")
                all_correct = False
        
        # 步骤5: 测试删除URL时的反向关联清理
        print(f"\n📋 步骤5: 测试删除任务时的反向关联清理")
        
        if task_ids:
            # 删除第一个任务
            delete_task_id = task_ids[0]
            print(f"  删除任务: {delete_task_id[:8]}...")
            
            delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{delete_task_id}", timeout=10)
            if delete_response.status_code in [200, 204]:
                print(f"    ✅ 任务删除成功")
                
                # 检查反向关联是否正确更新
                print(f"  检查反向关联更新:")
                for url_id in created_url_ids[:2]:  # 检查前两个URL（被删除任务使用的）
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    remaining_tasks = r.smembers(url_tasks_key)
                    print(f"    URL {url_id[:8]}... 剩余关联任务: {len(remaining_tasks)}")
            else:
                print(f"    ❌ 任务删除失败: {delete_response.text}")
        
        if all_correct:
            print(f"\n🎉 URL反向关联功能测试完全成功!")
        else:
            print(f"\n⚠️ URL反向关联功能存在问题")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:reverse_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_url_reverse_association()
