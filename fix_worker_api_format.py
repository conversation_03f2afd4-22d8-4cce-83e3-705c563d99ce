#!/usr/bin/env python3
"""
修复Worker API请求格式
将当前系统的简化格式转换为Worker后端期望的完整格式
"""

import json
from typing import Dict, List, Any, Optional

def create_worker_api_payload(
    urls: List[str],
    platform: str = "mercadolibre",
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    创建符合Worker后端期望的API请求格式
    
    Args:
        urls: URL列表
        platform: 平台类型
        options: 额外选项
        
    Returns:
        Dict: 完整的API请求payload
    """
    
    # 基础浏览器配置
    browser_config = {
        "headless": True,
        "verbose": False,
        "viewport_width": 1920,
        "viewport_height": 1080,
        "wait_for": 2,
        "timeout": 30,
        "ignore_https_errors": True,
        "extra_args": [
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation"
        ]
    }
    
    # 基础爬虫配置
    crawler_config = {
        "method": "arun_many",
        "verbose": True,
        "check_robots_txt": False,
        "fetch_ssl_certificate": False,
        "simulate_user": True,
        "magic": True,
        "override_navigator": True,
        "remove_overlay_elements": True,
        "ignore_body_visibility": True,
        "adjust_viewport_to_content": True,
        "wait_until": "domcontentloaded",
        "wait_for_images": False,
        "page_timeout": 60000,
        "delay_before_return_html": 0.1,
        "js_only": False,
        "scan_full_page": True,
        "process_iframes": True,
        "scroll_delay": 0.2,
        "cache_mode": "BYPASS",
        "screenshot": False,
        "pdf": False,
        "capture_mhtml": False,
        "exclude_external_images": False,
        "exclude_all_images": False,
        "image_score_threshold": 50,
        "image_description_min_word_threshold": 50,
        "table_score_threshold": 7,
        "capture_network_requests": False,
        "capture_console_messages": False,
        "log_console": False,
        "extraction_strategy": "LLMExtractionStrategy",
        "chunking_strategy": "IdentityChunking",
        "markdown_generator": "DefaultMarkdownGenerator",
        "bypass_cache": True,
        "semaphore_count": 2,
        "stream": True,
        "mean_delay": 0.1,
        "max_range": 3,
        "task_type": "batch",
        "scheduler": {
            "type": "AsyncQueueManager",
            "pool_size": 10,
            "memory_threshold": 4096
        },
        "monitor": {
            "display_mode": "detailed",
            "show_progress": True,
            "log_errors": True
        }
    }
    
    # 如果有options，合并配置
    if options:
        # 合并浏览器配置
        if "browser_config" in options:
            browser_config.update(options["browser_config"])
        
        # 合并爬虫配置
        if "crawler_config" in options:
            crawler_config.update(options["crawler_config"])
    
    # 构建完整的请求payload
    payload = {
        "urls": urls,
        "browser_config": browser_config,
        "crawler_config": crawler_config
    }
    
    return payload

def test_payload_creation():
    """测试payload创建"""
    
    # 测试URL
    test_urls = [
        "https://articulo.mercadolibre.com.mx/MLM-2317168093-car-crash-cymbal-mini-platillos-bateria-de-choque-de-auto-_JM",
        "https://www.mercadolibre.com.mx/mini-platillo-para-auto-cymbal-bateria-tambor-crash-auto/up/MLMU3190246156",
        "https://articulo.mercadolibre.com.mx/MLM-3699672552-car-crash-cymbal-platillo-de-ventilacion-de-aire-para-auto-_JM"
    ]
    
    # 创建payload
    payload = create_worker_api_payload(test_urls, "mercadolibre")
    
    # 输出格式化的JSON
    print("生成的Worker API请求格式:")
    print("=" * 60)
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    
    # 验证关键字段
    print("\n" + "=" * 60)
    print("格式验证:")
    print(f"✅ URLs数量: {len(payload['urls'])}")
    print(f"✅ 浏览器配置: {'browser_config' in payload}")
    print(f"✅ 爬虫配置: {'crawler_config' in payload}")
    print(f"✅ 提取策略: {payload['crawler_config']['extraction_strategy']}")
    print(f"✅ 任务类型: {payload['crawler_config']['task_type']}")
    
    return payload

if __name__ == "__main__":
    test_payload_creation()
