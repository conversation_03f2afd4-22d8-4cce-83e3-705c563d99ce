#!/usr/bin/env python3
"""
完整的任务创建集成测试

1. 直接向URL池添加测试URL
2. 从URL池创建任务
3. 验证任务在监控任务管理页面显示
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_complete_integration():
    """完整的任务创建集成测试"""
    base_url = "http://localhost:8000"
    
    print("🔍 完整的任务创建集成测试")
    print("=" * 50)
    
    created_url_ids = []
    task_id = None
    
    try:
        # 步骤1: 直接向URL池添加测试URL
        print("\n📋 步骤1: 向URL池添加测试URL")
        
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        test_urls = [
            {
                "url": "https://www.mercadolibre.com.ar/test-product-1",
                "platform": "mercadolibre",
                "title": "测试商品1",
                "price": "100.00",
                "currency": "ARS"
            },
            {
                "url": "https://www.mercadolibre.com.ar/test-product-2", 
                "platform": "mercadolibre",
                "title": "测试商品2",
                "price": "200.00",
                "currency": "ARS"
            }
        ]
        
        for i, url_data in enumerate(test_urls, 1):
            print(f"  添加URL {i}...")
            
            # 生成URL ID和哈希
            url_id = str(uuid4())
            url_hash = hashlib.md5(url_data['url'].encode()).hexdigest()
            now = datetime.now().isoformat()
            
            # 创建URL项目数据
            url_item_data = {
                'id': url_id,
                'url': url_data['url'],
                'platform': url_data['platform'],
                'source_file': 'integration_test',
                'added_at': now,
                'status': 'active',
                'last_check': '',
                'check_count': '0',
                'success_count': '0',
                'error_count': '0',
                'metadata': json.dumps({
                    'url_hash': url_hash,
                    'title': url_data.get('title', ''),
                    'price': url_data.get('price', ''),
                    'currency': url_data.get('currency', ''),
                    'added_via': 'integration_test'
                })
            }
            
            # 存储到Redis
            url_key = f"url_pool:items:{url_id}"
            r.hset(url_key, mapping=url_item_data)
            
            # 添加到索引
            r.sadd("url_pool:all_ids", url_id)
            r.sadd(f"url_pool:platform:{url_data['platform']}", url_id)
            r.sadd("url_pool:status:active", url_id)
            r.sadd("url_pool:source:integration_test", url_id)
            r.sadd("url_pool:url_hashes", url_hash)
            
            created_url_ids.append(url_id)
            print(f"    ✅ URL添加成功: {url_id}")
        
        print(f"  ✅ 成功添加 {len(created_url_ids)} 个URL到URL池")
        
        # 步骤2: 验证URL池API
        print(f"\n📋 步骤2: 验证URL池API")
        pool_response = requests.get(f"{base_url}/api/v1/urls/pool?page_size=10", timeout=10)
        print(f"  URL池API状态码: {pool_response.status_code}")
        
        if pool_response.status_code == 200:
            pool_data = pool_response.json()
            total_urls = pool_data.get('total', 0)
            print(f"  ✅ URL池总数: {total_urls}")
            
            # 检查我们添加的URL是否在列表中
            found_urls = 0
            for url_item in pool_data.get('data', []):
                if url_item.get('id') in created_url_ids:
                    found_urls += 1
            print(f"  ✅ 找到我们添加的URL: {found_urls}/{len(created_url_ids)}")
        else:
            print(f"  ❌ URL池API失败: {pool_response.text}")
            return
        
        # 步骤3: 获取创建前的任务列表
        print("\n📋 步骤3: 获取创建前的任务列表")
        list_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response.status_code}")
        
        if list_response.status_code == 200:
            before_data = list_response.json()
            before_count = before_data.get('total', 0)
            print(f"  创建前任务数量: {before_count}")
        else:
            print(f"  ❌ 获取任务列表失败: {list_response.text}")
            return
        
        # 步骤4: 从URL池创建任务
        print(f"\n📋 步骤4: 从URL池创建任务")
        create_data = {
            "name": "完整集成测试任务",
            "description": "用于测试完整集成流程的任务",
            "url_ids": created_url_ids,
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "10:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "retry_count": 3,
                "timeout": 300,
                "priority": "normal",
                "concurrent_limit": 5,
                "enable_notifications": True
            }
        }
        
        print(f"  发送创建请求...")
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        print(f"  状态码: {create_response.status_code}")
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            print(f"  ✅ 任务创建成功")
            task_id = create_result.get('task_id')
            print(f"  任务ID: {task_id}")
            print(f"  任务名称: {create_result.get('task_name')}")
            print(f"  URL数量: {create_result.get('url_count')}")
        else:
            print(f"  ❌ 任务创建失败")
            print(f"  错误: {create_response.text}")
            return
        
        # 步骤5: 获取创建后的任务列表
        print(f"\n📋 步骤5: 获取创建后的任务列表")
        list_response2 = requests.get(f"{base_url}/api/v1/monitoring-tasks/", timeout=10)
        print(f"  状态码: {list_response2.status_code}")
        
        if list_response2.status_code == 200:
            after_data = list_response2.json()
            after_count = after_data.get('total', 0)
            print(f"  创建后任务数量: {after_count}")
            
            if after_count > before_count:
                print(f"  ✅ 任务数量增加了 {after_count - before_count} 个")
                
                # 检查新创建的任务是否在列表中
                tasks = after_data.get('data', [])
                found_task = None
                for task in tasks:
                    if task.get('id') == task_id:
                        found_task = task
                        break
                
                if found_task:
                    print(f"  ✅ 新创建的任务在列表中找到")
                    print(f"  任务名称: {found_task.get('name')}")
                    print(f"  任务状态: {found_task.get('status')}")
                    print(f"  URL数量: {found_task.get('total_urls', 0)}")
                    print(f"  平台: {found_task.get('config', {}).get('platform', 'N/A')}")
                    print(f"  优先级: {found_task.get('config', {}).get('priority', 'N/A')}")
                else:
                    print(f"  ❌ 新创建的任务在列表中未找到")
                    print(f"  任务列表中的任务ID: {[t.get('id') for t in tasks]}")
                    print(f"  查找的任务ID: {task_id}")
            else:
                print(f"  ❌ 任务数量没有增加")
        else:
            print(f"  ❌ 获取任务列表失败: {list_response2.text}")
        
        # 步骤6: 直接获取新创建的任务详情
        if task_id:
            print(f"\n📋 步骤6: 获取新创建任务的详情")
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            print(f"  状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 任务详情获取成功")
                task_detail = detail_data.get('data', {})
                print(f"  任务名称: {task_detail.get('name')}")
                print(f"  任务状态: {task_detail.get('status')}")
                print(f"  URL数量: {task_detail.get('total_urls', 0)}")
                print(f"  创建时间: {task_detail.get('created_at')}")
                print(f"  调度类型: {task_detail.get('schedule', {}).get('type', 'N/A')}")
                print(f"  执行时间: {task_detail.get('schedule', {}).get('time', 'N/A')}")
            else:
                print(f"  ❌ 任务详情获取失败: {detail_response.text}")
        
        # 步骤7: 检查Redis存储结构
        print(f"\n📋 步骤7: 检查Redis存储结构")
        if task_id:
            # 检查新的分层存储
            basic_key = f"monitoring_tasks:tasks:{task_id}:basic"
            schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
            config_key = f"monitoring_tasks:tasks:{task_id}:config"
            urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
            
            print(f"  分层存储:")
            print(f"    basic存在: {r.exists(basic_key)}")
            print(f"    schedule存在: {r.exists(schedule_key)}")
            print(f"    config存在: {r.exists(config_key)}")
            print(f"    urls存在: {r.exists(urls_key)}")
            
            if r.exists(urls_key):
                urls_count = r.scard(urls_key)
                print(f"    URLs数量: {urls_count}")
            
            # 检查索引
            all_ids_key = "monitoring_tasks:indexes:all_task_ids"
            active_ids_key = "monitoring_tasks:indexes:active_task_ids"
            
            print(f"  索引:")
            print(f"    all_task_ids包含任务: {r.sismember(all_ids_key, task_id)}")
            print(f"    active_task_ids包含任务: {r.sismember(active_ids_key, task_id)}")
        
        print(f"\n🎉 完整集成测试完成!")
        print(f"✅ 成功验证了从URL池创建任务到监控任务管理页面显示的完整流程")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        if task_id:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id}")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {delete_response.text}")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:integration_test", url_id)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_complete_integration()
