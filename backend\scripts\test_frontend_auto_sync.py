#!/usr/bin/env python3
"""
测试前端自动同步功能的后端API调用
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_frontend_auto_sync():
    """测试前端自动同步功能"""
    
    print("🧪 测试前端自动同步功能的后端API调用...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 1. 模拟前端获取后端配置列表
            print("\n1. 获取后端配置列表...")
            response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            
            if response.status_code == 200:
                configs = response.json()
                if configs:
                    config_summary = configs[0]
                    backend_id = config_summary['backend_id']
                    print(f"   ✅ 获取到配置摘要: {config_summary['backend_name']}")
                    print(f"   - 配置ID: {backend_id}")
                    
                    # 2. 模拟前端选择后端配置时的API调用
                    print(f"\n2. 模拟选择后端配置时的详情获取...")
                    detail_response = await client.get(f"http://localhost:8000/api/v1/backend-configs/{backend_id}")
                    
                    if detail_response.status_code == 200:
                        config_detail = detail_response.json()
                        print(f"   ✅ 获取到配置详情")
                        print(f"   - 配置名称: {config_detail['backend_name']}")
                        print(f"   - 性能级别: {config_detail.get('performance_level', 'N/A')}")
                        
                        # 检查性能配置
                        perf_config = config_detail.get('performance_config', {})
                        if perf_config:
                            print(f"   - 性能配置存在: ✅")
                            print(f"     * 最大并发任务数: {perf_config.get('max_concurrent_tasks', 'N/A')}")
                            print(f"     * 内存限制: {perf_config.get('memory_limit_mb', 'N/A')}MB")
                            print(f"     * API超时: {perf_config.get('api_timeout', 'N/A')}ms")
                        else:
                            print(f"   - 性能配置存在: ❌")
                        
                        # 3. 验证前端自动同步逻辑的数据完整性
                        print(f"\n3. 验证自动同步所需的数据...")
                        
                        # 检查必需字段
                        required_fields = {
                            'backend_name': config_detail.get('backend_name'),
                            'performance_level': config_detail.get('performance_level'),
                            'performance_config': config_detail.get('performance_config'),
                        }
                        
                        all_present = True
                        for field, value in required_fields.items():
                            if value is not None:
                                print(f"   ✅ {field}: {value if field != 'performance_config' else '存在'}")
                            else:
                                print(f"   ❌ {field}: 缺失")
                                all_present = False
                        
                        if all_present and perf_config:
                            # 模拟前端同步逻辑
                            backend_max_concurrent = perf_config.get('max_concurrent_tasks', 10)
                            performance_level = config_detail.get('performance_level', 'medium')
                            
                            # 计算分配比例
                            if performance_level == 'high':
                                allocation_ratio = 0.9
                            elif performance_level == 'medium':
                                allocation_ratio = 0.8
                            else:
                                allocation_ratio = 0.6
                            
                            max_concurrent = backend_max_concurrent
                            allocated_concurrent = max(1, int(max_concurrent * allocation_ratio))
                            
                            print(f"\n   🎯 模拟前端自动同步结果:")
                            print(f"      - 后端最大并发: {backend_max_concurrent}")
                            print(f"      - 性能级别: {performance_level}")
                            print(f"      - 分配比例: {allocation_ratio*100}%")
                            print(f"      - 计算结果: 最大并发 {max_concurrent}, 分配并发 {allocated_concurrent}")
                            
                            print(f"\n   📋 前端应该执行的操作:")
                            print(f"      form.setFieldsValue({{")
                            print(f"        max_concurrent_tasks: {max_concurrent},")
                            print(f"        allocated_concurrent: {allocated_concurrent},")
                            print(f"      }});")
                            
                        else:
                            print(f"\n   ❌ 数据不完整，自动同步可能失败")
                        
                    else:
                        print(f"   ❌ 获取配置详情失败: {detail_response.status_code}")
                        print(f"   错误: {detail_response.text}")
                        
                else:
                    print(f"   ⚠️  没有后端配置数据")
            else:
                print(f"   ❌ 获取配置列表失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_frontend_auto_sync()
    print(f"\n✅ 前端自动同步功能测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
