"""
URL池管理服务

提供URL池的存储、查询、更新等功能
"""

import json
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional, Set
import redis.asyncio as redis
from uuid import uuid4

from app.models.url_pool import UrlPoolItem, UrlPoolStats, UrlPoolQuery

logger = logging.getLogger(__name__)


class UrlPoolService:
    """URL池管理服务"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        
        # Redis键名模板
        self.keys = {
            'url_item': 'url_pool:items:{}',           # URL详细信息
            'all_ids': 'url_pool:all_ids',             # 所有URL ID集合
            'platform_ids': 'url_pool:platform:{}',   # 按平台分组的URL ID
            'status_ids': 'url_pool:status:{}',        # 按状态分组的URL ID
            'source_ids': 'url_pool:source:{}',        # 按来源文件分组的URL ID
            'url_hashes': 'url_pool:url_hashes',       # URL哈希集合（去重用）
            'stats_cache': 'url_pool:stats',           # 统计信息缓存
        }
    
    async def store_urls(self, urls_data: List[Dict], source_filename: str) -> List[UrlPoolItem]:
        """
        批量存储URL到池中
        
        Args:
            urls_data: URL数据列表
            source_filename: 来源文件名
            
        Returns:
            存储的URL项目列表
        """
        try:
            logger.info(f"开始存储{len(urls_data)}个URL到池中")
            
            stored_urls = []
            new_count = 0
            duplicate_count = 0
            
            # 获取现有的URL哈希集合
            existing_hashes = await self.redis.smembers(self.keys['url_hashes'])
            existing_hashes = {h.decode() for h in existing_hashes}
            
            for url_data in urls_data:
                url_hash = url_data['url_hash']
                
                # 检查是否重复
                if url_hash in existing_hashes:
                    duplicate_count += 1
                    continue
                
                # 创建URL项目
                url_item = await self._create_url_item(url_data, source_filename)
                
                # 存储到Redis
                await self._store_single_url(url_item)
                
                stored_urls.append(url_item)
                new_count += 1
            
            # 更新统计缓存
            await self._invalidate_stats_cache()
            
            logger.info(f"URL存储完成: 新增{new_count}个，重复{duplicate_count}个")
            return stored_urls
            
        except Exception as e:
            logger.error(f"存储URL失败: {e}")
            raise
    
    async def _create_url_item(self, url_data: Dict, source_filename: str) -> UrlPoolItem:
        """创建URL项目"""
        now = datetime.now(timezone.utc)
        
        return UrlPoolItem(
            id=str(uuid4()),
            url=url_data['url'],
            platform=url_data['platform'],
            source_file=source_filename,
            added_at=now,
            status='active',
            last_check=None,
            check_count=0,
            success_count=0,
            error_count=0,
            metadata={
                'original_row': url_data.get('original_row'),
                'original_column': url_data.get('original_column'),
                'detected_platform': url_data['platform'],
                'url_hash': url_data['url_hash']
            }
        )
    
    async def _store_single_url(self, url_item: UrlPoolItem):
        """存储单个URL项目到Redis"""
        try:
            # 使用管道提高性能
            pipe = self.redis.pipeline()
            
            # 存储URL详细信息
            url_key = self.keys['url_item'].format(url_item.id)
            url_data = {
                'id': url_item.id,
                'url': url_item.url,
                'platform': url_item.platform,
                'source_file': url_item.source_file,
                'added_at': url_item.added_at.isoformat(),
                'status': url_item.status.value if hasattr(url_item.status, 'value') else str(url_item.status),
                'last_check': url_item.last_check.isoformat() if url_item.last_check else '',
                'check_count': str(url_item.check_count),
                'success_count': str(url_item.success_count),
                'error_count': str(url_item.error_count),
                'metadata': json.dumps(url_item.metadata or {})
            }
            pipe.hset(url_key, mapping=url_data)
            
            # 添加到各种索引
            pipe.sadd(self.keys['all_ids'], url_item.id)
            pipe.sadd(self.keys['platform_ids'].format(url_item.platform), url_item.id)
            status_value = url_item.status.value if hasattr(url_item.status, 'value') else str(url_item.status)
            pipe.sadd(self.keys['status_ids'].format(status_value), url_item.id)
            pipe.sadd(self.keys['source_ids'].format(url_item.source_file), url_item.id)
            pipe.sadd(self.keys['url_hashes'], url_item.metadata['url_hash'])
            
            # 执行管道
            await pipe.execute()
            
        except Exception as e:
            logger.error(f"存储单个URL失败: {url_item.id}, 错误: {e}")
            raise
    
    async def get_urls(self, query: UrlPoolQuery) -> Dict:
        """
        查询URL池
        
        Args:
            query: 查询参数
            
        Returns:
            查询结果
        """
        try:
            logger.info(f"查询URL池: {query}")
            
            # 获取符合条件的URL ID集合
            url_ids = await self._filter_url_ids(query)
            
            # 计算分页
            total = len(url_ids)
            start_idx = (query.page - 1) * query.page_size
            end_idx = start_idx + query.page_size
            
            # 排序
            if query.sort_by:
                url_ids = await self._sort_url_ids(url_ids, query.sort_by, query.sort_order)
            
            # 分页
            page_url_ids = url_ids[start_idx:end_idx]
            
            # 获取URL详细信息
            urls = []
            for url_id in page_url_ids:
                url_item = await self.get_url_by_id(url_id)
                if url_item:
                    urls.append(url_item)
            
            return {
                'urls': urls,
                'total': total,
                'page': query.page,
                'page_size': query.page_size,
                'has_more': end_idx < total
            }
            
        except Exception as e:
            logger.error(f"查询URL池失败: {e}")
            raise

    async def get_filtered_url_ids(self, query: UrlPoolQuery) -> List[str]:
        """
        获取符合筛选条件的所有URL ID

        Args:
            query: 查询参数

        Returns:
            URL ID列表
        """
        try:
            logger.info(f"获取筛选URL ID: {query}")

            # 获取符合条件的URL ID集合
            url_ids = await self._filter_url_ids(query)

            # 排序
            if query.sort_by:
                url_ids = await self._sort_url_ids(url_ids, query.sort_by, query.sort_order)

            return url_ids

        except Exception as e:
            logger.error(f"获取筛选URL ID失败: {e}")
            raise
    
    async def _filter_url_ids(self, query: UrlPoolQuery) -> List[str]:
        """根据查询条件筛选URL ID"""
        # 从所有URL开始
        url_ids = await self.redis.smembers(self.keys['all_ids'])
        url_ids = {id.decode() for id in url_ids}

        # 排除指定的URL ID
        if query.exclude_urls:
            exclude_set = set(query.exclude_urls)
            url_ids = url_ids - exclude_set
            logger.info(f"排除了{len(exclude_set)}个URL，剩余{len(url_ids)}个")

        # 默认排除已删除的URL（除非明确查询deleted状态）
        if query.status != 'deleted':
            deleted_ids = await self.redis.smembers(self.keys['status_ids'].format('deleted'))
            deleted_ids = {id.decode() for id in deleted_ids}
            url_ids = url_ids - deleted_ids
        
        # 按平台筛选
        if query.platform:
            platform_ids = await self.redis.smembers(self.keys['platform_ids'].format(query.platform))
            platform_ids = {id.decode() for id in platform_ids}
            url_ids = url_ids.intersection(platform_ids)
        
        # 按状态筛选
        if query.status:
            status_ids = await self.redis.smembers(self.keys['status_ids'].format(query.status))
            status_ids = {id.decode() for id in status_ids}
            url_ids = url_ids.intersection(status_ids)
        
        # 按来源文件筛选
        if query.source_file:
            source_ids = await self.redis.smembers(self.keys['source_ids'].format(query.source_file))
            source_ids = {id.decode() for id in source_ids}
            url_ids = url_ids.intersection(source_ids)
        
        # 文本搜索
        if query.search:
            url_ids = await self._search_urls(list(url_ids), query.search)
        
        return list(url_ids)
    
    async def _search_urls(self, url_ids: List[str], search_term: str) -> List[str]:
        """在URL中搜索文本"""
        matching_ids = []
        search_term_lower = search_term.lower()
        
        for url_id in url_ids:
            url_item = await self.get_url_by_id(url_id)
            if url_item and search_term_lower in url_item.url.lower():
                matching_ids.append(url_id)
        
        return matching_ids
    
    async def _sort_url_ids(self, url_ids: List[str], sort_by: str, sort_order: str) -> List[str]:
        """排序URL ID列表"""
        # 获取排序字段的值
        sort_data = []
        for url_id in url_ids:
            url_item = await self.get_url_by_id(url_id)
            if url_item:
                sort_value = getattr(url_item, sort_by, None)
                if isinstance(sort_value, datetime):
                    sort_value = sort_value.timestamp()
                sort_data.append((url_id, sort_value))
        
        # 排序
        reverse = sort_order == 'desc'
        sort_data.sort(key=lambda x: x[1] or 0, reverse=reverse)
        
        return [item[0] for item in sort_data]
    
    async def get_url_by_id(self, url_id: str) -> Optional[UrlPoolItem]:
        """根据ID获取URL项目"""
        try:
            url_key = self.keys['url_item'].format(url_id)
            url_data = await self.redis.hgetall(url_key)
            
            if not url_data:
                return None
            
            # 解码数据
            url_data = {k.decode(): v.decode() for k, v in url_data.items()}
            
            # 转换数据类型
            return UrlPoolItem(
                id=url_data['id'],
                url=url_data['url'],
                platform=url_data['platform'],
                source_file=url_data['source_file'],
                added_at=datetime.fromisoformat(url_data['added_at']),
                status=url_data['status'],
                last_check=datetime.fromisoformat(url_data['last_check']) if url_data['last_check'] and url_data['last_check'] != '' else None,
                check_count=int(url_data['check_count']),
                success_count=int(url_data['success_count']),
                error_count=int(url_data['error_count']),
                metadata=json.loads(url_data['metadata']) if url_data['metadata'] else {}
            )
            
        except Exception as e:
            logger.error(f"获取URL失败: {url_id}, 错误: {e}")
            return None
    
    async def batch_update_urls(self, url_ids: List[str], updates: Dict) -> int:
        """批量更新URL"""
        try:
            updated_count = 0
            
            for url_id in url_ids:
                success = await self._update_single_url(url_id, updates)
                if success:
                    updated_count += 1
            
            # 更新统计缓存
            await self._invalidate_stats_cache()
            
            logger.info(f"批量更新完成: {updated_count}/{len(url_ids)}")
            return updated_count

        except Exception as e:
            logger.error(f"批量更新URL失败: {e}")
            raise

    async def update_url_status(self, url_id: str, new_status: str) -> bool:
        """更新单个URL的状态"""
        try:
            # 获取当前URL信息
            url_item = await self.get_url_by_id(url_id)
            if not url_item:
                return False

            old_status = url_item.status

            # 更新URL状态
            url_key = self.keys['url_item'].format(url_id)
            await self.redis.hset(url_key, 'status', new_status)
            await self.redis.hset(url_key, 'updated_at', datetime.now(timezone.utc).isoformat())

            # 更新状态索引
            if old_status != new_status:
                # 从旧状态索引中移除
                old_status_value = old_status.value if hasattr(old_status, 'value') else str(old_status)
                await self.redis.srem(self.keys['status_ids'].format(old_status_value), url_id)

                # 添加到新状态索引
                await self.redis.sadd(self.keys['status_ids'].format(new_status), url_id)

            logger.info(f"URL状态已更新: {url_id}, {old_status} -> {new_status}")
            return True

        except Exception as e:
            logger.error(f"更新URL状态失败: {url_id}, 错误: {e}")
            return False
    
    async def _update_single_url(self, url_id: str, updates: Dict) -> bool:
        """更新单个URL"""
        try:
            url_key = self.keys['url_item'].format(url_id)
            
            # 获取当前数据
            current_data = await self.redis.hgetall(url_key)
            if not current_data:
                return False
            
            # 准备更新数据
            update_data = {}
            old_status = current_data.get(b'status', b'').decode()
            
            for field, value in updates.items():
                if field in ['status', 'check_count', 'success_count', 'error_count']:
                    update_data[field] = str(value)
                elif field == 'last_check' and value:
                    update_data[field] = value.isoformat()
            
            if not update_data:
                return False
            
            # 更新Redis
            await self.redis.hset(url_key, mapping=update_data)
            
            # 更新索引（如果状态改变）
            new_status = updates.get('status')
            if new_status and new_status != old_status:
                # 从旧状态索引中移除
                await self.redis.srem(self.keys['status_ids'].format(old_status), url_id)
                # 添加到新状态索引
                await self.redis.sadd(self.keys['status_ids'].format(new_status), url_id)
            
            return True
            
        except Exception as e:
            logger.error(f"更新URL失败: {url_id}, 错误: {e}")
            return False
    
    async def delete_urls(self, url_ids: List[str], permanent: bool = False) -> int:
        """
        批量删除URL

        Args:
            url_ids: URL ID列表
            permanent: 是否永久删除，False表示移到回收站
        """
        try:
            deleted_count = 0

            for url_id in url_ids:
                if permanent:
                    success = await self._permanent_delete_url(url_id)
                else:
                    success = await self._soft_delete_url(url_id)

                if success:
                    deleted_count += 1

            # 更新统计缓存
            await self._invalidate_stats_cache()

            action = "永久删除" if permanent else "移到回收站"
            logger.info(f"批量{action}完成: {deleted_count}/{len(url_ids)}")
            return deleted_count

        except Exception as e:
            logger.error(f"批量删除URL失败: {e}")
            raise
    
    async def _soft_delete_url(self, url_id: str) -> bool:
        """软删除URL（移到回收站）"""
        try:
            # 获取URL信息
            url_item = await self.get_url_by_id(url_id)
            if not url_item:
                return False

            # 更新状态为deleted
            success = await self.update_url_status(url_id, 'deleted')

            if success:
                logger.info(f"URL已移到回收站: {url_id}")

            return success

        except Exception as e:
            logger.error(f"软删除URL失败: {url_id}, 错误: {e}")
            return False

    async def _permanent_delete_url(self, url_id: str) -> bool:
        """永久删除单个URL"""
        try:
            # 获取URL信息
            url_item = await self.get_url_by_id(url_id)
            if not url_item:
                return False
            
            # 使用管道删除
            pipe = self.redis.pipeline()
            
            # 删除URL详细信息
            url_key = self.keys['url_item'].format(url_id)
            pipe.delete(url_key)
            
            # 从索引中移除
            pipe.srem(self.keys['all_ids'], url_id)
            pipe.srem(self.keys['platform_ids'].format(url_item.platform), url_id)
            status_value = url_item.status.value if hasattr(url_item.status, 'value') else str(url_item.status)
            pipe.srem(self.keys['status_ids'].format(status_value), url_id)
            pipe.srem(self.keys['source_ids'].format(url_item.source_file), url_id)
            
            # 从URL哈希集合中移除
            if url_item.metadata and 'url_hash' in url_item.metadata:
                pipe.srem(self.keys['url_hashes'], url_item.metadata['url_hash'])
            
            await pipe.execute()
            return True

        except Exception as e:
            logger.error(f"永久删除URL失败: {url_id}, 错误: {e}")
            return False

    async def restore_urls(self, url_ids: List[str]) -> int:
        """
        从回收站恢复URL

        Args:
            url_ids: URL ID列表

        Returns:
            恢复成功的数量
        """
        try:
            restored_count = 0

            for url_id in url_ids:
                # 检查URL是否在回收站
                url_item = await self.get_url_by_id(url_id)
                if url_item and url_item.status == 'deleted':
                    success = await self.update_url_status(url_id, 'active')
                    if success:
                        restored_count += 1
                        logger.info(f"URL已从回收站恢复: {url_id}")

            # 更新统计缓存
            await self._invalidate_stats_cache()

            logger.info(f"批量恢复完成: {restored_count}/{len(url_ids)}")
            return restored_count

        except Exception as e:
            logger.error(f"批量恢复URL失败: {e}")
            raise
    
    async def get_stats(self) -> UrlPoolStats:
        """获取URL池统计信息"""
        try:
            # 尝试从缓存获取
            cached_stats = await self.redis.get(self.keys['stats_cache'])
            if cached_stats:
                stats_data = json.loads(cached_stats)
                return UrlPoolStats(**stats_data)
            
            # 计算统计信息
            stats = await self._calculate_stats()
            
            # 缓存统计信息（5分钟）
            await self.redis.setex(
                self.keys['stats_cache'],
                300,
                json.dumps(stats.model_dump(), default=str)
            )
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            raise
    
    async def _calculate_stats(self) -> UrlPoolStats:
        """计算统计信息"""
        # 总URL数
        total_urls = await self.redis.scard(self.keys['all_ids'])
        
        # 按状态统计
        active_urls = await self.redis.scard(self.keys['status_ids'].format('active'))
        disabled_urls = await self.redis.scard(self.keys['status_ids'].format('disabled'))
        
        # 按平台统计
        platforms = {}
        platform_keys = await self.redis.keys(self.keys['platform_ids'].format('*'))
        for key in platform_keys:
            platform = key.decode().split(':')[-1]
            count = await self.redis.scard(key)
            platforms[platform] = count
        
        return UrlPoolStats(
            total_urls=total_urls,
            active_urls=active_urls,
            disabled_urls=disabled_urls,
            platforms=platforms,
            last_updated=datetime.now(timezone.utc)
        )
    
    async def _invalidate_stats_cache(self):
        """使统计缓存失效"""
        await self.redis.delete(self.keys['stats_cache'])
