input {
  beats {
    port => 5044
  }
  
  tcp {
    port => 5000
    codec => json_lines
  }
  
  udp {
    port => 5000
    codec => json_lines
  }
}

filter {
  # 解析MonIt应用日志
  if [fields][service] == "monit-backend" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service_type" => "backend" }
    }
  }
  
  # 解析Celery任务日志
  if [fields][service] == "monit-celery" {
    grok {
      match => { "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service_type" => "celery" }
    }
  }
  
  # 解析爬虫日志
  if [fields][service] == "monit-crawler" {
    json {
      source => "message"
    }
    
    mutate {
      add_field => { "service_type" => "crawler" }
    }
  }
  
  # 添加通用字段
  mutate {
    add_field => { "environment" => "development" }
    add_field => { "project" => "monit" }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "logstash-monit-%{+YYYY.MM.dd}"
  }
  
  stdout {
    codec => rubydebug
  }
}
