"""
URL池管理API路由

提供Excel上传、URL池查询、批量操作等API接口
"""

import time
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Query
import redis.asyncio as redis

from app.models.url_pool import (
    UrlPoolQuery, UrlPoolBatchUpdate, UrlPoolStats,
    ExcelUploadResponse, UrlPoolListResponse, UrlPoolIdsResponse, UrlPoolBatchResponse, UrlPoolStatsResponse,
    UrlStatus, SUPPORTED_PLATFORMS
)
from pydantic import BaseModel, Field
from typing import List

class UrlPoolBatchRequest(BaseModel):
    """简单的批量操作请求"""
    url_ids: List[str] = Field(..., description="URL ID列表")
from app.services.excel_parser import excel_parser, save_uploaded_file, cleanup_temp_file
from app.utils.excel_processor import ExcelProcessor
from app.services.url_pool_service import UrlPoolService

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1", tags=["url-pool"])

# Redis连接
_redis_client: Optional[redis.Redis] = None


async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global _redis_client
    if _redis_client is None:
        _redis_client = redis.from_url("redis://redis:6379/0")
    return _redis_client


async def get_url_pool_service() -> UrlPoolService:
    """获取URL池服务"""
    redis_client = await get_redis_client()
    return UrlPoolService(redis_client)


@router.post("/excel/upload-and-parse", response_model=ExcelUploadResponse)
async def upload_and_parse_excel(
    file: UploadFile = File(...),
    platform_filter: Optional[str] = Query(None, description="平台筛选"),
    auto_detect: bool = Query(True, description="自动检测平台"),
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    上传Excel文件并解析URL

    支持的文件格式：.xlsx, .xls
    无文件大小限制
    """
    start_time = time.time()
    temp_file_path = None
    
    try:
        logger.info(f"开始处理Excel文件上传: {file.filename}")
        
        # 验证文件格式
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400,
                detail="不支持的文件格式，请上传.xlsx或.xls文件"
            )
        
        # 读取文件内容（已移除文件大小限制）
        file_content = await file.read()
        
        # 保存临时文件
        temp_file_path = await save_uploaded_file(file_content, file.filename)
        
        # 使用ExcelProcessor解析Excel文件（与任务管理页面保持一致）
        excel_processor = ExcelProcessor()
        products, stats = excel_processor.read_product_links(temp_file_path)

        # 转换为URL池格式
        urls_data = []
        for product in products:
            # 生成URL哈希
            import hashlib
            url_hash = hashlib.md5(product['product_url'].encode()).hexdigest()

            url_data = {
                'url': product['product_url'],
                'platform': product.get('platform', 'unknown'),
                'source_file': file.filename,
                'original_row': product.get('row_index', 0),
                'original_column': 'product_url',
                'url_hash': url_hash,
                'chinese_name': product.get('chinese_name', ''),
                'spanish_name': product.get('spanish_name', '')
            }
            urls_data.append(url_data)
        
        # 平台筛选
        if platform_filter and platform_filter != 'all':
            urls_data = [url for url in urls_data if url['platform'] == platform_filter]
        
        # 存储到URL池
        stored_urls = await url_pool_service.store_urls(urls_data, file.filename)
        
        # 计算统计信息
        total_urls = len(urls_data)
        new_urls = len(stored_urls)
        duplicate_urls = total_urls - new_urls
        invalid_urls = 0  # 在解析阶段已经过滤了无效URL
        
        processing_time = time.time() - start_time
        
        logger.info(f"Excel处理完成: 总计{total_urls}个URL，新增{new_urls}个，重复{duplicate_urls}个")
        
        return ExcelUploadResponse(
            success=True,
            message=f"成功处理{total_urls}个URL，新增{new_urls}个到URL池",
            total_urls=total_urls,
            new_urls=new_urls,
            duplicate_urls=duplicate_urls,
            invalid_urls=invalid_urls,
            urls=stored_urls,
            processing_time=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理Excel文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理文件失败: {str(e)}")
    
    finally:
        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)


@router.get("/urls/pool")
async def get_url_pool(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    status: Optional[UrlStatus] = Query(None, description="状态筛选"),
    source_file: Optional[str] = Query(None, description="来源文件筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("added_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    ids_only: bool = Query(False, description="只返回ID列表"),
    exclude_urls: Optional[str] = Query(None, description="排除的URL ID列表，逗号分隔"),
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    获取URL池列表
    
    支持分页、筛选、搜索、排序
    """
    try:
        # 解析排除的URL列表
        exclude_url_list = []
        if exclude_urls:
            exclude_url_list = [url.strip() for url in exclude_urls.split(',') if url.strip()]

        logger.info(f"查询URL池: page={page}, platform={platform}, status={status}, ids_only={ids_only}, exclude_count={len(exclude_url_list)}")

        # 构建查询参数
        query = UrlPoolQuery(
            page=page,
            page_size=page_size,
            platform=platform,
            status=status,
            source_file=source_file,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
            exclude_urls=exclude_url_list
        )

        if ids_only:
            # 只返回ID列表
            url_ids = await url_pool_service.get_filtered_url_ids(query)
            return UrlPoolIdsResponse(
                success=True,
                data=url_ids,
                total=len(url_ids)
            )
        else:
            # 返回完整的URL数据
            result = await url_pool_service.get_urls(query)

            return UrlPoolListResponse(
                success=True,
                data=result['urls'],
                total=result['total'],
                page=result['page'],
                page_size=result['page_size'],
                has_more=result['has_more']
            )
        
    except Exception as e:
        logger.error(f"查询URL池失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.post("/urls/pool/batch-update", response_model=UrlPoolBatchResponse)
async def batch_update_urls(
    request: UrlPoolBatchUpdate,
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    批量更新URL状态
    
    支持的操作：enable（启用）、disable（禁用）、delete（删除）
    """
    try:
        logger.info(f"批量操作URL: action={request.action}, count={len(request.url_ids)}")
        
        affected_count = 0
        
        if request.action == "enable":
            affected_count = await url_pool_service.batch_update_urls(
                request.url_ids,
                {"status": UrlStatus.ACTIVE.value}
            )
            message = f"成功启用{affected_count}个URL"

        elif request.action == "disable":
            affected_count = await url_pool_service.batch_update_urls(
                request.url_ids,
                {"status": UrlStatus.DISABLED.value}
            )
            message = f"成功禁用{affected_count}个URL"
            
        elif request.action == "delete":
            affected_count = await url_pool_service.delete_urls(request.url_ids)
            message = f"成功删除{affected_count}个URL"
            
        else:
            raise HTTPException(status_code=400, detail="不支持的操作类型")
        
        return UrlPoolBatchResponse(
            success=True,
            message=message,
            affected_count=affected_count,
            total_count=len(request.url_ids)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量操作URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")


@router.get("/urls/pool/stats", response_model=UrlPoolStatsResponse)
async def get_url_pool_stats(
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    获取URL池统计信息
    
    包括总数、各状态数量、各平台分布等
    """
    try:
        logger.info("获取URL池统计信息")
        
        stats = await url_pool_service.get_stats()
        
        return UrlPoolStatsResponse(
            success=True,
            data=stats
        )
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/urls/platforms")
async def get_supported_platforms():
    """
    获取支持的平台列表
    """
    try:
        platforms = excel_parser.get_supported_platforms()
        
        return {
            "success": True,
            "platforms": platforms,
            "total": len(platforms)
        }
        
    except Exception as e:
        logger.error(f"获取平台列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取平台列表失败: {str(e)}")


@router.get("/urls/pool/source-files")
async def get_source_files(
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    获取所有来源文件列表
    """
    try:
        logger.info("获取来源文件列表")
        
        redis_client = await get_redis_client()
        
        # 获取所有来源文件的键
        source_keys = await redis_client.keys("url_pool:source:*")
        
        source_files = []
        for key in source_keys:
            source_file = key.decode().split(":")[-1]
            count = await redis_client.scard(key)
            source_files.append({
                "name": source_file,
                "url_count": count
            })
        
        # 按URL数量排序
        source_files.sort(key=lambda x: x['url_count'], reverse=True)
        
        return {
            "success": True,
            "source_files": source_files,
            "total": len(source_files)
        }

    except Exception as e:
        logger.error(f"获取来源文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")


@router.get("/urls/pool/{url_id}")
async def get_url_by_id(
    url_id: str,
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    根据ID获取URL详情
    """
    try:
        logger.info(f"获取URL详情: {url_id}")

        url_item = await url_pool_service.get_url_by_id(url_id)

        if not url_item:
            raise HTTPException(status_code=404, detail="URL不存在")

        return {
            "success": True,
            "data": url_item
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取URL详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取详情失败: {str(e)}")


@router.delete("/urls/pool/{url_id}")
async def delete_url_by_id(
    url_id: str,
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    根据ID删除URL
    """
    try:
        logger.info(f"删除URL: {url_id}")

        deleted_count = await url_pool_service.delete_urls([url_id])

        if deleted_count == 0:
            raise HTTPException(status_code=404, detail="URL不存在或删除失败")

        return {
            "success": True,
            "message": "URL删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.delete("/urls/pool/batch-delete")
async def batch_delete_urls(
    request: UrlPoolBatchUpdate,
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    批量删除URL（软删除到回收站）
    """
    try:
        logger.info(f"批量删除URL: {len(request.url_ids)}个")

        deleted_count = await url_pool_service.delete_urls(request.url_ids, permanent=False)

        return UrlPoolBatchResponse(
            success=True,
            message=f"成功删除 {deleted_count} 个URL到回收站",
            affected_count=deleted_count,
            total_count=len(request.url_ids)
        )

    except Exception as e:
        logger.error(f"批量删除URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")


@router.get("/urls/recycle-bin")
async def get_recycle_bin(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    获取回收站中的URL列表
    """
    try:
        logger.info(f"获取回收站URL列表: page={page}, page_size={page_size}")

        # 构建查询参数，只查询已删除的URL
        query = UrlPoolQuery(
            page=page,
            page_size=page_size,
            status='deleted',
            sort_by='updated_at',
            sort_order='desc'
        )

        result = await url_pool_service.get_urls(query)

        return UrlPoolListResponse(
            success=True,
            data=result['urls'],
            total=result['total'],
            page=result['page'],
            page_size=result['page_size'],
            has_more=result['has_more']
        )

    except Exception as e:
        logger.error(f"获取回收站失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取回收站失败: {str(e)}")


@router.post("/urls/recycle-bin/restore")
async def restore_from_recycle_bin(
    request: UrlPoolBatchRequest,
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    从回收站恢复URL
    """
    try:
        logger.info(f"从回收站恢复URL: {len(request.url_ids)}个")

        restored_count = await url_pool_service.restore_urls(request.url_ids)

        return UrlPoolBatchResponse(
            success=True,
            message=f"成功恢复 {restored_count} 个URL",
            affected_count=restored_count,
            total_count=len(request.url_ids)
        )

    except Exception as e:
        logger.error(f"恢复URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"恢复失败: {str(e)}")


@router.delete("/urls/recycle-bin/permanent")
async def permanent_delete_from_recycle_bin(
    request: UrlPoolBatchRequest,
    url_pool_service: UrlPoolService = Depends(get_url_pool_service)
):
    """
    从回收站永久删除URL
    """
    try:
        logger.info(f"永久删除URL: {len(request.url_ids)}个")

        deleted_count = await url_pool_service.delete_urls(request.url_ids, permanent=True)

        return UrlPoolBatchResponse(
            success=True,
            message=f"成功永久删除 {deleted_count} 个URL",
            affected_count=deleted_count,
            total_count=len(request.url_ids)
        )

    except Exception as e:
        logger.error(f"永久删除URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"永久删除失败: {str(e)}")
