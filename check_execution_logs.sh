#!/bin/bash

echo "🚀 检查立即执行任务的日志流程"
echo "=================================="

# 获取当前时间戳
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "检查时间: $TIMESTAMP"
echo ""

echo "1. 📋 检查主后端日志 (monit-backend)"
echo "-----------------------------------"
echo "查找Worker分配和任务提交信息..."
docker logs monit-backend --since 2m | grep -E "(Using assigned worker|Task submitted|Worker backend|split into)" || echo "❌ 没有找到相关日志"
echo ""

echo "2. 🔄 检查Celery Worker日志 (monit-celery-worker)"
echo "-----------------------------------------------"
echo "查找任务接收和处理信息..."
docker logs monit-celery-worker --since 2m | grep -E "(Task.*received|crawl_batch|Using crawler backend|Starting batch)" || echo "❌ 没有找到相关日志"
echo ""

echo "3. 📊 检查Redis队列状态"
echo "----------------------"
echo "Celery默认队列长度:"
docker exec monit-redis redis-cli LLEN default

echo "Celery任务结果数量:"
docker exec monit-redis redis-cli KEYS "celery-task-meta-*" | wc -l

echo "任务管理器状态:"
docker exec monit-redis redis-cli HGET monitoring_tasks:manager:stats running_batches
echo ""

echo "4. 🌐 检查Worker后端连接"
echo "----------------------"
echo "Worker后端健康检查:"
curl -s -w "HTTP状态: %{http_code}\n" "http://localhost:11235/health" -o /dev/null

echo "Worker后端最近访问日志 (如果有Docker容器):"
# 尝试常见的Worker容器名
for container in crawler-worker crawl4ai-worker worker-backend; do
    if docker ps --format "table {{.Names}}" | grep -q "$container"; then
        echo "找到Worker容器: $container"
        docker logs "$container" --since 2m | tail -10
        break
    fi
done
echo ""

echo "5. 📈 检查任务管理器API状态"
echo "-------------------------"
echo "任务管理器状态:"
curl -s "http://localhost:8000/api/v1/tasks/status" | python -m json.tool 2>/dev/null || echo "❌ 无法获取任务管理器状态"
echo ""

echo "6. 🔍 详细错误检查"
echo "-----------------"
echo "检查最近的错误日志:"

echo "主后端错误:"
docker logs monit-backend --since 2m | grep -i "error\|exception\|failed" | tail -5

echo "Celery Worker错误:"
docker logs monit-celery-worker --since 2m | grep -i "error\|exception\|failed" | tail -5
echo ""

echo "7. 📋 总结检查结果"
echo "-----------------"

# 检查各个组件状态
BACKEND_RUNNING=$(docker ps --filter "name=monit-backend" --format "{{.Names}}" | wc -l)
CELERY_RUNNING=$(docker ps --filter "name=monit-celery-worker" --format "{{.Names}}" | wc -l)
REDIS_RUNNING=$(docker ps --filter "name=monit-redis" --format "{{.Names}}" | wc -l)

echo "Docker容器状态:"
echo "  主后端 (monit-backend): $([ $BACKEND_RUNNING -eq 1 ] && echo '✅ 运行中' || echo '❌ 未运行')"
echo "  Celery Worker (monit-celery-worker): $([ $CELERY_RUNNING -eq 1 ] && echo '✅ 运行中' || echo '❌ 未运行')"
echo "  Redis (monit-redis): $([ $REDIS_RUNNING -eq 1 ] && echo '✅ 运行中' || echo '❌ 未运行')"

# 检查Worker后端
WORKER_STATUS=$(curl -s -w "%{http_code}" "http://localhost:11235/health" -o /dev/null)
echo "  Worker后端 (localhost:11235): $([ "$WORKER_STATUS" = "200" ] && echo '✅ 运行中' || echo '❌ 不可访问')"

echo ""
echo "🎯 如果没有看到预期的日志，可能的原因:"
echo "1. 任务没有成功提交到Celery队列"
echo "2. Celery Worker没有接收到任务"
echo "3. Worker后端配置不正确"
echo "4. 网络连接问题"
echo "5. API格式仍然不匹配"

echo ""
echo "💡 下一步建议:"
echo "1. 如果主后端有日志但Celery没有 → 检查Celery连接"
echo "2. 如果Celery有日志但Worker后端没有 → 检查API格式和网络"
echo "3. 如果都没有日志 → 检查任务是否真的被触发"
