import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { message } from 'antd';

import TaskEditWizard from '../../components/TaskEditWizard';

const TaskEditPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();

  // 处理编辑成功
  const handleSuccess = () => {
    message.success('任务修改成功！');
    
    // 返回到监控任务管理页面
    setTimeout(() => {
      navigate('/monitoring', { 
        state: { 
          editedTaskId: taskId,
          message: '任务修改成功'
        }
      });
    }, 1000);
  };

  // 处理取消
  const handleCancel = () => {
    navigate('/monitoring');
  };

  if (!taskId) {
    message.error('任务ID不能为空');
    navigate('/monitoring');
    return null;
  }

  return (
    <TaskEditWizard
      taskId={taskId}
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  );
};

export default TaskEditPage;
