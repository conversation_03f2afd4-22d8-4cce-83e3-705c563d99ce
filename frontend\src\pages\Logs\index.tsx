import React, { useState, useEffect, useRef } from 'react';
import {
  Typo<PERSON>,
  Card,
  Select,
  Input,
  Button,
  Space,
  Statistic,
  DatePicker,
  Switch,
  Tag,
  Row,
  Col,
  Tabs,
  message,
  Tooltip,
  Badge,
  Progress
} from 'antd';
import {
  MonitorOutlined,
  SearchOutlined,
  DownloadOutlined,
  ClearOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  FilterOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  BugOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import type { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import './index.css';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 类型定义
interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success' | 'debug';
  message: string;
  source: string;
  details?: any;
}

interface ErrorSummary {
  id: string;
  message: string;
  count: number;
  lastOccurred: string;
  source: string;
  stackTrace?: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
}

const Logs: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('realtime');
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [logLevel, setLogLevel] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isLiveMode, setIsLiveMode] = useState(true);
  const [autoScroll, setAutoScroll] = useState(true);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [errorSummaries, setErrorSummaries] = useState<ErrorSummary[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  const logsContainerRef = useRef<HTMLDivElement>(null);

  // 日志数据将从API获取，不再使用mock数据

  // 错误摘要和性能指标数据将从API获取

  // 初始化数据 - 从API获取
  useEffect(() => {
    const fetchData = async () => {
      try {
        // TODO: 调用真实API获取日志数据
        // const logsResponse = await systemApi.getLogs();
        // const metricsResponse = await systemApi.getSystemMetrics();
        // setLogs(logsResponse.data);
        // setFilteredLogs(logsResponse.data);
        // setPerformanceMetrics(metricsResponse.data.performance);

        // 暂时设置为空数组，等待API实现
        setLogs([]);
        setFilteredLogs([]);
        setErrorSummaries([]);
        setPerformanceMetrics([]);
      } catch (error) {
        console.error('获取日志数据失败:', error);
        setLogs([]);
        setFilteredLogs([]);
        setErrorSummaries([]);
        setPerformanceMetrics([]);
      }
    };

    fetchData();
  }, []);

  // 实时日志更新将通过WebSocket实现，不再使用模拟数据

  // 日志筛选
  useEffect(() => {
    let filtered = logs.filter(log => {
      const matchesLevel = logLevel === 'all' || log.level === logLevel;
      const matchesSearch = log.message.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                           log.source.toLowerCase().includes(searchKeyword.toLowerCase());

      return matchesLevel && matchesSearch;
    });

    setFilteredLogs(filtered);
  }, [logs, logLevel, searchKeyword]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && logsContainerRef.current) {
      logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
    }
  }, [filteredLogs, autoScroll]);

  // 统计数据计算
  const logStats = {
    total: logs.length,
    info: logs.filter(log => log.level === 'info').length,
    warning: logs.filter(log => log.level === 'warning').length,
    error: logs.filter(log => log.level === 'error').length,
    success: logs.filter(log => log.level === 'success').length
  };

  return (
    <div className="logs">
      {/* 页面头部 */}
      <div className="logs-header">
        <Title level={2}>日志监控</Title>
        <Paragraph>
          实时监控系统日志，分析错误趋势，查看性能指标和系统运行状态
        </Paragraph>
      </div>

      {/* 日志统计 */}
      <div className="logs-stats">
        <div className="stats-cards">
          <Card className="stats-card info">
            <Statistic
              title="总日志数"
              value={logStats.total}
              prefix={<InfoCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>

          <Card className="stats-card success">
            <Statistic
              title="成功日志"
              value={logStats.success}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>

          <Card className="stats-card warning">
            <Statistic
              title="警告日志"
              value={logStats.warning}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>

          <Card className="stats-card error">
            <Statistic
              title="错误日志"
              value={logStats.error}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </div>
      </div>

      {/* 日志控制 */}
      <div className="logs-controls">
        <div className="controls-row">
          <div className="control-item">
            <div className="control-label">日志级别</div>
            <Select
              value={logLevel}
              onChange={setLogLevel}
              style={{ width: 120 }}
            >
              <Option value="all">全部级别</Option>
              <Option value="info">信息</Option>
              <Option value="success">成功</Option>
              <Option value="warning">警告</Option>
              <Option value="error">错误</Option>
              <Option value="debug">调试</Option>
            </Select>
          </div>

          <div className="control-item">
            <div className="control-label">搜索关键词</div>
            <Search
              placeholder="搜索日志内容或来源"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              style={{ width: 250 }}
              allowClear
            />
          </div>

          <div className="control-item">
            <div className="control-label">时间范围</div>
            <RangePicker
              value={dateRange}
              onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
              showTime
              style={{ width: 300 }}
            />
          </div>

          <div className="control-item">
            <div className="control-label">实时模式</div>
            <Space>
              <Switch
                checked={isLiveMode}
                onChange={setIsLiveMode}
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
              {isLiveMode && (
                <div className="live-indicator">
                  <div className="live-dot"></div>
                  实时更新
                </div>
              )}
            </Space>
          </div>

          <div className="control-item">
            <div className="control-label">自动滚动</div>
            <Switch
              checked={autoScroll}
              onChange={setAutoScroll}
              checkedChildren="开启"
              unCheckedChildren="关闭"
            />
          </div>

          <div className="control-item">
            <div className="control-label">操作</div>
            <Space>
              <Button
                icon={<ClearOutlined />}
                onClick={() => {
                  setLogs([]);
                  setFilteredLogs([]);
                  message.success('日志已清空');
                }}
              >
                清空日志
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => message.info('导出功能开发中')}
              >
                导出日志
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        items={[
          {
            key: 'realtime',
            label: (
              <span>
                <MonitorOutlined />
                实时日志
                <Badge count={filteredLogs.length} offset={[10, 0]} />
              </span>
            ),
            children: (
          <div className="logs-viewer">
            <Card
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>实时日志流</span>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    显示 {filteredLogs.length} / {logs.length} 条日志
                  </div>
                </div>
              }
              className="logs-card"
            >
              <div className="logs-container">
                <div className="logs-toolbar">
                  <button
                    className={`toolbar-button ${autoScroll ? 'active' : ''}`}
                    onClick={() => setAutoScroll(!autoScroll)}
                  >
                    自动滚动
                  </button>
                  <button
                    className="toolbar-button"
                    onClick={() => {
                      if (logsContainerRef.current) {
                        logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
                      }
                    }}
                  >
                    滚动到底部
                  </button>
                  <button
                    className="toolbar-button"
                    onClick={() => {
                      if (logsContainerRef.current) {
                        logsContainerRef.current.scrollTop = 0;
                      }
                    }}
                  >
                    滚动到顶部
                  </button>
                </div>

                <div className="logs-content" ref={logsContainerRef}>
                  {filteredLogs.map(log => (
                    <div key={log.id} className={`log-entry ${log.level}`}>
                      <span className="log-timestamp">{log.timestamp}</span>
                      <span className={`log-level ${log.level}`}>
                        {log.level.toUpperCase()}
                      </span>
                      <span className="log-message">
                        {searchKeyword && log.message.toLowerCase().includes(searchKeyword.toLowerCase()) ? (
                          log.message.split(new RegExp(`(${searchKeyword})`, 'gi')).map((part, index) =>
                            part.toLowerCase() === searchKeyword.toLowerCase() ? (
                              <span key={index} className="search-highlight">{part}</span>
                            ) : (
                              part
                            )
                          )
                        ) : (
                          log.message
                        )}
                      </span>
                      <span className="log-source">[{log.source}]</span>
                    </div>
                  ))}

                  {filteredLogs.length === 0 && (
                    <div style={{ textAlign: 'center', color: '#666', marginTop: 50 }}>
                      <MonitorOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                      <div>暂无符合条件的日志</div>
                      <div style={{ fontSize: 12, marginTop: 8 }}>
                        请调整筛选条件或等待新的日志产生
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
            )
          },
          {
            key: 'errors',
            label: (
              <span>
                <BugOutlined />
                错误分析
                <Badge count={errorSummaries.length} offset={[10, 0]} />
              </span>
            ),
            children: (
          <div className="error-analysis">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card title="错误趋势图表" className="logs-card">
                  <ReactECharts
                    option={{
                      title: {
                        text: '24小时错误统计',
                        left: 'center',
                        textStyle: { fontSize: 16, fontWeight: 600 }
                      },
                      tooltip: {
                        trigger: 'axis',
                        formatter: (params: any) => {
                          const data = params[0];
                          return `${data.name}<br/>错误数量: ${data.value}`;
                        }
                      },
                      xAxis: {
                        type: 'category',
                        data: Array.from({ length: 24 }, (_, i) => `${i}:00`),
                      },
                      yAxis: {
                        type: 'value',
                        name: '错误数量'
                      },
                      series: [{
                        name: '错误数量',
                        type: 'line',
                        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 20)),
                        smooth: true,
                        lineStyle: {
                          color: '#ff4d4f',
                          width: 3
                        },
                        areaStyle: {
                          color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                              { offset: 0, color: 'rgba(255, 77, 79, 0.3)' },
                              { offset: 1, color: 'rgba(255, 77, 79, 0.1)' }
                            ]
                          }
                        }
                      }],
                      grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                      }
                    }}
                    style={{ height: '300px', width: '100%' }}
                  />
                </Card>
              </Col>

              <Col span={24}>
                <Card title="错误汇总列表" className="logs-card">
                  <div className="error-list">
                    {errorSummaries.map(error => (
                      <div key={error.id} className="error-item">
                        <div className="error-header">
                          <div>
                            <div className="error-message">{error.message}</div>
                            <div style={{ fontSize: 12, color: '#666' }}>
                              来源: {error.source} | 最后发生: {error.lastOccurred}
                            </div>
                          </div>
                          <div className="error-count">{error.count}</div>
                        </div>
                        {error.stackTrace && (
                          <div className="error-details">
                            {error.stackTrace}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
            )
          },
          {
            key: 'performance',
            label: (
              <span>
                <MonitorOutlined />
                性能监控
              </span>
            ),
            children: (
          <div className="performance-monitor">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card title="性能指标概览" className="logs-card">
                  <div className="performance-metrics">
                    {performanceMetrics.map((metric, index) => (
                      <div key={index} className="metric-card">
                        <div className="metric-value">
                          {metric.value}
                          <span style={{ fontSize: 14, fontWeight: 400, marginLeft: 4 }}>
                            {metric.unit}
                          </span>
                        </div>
                        <div className="metric-label">{metric.name}</div>
                        {metric.change !== 0 && (
                          <div className={`metric-trend ${metric.trend}`}>
                            {metric.trend === 'up' && <RiseOutlined />}
                            {metric.trend === 'down' && <FallOutlined />}
                            {metric.change > 0 ? '+' : ''}{metric.change}
                            {metric.unit === '%' ? 'pp' : metric.unit}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="系统资源使用率" className="logs-card">
                  <ReactECharts
                    option={{
                      title: {
                        text: 'CPU & 内存使用率',
                        left: 'center',
                        textStyle: { fontSize: 14, fontWeight: 600 }
                      },
                      tooltip: {
                        trigger: 'axis',
                        formatter: (params: any) => {
                          return params.map((param: any) =>
                            `${param.seriesName}: ${param.value}%`
                          ).join('<br/>');
                        }
                      },
                      legend: {
                        data: ['CPU使用率', '内存使用率'],
                        bottom: 0
                      },
                      xAxis: {
                        type: 'category',
                        data: Array.from({ length: 12 }, (_, i) => `${10 + i}:${String(i * 5).padStart(2, '0')}`),
                      },
                      yAxis: {
                        type: 'value',
                        name: '使用率 (%)',
                        max: 100
                      },
                      series: [
                        {
                          name: 'CPU使用率',
                          type: 'line',
                          data: Array.from({ length: 12 }, () => Math.floor(Math.random() * 60) + 20),
                          smooth: true,
                          lineStyle: { color: '#1890ff' }
                        },
                        {
                          name: '内存使用率',
                          type: 'line',
                          data: Array.from({ length: 12 }, () => Math.floor(Math.random() * 40) + 50),
                          smooth: true,
                          lineStyle: { color: '#52c41a' }
                        }
                      ],
                      grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                      }
                    }}
                    style={{ height: '300px', width: '100%' }}
                  />
                </Card>
              </Col>

              <Col span={12}>
                <Card title="请求响应时间" className="logs-card">
                  <ReactECharts
                    option={{
                      title: {
                        text: '平均响应时间趋势',
                        left: 'center',
                        textStyle: { fontSize: 14, fontWeight: 600 }
                      },
                      tooltip: {
                        trigger: 'axis',
                        formatter: (params: any) => {
                          const data = params[0];
                          return `${data.name}<br/>响应时间: ${data.value}ms`;
                        }
                      },
                      xAxis: {
                        type: 'category',
                        data: Array.from({ length: 12 }, (_, i) => `${10 + i}:${String(i * 5).padStart(2, '0')}`),
                      },
                      yAxis: {
                        type: 'value',
                        name: '响应时间 (ms)'
                      },
                      series: [{
                        name: '响应时间',
                        type: 'bar',
                        data: Array.from({ length: 12 }, () => Math.floor(Math.random() * 2000) + 500),
                        itemStyle: {
                          color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                              { offset: 0, color: '#faad14' },
                              { offset: 1, color: '#ffc53d' }
                            ]
                          }
                        }
                      }],
                      grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                      }
                    }}
                    style={{ height: '300px', width: '100%' }}
                  />
                </Card>
              </Col>
            </Row>
          </div>
            )
          }
        ]}
      />
    </div>
  );
};

export default Logs;
