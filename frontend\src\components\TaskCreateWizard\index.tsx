import React, { useState, useCallback } from 'react';
import {
  Steps,
  Card,
  Button,
  Space,
  message,
  Result,
  Typography
} from 'antd';
import {
  DatabaseOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

import type {
  TaskCreateWizardData,
  TaskCreateResponse,
  TaskCreateError,
  TaskPriority
} from '../../types/taskCreate';
import { CreateStep } from '../../types/taskCreate';
import { DEFAULT_TASK_CONFIG, DEFAULT_SCHEDULE_CONFIG } from '../../types/taskCreate';
import { createTaskFromUrls } from '../../services/taskCreateApi';

import UrlSelectionStep from './UrlSelectionStep';
import BasicConfigStep from './BasicConfigStep';
import ScheduleConfigStep from './ScheduleConfigStep';
import ConfirmStep from './ConfirmStep';

const { Title } = Typography;

// 时间转换函数：将前端的start_time转换为后端期望的time格式
const convertTimeForBackend = (timeValue: string | undefined, scheduleType: string): string | undefined => {
  if (!timeValue) return undefined;

  try {
    // 如果已经是HH:mm格式，直接返回
    if (timeValue.match(/^\d{2}:\d{2}$/)) {
      return timeValue;
    }

    // 否则尝试解析为dayjs对象
    const dayjsTime = dayjs(timeValue);

    // 检查dayjs对象是否有效
    if (!dayjsTime.isValid()) {
      console.warn('Invalid time value for conversion:', timeValue);
      return undefined;
    }

    if (scheduleType === 'daily') {
      // 对于每日任务，只需要时间部分（HH:mm）
      return dayjsTime.format('HH:mm');
    }
    // 对于其他类型，返回完整的ISO时间
    return dayjsTime.toISOString();
  } catch (error) {
    console.warn('Time conversion for backend failed:', timeValue, error);
    return undefined;
  }
};

interface TaskCreateWizardProps {
  onSuccess?: (response: TaskCreateResponse) => void;
  onCancel?: () => void;
  initialUrls?: string[];
}

const TaskCreateWizard: React.FC<TaskCreateWizardProps> = ({
  onSuccess,
  onCancel,
  initialUrls = []
}) => {
  const [currentStep, setCurrentStep] = useState<CreateStep>(CreateStep.SELECT_URLS);
  const [loading, setLoading] = useState(false);
  const [createResult, setCreateResult] = useState<TaskCreateResponse | null>(null);
  const [error, setError] = useState<TaskCreateError | null>(null);

  // 向导数据状态
  const [wizardData, setWizardData] = useState<TaskCreateWizardData>({
    selectedUrls: initialUrls,
    basicConfig: {
      name: '',
      description: '',
      platform: 'mercadolibre',
      priority: 'normal',
      tags: []
    },
    taskConfig: { ...DEFAULT_TASK_CONFIG },
    scheduleConfig: { ...DEFAULT_SCHEDULE_CONFIG }
  });

  // 步骤配置
  const steps = [
    {
      title: '选择URL',
      description: '从URL池中选择要监控的链接',
      icon: <DatabaseOutlined />,
      content: (
        <UrlSelectionStep
          selectedUrls={wizardData.selectedUrls}
          onSelectionChange={(urls) => updateWizardData({ selectedUrls: urls })}
        />
      )
    },
    {
      title: '基础配置',
      description: '设置任务名称和基本参数',
      icon: <SettingOutlined />,
      content: (
        <BasicConfigStep
          basicConfig={wizardData.basicConfig}
          taskConfig={wizardData.taskConfig}
          onConfigChange={(basic, task) => updateWizardData({
            basicConfig: basic,
            taskConfig: task
          })}
        />
      )
    },
    {
      title: '调度配置',
      description: '设置任务执行时间和频率',
      icon: <ClockCircleOutlined />,
      content: (
        <ScheduleConfigStep
          scheduleConfig={wizardData.scheduleConfig}
          onConfigChange={(schedule) => updateWizardData({ scheduleConfig: schedule })}
        />
      )
    },
    {
      title: '确认创建',
      description: '预览配置并创建任务',
      icon: <CheckCircleOutlined />,
      content: (
        <ConfirmStep
          wizardData={wizardData}
          loading={loading}
        />
      )
    }
  ];

  // 更新向导数据
  const updateWizardData = useCallback((updates: Partial<TaskCreateWizardData>) => {
    setWizardData(prev => ({ ...prev, ...updates }));
  }, []);

  // 下一步
  const handleNext = useCallback(() => {
    // 验证当前步骤是否可以进入下一步
    if (currentStep === CreateStep.SELECT_URLS) {
      if (wizardData.selectedUrls.length === 0) {
        message.warning('请至少选择一个URL');
        return;
      }
    } else if (currentStep === CreateStep.BASIC_CONFIG) {
      if (!wizardData.basicConfig.name.trim()) {
        message.error('请输入任务名称');
        return;
      }
      if (!wizardData.basicConfig.platform) {
        message.error('请选择目标平台');
        return;
      }
    } else if (currentStep === CreateStep.SCHEDULE_CONFIG) {
      if (wizardData.scheduleConfig.enabled) {
        if (wizardData.scheduleConfig.type === 'custom' && !wizardData.scheduleConfig.cron_expression) {
          message.error('自定义调度类型必须提供cron表达式');
          return;
        }
        if (wizardData.scheduleConfig.type === 'hourly' && !wizardData.scheduleConfig.interval) {
          message.error('小时间隔调度必须提供间隔时间');
          return;
        }
      }
    }

    if (currentStep < CreateStep.CONFIRM) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, wizardData]);

  // 上一步
  const handlePrev = useCallback(() => {
    if (currentStep > CreateStep.SELECT_URLS) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // 创建任务
  const handleCreateTask = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // 构建请求数据，转换调度配置中的时间格式
      console.log('Original schedule config:', wizardData.scheduleConfig);

      // 根据调度类型获取正确的时间字段
      const timeFieldValue = wizardData.scheduleConfig.type === 'daily'
        ? wizardData.scheduleConfig.time
        : wizardData.scheduleConfig.start_time;

      const convertedTime = convertTimeForBackend(timeFieldValue, wizardData.scheduleConfig.type);
      const convertedEndTime = convertTimeForBackend(wizardData.scheduleConfig.end_time, 'daily');

      console.log('Time conversion results:', {
        schedule_type: wizardData.scheduleConfig.type,
        time_field_value: timeFieldValue,
        converted_time: convertedTime,
        original_end_time: wizardData.scheduleConfig.end_time,
        converted_end_time: convertedEndTime
      });

      const convertedSchedule = {
        ...wizardData.scheduleConfig,
        time: convertedTime,
        end_time: convertedEndTime
      };

      // 移除前端特有的字段，但保留转换后的end_time
      const { start_time, ...backendSchedule } = convertedSchedule;

      // 优先级转换：确保使用后端接受的值
      const convertPriorityToBackend = (frontendPriority: string): TaskPriority => {
        // 后端接受: 'low', 'normal', 'high', 'urgent'
        const priorityMap: Record<string, TaskPriority> = {
          'low': 'low',
          'normal': 'normal',
          'medium': 'normal',  // 兼容处理
          'high': 'high',
          'urgent': 'urgent'
        };

        const mappedPriority = priorityMap[frontendPriority] || 'normal';
        console.log(`TaskCreateWizard 优先级转换: ${frontendPriority} → ${mappedPriority}`);
        return mappedPriority;
      };

      const request = {
        name: wizardData.basicConfig.name,
        description: wizardData.basicConfig.description,
        url_ids: wizardData.selectedUrls,
        schedule: backendSchedule,
        config: {
          ...wizardData.taskConfig,
          platform: wizardData.basicConfig.platform,
          priority: convertPriorityToBackend(wizardData.basicConfig.priority)
        },
        tags: wizardData.basicConfig.tags
      };

      console.log('Final request data:', request);

      // 调用API创建任务
      const response = await createTaskFromUrls(request);

      setCreateResult(response);

      // 调用成功回调
      if (onSuccess) {
        onSuccess(response);
      }

    } catch (err: any) {
      const error: TaskCreateError = {
        type: err.response?.status === 400 ? 'validation' : 'server',
        message: err.response?.data?.detail || err.message || '创建任务失败',
        details: err.response?.data
      };

      setError(error);
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  }, [wizardData, onSuccess]);

  // 重新开始
  const handleRestart = useCallback(() => {
    setCurrentStep(CreateStep.SELECT_URLS);
    setCreateResult(null);
    setError(null);
    setWizardData({
      selectedUrls: [],
      basicConfig: {
        name: '',
        description: '',
        platform: 'mercadolibre',
        priority: 'normal',
        tags: []
      },
      taskConfig: { ...DEFAULT_TASK_CONFIG },
      scheduleConfig: { ...DEFAULT_SCHEDULE_CONFIG }
    });
  }, []);

  // 如果创建成功，显示结果页面
  if (createResult) {
    return (
      <Card>
        <Result
          status="success"
          title="任务创建成功！"
          subTitle={
            <div>
              <p>任务 "{createResult.task_name}" 已成功创建</p>
              <p>关联URL数量: {createResult.url_count}</p>
              <p>调度信息: {createResult.schedule_info}</p>
              {createResult.next_run && (
                <p>下次执行时间: {new Date(createResult.next_run).toLocaleString()}</p>
              )}
            </div>
          }
          extra={[
            <Button key="restart" onClick={handleRestart}>
              创建新任务
            </Button>,
            <Button key="close" type="primary" onClick={onCancel}>
              关闭
            </Button>
          ]}
        />
      </Card>
    );
  }

  // 如果有错误，显示错误页面
  if (error) {
    return (
      <Card>
        <Result
          status="error"
          title="任务创建失败"
          subTitle={error.message}
          extra={[
            <Button key="retry" type="primary" onClick={() => setError(null)}>
              重试
            </Button>,
            <Button key="restart" onClick={handleRestart}>
              重新开始
            </Button>,
            <Button key="cancel" onClick={onCancel}>
              取消
            </Button>
          ]}
        />
      </Card>
    );
  }

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto', padding: 24 }}>
      <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
        创建监控任务
      </Title>

      {/* 步骤指示器 */}
      <Card style={{ marginBottom: 24 }}>
        <Steps
          current={currentStep}
          items={steps.map((step, index) => ({
            title: step.title,
            description: step.description,
            icon: step.icon,
            status: index === currentStep ? 'process' : 
                   index < currentStep ? 'finish' : 'wait'
          }))}
        />
      </Card>

      {/* 步骤内容 */}
      <Card>
        {steps[currentStep].content}
      </Card>

      {/* 底部导航按钮 */}
      <Card style={{ marginTop: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Button onClick={onCancel} disabled={loading}>
              取消
            </Button>
          </div>

          <div>
            <Space>
              {currentStep > CreateStep.SELECT_URLS && (
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={handlePrev}
                  disabled={loading}
                >
                  上一步
                </Button>
              )}

              {currentStep < CreateStep.CONFIRM ? (
                <Button
                  type="primary"
                  icon={<ArrowRightOutlined />}
                  onClick={handleNext}
                  disabled={loading}
                >
                  下一步
                </Button>
              ) : (
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={handleCreateTask}
                  loading={loading}
                >
                  创建任务
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TaskCreateWizard;
