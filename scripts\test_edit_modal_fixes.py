#!/usr/bin/env python3
"""
测试编辑模态框修复效果

目的：
1. 验证URL管理功能是否正常
2. 验证随机延迟配置是否正确显示
3. 测试分页功能
"""

import requests
import json


def test_edit_modal_fixes():
    """测试编辑模态框修复效果"""
    print("🧪 测试编辑模态框修复效果...")
    print("=" * 60)
    
    # 1. 找一个有很多URL的任务
    response = requests.get('http://localhost:8000/api/v1/monitoring-tasks/?page=1&page_size=10')
    if response.status_code != 200:
        print("❌ 无法获取任务列表")
        return
    
    tasks = response.json()['data']
    test_task = None
    
    for task in tasks:
        if task['total_urls'] > 20:
            test_task = task
            break
    
    if not test_task:
        print("❌ 没有找到有足够URL的任务进行测试")
        return
    
    task_id = test_task['id']
    task_name = test_task['name']
    total_urls = test_task['total_urls']
    
    print(f"📋 测试任务: {task_name}")
    print(f"   任务ID: {task_id}")
    print(f"   总URL数: {total_urls}")
    
    # 2. 测试URL管理API - 第一页
    print(f"\n🔍 测试URL管理API...")
    url_response = requests.get(f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}/urls?page=1&page_size=20')
    
    if url_response.status_code != 200:
        print(f"❌ URL列表API调用失败: {url_response.status_code}")
        return
    
    url_data = url_response.json()
    
    if not url_data.get('success'):
        print(f"❌ URL列表API返回失败")
        return
    
    page1_urls = url_data.get('data', [])
    api_total = url_data.get('total', 0)
    
    print(f"   ✅ 第一页URL数: {len(page1_urls)}")
    print(f"   ✅ API返回总数: {api_total}")
    print(f"   ✅ 数据一致性: {'✅' if api_total == total_urls else '❌'}")
    
    # 3. 测试分页功能
    if api_total > 20:
        print(f"\n📄 测试分页功能...")
        
        # 测试第二页
        page2_response = requests.get(f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}/urls?page=2&page_size=20')
        if page2_response.status_code == 200:
            page2_data = page2_response.json()
            page2_urls = page2_data.get('data', [])
            print(f"   ✅ 第二页URL数: {len(page2_urls)}")
            
            # 检查URL是否不同
            page1_ids = {url['id'] for url in page1_urls}
            page2_ids = {url['id'] for url in page2_urls}
            overlap = page1_ids & page2_ids
            
            if not overlap:
                print(f"   ✅ 分页数据无重复")
            else:
                print(f"   ❌ 分页数据有重复: {len(overlap)}个")
        else:
            print(f"   ❌ 第二页请求失败")
        
        # 测试不同页面大小
        page_size_response = requests.get(f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}/urls?page=1&page_size=50')
        if page_size_response.status_code == 200:
            page_size_data = page_size_response.json()
            page_size_urls = page_size_data.get('data', [])
            print(f"   ✅ 页面大小50: {len(page_size_urls)}个URL")
        else:
            print(f"   ❌ 不同页面大小测试失败")
    
    # 4. 测试任务详情API（编辑时调用）
    print(f"\n🔍 测试任务详情API...")
    detail_response = requests.get(f'http://localhost:8000/api/v1/monitoring-tasks/{task_id}')
    
    if detail_response.status_code != 200:
        print(f"❌ 任务详情API调用失败")
        return
    
    task_detail = detail_response.json()['data']
    schedule = task_detail['schedule']
    
    print(f"   ✅ 任务详情获取成功")
    
    # 5. 验证随机延迟配置
    print(f"\n🎛️ 验证随机延迟配置...")
    
    required_fields = ['enable_random_delay', 'random_delay_min', 'random_delay_max']
    missing_fields = []
    
    for field in required_fields:
        if field in schedule:
            value = schedule[field]
            print(f"   ✅ {field}: {value}")
        else:
            missing_fields.append(field)
            print(f"   ❌ {field}: 缺失")
    
    if not missing_fields:
        print(f"   ✅ 所有随机延迟配置字段都存在")
    else:
        print(f"   ❌ 缺失字段: {missing_fields}")
    
    # 6. 测试URL详情
    if page1_urls:
        print(f"\n🔗 验证URL详情...")
        sample_url = page1_urls[0]
        
        required_url_fields = ['id', 'url', 'platform', 'status', 'added_at']
        url_missing_fields = []
        
        for field in required_url_fields:
            if field in sample_url:
                print(f"   ✅ {field}: {sample_url[field]}")
            else:
                url_missing_fields.append(field)
                print(f"   ❌ {field}: 缺失")
        
        if not url_missing_fields:
            print(f"   ✅ URL字段完整")
        else:
            print(f"   ❌ URL缺失字段: {url_missing_fields}")
    
    # 7. 总结测试结果
    print(f"\n" + "=" * 60)
    print(f"🎯 测试总结:")
    
    tests = [
        ("URL列表API", len(page1_urls) > 0),
        ("数据总数一致", api_total == total_urls),
        ("分页功能", api_total > 20),
        ("任务详情API", 'schedule' in task_detail),
        ("随机延迟配置", len(missing_fields) == 0),
        ("URL字段完整", len(page1_urls) > 0 and 'id' in page1_urls[0])
    ]
    
    passed_tests = sum(1 for _, passed in tests if passed)
    total_tests = len(tests)
    
    for test_name, passed in tests:
        status = "✅" if passed else "❌"
        print(f"   {status} {test_name}")
    
    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print(f"🎉 所有测试通过！编辑模态框修复成功。")
    else:
        print(f"⚠️  部分测试失败，需要进一步检查。")
    
    return {
        'task_id': task_id,
        'task_name': task_name,
        'total_urls': total_urls,
        'api_total': api_total,
        'tests_passed': passed_tests,
        'tests_total': total_tests
    }


if __name__ == "__main__":
    test_edit_modal_fixes()
