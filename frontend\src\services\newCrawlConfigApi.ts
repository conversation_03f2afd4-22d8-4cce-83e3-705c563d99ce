/**
 * 新爬取配置API服务
 * 用于获取新的爬取配置管理中创建的配置
 */

// 导入现有的类型定义
import { CrawlerConfigSummary } from '../types/newArchitecture';

// 新爬取配置数据类型
export interface CrawlConfig {
  id: string;
  name: string;
  description: string;
  config_type: 'batch_task' | 'single_task' | 'template';
  platform: 'mercadolibre' | 'amazon' | '1688' | 'general';
  llm_query: string;
  llm_schema?: string;
  priority: 'high' | 'medium' | 'low';
  cache_enabled: boolean;
  max_concurrent_tasks?: number;
  batch_timeout?: number;
  callback_url?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  usage_count: number;
  last_used_at?: string;
}

export interface CrawlConfigListResponse {
  configs: CrawlConfig[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface CrawlConfigApiResponse {
  success: boolean;
  data: CrawlConfigListResponse;
  message: string;
}



const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const API_BASE = `${API_BASE_URL}/api/v1/crawl-configs`;

class NewCrawlConfigApiService {
  /**
   * 获取爬取配置列表
   */
  async getConfigs(params?: {
    platform?: string;
    config_type?: string;
    is_active?: boolean;
    page?: number;
    page_size?: number;
  }): Promise<CrawlerConfigSummary[]> {
    const searchParams = new URLSearchParams();
    
    if (params?.platform) searchParams.append('platform', params.platform);
    if (params?.config_type) searchParams.append('config_type', params.config_type);
    if (params?.is_active !== undefined) searchParams.append('is_active', params.is_active.toString());
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.page_size) searchParams.append('page_size', params.page_size.toString());

    const response = await fetch(`${API_BASE}/?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取配置列表失败: ${response.statusText}`);
    }
    
    const result: CrawlConfigApiResponse = await response.json();
    
    // 转换为Worker表单需要的格式
    return result.data.configs.map(config => ({
      config_id: config.id,
      config_name: config.name,
      version: '1.0', // 新配置暂时使用固定版本
      status: config.is_active ? 'active' as const : 'inactive' as const,
      tags: [config.platform, config.config_type], // 使用平台和类型作为标签
      description: config.description,
      usage_count: config.usage_count,
      last_used: config.last_used_at,
      created_at: config.created_at,
      updated_at: config.updated_at
    }));
  }

  /**
   * 获取单个爬取配置详情
   */
  async getConfig(configId: string): Promise<CrawlConfig> {
    const response = await fetch(`${API_BASE}/${configId}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('配置不存在');
      }
      throw new Error(`获取配置失败: ${response.statusText}`);
    }
    
    const result = await response.json();
    return result.data;
  }

  /**
   * 获取活跃的配置列表（用于Worker选择）
   */
  async getActiveConfigs(): Promise<CrawlerConfigSummary[]> {
    return this.getConfigs({ is_active: true });
  }
}

const newCrawlConfigApiService = new NewCrawlConfigApiService();
export default newCrawlConfigApiService;
