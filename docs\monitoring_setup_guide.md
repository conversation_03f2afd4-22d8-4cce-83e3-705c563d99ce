# MonIt监控系统部署指南

## 概述

MonIt监控系统基于现代化的监控技术栈，提供全方位的系统监控、日志管理和告警功能。

## 技术栈

- **Prometheus**: 指标收集和存储
- **Grafana**: 监控面板和可视化
- **Elasticsearch**: 日志存储和搜索
- **Logstash**: 日志处理和转换
- **Kibana**: 日志分析和可视化
- **Filebeat**: 日志收集代理

## 系统架构

```
应用服务 → Prometheus → Grafana (监控面板)
    ↓
  日志文件 → Filebeat → Logstash → Elasticsearch → Kibana (日志分析)
```

## 快速开始

### 1. 环境要求

- Docker 20.0+
- Docker Compose 2.0+
- 可用内存: 8GB+
- 磁盘空间: 50GB+

### 2. 启动监控系统

#### Windows
```bash
scripts\start_monitoring.bat
```

#### Linux/macOS
```bash
chmod +x scripts/start_monitoring.sh
./scripts/start_monitoring.sh
```

### 3. 验证部署

```bash
python scripts/test_monitoring.py
```

## 访问地址

| 服务 | 地址 | 用户名/密码 |
|------|------|-------------|
| Grafana | http://localhost:3001 | admin/admin123 |
| Prometheus | http://localhost:9090 | - |
| Kibana | http://localhost:5601 | - |
| Elasticsearch | http://localhost:9200 | - |

## 监控面板

### 系统概览面板
- CPU、内存、磁盘使用率
- 服务健康状态
- 网络流量统计

### 应用性能面板
- HTTP请求统计
- 响应时间分布
- 错误率监控

### 爬虫监控面板
- 任务执行统计
- URL处理速度
- 成功率分析

### 数据库监控面板
- 连接池状态
- 查询性能
- 事务统计

## 告警配置

### 系统告警
- CPU使用率 > 80%
- 内存使用率 > 85%
- 磁盘使用率 > 90%

### 应用告警
- 服务不可用
- 错误率 > 5%
- 响应时间 > 5秒

### 业务告警
- 爬虫失败率 > 10%
- 任务队列堆积
- 数据库连接异常

## 日志管理

### 日志收集
- 应用日志: `/logs/backend/`
- Celery日志: `/logs/celery/`
- 爬虫日志: `/logs/crawler/`

### 日志格式
```json
{
  "timestamp": "2024-12-29T10:00:00Z",
  "level": "INFO",
  "service": "monit-backend",
  "message": "Request processed",
  "request_id": "uuid",
  "duration": 0.123
}
```

### 日志查询
在Kibana中使用以下查询语法：
- 按服务过滤: `service:monit-backend`
- 按级别过滤: `level:ERROR`
- 时间范围: 使用时间选择器

## 性能优化

### Elasticsearch优化
```yaml
# 内存设置
ES_JAVA_OPTS: "-Xms2g -Xmx2g"

# 索引设置
number_of_shards: 1
number_of_replicas: 0
```

### Prometheus优化
```yaml
# 数据保留
retention.time: 30d
retention.size: 10GB

# 采集间隔
scrape_interval: 15s
```

## 故障排查

### 常见问题

1. **Elasticsearch启动失败**
   - 检查内存设置
   - 确认磁盘空间充足
   - 查看容器日志

2. **Grafana无法连接Prometheus**
   - 检查网络连接
   - 验证数据源配置
   - 确认服务状态

3. **日志未显示在Kibana**
   - 检查Filebeat配置
   - 验证Logstash处理
   - 确认索引模式

### 日志查看
```bash
# 查看所有服务状态
docker-compose -f docker-compose.monitoring.yml ps

# 查看特定服务日志
docker-compose -f docker-compose.monitoring.yml logs -f prometheus
docker-compose -f docker-compose.monitoring.yml logs -f grafana
docker-compose -f docker-compose.monitoring.yml logs -f elasticsearch
```

## 维护操作

### 备份数据
```bash
# 备份Grafana配置
docker cp monit-grafana:/var/lib/grafana ./backup/grafana

# 备份Prometheus数据
docker cp monit-prometheus:/prometheus ./backup/prometheus
```

### 清理日志
```bash
# 清理旧日志索引
curl -X DELETE "localhost:9200/logstash-*-$(date -d '30 days ago' +%Y.%m.%d)"
```

### 更新配置
```bash
# 重新加载Prometheus配置
curl -X POST http://localhost:9090/-/reload

# 重启服务
docker-compose -f docker-compose.monitoring.yml restart [service_name]
```

## 扩展配置

### 添加新的监控目标
编辑 `monitoring/prometheus/prometheus.yml`:
```yaml
scrape_configs:
  - job_name: 'new-service'
    static_configs:
      - targets: ['new-service:port']
```

### 自定义Grafana面板
1. 登录Grafana
2. 创建新面板
3. 配置查询和可视化
4. 保存面板配置

### 配置告警通知
编辑 `monitoring/grafana/provisioning/notifiers/`:
```yaml
notifiers:
  - name: email
    type: email
    settings:
      addresses: <EMAIL>
```

## 安全配置

### 启用认证
```yaml
# Grafana认证
GF_SECURITY_ADMIN_PASSWORD: your_secure_password

# Elasticsearch安全
xpack.security.enabled: true
```

### 网络安全
- 使用防火墙限制访问
- 配置SSL/TLS加密
- 设置访问控制列表

## 性能指标

### 系统指标
- CPU使用率: < 70%
- 内存使用率: < 80%
- 磁盘使用率: < 85%

### 应用指标
- 响应时间: < 1秒
- 错误率: < 1%
- 可用性: > 99.9%

### 监控指标
- 指标收集延迟: < 30秒
- 日志处理延迟: < 1分钟
- 告警响应时间: < 2分钟
