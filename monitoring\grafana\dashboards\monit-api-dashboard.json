{"dashboard": {"id": null, "title": "MonIt API Performance", "tags": ["monit", "api", "<PERSON><PERSON><PERSON>"], "timezone": "browser", "editable": true, "graphTooltip": 1, "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "HTTP Requests Rate", "type": "timeseries", "targets": [{"expr": "rate(monit_http_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}} ({{status}})", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "HTTP Response Time", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(monit_http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(monit_http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(monit_http_request_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "HTTP Status Codes", "type": "timeseries", "targets": [{"expr": "rate(monit_http_requests_total{status=~\"2..\"}[5m])", "legendFormat": "2xx Success", "refId": "A"}, {"expr": "rate(monit_http_requests_total{status=~\"4..\"}[5m])", "legendFormat": "4xx Client Error", "refId": "B"}, {"expr": "rate(monit_http_requests_total{status=~\"5..\"}[5m])", "legendFormat": "5xx Server Error", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(monit_http_requests_total{status=~\"5..\"}[5m]) / rate(monit_http_requests_total[5m]) * 100", "legendFormat": "Error Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background"}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Top Endpoints by Request Count", "type": "timeseries", "targets": [{"expr": "topk(10, rate(monit_http_requests_total[5m]))", "legendFormat": "{{method}} {{endpoint}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Slowest Endpoints", "type": "timeseries", "targets": [{"expr": "topk(10, histogram_quantile(0.95, rate(monit_http_request_duration_seconds_bucket[5m])))", "legendFormat": "{{method}} {{endpoint}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}