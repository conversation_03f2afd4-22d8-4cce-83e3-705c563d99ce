from typing import Optional
from pydantic import field_validator, Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # 基础配置
    APP_NAME: str = "MonIt Crawler System"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://crawler_user:crawler_password@localhost:5432/monit_crawler_dev"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # API配置
    API_PREFIX: str = "/api/v1"
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # CORS配置
    CORS_ORIGINS: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 爬虫API客户端配置
    CRAWLER_API_BASE_URL: str = "http://localhost:8080"
    CRAWLER_API_VERSION: str = "v1"
    CRAWLER_API_KEY: Optional[str] = None
    CRAWLER_API_SECRET: Optional[str] = None
    CRAWLER_API_AUTH_TYPE: str = "api_key"  # api_key, bearer_token, basic_auth, none
    CRAWLER_API_TIMEOUT: float = 30.0
    CRAWLER_API_MAX_RETRIES: int = 3
    CRAWLER_API_RETRY_DELAY: float = 1.0
    CRAWLER_API_VERIFY_SSL: bool = True

    # 爬虫引擎配置
    CRAWLER_MAX_CONCURRENT: int = 10
    CRAWLER_DEFAULT_PLATFORM: str = "mercadolibre"
    CRAWLER_SUPPORTED_PLATFORMS: list = ["mercadolibre", "amazon", "ebay"]

    # API限流配置
    CRAWLER_API_RATE_LIMIT_REQUESTS: int = 100
    CRAWLER_API_RATE_LIMIT_PERIOD: int = 60  # seconds
    
    # 文件配置
    UPLOAD_DIR: str = "data/uploads"
    EXPORT_DIR: str = "data/exports"
    LOG_DIR: str = "logs"
    MAX_UPLOAD_SIZE: int = 50 * 1024 * 1024  # 50MB
    
    # Celery配置
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: list = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    CELERY_ENABLE_UTC: bool = True
    
    # TimescaleDB配置
    TIMESCALE_CHUNK_TIME_INTERVAL: str = "1 day"
    TIMESCALE_COMPRESS_AFTER: str = "7 days"
    TIMESCALE_DROP_AFTER: str = "365 days"
    
    # 监控配置
    METRICS_ENABLED: bool = True
    LOG_LEVEL: str = "INFO"
    
    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def validate_database_url(cls, v: Optional[str]) -> str:
        if isinstance(v, str) and v:
            return v
        # 如果没有提供DATABASE_URL，使用默认值
        return "postgresql://crawler_user:crawler_password@localhost:5432/monit_crawler_dev"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局设置实例
settings = Settings()

# 兼容性函数
def get_settings() -> Settings:
    """获取设置实例"""
    return settings