#!/usr/bin/env python3
"""
测试前端集成
验证前端编译和API集成是否正常
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any


class FrontendIntegrationTester:
    """前端集成测试器"""
    
    def __init__(self, frontend_url: str = "http://localhost:3000", backend_url: str = "http://localhost:8000"):
        self.frontend_url = frontend_url
        self.backend_url = backend_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_frontend_accessibility(self) -> Dict[str, Any]:
        """测试前端页面可访问性"""
        print("🌐 测试前端页面可访问性...")
        
        try:
            async with self.session.get(self.frontend_url, timeout=10) as response:
                if response.status == 200:
                    content = await response.text()
                    if "MonIt" in content or "React" in content:
                        print("  ✅ 前端页面正常访问")
                        return {"success": True, "status": response.status}
                    else:
                        print("  ⚠️ 前端页面内容异常")
                        return {"success": False, "error": "页面内容异常"}
                else:
                    print(f"  ❌ 前端页面访问失败: {response.status}")
                    return {"success": False, "error": f"HTTP {response.status}"}
        except Exception as e:
            print(f"  ❌ 前端页面访问异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_backend_api_accessibility(self) -> Dict[str, Any]:
        """测试后端API可访问性"""
        print("🔧 测试后端API可访问性...")
        
        try:
            url = f"{self.backend_url}/api/v1/crawl-configs/system-config"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "success":
                        print("  ✅ 后端API正常访问")
                        return {"success": True, "data": data}
                    else:
                        print("  ⚠️ 后端API响应异常")
                        return {"success": False, "error": "API响应异常"}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 后端API访问失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 后端API访问异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_cors_configuration(self) -> Dict[str, Any]:
        """测试CORS配置"""
        print("🔗 测试CORS配置...")
        
        try:
            url = f"{self.backend_url}/api/v1/crawl-configs/system-config"
            headers = {
                'Origin': self.frontend_url,
                'Content-Type': 'application/json'
            }
            
            async with self.session.get(url, headers=headers, timeout=10) as response:
                cors_headers = {
                    'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
                    'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
                    'access-control-allow-headers': response.headers.get('access-control-allow-headers')
                }
                
                if response.status == 200:
                    print("  ✅ CORS配置正常")
                    print(f"  📋 CORS头信息: {cors_headers}")
                    return {"success": True, "cors_headers": cors_headers}
                else:
                    print(f"  ❌ CORS测试失败: {response.status}")
                    return {"success": False, "error": f"HTTP {response.status}"}
        except Exception as e:
            print(f"  ❌ CORS测试异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_system_config_integration(self) -> Dict[str, Any]:
        """测试系统配置集成"""
        print("⚙️ 测试系统配置集成...")
        
        try:
            # 测试系统配置API
            url = f"{self.backend_url}/api/v1/crawl-configs/system-config"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 验证关键配置
                    crawler_config = data.get("data", {}).get("crawler_system_config", {})
                    required_fields = ["max_concurrent_tasks", "batch_timeout", "default_callback_url"]
                    
                    missing_fields = [field for field in required_fields if field not in crawler_config]
                    
                    if not missing_fields:
                        print("  ✅ 系统配置集成正常")
                        print(f"  📊 配置参数: 并发数={crawler_config['max_concurrent_tasks']}, 超时={crawler_config['batch_timeout']}秒")
                        return {"success": True, "config": crawler_config}
                    else:
                        print(f"  ❌ 系统配置缺少字段: {missing_fields}")
                        return {"success": False, "error": f"缺少字段: {missing_fields}"}
                else:
                    error_text = await response.text()
                    print(f"  ❌ 系统配置API失败: {response.status} - {error_text}")
                    return {"success": False, "error": error_text}
        except Exception as e:
            print(f"  ❌ 系统配置集成异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def run_all_tests(self):
        """运行所有集成测试"""
        print("🚀 开始前端集成测试...\n")
        
        results = {}
        
        # 1. 测试前端可访问性
        frontend_result = await self.test_frontend_accessibility()
        results["frontend_accessibility"] = frontend_result
        
        # 2. 测试后端API可访问性
        backend_result = await self.test_backend_api_accessibility()
        results["backend_api_accessibility"] = backend_result
        
        # 3. 测试CORS配置
        cors_result = await self.test_cors_configuration()
        results["cors_configuration"] = cors_result
        
        # 4. 测试系统配置集成
        system_config_result = await self.test_system_config_integration()
        results["system_config_integration"] = system_config_result
        
        # 总结测试结果
        print("\n📊 集成测试结果总结:")
        success_count = sum(1 for result in results.values() if result.get("success"))
        total_count = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result.get("success") else "❌ 失败"
            print(f"  {test_name}: {status}")
            if not result.get("success") and "error" in result:
                print(f"    错误: {result['error']}")
        
        print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
        
        if success_count == total_count:
            print("🎉 所有集成测试通过！前端和后端集成正常！")
        else:
            print("⚠️  部分测试失败，需要检查集成配置")
        
        return results


async def main():
    """主函数"""
    async with FrontendIntegrationTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
