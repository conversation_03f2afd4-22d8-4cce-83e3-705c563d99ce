"""
爬虫API客户端

实现对外部爬虫服务的HTTP API调用。
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import json

import httpx
from httpx import AsyncClient, Response

from .api_config import APIConfig
from .api_exceptions import (
    APIException, 
    APITimeoutException, 
    APIAuthException,
    APIRateLimitException,
    APIServerException,
    APIValidationException
)


class CrawlerAPIClient:
    """爬虫API客户端"""
    
    def __init__(self, config: Optional[APIConfig] = None):
        """初始化API客户端
        
        Args:
            config: API配置，如果为None则使用默认配置
        """
        self.config = config or APIConfig.from_env()
        self.config.validate()
        
        self.logger = logging.getLogger(__name__)
        self.client: Optional[AsyncClient] = None
        
        # 限流控制
        self._request_times: List[float] = []
        self._rate_limit_lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            "requests_sent": 0,
            "requests_successful": 0,
            "requests_failed": 0,
            "total_response_time": 0.0,
            "last_request_time": None
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self) -> None:
        """启动客户端"""
        if self.client is None:
            self.client = AsyncClient(
                timeout=httpx.Timeout(self.config.timeout),
                verify=self.config.verify_ssl,
                headers=self.config.headers
            )
            self.logger.info("API client started")
    
    async def close(self) -> None:
        """关闭客户端"""
        if self.client:
            await self.client.aclose()
            self.client = None
            self.logger.info("API client closed")
    
    async def _check_rate_limit(self) -> None:
        """检查限流"""
        async with self._rate_limit_lock:
            now = time.time()
            
            # 清理过期的请求时间
            cutoff_time = now - self.config.rate_limit_period
            self._request_times = [t for t in self._request_times if t > cutoff_time]
            
            # 检查是否超过限流
            if len(self._request_times) >= self.config.rate_limit_requests:
                sleep_time = self._request_times[0] + self.config.rate_limit_period - now
                if sleep_time > 0:
                    self.logger.warning(f"Rate limit reached, sleeping for {sleep_time:.2f}s")
                    await asyncio.sleep(sleep_time)
            
            # 记录当前请求时间
            self._request_times.append(now)
    
    async def _make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Response:
        """发送HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            Response: HTTP响应
        """
        if not self.client:
            await self.start()
        
        # 限流检查
        await self._check_rate_limit()
        
        start_time = time.time()
        self.stats["requests_sent"] += 1
        
        try:
            response = await self.client.request(method, url, **kwargs)
            
            # 更新统计信息
            response_time = time.time() - start_time
            self.stats["total_response_time"] += response_time
            self.stats["last_request_time"] = datetime.now()
            
            # 检查响应状态
            if response.status_code == 401:
                self.stats["requests_failed"] += 1
                raise APIAuthException("Authentication failed")
            elif response.status_code == 429:
                self.stats["requests_failed"] += 1
                retry_after = response.headers.get("Retry-After")
                raise APIRateLimitException(
                    "Rate limit exceeded",
                    retry_after=int(retry_after) if retry_after else None
                )
            elif response.status_code >= 500:
                self.stats["requests_failed"] += 1
                raise APIServerException(
                    f"Server error: {response.text}",
                    status_code=response.status_code
                )
            elif response.status_code >= 400:
                self.stats["requests_failed"] += 1
                raise APIValidationException(
                    f"Client error: {response.text}",
                    validation_errors=response.json() if response.headers.get("content-type", "").startswith("application/json") else None
                )
            
            self.stats["requests_successful"] += 1
            return response
            
        except httpx.TimeoutException as e:
            self.stats["requests_failed"] += 1
            raise APITimeoutException(f"Request timeout: {e}", timeout=self.config.timeout)
        except httpx.RequestError as e:
            self.stats["requests_failed"] += 1
            raise APIException(f"Request error: {e}")
    
    async def _retry_request(
        self,
        method: str,
        url: str,
        max_retries: Optional[int] = None,
        **kwargs
    ) -> Response:
        """带重试的请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            max_retries: 最大重试次数
            **kwargs: 其他请求参数
            
        Returns:
            Response: HTTP响应
        """
        max_retries = max_retries or self.config.max_retries
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return await self._make_request(method, url, **kwargs)
            except (APITimeoutException, APIServerException, APIException) as e:
                last_exception = e
                if attempt < max_retries:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"Request failed (attempt {attempt + 1}/{max_retries + 1}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"Request failed after {max_retries + 1} attempts: {e}")
            except APIRateLimitException as e:
                if e.retry_after and attempt < max_retries:
                    self.logger.warning(f"Rate limited, waiting {e.retry_after}s before retry")
                    await asyncio.sleep(e.retry_after)
                else:
                    raise
            except APIAuthException:
                # 认证错误不重试
                raise
        
        raise last_exception

    async def crawl_url(
        self,
        url: str,
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """爬取单个URL

        Args:
            url: 目标URL
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 爬取结果
        """
        # 单个URL转换为批量格式
        return await self.crawl_batch([url], platform, options)

    async def crawl_batch(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """批量爬取URL

        Args:
            urls: URL列表
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 批量爬取任务信息
        """
        endpoint_url = self.config.get_batch_crawl_url()

        # 创建符合新API格式的请求载荷
        payload = self._create_new_api_payload(urls, platform, options)

        response = await self._retry_request(
            "POST",
            endpoint_url,
            json=payload
        )

        return response.json()

    def _create_worker_api_payload(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建符合Worker后端期望的API请求格式"""

        # 基础浏览器配置
        browser_config = {
            "headless": True,
            "verbose": False,
            "viewport_width": 1920,
            "viewport_height": 1080,
            "wait_for": 2,
            "timeout": 30,
            "ignore_https_errors": True,
            "extra_args": [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation"
            ]
        }

        # 基础爬虫配置 - 符合标准API格式
        crawler_config = {
            "method": "arun_many",
            "verbose": True,
            "check_robots_txt": False,
            "fetch_ssl_certificate": False,
            "simulate_user": True,
            "magic": True,
            "override_navigator": True,
            "remove_overlay_elements": True,
            "ignore_body_visibility": True,
            "adjust_viewport_to_content": True,
            "wait_until": "domcontentloaded",
            "wait_for_images": False,
            "page_timeout": 60000,
            "delay_before_return_html": 0.1,
            "js_only": False,
            "scan_full_page": True,
            "process_iframes": True,
            "scroll_delay": 0.2,
            "cache_mode": "BYPASS",
            "screenshot": False,
            "pdf": False,
            "capture_mhtml": False,
            "exclude_external_images": False,
            "exclude_all_images": False,
            "image_score_threshold": 50,
            "image_description_min_word_threshold": 50,
            "table_score_threshold": 7,
            "capture_network_requests": False,
            "capture_console_messages": False,
            "log_console": False,
            "extraction_strategy": "LLMExtractionStrategy",
            "chunking_strategy": "IdentityChunking",
            "markdown_generator": "DefaultMarkdownGenerator",
            "bypass_cache": True,
            "semaphore_count": 2,
            "stream": True,
            "mean_delay": 0.1,
            "max_range": 3,
            "task_type": "batch",
            "scheduler": {
                "type": "AsyncQueueManager",
                "pool_size": 10,
                "memory_threshold": 4096
            },
            "monitor": {
                "display_mode": "detailed",
                "show_progress": True,
                "log_errors": True
            },
            # 添加LLM提取配置
            "llm_extraction": {
                "query": "提取页面中所有文章的标题、作者和发布时间",
                "provider": "openai",
                "model": "deepseek-v3-0324",
                "api_key": "sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi",
                "temperature": 0,
                "max_tokens": 16384,
                "top_p": 0.9,
                "base_url": "https://api.lkeap.cloud.tencent.com/v1"
            },
            # 添加Schema提取配置
            "schema_extraction": {
                "schema": self._get_default_schema(platform),
                "instructions": self._get_default_instructions(platform),
                "validate_schema": True,
                "return_raw": False
            },
            # 添加内容处理配置
            "content_processing": {
                "word_count_threshold": 200,
                "css_selector": "",
                "target_elements": [],
                "excluded_tags": ["nav", "footer", "aside"],
                "excluded_selector": "",
                "remove_forms": False,
                "only_text": False,
                "prettify": False,
                "parser_type": "lxml",
                "keep_data_attributes": False,
                "keep_attrs": []
            },
            # 添加链接过滤配置
            "link_filtering": {
                "exclude_external_links": False,
                "exclude_internal_links": False,
                "exclude_social_media_links": False,
                "exclude_domains": ["example.com", "ads.google.com", "facebook.com", "twitter.com"],
                "social_media_domains": ["facebook.com", "twitter.com", "instagram.com", "linkedin.com", "youtube.com", "tiktok.com", "pinterest.com", "reddit.com"],
                "exclude_external_images": False,
                "exclude_all_images": False,
                "image_score_threshold": 3,
                "image_description_min_word_threshold": 50,
                "table_score_threshold": 7
            }
        }

        # 如果有options，合并配置
        if options:
            # 合并浏览器配置
            if "browser_config" in options:
                browser_config.update(options["browser_config"])

            # 合并爬虫配置
            if "crawler_config" in options:
                crawler_config.update(options["crawler_config"])

        # 构建完整的请求payload
        payload = {
            "urls": urls,
            "browser_config": browser_config,
            "crawler_config": crawler_config
        }

        return payload

    def _create_new_api_payload(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建符合新API格式的请求载荷（已废弃，建议使用CrawlerRequestBuilder）"""

        # 为了向后兼容，保留此方法，但建议使用CrawlerRequestBuilder
        import warnings
        warnings.warn(
            "_create_new_api_payload is deprecated. Use CrawlerRequestBuilder.build_crawler_request instead.",
            DeprecationWarning,
            stacklevel=2
        )

        # 从options中获取配置，或使用默认值
        if not options:
            options = {}

        # 构建request部分
        request_data = {
            "urls": urls,
            "q": options.get("query", options.get("q", "")),  # 查询参数
            "schema_str": options.get("schema_str", ""),  # 数据提取模式
            "cache": options.get("cache", False),  # 缓存设置
            "priority": options.get("priority", "medium"),  # 优先级
            "batch_name": options.get("batch_name", f"batch_{int(time.time())}"),  # 批次名称
            "batch_description": options.get("batch_description", f"Batch crawl for {len(urls)} URLs"),  # 批次描述
            "callback_url": options.get("callback_url", ""),  # 回调URL
            "max_concurrent_tasks": options.get("max_concurrent_tasks", 2),  # 最大并发任务数
            "batch_timeout": options.get("batch_timeout", 300)  # 批次超时时间(秒)
        }

        # 构建auth_context部分
        auth_context = {
            "user_id": options.get("user_id", "monit_system"),
            "role": options.get("role", "user"),
            "permissions": options.get("permissions", []),
            "auth_method": options.get("auth_method", "api_key"),
            "api_key_prefix": options.get("api_key_prefix", ""),
            "client_ip": options.get("client_ip", "127.0.0.1"),
            "user_agent": options.get("user_agent", "MonIt-Crawler/1.0"),
            "authenticated_at": datetime.now().isoformat()
        }

        # 构建完整的请求载荷
        payload = {
            "request": request_data,
            "auth_context": auth_context
        }

        return payload

    def _get_default_schema(self, platform: str) -> Dict[str, Any]:
        """获取默认的Schema配置"""
        if platform == "mercadolibre":
            return {
                "type": "object",
                "description": "电商商品完整信息提取结构",
                "properties": {
                    "task_info": {
                        "type": "object",
                        "description": "任务执行信息",
                        "properties": {
                            "start_timestamp": {
                                "type": "string",
                                "pattern": "^[0-9]{14}$",
                                "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
                            }
                        },
                        "required": ["start_timestamp"]
                    },
                    "product_basic_info": {
                        "type": "object",
                        "description": "商品基础信息",
                        "properties": {
                            "product_url": {"type": "string", "format": "uri", "description": "商品链接"},
                            "product_name": {"type": "string", "description": "商品名称"},
                            "mlm_id": {"type": "number", "description": "商品MLM-ID中的数值部分"}
                        },
                        "required": ["product_url", "product_name", "mlm_id"]
                    },
                    "pricing_info": {
                        "type": "object",
                        "description": "价格和库存信息",
                        "properties": {
                            "current_price": {"type": "number", "description": "商品现价（数值）"},
                            "original_price": {"type": "number", "description": "商品原价（数值）"},
                            "sales_count": {"type": "number", "description": "商品销量"},
                            "stock_quantity": {"type": "number", "description": "商品库存数量"}
                        },
                        "required": ["current_price"]
                    },
                    "category_info": {
                        "type": "object",
                        "description": "商品目录分类信息",
                        "properties": {
                            "category_breadcrumb": {"type": "string", "description": "商品各级目录文本"}
                        },
                        "required": ["category_breadcrumb"]
                    },
                    "seller_info": {
                        "type": "object",
                        "description": "销售商信息",
                        "properties": {
                            "seller_name": {"type": "string", "description": "商品销售商名称"}
                        },
                        "required": ["seller_name"]
                    },
                    "media_info": {
                        "type": "object",
                        "description": "商品媒体信息",
                        "properties": {
                            "main_image_url": {"type": "string", "format": "uri", "description": "商品第一张图片链接"}
                        },
                        "required": ["main_image_url"]
                    },
                    "rating_info": {
                        "type": "object",
                        "description": "评分信息",
                        "properties": {
                            "rating_score": {"type": "number", "minimum": 0, "maximum": 5, "description": "商品评分数（0-5分）"},
                            "rating_count": {"type": "integer", "description": "商品评分数量"}
                        },
                        "required": ["rating_score", "rating_count"]
                    }
                },
                "required": ["product_basic_info", "category_info", "pricing_info", "media_info", "rating_info"]
            }
        else:
            # 其他平台的默认schema
            return {
                "type": "object",
                "description": "通用商品信息提取结构",
                "properties": {
                    "title": {"type": "string", "description": "商品标题"},
                    "price": {"type": "string", "description": "商品价格"},
                    "description": {"type": "string", "description": "商品描述"}
                },
                "required": ["title"]
            }

    def _get_default_instructions(self, platform: str) -> str:
        """获取默认的提取指令"""
        if platform == "mercadolibre":
            return """请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量、商品原价、商品现价、商品折扣率、商品库存数量；
获取商品的各级目录文本；
商品销售商名称；
商品第一张图片链接；
商品评分数、商品评分数量；
任务开始的时间戳YYYYMMDDHHMMSS"""
        else:
            return "提取页面中的主要商品信息，包括标题、价格、描述等关键字段。"

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        endpoint_url = self.config.get_status_url(task_id)

        response = await self._retry_request("GET", endpoint_url)
        return response.json()

    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务结果
        """
        endpoint_url = self.config.get_result_url(task_id)

        response = await self._retry_request("GET", endpoint_url)
        return response.json()

    async def wait_for_task_completion(
        self,
        task_id: str,
        timeout: Optional[float] = None,
        poll_interval: float = 2.0
    ) -> Dict[str, Any]:
        """等待任务完成

        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            poll_interval: 轮询间隔（秒）

        Returns:
            Dict[str, Any]: 任务结果
        """
        start_time = time.time()
        timeout = timeout or self.config.timeout * 10  # 默认为请求超时的10倍

        while True:
            status_info = await self.get_task_status(task_id)
            status = status_info.get("status")

            if status in ["completed", "success"]:
                return await self.get_task_result(task_id)
            elif status in ["failed", "error"]:
                error_msg = status_info.get("error", "Task failed")
                raise APIException(f"Task {task_id} failed: {error_msg}")
            elif status == "cancelled":
                raise APIException(f"Task {task_id} was cancelled")

            # 检查超时
            if time.time() - start_time > timeout:
                raise APITimeoutException(f"Task {task_id} timeout after {timeout}s")

            await asyncio.sleep(poll_interval)

    def get_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()

        # 计算平均响应时间
        if stats["requests_successful"] > 0:
            stats["avg_response_time"] = stats["total_response_time"] / stats["requests_successful"]
        else:
            stats["avg_response_time"] = 0.0

        # 计算成功率
        total_requests = stats["requests_successful"] + stats["requests_failed"]
        if total_requests > 0:
            stats["success_rate"] = stats["requests_successful"] / total_requests
        else:
            stats["success_rate"] = 0.0

        return stats

    async def health_check(self) -> bool:
        """健康检查

        Returns:
            bool: 服务是否健康
        """
        try:
            health_url = self.config.get_endpoint_url("health")
            response = await self._make_request("GET", health_url)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
