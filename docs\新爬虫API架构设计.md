# MonIt 新爬虫API架构设计

## 概述

基于外部爬虫API (http://localhost:11238) 重新设计MonIt系统的爬虫相关功能，实现更加标准化、模块化和可扩展的架构。

## 架构组件

### 1. CrawlerAPIService (爬虫API服务)
**文件**: `backend/app/services/crawler_api_service.py`

**功能**:
- 封装对外部爬虫API的HTTP调用
- 提供异步客户端管理
- 支持认证和限流控制
- 统计和监控API调用

**主要方法**:
```python
async def create_batch_tasks(request: BatchTaskRequest) -> Dict[str, Any]
async def create_llm_job(request: LlmJobRequest) -> Dict[str, Any]
async def get_task_status(task_id: str) -> Dict[str, Any]
async def get_task_result(task_id: str) -> Dict[str, Any]
async def get_batch_tasks(batch_id: str) -> Dict[str, Any]
async def get_system_status() -> Dict[str, Any]
```

### 2. CrawlerConfigService (爬虫配置服务)
**文件**: `backend/app/api/v1/crawler_config.py`

**功能**:
- 管理爬虫任务配置
- 支持多平台配置模板
- 配置验证和测试
- Redis持久化存储

**配置类型**:
- `BATCH_TASK`: 批量任务配置
- `SINGLE_TASK`: 单个任务配置  
- `TEMPLATE`: 配置模板

**支持平台**:
- `MERCADOLIBRE`: MercadoLibre平台
- `AMAZON`: Amazon平台
- `ALIBABA_1688`: 1688平台
- `GENERAL`: 通用平台

### 3. CrawlerTaskExecutor (任务执行器)
**文件**: `backend/app/services/crawler_task_executor.py`

**功能**:
- 执行监控任务的爬虫作业
- 支持批量和单个执行模式
- 执行历史和状态跟踪
- 与MonIt监控任务系统集成

**执行模式**:
- `BATCH`: 批量执行（推荐）
- `SINGLE`: 单个执行
- `STREAMING`: 流式执行（预留）

## API端点

### 配置管理 (`/api/v1/crawler/configs/`)

| 方法 | 端点 | 功能 |
|------|------|------|
| GET | `/configs/` | 获取配置列表 |
| POST | `/configs/` | 创建配置 |
| GET | `/configs/{id}` | 获取单个配置 |
| PUT | `/configs/{id}` | 更新配置 |
| DELETE | `/configs/{id}` | 删除配置 |
| POST | `/configs/{id}/validate` | 验证配置 |
| POST | `/configs/{id}/test` | 测试配置 |
| GET | `/platforms/` | 获取支持平台 |
| GET | `/templates/` | 获取配置模板 |

### 任务执行 (`/api/v1/crawler/`)

| 方法 | 端点 | 功能 |
|------|------|------|
| POST | `/execute` | 执行单个任务 |
| POST | `/execute/batch` | 批量执行任务 |
| GET | `/executions/{id}` | 获取执行状态 |
| GET | `/tasks/{id}/executions` | 获取任务执行历史 |
| POST | `/executions/{id}/cancel` | 取消执行 |
| GET | `/stats` | 获取执行统计 |
| POST | `/test` | 测试API连接 |
| GET | `/health` | 健康检查 |

## 数据流程

### 1. 配置创建流程
```
用户创建配置 → 验证配置参数 → 存储到Redis → 返回配置ID
```

### 2. 任务执行流程
```
监控任务触发 → 获取爬虫配置 → 构建API请求 → 提交到外部API → 记录执行状态 → 返回执行结果
```

### 3. 状态跟踪流程
```
执行记录存储 → 定期状态更新 → 结果获取 → 状态同步到监控任务
```

## Redis存储结构

### 配置存储
```
crawler_configs:{config_id} - Hash: 配置详情
crawler_configs:index - Set: 所有配置ID
crawler_configs:by_platform:{platform} - Set: 按平台分组的配置ID
crawler_configs:by_type:{type} - Set: 按类型分组的配置ID
```

### 执行记录存储
```
crawler_executions:{execution_id} - Hash: 执行记录详情
monitoring_tasks:executions:{task_id} - List: 任务执行历史
```

## 配置示例

### 基本配置
```json
{
  "name": "MercadoLibre商品信息",
  "description": "提取MercadoLibre商品详情",
  "platform": "mercadolibre",
  "llm_query": "提取商品名称、价格、评分、库存状态",
  "llm_schema": "{\"name\": \"string\", \"price\": \"number\", \"rating\": \"number\", \"stock\": \"string\"}",
  "priority": "medium",
  "cache_enabled": true,
  "max_concurrent_tasks": 10,
  "batch_timeout": 300
}
```

### 执行请求
```json
{
  "monitoring_task_id": "task_001",
  "urls": ["https://example.com/product1", "https://example.com/product2"],
  "config_id": "config_123",
  "execution_mode": "batch",
  "priority": "high",
  "metadata": {"source": "scheduled_task"}
}
```

## 集成点

### 与MonIt系统集成
1. **监控任务系统**: 通过`monitoring_task_id`关联
2. **URL池系统**: 提供待爬取的URL列表
3. **任务调度系统**: 定时触发爬虫任务执行
4. **结果存储系统**: 存储爬取结果到TimescaleDB

### 与外部爬虫API集成
1. **认证机制**: 支持API Key和Bearer Token
2. **请求格式**: 遵循外部API的请求规范
3. **状态映射**: 将外部API状态映射到MonIt状态
4. **错误处理**: 统一的错误处理和重试机制

## 优势特点

### 1. 标准化设计
- 基于外部API标准接口
- 统一的请求/响应格式
- 标准化的错误处理

### 2. 模块化架构
- 清晰的职责分离
- 可独立测试和部署
- 易于扩展和维护

### 3. 配置驱动
- 灵活的配置管理
- 支持多平台模板
- 动态配置更新

### 4. 可观测性
- 完整的执行历史
- 详细的统计信息
- 实时状态监控

### 5. 高可用性
- 异步处理机制
- 错误恢复能力
- 连接池管理

## 部署和配置

### 环境变量
```bash
CRAWLER_API_BASE_URL=http://localhost:11238
CRAWLER_API_KEY=your_api_key
REDIS_URL=redis://localhost:6379/0
```

### 依赖服务
- 外部爬虫API服务 (端口11238)
- Redis服务器
- MonIt后端服务

## 测试和验证

运行测试脚本验证功能:
```bash
python test_new_crawler_api.py
```

测试覆盖:
- API服务连接测试
- 配置管理功能测试
- 任务执行功能测试
- 集成测试

## 未来扩展

### 1. 支持更多平台
- 添加新的平台类型
- 扩展配置模板
- 优化平台特定逻辑

### 2. 增强监控能力
- 实时性能监控
- 告警机制
- 自动故障恢复

### 3. 优化执行策略
- 智能负载均衡
- 动态优先级调整
- 资源使用优化

这个新的爬虫API架构为MonIt系统提供了更加强大、灵活和可维护的爬虫功能基础。
