#!/usr/bin/env python3
"""
测试带有time字段的验证API

验证修复后的前端数据格式是否能正确验证
"""

import requests
import json

def test_validate_with_time():
    """测试带有time字段的验证API"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试带有time字段的验证API")
    print("=" * 50)
    
    try:
        # 测试1: 使用time字段的daily类型
        print("\n📋 测试1: daily类型使用time字段")
        validate_data = {
            "name": "前端测试任务",
            "url_ids": ["url_123", "url_456"],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "timezone": "Asia/Shanghai"
            }
        }
        
        print(f"  发送数据:")
        print(json.dumps(validate_data, indent=2, ensure_ascii=False))
        
        response = requests.post(f"{base_url}/api/v1/tasks/validate", json=validate_data, timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("  ✅ 验证成功")
            print(f"  响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print("  ❌ 验证失败")
            print(f"  错误: {response.text}")
        
        # 测试2: 使用start_time字段的其他类型
        print("\n📋 测试2: weekly类型使用start_time字段")
        validate_data2 = {
            "name": "前端测试任务2",
            "url_ids": ["url_123", "url_456"],
            "schedule": {
                "type": "weekly",
                "enabled": True,
                "start_time": "2025-07-13T09:00:00+08:00",
                "timezone": "Asia/Shanghai"
            }
        }
        
        print(f"  发送数据:")
        print(json.dumps(validate_data2, indent=2, ensure_ascii=False))
        
        response2 = requests.post(f"{base_url}/api/v1/tasks/validate", json=validate_data2, timeout=10)
        print(f"  状态码: {response2.status_code}")
        
        if response2.status_code == 200:
            data2 = response2.json()
            print("  ✅ 验证成功")
            print(f"  响应: {json.dumps(data2, indent=2, ensure_ascii=False)}")
        else:
            print("  ❌ 验证失败")
            print(f"  错误: {response2.text}")
        
        # 测试3: 混合字段测试
        print("\n📋 测试3: 同时包含time和start_time字段")
        validate_data3 = {
            "name": "前端测试任务3",
            "url_ids": ["url_123", "url_456"],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "start_time": "2025-07-13T09:00:00+08:00",
                "timezone": "Asia/Shanghai"
            }
        }
        
        print(f"  发送数据:")
        print(json.dumps(validate_data3, indent=2, ensure_ascii=False))
        
        response3 = requests.post(f"{base_url}/api/v1/tasks/validate", json=validate_data3, timeout=10)
        print(f"  状态码: {response3.status_code}")
        
        if response3.status_code == 200:
            data3 = response3.json()
            print("  ✅ 验证成功")
            print(f"  响应: {json.dumps(data3, indent=2, ensure_ascii=False)}")
        else:
            print("  ❌ 验证失败")
            print(f"  错误: {response3.text}")
        
        print("\n🎉 验证API测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_validate_with_time()
