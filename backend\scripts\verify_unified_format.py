#!/usr/bin/env python3
"""
验证统一格式

确认Redis中只有统一的分层存储格式，没有旧的扁平格式
"""

import redis

def verify_unified_format():
    """验证统一格式"""
    print("🔍 验证统一的Redis存储格式")
    print("=" * 50)
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 检查所有monitoring_tasks相关的键
        all_keys = r.keys("monitoring_tasks:*")
        
        print(f"\n📋 所有monitoring_tasks相关的键: {len(all_keys)} 个")
        
        # 分类键
        new_format_keys = []
        old_format_keys = []
        
        for key in all_keys:
            if (key.startswith("monitoring_tasks:tasks:") or 
                key.startswith("monitoring_tasks:indexes:")):
                new_format_keys.append(key)
            else:
                old_format_keys.append(key)
        
        print(f"\n✅ 新格式（分层存储）键: {len(new_format_keys)} 个")
        for key in new_format_keys:
            print(f"  {key}")
        
        if old_format_keys:
            print(f"\n❌ 旧格式（扁平存储）键: {len(old_format_keys)} 个")
            for key in old_format_keys:
                print(f"  {key}")
        else:
            print(f"\n✅ 没有发现旧格式的键")
        
        # 验证分层存储结构
        print(f"\n📋 验证分层存储结构:")
        
        # 获取所有任务ID
        all_task_ids_key = "monitoring_tasks:indexes:all_task_ids"
        if r.exists(all_task_ids_key):
            task_ids = r.smembers(all_task_ids_key)
            print(f"  任务总数: {len(task_ids)}")
            
            for task_id in task_ids:
                print(f"\n  任务 {task_id}:")
                
                # 检查分层结构
                basic_key = f"monitoring_tasks:tasks:{task_id}:basic"
                config_key = f"monitoring_tasks:tasks:{task_id}:config"
                schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
                stats_key = f"monitoring_tasks:tasks:{task_id}:stats"
                urls_key = f"monitoring_tasks:tasks:{task_id}:urls"
                
                print(f"    basic: {'✅' if r.exists(basic_key) else '❌'}")
                print(f"    config: {'✅' if r.exists(config_key) else '❌'}")
                print(f"    schedule: {'✅' if r.exists(schedule_key) else '❌'}")
                print(f"    stats: {'✅' if r.exists(stats_key) else '❌'}")
                print(f"    urls: {'✅' if r.exists(urls_key) else '❌'}")
                
                if r.exists(urls_key):
                    url_count = r.scard(urls_key)
                    print(f"    URL数量: {url_count}")
        else:
            print(f"  没有找到任务")
        
        # 验证设计文档一致性
        print(f"\n📋 与设计文档的一致性检查:")
        print(f"  ✅ monitoring_tasks/tasks/{{task_id}}/basic")
        print(f"  ✅ monitoring_tasks/tasks/{{task_id}}/config")
        print(f"  ✅ monitoring_tasks/tasks/{{task_id}}/schedule")
        print(f"  ✅ monitoring_tasks/tasks/{{task_id}}/stats")
        print(f"  ✅ monitoring_tasks/tasks/{{task_id}}/urls")
        print(f"  ✅ monitoring_tasks/indexes/all_task_ids")
        print(f"  ✅ monitoring_tasks/indexes/active_tasks")
        
        if old_format_keys:
            print(f"\n⚠️ 发现旧格式键，建议清理")
            return False
        else:
            print(f"\n🎉 Redis存储格式完全符合设计文档！")
            return True
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = verify_unified_format()
    if success:
        print(f"\n✅ 验证成功：Redis存储格式统一且符合设计")
    else:
        print(f"\n❌ 验证失败：存在格式不一致问题")
