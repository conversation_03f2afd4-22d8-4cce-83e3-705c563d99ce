/**
 * 新爬虫管理页面
 * 基于重新设计的爬虫API架构
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  message,
  Popconfirm,
  Tooltip,
  Select,
  Input,
  Row,
  Col,
  Statistic,
  Badge,
  // Typography
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { newCrawlerApi, CrawlerTaskConfig, PlatformType, CrawlerConfigType } from '../../services/newCrawlerApi';
import CrawlerConfigForm from './components/CrawlerConfigForm';
import './index.css';

// const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

interface CrawlerManagementProps {}

const CrawlerManagement: React.FC<CrawlerManagementProps> = () => {
  // 状态管理
  const [configs, setConfigs] = useState<CrawlerTaskConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  
  // 筛选状态
  interface FilterState {
    platform?: PlatformType;
    config_type?: CrawlerConfigType;
    is_active?: boolean;
    search: string;
  }

  const [filters, setFilters] = useState<FilterState>({
    platform: undefined,
    config_type: undefined,
    is_active: undefined,
    search: ''
  });
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  
  // 模态框状态
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<CrawlerTaskConfig | null>(null);
  
  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    templates: 0
  });

  // 加载配置列表
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const response = await newCrawlerApi.getConfigs({
        platform: filters.platform,
        config_type: filters.config_type,
        is_active: filters.is_active,
        page: pagination.current,
        page_size: pagination.pageSize
      });
      
      setConfigs(response.configs);
      setPagination(prev => ({
        ...prev,
        total: response.total
      }));
      
      // 更新统计数据
      setStats({
        total: response.total,
        active: response.configs.filter(c => c.is_active).length,
        inactive: response.configs.filter(c => !c.is_active).length,
        templates: response.configs.filter(c => c.config_type === 'template' as CrawlerConfigType).length
      });
      
    } catch (error) {
      message.error(`加载配置失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadConfigs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize, filters]);

  // 表格列定义
  const columns: ColumnsType<CrawlerTaskConfig> = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
              {record.description}
            </div>
          )}
        </div>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 100,
      render: (platform: PlatformType) => {
        const platformColors: Record<PlatformType, string> = {
          mercadolibre: 'blue',
          amazon: 'orange',
          '1688': 'green',
          general: 'default'
        };
        return <Tag color={platformColors[platform]}>{platform.toUpperCase()}</Tag>;
      }
    },
    {
      title: '类型',
      dataIndex: 'config_type',
      key: 'config_type',
      width: 100,
      render: (type: CrawlerConfigType) => {
        const typeColors: Record<CrawlerConfigType, string> = {
          batch_task: 'processing',
          single_task: 'default',
          template: 'warning'
        };
        return <Tag color={typeColors[type]}>{type.replace('_', ' ').toUpperCase()}</Tag>;
      }
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => {
        const priorityColors = {
          high: 'red',
          medium: 'orange',
          low: 'green'
        };
        return <Tag color={priorityColors[priority as keyof typeof priorityColors]}>{priority.toUpperCase()}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Badge 
          status={isActive ? 'success' : 'default'} 
          text={isActive ? '激活' : '禁用'} 
        />
      )
    },
    {
      title: '使用统计',
      key: 'usage',
      width: 120,
      render: (_, record) => (
        <div>
          <div>使用次数: {record.usage_count}</div>
          {record.last_used_at && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              最后使用: {new Date(record.last_used_at).toLocaleDateString()}
            </div>
          )}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑配置">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>

          <Tooltip title="验证配置">
            <Button
              type="text"
              icon={<CheckCircleOutlined />}
              onClick={() => handleValidate(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除配置">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 事件处理函数
  const handleCreate = () => {
    setEditingConfig(null);
    setConfigModalVisible(true);
  };

  const handleEdit = (config: CrawlerTaskConfig) => {
    setEditingConfig(config);
    setConfigModalVisible(true);
  };



  const handleValidate = async (config: CrawlerTaskConfig) => {
    try {
      // 只验证配置本身的合法性，不检查API连接
      const validationResults = [];

      // 检查基本配置
      if (!config.name || config.name.trim() === '') {
        validationResults.push('❌ 配置名称不能为空');
      } else {
        validationResults.push('✅ 配置名称有效');
      }

      if (!config.platform) {
        validationResults.push('❌ 目标平台未设置');
      } else {
        validationResults.push(`✅ 目标平台: ${config.platform}`);
      }

      // 检查LLM查询
      if (!config.llm_query || config.llm_query.trim() === '') {
        validationResults.push('❌ LLM查询指令为空');
      } else {
        validationResults.push('✅ LLM查询指令已设置');
      }

      // 检查JSON Schema
      if (config.llm_schema) {
        try {
          JSON.parse(config.llm_schema);
          validationResults.push('✅ JSON Schema格式有效');
        } catch {
          validationResults.push('❌ JSON Schema格式无效');
        }
      } else {
        validationResults.push('⚠️ JSON Schema未设置（可选）');
      }

      // 检查执行参数
      const maxConcurrent = config.max_concurrent_tasks || 2;
      const batchTimeout = config.batch_timeout || 300;

      if (maxConcurrent > 0 && maxConcurrent <= 50) {
        validationResults.push(`✅ 并发任务数: ${maxConcurrent}`);
      } else {
        validationResults.push('❌ 并发任务数超出范围(1-50)');
      }

      if (batchTimeout >= 60 && batchTimeout <= 86400) {
        validationResults.push(`✅ 批次超时: ${batchTimeout}秒`);
      } else {
        validationResults.push('❌ 批次超时超出范围(60-86400秒)');
      }

      // 检查配置类型
      const validTypes = ['batch_task', 'single_task', 'template'];
      if (validTypes.includes(config.config_type)) {
        validationResults.push(`✅ 配置类型: ${config.config_type}`);
      } else {
        validationResults.push('❌ 配置类型无效');
      }

      // 检查优先级
      const validPriorities = ['high', 'medium', 'low'];
      if (validPriorities.includes(config.priority)) {
        validationResults.push(`✅ 优先级: ${config.priority}`);
      } else {
        validationResults.push('❌ 优先级设置无效');
      }

      const hasErrors = validationResults.some(result => result.startsWith('❌'));

      Modal.info({
        title: hasErrors ? '配置验证失败' : '配置验证通过',
        content: (
          <div>
            <p><strong>配置: {config.name}</strong></p>
            <div style={{ marginTop: 12 }}>
              {validationResults.map((result, index) => (
                <p key={index} style={{ margin: '4px 0' }}>{result}</p>
              ))}
            </div>
          </div>
        ),
        width: 500
      });
    } catch (error) {
      message.error(`配置验证失败: ${error}`);
    }
  };

  const handleDelete = async (configId: string) => {
    try {
      await newCrawlerApi.deleteConfig(configId);
      message.success('配置删除成功');
      loadConfigs();
    } catch (error) {
      message.error(`删除配置失败: ${error}`);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的配置');
      return;
    }
    
    Modal.confirm({
      title: '批量删除确认',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个配置吗？`,
      onOk: async () => {
        try {
          await Promise.all(selectedRowKeys.map(id => newCrawlerApi.deleteConfig(id)));
          message.success('批量删除成功');
          setSelectedRowKeys([]);
          loadConfigs();
        } catch (error) {
          message.error(`批量删除失败: ${error}`);
        }
      }
    });
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleTableChange = (paginationInfo: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
  };

  return (
    <div className="crawler-management">
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总配置数" value={stats.total} prefix={<SettingOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="激活配置" value={stats.active} prefix={<CheckCircleOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="禁用配置" value={stats.inactive} prefix={<ExclamationCircleOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="配置模板" value={stats.templates} prefix={<SettingOutlined />} />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card>
        {/* 工具栏 */}
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Space>
                <Search
                  placeholder="搜索配置名称或描述"
                  style={{ width: 250 }}
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  onSearch={() => loadConfigs()}
                />
                
                <Select
                  placeholder="选择平台"
                  style={{ width: 120 }}
                  value={filters.platform}
                  onChange={(value) => handleFilterChange('platform', value)}
                  allowClear
                >
                  <Option value="mercadolibre">MercadoLibre</Option>
                  <Option value="amazon">Amazon</Option>
                  <Option value="1688">1688</Option>
                  <Option value="general">通用</Option>
                </Select>
                
                <Select
                  placeholder="配置类型"
                  style={{ width: 120 }}
                  value={filters.config_type}
                  onChange={(value) => handleFilterChange('config_type', value)}
                  allowClear
                >
                  <Option value="batch_task">批量任务</Option>
                  <Option value="single_task">单个任务</Option>
                  <Option value="template">模板</Option>
                </Select>
                
                <Select
                  placeholder="状态"
                  style={{ width: 100 }}
                  value={filters.is_active}
                  onChange={(value) => handleFilterChange('is_active', value)}
                  allowClear
                >
                  <Option value={true}>激活</Option>
                  <Option value={false}>禁用</Option>
                </Select>
              </Space>
            </Col>
            
            <Col>
              <Space>
                {selectedRowKeys.length > 0 && (
                  <Button danger onClick={handleBatchDelete}>
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                )}
                
                <Button icon={<ReloadOutlined />} onClick={loadConfigs} loading={loading}>
                  刷新
                </Button>
                
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建配置
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 配置表格 */}
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as string[]),
            type: 'checkbox'
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 配置表单模态框 */}
      <Modal
        title={editingConfig ? '编辑爬虫配置' : '新建爬虫配置'}
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: '1200px' }}
        destroyOnHidden
      >
        <CrawlerConfigForm
          config={editingConfig}
          onSave={async () => {
            setConfigModalVisible(false);
            loadConfigs();
          }}
          onCancel={() => setConfigModalVisible(false)}
        />
      </Modal>


    </div>
  );
};

export default CrawlerManagement;
