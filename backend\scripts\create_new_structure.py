#!/usr/bin/env python3
"""
创建新的Redis存储结构

手动创建分层存储和索引系统
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis


async def create_new_structure():
    """创建新的存储结构"""
    try:
        # 连接Redis
        redis_client = redis.from_url("redis://localhost:6379/0")
        await redis_client.ping()
        
        print("🏗️ 创建新的Redis存储结构")
        print("=" * 50)
        
        # 创建基础索引结构
        print("📋 创建基础索引...")
        
        # 使用SET命令创建空字符串值来标记索引存在
        await redis_client.set('monitoring_tasks:indexes:all_task_ids:_meta', 'empty_set')
        await redis_client.set('monitoring_tasks:indexes:active_tasks:_meta', 'empty_set')
        
        print("  ✅ 基础索引创建完成")
        
        # 创建状态索引
        print("📊 创建状态索引...")
        status_list = ['active', 'paused', 'completed', 'draft', 'failed']
        for status in status_list:
            key = f'monitoring_tasks:indexes:by_status:{status}:_meta'
            await redis_client.set(key, 'empty_set')
        
        print(f"  ✅ 状态索引创建完成 ({len(status_list)}个)")
        
        # 创建平台索引
        print("🌐 创建平台索引...")
        platform_list = ['mercadolibre', 'amazon', 'taobao', 'ebay']
        for platform in platform_list:
            key = f'monitoring_tasks:indexes:by_platform:{platform}:_meta'
            await redis_client.set(key, 'empty_set')
        
        print(f"  ✅ 平台索引创建完成 ({len(platform_list)}个)")
        
        # 创建结构说明文档
        structure_doc = {
            "version": "2.0",
            "created": "2025-07-13",
            "description": "分层存储结构",
            "structure": {
                "tasks": "monitoring_tasks:tasks:{task_id}:{layer}",
                "indexes": "monitoring_tasks:indexes:{type}",
                "url_tasks": "monitoring_tasks:url_tasks:{url_id}"
            },
            "layers": ["basic", "config", "schedule", "stats", "urls"],
            "index_types": ["all_task_ids", "active_tasks", "by_status:{status}", "by_platform:{platform}"]
        }
        
        import json
        await redis_client.set('monitoring_tasks:_structure_info', json.dumps(structure_doc))
        print("  ✅ 结构文档创建完成")
        
        # 验证创建结果
        print("\n🔍 验证创建结果...")
        all_keys = await redis_client.keys("monitoring_tasks:*")
        print(f"  📊 创建的键数量: {len(all_keys)}")
        
        for key in all_keys:
            key_str = key.decode() if isinstance(key, bytes) else key
            print(f"    - {key_str}")
        
        await redis_client.close()
        
        print("\n🎉 新存储结构创建完成!")
        return True
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False


async def main():
    """主函数"""
    success = await create_new_structure()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
