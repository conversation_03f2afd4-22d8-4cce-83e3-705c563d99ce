"""
Worker动态分配服务
实现智能的Worker-任务匹配算法，支持基于Worker性能、当前负载、任务复杂度的动态分配策略
"""

import asyncio
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import redis.asyncio as redis
import json

from app.schemas.crawler_worker import CrawlerWorker
from app.schemas.monitoring_task import MonitoringTask


class AllocationStrategy(Enum):
    """分配策略枚举"""
    PERFORMANCE_BASED = "performance_based"  # 基于性能分配
    LOAD_BALANCED = "load_balanced"         # 负载均衡分配
    COST_OPTIMIZED = "cost_optimized"       # 成本优化分配
    HYBRID = "hybrid"                       # 混合策略


@dataclass
class TaskComplexity:
    """任务复杂度评估"""
    url_count: int
    platform_difficulty: float  # 1.0-5.0
    historical_avg_time: float  # 秒
    data_extraction_complexity: float  # 1.0-5.0
    total_score: float = 0.0
    
    def __post_init__(self):
        # 计算总复杂度分数
        self.total_score = (
            (self.url_count / 100) * 0.3 +  # URL数量权重30%
            self.platform_difficulty * 0.25 +  # 平台难度权重25%
            (self.historical_avg_time / 60) * 0.25 +  # 历史时间权重25%
            self.data_extraction_complexity * 0.2  # 提取复杂度权重20%
        )


@dataclass
class WorkerCapability:
    """Worker能力评估"""
    worker_id: str
    max_concurrent: int
    current_load: int  # 当前正在处理的任务数
    performance_score: float  # 性能评分 1.0-10.0
    success_rate: float  # 成功率 0.0-1.0
    avg_response_time: float  # 平均响应时间（秒）
    platform_expertise: Dict[str, float]  # 平台专业度 {platform: score}
    availability_score: float = 0.0
    
    def __post_init__(self):
        # 计算可用性评分
        load_ratio = self.current_load / max(self.max_concurrent, 1)
        self.availability_score = max(0.0, 1.0 - load_ratio)


@dataclass
class WorkerAssignment:
    """Worker分配结果"""
    worker_id: str
    allocated_urls: List[str]
    estimated_completion_time: float
    priority_score: float
    assignment_reason: str


class WorkerAllocationService:
    """Worker动态分配服务"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.redis_client = redis.from_url(redis_url)
        self.logger = logging.getLogger(__name__)
        
        # 平台难度系数
        self.platform_difficulty = {
            "taobao": 4.5,
            "tmall": 4.0,
            "jd": 3.5,
            "amazon": 3.0,
            "mercadolibre": 2.5,
            "ebay": 2.0
        }
    
    async def allocate_workers(
        self,
        task: MonitoringTask,
        active_urls: List[str],
        strategy: AllocationStrategy = AllocationStrategy.HYBRID
    ) -> List[WorkerAssignment]:
        """
        为任务分配Worker
        
        Args:
            task: 监控任务
            active_urls: 活跃URL列表
            strategy: 分配策略
            
        Returns:
            Worker分配结果列表
        """
        try:
            # 1. 评估任务复杂度
            complexity = await self._evaluate_task_complexity(task, active_urls)
            self.logger.info(f"Task {task.task_id} complexity score: {complexity.total_score}")
            
            # 2. 获取可用Worker列表
            available_workers = await self._get_available_workers()
            if not available_workers:
                raise ValueError("No available workers found")
            
            # 3. 评估Worker能力
            worker_capabilities = []
            for worker in available_workers:
                capability = await self._evaluate_worker_capability(worker, task.config.platform)
                worker_capabilities.append(capability)
            
            # 4. 根据策略分配Worker
            assignments = await self._assign_workers(
                active_urls, worker_capabilities, complexity, strategy
            )
            
            # 5. 记录分配结果
            await self._record_allocation(task.task_id, assignments)
            
            self.logger.info(f"Allocated {len(assignments)} workers for task {task.task_id}")
            return assignments
            
        except Exception as e:
            self.logger.error(f"Failed to allocate workers for task {task.task_id}: {e}")
            raise
    
    async def _evaluate_task_complexity(
        self, 
        task: MonitoringTask, 
        active_urls: List[str]
    ) -> TaskComplexity:
        """评估任务复杂度"""
        
        # 获取平台难度
        platform_difficulty = self.platform_difficulty.get(task.config.platform, 3.0)
        
        # 获取历史平均执行时间
        historical_avg_time = await self._get_historical_avg_time(task.config.platform)
        
        # 评估数据提取复杂度（基于schema复杂度）
        extraction_complexity = await self._evaluate_extraction_complexity(task)
        
        return TaskComplexity(
            url_count=len(active_urls),
            platform_difficulty=platform_difficulty,
            historical_avg_time=historical_avg_time,
            data_extraction_complexity=extraction_complexity
        )
    
    async def _get_available_workers(self) -> List[CrawlerWorker]:
        """获取可用Worker列表"""
        try:
            # 从Redis获取所有Worker
            worker_keys = await self.redis_client.keys("crawler_workers:*")
            workers = []
            
            for key in worker_keys:
                worker_data = await self.redis_client.hgetall(key)
                if worker_data and worker_data.get(b'status') == b'active':
                    # 解析Worker数据
                    worker_dict = {}
                    for k, v in worker_data.items():
                        key_str = k.decode() if isinstance(k, bytes) else k
                        value_str = v.decode() if isinstance(v, bytes) else v
                        worker_dict[key_str] = value_str
                    
                    # 创建Worker对象
                    worker = CrawlerWorker(**worker_dict)
                    workers.append(worker)
            
            return workers
            
        except Exception as e:
            self.logger.error(f"Failed to get available workers: {e}")
            return []
    
    async def _evaluate_worker_capability(
        self, 
        worker: CrawlerWorker, 
        platform: str
    ) -> WorkerCapability:
        """评估Worker能力"""
        
        # 获取Worker当前负载
        current_load = await self._get_worker_current_load(worker.worker_id)
        
        # 获取Worker性能统计
        performance_stats = await self._get_worker_performance_stats(worker.worker_id)
        
        # 计算平台专业度
        platform_expertise = await self._get_platform_expertise(worker.worker_id, platform)
        
        return WorkerCapability(
            worker_id=worker.worker_id,
            max_concurrent=worker.max_concurrent_tasks,
            current_load=current_load,
            performance_score=performance_stats.get('performance_score', 5.0),
            success_rate=performance_stats.get('success_rate', 0.8),
            avg_response_time=performance_stats.get('avg_response_time', 30.0),
            platform_expertise={platform: platform_expertise}
        )
    
    async def _assign_workers(
        self,
        active_urls: List[str],
        worker_capabilities: List[WorkerCapability],
        complexity: TaskComplexity,
        strategy: AllocationStrategy
    ) -> List[WorkerAssignment]:
        """分配Worker"""
        
        if strategy == AllocationStrategy.PERFORMANCE_BASED:
            return await self._performance_based_assignment(active_urls, worker_capabilities, complexity)
        elif strategy == AllocationStrategy.LOAD_BALANCED:
            return await self._load_balanced_assignment(active_urls, worker_capabilities, complexity)
        elif strategy == AllocationStrategy.COST_OPTIMIZED:
            return await self._cost_optimized_assignment(active_urls, worker_capabilities, complexity)
        else:  # HYBRID
            return await self._hybrid_assignment(active_urls, worker_capabilities, complexity)
    
    async def _performance_based_assignment(
        self,
        active_urls: List[str],
        worker_capabilities: List[WorkerCapability],
        complexity: TaskComplexity
    ) -> List[WorkerAssignment]:
        """基于性能的分配策略"""
        
        # 按性能评分排序
        sorted_workers = sorted(
            worker_capabilities,
            key=lambda w: w.performance_score * w.success_rate * w.availability_score,
            reverse=True
        )
        
        assignments = []
        remaining_urls = active_urls.copy()
        
        for worker in sorted_workers:
            if not remaining_urls:
                break
            
            # 计算该Worker可分配的URL数量
            available_capacity = worker.max_concurrent - worker.current_load
            if available_capacity <= 0:
                continue
            
            # 分配URL
            allocated_count = min(available_capacity, len(remaining_urls))
            allocated_urls = remaining_urls[:allocated_count]
            remaining_urls = remaining_urls[allocated_count:]
            
            # 估算完成时间
            estimated_time = self._estimate_completion_time(
                allocated_urls, worker, complexity
            )
            
            assignment = WorkerAssignment(
                worker_id=worker.worker_id,
                allocated_urls=allocated_urls,
                estimated_completion_time=estimated_time,
                priority_score=worker.performance_score,
                assignment_reason=f"High performance worker (score: {worker.performance_score:.2f})"
            )
            assignments.append(assignment)
        
        return assignments
    
    async def _load_balanced_assignment(
        self,
        active_urls: List[str],
        worker_capabilities: List[WorkerCapability],
        complexity: TaskComplexity
    ) -> List[WorkerAssignment]:
        """负载均衡分配策略"""

        # 按可用性排序（负载最低的优先）
        sorted_workers = sorted(
            worker_capabilities,
            key=lambda w: w.current_load / max(w.max_concurrent, 1)
        )

        assignments = []
        remaining_urls = active_urls.copy()

        # 轮询分配，确保负载均衡
        while remaining_urls and sorted_workers:
            for worker in sorted_workers:
                if not remaining_urls:
                    break

                available_capacity = worker.max_concurrent - worker.current_load
                if available_capacity <= 0:
                    continue

                # 每次分配少量URL以保持均衡
                batch_size = min(max(1, available_capacity // 4), len(remaining_urls))
                allocated_urls = remaining_urls[:batch_size]
                remaining_urls = remaining_urls[batch_size:]

                # 更新Worker负载
                worker.current_load += len(allocated_urls)

                # 查找现有分配或创建新分配
                existing_assignment = next(
                    (a for a in assignments if a.worker_id == worker.worker_id), None
                )

                if existing_assignment:
                    existing_assignment.allocated_urls.extend(allocated_urls)
                    existing_assignment.estimated_completion_time = self._estimate_completion_time(
                        existing_assignment.allocated_urls, worker, complexity
                    )
                else:
                    estimated_time = self._estimate_completion_time(allocated_urls, worker, complexity)
                    assignment = WorkerAssignment(
                        worker_id=worker.worker_id,
                        allocated_urls=allocated_urls,
                        estimated_completion_time=estimated_time,
                        priority_score=worker.availability_score,
                        assignment_reason=f"Load balanced assignment (load: {worker.current_load}/{worker.max_concurrent})"
                    )
                    assignments.append(assignment)

        return assignments

    async def _cost_optimized_assignment(
        self,
        active_urls: List[str],
        worker_capabilities: List[WorkerCapability],
        complexity: TaskComplexity
    ) -> List[WorkerAssignment]:
        """成本优化分配策略"""

        # 计算每个Worker的成本效益比
        for worker in worker_capabilities:
            # 成本效益 = 性能 / (响应时间 * 复杂度因子)
            cost_efficiency = worker.performance_score / (worker.avg_response_time * complexity.total_score)
            worker.cost_efficiency = cost_efficiency

        # 按成本效益排序
        sorted_workers = sorted(
            worker_capabilities,
            key=lambda w: getattr(w, 'cost_efficiency', 0),
            reverse=True
        )

        assignments = []
        remaining_urls = active_urls.copy()

        for worker in sorted_workers:
            if not remaining_urls:
                break

            available_capacity = worker.max_concurrent - worker.current_load
            if available_capacity <= 0:
                continue

            # 优先给成本效益高的Worker分配更多任务
            allocated_count = min(available_capacity, len(remaining_urls))
            allocated_urls = remaining_urls[:allocated_count]
            remaining_urls = remaining_urls[allocated_count:]

            estimated_time = self._estimate_completion_time(allocated_urls, worker, complexity)

            assignment = WorkerAssignment(
                worker_id=worker.worker_id,
                allocated_urls=allocated_urls,
                estimated_completion_time=estimated_time,
                priority_score=getattr(worker, 'cost_efficiency', 0),
                assignment_reason=f"Cost optimized (efficiency: {getattr(worker, 'cost_efficiency', 0):.2f})"
            )
            assignments.append(assignment)

        return assignments

    async def _hybrid_assignment(
        self,
        active_urls: List[str],
        worker_capabilities: List[WorkerCapability],
        complexity: TaskComplexity
    ) -> List[WorkerAssignment]:
        """混合分配策略"""

        # 综合评分：性能 * 可用性 * 平台专业度
        for worker in worker_capabilities:
            platform_score = list(worker.platform_expertise.values())[0] if worker.platform_expertise else 1.0
            worker.priority_score = (
                worker.performance_score * 0.4 +
                worker.availability_score * 10 * 0.3 +  # 转换为10分制
                platform_score * 0.3
            )

        # 按综合评分排序
        sorted_workers = sorted(
            worker_capabilities,
            key=lambda w: w.priority_score,
            reverse=True
        )

        return await self._performance_based_assignment(active_urls, sorted_workers, complexity)
    
    def _estimate_completion_time(
        self,
        urls: List[str],
        worker: WorkerCapability,
        complexity: TaskComplexity
    ) -> float:
        """估算完成时间"""
        
        # 基础时间 = URL数量 * 平均响应时间
        base_time = len(urls) * worker.avg_response_time
        
        # 复杂度调整
        complexity_factor = 1.0 + (complexity.total_score - 1.0) * 0.5
        
        # 并发调整
        concurrent_factor = 1.0 / max(worker.max_concurrent - worker.current_load, 1)
        
        return base_time * complexity_factor * concurrent_factor
    
    async def _get_historical_avg_time(self, platform: str) -> float:
        """获取平台历史平均执行时间"""
        try:
            stats_key = f"platform_stats:{platform}"
            avg_time = await self.redis_client.hget(stats_key, "avg_execution_time")
            return float(avg_time) if avg_time else 30.0
        except:
            return 30.0  # 默认30秒
    
    async def _evaluate_extraction_complexity(self, task: MonitoringTask) -> float:
        """评估数据提取复杂度"""
        # 简化实现，后续可以基于schema复杂度进行评估
        return 2.5  # 默认中等复杂度
    
    async def _get_worker_current_load(self, worker_id: str) -> int:
        """获取Worker当前负载"""
        try:
            load_key = f"worker_load:{worker_id}"
            load = await self.redis_client.get(load_key)
            return int(load) if load else 0
        except:
            return 0
    
    async def _get_worker_performance_stats(self, worker_id: str) -> Dict[str, float]:
        """获取Worker性能统计"""
        try:
            stats_key = f"worker_stats:{worker_id}"
            stats = await self.redis_client.hgetall(stats_key)
            
            result = {}
            for k, v in stats.items():
                key_str = k.decode() if isinstance(k, bytes) else k
                value_str = v.decode() if isinstance(v, bytes) else v
                result[key_str] = float(value_str)
            
            return result
        except:
            return {
                'performance_score': 5.0,
                'success_rate': 0.8,
                'avg_response_time': 30.0
            }
    
    async def _get_platform_expertise(self, worker_id: str, platform: str) -> float:
        """获取Worker平台专业度"""
        try:
            expertise_key = f"worker_expertise:{worker_id}:{platform}"
            expertise = await self.redis_client.get(expertise_key)
            return float(expertise) if expertise else 1.0
        except:
            return 1.0  # 默认专业度
    
    async def _record_allocation(self, task_id: str, assignments: List[WorkerAssignment]):
        """记录分配结果"""
        try:
            allocation_key = f"task_allocation:{task_id}"
            allocation_data = {
                "task_id": task_id,
                "assignments": [
                    {
                        "worker_id": a.worker_id,
                        "url_count": len(a.allocated_urls),
                        "estimated_time": a.estimated_completion_time,
                        "priority_score": a.priority_score,
                        "reason": a.assignment_reason
                    }
                    for a in assignments
                ],
                "allocated_at": datetime.now().isoformat()
            }
            
            await self.redis_client.set(
                allocation_key,
                json.dumps(allocation_data),
                ex=86400  # 24小时过期
            )
            
        except Exception as e:
            self.logger.error(f"Failed to record allocation: {e}")
    
    async def close(self):
        """关闭Redis连接"""
        await self.redis_client.close()
