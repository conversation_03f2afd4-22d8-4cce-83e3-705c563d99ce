import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Button,
  Space,
  Tag
} from 'antd';
import {
  ReloadOutlined,
  TeamOutlined,
  BarsOutlined,
  ClockCircleOutlined,
  DatabaseOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

interface PerformanceMetrics {
  workers: {
    online: number;
    total: number;
    load_avg: number[];
  };
  tasks: {
    processed_per_minute: number;
    failed_per_minute: number;
    avg_processing_time: number;
    queue_lengths: { [key: string]: number };
  };
  system: {
    memory_usage: number;
    cpu_usage: number;
    redis_memory: string;
    uptime: string;
  };
}

const PerformancePanel: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取性能指标
  const fetchMetrics = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/celery/performance');
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      } else {
        // 如果API不存在，使用模拟数据
        setMetrics({
          workers: {
            online: 2,
            total: 2,
            load_avg: [0.5, 0.6, 0.4]
          },
          tasks: {
            processed_per_minute: 15,
            failed_per_minute: 1,
            avg_processing_time: 2.3,
            queue_lengths: {
              'default': 5,
              'monitor_queue': 12,
              'high_priority': 0
            }
          },
          system: {
            memory_usage: 65,
            cpu_usage: 45,
            redis_memory: '128MB',
            uptime: '2 days, 14 hours'
          }
        });
      }
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error);
      // 使用模拟数据作为fallback
      setMetrics({
        workers: {
          online: 1,
          total: 2,
          load_avg: [0.3, 0.4, 0.2]
        },
        tasks: {
          processed_per_minute: 8,
          failed_per_minute: 0,
          avg_processing_time: 1.8,
          queue_lengths: {
            'default': 2,
            'monitor_queue': 8
          }
        },
        system: {
          memory_usage: 55,
          cpu_usage: 35,
          redis_memory: '96MB',
          uptime: '1 day, 8 hours'
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchMetrics();
  }, []);

  // 监听刷新事件
  useEffect(() => {
    const handleRefresh = () => fetchMetrics();
    window.addEventListener('celery-refresh', handleRefresh);
    return () => window.removeEventListener('celery-refresh', handleRefresh);
  }, []);

  // 获取性能状态颜色
  const getPerformanceColor = (value: number, thresholds: [number, number]) => {
    if (value < thresholds[0]) return '#52c41a';
    if (value < thresholds[1]) return '#faad14';
    return '#ff4d4f';
  };

  if (!metrics) {
    return <div>加载中...</div>;
  }

  return (
    <div className="performance-panel">
      {/* 头部控制 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={4} style={{ margin: 0 }}>性能监控</Title>
          <Text type="secondary">系统性能指标和资源使用情况</Text>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchMetrics}
            loading={loading}
          >
            刷新指标
          </Button>
        </Col>
      </Row>

      {/* Worker性能 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="在线Worker"
              value={metrics.workers.online}
              suffix={`/ ${metrics.workers.total}`}
              prefix={<TeamOutlined />}
              valueStyle={{ 
                color: metrics.workers.online === metrics.workers.total ? '#52c41a' : '#faad14' 
              }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                可用率: {Math.round((metrics.workers.online / metrics.workers.total) * 100)}%
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="处理速度"
              value={metrics.tasks.processed_per_minute}
              suffix="任务/分钟"
              prefix={<BarsOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                失败率: {metrics.tasks.failed_per_minute} 失败/分钟
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="平均处理时间"
              value={metrics.tasks.avg_processing_time}
              suffix="秒"
              prefix={<ClockCircleOutlined />}
              precision={1}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                性能良好
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 系统资源 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card title="系统资源" size="small">
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>CPU使用率</Text>
                <Text>{metrics.system.cpu_usage}%</Text>
              </div>
              <Progress
                percent={metrics.system.cpu_usage}
                strokeColor={getPerformanceColor(metrics.system.cpu_usage, [70, 85])}
                size="small"
              />
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>内存使用率</Text>
                <Text>{metrics.system.memory_usage}%</Text>
              </div>
              <Progress
                percent={metrics.system.memory_usage}
                strokeColor={getPerformanceColor(metrics.system.memory_usage, [75, 90])}
                size="small"
              />
            </div>
            
            <Row gutter={16}>
              <Col span={12}>
                <Text type="secondary" style={{ fontSize: 12 }}>Redis内存:</Text>
                <div>{metrics.system.redis_memory}</div>
              </Col>
              <Col span={12}>
                <Text type="secondary" style={{ fontSize: 12 }}>运行时间:</Text>
                <div>{metrics.system.uptime}</div>
              </Col>
            </Row>
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="队列状态" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(metrics.tasks.queue_lengths).map(([queue, length]) => (
                <div key={queue} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>{queue}</Text>
                  <Space>
                    <Tag color={length > 10 ? 'red' : length > 5 ? 'orange' : 'green'}>
                      {length} 任务
                    </Tag>
                  </Space>
                </div>
              ))}
            </Space>
            
            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                总队列长度: {Object.values(metrics.tasks.queue_lengths).reduce((a, b) => a + b, 0)}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Worker负载 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="Worker负载均衡" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">1分钟负载</Text>
                  <div style={{ fontSize: 18, fontWeight: 'bold', color: '#1890ff' }}>
                    {metrics.workers.load_avg[0]?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">5分钟负载</Text>
                  <div style={{ fontSize: 18, fontWeight: 'bold', color: '#52c41a' }}>
                    {metrics.workers.load_avg[1]?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">15分钟负载</Text>
                  <div style={{ fontSize: 18, fontWeight: 'bold', color: '#faad14' }}>
                    {metrics.workers.load_avg[2]?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </Col>
            </Row>
            
            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                负载均衡状态: 
                <Tag color="green" style={{ marginLeft: 8 }}>
                  正常
                </Tag>
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PerformancePanel;
