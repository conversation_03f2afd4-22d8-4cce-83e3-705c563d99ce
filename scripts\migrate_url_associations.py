#!/usr/bin/env python3
"""
URL关联数据迁移脚本

目的：
1. 将替代关联方式 (task_urls:{task_id}) 迁移到标准关联方式 (monitoring_tasks:task_urls:{task_id})
2. 统一URL关联存储格式，便于维护
3. 清理旧的关联数据
4. 验证迁移结果
"""

import redis
import json
import sys
from datetime import datetime
from typing import List, Dict, Set


class UrlAssociationMigrator:
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """初始化Redis连接"""
        self.redis = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db, 
            decode_responses=True
        )
        
    def analyze_current_state(self) -> Dict:
        """分析当前的URL关联状态"""
        print("🔍 分析当前URL关联状态...")
        
        state = {
            'tasks_using_standard': [],      # 使用标准关联的任务
            'tasks_using_alternative': [],   # 使用替代关联的任务
            'tasks_using_both': [],          # 同时使用两种关联的任务
            'orphaned_alternative_keys': [], # 孤立的替代关联键
            'total_tasks': 0,
            'total_standard_associations': 0,
            'total_alternative_associations': 0
        }
        
        # 获取所有任务
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        state['total_tasks'] = len(task_ids)
        
        print(f"   总任务数: {state['total_tasks']}")
        
        for task_id in task_ids:
            standard_key = f'monitoring_tasks:task_urls:{task_id}'
            alternative_key = f'task_urls:{task_id}'
            
            standard_count = self.redis.scard(standard_key)
            alternative_count = self.redis.scard(alternative_key)
            
            state['total_standard_associations'] += standard_count
            state['total_alternative_associations'] += alternative_count
            
            task_data = self.redis.hgetall(f'monitoring_tasks:{task_id}')
            task_name = task_data.get('name', '未知')
            
            if standard_count > 0 and alternative_count > 0:
                state['tasks_using_both'].append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'standard_count': standard_count,
                    'alternative_count': alternative_count
                })
            elif standard_count > 0:
                state['tasks_using_standard'].append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'url_count': standard_count
                })
            elif alternative_count > 0:
                state['tasks_using_alternative'].append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'url_count': alternative_count
                })
        
        # 检查孤立的替代关联键
        all_alternative_keys = self.redis.keys('task_urls:*')
        for key in all_alternative_keys:
            task_id = key.split(':')[-1]
            if not self.redis.exists(f'monitoring_tasks:{task_id}'):
                state['orphaned_alternative_keys'].append(key)
        
        return state
    
    def migrate_alternative_to_standard(self, task_id: str) -> Dict:
        """将替代关联迁移到标准关联"""
        print(f"   🔄 迁移任务 {task_id[:8]}... 的URL关联")
        
        alternative_key = f'task_urls:{task_id}'
        standard_key = f'monitoring_tasks:task_urls:{task_id}'
        
        # 获取替代关联中的URL ID
        url_ids = self.redis.smembers(alternative_key)
        
        if not url_ids:
            return {'migrated_count': 0, 'error': 'No URLs to migrate'}
        
        migrated_count = 0
        errors = []
        
        try:
            # 使用管道操作提高性能
            pipe = self.redis.pipeline()
            
            for url_id in url_ids:
                # 添加到标准关联
                pipe.sadd(standard_key, url_id)
                
                # 更新URL的反向关联
                old_url_tasks_key = f'url_tasks:{url_id}'
                new_url_tasks_key = f'monitoring_tasks:url_tasks:{url_id}'
                
                # 如果URL使用的是旧的反向关联，也需要迁移
                if self.redis.sismember(old_url_tasks_key, task_id):
                    pipe.sadd(new_url_tasks_key, task_id)
                    pipe.srem(old_url_tasks_key, task_id)
                
                migrated_count += 1
            
            # 执行所有操作
            pipe.execute()
            
            print(f"      ✅ 成功迁移 {migrated_count} 个URL关联")
            
        except Exception as e:
            error_msg = f"Migration failed: {e}"
            errors.append(error_msg)
            print(f"      ❌ 迁移失败: {error_msg}")
        
        return {
            'migrated_count': migrated_count,
            'errors': errors
        }
    
    def cleanup_alternative_associations(self, task_ids: List[str]) -> int:
        """清理替代关联数据"""
        print(f"🧹 清理 {len(task_ids)} 个任务的替代关联数据...")
        
        cleaned_count = 0
        
        for task_id in task_ids:
            alternative_key = f'task_urls:{task_id}'
            
            if self.redis.exists(alternative_key):
                self.redis.delete(alternative_key)
                cleaned_count += 1
        
        print(f"   ✅ 清理了 {cleaned_count} 个替代关联键")
        return cleaned_count
    
    def cleanup_orphaned_keys(self, orphaned_keys: List[str]) -> int:
        """清理孤立的关联键"""
        if not orphaned_keys:
            return 0
            
        print(f"🧹 清理 {len(orphaned_keys)} 个孤立的关联键...")
        
        cleaned_count = 0
        for key in orphaned_keys:
            if self.redis.exists(key):
                self.redis.delete(key)
                cleaned_count += 1
        
        print(f"   ✅ 清理了 {cleaned_count} 个孤立键")
        return cleaned_count
    
    def verify_migration(self) -> Dict:
        """验证迁移结果"""
        print("✅ 验证迁移结果...")
        
        results = {
            'total_tasks': 0,
            'tasks_using_standard_only': 0,
            'tasks_using_alternative_only': 0,
            'tasks_using_both': 0,
            'remaining_alternative_keys': 0,
            'data_consistency_issues': []
        }
        
        # 检查所有任务
        task_ids = self.redis.smembers('monitoring_tasks:all_ids')
        results['total_tasks'] = len(task_ids)
        
        for task_id in task_ids:
            standard_key = f'monitoring_tasks:task_urls:{task_id}'
            alternative_key = f'task_urls:{task_id}'
            
            standard_count = self.redis.scard(standard_key)
            alternative_count = self.redis.scard(alternative_key)
            
            if standard_count > 0 and alternative_count > 0:
                results['tasks_using_both'] += 1
                results['data_consistency_issues'].append({
                    'task_id': task_id,
                    'issue': 'Still using both association methods',
                    'standard_count': standard_count,
                    'alternative_count': alternative_count
                })
            elif standard_count > 0:
                results['tasks_using_standard_only'] += 1
            elif alternative_count > 0:
                results['tasks_using_alternative_only'] += 1
                results['data_consistency_issues'].append({
                    'task_id': task_id,
                    'issue': 'Still using alternative association method',
                    'alternative_count': alternative_count
                })
        
        # 检查剩余的替代关联键
        remaining_keys = self.redis.keys('task_urls:*')
        results['remaining_alternative_keys'] = len(remaining_keys)
        
        return results
    
    def run_migration(self) -> Dict:
        """执行完整的迁移流程"""
        print("🚀 开始URL关联数据迁移...")
        print("=" * 60)
        
        # 1. 分析当前状态
        current_state = self.analyze_current_state()
        
        print(f"\n📊 当前状态分析:")
        print(f"   - 使用标准关联的任务: {len(current_state['tasks_using_standard'])}")
        print(f"   - 使用替代关联的任务: {len(current_state['tasks_using_alternative'])}")
        print(f"   - 同时使用两种关联的任务: {len(current_state['tasks_using_both'])}")
        print(f"   - 孤立的替代关联键: {len(current_state['orphaned_alternative_keys'])}")
        print(f"   - 标准关联总数: {current_state['total_standard_associations']}")
        print(f"   - 替代关联总数: {current_state['total_alternative_associations']}")
        
        if (len(current_state['tasks_using_alternative']) == 0 and 
            len(current_state['tasks_using_both']) == 0 and 
            len(current_state['orphaned_alternative_keys']) == 0):
            print(f"\n✅ 所有任务已使用标准关联方式，无需迁移！")
            return current_state
        
        print(f"\n🔧 开始迁移...")
        
        # 2. 迁移使用替代关联的任务
        migration_results = []
        tasks_to_migrate = (current_state['tasks_using_alternative'] + 
                           current_state['tasks_using_both'])
        
        for task_info in tasks_to_migrate:
            task_id = task_info['task_id']
            result = self.migrate_alternative_to_standard(task_id)
            migration_results.append({
                'task_id': task_id,
                'task_name': task_info['task_name'],
                **result
            })
        
        # 3. 清理替代关联数据
        if tasks_to_migrate:
            task_ids_to_clean = [t['task_id'] for t in tasks_to_migrate]
            cleaned_count = self.cleanup_alternative_associations(task_ids_to_clean)
        
        # 4. 清理孤立的关联键
        orphaned_cleaned = self.cleanup_orphaned_keys(current_state['orphaned_alternative_keys'])
        
        # 5. 验证迁移结果
        print(f"\n" + "=" * 60)
        verification_results = self.verify_migration()
        
        print(f"📊 迁移结果:")
        print(f"   - 总任务数: {verification_results['total_tasks']}")
        print(f"   - 仅使用标准关联: {verification_results['tasks_using_standard_only']}")
        print(f"   - 仅使用替代关联: {verification_results['tasks_using_alternative_only']}")
        print(f"   - 同时使用两种关联: {verification_results['tasks_using_both']}")
        print(f"   - 剩余替代关联键: {verification_results['remaining_alternative_keys']}")
        
        if verification_results['data_consistency_issues']:
            print(f"   ⚠️  发现 {len(verification_results['data_consistency_issues'])} 个数据一致性问题")
            for issue in verification_results['data_consistency_issues']:
                print(f"      - 任务 {issue['task_id'][:8]}...: {issue['issue']}")
        
        success = (verification_results['tasks_using_alternative_only'] == 0 and 
                  verification_results['tasks_using_both'] == 0 and
                  verification_results['remaining_alternative_keys'] == 0)
        
        if success:
            print(f"\n🎉 迁移完成！所有任务现在都使用标准关联方式。")
        else:
            print(f"\n⚠️  迁移部分完成，可能需要进一步检查。")
        
        return {
            'current_state': current_state,
            'migration_results': migration_results,
            'verification_results': verification_results,
            'success': success
        }


if __name__ == "__main__":
    migrator = UrlAssociationMigrator()
    results = migrator.run_migration()
