#!/usr/bin/env python3
"""
Task 03-03 测试验证脚本

验证基础反爬虫策略实现的功能完整性。
"""

import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_task03_03():
    """测试Task 03-03的所有组件"""
    
    print("=" * 60)
    print("🧪 Task 03-03: 基础反爬虫策略实现 - 测试开始")
    print("=" * 60)
    
    try:
        # 导入所有模块
        from backend.crawler.anti_detection.user_agent_manager import UserAgentManager
        from backend.crawler.anti_detection.header_manager import RequestHeaderManager
        from backend.crawler.anti_detection.behavior_simulator import BehaviorSimulator
        from backend.crawler.anti_detection.session_manager import SessionManager
        from backend.crawler.anti_detection.fingerprint_faker import FingerprintFaker
        from backend.crawler.anti_detection.mercadolibre_anti_detection import MercadoLibreAntiDetection
        
        print("✅ 模块导入成功")
        
        # 测试1: 用户代理管理器
        print("\n📱 测试用户代理管理器...")
        ua_manager = UserAgentManager()
        ua_string, ua_info = ua_manager.get_random_ua(device_type="desktop")
        print(f"   随机UA: {ua_string[:50]}...")
        print(f"   浏览器: {ua_info.browser}, 设备类型: {ua_info.device_type}")
        
        # 测试2: 请求头管理器
        print("\n🌐 测试请求头管理器...")
        header_manager = RequestHeaderManager()
        headers = header_manager.generate_headers(ua_string, browser=ua_info.browser)
        print(f"   生成请求头数量: {len(headers)}")
        print(f"   包含User-Agent: {'User-Agent' in headers}")
        print(f"   包含Accept: {'Accept' in headers}")
        
        # 测试ML特定请求头
        ml_headers = header_manager.get_mercadolibre_headers(ua_string, country_code="AR")
        print(f"   ML特定头部数量: {len(ml_headers)}")
        print(f"   包含X-Client-ID: {'X-Client-ID' in ml_headers}")
        
        # 测试3: 行为模拟器
        print("\n🎭 测试行为模拟器...")
        behavior_simulator = BehaviorSimulator()
        
        async def test_behavior():
            # 测试随机延迟
            start_time = datetime.now()
            delay = await behavior_simulator.random_delay(0.1, 0.2)
            end_time = datetime.now()
            actual_delay = (end_time - start_time).total_seconds()
            print(f"   随机延迟: {delay:.3f}秒 (实际: {actual_delay:.3f}秒)")
            
            # 测试阅读模拟
            reading_time = await behavior_simulator.simulate_reading(1000)
            print(f"   阅读模拟时间: {reading_time:.3f}秒")
            
            # 测试访问模式
            pattern = behavior_simulator.get_visit_pattern()
            print(f"   访问模式: {pattern}")
            
            return True
        
        # 运行异步测试
        result = asyncio.run(test_behavior())
        if result:
            print("   ✅ 行为模拟器测试通过")
        
        # 测试4: 会话管理器
        print("\n🍪 测试会话管理器...")
        session_manager = SessionManager()
        
        # 创建普通会话
        session = session_manager.create_session()
        print(f"   创建会话ID: {session.session_id[:8]}...")
        
        # 创建ML会话
        ml_session = session_manager.create_session(platform="mercadolibre")
        print(f"   ML会话Cookie数量: {len(ml_session.cookies)}")
        
        # 测试添加Cookie
        cookie_data = {
            'name': 'test_cookie',
            'value': 'test_value',
            'domain': '.mercadolibre.com.ar'
        }
        session_manager.add_cookie(ml_session.session_id, cookie_data)
        cookies = session_manager.get_cookies_for_domain(ml_session.session_id, 'mercadolibre.com.ar')
        print(f"   域Cookie数量: {len(cookies)}")
        
        # 测试5: 指纹伪装器
        print("\n🔍 测试指纹伪装器...")
        fingerprint_faker = FingerprintFaker()
        
        # 生成基础指纹
        profile = fingerprint_faker.generate_fingerprint_profile()
        print(f"   基础指纹字段: {list(profile.keys())}")
        
        # 生成阿根廷地区指纹
        ar_profile = fingerprint_faker.generate_fingerprint_profile(platform="argentina")
        print(f"   阿根廷指纹语言: {ar_profile['locale']['language']}")
        print(f"   阿根廷指纹货币: {ar_profile['locale']['currency']}")
        
        # 验证指纹
        is_valid = fingerprint_faker.validate_fingerprint(ar_profile)
        print(f"   指纹验证结果: {is_valid}")
        
        # 测试6: MercadoLibre反检测
        print("\n🛡️ 测试MercadoLibre反检测...")
        ml_anti_detection = MercadoLibreAntiDetection()
        
        # 获取ML头部
        ml_headers = ml_anti_detection.get_headers(country_code="AR")
        print(f"   ML头部数量: {len(ml_headers)}")
        print(f"   客户端ID: {ml_headers.get('X-Client-ID')}")
        print(f"   国家代码: {ml_headers.get('X-Country')}")
        
        # 测试生成New Relic ID
        newrelic_id = ml_anti_detection._generate_newrelic_id()
        print(f"   New Relic ID: {newrelic_id}")
        
        # 集成测试
        print("\n🔄 执行集成测试...")
        
        # 模拟完整的反检测流程
        print("   1. 获取随机UA...")
        ua_string, ua_info = ua_manager.get_random_ua(device_type="desktop")
        
        print("   2. 生成伪装请求头...")
        headers = header_manager.generate_headers(ua_string, browser=ua_info.browser)
        
        print("   3. 创建ML会话...")
        session = session_manager.create_session(platform="mercadolibre")
        
        print("   4. 生成地区指纹...")
        fingerprint = fingerprint_faker.generate_fingerprint_profile(platform="argentina")
        
        print("   5. 获取ML特定头部...")
        ml_headers = ml_anti_detection.get_headers(country_code="AR")
        
        print("   6. 合并所有配置...")
        combined_headers = {**headers, **ml_headers}
        
        print(f"   最终请求头数量: {len(combined_headers)}")
        print(f"   会话Cookie数量: {len(session.cookies)}")
        print(f"   指纹配置字段: {len(fingerprint)}")
        
        print("\n" + "=" * 60)
        print("🎉 Task 03-03 测试完成 - 所有功能正常")
        print("=" * 60)
        
        # 显示测试摘要
        print("\n📊 测试摘要:")
        print("   ✅ 用户代理管理器 - 正常")
        print("   ✅ 请求头管理器 - 正常")
        print("   ✅ 行为模拟器 - 正常")
        print("   ✅ 会话管理器 - 正常")
        print("   ✅ 指纹伪装器 - 正常")
        print("   ✅ MercadoLibre反检测 - 正常")
        print("   ✅ 集成测试 - 通过")
        
        # 保存测试日志
        log_file = f"test_task03_03_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"Task 03-03 测试报告\n")
            f.write(f"测试时间: {datetime.now()}\n")
            f.write(f"测试结果: 通过\n\n")
            f.write(f"测试的组件:\n")
            f.write(f"- UserAgentManager: ✅\n")
            f.write(f"- RequestHeaderManager: ✅\n")
            f.write(f"- BehaviorSimulator: ✅\n")
            f.write(f"- SessionManager: ✅\n")
            f.write(f"- FingerprintFaker: ✅\n")
            f.write(f"- MercadoLibreAntiDetection: ✅\n")
            f.write(f"\n测试通过的功能:\n")
            f.write(f"- UA轮换和管理\n")
            f.write(f"- 请求头伪装\n")
            f.write(f"- 行为模拟\n")
            f.write(f"- Cookie和会话管理\n")
            f.write(f"- 浏览器指纹伪装\n")
            f.write(f"- MercadoLibre特定策略\n")
        
        print(f"\n📝 测试报告已保存: {log_file}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_task03_03()
    sys.exit(0 if success else 1) 