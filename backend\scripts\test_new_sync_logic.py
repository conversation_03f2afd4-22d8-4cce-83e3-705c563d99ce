#!/usr/bin/env python3
"""
测试新的前端同步逻辑（充分利用后端能力）
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_new_sync_logic():
    """测试新的同步逻辑"""
    
    print("🧪 测试新的前端同步逻辑（充分利用后端能力）...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 获取后端配置详情
            response = await client.get("http://localhost:8000/api/v1/backend-configs/")
            
            if response.status_code == 200:
                configs = response.json()
                if configs:
                    config_summary = configs[0]
                    backend_id = config_summary['backend_id']
                    
                    # 获取详细配置
                    detail_response = await client.get(f"http://localhost:8000/api/v1/backend-configs/{backend_id}")
                    
                    if detail_response.status_code == 200:
                        config_detail = detail_response.json()
                        print(f"✅ 获取到配置详情: {config_detail['backend_name']}")
                        
                        # 模拟新的前端同步逻辑
                        performance_level = config_detail.get('performance_level', 'medium')
                        perf_config = config_detail.get('performance_config', {})
                        
                        print(f"📋 配置信息:")
                        print(f"   - 性能级别: {performance_level}")
                        print(f"   - 后端最大并发: {perf_config.get('max_concurrent_tasks', 'N/A')}")
                        print(f"   - 内存限制: {perf_config.get('memory_limit_mb', 'N/A')}MB")
                        
                        # 新的同步逻辑：充分利用后端能力
                        backend_max_concurrent = perf_config.get('max_concurrent_tasks', 10)
                        
                        # 基于后端实际能力设置Worker配置
                        max_concurrent = backend_max_concurrent
                        
                        # 根据性能级别调整分配比例
                        if performance_level == 'high':
                            allocation_ratio = 0.9  # 高性能环境，分配90%
                        elif performance_level == 'medium':
                            allocation_ratio = 0.8  # 中等性能环境，分配80%
                        else:  # low
                            allocation_ratio = 0.6  # 低性能环境，分配60%
                        
                        allocated_concurrent = max(1, int(max_concurrent * allocation_ratio))
                        
                        print(f"\n🎯 新的同步计算过程:")
                        print(f"   1. 后端最大并发能力: {backend_max_concurrent}")
                        print(f"   2. 性能级别 '{performance_level}' 分配比例: {allocation_ratio*100}%")
                        print(f"   3. 初始分配结果:")
                        print(f"      - 最大并发: {max_concurrent}")
                        print(f"      - 分配并发: {allocated_concurrent}")
                        
                        # 考虑内存限制（仅在极端情况下调整）
                        memory_limit = perf_config.get('memory_limit_mb')
                        if memory_limit:
                            if memory_limit < 256:
                                # 极低内存环境
                                max_concurrent = max(2, int(max_concurrent * 0.7))
                                allocated_concurrent = max(1, int(max_concurrent * 0.6))
                                print(f"   4. 极低内存环境 ({memory_limit}MB)，调整为:")
                                print(f"      - 最大并发: {max_concurrent}")
                                print(f"      - 分配并发: {allocated_concurrent}")
                            elif memory_limit < 512:
                                # 低内存环境
                                max_concurrent = max(3, int(max_concurrent * 0.8))
                                allocated_concurrent = max(2, int(max_concurrent * 0.7))
                                print(f"   4. 低内存环境 ({memory_limit}MB)，调整为:")
                                print(f"      - 最大并发: {max_concurrent}")
                                print(f"      - 分配并发: {allocated_concurrent}")
                            else:
                                print(f"   4. 充足内存环境 ({memory_limit}MB)，不限制并发能力")
                        else:
                            print(f"   4. 没有内存限制信息，充分利用后端能力")
                        
                        # 最终检查
                        allocated_concurrent = min(allocated_concurrent, max(1, max_concurrent - 1))
                        
                        print(f"\n✅ 新的同步结果:")
                        print(f"   - 最大并发任务数: {max_concurrent}")
                        print(f"   - 分配并发数: {allocated_concurrent}")
                        
                        print(f"\n📝 前端应该设置的表单值:")
                        print(f"   form.setFieldsValue({{")
                        print(f"     max_concurrent_tasks: {max_concurrent},")
                        print(f"     allocated_concurrent: {allocated_concurrent},")
                        print(f"   }});")
                        
                        print(f"\n🔄 对比旧逻辑:")
                        print(f"   - 旧逻辑: 最大并发 5, 分配并发 3 (严重浪费后端能力)")
                        print(f"   - 新逻辑: 最大并发 {max_concurrent}, 分配并发 {allocated_concurrent} (充分利用后端能力)")
                        
                    else:
                        print(f"❌ 获取配置详情失败: {detail_response.status_code}")
                        
                else:
                    print(f"⚠️  没有后端配置数据")
            else:
                print(f"❌ 获取配置列表失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_new_sync_logic()

if __name__ == "__main__":
    asyncio.run(main())
