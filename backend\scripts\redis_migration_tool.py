#!/usr/bin/env python3
"""
Redis存储结构重构迁移工具

执行从扁平化结构到分层结构的完整迁移
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis


class RedisMigrationTool:
    """Redis迁移工具"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self.backup_data = {}
        self.migration_log = []
        
    async def get_redis_client(self):
        """获取Redis客户端"""
        return redis.from_url(self.redis_url)
    
    def log(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        self.migration_log.append(log_entry)
    
    async def backup_current_data(self):
        """备份当前数据"""
        self.log("🔄 开始备份当前Redis数据...")
        
        try:
            redis_client = await self.get_redis_client()
            
            # 获取所有键
            all_keys = await redis_client.keys("*")
            self.log(f"📊 发现 {len(all_keys)} 个键")
            
            # 备份所有数据
            for key in all_keys:
                key_str = key.decode() if isinstance(key, bytes) else key
                key_type = await redis_client.type(key)
                
                if key_type == b'hash':
                    self.backup_data[key_str] = {
                        'type': 'hash',
                        'data': await redis_client.hgetall(key)
                    }
                elif key_type == b'set':
                    self.backup_data[key_str] = {
                        'type': 'set',
                        'data': await redis_client.smembers(key)
                    }
                elif key_type == b'string':
                    self.backup_data[key_str] = {
                        'type': 'string',
                        'data': await redis_client.get(key)
                    }
                elif key_type == b'list':
                    self.backup_data[key_str] = {
                        'type': 'list',
                        'data': await redis_client.lrange(key, 0, -1)
                    }
            
            # 保存备份到文件
            backup_file = f"redis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            backup_path = Path(__file__).parent / backup_file
            
            # 序列化备份数据
            def decode_bytes(obj):
                """递归解码bytes对象"""
                if isinstance(obj, bytes):
                    return obj.decode('utf-8', errors='ignore')
                elif isinstance(obj, dict):
                    return {decode_bytes(k): decode_bytes(v) for k, v in obj.items()}
                elif isinstance(obj, (list, set, tuple)):
                    return [decode_bytes(item) for item in obj]
                else:
                    return obj

            serializable_backup = {}
            for key, value in self.backup_data.items():
                serializable_backup[key] = {
                    'type': value['type'],
                    'data': decode_bytes(value['data'])
                }
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_backup, f, indent=2, ensure_ascii=False)
            
            self.log(f"✅ 备份完成，保存到: {backup_path}")
            await redis_client.close()
            return backup_path
            
        except Exception as e:
            self.log(f"❌ 备份失败: {e}")
            raise
    
    async def clear_redis_data(self):
        """清除Redis数据"""
        self.log("🧹 开始清除Redis数据...")
        
        try:
            redis_client = await self.get_redis_client()
            
            # 获取所有键
            all_keys = await redis_client.keys("*")
            
            if all_keys:
                # 删除所有键
                await redis_client.delete(*all_keys)
                self.log(f"✅ 已删除 {len(all_keys)} 个键")
            else:
                self.log("ℹ️ Redis中没有数据需要清除")
            
            await redis_client.close()
            
        except Exception as e:
            self.log(f"❌ 清除数据失败: {e}")
            raise
    
    async def create_new_structure(self):
        """创建新的存储结构"""
        self.log("🏗️ 开始创建新的存储结构...")
        
        try:
            redis_client = await self.get_redis_client()
            
            # 创建基础索引结构（先创建空集合）
            await redis_client.sadd('monitoring_tasks:indexes:all_task_ids', 'temp')
            await redis_client.srem('monitoring_tasks:indexes:all_task_ids', 'temp')

            await redis_client.sadd('monitoring_tasks:indexes:active_tasks', 'temp')
            await redis_client.srem('monitoring_tasks:indexes:active_tasks', 'temp')

            # 创建状态索引
            status_list = ['active', 'paused', 'completed', 'draft', 'failed']
            for status in status_list:
                key = f'monitoring_tasks:indexes:by_status:{status}'
                await redis_client.sadd(key, 'temp')
                await redis_client.srem(key, 'temp')

            # 创建平台索引
            platform_list = ['mercadolibre', 'amazon', 'taobao', 'ebay']
            for platform in platform_list:
                key = f'monitoring_tasks:indexes:by_platform:{platform}'
                await redis_client.sadd(key, 'temp')
                await redis_client.srem(key, 'temp')
            
            self.log("✅ 新存储结构创建完成")
            await redis_client.close()
            
        except Exception as e:
            self.log(f"❌ 创建新结构失败: {e}")
            raise
    
    async def migrate_existing_data(self):
        """迁移现有数据到新结构"""
        self.log("🔄 开始迁移现有数据...")
        
        try:
            # 从备份数据中提取任务数据
            task_data_found = False
            
            for key, value in self.backup_data.items():
                if key.startswith('monitoring_tasks:') and ':' in key and value['type'] == 'hash':
                    # 这是一个任务数据
                    if key.count(':') == 1:  # monitoring_tasks:{task_id}
                        task_id = key.split(':')[1]
                        if len(task_id) == 36:  # UUID格式
                            await self._migrate_single_task(task_id, value['data'])
                            task_data_found = True
            
            if not task_data_found:
                self.log("ℹ️ 没有发现需要迁移的任务数据")
            
        except Exception as e:
            self.log(f"❌ 数据迁移失败: {e}")
            raise
    
    async def _migrate_single_task(self, task_id: str, task_data: Dict):
        """迁移单个任务数据"""
        self.log(f"📝 迁移任务: {task_id}")
        
        try:
            redis_client = await self.get_redis_client()
            
            # 分类字段
            basic_fields = {}
            config_fields = {}
            schedule_fields = {}
            stats_fields = {}
            
            for field, value in task_data.items():
                field_str = field.decode() if isinstance(field, bytes) else field
                value_str = value.decode() if isinstance(value, bytes) else value
                
                # 分类字段
                if field_str in ['id', 'name', 'description', 'created_at', 'updated_at']:
                    basic_fields[field_str] = value_str
                elif field_str in ['config'] or field_str.startswith('config_'):
                    if field_str == 'config' and value_str.startswith('{'):
                        # 解析JSON配置
                        try:
                            config_json = json.loads(value_str)
                            config_fields.update(config_json)
                        except:
                            config_fields[field_str] = value_str
                    else:
                        config_fields[field_str] = value_str
                elif field_str in ['schedule', 'next_run', 'last_run', 'timezone'] or field_str.startswith('schedule_'):
                    if field_str == 'schedule' and value_str.startswith('{'):
                        # 解析JSON调度配置
                        try:
                            schedule_json = json.loads(value_str)
                            schedule_fields.update(schedule_json)
                            schedule_fields['next_run'] = task_data.get('next_run', '')
                            schedule_fields['last_run'] = task_data.get('last_run', '')
                        except:
                            schedule_fields[field_str] = value_str
                    else:
                        schedule_fields[field_str] = value_str
                else:
                    # 其他字段归类为统计信息
                    stats_fields[field_str] = value_str
            
            # 存储分层数据
            if basic_fields:
                await redis_client.hset(f'monitoring_tasks:tasks:{task_id}:basic', mapping=basic_fields)
            
            if config_fields:
                await redis_client.hset(f'monitoring_tasks:tasks:{task_id}:config', mapping=config_fields)
            
            if schedule_fields:
                await redis_client.hset(f'monitoring_tasks:tasks:{task_id}:schedule', mapping=schedule_fields)
            
            if stats_fields:
                await redis_client.hset(f'monitoring_tasks:tasks:{task_id}:stats', mapping=stats_fields)
            
            # 更新索引
            await redis_client.sadd('monitoring_tasks:indexes:all_task_ids', task_id)
            
            # 状态索引
            status = stats_fields.get('status', 'draft')
            await redis_client.sadd(f'monitoring_tasks:indexes:by_status:{status}', task_id)
            
            # 平台索引
            platform = config_fields.get('platform', 'unknown')
            if platform != 'unknown':
                await redis_client.sadd(f'monitoring_tasks:indexes:by_platform:{platform}', task_id)
            
            # 迁移URL关联
            old_url_key = f'monitoring_tasks:task_urls:{task_id}'
            if old_url_key in self.backup_data:
                url_data = self.backup_data[old_url_key]['data']
                if url_data:
                    # 新的URL存储位置
                    await redis_client.sadd(f'monitoring_tasks:tasks:{task_id}:urls', *url_data)
                    
                    # 创建URL反向关联
                    for url_id in url_data:
                        url_id_str = url_id.decode() if isinstance(url_id, bytes) else url_id
                        await redis_client.sadd(f'monitoring_tasks:url_tasks:{url_id_str}', task_id)
            
            self.log(f"✅ 任务 {task_id} 迁移完成")
            await redis_client.close()
            
        except Exception as e:
            self.log(f"❌ 迁移任务 {task_id} 失败: {e}")
            raise
    
    async def verify_migration(self):
        """验证迁移结果"""
        self.log("🔍 开始验证迁移结果...")
        
        try:
            redis_client = await self.get_redis_client()
            
            # 检查索引
            all_tasks = await redis_client.smembers('monitoring_tasks:indexes:all_task_ids')
            self.log(f"📊 总任务数: {len(all_tasks)}")
            
            # 检查每个任务的分层数据
            for task_id in all_tasks:
                task_id_str = task_id.decode() if isinstance(task_id, bytes) else task_id
                
                basic_exists = await redis_client.exists(f'monitoring_tasks:tasks:{task_id_str}:basic')
                config_exists = await redis_client.exists(f'monitoring_tasks:tasks:{task_id_str}:config')
                schedule_exists = await redis_client.exists(f'monitoring_tasks:tasks:{task_id_str}:schedule')
                stats_exists = await redis_client.exists(f'monitoring_tasks:tasks:{task_id_str}:stats')
                
                self.log(f"  任务 {task_id_str}: basic={basic_exists}, config={config_exists}, schedule={schedule_exists}, stats={stats_exists}")
            
            self.log("✅ 迁移验证完成")
            await redis_client.close()
            
        except Exception as e:
            self.log(f"❌ 验证失败: {e}")
            raise
    
    async def save_migration_report(self):
        """保存迁移报告"""
        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_path = Path(__file__).parent / report_file
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("Redis存储结构重构迁移报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"迁移时间: {datetime.now()}\n\n")
            
            for log_entry in self.migration_log:
                f.write(log_entry + "\n")
        
        self.log(f"📄 迁移报告保存到: {report_path}")
        return report_path


async def main():
    """主函数"""
    print("🚀 Redis存储结构重构迁移工具")
    print("=" * 50)
    
    migrator = RedisMigrationTool()
    
    try:
        # 1. 备份当前数据
        backup_path = await migrator.backup_current_data()
        
        # 2. 清除Redis数据
        await migrator.clear_redis_data()
        
        # 3. 创建新结构
        await migrator.create_new_structure()
        
        # 4. 迁移数据
        await migrator.migrate_existing_data()
        
        # 5. 验证迁移
        await migrator.verify_migration()
        
        # 6. 保存报告
        report_path = await migrator.save_migration_report()
        
        print("\n🎉 迁移完成!")
        print(f"📄 备份文件: {backup_path}")
        print(f"📄 迁移报告: {report_path}")
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
