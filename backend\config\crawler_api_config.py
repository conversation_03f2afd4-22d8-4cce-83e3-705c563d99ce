"""
爬虫API客户端配置

提供爬虫API客户端的配置管理功能。
"""

import os
from typing import Dict, Any, Optional
from backend.crawler.api_client import APIConfig


def get_crawler_api_config() -> APIConfig:
    """获取爬虫API客户端配置
    
    Returns:
        APIConfig: API客户端配置实例
    """
    config_dict = {
        "base_url": os.getenv("CRAWLER_API_BASE_URL", "http://localhost:8080"),
        "api_version": os.getenv("CRAWLER_API_VERSION", "v1"),
        "timeout": float(os.getenv("CRAWLER_API_TIMEOUT", "30.0")),
        "max_retries": int(os.getenv("CRAWLER_API_MAX_RETRIES", "3")),
        "retry_delay": float(os.getenv("CRAWLER_API_RETRY_DELAY", "1.0")),
        "api_key": os.getenv("CRAWLER_API_KEY"),
        "api_secret": os.getenv("CRAWLER_API_SECRET"),
        "auth_type": os.getenv("CRAWLER_API_AUTH_TYPE", "api_key"),
        "verify_ssl": os.getenv("CRAWLER_API_VERIFY_SSL", "true").lower() == "true",
        "rate_limit_requests": int(os.getenv("CRAWLER_API_RATE_LIMIT_REQUESTS", "100")),
        "rate_limit_period": int(os.getenv("CRAWLER_API_RATE_LIMIT_PERIOD", "60")),
        "default_platform": os.getenv("CRAWLER_DEFAULT_PLATFORM", "mercadolibre"),
        "supported_platforms": os.getenv(
            "CRAWLER_SUPPORTED_PLATFORMS", 
            "mercadolibre,amazon,ebay"
        ).split(",")
    }
    
    return APIConfig.from_dict(config_dict)


def get_crawler_engine_config() -> Dict[str, Any]:
    """获取爬虫引擎配置
    
    Returns:
        Dict[str, Any]: 爬虫引擎配置字典
    """
    return {
        "api_client": get_crawler_api_config().to_dict(),
        "max_concurrent_tasks": int(os.getenv("CRAWLER_MAX_CONCURRENT", "10")),
        "scheduler": {
            "max_workers": int(os.getenv("CRAWLER_SCHEDULER_MAX_WORKERS", "5")),
            "queue_size": int(os.getenv("CRAWLER_SCHEDULER_QUEUE_SIZE", "1000"))
        }
    }


# 预定义的平台配置
PLATFORM_CONFIGS = {
    "mercadolibre": {
        "name": "MercadoLibre",
        "domains": ["mercadolibre.com.ar", "mercadolibre.com.mx", "mercadolibre.com.br"],
        "default_options": {
            "wait_for_load": True,
            "extract_images": True,
            "extract_reviews": False
        }
    },
    "amazon": {
        "name": "Amazon",
        "domains": ["amazon.com", "amazon.co.uk", "amazon.de"],
        "default_options": {
            "wait_for_load": True,
            "extract_images": True,
            "extract_reviews": False
        }
    },
    "ebay": {
        "name": "eBay",
        "domains": ["ebay.com", "ebay.co.uk", "ebay.de"],
        "default_options": {
            "wait_for_load": True,
            "extract_images": True,
            "extract_reviews": False
        }
    }
}


def get_platform_config(platform: str) -> Optional[Dict[str, Any]]:
    """获取平台配置
    
    Args:
        platform: 平台名称
        
    Returns:
        Optional[Dict[str, Any]]: 平台配置，如果不存在则返回None
    """
    return PLATFORM_CONFIGS.get(platform.lower())


def get_all_platforms() -> Dict[str, Dict[str, Any]]:
    """获取所有平台配置
    
    Returns:
        Dict[str, Dict[str, Any]]: 所有平台配置
    """
    return PLATFORM_CONFIGS.copy()
