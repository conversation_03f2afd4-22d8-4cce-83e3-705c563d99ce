#!/usr/bin/env python3
"""
测试编辑任务功能集成
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_edit_task_integration():
    """测试编辑任务功能集成"""
    
    async with aiohttp.ClientSession() as session:
        print("✏️ 测试编辑任务功能集成...")
        
        # 1. 获取任务列表
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 测试编辑任务的完整流程
        print(f"\n2. 测试编辑任务完整流程...")
        
        # 2.1 获取任务详情（模拟前端获取数据）
        print(f"   2.1 获取任务详情...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    original_task = data.get('data', {})
                    print(f"   ✅ 原始任务数据:")
                    print(f"      名称: {original_task.get('name')}")
                    print(f"      描述: {original_task.get('description') or '无'}")
                    print(f"      状态: {original_task.get('status')}")
                    print(f"      运行中: {original_task.get('is_running')}")
                else:
                    print(f"   ❌ 获取任务详情失败: {response.status}")
                    return
        except Exception as e:
            print(f"   ❌ 获取任务详情异常: {e}")
            return
        
        # 2.2 模拟前端编辑操作
        print(f"   2.2 模拟前端编辑操作...")
        edit_data = {
            "name": f"{original_task.get('name')} [前端编辑测试]",
            "description": "这是通过前端编辑功能修改的描述信息"
        }
        
        # 如果任务未运行，也更新配置
        if not original_task.get('is_running'):
            edit_data.update({
                "schedule": {
                    "type": "daily",
                    "time": "16:00",
                    "timezone": "Asia/Shanghai"
                },
                "config": {
                    "platform": "jd",
                    "priority": "high",
                    "retry_count": 4,
                    "timeout": 45,
                    "batch_size": 15
                }
            })
            print(f"   ✅ 任务未运行，将更新完整配置")
        else:
            print(f"   ⚠️ 任务正在运行，只更新基本信息")
        
        try:
            async with session.put(
                f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}",
                json=edit_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    updated_task = data.get('data', {})
                    print(f"   ✅ 编辑成功:")
                    print(f"      新名称: {updated_task.get('name')}")
                    print(f"      新描述: {updated_task.get('description')}")
                    if not original_task.get('is_running'):
                        schedule = updated_task.get('schedule', {})
                        config = updated_task.get('config', {})
                        print(f"      新调度: {schedule.get('type')} {schedule.get('time')}")
                        print(f"      新平台: {config.get('platform')}")
                        print(f"      新优先级: {config.get('priority')}")
                else:
                    error_data = await response.json()
                    print(f"   ❌ 编辑失败: {response.status} - {error_data.get('detail')}")
                    return
        except Exception as e:
            print(f"   ❌ 编辑异常: {e}")
            return
        
        # 2.3 验证编辑结果
        print(f"   2.3 验证编辑结果...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    final_task = data.get('data', {})
                    print(f"   ✅ 验证成功:")
                    print(f"      最终名称: {final_task.get('name')}")
                    print(f"      最终描述: {final_task.get('description')}")
                    print(f"      更新时间: {final_task.get('updated_at')}")
                    
                    # 检查是否正确更新
                    name_updated = final_task.get('name') != original_task.get('name')
                    desc_updated = final_task.get('description') != original_task.get('description')
                    
                    if name_updated and desc_updated:
                        print(f"   ✅ 编辑功能正常工作")
                    else:
                        print(f"   ❌ 编辑功能可能有问题")
                        print(f"      名称更新: {name_updated}")
                        print(f"      描述更新: {desc_updated}")
                else:
                    print(f"   ❌ 验证失败: {response.status}")
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
        
        # 3. 测试任务列表刷新
        print(f"\n3. 测试任务列表刷新...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    updated_task_in_list = None
                    for task in tasks:
                        if task['id'] == task_id:
                            updated_task_in_list = task
                            break
                    
                    if updated_task_in_list:
                        print(f"✅ 任务列表中的更新数据:")
                        print(f"   名称: {updated_task_in_list.get('name')}")
                        print(f"   描述: {updated_task_in_list.get('description')}")
                        print(f"   更新时间: {updated_task_in_list.get('updated_at')}")
                    else:
                        print(f"❌ 在任务列表中未找到更新的任务")
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
        except Exception as e:
            print(f"❌ 任务列表刷新测试异常: {e}")
        
        print("\n🎉 编辑任务功能集成测试完成!")
        print("\n📋 测试总结:")
        print("   ✅ 后端更新API - 正常工作")
        print("   ✅ 数据获取和预填充 - 已验证")
        print("   ✅ 编辑操作和保存 - 已验证")
        print("   ✅ 任务列表刷新 - 已验证")
        print("   ✅ 运行中任务限制 - 已考虑")
        print("\n🌐 前端测试:")
        print(f"   请访问: http://localhost:3000/monitoring")
        print(f"   点击任务的编辑按钮测试前端功能")

if __name__ == "__main__":
    asyncio.run(test_edit_task_integration())
