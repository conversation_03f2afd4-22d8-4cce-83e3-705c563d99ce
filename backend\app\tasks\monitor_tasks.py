"""
监控任务

系统监控、指标收集和健康检查任务
"""

import asyncio
import logging
import psutil
import time
from typing import Dict, Any, List
from datetime import datetime, timedelta
from celery import Task
import redis.asyncio as redis

from app.celery_app import celery_app
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.log import SystemLog
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class MonitorTask(Task):
    """监控任务基类"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """失败时的回调"""
        logger.error(f"Monitor task {task_id} failed: {exc}")


@celery_app.task(
    bind=True,
    base=MonitorTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30}
)
def collect_system_metrics(self) -> Dict[str, Any]:
    """收集系统指标"""
    return asyncio.run(_collect_system_metrics_async())


async def _collect_system_metrics_async() -> Dict[str, Any]:
    """异步收集系统指标"""
    
    try:
        # 收集系统资源指标
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 收集网络指标
        network = psutil.net_io_counters()
        
        # 收集进程指标
        process_count = len(psutil.pids())
        
        # 收集数据库指标
        db_metrics = await _collect_database_metrics()
        
        # 收集Redis指标
        redis_metrics = await _collect_redis_metrics()
        
        # 收集Celery指标
        celery_metrics = await _collect_celery_metrics()
        
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used,
                    "free": memory.free
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "processes": process_count
            },
            "database": db_metrics,
            "redis": redis_metrics,
            "celery": celery_metrics
        }
        
        # 保存指标到数据库
        await _save_system_metrics(metrics)
        
        logger.debug("System metrics collected successfully")
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to collect system metrics: {e}")
        raise


async def _collect_database_metrics() -> Dict[str, Any]:
    """收集数据库指标"""
    try:
        async with get_async_session() as session:
            # 查询任务统计
            task_stats = await session.execute(
                select(
                    CrawlTask.status,
                    func.count(CrawlTask.id).label('count')
                ).group_by(CrawlTask.status)
            )
            
            task_counts = {row.status: row.count for row in task_stats}
            
            # 查询最近24小时的任务
            yesterday = datetime.now() - timedelta(days=1)
            recent_tasks = await session.execute(
                select(func.count(CrawlTask.id))
                .where(CrawlTask.created_at >= yesterday)
            )
            
            recent_count = recent_tasks.scalar() or 0
            
            return {
                "task_counts": task_counts,
                "recent_tasks_24h": recent_count,
                "connection_status": "healthy"
            }
            
    except Exception as e:
        logger.error(f"Failed to collect database metrics: {e}")
        return {
            "task_counts": {},
            "recent_tasks_24h": 0,
            "connection_status": "error",
            "error": str(e)
        }


async def _collect_redis_metrics() -> Dict[str, Any]:
    """收集Redis指标"""
    try:
        import os
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        redis_client = redis.from_url(redis_url)
        
        # 获取Redis信息
        info = await redis_client.info()
        
        # 获取队列长度
        queue_lengths = {}
        queues = [
            "crawler_queue",
            "crawler_queue_high", 
            "monitor_queue",
            "processor_queue",
            "cleanup_queue_low"
        ]
        
        for queue in queues:
            length = await redis_client.llen(queue)
            queue_lengths[queue] = length
        
        await redis_client.close()
        
        return {
            "connection_status": "healthy",
            "memory_used": info.get("used_memory", 0),
            "memory_peak": info.get("used_memory_peak", 0),
            "connected_clients": info.get("connected_clients", 0),
            "total_commands_processed": info.get("total_commands_processed", 0),
            "keyspace_hits": info.get("keyspace_hits", 0),
            "keyspace_misses": info.get("keyspace_misses", 0),
            "queue_lengths": queue_lengths
        }
        
    except Exception as e:
        logger.error(f"Failed to collect Redis metrics: {e}")
        return {
            "connection_status": "error",
            "error": str(e),
            "queue_lengths": {}
        }


async def _collect_celery_metrics() -> Dict[str, Any]:
    """收集Celery指标"""
    try:
        from celery import current_app
        
        # 获取活跃任务
        inspect = current_app.control.inspect()
        
        # 获取工作进程状态
        stats = inspect.stats()
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        reserved_tasks = inspect.reserved()
        
        # 计算总数
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_scheduled = sum(len(tasks) for tasks in (scheduled_tasks or {}).values())
        total_reserved = sum(len(tasks) for tasks in (reserved_tasks or {}).values())
        
        return {
            "worker_stats": stats or {},
            "active_tasks": total_active,
            "scheduled_tasks": total_scheduled,
            "reserved_tasks": total_reserved,
            "workers_online": len(stats or {}),
            "status": "healthy" if stats else "no_workers"
        }
        
    except Exception as e:
        logger.error(f"Failed to collect Celery metrics: {e}")
        return {
            "worker_stats": {},
            "active_tasks": 0,
            "scheduled_tasks": 0,
            "reserved_tasks": 0,
            "workers_online": 0,
            "status": "error",
            "error": str(e)
        }


async def _save_system_metrics(metrics: Dict[str, Any]) -> None:
    """保存系统指标到数据库"""
    try:
        async with get_async_session() as session:
            log_entry = SystemLog(
                level="INFO",
                message="System metrics collected",
                module="monitor",
                details=metrics,
                created_at=datetime.now()
            )
            
            session.add(log_entry)
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to save system metrics: {e}")


@celery_app.task(
    bind=True,
    base=MonitorTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 60}
)
def check_queue_health(self) -> Dict[str, Any]:
    """检查队列健康状态"""
    return asyncio.run(_check_queue_health_async())


async def _check_queue_health_async() -> Dict[str, Any]:
    """异步检查队列健康状态"""

    try:
        import os
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        redis_client = redis.from_url(redis_url)
        
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "issues": [],
            "queue_status": {}
        }
        
        # 检查各个队列
        queues_to_check = {
            "crawler_queue": {"max_length": 1000, "warning_length": 500},
            "crawler_queue_high": {"max_length": 100, "warning_length": 50},
            "monitor_queue": {"max_length": 50, "warning_length": 20},
            "processor_queue": {"max_length": 200, "warning_length": 100},
            "cleanup_queue_low": {"max_length": 10, "warning_length": 5}
        }
        
        for queue_name, limits in queues_to_check.items():
            try:
                length = await redis_client.llen(queue_name)
                
                status = "healthy"
                if length >= limits["max_length"]:
                    status = "critical"
                    health_status["issues"].append(
                        f"Queue {queue_name} is full ({length}/{limits['max_length']})"
                    )
                    health_status["overall_status"] = "critical"
                elif length >= limits["warning_length"]:
                    status = "warning"
                    health_status["issues"].append(
                        f"Queue {queue_name} is getting full ({length}/{limits['max_length']})"
                    )
                    if health_status["overall_status"] == "healthy":
                        health_status["overall_status"] = "warning"
                
                health_status["queue_status"][queue_name] = {
                    "length": length,
                    "max_length": limits["max_length"],
                    "status": status
                }
                
            except Exception as e:
                health_status["queue_status"][queue_name] = {
                    "status": "error",
                    "error": str(e)
                }
                health_status["issues"].append(f"Failed to check queue {queue_name}: {e}")
                health_status["overall_status"] = "critical"
        
        await redis_client.close()
        
        # 记录健康检查结果
        if health_status["overall_status"] != "healthy":
            await _log_health_issue(health_status)
        
        logger.info(f"Queue health check completed: {health_status['overall_status']}")
        return health_status
        
    except Exception as e:
        logger.error(f"Failed to check queue health: {e}")
        raise


async def _log_health_issue(health_status: Dict[str, Any]) -> None:
    """记录健康问题"""
    try:
        async with get_async_session() as session:
            log_entry = SystemLog(
                level="WARNING" if health_status["overall_status"] == "warning" else "ERROR",
                message=f"Queue health issue detected: {health_status['overall_status']}",
                module="monitor",
                details=health_status,
                created_at=datetime.now()
            )
            
            session.add(log_entry)
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to log health issue: {e}")


@celery_app.task(
    bind=True,
    base=MonitorTask
)
def urgent_monitor_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """紧急监控任务"""
    return asyncio.run(_urgent_monitor_task_async(task_data))


async def _urgent_monitor_task_async(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """异步紧急监控任务"""
    
    try:
        monitor_type = task_data.get("type", "unknown")
        
        if monitor_type == "api_failure":
            return await _handle_api_failure_alert(task_data)
        elif monitor_type == "queue_overflow":
            return await _handle_queue_overflow_alert(task_data)
        elif monitor_type == "system_resource":
            return await _handle_system_resource_alert(task_data)
        else:
            logger.warning(f"Unknown urgent monitor type: {monitor_type}")
            return {"status": "unknown_type", "type": monitor_type}
            
    except Exception as e:
        logger.error(f"Failed to handle urgent monitor task: {e}")
        raise


async def _handle_api_failure_alert(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理API失败告警"""
    logger.error(f"API failure alert: {task_data}")
    
    # 记录到数据库
    async with get_async_session() as session:
        log_entry = SystemLog(
            level="ERROR",
            message="API failure detected",
            module="api_monitor",
            details=task_data,
            created_at=datetime.now()
        )
        session.add(log_entry)
        await session.commit()
    
    return {"status": "logged", "type": "api_failure"}


async def _handle_queue_overflow_alert(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理队列溢出告警"""
    logger.warning(f"Queue overflow alert: {task_data}")
    
    # 记录到数据库
    async with get_async_session() as session:
        log_entry = SystemLog(
            level="WARNING",
            message="Queue overflow detected",
            module="queue_monitor",
            details=task_data,
            created_at=datetime.now()
        )
        session.add(log_entry)
        await session.commit()
    
    return {"status": "logged", "type": "queue_overflow"}


async def _handle_system_resource_alert(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理系统资源告警"""
    logger.warning(f"System resource alert: {task_data}")
    
    # 记录到数据库
    async with get_async_session() as session:
        log_entry = SystemLog(
            level="WARNING",
            message="System resource alert",
            module="system_monitor",
            details=task_data,
            created_at=datetime.now()
        )
        session.add(log_entry)
        await session.commit()
    
    return {"status": "logged", "type": "system_resource"}
