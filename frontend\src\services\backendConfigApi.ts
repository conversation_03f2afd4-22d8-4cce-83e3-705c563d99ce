/**
 * 后端配置API服务
 * 提供后端配置的CRUD操作和健康检查功能
 */

import {
  BackendConfigData,
  BackendConfigCreate,
  BackendConfigUpdate,
  BackendConfigSummary,
  BackendHealthCheck,
  BackendConfigStats,
  ApiError
} from '../types/newArchitecture';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const API_BASE = `${API_BASE_URL}/api/v1/backend-configs`;

class BackendConfigApiService {
  /**
   * 获取后端配置列表
   */
  async getConfigs(params?: {
    status?: string;
    performance_level?: string;
    search?: string;
    page?: number;
    size?: number;
  }): Promise<BackendConfigSummary[]> {
    const searchParams = new URLSearchParams();
    
    if (params?.status) searchParams.append('status', params.status);
    if (params?.performance_level) searchParams.append('performance_level', params.performance_level);
    if (params?.search) searchParams.append('search', params.search);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.size) searchParams.append('size', params.size.toString());

    const response = await fetch(`${API_BASE}/?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取后端配置列表失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取单个后端配置详情
   */
  async getConfig(backendId: string): Promise<BackendConfigData> {
    const response = await fetch(`${API_BASE}/${backendId}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('后端配置不存在');
      }
      throw new Error(`获取后端配置详情失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 根据名称获取后端配置
   */
  async getConfigByName(backendName: string): Promise<BackendConfigData> {
    const response = await fetch(`${API_BASE}/by-name/${encodeURIComponent(backendName)}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('后端配置不存在');
      }
      throw new Error(`获取后端配置详情失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 创建新的后端配置
   */
  async createConfig(configData: BackendConfigCreate): Promise<BackendConfigData> {
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(configData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '创建后端配置失败');
    }

    return response.json();
  }

  /**
   * 更新后端配置
   */
  async updateConfig(backendId: string, updateData: BackendConfigUpdate): Promise<BackendConfigData> {
    const response = await fetch(`${API_BASE}/${backendId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '更新后端配置失败');
    }

    return response.json();
  }

  /**
   * 删除后端配置
   */
  async deleteConfig(backendId: string): Promise<void> {
    const response = await fetch(`${API_BASE}/${backendId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '删除后端配置失败');
    }
  }

  /**
   * 获取后端配置统计信息
   */
  async getConfigStats(backendId: string): Promise<BackendConfigStats> {
    const response = await fetch(`${API_BASE}/${backendId}/stats`);
    if (!response.ok) {
      throw new Error(`获取统计信息失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(backendId: string): Promise<BackendHealthCheck> {
    const response = await fetch(`${API_BASE}/${backendId}/health-check`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '健康检查失败');
    }

    return response.json();
  }

  /**
   * 获取健康检查历史
   */
  async getHealthHistory(backendId: string, limit?: number): Promise<BackendHealthCheck[]> {
    const searchParams = new URLSearchParams();
    if (limit) searchParams.append('limit', limit.toString());

    const response = await fetch(`${API_BASE}/${backendId}/health-history?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取健康检查历史失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 测试后端连接
   */
  async testConnection(configData: BackendConfigCreate): Promise<{
    success: boolean;
    response_time: number;
    error_message?: string;
    api_version?: string;
    server_info?: any;
  }> {
    const response = await fetch(`${API_BASE}/test-connection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(configData),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '测试连接失败');
    }

    return response.json();
  }

  /**
   * 获取可用的后端配置（用于Worker分配）
   */
  async getAvailableConfigs(): Promise<BackendConfigSummary[]> {
    const response = await fetch(`${API_BASE}/available`);
    if (!response.ok) {
      throw new Error(`获取可用后端配置失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 批量健康检查
   */
  async batchHealthCheck(backendIds: string[]): Promise<{
    results: BackendHealthCheck[];
    summary: {
      total: number;
      healthy: number;
      unhealthy: number;
      errors: number;
    };
  }> {
    const response = await fetch(`${API_BASE}/batch-health-check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ backend_ids: backendIds }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '批量健康检查失败');
    }

    return response.json();
  }

  /**
   * 获取性能监控数据
   */
  async getPerformanceMetrics(backendId: string, timeRange?: {
    start: string;
    end: string;
  }): Promise<{
    response_times: { timestamp: string; value: number }[];
    request_counts: { timestamp: string; value: number }[];
    error_rates: { timestamp: string; value: number }[];
    load_levels: { timestamp: string; value: number }[];
  }> {
    const searchParams = new URLSearchParams();
    if (timeRange?.start) searchParams.append('start', timeRange.start);
    if (timeRange?.end) searchParams.append('end', timeRange.end);

    const response = await fetch(`${API_BASE}/${backendId}/metrics?${searchParams}`);
    if (!response.ok) {
      throw new Error(`获取性能监控数据失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 重置后端配置统计
   */
  async resetStats(backendId: string): Promise<void> {
    const response = await fetch(`${API_BASE}/${backendId}/reset-stats`, {
      method: 'POST',
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '重置统计失败');
    }
  }

  /**
   * 启用/禁用后端配置
   */
  async toggleStatus(backendId: string, enabled: boolean): Promise<BackendConfigData> {
    const response = await fetch(`${API_BASE}/${backendId}/toggle`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ enabled }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '切换状态失败');
    }

    return response.json();
  }

  /**
   * 获取后端配置推荐
   */
  async getRecommendations(requirements: {
    expected_load: number;
    performance_requirement: string;
    availability_requirement: number;
  }): Promise<{
    recommended: BackendConfigSummary[];
    alternatives: BackendConfigSummary[];
    reasons: string[];
  }> {
    const response = await fetch(`${API_BASE}/recommendations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requirements),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.detail || '获取推荐失败');
    }

    return response.json();
  }
}

// 导出单例实例
export const backendConfigApi = new BackendConfigApiService();
export default backendConfigApi;
