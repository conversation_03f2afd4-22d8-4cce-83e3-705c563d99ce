"""
商品相关数据模型
"""

from typing import Optional, List
from sqlalchemy import String, Text, ForeignKey, Integer, CheckConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, IDMixin, TimestampMixin


class ProductCategory(Base, IDMixin, TimestampMixin):
    """商品分类表"""
    
    __tablename__ = "product_categories"
    __table_args__ = (
        CheckConstraint("level >= 1", name="category_level_check"),
    )
    
    # 基础字段
    name: Mapped[str] = mapped_column(String(200), nullable=False, comment="分类名称")
    parent_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("product_categories.id"),
        nullable=True,
        comment="父分类ID"
    )
    level: Mapped[int] = mapped_column(Integer, default=1, comment="分类层级")
    path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="分类路径")
    
    # 关系
    parent: Mapped[Optional["ProductCategory"]] = relationship(
        "ProductCategory",
        remote_side="ProductCategory.id",
        back_populates="children"
    )
    children: Mapped[List["ProductCategory"]] = relationship(
        "ProductCategory",
        back_populates="parent"
    )
    products: Mapped[List["Product"]] = relationship(
        "Product",
        back_populates="category"
    )


class Product(Base, IDMixin, TimestampMixin):
    """商品基础信息表"""
    
    __tablename__ = "products"
    __table_args__ = (
        CheckConstraint(
            "status IN ('active', 'inactive', 'deleted')",
            name="products_status_check"
        ),
    )
    
    # 基础字段
    name_cn: Mapped[str] = mapped_column(String(500), nullable=False, comment="中文品名")
    name_original: Mapped[str] = mapped_column(String(500), nullable=False, comment="原始西班牙语品名")
    url: Mapped[str] = mapped_column(Text, nullable=False, unique=True, comment="商品链接")
    domain: Mapped[str] = mapped_column(String(100), nullable=False, comment="所属域名")
    
    # 分类关联
    category_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("product_categories.id"),
        nullable=True,
        comment="商品分类ID"
    )
    
    # 状态
    status: Mapped[str] = mapped_column(
        String(20),
        default="active",
        comment="状态: active, inactive, deleted"
    )
    
    # 关系
    category: Mapped[Optional[ProductCategory]] = relationship(
        "ProductCategory",
        back_populates="products"
    )
    snapshots: Mapped[List["ProductSnapshot"]] = relationship(
        "ProductSnapshot",
        back_populates="product",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Product(id={self.id}, name_cn='{self.name_cn[:30]}...', url='{self.url[:50]}...')>" 