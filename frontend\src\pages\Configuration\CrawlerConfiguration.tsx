/**
 * 爬取配置管理页面
 * 专门管理爬取配置的独立页面
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Button,
  Space,
  Table,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  Badge,
  Statistic,
  Spin
} from 'antd';
import {
  CodeOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

// 导入新架构的API和类型
import {
  crawlerConfigApi,
  CrawlerConfigSummary,
  CrawlerConfigCreate,
  CrawlerConfigUpdate,
  ConfigStatus
} from '../../services';

// 导入子组件
import CrawlerConfigForm from './components/CrawlerConfigForm';

const { Title, Text } = Typography;

const CrawlerConfiguration: React.FC = () => {
  // 状态管理
  const [crawlerConfigs, setCrawlerConfigs] = useState<CrawlerConfigSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCrawlerConfig, setSelectedCrawlerConfig] = useState<string | null>(null);
  const [configDetailVisible, setConfigDetailVisible] = useState(false);
  const [crawlerConfigModalVisible, setCrawlerConfigModalVisible] = useState(false);
  const [editingCrawlerConfig, setEditingCrawlerConfig] = useState<string | null>(null);
  const [editingConfigData, setEditingConfigData] = useState<any>(null);
  const [configDetailData, setConfigDetailData] = useState<any>(null);

  // 初始化数据
  useEffect(() => {
    fetchConfigs();
  }, []);

  // 获取配置数据
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const crawlerConfigsData = await crawlerConfigApi.getConfigs();
      setCrawlerConfigs(crawlerConfigsData);
    } catch (error) {
      message.error(`获取爬取配置数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 爬取配置相关操作
  const handleViewCrawlerConfig = async (configId: string) => {
    try {
      setLoading(true);
      const configData = await crawlerConfigApi.getConfig(configId);
      setConfigDetailData(configData);
      setSelectedCrawlerConfig(configId);
      setConfigDetailVisible(true);
    } catch (error) {
      message.error(`获取配置详情失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCrawlerConfig = () => {
    setEditingCrawlerConfig(null);
    setEditingConfigData(null);
    setCrawlerConfigModalVisible(true);
  };

  const handleEditCrawlerConfig = async (configId: string) => {
    try {
      setLoading(true);
      const configData = await crawlerConfigApi.getConfig(configId);
      setEditingCrawlerConfig(configId);
      setEditingConfigData(configData);
      setCrawlerConfigModalVisible(true);
    } catch (error) {
      message.error(`获取配置数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCrawlerConfig = async (configId: string) => {
    try {
      await crawlerConfigApi.deleteConfig(configId);
      message.success('删除爬取配置成功');
      fetchConfigs();
    } catch (error) {
      message.error(`删除爬取配置失败: ${error}`);
    }
  };

  const handleDuplicateCrawlerConfig = async (configId: string) => {
    try {
      const config = await crawlerConfigApi.getConfig(configId);
      const newName = `${config.config_name} - 副本`;
      await crawlerConfigApi.duplicateConfig(configId, newName);
      message.success('复制爬取配置成功');
      fetchConfigs();
    } catch (error) {
      message.error(`复制爬取配置失败: ${error}`);
    }
  };

  const handleValidateCrawlerConfig = async (configId: string) => {
    try {
      const result = await crawlerConfigApi.validateConfig(configId);
      if (result.is_valid) {
        message.success('配置验证通过');
      } else {
        Modal.warning({
          title: '配置验证失败',
          content: (
            <div>
              <p>发现以下问题：</p>
              <ul>
                {result.errors.map((error, index) => (
                  <li key={index} style={{ color: '#ff4d4f' }}>{error}</li>
                ))}
              </ul>
              {result.warnings.length > 0 && (
                <>
                  <p>警告：</p>
                  <ul>
                    {result.warnings.map((warning, index) => (
                      <li key={index} style={{ color: '#faad14' }}>{warning}</li>
                    ))}
                  </ul>
                </>
              )}
            </div>
          ),
        });
      }
    } catch (error) {
      message.error(`验证配置失败: ${error}`);
    }
  };

  // 状态标签渲染
  const renderConfigStatus = (status: ConfigStatus) => {
    const statusConfig = {
      active: { color: 'green', text: '活跃' },
      inactive: { color: 'default', text: '未激活' },
      draft: { color: 'orange', text: '草稿' },
      deprecated: { color: 'red', text: '已废弃' }
    };
    
    const config = statusConfig[status] || statusConfig.inactive;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 爬取配置表格列定义
  const crawlerConfigColumns: TableColumnsType<CrawlerConfigSummary> = [
    {
      title: '配置名称',
      dataIndex: 'config_name',
      key: 'config_name',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderConfigStatus,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      render: (count) => <Badge count={count} showZero />,
    },
    {
      title: '最后使用',
      dataIndex: 'last_used',
      key: 'last_used',
      render: (date) => date ? new Date(date).toLocaleString() : '从未使用',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Tooltip title="验证配置">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handleValidateCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              onClick={() => handleDuplicateCrawlerConfig(record.config_id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDeleteCrawlerConfig(record.config_id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <CodeOutlined /> 爬取配置管理
        </Title>
        <Text type="secondary">
          管理爬取配置，支持创建、编辑、验证和监控配置状态
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="爬取配置总数"
              value={crawlerConfigs.length}
              prefix={<CodeOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="活跃配置"
              value={crawlerConfigs.filter(c => c.status === 'active').length}
              prefix={<PlayCircleOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="草稿配置"
              value={crawlerConfigs.filter(c => c.status === 'draft').length}
              prefix={<EditOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>配置列表</Title>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchConfigs}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateCrawlerConfig}
            >
              新建爬取配置
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Table
            columns={crawlerConfigColumns}
            dataSource={crawlerConfigs}
            rowKey="config_id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100']
            }}
          />
        </Spin>
      </Card>

      {/* 爬取配置表单模态框 */}
      <Modal
        title={editingCrawlerConfig ? "编辑爬取配置" : "新建爬取配置"}
        open={crawlerConfigModalVisible}
        onCancel={() => setCrawlerConfigModalVisible(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: '1400px', minWidth: '800px' }}
        destroyOnHidden
      >
        <CrawlerConfigForm
          configId={editingCrawlerConfig || undefined}
          initialData={editingConfigData}
          onSave={async (configData) => {
            try {
              if (editingCrawlerConfig) {
                // 更新配置
                await crawlerConfigApi.updateConfig(editingCrawlerConfig, configData as CrawlerConfigUpdate);
                message.success('配置更新成功');
              } else {
                // 创建新配置
                await crawlerConfigApi.createConfig(configData as CrawlerConfigCreate);
                message.success('配置创建成功');
              }
              setCrawlerConfigModalVisible(false);
              setEditingConfigData(null); // 清空编辑数据
              fetchConfigs();
            } catch (error) {
              console.error('保存配置失败:', error);
              message.error('保存配置失败，请检查输入');
              throw error; // 重新抛出错误，让表单知道保存失败
            }
          }}
          onCancel={() => {
            setCrawlerConfigModalVisible(false);
            setEditingConfigData(null); // 清空编辑数据
          }}
        />
      </Modal>

      {/* 配置详情模态框 */}
      <Modal
        title={`配置详情 - ${configDetailData?.config_name || '未知配置'}`}
        open={configDetailVisible}
        onCancel={() => setConfigDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setConfigDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {configDetailData && (
          <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <strong>配置ID:</strong> {configDetailData.config_id}
              </Col>
              <Col span={12}>
                <strong>配置名称:</strong> {configDetailData.config_name}
              </Col>
              <Col span={24}>
                <strong>描述:</strong> {configDetailData.description || '无描述'}
              </Col>
              <Col span={12}>
                <strong>状态:</strong> <Tag color={configDetailData.status === 'active' ? 'green' : 'default'}>{configDetailData.status}</Tag>
              </Col>
              <Col span={12}>
                <strong>版本:</strong> {configDetailData.version}
              </Col>
              <Col span={24}>
                <strong>标签:</strong> {configDetailData.tags?.map((tag: string) => (
                  <Tag key={tag}>{tag}</Tag>
                )) || '无标签'}
              </Col>
              <Col span={12}>
                <strong>使用次数:</strong> {configDetailData.usage_count}
              </Col>
              <Col span={12}>
                <strong>最后使用:</strong> {configDetailData.last_used ? new Date(configDetailData.last_used).toLocaleString() : '从未使用'}
              </Col>
              <Col span={12}>
                <strong>创建时间:</strong> {configDetailData.created_at ? new Date(configDetailData.created_at).toLocaleString() : '-'}
              </Col>
              <Col span={12}>
                <strong>更新时间:</strong> {configDetailData.updated_at ? new Date(configDetailData.updated_at).toLocaleString() : '-'}
              </Col>
            </Row>

            <div style={{ marginTop: '24px' }}>
              <h4>配置详情</h4>
              <pre style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px',
                maxHeight: '300px',
                overflow: 'auto'
              }}>
                {JSON.stringify(configDetailData, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CrawlerConfiguration;