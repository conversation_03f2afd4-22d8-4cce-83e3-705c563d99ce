#!/usr/bin/env python3
"""
验证新的Redis存储结构

检查分层存储和索引系统是否正确创建
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import redis.asyncio as redis


async def verify_new_structure():
    """验证新的存储结构"""
    try:
        # 连接Redis
        redis_client = redis.from_url("redis://localhost:6379/0")
        await redis_client.ping()
        
        print("🔍 验证新的Redis存储结构")
        print("=" * 50)
        
        # 获取所有键
        all_keys = await redis_client.keys("*")
        print(f"📊 总键数量: {len(all_keys)}")
        
        # 检查索引结构
        print("\n📋 索引结构检查:")
        
        # 基础索引
        all_tasks_exists = await redis_client.exists('monitoring_tasks:indexes:all_task_ids')
        active_tasks_exists = await redis_client.exists('monitoring_tasks:indexes:active_tasks')
        print(f"  ✅ 基础索引: all_task_ids={all_tasks_exists}, active_tasks={active_tasks_exists}")
        
        # 状态索引
        status_list = ['active', 'paused', 'completed', 'draft', 'failed']
        status_indexes = {}
        for status in status_list:
            key = f'monitoring_tasks:indexes:by_status:{status}'
            exists = await redis_client.exists(key)
            status_indexes[status] = exists
        
        print(f"  ✅ 状态索引: {status_indexes}")
        
        # 平台索引
        platform_list = ['mercadolibre', 'amazon', 'taobao', 'ebay']
        platform_indexes = {}
        for platform in platform_list:
            key = f'monitoring_tasks:indexes:by_platform:{platform}'
            exists = await redis_client.exists(key)
            platform_indexes[platform] = exists
        
        print(f"  ✅ 平台索引: {platform_indexes}")
        
        # 检查新结构的键模式
        print("\n🎯 新结构键模式:")
        
        # 分类所有键
        structure_keys = {
            'indexes': [],
            'tasks': [],
            'url_tasks': [],
            'other': []
        }
        
        for key in all_keys:
            key_str = key.decode() if isinstance(key, bytes) else key
            
            if key_str.startswith('monitoring_tasks:indexes:'):
                structure_keys['indexes'].append(key_str)
            elif key_str.startswith('monitoring_tasks:tasks:'):
                structure_keys['tasks'].append(key_str)
            elif key_str.startswith('monitoring_tasks:url_tasks:'):
                structure_keys['url_tasks'].append(key_str)
            else:
                structure_keys['other'].append(key_str)
        
        for category, keys in structure_keys.items():
            print(f"  {category}: {len(keys)}个键")
            for key in keys[:5]:  # 只显示前5个
                print(f"    - {key}")
            if len(keys) > 5:
                print(f"    ... 还有{len(keys)-5}个键")
        
        # 验证结构完整性
        print("\n✅ 结构完整性验证:")
        
        expected_indexes = [
            'monitoring_tasks:indexes:all_task_ids',
            'monitoring_tasks:indexes:active_tasks'
        ]
        
        for status in status_list:
            expected_indexes.append(f'monitoring_tasks:indexes:by_status:{status}')
        
        for platform in platform_list:
            expected_indexes.append(f'monitoring_tasks:indexes:by_platform:{platform}')
        
        missing_indexes = []
        for index in expected_indexes:
            if not await redis_client.exists(index):
                missing_indexes.append(index)
        
        if missing_indexes:
            print(f"  ❌ 缺失的索引: {missing_indexes}")
        else:
            print(f"  ✅ 所有预期索引都已创建 ({len(expected_indexes)}个)")
        
        # 检查内存使用
        info = await redis_client.info('memory')
        used_memory = info.get('used_memory_human', 'unknown')
        print(f"\n📊 内存使用: {used_memory}")
        
        await redis_client.close()
        
        print("\n🎉 新存储结构验证完成!")
        
        return len(missing_indexes) == 0
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


async def main():
    """主函数"""
    success = await verify_new_structure()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
