"""
监控和告警API路由

提供系统监控、性能指标和告警管理的API端点
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, timedelta
import asyncio

import redis.asyncio as redis
from app.core.persistent_task_manager import PersistentTaskManager
from app.services.crawler_request_monitor import get_crawler_request_monitor

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/monitoring", tags=["monitoring"])

# 全局监控实例
_redis_client: Optional[redis.Redis] = None
_monitoring_data = []


# Pydantic模型
class SystemMetrics(BaseModel):
    """系统指标"""
    timestamp: str
    redis_metrics: Dict[str, Any]
    taskmanager_metrics: Dict[str, Any]
    alerts: List[Dict[str, Any]]


class AlertRule(BaseModel):
    """告警规则"""
    name: str
    component: str  # redis, taskmanager, system
    metric: str
    operator: str  # gt, lt, eq, gte, lte
    threshold: float
    severity: str  # info, warning, error, critical
    enabled: bool = True
    description: Optional[str] = None


class AlertResponse(BaseModel):
    """告警响应"""
    id: str
    rule_name: str
    component: str
    metric: str
    current_value: float
    threshold: float
    severity: str
    message: str
    timestamp: str
    acknowledged: bool = False


async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global _redis_client

    if _redis_client is None:
        import os
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        _redis_client = redis.from_url(redis_url)
        await _redis_client.ping()

    return _redis_client


async def collect_redis_metrics() -> Dict[str, Any]:
    """收集Redis指标"""
    try:
        redis_client = await get_redis_client()
        info = await redis_client.info()
        
        return {
            "server": {
                "redis_version": info.get("redis_version"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
                "connected_clients": info.get("connected_clients"),
                "blocked_clients": info.get("blocked_clients")
            },
            "memory": {
                "used_memory": info.get("used_memory"),
                "used_memory_human": info.get("used_memory_human"),
                "used_memory_peak": info.get("used_memory_peak"),
                "used_memory_peak_human": info.get("used_memory_peak_human"),
                "maxmemory": info.get("maxmemory"),
                "maxmemory_human": info.get("maxmemory_human"),
                "mem_fragmentation_ratio": info.get("mem_fragmentation_ratio")
            },
            "persistence": {
                "aof_enabled": info.get("aof_enabled"),
                "rdb_last_save_time": info.get("rdb_last_save_time"),
                "aof_last_rewrite_time_sec": info.get("aof_last_rewrite_time_sec"),
                "aof_current_size": info.get("aof_current_size"),
                "rdb_changes_since_last_save": info.get("rdb_changes_since_last_save")
            },
            "stats": {
                "total_connections_received": info.get("total_connections_received"),
                "total_commands_processed": info.get("total_commands_processed"),
                "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
                "expired_keys": info.get("expired_keys"),
                "evicted_keys": info.get("evicted_keys")
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to collect Redis metrics: {e}")
        return {}


async def collect_taskmanager_metrics() -> Dict[str, Any]:
    """收集TaskManager指标"""
    try:
        redis_client = await get_redis_client()
        
        # TaskManager队列键
        keys = {
            "pending": "taskmanager:pending_batches",
            "running": "taskmanager:running_batches",
            "completed": "taskmanager:completed_batches",
            "failed": "taskmanager:failed_batches"
        }
        
        metrics = {
            "queues": {},
            "total_items": 0
        }
        
        # 收集队列指标
        pending_count = await redis_client.llen(keys["pending"])
        running_count = await redis_client.hlen(keys["running"])
        completed_count = await redis_client.scard(keys["completed"])
        failed_count = await redis_client.scard(keys["failed"])
        
        metrics["queues"] = {
            "pending": {"count": pending_count, "type": "list"},
            "running": {"count": running_count, "type": "hash"},
            "completed": {"count": completed_count, "type": "set"},
            "failed": {"count": failed_count, "type": "set"}
        }
        
        metrics["total_items"] = pending_count + running_count + completed_count + failed_count
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to collect TaskManager metrics: {e}")
        return {}


def check_alert_rules(redis_metrics: Dict[str, Any], tm_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
    """检查告警规则"""
    alerts = []
    
    # 预定义告警规则
    rules = [
        {
            "name": "redis_memory_fragmentation",
            "component": "redis",
            "metric": "mem_fragmentation_ratio",
            "operator": "gt",
            "threshold": 2.0,
            "severity": "warning",
            "message": "Redis内存碎片率过高"
        },
        {
            "name": "redis_connected_clients",
            "component": "redis", 
            "metric": "connected_clients",
            "operator": "gt",
            "threshold": 1000,
            "severity": "warning",
            "message": "Redis连接数过多"
        },
        {
            "name": "taskmanager_pending_queue",
            "component": "taskmanager",
            "metric": "pending_count",
            "operator": "gt",
            "threshold": 100,
            "severity": "warning",
            "message": "TaskManager待处理队列积压"
        },
        {
            "name": "taskmanager_failed_queue",
            "component": "taskmanager",
            "metric": "failed_count",
            "operator": "gt",
            "threshold": 50,
            "severity": "error",
            "message": "TaskManager失败任务过多"
        }
    ]
    
    # 检查每个规则
    for rule in rules:
        try:
            if rule["component"] == "redis" and redis_metrics:
                if rule["metric"] == "mem_fragmentation_ratio":
                    value = redis_metrics.get("memory", {}).get("mem_fragmentation_ratio", 0)
                elif rule["metric"] == "connected_clients":
                    value = redis_metrics.get("server", {}).get("connected_clients", 0)
                else:
                    continue
            elif rule["component"] == "taskmanager" and tm_metrics:
                if rule["metric"] == "pending_count":
                    value = tm_metrics.get("queues", {}).get("pending", {}).get("count", 0)
                elif rule["metric"] == "failed_count":
                    value = tm_metrics.get("queues", {}).get("failed", {}).get("count", 0)
                else:
                    continue
            else:
                continue
            
            # 检查阈值
            triggered = False
            if rule["operator"] == "gt" and value > rule["threshold"]:
                triggered = True
            elif rule["operator"] == "lt" and value < rule["threshold"]:
                triggered = True
            elif rule["operator"] == "gte" and value >= rule["threshold"]:
                triggered = True
            elif rule["operator"] == "lte" and value <= rule["threshold"]:
                triggered = True
            elif rule["operator"] == "eq" and value == rule["threshold"]:
                triggered = True
            
            if triggered:
                alerts.append({
                    "id": f"{rule['name']}_{int(datetime.now().timestamp())}",
                    "rule_name": rule["name"],
                    "component": rule["component"],
                    "metric": rule["metric"],
                    "current_value": value,
                    "threshold": rule["threshold"],
                    "severity": rule["severity"],
                    "message": rule["message"],
                    "timestamp": datetime.now().isoformat(),
                    "acknowledged": False
                })
                
        except Exception as e:
            logger.error(f"Error checking alert rule {rule['name']}: {e}")
    
    return alerts


@router.get("/metrics", response_model=SystemMetrics)
async def get_system_metrics():
    """获取系统指标"""
    try:
        # 收集指标
        redis_metrics = await collect_redis_metrics()
        tm_metrics = await collect_taskmanager_metrics()
        
        # 检查告警
        alerts = check_alert_rules(redis_metrics, tm_metrics)
        
        metrics = SystemMetrics(
            timestamp=datetime.now().isoformat(),
            redis_metrics=redis_metrics,
            taskmanager_metrics=tm_metrics,
            alerts=alerts
        )
        
        # 保存到历史记录
        global _monitoring_data
        _monitoring_data.append(metrics.dict())
        
        # 限制历史记录大小
        if len(_monitoring_data) > 100:
            _monitoring_data = _monitoring_data[-100:]
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get system metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/history")
async def get_metrics_history(
    hours: int = 1,
    limit: int = 100
):
    """获取历史指标"""
    try:
        # 计算时间范围
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤历史数据
        filtered_data = []
        for data in _monitoring_data[-limit:]:
            data_time = datetime.fromisoformat(data["timestamp"])
            if data_time >= cutoff_time:
                filtered_data.append(data)
        
        return {
            "total_records": len(filtered_data),
            "time_range_hours": hours,
            "data": filtered_data
        }
        
    except Exception as e:
        logger.error(f"Failed to get metrics history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts")
async def get_active_alerts():
    """获取活跃告警"""
    try:
        # 获取最新指标
        redis_metrics = await collect_redis_metrics()
        tm_metrics = await collect_taskmanager_metrics()
        
        # 检查告警
        alerts = check_alert_rules(redis_metrics, tm_metrics)
        
        return {
            "total_alerts": len(alerts),
            "alerts": alerts,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get active alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "components": {}
        }
        
        # 检查Redis
        try:
            redis_client = await get_redis_client()
            await redis_client.ping()
            health_status["components"]["redis"] = {
                "status": "healthy",
                "message": "Redis connection successful"
            }
        except Exception as e:
            health_status["components"]["redis"] = {
                "status": "unhealthy",
                "message": f"Redis connection failed: {e}"
            }
            health_status["overall_status"] = "degraded"
        
        # 检查TaskManager队列
        try:
            tm_metrics = await collect_taskmanager_metrics()
            total_items = tm_metrics.get("total_items", 0)
            
            if total_items < 10000:  # 假设阈值
                health_status["components"]["taskmanager"] = {
                    "status": "healthy",
                    "message": f"TaskManager queues normal ({total_items} items)"
                }
            else:
                health_status["components"]["taskmanager"] = {
                    "status": "warning",
                    "message": f"TaskManager queues high ({total_items} items)"
                }
                if health_status["overall_status"] == "healthy":
                    health_status["overall_status"] = "warning"
                    
        except Exception as e:
            health_status["components"]["taskmanager"] = {
                "status": "unhealthy",
                "message": f"TaskManager check failed: {e}"
            }
            health_status["overall_status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dashboard")
async def get_dashboard_data():
    """获取仪表板数据"""
    try:
        # 收集所有指标
        redis_metrics = await collect_redis_metrics()
        tm_metrics = await collect_taskmanager_metrics()
        alerts = check_alert_rules(redis_metrics, tm_metrics)
        
        # 计算关键指标
        dashboard_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "redis_status": "healthy" if redis_metrics else "unhealthy",
                "taskmanager_status": "healthy" if tm_metrics else "unhealthy",
                "total_alerts": len(alerts),
                "critical_alerts": len([a for a in alerts if a["severity"] == "critical"]),
                "warning_alerts": len([a for a in alerts if a["severity"] == "warning"])
            },
            "redis_summary": {
                "version": redis_metrics.get("server", {}).get("redis_version", "unknown"),
                "uptime_hours": round(redis_metrics.get("server", {}).get("uptime_in_seconds", 0) / 3600, 1),
                "memory_used": redis_metrics.get("memory", {}).get("used_memory_human", "unknown"),
                "connected_clients": redis_metrics.get("server", {}).get("connected_clients", 0),
                "ops_per_sec": redis_metrics.get("stats", {}).get("instantaneous_ops_per_sec", 0)
            },
            "taskmanager_summary": {
                "pending_tasks": tm_metrics.get("queues", {}).get("pending", {}).get("count", 0),
                "running_tasks": tm_metrics.get("queues", {}).get("running", {}).get("count", 0),
                "completed_tasks": tm_metrics.get("queues", {}).get("completed", {}).get("count", 0),
                "failed_tasks": tm_metrics.get("queues", {}).get("failed", {}).get("count", 0),
                "total_tasks": tm_metrics.get("total_items", 0)
            },
            "recent_alerts": alerts[:5]  # 最近5个告警
        }
        
        return dashboard_data
        
    except Exception as e:
        logger.error(f"Failed to get dashboard data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str):
    """确认告警"""
    try:
        # 这里应该实现告警确认逻辑
        # 在实际应用中，可能需要数据库来存储告警状态
        
        return {
            "success": True,
            "alert_id": alert_id,
            "acknowledged_at": datetime.now().isoformat(),
            "message": "Alert acknowledged successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to acknowledge alert {alert_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def start_background_monitoring():
    """启动后台监控任务"""
    async def monitoring_loop():
        while True:
            try:
                # 收集指标
                await get_system_metrics()
                
                # 等待下次收集
                await asyncio.sleep(60)  # 每分钟收集一次
                
            except Exception as e:
                logger.error(f"Background monitoring error: {e}")
                await asyncio.sleep(60)
    
    # 启动后台任务
    asyncio.create_task(monitoring_loop())


# ==================== 爬虫请求监控端点 ====================

@router.get("/crawler-request-metrics/summary")
async def get_crawler_request_metrics_summary(
    days: int = Query(1, ge=1, le=30, description="查询天数")
) -> Dict[str, Any]:
    """
    获取爬虫请求构建的指标摘要

    Args:
        days: 查询最近几天的数据

    Returns:
        指标摘要数据
    """
    try:
        monitor = await get_crawler_request_monitor()
        summary = await monitor.get_metrics_summary(days)

        return {
            "success": True,
            "data": summary,
            "query_params": {
                "days": days,
                "query_time": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"获取爬虫请求指标摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标摘要失败: {str(e)}")

    finally:
        try:
            monitor = await get_crawler_request_monitor()
            await monitor.close()
        except:
            pass


@router.get("/crawler-request-metrics/task/{task_id}")
async def get_task_crawler_request_metrics(
    task_id: str
) -> Dict[str, Any]:
    """
    获取特定任务的爬虫请求构建指标

    Args:
        task_id: 监控任务ID

    Returns:
        任务的指标历史数据
    """
    try:
        monitor = await get_crawler_request_monitor()
        metrics = await monitor.get_task_metrics(task_id)

        return {
            "success": True,
            "data": {
                "task_id": task_id,
                "metrics_count": len(metrics),
                "metrics": metrics
            }
        }

    except Exception as e:
        logger.error(f"获取任务爬虫请求指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务指标失败: {str(e)}")

    finally:
        try:
            monitor = await get_crawler_request_monitor()
            await monitor.close()
        except:
            pass


@router.get("/crawler-request-metrics/report/{task_id}")
async def get_task_monitoring_report(
    task_id: str
) -> Dict[str, Any]:
    """
    获取任务的监控报告

    Args:
        task_id: 监控任务ID

    Returns:
        格式化的监控报告
    """
    try:
        monitor = await get_crawler_request_monitor()
        report = await monitor.generate_monitoring_report(task_id)

        return {
            "success": True,
            "data": {
                "task_id": task_id,
                "report": report,
                "generated_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"生成任务监控报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成监控报告失败: {str(e)}")

    finally:
        try:
            monitor = await get_crawler_request_monitor()
            await monitor.close()
        except:
            pass
