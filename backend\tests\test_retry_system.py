#!/usr/bin/env python3
"""
重试系统测试

测试任务失败重试、队列管理和死信队列功能
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.retry_manager import RetryManager, RetryConfig, FailureType, RetryStrategy
from backend.app.core.task_splitter import TaskPriority

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_retry_manager():
    """测试重试管理器"""
    
    logger.info("🔄 Testing Retry Manager")
    logger.info("=" * 40)
    
    # 创建重试配置
    retry_config = RetryConfig(
        max_retries=3,
        base_delay=5.0,  # 5秒基础延迟
        max_delay=60.0,
        backoff_multiplier=2.0,
        jitter=True
    )
    
    # 创建重试管理器
    retry_manager = RetryManager(retry_config)
    
    try:
        await retry_manager.start()
        
        # 测试1: 网络错误重试
        logger.info("📡 Test 1: Network Error Retry")
        
        network_error = ConnectionError("Connection timeout")
        will_retry = await retry_manager.handle_batch_failure(
            batch_id="test_batch_1",
            task_id=1,
            urls=["https://example.com/1", "https://example.com/2"],
            error=network_error,
            priority=TaskPriority.NORMAL
        )
        
        logger.info(f"Network error will retry: {will_retry}")
        
        # 测试2: API限制错误重试
        logger.info("🚦 Test 2: API Limit Error Retry")
        
        api_error = Exception("Rate limit exceeded (429)")
        will_retry = await retry_manager.handle_batch_failure(
            batch_id="test_batch_2",
            task_id=2,
            urls=["https://example.com/3", "https://example.com/4"],
            error=api_error,
            priority=TaskPriority.HIGH
        )
        
        logger.info(f"API limit error will retry: {will_retry}")
        
        # 测试3: 超过最大重试次数
        logger.info("❌ Test 3: Exceed Max Retries")
        
        # 模拟多次失败
        for i in range(4):  # 超过max_retries=3
            timeout_error = TimeoutError("Request timeout")
            will_retry = await retry_manager.handle_batch_failure(
                batch_id="test_batch_3",
                task_id=3,
                urls=["https://example.com/5"],
                error=timeout_error,
                priority=TaskPriority.LOW
            )
            logger.info(f"Attempt {i+1}: will retry = {will_retry}")
        
        # 测试4: 获取重试统计
        logger.info("📊 Test 4: Retry Statistics")
        
        stats = retry_manager.get_stats()
        logger.info(f"Retry stats: {stats}")
        
        # 测试5: 检查准备重试的任务
        logger.info("⏰ Test 5: Ready Retries")
        
        # 等待一段时间让重试任务准备好
        await asyncio.sleep(6)  # 等待超过base_delay
        
        ready_retries = await retry_manager.get_ready_retries()
        logger.info(f"Ready retries: {len(ready_retries)}")
        
        for retry_task in ready_retries:
            logger.info(f"  - {retry_task.batch_id}: attempt {retry_task.retry_count}")
        
        logger.info("✅ Retry Manager tests completed")
        
    except Exception as e:
        logger.error(f"❌ Retry Manager test failed: {e}")
        raise
    finally:
        await retry_manager.stop()


async def test_failure_type_detection():
    """测试失败类型检测"""
    
    logger.info("🔍 Testing Failure Type Detection")
    logger.info("=" * 40)
    
    retry_manager = RetryManager()
    
    # 测试不同类型的错误
    test_errors = [
        (ConnectionError("Connection failed"), FailureType.NETWORK_ERROR),
        (TimeoutError("Request timeout"), FailureType.TIMEOUT_ERROR),
        (Exception("Rate limit exceeded (429)"), FailureType.API_LIMIT_ERROR),
        (ValueError("JSON parse error"), FailureType.PARSE_ERROR),
        (RuntimeError("Unknown error"), FailureType.UNKNOWN_ERROR),
    ]
    
    for error, expected_type in test_errors:
        detected_type = retry_manager._analyze_failure_type(error)
        status = "✅" if detected_type == expected_type else "❌"
        logger.info(f"{status} {error.__class__.__name__}: {detected_type.value} (expected: {expected_type.value})")


async def test_retry_delay_calculation():
    """测试重试延迟计算"""
    
    logger.info("⏱️ Testing Retry Delay Calculation")
    logger.info("=" * 40)
    
    from backend.app.core.retry_manager import FailedTask
    
    retry_config = RetryConfig(
        base_delay=10.0,
        backoff_multiplier=2.0,
        max_delay=300.0,
        jitter=False  # 关闭抖动以便测试
    )
    
    retry_manager = RetryManager(retry_config)
    
    # 测试不同重试次数的延迟
    for retry_count in range(1, 5):
        failed_task = FailedTask(
            batch_id=f"test_{retry_count}",
            task_id=1,
            urls=["https://example.com"],
            failure_type=FailureType.NETWORK_ERROR,
            error_message="Test error",
            retry_count=retry_count,
            last_attempt=datetime.now(),
            next_retry_time=None,
            priority=TaskPriority.NORMAL
        )
        
        delay = retry_manager._calculate_retry_delay(failed_task)
        expected_delay = min(10.0 * (2.0 ** retry_count), 300.0)
        
        logger.info(f"Retry {retry_count}: {delay:.1f}s (expected: {expected_delay:.1f}s)")


async def test_priority_downgrade():
    """测试优先级降级"""
    
    logger.info("📉 Testing Priority Downgrade")
    logger.info("=" * 40)
    
    retry_manager = RetryManager()
    
    priorities = [
        TaskPriority.URGENT,
        TaskPriority.HIGH,
        TaskPriority.NORMAL,
        TaskPriority.LOW
    ]
    
    for priority in priorities:
        downgraded = retry_manager._downgrade_priority(priority)
        logger.info(f"{priority.name} → {downgraded.name}")


async def main():
    """主函数"""
    
    print("🧪 MonIt Retry System Tests")
    print("=" * 30)
    
    try:
        # 测试失败类型检测
        await test_failure_type_detection()
        print()
        
        # 测试重试延迟计算
        await test_retry_delay_calculation()
        print()
        
        # 测试优先级降级
        await test_priority_downgrade()
        print()
        
        # 测试重试管理器（需要Redis）
        try:
            await test_retry_manager()
        except Exception as e:
            logger.warning(f"Retry manager test skipped (Redis required): {e}")
        
        logger.info("🎉 All retry system tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Tests failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
