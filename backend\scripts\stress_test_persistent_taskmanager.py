#!/usr/bin/env python3
"""
持久化TaskManager压力测试

测试系统在高负载下的性能和稳定性
"""

import asyncio
import json
import logging
import random
import sys
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import statistics

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

import redis.asyncio as redis
from backend.app.core.persistent_task_manager import PersistentTaskManager
from backend.app.core.task_manager import TaskManagerConfig
from backend.app.core.task_splitter import SplitterConfig, TaskPriority
from backend.app.core.rate_limiter import RateLimitConfig
from backend.app.core.retry_manager import RetryConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class StressTestConfig:
    """压力测试配置"""
    
    def __init__(self):
        # 测试参数 - 适合Windows环境的较小规模
        self.total_tasks = 200           # 总任务数
        self.concurrent_submissions = 20  # 并发提交数
        self.urls_per_task = 10          # 每个任务的URL数
        self.test_duration_minutes = 15  # 测试持续时间
        
        # 任务分布
        self.priority_distribution = {
            TaskPriority.URGENT: 0.05,    # 5% 紧急
            TaskPriority.HIGH: 0.15,      # 15% 高优先级
            TaskPriority.NORMAL: 0.70,    # 70% 普通
            TaskPriority.LOW: 0.10        # 10% 低优先级
        }
        
        # 平台分布
        self.platform_distribution = {
            "mercadolibre": 0.80,         # 80% MercadoLibre
            "amazon": 0.15,               # 15% Amazon
            "ebay": 0.05                  # 5% eBay
        }
        
        # Redis配置
        self.redis_url = "redis://localhost:6379/0"
        
        # 监控间隔
        self.monitoring_interval = 10     # 秒


class StressTestMetrics:
    """压力测试指标"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        
        # 任务提交指标
        self.tasks_submitted = 0
        self.tasks_failed = 0
        self.submission_times = []
        
        # 系统性能指标
        self.memory_usage_samples = []
        self.redis_metrics_samples = []
        self.queue_size_samples = []
        
        # 错误统计
        self.errors = []
        
        # 吞吐量指标
        self.throughput_samples = []


class PersistentTaskManagerStressTest:
    """持久化TaskManager压力测试"""
    
    def __init__(self, config: StressTestConfig):
        self.config = config
        self.metrics = StressTestMetrics()
        self.task_manager = None
        self.redis_client = None
        self.is_running = False
        
        # 测试数据
        self.test_urls = self._generate_test_urls()
        
    def _generate_test_urls(self) -> List[str]:
        """生成测试URL"""
        urls = []
        
        # MercadoLibre URLs
        for i in range(1000):
            urls.append(f"https://mercadolibre.com.ar/product/stress-test-{i}")
        
        # Amazon URLs
        for i in range(200):
            urls.append(f"https://amazon.com/dp/stress-test-{i}")
        
        # eBay URLs
        for i in range(100):
            urls.append(f"https://ebay.com/itm/stress-test-{i}")
        
        return urls
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up stress test environment...")
        
        try:
            # 连接Redis
            self.redis_client = redis.from_url(self.config.redis_url)
            await self.redis_client.ping()
            logger.info("✅ Connected to Redis")
            
            # 清理测试数据
            await self._cleanup_test_data()
            
            # 创建TaskManager配置
            manager_config = TaskManagerConfig(
                max_concurrent_batches=10,  # 增加并发批次以支持压力测试
                batch_check_interval=2.0,   # 更频繁的检查
                task_timeout=3600,          # 1小时超时
                retry_failed_interval=60,   # 1分钟重试间隔
                cleanup_interval=300,       # 5分钟清理间隔
                max_queue_size=50000,       # 增加队列大小
                splitter_config=SplitterConfig(
                    max_batch_size=100,     # 增加批次大小
                    min_batch_size=20,
                    adaptive_sizing=True
                ),
                rate_limiter_config=RateLimitConfig(
                    max_concurrent_requests=10,  # 增加并发请求数
                    requests_per_minute=300,     # 增加每分钟请求数
                    redis_url=self.config.redis_url
                ),
                retry_config=RetryConfig(
                    max_retries=3,
                    base_delay=30.0,
                    max_delay=1800.0,
                    backoff_multiplier=2.0,
                    jitter=True
                )
            )
            
            # 创建持久化TaskManager
            self.task_manager = PersistentTaskManager(manager_config, self.config.redis_url)
            await self.task_manager.start()
            
            logger.info("✅ PersistentTaskManager started")
            
            # 记录开始时间
            self.metrics.start_time = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup test environment: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 Cleaning up test environment...")
        
        try:
            if self.task_manager:
                await self.task_manager.stop()
            
            if self.redis_client:
                await self._cleanup_test_data()
                await self.redis_client.aclose()
            
            self.metrics.end_time = datetime.now()
            
            logger.info("✅ Test environment cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        test_keys = [
            "taskmanager:pending_batches",
            "taskmanager:running_batches",
            "taskmanager:completed_batches",
            "taskmanager:failed_batches",
            "taskmanager:state"
        ]
        
        for key in test_keys:
            await self.redis_client.delete(key)
    
    def _select_random_priority(self) -> TaskPriority:
        """随机选择任务优先级"""
        rand = random.random()
        cumulative = 0
        
        for priority, probability in self.config.priority_distribution.items():
            cumulative += probability
            if rand <= cumulative:
                return priority
        
        return TaskPriority.NORMAL
    
    def _select_random_platform(self) -> str:
        """随机选择平台"""
        rand = random.random()
        cumulative = 0
        
        for platform, probability in self.config.platform_distribution.items():
            cumulative += probability
            if rand <= cumulative:
                return platform
        
        return "mercadolibre"
    
    def _generate_task_urls(self, count: int, platform: str) -> List[str]:
        """生成任务URL"""
        platform_urls = [url for url in self.test_urls if platform in url]
        
        if len(platform_urls) < count:
            # 如果不够，重复使用
            urls = platform_urls * (count // len(platform_urls) + 1)
            return urls[:count]
        else:
            return random.sample(platform_urls, count)
    
    async def submit_single_task(self, task_id: int) -> Dict[str, Any]:
        """提交单个任务"""
        try:
            start_time = time.time()
            
            # 随机选择参数
            priority = self._select_random_priority()
            platform = self._select_random_platform()
            urls = self._generate_task_urls(self.config.urls_per_task, platform)
            
            # 提交任务
            submission_id = await self.task_manager.submit_task(
                task_id=task_id,
                urls=urls,
                platform=platform,
                priority=priority,
                options={"stress_test": True, "test_id": task_id}
            )
            
            submission_time = time.time() - start_time
            
            # 记录指标
            self.metrics.tasks_submitted += 1
            self.metrics.submission_times.append(submission_time)
            
            return {
                "success": True,
                "task_id": task_id,
                "submission_id": submission_id,
                "submission_time": submission_time,
                "urls_count": len(urls),
                "priority": priority.value,
                "platform": platform
            }
            
        except Exception as e:
            self.metrics.tasks_failed += 1
            self.metrics.errors.append({
                "task_id": task_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e)
            }
    
    async def run_concurrent_submissions(self, task_count: int, concurrency: int):
        """运行并发任务提交"""
        logger.info(f"🚀 Starting concurrent submissions: {task_count} tasks, {concurrency} concurrent")
        
        semaphore = asyncio.Semaphore(concurrency)
        
        async def submit_with_semaphore(task_id: int):
            async with semaphore:
                return await self.submit_single_task(task_id)
        
        # 创建任务
        tasks = [submit_with_semaphore(i) for i in range(task_count)]
        
        # 执行任务
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 统计结果
        successful_tasks = len([r for r in results if isinstance(r, dict) and r.get("success")])
        failed_tasks = len([r for r in results if isinstance(r, dict) and not r.get("success")])
        exceptions = len([r for r in results if isinstance(r, Exception)])
        
        total_time = end_time - start_time
        throughput = task_count / total_time
        
        logger.info(f"✅ Concurrent submissions completed:")
        logger.info(f"   Total time: {total_time:.2f}s")
        logger.info(f"   Successful: {successful_tasks}")
        logger.info(f"   Failed: {failed_tasks}")
        logger.info(f"   Exceptions: {exceptions}")
        logger.info(f"   Throughput: {throughput:.2f} tasks/sec")
        
        self.metrics.throughput_samples.append(throughput)
        
        return results
    
    async def monitor_system_metrics(self):
        """监控系统指标"""
        logger.info("📊 Starting system metrics monitoring...")
        
        while self.is_running:
            try:
                # 收集TaskManager状态
                if self.task_manager:
                    status = self.task_manager.get_status()
                    self.metrics.queue_size_samples.append({
                        "timestamp": datetime.now().isoformat(),
                        "pending": status["pending_batches"],
                        "running": status["running_batches"],
                        "completed": status["completed_batches"],
                        "failed": status["failed_batches"]
                    })
                
                # 收集Redis指标
                if self.redis_client:
                    info = await self.redis_client.info()
                    self.metrics.redis_metrics_samples.append({
                        "timestamp": datetime.now().isoformat(),
                        "used_memory": info.get("used_memory", 0),
                        "connected_clients": info.get("connected_clients", 0),
                        "ops_per_sec": info.get("instantaneous_ops_per_sec", 0),
                        "keyspace_hits": info.get("keyspace_hits", 0),
                        "keyspace_misses": info.get("keyspace_misses", 0)
                    })
                
                await asyncio.sleep(self.config.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error monitoring system metrics: {e}")
                await asyncio.sleep(self.config.monitoring_interval)
    
    async def run_stress_test(self):
        """运行压力测试"""
        logger.info("🧪 Starting PersistentTaskManager Stress Test")
        logger.info("=" * 60)
        
        try:
            # 启动监控
            self.is_running = True
            monitor_task = asyncio.create_task(self.monitor_system_metrics())
            
            # 阶段1: 基础负载测试
            logger.info("\n📤 Phase 1: Basic Load Test")
            logger.info("-" * 40)
            
            await self.run_concurrent_submissions(
                task_count=100,
                concurrency=10
            )
            
            # 等待处理
            await asyncio.sleep(30)
            
            # 阶段2: 中等负载测试
            logger.info("\n⚙️ Phase 2: Medium Load Test")
            logger.info("-" * 40)
            
            await self.run_concurrent_submissions(
                task_count=500,
                concurrency=25
            )
            
            # 等待处理
            await asyncio.sleep(60)
            
            # 阶段3: 高负载测试
            logger.info("\n🔥 Phase 3: High Load Test")
            logger.info("-" * 40)
            
            await self.run_concurrent_submissions(
                task_count=1000,
                concurrency=50
            )
            
            # 等待处理
            await asyncio.sleep(120)
            
            # 阶段4: 持久化恢复测试
            logger.info("\n🔄 Phase 4: Persistence Recovery Test")
            logger.info("-" * 40)
            
            await self._test_persistence_recovery()
            
            # 停止监控
            self.is_running = False
            monitor_task.cancel()
            
            # 生成报告
            self._generate_test_report()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Stress test failed: {e}")
            return False
        
        finally:
            self.is_running = False
    
    async def _test_persistence_recovery(self):
        """测试持久化恢复"""
        logger.info("Testing persistence recovery...")
        
        # 获取重启前状态
        before_status = self.task_manager.get_status()
        logger.info(f"Before restart: {before_status['pending_batches']} pending, "
                   f"{before_status['running_batches']} running")
        
        # 停止TaskManager
        await self.task_manager.stop()
        logger.info("TaskManager stopped")
        
        # 等待一段时间
        await asyncio.sleep(5)
        
        # 重新创建并启动TaskManager
        manager_config = self.task_manager.config
        self.task_manager = PersistentTaskManager(manager_config, self.config.redis_url)
        await self.task_manager.start()
        logger.info("TaskManager restarted")
        
        # 获取恢复后状态
        after_status = self.task_manager.get_status()
        logger.info(f"After restart: {after_status['pending_batches']} pending, "
                   f"{after_status['running_batches']} running")
        
        # 验证恢复
        recovery_success = (
            before_status['pending_batches'] == after_status['pending_batches'] and
            before_status['running_batches'] == after_status['running_batches']
        )
        
        if recovery_success:
            logger.info("✅ Persistence recovery test PASSED")
        else:
            logger.error("❌ Persistence recovery test FAILED")
        
        return recovery_success
    
    def _generate_test_report(self):
        """生成测试报告"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 STRESS TEST REPORT")
        logger.info("=" * 60)
        
        # 基本统计
        total_time = (self.metrics.end_time - self.metrics.start_time).total_seconds()
        
        logger.info(f"\n📋 Test Summary:")
        logger.info(f"   Duration: {total_time:.2f} seconds")
        logger.info(f"   Tasks Submitted: {self.metrics.tasks_submitted}")
        logger.info(f"   Tasks Failed: {self.metrics.tasks_failed}")
        logger.info(f"   Success Rate: {(self.metrics.tasks_submitted / (self.metrics.tasks_submitted + self.metrics.tasks_failed)) * 100:.2f}%")
        
        # 性能指标
        if self.metrics.submission_times:
            avg_submission_time = statistics.mean(self.metrics.submission_times)
            p95_submission_time = statistics.quantiles(self.metrics.submission_times, n=20)[18]  # 95th percentile
            
            logger.info(f"\n⚡ Performance Metrics:")
            logger.info(f"   Average Submission Time: {avg_submission_time:.3f}s")
            logger.info(f"   95th Percentile Submission Time: {p95_submission_time:.3f}s")
            logger.info(f"   Min Submission Time: {min(self.metrics.submission_times):.3f}s")
            logger.info(f"   Max Submission Time: {max(self.metrics.submission_times):.3f}s")
        
        # 吞吐量
        if self.metrics.throughput_samples:
            avg_throughput = statistics.mean(self.metrics.throughput_samples)
            max_throughput = max(self.metrics.throughput_samples)
            
            logger.info(f"\n🚀 Throughput Metrics:")
            logger.info(f"   Average Throughput: {avg_throughput:.2f} tasks/sec")
            logger.info(f"   Peak Throughput: {max_throughput:.2f} tasks/sec")
        
        # 队列状态
        if self.metrics.queue_size_samples:
            final_sample = self.metrics.queue_size_samples[-1]
            max_pending = max(sample["pending"] for sample in self.metrics.queue_size_samples)
            max_running = max(sample["running"] for sample in self.metrics.queue_size_samples)
            
            logger.info(f"\n📊 Queue Metrics:")
            logger.info(f"   Final Pending: {final_sample['pending']}")
            logger.info(f"   Final Running: {final_sample['running']}")
            logger.info(f"   Final Completed: {final_sample['completed']}")
            logger.info(f"   Final Failed: {final_sample['failed']}")
            logger.info(f"   Max Pending: {max_pending}")
            logger.info(f"   Max Running: {max_running}")
        
        # 错误统计
        if self.metrics.errors:
            logger.info(f"\n❌ Error Analysis:")
            logger.info(f"   Total Errors: {len(self.metrics.errors)}")
            
            # 错误类型统计
            error_types = {}
            for error in self.metrics.errors:
                error_type = type(error.get("error", "Unknown")).__name__
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            for error_type, count in error_types.items():
                logger.info(f"   {error_type}: {count}")
        
        logger.info("\n" + "=" * 60)
        logger.info("🎯 Test completed successfully!")


async def main():
    """主函数"""
    print("🧪 PersistentTaskManager Stress Test")
    print("=" * 60)
    
    # 创建测试配置
    config = StressTestConfig()
    
    # 创建测试实例
    stress_test = PersistentTaskManagerStressTest(config)
    
    try:
        # 设置测试环境
        if not await stress_test.setup():
            sys.exit(1)
        
        # 运行压力测试
        success = await stress_test.run_stress_test()
        
        if success:
            logger.info("🎉 Stress test completed successfully!")
        else:
            logger.error("❌ Stress test failed!")
            sys.exit(1)
    
    finally:
        # 清理测试环境
        await stress_test.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
