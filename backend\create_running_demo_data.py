#!/usr/bin/env python3
"""
创建正在运行的演示数据
为Celery监控系统创建正在运行状态的任务
"""

import asyncio
import time
from datetime import datetime

# 演示配置
DEMO_TASK_ID = "2243"
DEMO_URLS = [
    "https://articulo.mercadolibre.com.mx/MLM-running-demo-1",
    "https://articulo.mercadolibre.com.mx/MLM-running-demo-2",
    "https://articulo.mercadolibre.com.mx/MLM-running-demo-3"
]


async def create_running_demo_data():
    """创建正在运行的演示数据"""
    print("🔍 创建正在运行的演示数据...")
    
    try:
        from app.services.unified_execution_service import UnifiedExecutionService
        service = UnifiedExecutionService(redis_url="redis://localhost:6379/0")
        
        # 创建执行会话
        session = await service.create_execution_session(
            monitoring_task_id=DEMO_TASK_ID,
            celery_task_id=f"celery_running_{int(time.time())}",
            execution_type="manual",
            urls=DEMO_URLS
        )
        
        print(f"✅ 创建执行会话: {session.session_id}")
        
        # 创建正在运行的URL执行记录
        workers = ["11235w", "worker_v2", "crawler_001"]
        
        for i, url in enumerate(DEMO_URLS):
            worker_id = workers[i % len(workers)]
            
            execution = await service.create_url_execution(
                session_id=session.session_id,
                monitoring_task_id=DEMO_TASK_ID,
                celery_task_id=session.celery_task_id,
                url=url,
                assigned_worker_id=worker_id,
                crawler_endpoint=f"http://localhost:11235/crawl"
            )
            
            # 设置为正在运行状态
            await service.update_url_execution_status(
                execution.execution_id,
                "running"  # 所有任务都设置为running状态
            )
            
            print(f"✅ URL执行记录: {url} -> running (Worker: {worker_id})")
        
        print(f"\n🎉 正在运行的演示数据创建完成！")
        print(f"📋 监控任务ID: {DEMO_TASK_ID}")
        print(f"📋 Celery任务ID: {session.celery_task_id}")
        print(f"📋 执行会话ID: {session.session_id}")
        print(f"📋 正在运行的URL数量: {len(DEMO_URLS)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建正在运行的演示数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_celery_monitoring_apis():
    """测试Celery监控API"""
    print(f"\n🔍 测试Celery监控API...")
    
    import requests
    
    try:
        # 1. 测试统计API
        response = requests.get("http://localhost:8000/api/v1/celery/stats")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 统计API测试成功")
            print(f"   活跃任务: {data.get('active_tasks', 0)}")
            print(f"   等待任务: {data.get('pending_tasks', 0)}")
            print(f"   在线Worker: {data.get('workers_online', 0)}")
        else:
            print(f"❌ 统计API测试失败: {response.status_code}")
        
        # 2. 测试活跃任务API
        response = requests.get("http://localhost:8000/api/v1/celery/tasks/active")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 活跃任务API测试成功")
            print(f"   任务数量: {len(data)}")
            for task in data[:3]:  # 只显示前3个
                print(f"   - {task.get('id', 'N/A')[:20]}...: {task.get('state', 'N/A')} (Worker: {task.get('worker', 'N/A')})")
        else:
            print(f"❌ 活跃任务API测试失败: {response.status_code}")
        
        # 3. 测试实时任务流API
        response = requests.get("http://localhost:8000/api/v1/celery/tasks/live-stream")
        if response.status_code == 200:
            data = response.json()
            executing_tasks = data.get('executing_tasks', [])
            print(f"✅ 实时任务流API测试成功")
            print(f"   执行中任务数量: {len(executing_tasks)}")
            for task in executing_tasks[:3]:  # 只显示前3个
                print(f"   - {task.get('id', 'N/A')[:20]}...: {task.get('name', 'N/A')} (Worker: {task.get('worker', 'N/A')})")
        else:
            print(f"❌ 实时任务流API测试失败: {response.status_code}")
        
        # 4. 测试Worker状态API
        response = requests.get("http://localhost:8000/api/v1/celery/workers")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Worker状态API测试成功")
            print(f"   Worker数量: {len(data)}")
            for worker in data:
                print(f"   - {worker.get('name', 'N/A')}: {worker.get('active_tasks', 0)} 个活跃任务")
        else:
            print(f"❌ Worker状态API测试失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery监控API测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 开始创建正在运行的演示数据\n")
    
    # 1. 创建正在运行的演示数据
    success = await create_running_demo_data()
    if not success:
        return False
    
    # 等待数据写入
    await asyncio.sleep(2)
    
    # 2. 测试Celery监控API
    api_success = test_celery_monitoring_apis()
    
    if success and api_success:
        print(f"\n🎉 正在运行的演示数据创建成功！")
        print(f"\n💡 现在可以在前端查看Celery监控与管理中心:")
        print(f"   - 访问: http://localhost:3000/celery-monitoring")
        print(f"   - 查看系统概览中的活跃任务数量")
        print(f"   - 查看实时任务监控中的任务列表")
        print(f"   - 查看Worker状态监控中的任务分配")
        print(f"\n📊 应该能看到:")
        print(f"   - ✅ 系统概览显示正确的活跃任务数量")
        print(f"   - ✅ 实时任务监控显示正在执行的任务")
        print(f"   - ✅ Worker状态监控显示任务分配情况")
        print(f"   - ✅ 爬虫请求调试显示活跃任务的请求")
        return True
    else:
        print(f"\n❌ 正在运行的演示数据创建失败")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
