import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  message,
  Input,
  Select,
  Row,
  Col,
  Card,
  Typography,
  Tooltip,
  Popconfirm
} from 'antd';
import type { Key } from 'antd/es/table/interface';
import {
  ReloadOutlined,
  StopOutlined,
  RedoOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

interface TaskInfo {
  id: string;
  name: string;
  state: string;
  args: any[];
  kwargs: any;
  result?: string;
  traceback?: string;
  timestamp: string;
  worker?: string;
  queue?: string;
}

const TaskManagementPanel: React.FC = () => {
  const [tasks, setTasks] = useState<TaskInfo[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<TaskInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [stateFilter, setStateFilter] = useState<string>('');
  const [taskDetailModal, setTaskDetailModal] = useState<{
    visible: boolean;
    task: TaskInfo | null;
  }>({ visible: false, task: null });

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/celery/tasks');
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks || []);
      } else {
        message.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取失败任务
  const fetchFailedTasks = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/celery/tasks/failed');
      if (response.ok) {
        const data = await response.json();
        setTasks(data || []);
      } else {
        message.error('获取失败任务失败');
      }
    } catch (error) {
      console.error('Failed to fetch failed tasks:', error);
      message.error('获取失败任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 撤销任务
  const revokeTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/v1/celery/tasks/${taskId}/revoke`, {
        method: 'POST'
      });
      
      if (response.ok) {
        message.success('任务撤销成功');
        fetchTasks();
      } else {
        message.error('任务撤销失败');
      }
    } catch (error) {
      console.error('Failed to revoke task:', error);
      message.error('任务撤销失败');
    }
  };

  // 重试任务
  const retryTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/v1/celery/tasks/${taskId}/retry`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const result = await response.json();
        message.success(`任务重试成功，新任务ID: ${result.new_task_id}`);
        fetchTasks();
      } else {
        message.error('任务重试失败');
      }
    } catch (error) {
      console.error('Failed to retry task:', error);
      message.error('任务重试失败');
    }
  };

  // 批量撤销任务
  const batchRevokeTasks = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要撤销的任务');
      return;
    }

    try {
      const response = await fetch('/api/v1/celery/tasks/batch/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(selectedRowKeys.map(key => String(key)))
      });
      
      if (response.ok) {
        const result = await response.json();
        message.success(result.message);
        setSelectedRowKeys([]);
        fetchTasks();
      } else {
        message.error('批量撤销失败');
      }
    } catch (error) {
      console.error('Failed to batch revoke tasks:', error);
      message.error('批量撤销失败');
    }
  };

  // 查看任务详情
  const viewTaskDetail = (task: TaskInfo) => {
    setTaskDetailModal({ visible: true, task });
  };

  // 过滤任务
  useEffect(() => {
    let filtered = tasks;

    // 按状态过滤
    if (stateFilter) {
      filtered = filtered.filter(task => task.state === stateFilter);
    }

    // 按关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(task => 
        task.name.toLowerCase().includes(keyword) ||
        task.id.toLowerCase().includes(keyword) ||
        task.state.toLowerCase().includes(keyword) ||
        (task.worker && task.worker.toLowerCase().includes(keyword))
      );
    }

    setFilteredTasks(filtered);
  }, [tasks, stateFilter, searchKeyword]);

  // 初始化数据
  useEffect(() => {
    fetchTasks();
  }, []);

  // 监听刷新事件
  useEffect(() => {
    const handleRefresh = () => fetchTasks();
    window.addEventListener('celery-refresh', handleRefresh);
    return () => window.removeEventListener('celery-refresh', handleRefresh);
  }, []);

  // 获取状态标签颜色
  const getStateColor = (state: string) => {
    switch (state) {
      case 'SUCCESS': return 'success';
      case 'FAILURE': return 'error';
      case 'PENDING': return 'warning';
      case 'STARTED': return 'processing';
      case 'REVOKED': return 'default';
      default: return 'default';
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <Tooltip title={id}>
          <Text code>{id.slice(0, 8)}...</Text>
        </Tooltip>
      )
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state: string) => (
        <Tag color={getStateColor(state)}>{state}</Tag>
      )
    },
    {
      title: 'Worker',
      dataIndex: 'worker',
      key: 'worker',
      width: 120,
      ellipsis: true
    },
    {
      title: '队列',
      dataIndex: 'queue',
      key: 'queue',
      width: 100
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (timestamp: string) => {
        try {
          return new Date(timestamp).toLocaleString();
        } catch {
          return timestamp;
        }
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: TaskInfo) => (
        <Space size="small">
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => viewTaskDetail(record)}
          >
            详情
          </Button>
          
          {record.state === 'FAILURE' && (
            <Button
              size="small"
              icon={<RedoOutlined />}
              onClick={() => retryTask(record.id)}
            >
              重试
            </Button>
          )}
          
          {['PENDING', 'STARTED'].includes(record.state) && (
            <Popconfirm
              title="确定要撤销这个任务吗？"
              onConfirm={() => revokeTask(record.id)}
            >
              <Button
                size="small"
                icon={<StopOutlined />}
                danger
              >
                撤销
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="task-management-panel">
      {/* 控制面板 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="搜索任务ID、名称、Worker"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              allowClear
            />
          </Col>
          
          <Col span={4}>
            <Select
              placeholder="任务状态"
              value={stateFilter}
              onChange={setStateFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="SUCCESS">成功</Option>
              <Option value="FAILURE">失败</Option>
              <Option value="PENDING">等待</Option>
              <Option value="STARTED">执行中</Option>
              <Option value="REVOKED">已撤销</Option>
            </Select>
          </Col>
          
          <Col span={8}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchTasks}
                loading={loading}
              >
                刷新
              </Button>
              
              <Button
                onClick={fetchFailedTasks}
                loading={loading}
              >
                失败任务
              </Button>
              
              <Popconfirm
                title={`确定要撤销选中的 ${selectedRowKeys.length} 个任务吗？`}
                onConfirm={batchRevokeTasks}
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  icon={<StopOutlined />}
                  danger
                  disabled={selectedRowKeys.length === 0}
                >
                  批量撤销 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            </Space>
          </Col>
          
          <Col span={6} style={{ textAlign: 'right' }}>
            <Text type="secondary">
              显示: {filteredTasks.length} / {tasks.length} 个任务
            </Text>
          </Col>
        </Row>
      </Card>

      {/* 任务表格 */}
      <Table
        columns={columns}
        dataSource={filteredTasks}
        rowKey="id"
        loading={loading}
        size="small"
        scroll={{ x: 800 }}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
          getCheckboxProps: (record) => ({
            disabled: !['PENDING', 'STARTED'].includes(record.state)
          })
        }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
        }}
      />

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={taskDetailModal.visible}
        onCancel={() => setTaskDetailModal({ visible: false, task: null })}
        footer={null}
        width={800}
      >
        {taskDetailModal.task && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>任务ID:</Text> {taskDetailModal.task.id}
              </Col>
              <Col span={12}>
                <Text strong>状态:</Text> 
                <Tag color={getStateColor(taskDetailModal.task.state)} style={{ marginLeft: 8 }}>
                  {taskDetailModal.task.state}
                </Tag>
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={24}>
                <Text strong>任务名称:</Text>
                <div style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                  <Text code>{taskDetailModal.task.name}</Text>
                </div>
              </Col>
            </Row>
            
            {taskDetailModal.task.args && taskDetailModal.task.args.length > 0 && (
              <Row style={{ marginTop: 16 }}>
                <Col span={24}>
                  <Text strong>参数:</Text>
                  <pre style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                    {JSON.stringify(taskDetailModal.task.args, null, 2)}
                  </pre>
                </Col>
              </Row>
            )}
            
            {taskDetailModal.task.result && (
              <Row style={{ marginTop: 16 }}>
                <Col span={24}>
                  <Text strong>结果:</Text>
                  <pre style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                    {taskDetailModal.task.result}
                  </pre>
                </Col>
              </Row>
            )}
            
            {taskDetailModal.task.traceback && (
              <Row style={{ marginTop: 16 }}>
                <Col span={24}>
                  <Text strong>错误堆栈:</Text>
                  <pre style={{ 
                    marginTop: 8, 
                    padding: 8, 
                    background: '#fff2f0', 
                    borderRadius: 4,
                    color: '#ff4d4f',
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    {taskDetailModal.task.traceback}
                  </pre>
                </Col>
              </Row>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TaskManagementPanel;
