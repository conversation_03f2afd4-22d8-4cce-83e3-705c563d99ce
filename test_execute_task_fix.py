#!/usr/bin/env python3
"""
测试任务详情界面"立即执行"功能修复
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def test_execute_task_fix():
    """测试立即执行功能修复"""
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 首先获取现有的监控任务列表
        logger.info("1. 获取监控任务列表...")
        async with session.get(f"{API_BASE}/monitoring-tasks") as response:
            if response.status == 200:
                data = await response.json()
                tasks = data.get("data", [])
                logger.info(f"找到 {len(tasks)} 个监控任务")
                
                if not tasks:
                    logger.error("没有找到监控任务，请先创建一个任务")
                    return False
                
                # 选择第一个任务进行测试
                test_task = tasks[0]
                task_id = test_task["id"]
                logger.info(f"选择任务进行测试: {task_id} - {test_task.get('name', 'Unknown')}")
                
            else:
                logger.error(f"获取任务列表失败: {response.status}")
                return False
        
        # 2. 获取任务详情，确保任务有URL
        logger.info(f"2. 获取任务详情: {task_id}")
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}") as response:
            if response.status == 200:
                task_detail = await response.json()
                logger.info(f"任务详情: {task_detail.get('name', 'Unknown')}")
                logger.info(f"任务状态: {'运行中' if task_detail.get('is_running') else '未运行'}")
                
                # 检查任务是否有URL
                urls = task_detail.get("urls", [])
                logger.info(f"任务包含 {len(urls)} 个URL")
                
                if len(urls) == 0:
                    logger.warning("任务没有URL，可能无法执行")
                
            else:
                logger.error(f"获取任务详情失败: {response.status}")
                return False
        
        # 3. 测试立即执行功能
        logger.info(f"3. 测试立即执行任务: {task_id}")
        
        # 检查任务是否正在运行
        if task_detail.get("is_running"):
            logger.warning("任务正在运行中，先尝试终止...")
            async with session.post(f"{API_BASE}/monitoring-tasks/{task_id}/terminate") as response:
                if response.status == 200:
                    logger.info("任务已终止")
                    await asyncio.sleep(2)  # 等待任务完全停止
                else:
                    logger.warning(f"终止任务失败: {response.status}")
        
        # 执行任务
        async with session.post(f"{API_BASE}/monitoring-tasks/{task_id}/execute") as response:
            if response.status == 200:
                result = await response.json()
                logger.info("✅ 立即执行请求成功!")
                logger.info(f"执行ID: {result.get('execution_id')}")
                logger.info(f"提交ID: {result.get('submission_id', 'N/A')}")
                logger.info(f"URL数量: {result.get('url_count')}")
                logger.info(f"消息: {result.get('message')}")
                
                # 检查是否有submission_id，这表明任务已提交到TaskManager
                if result.get('submission_id'):
                    logger.info("✅ 任务已成功提交到TaskManager")
                else:
                    logger.warning("⚠️ 任务执行成功但没有submission_id")
                
            else:
                error_text = await response.text()
                logger.error(f"❌ 立即执行失败: {response.status}")
                logger.error(f"错误信息: {error_text}")
                return False
        
        # 4. 检查任务状态变化
        logger.info("4. 检查任务状态变化...")
        await asyncio.sleep(2)  # 等待状态更新
        
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}") as response:
            if response.status == 200:
                updated_task = await response.json()
                is_running = updated_task.get("is_running", False)
                last_run = updated_task.get("last_run")
                
                logger.info(f"任务运行状态: {'运行中' if is_running else '未运行'}")
                logger.info(f"最后运行时间: {last_run}")
                
                if is_running:
                    logger.info("✅ 任务状态已更新为运行中")
                else:
                    logger.warning("⚠️ 任务状态未更新为运行中")
                
            else:
                logger.error(f"获取更新后的任务状态失败: {response.status}")
        
        # 5. 检查执行历史
        logger.info("5. 检查执行历史...")
        async with session.get(f"{API_BASE}/monitoring-tasks/{task_id}/history") as response:
            if response.status == 200:
                history = await response.json()
                executions = history.get("data", [])
                logger.info(f"找到 {len(executions)} 条执行记录")
                
                if executions:
                    latest_execution = executions[0]  # 假设按时间倒序
                    logger.info(f"最新执行记录:")
                    logger.info(f"  - 执行ID: {latest_execution.get('id')}")
                    logger.info(f"  - 状态: {latest_execution.get('status')}")
                    logger.info(f"  - 触发方式: {latest_execution.get('trigger')}")
                    logger.info(f"  - URL数量: {latest_execution.get('url_count')}")
                    logger.info(f"  - 开始时间: {latest_execution.get('start_time')}")
                    
                    if latest_execution.get('trigger') == 'manual':
                        logger.info("✅ 找到手动触发的执行记录")
                    else:
                        logger.warning("⚠️ 最新执行记录不是手动触发")
                
            else:
                logger.error(f"获取执行历史失败: {response.status}")
        
        # 6. 检查Celery任务状态（如果可能）
        logger.info("6. 检查任务管理器状态...")
        async with session.get(f"{API_BASE}/tasks/status") as response:
            if response.status == 200:
                task_manager_status = await response.json()
                logger.info(f"任务管理器状态: {task_manager_status.get('status')}")
                logger.info(f"待处理批次: {task_manager_status.get('pending_batches')}")
                logger.info(f"运行中批次: {task_manager_status.get('running_batches')}")
                logger.info(f"已完成批次: {task_manager_status.get('completed_batches')}")
                
                if task_manager_status.get('running_batches', 0) > 0:
                    logger.info("✅ 任务管理器中有运行中的批次")
                else:
                    logger.info("ℹ️ 任务管理器中没有运行中的批次")
                
            else:
                logger.warning(f"获取任务管理器状态失败: {response.status}")
        
        logger.info("🎉 测试完成!")
        return True

async def main():
    """主函数"""
    logger.info("开始测试任务详情界面'立即执行'功能修复...")
    logger.info("=" * 60)
    
    try:
        success = await test_execute_task_fix()
        
        if success:
            logger.info("✅ 测试成功完成!")
            logger.info("立即执行功能已修复，现在应该能正确向Celery worker发送任务")
        else:
            logger.error("❌ 测试失败!")
            logger.error("立即执行功能仍有问题，需要进一步调试")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
