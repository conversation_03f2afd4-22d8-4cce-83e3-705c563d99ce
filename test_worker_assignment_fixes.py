#!/usr/bin/env python3
"""
测试Worker指派面板修复功能的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_single_worker_removal():
    """测试单个Worker移除功能"""

    print("🧪 测试单个Worker移除功能...")

    task_id = "success"

    # 1. 获取当前任务的Worker列表
    print(f"\n📋 获取任务 {task_id} 的Worker列表...")
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/task/{task_id}/assigned-workers")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 当前有 {len(workers)} 个Worker:")
            for i, worker in enumerate(workers, 1):
                print(f"  {i}. {worker['worker_name']} ({worker['worker_id']})")
                print(f"     分配ID: {worker['assignment_id']}")

            if len(workers) >= 2:
                # 选择第一个Worker进行移除测试
                target_worker = workers[0]
                worker_id = target_worker['worker_id']
                worker_name = target_worker['worker_name']

                print(f"\n🎯 测试移除Worker: {worker_name}")

                # 2. 移除单个Worker
                remove_response = requests.delete(f"{BASE_URL}/task-assignments/task/{task_id}/worker/{worker_id}")

                print(f"移除响应状态码: {remove_response.status_code}")

                if remove_response.status_code == 200:
                    result = remove_response.json()
                    print(f"✅ 移除成功: {result['message']}")
                    return True
                else:
                    print(f"❌ 移除失败: {remove_response.text}")
                    return False
            else:
                print("❌ Worker数量不足，需要至少2个Worker进行测试")
                return False
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def get_task_assignments_detailed(task_id):
    """获取任务分配的详细信息"""
    
    print(f"📋 获取任务 {task_id} 的详细分配信息...")
    
    try:
        # 1. 获取分配摘要列表
        response = requests.get(f"{BASE_URL}/task-assignments/task/{task_id}/assignments")
        
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ 获取到 {len(assignments)} 个分配摘要")
            
            detailed_assignments = []
            
            # 2. 获取每个分配的详细信息
            for assignment in assignments:
                assignment_id = assignment.get('assignment_id')
                print(f"  📄 获取分配 {assignment_id} 的详细信息...")
                
                detail_response = requests.get(f"{BASE_URL}/task-assignments/{assignment_id}")
                
                if detail_response.status_code == 200:
                    detail = detail_response.json()
                    detailed_assignments.append(detail)
                    
                    worker_ids = detail.get('worker_ids', [])
                    print(f"    ✅ Worker IDs: {worker_ids}")
                    print(f"    ✅ 状态: {detail.get('status')}")
                    print(f"    ✅ 分配名称: {detail.get('assignment_name')}")
                else:
                    print(f"    ❌ 获取详细信息失败: {detail_response.text}")
            
            return detailed_assignments
        else:
            print(f"❌ 获取分配列表失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []

def test_multi_worker_assignment():
    """测试多Worker指派"""
    
    print(f"\n🧪 测试多Worker指派...")
    
    # 任务ID
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    # 获取可用Worker
    try:
        workers_response = requests.get(f"{BASE_URL}/crawler-workers/")
        if workers_response.status_code == 200:
            workers = workers_response.json()
            if len(workers) >= 2:
                # 选择前两个Worker
                worker_ids = [workers[0]['worker_id'], workers[1]['worker_id']]
                worker_names = [workers[0]['worker_name'], workers[1]['worker_name']]
                
                print(f"选择的Worker: {worker_names} ({worker_ids})")
                
                # 创建多Worker分配
                assignment_data = {
                    "task_id": task_id,
                    "worker_ids": worker_ids,
                    "assignment_name": "多Worker测试分配",
                    "description": "测试多Worker指派功能",
                    "assignment_strategy": "health_based",
                    "auto_failover": True,
                    "total_urls": 49
                }
                
                print(f"创建多Worker分配...")
                response = requests.post(
                    f"{BASE_URL}/task-assignments/",
                    json=assignment_data,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    assignment = response.json()
                    print("✅ 多Worker分配创建成功!")
                    print(f"  分配ID: {assignment.get('assignment_id')}")
                    print(f"  Worker数量: {len(assignment.get('worker_ids', []))}")
                    print(f"  Worker IDs: {assignment.get('worker_ids')}")
                    return assignment.get('assignment_id')
                elif response.status_code == 400:
                    error_detail = response.json().get('detail', '')
                    print(f"❌ 分配被拒绝: {error_detail}")
                    return None
                else:
                    print(f"❌ 意外错误: {response.text}")
                    return None
            else:
                print("❌ 可用Worker数量不足")
                return None
        else:
            print(f"❌ 获取Worker列表失败: {workers_response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_duplicate_assignment_prevention():
    """测试重复指派防护"""
    
    print(f"\n🛡️ 测试重复指派防护...")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    # 获取现有分配
    existing_assignments = get_task_assignments_detailed(task_id)
    
    if existing_assignments:
        # 尝试重复指派第一个分配中的Worker
        first_assignment = existing_assignments[0]
        worker_ids = first_assignment.get('worker_ids', [])
        
        if worker_ids:
            print(f"尝试重复指派Worker: {worker_ids}")
            
            assignment_data = {
                "task_id": task_id,
                "worker_ids": worker_ids,
                "assignment_name": "重复指派测试",
                "description": "测试重复指派防护功能",
                "assignment_strategy": "health_based",
                "auto_failover": True,
                "total_urls": 49
            }
            
            response = requests.post(
                f"{BASE_URL}/task-assignments/",
                json=assignment_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 400:
                error_detail = response.json().get('detail', '')
                print(f"✅ 重复指派被正确阻止: {error_detail}")
                return True
            elif response.status_code == 200:
                print("❌ 重复指派未被阻止！这是一个问题")
                return False
            else:
                print(f"❓ 意外响应: {response.text}")
                return False
        else:
            print("❌ 现有分配中没有Worker ID")
            return False
    else:
        print("❌ 没有现有分配可供测试")
        return False

def main():
    """主函数"""
    print("🚀 开始测试Worker指派面板修复功能\n")
    
    task_id = "20baf174-bb0d-4d45-b931-d1580bac02da"
    
    # 1. 获取当前分配的详细信息
    print("=" * 50)
    detailed_assignments = get_task_assignments_detailed(task_id)
    
    # 2. 测试多Worker指派
    print("=" * 50)
    multi_assignment_id = test_multi_worker_assignment()
    
    # 3. 测试重复指派防护
    print("=" * 50)
    duplicate_prevention_works = test_duplicate_assignment_prevention()
    
    # 4. 再次获取分配信息，查看变化
    print("=" * 50)
    print(f"\n📊 测试后的分配状态:")
    final_assignments = get_task_assignments_detailed(task_id)
    
    # 5. 总结测试结果
    print("=" * 50)
    print(f"\n🎯 测试结果总结:")
    print(f"  初始分配数量: {len(detailed_assignments)}")
    print(f"  最终分配数量: {len(final_assignments)}")
    print(f"  多Worker分配: {'成功' if multi_assignment_id else '失败'}")
    print(f"  重复指派防护: {'正常' if duplicate_prevention_works else '失效'}")
    
    # 检查Worker ID的完整性
    print(f"\n📋 Worker ID 详细信息:")
    for i, assignment in enumerate(final_assignments, 1):
        worker_ids = assignment.get('worker_ids', [])
        print(f"  分配 {i}: {len(worker_ids)} 个Worker - {worker_ids}")

if __name__ == "__main__":
    main()
