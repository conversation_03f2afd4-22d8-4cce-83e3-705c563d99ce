# 任务调度与管理系统使用指南

## 概述

MonIt任务调度与管理系统是专门为解决外部爬虫API限制问题而设计的高性能任务调度系统。

### 核心挑战
- **外部API限制**: 最多2个URL并行处理，单次API调用最多100个URL
- **业务需求**: 需要处理1000+商品URL的监控任务
- **解决方案**: 智能任务分片 + 限流控制 + 分布式任务队列

## 系统架构

```
用户请求 → 任务分片器 → 任务队列 → 工作进程 → 外部API → 结果聚合
    ↓           ↓          ↓        ↓         ↓         ↓
  1000+ URLs  100个/片   Redis队列  Celery   2并发限制  数据库存储
```

## 核心组件

### 1. 任务分片器 (TaskSplitter)
- **功能**: 将大批量URL任务分解为小批次
- **配置**: 最大批次大小100个URL，最小批次大小10个URL
- **特性**: 支持优先级队列、负载均衡、自适应分片

### 2. 限流控制器 (RateLimiter)
- **功能**: 严格控制对外部API的调用频率
- **限制**: 最大2个并发请求，每分钟60次，每小时1000次
- **特性**: 熔断机制、降级策略、Redis状态管理

### 3. 任务管理器 (TaskManager)
- **功能**: 协调任务分片、限流控制和Celery任务执行
- **特性**: 异步处理、状态监控、故障恢复

### 4. Celery任务队列
- **队列类型**:
  - `crawler_queue`: 普通爬取任务
  - `crawler_queue_high`: 高优先级爬取任务
  - `monitor_queue`: 监控任务
  - `processor_queue`: 结果处理任务
  - `cleanup_queue_low`: 清理任务

## 快速开始

### 1. 环境准备

```bash
# 启动Redis
redis-server

# 启动数据库
docker-compose up timescaledb

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动Celery Workers

```bash
# 启动所有worker
chmod +x backend/scripts/start_workers.sh
./backend/scripts/start_workers.sh start

# 检查状态
./backend/scripts/start_workers.sh status
```

### 3. 启动任务管理器

```bash
# 正常模式
python backend/scripts/start_task_manager.py

# 测试模式
python backend/scripts/start_task_manager.py test
```

### 4. 提交任务

```python
from app.core.task_manager import TaskManager
from app.core.task_splitter import TaskPriority

# 创建任务管理器
manager = TaskManager()
await manager.start()

# 提交大批量任务
urls = [f"https://mercadolibre.com.ar/product/{i}" for i in range(1500)]
submission_id = await manager.submit_task(
    task_id=1,
    urls=urls,
    platform="mercadolibre",
    priority=TaskPriority.NORMAL
)

# 提交单URL高优先级任务
task_id = await manager.submit_single_url(
    url="https://mercadolibre.com.ar/product/urgent",
    priority=TaskPriority.URGENT
)
```

## 配置说明

### 任务分片器配置

```python
from app.core.task_splitter import SplitterConfig

config = SplitterConfig(
    max_batch_size=100,          # 最大批次大小
    max_concurrent_batches=2,    # 最大并发批次数
    min_batch_size=10,           # 最小批次大小
    adaptive_sizing=True,        # 自适应分片大小
    load_balance_enabled=True    # 负载均衡
)
```

### 限流器配置

```python
from app.core.rate_limiter import RateLimitConfig

config = RateLimitConfig(
    max_concurrent_requests=2,    # 最大并发请求数
    requests_per_minute=60,       # 每分钟最大请求数
    requests_per_hour=1000,       # 每小时最大请求数
    failure_threshold=5,          # 失败阈值
    failure_rate_threshold=0.5,   # 失败率阈值
    recovery_timeout=60           # 恢复超时时间
)
```

### 任务管理器配置

```python
from app.core.task_manager import TaskManagerConfig

config = TaskManagerConfig(
    max_concurrent_batches=2,     # 最大并发批次数
    batch_check_interval=5.0,     # 批次检查间隔
    task_timeout=1800,            # 任务超时时间
    max_queue_size=10000          # 最大队列大小
)
```

## 监控和管理

### 1. 查看系统状态

```python
# 获取任务管理器状态
status = manager.get_status()
print(f"Pending: {status['pending_batches']}")
print(f"Running: {status['running_batches']}")
print(f"Completed: {status['completed_batches']}")

# 获取限流器指标
metrics = manager.rate_limiter.get_metrics()
print(f"Success rate: {metrics['success_rate']}")
print(f"Circuit state: {metrics['circuit_state']}")
```

### 2. Celery监控

```bash
# 查看活跃任务
celery -A app.celery_app inspect active

# 查看统计信息
celery -A app.celery_app inspect stats

# 启动Flower监控界面
celery -A app.celery_app flower
```

### 3. 队列监控

```bash
# 检查队列长度
redis-cli llen crawler_queue
redis-cli llen crawler_queue_high
redis-cli llen monitor_queue
```

## 性能优化

### 1. 批次大小调优
- 小批次: 更快的响应时间，但开销更大
- 大批次: 更高的吞吐量，但响应时间更长
- 建议: 根据业务需求在50-100之间调整

### 2. 并发控制
- 严格遵守外部API限制（2并发）
- 可以通过增加worker数量来提高处理能力
- 注意Redis和数据库的连接数限制

### 3. 内存优化
- 定期清理完成的任务记录
- 使用Redis过期策略
- 监控内存使用情况

## 故障排除

### 1. 常见问题

**问题**: 任务堆积在队列中不执行
**解决**: 检查worker是否正常运行，检查Redis连接

**问题**: 大量任务失败
**解决**: 检查外部API状态，查看限流器熔断状态

**问题**: 内存使用过高
**解决**: 减少批次大小，增加清理频率

### 2. 日志查看

```bash
# 查看任务管理器日志
tail -f logs/task_manager.log

# 查看Celery worker日志
tail -f logs/celery/crawler_worker.log

# 查看系统日志
tail -f logs/system.log
```

### 3. 重启服务

```bash
# 重启所有worker
./backend/scripts/start_workers.sh restart

# 重启任务管理器
# 先停止当前进程，然后重新启动
python backend/scripts/start_task_manager.py
```

## 最佳实践

### 1. 任务提交
- 优先使用批量提交而不是单个URL提交
- 合理设置任务优先级
- 避免在高峰期提交大量任务

### 2. 监控告警
- 设置队列长度告警
- 监控失败率和响应时间
- 定期检查系统资源使用情况

### 3. 维护管理
- 定期清理过期数据
- 备份重要配置和数据
- 更新外部API配置

## API参考

### TaskManager主要方法

```python
# 启动管理器
await manager.start()

# 提交任务
submission_id = await manager.submit_task(task_id, urls, platform, priority)

# 提交单URL任务
task_id = await manager.submit_single_url(url, platform, priority)

# 获取状态
status = manager.get_status()
task_status = await manager.get_task_status(task_id)

# 停止管理器
await manager.stop()
```

### RateLimiter主要方法

```python
# 获取许可
success = await limiter.acquire(request_id)

# 释放许可
await limiter.release(request_id, success, response_time)

# 获取当前限制状态
limits = await limiter.get_current_limits()

# 获取指标
metrics = limiter.get_metrics()
```

## 扩展开发

### 1. 自定义任务类型
继承`CrawlBatchTask`基类，实现自定义的任务处理逻辑。

### 2. 自定义监控指标
扩展`TaskMetrics`类，添加业务相关的监控指标。

### 3. 自定义告警规则
在`monitor_tasks.py`中添加自定义的告警逻辑。

---

更多详细信息请参考源代码注释和测试用例。
