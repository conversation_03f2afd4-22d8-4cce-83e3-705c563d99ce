#!/usr/bin/env python3
"""
测试Pydantic模型

直接测试ScheduleConfig模型的验证行为
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.models.monitoring_task import ScheduleConfig

def test_schedule_config():
    """测试ScheduleConfig模型"""
    print("🔍 测试ScheduleConfig模型")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "测试1: 基本daily配置",
            "data": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "timezone": "Asia/Shanghai"
            }
        },
        {
            "name": "测试2: 包含end_time的配置",
            "data": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "end_time": "18:00",
                "timezone": "Asia/Shanghai"
            }
        },
        {
            "name": "测试3: 包含空字符串的配置",
            "data": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "start_time": "",
                "end_time": "",
                "timezone": "Asia/Shanghai"
            }
        },
        {
            "name": "测试4: 包含None值的配置",
            "data": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "start_time": None,
                "end_time": None,
                "timezone": "Asia/Shanghai"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 {test_case['name']}")
        
        try:
            # 尝试创建ScheduleConfig实例
            config = ScheduleConfig(**test_case['data'])
            print(f"  ✅ 验证成功")
            print(f"  结果: {config.model_dump()}")
            
        except Exception as e:
            print(f"  ❌ 验证失败: {e}")
            print(f"  错误类型: {type(e).__name__}")
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    test_schedule_config()
