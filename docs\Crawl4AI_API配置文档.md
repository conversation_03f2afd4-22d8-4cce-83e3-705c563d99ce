# Crawl4AI 爬虫API配置文档

**文档版本**: 1.0  
**创建时间**: 2025年7月20日  
**基于**: LinksToMonit/crawlerstuff/API_request/ 中的实际API请求格式  

## 📋 API基本信息

### 端点信息
- **主要端点**: `/api/crawl/job` (任务管理器模式)
- **流式端点**: `/crawl/stream` (流式处理模式)
- **请求方法**: POST
- **内容类型**: application/json
- **默认端口**: 11234 (可配置)

### 请求结构
```json
{
  "urls": ["url1", "url2", ...],
  "browser_config": { ... },
  "crawler_config": { ... }
}
```

## 🌐 浏览器配置 (browser_config)

### 基础设置
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `headless` | boolean | true | 是否无头模式运行 |
| `verbose` | boolean | false | 是否输出详细日志 |
| `viewport_width` | number | 1920 | 浏览器视口宽度 |
| `viewport_height` | number | 1080 | 浏览器视口高度 |
| `wait_for` | number | 2 | 页面加载等待时间(秒) |
| `timeout` | number | 30 | 页面超时时间(秒) |
| `ignore_https_errors` | boolean | true | 忽略HTTPS错误 |

### 反检测参数
```json
"extra_args": [
  "--no-sandbox",
  "--disable-dev-shm-usage", 
  "--disable-blink-features=AutomationControlled",
  "--exclude-switches=enable-automation"
]
```

## 🕷️ 爬虫配置 (crawler_config)

### 核心执行参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `method` | string | "arun_many" | 执行方法 |
| `verbose` | boolean | true | 详细输出 |
| `simulate_user` | boolean | true | 模拟用户行为 |
| `magic` | boolean | true | 启用智能处理 |
| `override_navigator` | boolean | true | 覆盖导航器信息 |

### 页面处理参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `remove_overlay_elements` | boolean | true | 移除覆盖元素 |
| `ignore_body_visibility` | boolean | true | 忽略body可见性 |
| `adjust_viewport_to_content` | boolean | true | 调整视口适应内容 |
| `wait_until` | string | "domcontentloaded" | 等待条件 |
| `wait_for_images` | boolean | false | 等待图片加载 |
| `page_timeout` | number | 60000 | 页面超时(毫秒) |
| `delay_before_return_html` | number | 0.1 | 返回HTML前延迟 |

### 内容抓取参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `js_only` | boolean | false | 仅执行JS |
| `scan_full_page` | boolean | true | 扫描整页 |
| `process_iframes` | boolean | true | 处理iframe |
| `scroll_delay` | number | 0.2 | 滚动延迟 |
| `cache_mode` | string | "BYPASS" | 缓存模式 |

### 输出控制参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `screenshot` | boolean | false | 截图 |
| `pdf` | boolean | false | 生成PDF |
| `capture_mhtml` | boolean | false | 捕获MHTML |
| `exclude_external_images` | boolean | false | 排除外部图片 |
| `exclude_all_images` | boolean | false | 排除所有图片 |

### 质量控制参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `image_score_threshold` | number | 50 | 图片质量阈值 |
| `image_description_min_word_threshold` | number | 50 | 图片描述最小词数 |
| `table_score_threshold` | number | 7 | 表格质量阈值 |

### 监控和调试参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `capture_network_requests` | boolean | false | 捕获网络请求 |
| `capture_console_messages` | boolean | false | 捕获控制台消息 |
| `log_console` | boolean | false | 记录控制台日志 |

## 🤖 LLM提取配置 (llm_extraction)

### 基础配置
| 参数 | 类型 | 说明 |
|------|------|------|
| `query` | string | 提取查询指令 |
| `provider` | string | LLM提供商 (openai) |
| `model` | string | 模型名称 (deepseek-v3-0324) |
| `api_key` | string | API密钥 |
| `base_url` | string | API基础URL |

### 生成参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `temperature` | number | 0 | 生成温度 |
| `max_tokens` | number | 16384 | 最大token数 |
| `top_p` | number | 0.9 | Top-p采样 |

## 📊 结构化提取配置 (schema_extraction)

### 电商商品信息提取Schema
系统预定义了完整的电商商品信息提取结构，包括：

#### 核心数据结构
- **task_info**: 任务执行信息
- **product_basic_info**: 商品基础信息
- **pricing_info**: 价格和库存信息  
- **category_info**: 商品分类信息
- **seller_info**: 销售商信息
- **media_info**: 商品媒体信息
- **qa_section**: 问答区域信息
- **rating_info**: 评分信息
- **reviews_section**: 评论区域信息

#### 提取指令
系统包含详细的提取指令，确保：
1. 只提取主要商品信息
2. 忽略推荐和广告商品
3. 确保数据与URL匹配
4. 提取完整的商品属性

## 🔧 内容处理配置 (content_processing)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `word_count_threshold` | number | 200 | 词数阈值 |
| `css_selector` | string | "" | CSS选择器 |
| `excluded_tags` | array | ["nav","footer","aside"] | 排除标签 |
| `remove_forms` | boolean | false | 移除表单 |
| `only_text` | boolean | false | 仅文本 |
| `prettify` | boolean | false | 美化输出 |
| `parser_type` | string | "lxml" | 解析器类型 |

## 🔗 链接过滤配置 (link_filtering)

### 链接控制
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `exclude_external_links` | boolean | false | 排除外部链接 |
| `exclude_internal_links` | boolean | false | 排除内部链接 |
| `exclude_social_media_links` | boolean | false | 排除社交媒体链接 |

### 域名过滤
```json
"exclude_domains": [
  "example.com",
  "ads.google.com", 
  "facebook.com",
  "twitter.com"
]
```

### 社交媒体域名
```json
"social_media_domains": [
  "facebook.com", "twitter.com", "instagram.com",
  "linkedin.com", "youtube.com", "tiktok.com",
  "pinterest.com", "reddit.com"
]
```

## ⚙️ 任务调度配置

### 并发控制
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `semaphore_count` | number | 2 | 信号量计数 |
| `mean_delay` | number | 0.1 | 平均延迟 |
| `max_range` | number | 3 | 最大范围 |

### 调度器配置
```json
"scheduler": {
  "type": "AsyncQueueManager",
  "pool_size": 10,
  "memory_threshold": 4096
}
```

### 监控配置
```json
"monitor": {
  "display_mode": "detailed",
  "show_progress": true,
  "log_errors": true
}
```

## 🎯 配置策略建议

### 高性能配置
- `semaphore_count`: 5-10
- `pool_size`: 20
- `page_timeout`: 30000
- `headless`: true

### 高质量配置  
- `wait_for`: 3-5
- `scroll_delay`: 0.5
- `image_score_threshold`: 70
- `simulate_user`: true

### 反检测配置
- 启用所有反检测参数
- 随机化延迟时间
- 使用真实用户代理
- 启用`magic`模式
