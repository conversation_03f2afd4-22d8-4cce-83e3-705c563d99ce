#!/usr/bin/env python3
"""
测试统一执行存储结构
验证监控任务、URL、Worker、Celery任务之间的关联关系
"""

import asyncio
import json
import time
import requests
from datetime import datetime

# 测试配置
BACKEND_URL = "http://localhost:8000"
TEST_MONITORING_TASK_ID = "test_task_unified_" + str(int(time.time()))
TEST_CELERY_TASK_ID = "celery_test_" + str(int(time.time()))
TEST_URLS = [
    "https://articulo.mercadolibre.com.mx/MLM-test-unified-1",
    "https://articulo.mercadolibre.com.mx/MLM-test-unified-2"
]


async def test_unified_execution_service():
    """测试统一执行服务"""
    print("🔍 测试统一执行服务...")
    
    try:
        from app.services.unified_execution_service import UnifiedExecutionService
        # 使用本地Redis连接
        unified_execution_service = UnifiedExecutionService(redis_url="redis://localhost:6379/0")
        
        # 1. 创建执行会话
        print("1. 创建执行会话...")
        session = await unified_execution_service.create_execution_session(
            monitoring_task_id=TEST_MONITORING_TASK_ID,
            celery_task_id=TEST_CELERY_TASK_ID,
            execution_type="manual",
            urls=TEST_URLS
        )
        print(f"✅ 执行会话创建成功: {session.session_id}")
        
        # 2. 创建URL执行记录
        print("2. 创建URL执行记录...")
        url_executions = []
        for i, url in enumerate(TEST_URLS):
            execution = await unified_execution_service.create_url_execution(
                session_id=session.session_id,
                monitoring_task_id=TEST_MONITORING_TASK_ID,
                celery_task_id=TEST_CELERY_TASK_ID,
                url=url,
                assigned_worker_id=f"test_worker_{i+1}",
                crawler_endpoint=f"http://localhost:1123{i+5}/crawl"
            )
            url_executions.append(execution)
            print(f"✅ URL执行记录创建成功: {execution.execution_id}")
        
        # 3. 更新URL执行状态
        print("3. 更新URL执行状态...")
        await unified_execution_service.update_url_execution_status(
            url_executions[0].execution_id,
            "completed",
            response_time=2.5
        )
        print(f"✅ URL执行状态更新成功: {url_executions[0].execution_id} -> completed")
        
        await unified_execution_service.update_url_execution_status(
            url_executions[1].execution_id,
            "failed",
            response_time=1.2,
            error_message="Connection timeout"
        )
        print(f"✅ URL执行状态更新成功: {url_executions[1].execution_id} -> failed")
        
        # 4. 查询URL当前执行状态
        print("4. 查询URL执行状态...")
        for url in TEST_URLS:
            status = await unified_execution_service.get_current_url_execution_status(url)
            if status:
                print(f"✅ URL状态查询成功: {url} -> {status['status']}")
            else:
                print(f"⚠️  URL状态未找到: {url}")
        
        # 5. 查询监控任务执行链路
        print("5. 查询监控任务执行链路...")
        chain = await unified_execution_service.get_monitoring_task_execution_chain(TEST_MONITORING_TASK_ID)
        print(f"✅ 执行链路查询成功: {chain['total_sessions']} 个会话")
        
        # 6. 查询Worker负载
        print("6. 查询Worker负载...")
        for i in range(2):
            worker_id = f"test_worker_{i+1}"
            load = await unified_execution_service.get_worker_current_load(worker_id)
            print(f"✅ Worker负载查询成功: {worker_id} -> {load['current_tasks']} 个任务")
        
        # 7. 查询Celery任务执行信息
        print("7. 查询Celery任务执行信息...")
        celery_info = await unified_execution_service.get_celery_task_execution_info(TEST_CELERY_TASK_ID)
        print(f"✅ Celery任务信息查询成功: {celery_info['total_sessions']} 个会话")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一执行服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_unified_execution_api():
    """测试统一执行API"""
    print("\n🔍 测试统一执行API...")
    
    try:
        # 1. 测试URL执行状态查询API
        print("1. 测试URL执行状态查询API...")
        for url in TEST_URLS:
            try:
                response = requests.get(
                    f"{BACKEND_URL}/api/v1/crawler-debug/execution/url-status",
                    params={"url": url}
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ URL状态API查询成功: {url} -> {data.get('status', 'unknown')}")
                elif response.status_code == 404:
                    print(f"⚠️  URL状态未找到: {url}")
                else:
                    print(f"❌ URL状态API查询失败: {response.status_code}")
            except Exception as e:
                print(f"❌ URL状态API查询异常: {e}")
        
        # 2. 测试监控任务执行链路查询API
        print("2. 测试监控任务执行链路查询API...")
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/crawler-debug/execution/monitoring-task/{TEST_MONITORING_TASK_ID}"
            )
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 监控任务执行链路API查询成功: {data.get('total_sessions', 0)} 个会话")
            else:
                print(f"❌ 监控任务执行链路API查询失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 监控任务执行链路API查询异常: {e}")
        
        # 3. 测试Worker负载查询API
        print("3. 测试Worker负载查询API...")
        for i in range(2):
            worker_id = f"test_worker_{i+1}"
            try:
                response = requests.get(
                    f"{BACKEND_URL}/api/v1/crawler-debug/execution/worker-load/{worker_id}"
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Worker负载API查询成功: {worker_id} -> {data.get('current_tasks', 0)} 个任务")
                else:
                    print(f"❌ Worker负载API查询失败: {response.status_code}")
            except Exception as e:
                print(f"❌ Worker负载API查询异常: {e}")
        
        # 4. 测试Celery任务执行信息查询API
        print("4. 测试Celery任务执行信息查询API...")
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/crawler-debug/execution/celery-task/{TEST_CELERY_TASK_ID}"
            )
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Celery任务执行信息API查询成功: {data.get('total_sessions', 0)} 个会话")
            else:
                print(f"❌ Celery任务执行信息API查询失败: {response.status_code}")
        except Exception as e:
            print(f"❌ Celery任务执行信息API查询异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一执行API测试失败: {e}")
        return False


def test_redis_data_structure():
    """测试Redis数据结构"""
    print("\n🔍 测试Redis数据结构...")
    
    try:
        import redis
        redis_client = redis.from_url("redis://localhost:6379/0", decode_responses=True)
        
        # 1. 检查执行会话数据
        print("1. 检查执行会话数据...")
        session_pattern = f"execution_sessions:*"
        session_keys = redis_client.keys(session_pattern)
        print(f"✅ 找到 {len(session_keys)} 个执行会话记录")
        
        # 2. 检查URL执行数据
        print("2. 检查URL执行数据...")
        execution_pattern = f"url_executions:*"
        execution_keys = redis_client.keys(execution_pattern)
        print(f"✅ 找到 {len(execution_keys)} 个URL执行记录")
        
        # 3. 检查索引数据
        print("3. 检查索引数据...")
        index_patterns = [
            "monitoring_task_sessions:*",
            "celery_task_sessions:*",
            "worker_url_executions:*",
            "url_execution_history:*"
        ]
        
        for pattern in index_patterns:
            keys = redis_client.keys(pattern)
            print(f"✅ 索引 {pattern}: {len(keys)} 个键")
        
        # 4. 检查实时状态数据
        print("4. 检查实时状态数据...")
        current_pattern = "current_executions:*"
        current_keys = redis_client.keys(current_pattern)
        print(f"✅ 实时状态: {len(current_keys)} 个Worker")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis数据结构测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试统一执行存储结构\n")
    
    test_results = []
    
    # 测试1: 统一执行服务
    result1 = await test_unified_execution_service()
    test_results.append(("统一执行服务", result1))
    
    # 等待一下让数据写入完成
    await asyncio.sleep(2)
    
    # 测试2: 统一执行API
    result2 = test_unified_execution_api()
    test_results.append(("统一执行API", result2))
    
    # 测试3: Redis数据结构
    result3 = test_redis_data_structure()
    test_results.append(("Redis数据结构", result3))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！统一执行存储结构工作正常！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
