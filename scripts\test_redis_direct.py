#!/usr/bin/env python3
"""
直接测试Redis连接和数据写入
"""

import redis
import json
from datetime import datetime

def test_redis_direct():
    """直接测试Redis"""
    print("🧪 Testing Redis connection and data persistence...")
    print("=" * 50)
    
    try:
        # 连接Redis
        client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        client.ping()
        print("✅ Connected to Redis")
        
        # 清理之前的数据
        keys_to_delete = [
            "taskmanager:pending_batches",
            "taskmanager:running_batches", 
            "taskmanager:completed_batches",
            "taskmanager:failed_batches",
            "taskmanager:state"
        ]
        
        for key in keys_to_delete:
            client.delete(key)
        print("🧹 Cleaned previous data")
        
        # 创建测试任务数据
        task_data = {
            "batch_id": f"batch_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "task_id": "test_task_001",
            "urls": [
                "https://articulo.mercadolibre.com.mx/MLM-123456-test-product-1",
                "https://articulo.mercadolibre.com.mx/MLM-789012-test-product-2",
                "https://articulo.mercadolibre.com.mx/MLM-345678-test-product-3"
            ],
            "platform": "mercadolibre",
            "priority": "normal",
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "options": {"test": True}
        }
        
        # 写入到Redis
        print("\n📤 Writing task data to Redis...")
        
        # 1. 添加到待处理队列
        client.lpush("taskmanager:pending_batches", json.dumps(task_data))
        print(f"  ✅ Added to pending queue: {task_data['batch_id']}")
        
        # 2. 保存管理器状态
        state_data = {
            "pending_count": 1,
            "running_count": 0,
            "completed_count": 0,
            "failed_count": 0,
            "last_update": datetime.now().isoformat()
        }
        
        client.set("taskmanager:state", json.dumps(state_data), ex=3600)
        print(f"  ✅ Saved manager state")
        
        # 3. 验证数据
        print("\n💾 Verifying Redis data...")
        
        pending_count = client.llen("taskmanager:pending_batches")
        print(f"  Pending tasks count: {pending_count}")
        
        if pending_count > 0:
            # 获取第一个任务
            first_task_raw = client.lindex("taskmanager:pending_batches", 0)
            first_task = json.loads(first_task_raw)
            print(f"  First task ID: {first_task['task_id']}")
            print(f"  First task URLs count: {len(first_task['urls'])}")
            print(f"  First task platform: {first_task['platform']}")
        
        # 获取管理器状态
        state_raw = client.get("taskmanager:state")
        if state_raw:
            state = json.loads(state_raw)
            print(f"  Manager state: {state}")
        
        print("\n🎉 Redis persistence test PASSED!")
        print(f"✅ Successfully stored task data in Redis")
        print(f"✅ Task contains {len(task_data['urls'])} real URLs")
        
        # 显示存储的URL
        print(f"\n📋 Stored URLs:")
        for i, url in enumerate(task_data['urls'], 1):
            print(f"  {i}. {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_redis_direct()
    print(f"\n{'='*50}")
    if success:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("❌ TESTS FAILED!")
