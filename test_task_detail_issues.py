#!/usr/bin/env python3
"""
测试任务详情页面的问题
"""

import asyncio
import aiohttp
import json

API_BASE_URL = "http://localhost:8000"

async def test_task_detail_issues():
    """测试任务详情页面的问题"""
    
    async with aiohttp.ClientSession() as session:
        print("🔍 开始测试任务详情页面问题...")
        
        # 1. 获取任务列表
        print("\n1. 获取任务列表...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/") as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('data', [])
                    if tasks:
                        task_id = tasks[0]['id']
                        task_name = tasks[0]['name']
                        print(f"✅ 找到任务: {task_name} (ID: {task_id})")
                    else:
                        print("❌ 没有找到任务")
                        return
                else:
                    print(f"❌ 获取任务列表失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return
        
        # 2. 测试任务详情API
        print(f"\n2. 测试任务详情API (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    task_detail = data.get('data', {})
                    print(f"✅ 任务详情API正常")
                    print(f"   任务名称: {task_detail.get('name')}")
                    print(f"   任务状态: {task_detail.get('status')}")
                    print(f"   总URL数: {task_detail.get('total_urls')}")
                    print(f"   活跃URL: {task_detail.get('active_urls')}")
                    print(f"   是否运行中: {task_detail.get('is_running')}")
                    print(f"   上次执行: {task_detail.get('last_run') or '未执行'}")
                    print(f"   下次执行: {task_detail.get('next_run') or '未安排'}")
                else:
                    print(f"❌ 任务详情API失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 任务详情API异常: {e}")
            return
        
        # 3. 测试任务URL列表API
        print(f"\n3. 测试任务URL列表API (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/urls") as response:
                if response.status == 200:
                    data = await response.json()
                    urls = data.get('data', [])
                    total = data.get('total', 0)
                    print(f"✅ 任务URL列表API正常: {total} 个URL")
                    
                    if urls:
                        print(f"   URL示例:")
                        for i, url in enumerate(urls[:3]):
                            print(f"     {i+1}. {url.get('url')}")
                            print(f"        状态: {url.get('status')}")
                            print(f"        平台: {url.get('platform')}")
                            print(f"        添加时间: {url.get('added_at')}")
                    else:
                        print(f"   ⚠️ 没有找到URL数据")
                else:
                    print(f"❌ 任务URL列表API失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 任务URL列表API异常: {e}")
        
        # 4. 测试任务统计信息API
        print(f"\n4. 测试任务统计信息API (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    stats = data.get('data', {})
                    print(f"✅ 任务统计信息API正常")
                    
                    url_stats = stats.get('url_stats', {})
                    execution_stats = stats.get('execution_stats', {})
                    task_info = stats.get('task_info', {})
                    
                    print(f"   URL统计:")
                    print(f"     总URL数: {url_stats.get('total_urls')}")
                    print(f"     活跃URL: {url_stats.get('active_urls')}")
                    print(f"     禁用URL: {url_stats.get('disabled_urls')}")
                    print(f"     错误URL: {url_stats.get('error_urls')}")
                    
                    print(f"   执行统计:")
                    print(f"     总执行次数: {execution_stats.get('total_executions')}")
                    print(f"     成功次数: {execution_stats.get('successful_executions')}")
                    print(f"     失败次数: {execution_stats.get('failed_executions')}")
                    print(f"     成功率: {execution_stats.get('success_rate')}%")
                    print(f"     平均时长: {execution_stats.get('avg_duration')}s")
                    
                    print(f"   任务信息:")
                    print(f"     创建时间: {task_info.get('created_at')}")
                    print(f"     上次执行: {task_info.get('last_run') or '未执行'}")
                    print(f"     下次执行: {task_info.get('next_run') or '未安排'}")
                    print(f"     任务状态: {task_info.get('status')}")
                    print(f"     是否运行中: {task_info.get('is_running')}")
                else:
                    print(f"❌ 任务统计信息API失败: {response.status}")
                    error_data = await response.json()
                    print(f"   错误信息: {error_data.get('detail')}")
        except Exception as e:
            print(f"❌ 任务统计信息API异常: {e}")
        
        # 5. 测试任务执行历史API
        print(f"\n5. 测试任务执行历史API (ID: {task_id})...")
        try:
            async with session.get(f"{API_BASE_URL}/api/v1/monitoring-tasks/{task_id}/history") as response:
                if response.status == 200:
                    data = await response.json()
                    history = data.get('data', [])
                    total = data.get('total', 0)
                    print(f"✅ 任务执行历史API正常: {total} 条记录")
                    
                    if history:
                        print(f"   执行历史示例:")
                        for i, record in enumerate(history[:2]):
                            print(f"     {i+1}. 执行ID: {record.get('id')}")
                            print(f"        状态: {record.get('status')}")
                            print(f"        触发方式: {record.get('trigger')}")
                            print(f"        开始时间: {record.get('start_time')}")
                            print(f"        URL数量: {record.get('url_count')}")
                    else:
                        print(f"   ⚠️ 没有找到执行历史")
                else:
                    print(f"❌ 任务执行历史API失败: {response.status}")
        except Exception as e:
            print(f"❌ 任务执行历史API异常: {e}")
        
        print("\n🎉 任务详情页面问题测试完成!")
        print(f"\n📋 前端测试URL:")
        print(f"   任务详情页面: http://localhost:3000/monitoring/tasks/{task_id}")

if __name__ == "__main__":
    asyncio.run(test_task_detail_issues())
