"""
新架构：爬虫Worker API接口
提供Worker的CRUD操作和兼容性检查功能
"""

from typing import List, Optional, Dict
from fastapi import APIRouter, HTTPException, Query

from ...schemas.crawler_worker import (
    CrawlerWorker, CrawlerWorkerCreate, CrawlerWorkerUpdate,
    CrawlerWorkerSummary, CrawlerWorkerDetail, CrawlerWorkerStats,
    WorkerDetailStats, TaskExecutionRecord, WorkerCompatibilityCheck, WorkerGroupCompatibilityCheck,
    WorkerStatus, WorkerPriority
)
from ...services.crawler_worker_service import crawler_worker_service
from ...services.task_execution_service import task_execution_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/crawler-workers", tags=["爬虫Worker管理"])


@router.post("/", response_model=CrawlerWorker, summary="创建Worker")
async def create_crawler_worker(worker_data: CrawlerWorkerCreate):
    """
    创建新的爬虫Worker
    
    - **worker_name**: Worker名称（必须唯一）
    - **crawler_config_id**: 爬取配置ID
    - **backend_config_id**: 后端配置ID
    - **allocated_concurrent**: 分配的并发数
    - **priority**: Worker优先级
    """
    try:
        worker = await crawler_worker_service.create_worker(worker_data)
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create crawler worker: {e}")
        raise HTTPException(status_code=500, detail=f"创建Worker失败: {str(e)}")


@router.get("/", response_model=List[CrawlerWorkerSummary], summary="获取Worker列表")
async def list_crawler_workers(
    status: Optional[WorkerStatus] = Query(None, description="状态过滤"),
    priority: Optional[WorkerPriority] = Query(None, description="优先级过滤"),
    backend_config_id: Optional[str] = Query(None, description="后端配置ID过滤"),
    crawler_config_id: Optional[str] = Query(None, description="爬取配置ID过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取爬虫Worker列表
    
    支持多种过滤条件和分页
    """
    try:
        workers = await crawler_worker_service.list_workers(
            status=status,
            priority=priority,
            backend_config_id=backend_config_id,
            crawler_config_id=crawler_config_id,
            limit=limit,
            offset=offset
        )
        return workers
    except Exception as e:
        logger.error(f"Failed to list crawler workers: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker列表失败: {str(e)}")


@router.get("/available", response_model=List[CrawlerWorkerSummary], summary="获取可用Worker")
async def get_available_workers():
    """
    获取所有可用的Worker
    
    只返回状态为活跃且有可用容量的Worker
    """
    try:
        workers = await crawler_worker_service.list_workers(
            status=WorkerStatus.ACTIVE,
            limit=1000
        )
        # 过滤出可用的Worker
        available_workers = [w for w in workers if w.is_available]
        return available_workers
    except Exception as e:
        logger.error(f"Failed to get available workers: {e}")
        raise HTTPException(status_code=500, detail=f"获取可用Worker失败: {str(e)}")


@router.get("/{worker_id}", response_model=CrawlerWorker, summary="获取Worker详情")
async def get_crawler_worker(worker_id: str):
    """
    根据ID获取Worker详情
    """
    try:
        worker = await crawler_worker_service.get_worker(worker_id)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker失败: {str(e)}")


@router.get("/{worker_id}/detail", response_model=CrawlerWorkerDetail, summary="获取Worker详细信息")
async def get_crawler_worker_detail(worker_id: str):
    """
    获取Worker详细信息（包含关联配置）
    """
    try:
        worker_detail = await crawler_worker_service.get_worker_detail(worker_id)
        if not worker_detail:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker_detail
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler worker detail {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker详情失败: {str(e)}")


@router.get("/by-name/{worker_name}", response_model=CrawlerWorker, summary="根据名称获取Worker")
async def get_crawler_worker_by_name(worker_name: str):
    """
    根据名称获取Worker
    """
    try:
        worker = await crawler_worker_service.get_worker_by_name(worker_name)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawler worker by name {worker_name}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker失败: {str(e)}")


@router.put("/{worker_id}", response_model=CrawlerWorker, summary="更新Worker")
async def update_crawler_worker(worker_id: str, update_data: CrawlerWorkerUpdate):
    """
    更新Worker配置
    
    只更新提供的字段，其他字段保持不变
    """
    try:
        worker = await crawler_worker_service.update_worker(worker_id, update_data)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update crawler worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新Worker失败: {str(e)}")


@router.delete("/{worker_id}", summary="删除Worker")
async def delete_crawler_worker(worker_id: str):
    """
    删除Worker
    
    注意：如果Worker正在执行任务，删除会失败
    """
    try:
        success = await crawler_worker_service.delete_worker(worker_id)
        if not success:
            raise HTTPException(status_code=400, detail="删除Worker失败")
        return {"message": "Worker删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete crawler worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"删除Worker失败: {str(e)}")


@router.post("/{worker_id}/check-compatibility", response_model=WorkerCompatibilityCheck, summary="检查Worker兼容性")
async def check_worker_compatibility(worker_id: str):
    """
    检查Worker兼容性
    
    验证Worker的配置是否有效，是否存在资源冲突
    """
    try:
        compatibility_check = await crawler_worker_service.check_worker_compatibility(worker_id)
        if not compatibility_check:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return compatibility_check
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to check worker compatibility {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"兼容性检查失败: {str(e)}")


@router.get("/{worker_id}/conflicts", response_model=List[str], summary="获取冲突Worker列表")
async def get_conflicted_workers(worker_id: str):
    """
    获取与指定Worker冲突的Worker列表
    
    返回使用相同后端资源的其他Worker ID
    """
    try:
        conflicted_workers = await crawler_worker_service.get_conflicted_workers(worker_id)
        return conflicted_workers
    except Exception as e:
        logger.error(f"Failed to get conflicted workers for {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取冲突Worker失败: {str(e)}")


@router.patch("/{worker_id}/status", response_model=CrawlerWorker, summary="更新Worker状态")
async def update_worker_status(worker_id: str, status: WorkerStatus):
    """
    更新Worker状态
    
    - **active**: 活跃状态，可以接收任务
    - **inactive**: 非活跃状态，暂停接收任务
    - **busy**: 忙碌状态，正在执行任务
    - **error**: 错误状态，出现故障
    - **maintenance**: 维护状态，正在维护
    """
    try:
        update_data = CrawlerWorkerUpdate(status=status)
        worker = await crawler_worker_service.update_worker(worker_id, update_data)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update worker status {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")





@router.get("/{worker_id}/performance", summary="获取Worker性能指标")
async def get_worker_performance(worker_id: str):
    """
    获取Worker性能指标
    
    包括任务统计、成功率、利用率等信息
    """
    try:
        worker = await crawler_worker_service.get_worker(worker_id)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        
        performance = {
            "worker_id": worker_id,
            "worker_name": worker.worker_name,
            "status": worker.status,
            "priority": worker.priority,
            "allocated_concurrent": worker.allocated_concurrent,
            "current_tasks": worker.current_tasks,
            "available_capacity": worker.available_capacity,
            "utilization_rate": worker.utilization_rate,
            "total_tasks": worker.total_tasks,
            "completed_tasks": worker.completed_tasks,
            "failed_tasks": worker.failed_tasks,
            "success_rate": worker.success_rate,
            "avg_task_duration": worker.avg_task_duration,
            "health_score": worker.health_score,
            "last_task_time": worker.last_task_time,
            "last_health_check": worker.last_health_check,
            "error_count": worker.error_count
        }
        
        return performance
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get worker performance {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")


@router.get("/{worker_id}/stats", response_model=WorkerDetailStats, summary="获取Worker统计信息")
async def get_worker_stats(worker_id: str):
    """
    获取Worker的详细统计信息

    包括任务执行统计、性能指标、错误统计等
    """
    try:
        stats = await crawler_worker_service.get_worker_stats(worker_id)
        if not stats:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get worker stats {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")



@router.post("/{worker_id}/record-execution", summary="记录任务执行")
async def record_task_execution(worker_id: str, execution_record: TaskExecutionRecord):
    """
    记录Worker的任务执行

    用于记录任务的执行状态、结果和性能指标
    """
    try:
        # 验证worker_id匹配
        if execution_record.worker_id != worker_id:
            raise HTTPException(status_code=400, detail="Worker ID不匹配")

        await crawler_worker_service.record_task_execution(execution_record)
        return {"success": True, "message": "任务执行记录已保存"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to record task execution for worker {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"记录任务执行失败: {str(e)}")


@router.get("/active-executions", response_model=List[TaskExecutionRecord], summary="获取活跃的任务执行")
async def get_active_executions():
    """
    获取所有活跃的任务执行

    返回当前正在执行的任务列表
    """
    try:
        executions = await task_execution_service.get_active_executions()
        return executions
    except Exception as e:
        logger.error(f"Failed to get active executions: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃执行失败: {str(e)}")


@router.post("/{worker_id}/reset-stats", response_model=CrawlerWorker, summary="重置Worker统计")
async def reset_worker_stats(worker_id: str):
    """
    重置Worker统计信息
    
    清零任务计数、错误计数等统计数据
    """
    try:
        update_data = CrawlerWorkerUpdate(
            total_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            current_tasks=0,
            avg_task_duration=0.0,
            success_rate=0.0,
            error_count=0
        )
        worker = await crawler_worker_service.update_worker(worker_id, update_data)
        if not worker:
            raise HTTPException(status_code=404, detail="Worker不存在")
        return worker
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset worker stats {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"重置统计失败: {str(e)}")


@router.get("/backend/{backend_config_id}/workers", response_model=List[CrawlerWorkerSummary], summary="获取后端的Worker列表")
async def get_workers_by_backend(backend_config_id: str):
    """
    获取使用指定后端配置的所有Worker
    """
    try:
        workers = await crawler_worker_service.list_workers(
            backend_config_id=backend_config_id,
            limit=1000
        )
        return workers
    except Exception as e:
        logger.error(f"Failed to get workers by backend {backend_config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取后端Worker失败: {str(e)}")


@router.get("/config/{crawler_config_id}/workers", response_model=List[CrawlerWorkerSummary], summary="获取配置的Worker列表")
async def get_workers_by_config(crawler_config_id: str):
    """
    获取使用指定爬取配置的所有Worker
    """
    try:
        workers = await crawler_worker_service.list_workers(
            crawler_config_id=crawler_config_id,
            limit=1000
        )
        return workers
    except Exception as e:
        logger.error(f"Failed to get workers by config {crawler_config_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置Worker失败: {str(e)}")


@router.post("/check-group-compatibility", response_model=WorkerGroupCompatibilityCheck, summary="检查Worker组兼容性")
async def check_worker_group_compatibility(request: Dict[str, List[str]]):
    """
    检查多个Worker之间的兼容性

    检查Worker组是否存在资源冲突、配置冲突等问题
    """
    try:
        worker_ids = request.get('worker_ids', [])
        if not worker_ids:
            raise HTTPException(status_code=400, detail="Worker ID列表不能为空")

        result = await crawler_worker_service.check_worker_group_compatibility(worker_ids)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to check worker group compatibility: {e}")
        raise HTTPException(status_code=500, detail=f"Worker组兼容性检查失败: {str(e)}")


@router.get("/task/{task_id}/assigned-workers", response_model=List[Dict], summary="获取任务的Worker列表")
async def get_task_assigned_workers(task_id: str):
    """
    获取指派给指定任务的所有Worker

    返回以Worker为单位的列表，而不是分配记录
    """
    try:
        from ...services.task_assignment_service import task_assignment_service

        # 获取任务的所有活跃分配
        assignments = await task_assignment_service.list_assignments(
            task_id=task_id,
            limit=1000
        )

        # 过滤活跃分配
        active_assignments = [
            a for a in assignments
            if a.status not in ['cancelled', 'failed']
        ]

        # 收集所有Worker ID
        worker_ids = set()
        assignment_map = {}  # worker_id -> assignment_info

        for assignment in active_assignments:
            # 获取完整分配信息
            full_assignment = await task_assignment_service.get_assignment(assignment.assignment_id)
            if full_assignment:
                for worker_id in full_assignment.worker_ids:
                    worker_ids.add(worker_id)
                    assignment_map[worker_id] = {
                        'assignment_id': assignment.assignment_id,
                        'assignment_name': assignment.assignment_name,
                        'assignment_status': assignment.status,
                        'created_at': assignment.created_at,
                        'start_time': assignment.start_time
                    }

        # 获取Worker详细信息
        worker_list = []
        for worker_id in worker_ids:
            worker = await crawler_worker_service.get_worker(worker_id)
            if worker:
                assignment_info = assignment_map.get(worker_id, {})

                # 获取配置名称
                crawler_config_name = worker.crawler_config_id
                backend_config_name = worker.backend_config_id

                try:
                    # 获取爬取配置名称
                    from ...services.crawler_config_service import crawler_config_service
                    crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
                    if crawler_config:
                        crawler_config_name = crawler_config.config_name
                except Exception as e:
                    logger.warning(f"Failed to get crawler config name for {worker.crawler_config_id}: {e}")

                try:
                    # 获取后端配置名称
                    from ...services.backend_config_service import backend_config_service
                    backend_config = await backend_config_service.get_config(worker.backend_config_id)
                    if backend_config:
                        backend_config_name = backend_config.backend_name
                except Exception as e:
                    logger.warning(f"Failed to get backend config name for {worker.backend_config_id}: {e}")

                worker_info = {
                    'worker_id': worker.worker_id,
                    'worker_name': worker.worker_name,
                    'status': worker.status,
                    'priority': worker.priority,
                    'current_tasks': worker.current_tasks,
                    'allocated_concurrent': worker.allocated_concurrent,
                    'available_capacity': worker.available_capacity,
                    'utilization_rate': worker.utilization_rate,
                    'success_rate': worker.success_rate,
                    'health_score': worker.health_score,
                    'last_task_time': worker.last_task_time,
                    'last_health_check': worker.last_health_check,
                    # 配置信息
                    'crawler_config_id': worker.crawler_config_id,
                    'backend_config_id': worker.backend_config_id,
                    'crawler_config_name': crawler_config_name,
                    'backend_config_name': backend_config_name,
                    # 分配相关信息
                    'assignment_id': assignment_info.get('assignment_id'),
                    'assignment_name': assignment_info.get('assignment_name'),
                    'assignment_status': assignment_info.get('assignment_status'),
                    'assigned_at': assignment_info.get('created_at'),
                    'started_at': assignment_info.get('start_time'),
                    # URL处理统计（可以后续扩展）
                    'assigned_urls': 0,  # TODO: 从URL分配中计算
                    'processed_urls': 0,  # TODO: 从执行记录中计算
                    'failed_urls': 0,    # TODO: 从执行记录中计算
                }

                worker_list.append(worker_info)

        # 按Worker名称排序
        worker_list.sort(key=lambda x: x['worker_name'])

        return worker_list

    except Exception as e:
        logger.error(f"Failed to get task assigned workers {task_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务Worker列表失败: {str(e)}")


@router.get("/{worker_id}/assignments", response_model=List[Dict], summary="获取Worker的任务分配")
async def get_worker_assignments(worker_id: str):
    """
    获取指定Worker的所有任务分配

    返回Worker被分配的任务列表
    """
    try:
        from ...services.task_assignment_service import task_assignment_service
        from ...services.crawler_worker_service import crawler_worker_service
        from ...services.task_storage_service import get_task_storage

        # 获取Worker的所有分配
        assignments = await task_assignment_service.list_assignments(
            worker_id=worker_id,
            limit=1000
        )

        # 过滤活跃分配并转换为API格式
        assignment_list = []
        for assignment in assignments:
            if assignment.status not in ['cancelled', 'failed']:
                # 检查Worker是否在分配中
                if worker_id not in assignment.worker_ids:
                    continue

                # 验证任务是否存在
                task_storage = await get_task_storage()
                task_exists = await task_storage.task_exists(assignment.task_id)
                if not task_exists:
                    logger.warning(f"Skipping assignment {assignment.assignment_id} - task {assignment.task_id} does not exist")
                    continue

                # 获取Worker名称
                worker = await crawler_worker_service.get_worker(worker_id)
                worker_name = worker.worker_name if worker else worker_id

                assignment_info = {
                    'assignment_id': assignment.assignment_id,
                    'assignment_name': assignment.assignment_name,
                    'assignment_status': assignment.status,
                    'task_id': assignment.task_id,
                    'task_name': assignment.task_id,  # 暂时使用task_id作为名称
                    'task_status': '未知',  # 暂时设为未知
                    'created_at': assignment.created_at,
                    'start_time': assignment.start_time,
                    'total_urls': assignment.total_urls,
                    'processed_urls': assignment.processed_urls,
                    'progress_rate': assignment.progress_rate,
                    'success_rate': assignment.success_rate,
                    # Worker角色信息
                    'is_primary_worker': assignment.primary_worker_name == worker_name,
                    'is_current_worker': assignment.current_worker_name == worker_name,
                    'worker_count': assignment.worker_count,
                }

                assignment_list.append(assignment_info)

        # 按创建时间排序
        assignment_list.sort(key=lambda x: x['created_at'], reverse=True)

        # 临时调试信息
        active_assignment_count = len([a for a in assignments if a.status not in ['cancelled', 'failed']])
        debug_info = {
            'worker_id': worker_id,
            'total_assignments': len(assignments),
            'active_assignments': active_assignment_count,
            'final_assignments': len(assignment_list),
            'assignment_ids': [a.assignment_id for a in assignments]
        }
        logger.info(f"Worker assignments debug: {debug_info}")

        return assignment_list

    except Exception as e:
        logger.error(f"Failed to get worker assignments {worker_id}: {e}")
        raise HTTPException(status_code=500, detail=f"获取Worker任务分配失败: {str(e)}")
