#!/usr/bin/env python3
"""
测试Worker兼容性检查404错误
"""

import asyncio
import sys
import os
import json
import httpx
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

async def test_worker_compatibility_404():
    """测试Worker兼容性检查404错误"""
    
    print("🧪 测试Worker兼容性检查404错误...")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 1. 获取现有的Worker列表
            print("\n1. 获取现有的Worker列表...")
            response = await client.get("http://localhost:8000/api/v1/crawler-workers/")
            
            if response.status_code == 200:
                workers = response.json()
                print(f"✅ 获取到 {len(workers)} 个Worker:")
                
                for worker in workers:
                    print(f"   - ID: {worker['worker_id']}")
                    print(f"     名称: {worker['worker_name']}")
                    print(f"     状态: {worker['status']}")
                    print()
                
                if not workers:
                    print("❌ 没有Worker，无法测试兼容性检查")
                    return
                
                # 2. 测试第一个Worker的兼容性检查
                test_worker = workers[0]
                worker_id = test_worker['worker_id']
                
                print(f"2. 测试Worker兼容性检查...")
                print(f"   Worker ID: {worker_id}")
                print(f"   Worker名称: {test_worker['worker_name']}")
                
                compat_response = await client.post(f"http://localhost:8000/api/v1/crawler-workers/{worker_id}/check-compatibility")
                
                print(f"   状态码: {compat_response.status_code}")
                
                if compat_response.status_code == 200:
                    result = compat_response.json()
                    print(f"   ✅ 兼容性检查成功:")
                    print(f"      - 兼容: {result.get('is_compatible', 'N/A')}")
                    print(f"      - 配置存在: {result.get('config_exists', 'N/A')}")
                    print(f"      - 错误: {result.get('errors', [])}")
                    print(f"      - 警告: {result.get('warnings', [])}")
                elif compat_response.status_code == 404:
                    print(f"   ❌ Worker不存在 (404)")
                    error_text = compat_response.text
                    print(f"   错误信息: {error_text}")
                    
                    # 检查Worker是否真的存在
                    detail_response = await client.get(f"http://localhost:8000/api/v1/crawler-workers/{worker_id}")
                    print(f"   Worker详情查询状态码: {detail_response.status_code}")
                    
                    if detail_response.status_code == 200:
                        print(f"   🤔 Worker详情存在，但兼容性检查返回404")
                    else:
                        print(f"   💡 Worker详情也不存在，可能是数据不一致")
                        
                else:
                    print(f"   ❌ 兼容性检查失败: {compat_response.status_code}")
                    print(f"   错误: {compat_response.text}")
                
                # 3. 测试不存在的Worker ID
                print(f"\n3. 测试不存在的Worker ID...")
                fake_id = "00000000-0000-0000-0000-000000000000"
                fake_response = await client.post(f"http://localhost:8000/api/v1/crawler-workers/{fake_id}/check-compatibility")
                
                print(f"   假Worker ID: {fake_id}")
                print(f"   状态码: {fake_response.status_code}")
                print(f"   响应: {fake_response.text}")
                
            else:
                print(f"❌ 获取Worker列表失败: {response.status_code}")
                print(f"错误: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

async def test_specific_worker_id():
    """测试特定的Worker ID"""
    
    worker_id = "bdfc7391-3c0f-4627-acee-10761740c139"  # 从错误信息中的ID
    
    print(f"\n4. 测试特定Worker ID: {worker_id}")
    
    try:
        async with httpx.AsyncClient() as client:
            
            # 检查Worker是否存在
            detail_response = await client.get(f"http://localhost:8000/api/v1/crawler-workers/{worker_id}")
            print(f"   Worker详情查询状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                worker = detail_response.json()
                print(f"   ✅ Worker存在: {worker['worker_name']}")
                
                # 测试兼容性检查
                compat_response = await client.post(f"http://localhost:8000/api/v1/crawler-workers/{worker_id}/check-compatibility")
                print(f"   兼容性检查状态码: {compat_response.status_code}")
                
                if compat_response.status_code == 200:
                    result = compat_response.json()
                    print(f"   ✅ 兼容性检查成功")
                else:
                    print(f"   ❌ 兼容性检查失败: {compat_response.text}")
                    
            elif detail_response.status_code == 404:
                print(f"   ❌ Worker不存在")
            else:
                print(f"   ❌ 查询失败: {detail_response.text}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")

async def main():
    """主函数"""
    await test_worker_compatibility_404()
    await test_specific_worker_id()
    print(f"\n🏁 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
