/**
 * 前端TypeScript类型验证脚本
 * 验证修复后的类型定义是否正确
 */

// 模拟类型定义验证
const typeValidation = {
  // 平台类型验证
  platformTypes: ['mercadolibre', 'amazon', '1688', 'general'],
  
  // 配置类型验证
  configTypes: ['batch_task', 'single_task', 'template'],
  
  // 优先级类型验证
  priorityTypes: ['high', 'medium', 'low'],
  
  // 执行模式类型验证
  executionModes: ['batch', 'single', 'streaming']
};

// 验证类型定义
function validateTypes() {
  console.log('🔍 开始验证前端类型定义...');
  
  // 验证平台类型
  console.log('✅ 平台类型:', typeValidation.platformTypes);
  
  // 验证配置类型
  console.log('✅ 配置类型:', typeValidation.configTypes);
  
  // 验证优先级类型
  console.log('✅ 优先级类型:', typeValidation.priorityTypes);
  
  // 验证执行模式类型
  console.log('✅ 执行模式类型:', typeValidation.executionModes);
  
  console.log('🎉 类型定义验证完成！');
}

// 模拟API调用参数验证
function validateApiParams() {
  console.log('\n🔍 验证API调用参数...');
  
  const mockParams = {
    platform: 'mercadolibre',
    config_type: 'batch_task',
    is_active: true,
    page: 1,
    page_size: 20
  };
  
  // 验证参数类型
  const validations = [
    { key: 'platform', value: mockParams.platform, valid: typeValidation.platformTypes.includes(mockParams.platform) },
    { key: 'config_type', value: mockParams.config_type, valid: typeValidation.configTypes.includes(mockParams.config_type) },
    { key: 'is_active', value: mockParams.is_active, valid: typeof mockParams.is_active === 'boolean' },
    { key: 'page', value: mockParams.page, valid: typeof mockParams.page === 'number' && mockParams.page > 0 },
    { key: 'page_size', value: mockParams.page_size, valid: typeof mockParams.page_size === 'number' && mockParams.page_size > 0 }
  ];
  
  validations.forEach(({ key, value, valid }) => {
    console.log(`${valid ? '✅' : '❌'} ${key}: ${value} (${typeof value})`);
  });
  
  const allValid = validations.every(v => v.valid);
  console.log(`\n${allValid ? '🎉' : '⚠️'} API参数验证${allValid ? '通过' : '失败'}！`);
  
  return allValid;
}

// 模拟配置对象验证
function validateConfigObject() {
  console.log('\n🔍 验证配置对象结构...');
  
  const mockConfig = {
    id: 'config_123',
    name: '测试配置',
    description: '测试用配置',
    config_type: 'batch_task',
    platform: 'mercadolibre',
    llm_query: '提取商品信息',
    llm_schema: '{"name": "string", "price": "number"}',
    priority: 'medium',
    cache_enabled: false,
    max_concurrent_tasks: 5,
    batch_timeout: 300,
    callback_url: 'http://example.com/callback',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    created_by: 'test_user',
    is_active: true,
    usage_count: 0,
    last_used_at: null
  };
  
  // 验证必需字段
  const requiredFields = ['id', 'name', 'config_type', 'platform', 'llm_query', 'priority'];
  const missingFields = requiredFields.filter(field => !mockConfig[field]);
  
  if (missingFields.length === 0) {
    console.log('✅ 所有必需字段都存在');
  } else {
    console.log('❌ 缺少必需字段:', missingFields);
  }
  
  // 验证类型约束
  const typeChecks = [
    { field: 'config_type', valid: typeValidation.configTypes.includes(mockConfig.config_type) },
    { field: 'platform', valid: typeValidation.platformTypes.includes(mockConfig.platform) },
    { field: 'priority', valid: typeValidation.priorityTypes.includes(mockConfig.priority) },
    { field: 'cache_enabled', valid: typeof mockConfig.cache_enabled === 'boolean' },
    { field: 'is_active', valid: typeof mockConfig.is_active === 'boolean' },
    { field: 'usage_count', valid: typeof mockConfig.usage_count === 'number' }
  ];
  
  typeChecks.forEach(({ field, valid }) => {
    console.log(`${valid ? '✅' : '❌'} ${field}: ${mockConfig[field]}`);
  });
  
  const allValid = missingFields.length === 0 && typeChecks.every(check => check.valid);
  console.log(`\n${allValid ? '🎉' : '⚠️'} 配置对象验证${allValid ? '通过' : '失败'}！`);
  
  return allValid;
}

// 模拟执行请求验证
function validateExecutionRequest() {
  console.log('\n🔍 验证执行请求结构...');
  
  const mockRequest = {
    monitoring_task_id: 'task_001',
    urls: ['https://example.com/1', 'https://example.com/2'],
    config_id: 'config_123',
    execution_mode: 'batch',
    priority: 'high',
    callback_url: 'http://example.com/callback',
    metadata: { test: true, source: 'validation' }
  };
  
  const validations = [
    { field: 'monitoring_task_id', valid: typeof mockRequest.monitoring_task_id === 'string' && mockRequest.monitoring_task_id.length > 0 },
    { field: 'urls', valid: Array.isArray(mockRequest.urls) && mockRequest.urls.length > 0 },
    { field: 'config_id', valid: typeof mockRequest.config_id === 'string' && mockRequest.config_id.length > 0 },
    { field: 'execution_mode', valid: typeValidation.executionModes.includes(mockRequest.execution_mode) },
    { field: 'priority', valid: typeValidation.priorityTypes.includes(mockRequest.priority) },
    { field: 'metadata', valid: typeof mockRequest.metadata === 'object' && mockRequest.metadata !== null }
  ];
  
  validations.forEach(({ field, valid }) => {
    console.log(`${valid ? '✅' : '❌'} ${field}: ${JSON.stringify(mockRequest[field])}`);
  });
  
  const allValid = validations.every(v => v.valid);
  console.log(`\n${allValid ? '🎉' : '⚠️'} 执行请求验证${allValid ? '通过' : '失败'}！`);
  
  return allValid;
}

// 主验证函数
function runValidation() {
  console.log('🚀 开始前端类型修复验证\n');
  
  validateTypes();
  const apiValid = validateApiParams();
  const configValid = validateConfigObject();
  const requestValid = validateExecutionRequest();
  
  const overallValid = apiValid && configValid && requestValid;
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 验证结果汇总');
  console.log('='.repeat(50));
  console.log(`API参数验证: ${apiValid ? '✅ 通过' : '❌ 失败'}`);
  console.log(`配置对象验证: ${configValid ? '✅ 通过' : '❌ 失败'}`);
  console.log(`执行请求验证: ${requestValid ? '✅ 通过' : '❌ 失败'}`);
  console.log('='.repeat(50));
  console.log(`🎯 总体结果: ${overallValid ? '✅ 所有验证通过' : '❌ 部分验证失败'}`);
  
  if (overallValid) {
    console.log('🎉 前端类型修复成功！TypeScript编译错误应该已经解决。');
  } else {
    console.log('⚠️  仍有类型问题需要修复，请检查上述失败项。');
  }
  
  return overallValid;
}

// 运行验证
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runValidation, typeValidation };
} else {
  runValidation();
}
