/**
 * URL池相关的TypeScript类型定义
 */

export type UrlStatus = 'active' | 'disabled' | 'deleted';

export interface UrlPoolItem {
  id: string;
  url: string;
  platform: string;
  source_file: string;
  added_at: string;
  status: UrlStatus;
  last_check?: string;
  check_count: number;
  success_count: number;
  error_count: number;
  metadata?: Record<string, any>;
}

export interface UrlPoolQuery {
  page: number;
  page_size: number;
  platform?: string | null;
  status?: UrlStatus | null;
  source_file?: string | null;
  search?: string | null;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  exclude_urls?: string[]; // 排除的URL ID列表
}

export interface UrlPoolBatchUpdate {
  url_ids: string[];
  action: 'enable' | 'disable' | 'delete';
}

export interface UrlPoolStats {
  total_urls: number;
  active_urls: number;
  disabled_urls: number;
  platforms: Record<string, number>;
  last_updated: string;
}

export interface ExcelUploadResponse {
  success: boolean;
  message: string;
  total_urls: number;
  new_urls: number;
  duplicate_urls: number;
  invalid_urls: number;
  urls: UrlPoolItem[];
  processing_time: number;
}

export interface UrlPoolListResponse {
  success: boolean;
  data: UrlPoolItem[];
  total: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

export interface UrlPoolBatchResponse {
  success: boolean;
  message: string;
  affected_count: number;
  total_count: number;
}

export interface UrlPoolStatsResponse {
  success: boolean;
  data: UrlPoolStats;
}

export interface UrlPoolFilter {
  platforms: string[];
  statuses: UrlStatus[];
  source_files: string[];
  date_range?: {
    start: string;
    end: string;
  };
}

export interface UrlPoolItemCreate {
  url: string;
  platform?: string;
  source_file: string;
  metadata?: Record<string, any>;
}

export interface UrlPoolItemUpdate {
  status?: UrlStatus;
  metadata?: Record<string, any>;
}

export interface SourceFile {
  name: string;
  url_count: number;
}

export interface PlatformInfo {
  name: string;
  display_name: string;
  url_count: number;
}

export interface UrlPoolOverview {
  stats: UrlPoolStats;
  platforms: string[];
  sourceFiles: SourceFile[];
}

// 表格列配置
export interface UrlPoolTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  filterable?: boolean;
  render?: (value: any, record: UrlPoolItem) => React.ReactNode;
}

// 筛选器配置
export interface FilterConfig {
  type: 'select' | 'multiSelect' | 'dateRange' | 'search';
  key: string;
  label: string;
  options?: Array<{
    label: string;
    value: string;
  }>;
  placeholder?: string;
}

// 批量操作配置
export interface BatchAction {
  key: string;
  label: string;
  icon?: React.ReactNode;
  danger?: boolean;
  disabled?: (selectedItems: UrlPoolItem[]) => boolean;
  confirm?: {
    title: string;
    content: string;
  };
}

// 导出配置
export interface ExportConfig {
  format: 'excel' | 'csv' | 'json';
  filename?: string;
  includeMetadata?: boolean;
  filter?: UrlPoolFilter;
}

// 分页配置
export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: (total: number, range: [number, number]) => string;
}

// URL池管理页面状态
export interface UrlPoolPageState {
  loading: boolean;
  data: UrlPoolItem[];
  total: number;
  selectedRowKeys: string[];
  selectedRows: UrlPoolItem[];
  query: UrlPoolQuery;
  filters: UrlPoolFilter;
  stats: UrlPoolStats | null;
  error: string | null;
}

// Excel上传状态
export interface ExcelUploadState {
  uploading: boolean;
  progress: number;
  result: ExcelUploadResponse | null;
  error: string | null;
}

// URL详情模态框状态
export interface UrlDetailModalState {
  visible: boolean;
  url: UrlPoolItem | null;
  loading: boolean;
}

// 批量操作模态框状态
export interface BatchActionModalState {
  visible: boolean;
  action: 'enable' | 'disable' | 'delete' | null;
  selectedItems: UrlPoolItem[];
  loading: boolean;
}

// 统计卡片数据
export interface StatisticCardData {
  title: string;
  value: number;
  suffix?: string;
  prefix?: React.ReactNode;
  valueStyle?: React.CSSProperties;
  loading?: boolean;
}

// 平台分布图表数据
export interface PlatformChartData {
  platform: string;
  count: number;
  percentage: number;
}

// 时间趋势图表数据
export interface TimeTrendData {
  date: string;
  count: number;
  type: 'upload' | 'active' | 'disabled';
}

// URL状态历史记录
export interface UrlStatusHistory {
  id: string;
  url_id: string;
  old_status: UrlStatus;
  new_status: UrlStatus;
  changed_at: string;
  changed_by?: string;
  reason?: string;
}

// 错误信息
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// 成功响应基础接口
export interface BaseResponse {
  success: boolean;
  message?: string;
}

// 分页响应基础接口
export interface PaginatedResponse<T> extends BaseResponse {
  data: T[];
  total: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

// 操作结果接口
export interface OperationResult extends BaseResponse {
  affected_count?: number;
  total_count?: number;
}
