"""
清理任务

定期清理过期数据、失败任务和系统垃圾
"""

import asyncio
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
from celery import Task

from app.celery_app import celery_app
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.log import SystemLog
from app.models.snapshot import ProductSnapshot
from sqlalchemy import select, delete, func, and_
from sqlalchemy.ext.asyncio import AsyncSession
import redis.asyncio as redis

logger = logging.getLogger(__name__)


class CleanupTask(Task):
    """清理任务基类"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """失败时的回调"""
        logger.error(f"Cleanup task {task_id} failed: {exc}")


@celery_app.task(
    bind=True,
    base=CleanupTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 300}
)
def cleanup_expired_tasks(self) -> Dict[str, Any]:
    """清理过期任务"""
    return asyncio.run(_cleanup_expired_tasks_async())


async def _cleanup_expired_tasks_async() -> Dict[str, Any]:
    """异步清理过期任务"""
    
    cleanup_stats = {
        "start_time": datetime.now().isoformat(),
        "expired_tasks_deleted": 0,
        "old_logs_deleted": 0,
        "redis_keys_cleaned": 0,
        "errors": []
    }
    
    try:
        # 清理过期的已完成任务（保留30天）
        cutoff_date = datetime.now() - timedelta(days=30)
        
        async with get_async_session() as session:
            # 查询过期的已完成任务
            expired_tasks_query = select(CrawlTask).where(
                and_(
                    CrawlTask.status.in_(["completed", "failed", "cancelled"]),
                    CrawlTask.completed_at < cutoff_date
                )
            )
            
            result = await session.execute(expired_tasks_query)
            expired_tasks = result.scalars().all()
            
            logger.info(f"Found {len(expired_tasks)} expired tasks to clean up")
            
            # 删除相关的产品快照
            for task in expired_tasks:
                try:
                    # 删除产品快照
                    await session.execute(
                        delete(ProductSnapshot).where(ProductSnapshot.task_id == task.id)
                    )
                    
                    # 删除任务记录
                    await session.execute(
                        delete(CrawlTask).where(CrawlTask.id == task.id)
                    )
                    
                    cleanup_stats["expired_tasks_deleted"] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to delete task {task.id}: {e}"
                    logger.error(error_msg)
                    cleanup_stats["errors"].append(error_msg)
            
            await session.commit()
        
        # 清理旧的系统日志（保留7天）
        log_cutoff_date = datetime.now() - timedelta(days=7)
        
        async with get_async_session() as session:
            old_logs_result = await session.execute(
                delete(SystemLog).where(SystemLog.created_at < log_cutoff_date)
            )
            cleanup_stats["old_logs_deleted"] = old_logs_result.rowcount
            await session.commit()
        
        # 清理Redis中的过期键
        redis_cleanup_count = await _cleanup_redis_keys()
        cleanup_stats["redis_keys_cleaned"] = redis_cleanup_count
        
        cleanup_stats["end_time"] = datetime.now().isoformat()
        cleanup_stats["success"] = True
        
        logger.info(
            f"Cleanup completed: {cleanup_stats['expired_tasks_deleted']} tasks, "
            f"{cleanup_stats['old_logs_deleted']} logs, "
            f"{cleanup_stats['redis_keys_cleaned']} Redis keys"
        )
        
        return cleanup_stats
        
    except Exception as e:
        error_msg = f"Cleanup task failed: {e}"
        logger.error(error_msg)
        cleanup_stats["errors"].append(error_msg)
        cleanup_stats["success"] = False
        cleanup_stats["end_time"] = datetime.now().isoformat()
        
        raise


async def _cleanup_redis_keys() -> int:
    """清理Redis中的过期键"""
    
    try:
        redis_client = redis.from_url("redis://localhost:6379/0")
        cleanup_count = 0
        
        # 清理过期的限流键
        current_time = int(datetime.now().timestamp())
        
        # 清理过期的分钟级限流键（保留2小时）
        minute_cutoff = (current_time // 60) - 120  # 2小时前
        minute_pattern = "rate_limiter:minute:*"
        
        async for key in redis_client.scan_iter(match=minute_pattern):
            key_str = key.decode('utf-8')
            try:
                minute_timestamp = int(key_str.split(':')[-1])
                if minute_timestamp < minute_cutoff:
                    await redis_client.delete(key)
                    cleanup_count += 1
            except (ValueError, IndexError):
                # 无效的键格式，删除
                await redis_client.delete(key)
                cleanup_count += 1
        
        # 清理过期的小时级限流键（保留7天）
        hour_cutoff = (current_time // 3600) - (7 * 24)  # 7天前
        hour_pattern = "rate_limiter:hour:*"
        
        async for key in redis_client.scan_iter(match=hour_pattern):
            key_str = key.decode('utf-8')
            try:
                hour_timestamp = int(key_str.split(':')[-1])
                if hour_timestamp < hour_cutoff:
                    await redis_client.delete(key)
                    cleanup_count += 1
            except (ValueError, IndexError):
                await redis_client.delete(key)
                cleanup_count += 1
        
        # 清理过期的请求记录（保留1天）
        request_cutoff = current_time - (24 * 3600)  # 1天前
        request_pattern = "rate_limiter:requests:*"
        
        async for key in redis_client.scan_iter(match=request_pattern):
            try:
                request_data = await redis_client.hgetall(key)
                if request_data:
                    start_time = float(request_data.get(b'start_time', 0))
                    if start_time < request_cutoff:
                        await redis_client.delete(key)
                        cleanup_count += 1
            except (ValueError, TypeError):
                await redis_client.delete(key)
                cleanup_count += 1
        
        await redis_client.close()
        
        logger.info(f"Cleaned up {cleanup_count} Redis keys")
        return cleanup_count
        
    except Exception as e:
        logger.error(f"Failed to cleanup Redis keys: {e}")
        return 0


@celery_app.task(
    bind=True,
    base=CleanupTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 60}
)
def cleanup_failed_tasks(self, max_age_hours: int = 24) -> Dict[str, Any]:
    """清理失败任务并尝试重新调度"""
    return asyncio.run(_cleanup_failed_tasks_async(max_age_hours))


async def _cleanup_failed_tasks_async(max_age_hours: int) -> Dict[str, Any]:
    """异步清理失败任务"""
    
    cleanup_stats = {
        "start_time": datetime.now().isoformat(),
        "failed_tasks_found": 0,
        "tasks_rescheduled": 0,
        "tasks_abandoned": 0,
        "errors": []
    }
    
    try:
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        async with get_async_session() as session:
            # 查询失败的任务
            failed_tasks_query = select(CrawlTask).where(
                and_(
                    CrawlTask.status == "failed",
                    CrawlTask.completed_at < cutoff_time
                )
            )
            
            result = await session.execute(failed_tasks_query)
            failed_tasks = result.scalars().all()
            
            cleanup_stats["failed_tasks_found"] = len(failed_tasks)
            
            for task in failed_tasks:
                try:
                    # 检查重试次数
                    retry_count = getattr(task, 'retry_count', 0)
                    max_retries = getattr(task, 'max_retry_count', 3)
                    
                    if retry_count < max_retries:
                        # 重新调度任务
                        task.status = "pending"
                        task.retry_count = retry_count + 1
                        task.completed_at = None
                        task.started_at = None
                        
                        cleanup_stats["tasks_rescheduled"] += 1
                        logger.info(f"Rescheduled failed task {task.id} (retry {retry_count + 1}/{max_retries})")
                    else:
                        # 放弃任务
                        task.status = "abandoned"
                        cleanup_stats["tasks_abandoned"] += 1
                        logger.warning(f"Abandoned failed task {task.id} after {retry_count} retries")
                    
                except Exception as e:
                    error_msg = f"Failed to process failed task {task.id}: {e}"
                    logger.error(error_msg)
                    cleanup_stats["errors"].append(error_msg)
            
            await session.commit()
        
        cleanup_stats["end_time"] = datetime.now().isoformat()
        cleanup_stats["success"] = True
        
        logger.info(
            f"Failed task cleanup completed: {cleanup_stats['tasks_rescheduled']} rescheduled, "
            f"{cleanup_stats['tasks_abandoned']} abandoned"
        )
        
        return cleanup_stats
        
    except Exception as e:
        error_msg = f"Failed task cleanup failed: {e}"
        logger.error(error_msg)
        cleanup_stats["errors"].append(error_msg)
        cleanup_stats["success"] = False
        cleanup_stats["end_time"] = datetime.now().isoformat()
        
        raise


@celery_app.task(
    bind=True,
    base=CleanupTask
)
def optimize_database(self) -> Dict[str, Any]:
    """优化数据库性能"""
    return asyncio.run(_optimize_database_async())


async def _optimize_database_async() -> Dict[str, Any]:
    """异步优化数据库"""
    
    optimization_stats = {
        "start_time": datetime.now().isoformat(),
        "operations_performed": [],
        "errors": []
    }
    
    try:
        async with get_async_session() as session:
            # 更新表统计信息
            await session.execute("ANALYZE crawl_tasks")
            optimization_stats["operations_performed"].append("ANALYZE crawl_tasks")
            
            await session.execute("ANALYZE product_snapshots")
            optimization_stats["operations_performed"].append("ANALYZE product_snapshots")
            
            await session.execute("ANALYZE system_logs")
            optimization_stats["operations_performed"].append("ANALYZE system_logs")
            
            # 如果是TimescaleDB，执行压缩
            try:
                # 检查是否有需要压缩的chunks
                compress_query = """
                SELECT compress_chunk(chunk_schema||'.'||chunk_name)
                FROM timescaledb_information.chunks
                WHERE NOT is_compressed
                AND range_end < NOW() - INTERVAL '1 day'
                """
                
                result = await session.execute(compress_query)
                compressed_chunks = result.rowcount
                
                if compressed_chunks > 0:
                    optimization_stats["operations_performed"].append(
                        f"Compressed {compressed_chunks} TimescaleDB chunks"
                    )
                
            except Exception as e:
                # 如果不是TimescaleDB或压缩失败，记录但不中断
                logger.warning(f"TimescaleDB compression failed (this is normal if not using TimescaleDB): {e}")
            
            await session.commit()
        
        optimization_stats["end_time"] = datetime.now().isoformat()
        optimization_stats["success"] = True
        
        logger.info(f"Database optimization completed: {optimization_stats['operations_performed']}")
        
        return optimization_stats
        
    except Exception as e:
        error_msg = f"Database optimization failed: {e}"
        logger.error(error_msg)
        optimization_stats["errors"].append(error_msg)
        optimization_stats["success"] = False
        optimization_stats["end_time"] = datetime.now().isoformat()
        
        raise


@celery_app.task(
    bind=True,
    base=CleanupTask
)
def generate_cleanup_report(self) -> Dict[str, Any]:
    """生成清理报告"""
    return asyncio.run(_generate_cleanup_report_async())


async def _generate_cleanup_report_async() -> Dict[str, Any]:
    """异步生成清理报告"""
    
    try:
        async with get_async_session() as session:
            # 统计各种状态的任务数量
            task_stats = await session.execute(
                select(
                    CrawlTask.status,
                    func.count(CrawlTask.id).label('count')
                ).group_by(CrawlTask.status)
            )
            
            task_counts = {row.status: row.count for row in task_stats}
            
            # 统计数据库大小
            total_tasks = await session.execute(select(func.count(CrawlTask.id)))
            total_snapshots = await session.execute(select(func.count(ProductSnapshot.id)))
            total_logs = await session.execute(select(func.count(SystemLog.id)))
            
            # 统计最近的活动
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_tasks = await session.execute(
                select(func.count(CrawlTask.id))
                .where(CrawlTask.created_at >= recent_cutoff)
            )
            
            report = {
                "generated_at": datetime.now().isoformat(),
                "task_statistics": task_counts,
                "database_size": {
                    "total_tasks": total_tasks.scalar(),
                    "total_snapshots": total_snapshots.scalar(),
                    "total_logs": total_logs.scalar()
                },
                "recent_activity": {
                    "tasks_created_24h": recent_tasks.scalar()
                },
                "recommendations": []
            }
            
            # 生成建议
            if task_counts.get("failed", 0) > 100:
                report["recommendations"].append(
                    "High number of failed tasks detected. Consider reviewing error patterns."
                )
            
            if total_logs.scalar() > 100000:
                report["recommendations"].append(
                    "Large number of log entries. Consider more aggressive log cleanup."
                )
            
            logger.info("Cleanup report generated successfully")
            return report
            
    except Exception as e:
        logger.error(f"Failed to generate cleanup report: {e}")
        raise
