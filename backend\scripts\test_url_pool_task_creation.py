#!/usr/bin/env python3
"""
测试URL池创建任务的配置保存

检查基础配置中的执行参数和调度配置中的时间设置
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_url_pool_task_creation():
    """测试URL池创建任务的配置保存"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试URL池创建任务的配置保存")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/url-pool-task-test",
            "platform": "mercadolibre",
            "title": "URL池任务创建测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'url_pool_task_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'url_pool_task_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:url_pool_task_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 模拟前端URL池创建任务的请求
        print(f"\n📋 步骤2: 模拟前端URL池创建任务的请求")
        
        # 这是前端实际发送的数据结构（模拟前端的问题）
        frontend_request = {
            "name": "URL池任务配置测试",
            "description": "测试URL池创建任务时的配置保存问题",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "15:45",  # 前端转换后的执行时间
                "timezone": "Asia/Shanghai",
                # 注意：前端移除了end_time字段！
                "max_runs": 20,
                "enable_random_delay": True,
                "random_delay_min": 5,
                "random_delay_max": 25
            },
            "config": {
                "platform": "mercadolibre",
                "priority": "normal",
                "batch_size": 15,
                "timeout": 600,
                # 注意：检查这些字段是否正确传递
                "retry_count": 5,
                "concurrent_limit": 8,
                "enable_notifications": True
            },
            "tags": ["url_pool_test"]
        }
        
        print(f"  发送的请求数据:")
        print(f"    基础配置 - 重试次数: {frontend_request['config']['retry_count']}")
        print(f"    基础配置 - 并发限制: {frontend_request['config']['concurrent_limit']}")
        print(f"    基础配置 - 批次大小: {frontend_request['config']['batch_size']}")
        print(f"    基础配置 - 超时时间: {frontend_request['config']['timeout']}")
        print(f"    调度配置 - 执行时间: {frontend_request['schedule']['time']}")
        print(f"    调度配置 - 结束时间: {frontend_request['schedule'].get('end_time', '未设置')}")
        print(f"    调度配置 - 最大执行次数: {frontend_request['schedule']['max_runs']}")
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=frontend_request,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建成功: {task_id}")
            
            # 步骤3: 检查保存的配置
            print(f"\n📋 步骤3: 检查保存的配置")
            
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if detail_response.status_code == 200:
                detail_result = detail_response.json()
                task_data = detail_result.get('data', {})
                saved_config = task_data.get('config', {})
                saved_schedule = task_data.get('schedule', {})
                
                print(f"  保存的基础配置:")
                print(f"    重试次数: {saved_config.get('retry_count')} (期望: 5)")
                print(f"    并发限制: {saved_config.get('concurrent_limit')} (期望: 8)")
                print(f"    批次大小: {saved_config.get('batch_size')} (期望: 15)")
                print(f"    超时时间: {saved_config.get('timeout')} (期望: 600)")
                
                print(f"  保存的调度配置:")
                print(f"    执行时间: {saved_schedule.get('time')} (期望: 15:45)")
                print(f"    结束时间: {saved_schedule.get('end_time')} (期望: 应该有值)")
                print(f"    最大执行次数: {saved_schedule.get('max_runs')} (期望: 20)")
                print(f"    随机延迟: {saved_schedule.get('enable_random_delay')} (期望: True)")
                
                # 步骤4: 验证配置是否正确保存
                print(f"\n📋 步骤4: 验证配置是否正确保存")
                
                # 检查基础配置
                config_issues = []
                if saved_config.get('retry_count') != 5:
                    config_issues.append(f"重试次数错误: 期望5, 实际{saved_config.get('retry_count')}")
                if saved_config.get('concurrent_limit') != 8:
                    config_issues.append(f"并发限制错误: 期望8, 实际{saved_config.get('concurrent_limit')}")
                if saved_config.get('batch_size') != 15:
                    config_issues.append(f"批次大小错误: 期望15, 实际{saved_config.get('batch_size')}")
                if saved_config.get('timeout') != 600:
                    config_issues.append(f"超时时间错误: 期望600, 实际{saved_config.get('timeout')}")
                
                # 检查调度配置
                schedule_issues = []
                if saved_schedule.get('time') != '15:45':
                    schedule_issues.append(f"执行时间错误: 期望15:45, 实际{saved_schedule.get('time')}")
                if not saved_schedule.get('end_time'):
                    schedule_issues.append(f"结束时间丢失: 应该有值但实际为{saved_schedule.get('end_time')}")
                if saved_schedule.get('max_runs') != 20:
                    schedule_issues.append(f"最大执行次数错误: 期望20, 实际{saved_schedule.get('max_runs')}")
                
                # 报告结果
                if not config_issues and not schedule_issues:
                    print(f"  ✅ 所有配置都正确保存")
                else:
                    print(f"  ❌ 发现配置保存问题:")
                    for issue in config_issues:
                        print(f"    - {issue}")
                    for issue in schedule_issues:
                        print(f"    - {issue}")
                
                # 步骤5: 检查Redis原始数据
                print(f"\n📋 步骤5: 检查Redis原始数据")
                
                config_key = f"monitoring_tasks:tasks:{task_id}:config"
                schedule_key = f"monitoring_tasks:tasks:{task_id}:schedule"
                
                redis_config = r.hgetall(config_key)
                redis_schedule = r.hgetall(schedule_key)
                
                print(f"  Redis配置数据: {dict(redis_config)}")
                print(f"  Redis调度数据: {dict(redis_schedule)}")
                
            else:
                print(f"  ❌ 获取任务详情失败: {detail_response.text}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 URL池任务创建配置测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:url_pool_task_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_url_pool_task_creation()
