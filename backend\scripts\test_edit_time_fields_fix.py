#!/usr/bin/env python3
"""
测试编辑任务时间字段修复效果

验证每日执行时间和结束时间的正确读取和显示
"""

import asyncio
import sys
import os
import json
import requests
import redis
import hashlib
from pathlib import Path
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def test_edit_time_fields_fix():
    """测试编辑任务时间字段修复效果"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试编辑任务时间字段修复效果")
    print("=" * 60)
    
    created_url_ids = []
    task_ids = []
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 步骤1: 创建测试URL
        print("\n📋 步骤1: 创建测试URL")
        
        test_url = {
            "url": "https://www.mercadolibre.com.ar/time-fields-fix-test",
            "platform": "mercadolibre",
            "title": "时间字段修复测试URL",
        }
        
        # 生成URL ID和哈希
        url_id = str(uuid4())
        url_hash = hashlib.md5(test_url['url'].encode()).hexdigest()
        now = datetime.now().isoformat()
        
        # 创建URL项目数据
        url_item_data = {
            'id': url_id,
            'url': test_url['url'],
            'platform': test_url['platform'],
            'source_file': 'time_fields_fix_test',
            'added_at': now,
            'status': 'active',
            'last_check': '',
            'check_count': '0',
            'success_count': '0',
            'error_count': '0',
            'metadata': json.dumps({
                'url_hash': url_hash,
                'title': test_url.get('title', ''),
                'added_via': 'time_fields_fix_test'
            })
        }
        
        # 存储到Redis
        url_key = f"url_pool:items:{url_id}"
        r.hset(url_key, mapping=url_item_data)
        
        # 添加到索引
        r.sadd("url_pool:all_ids", url_id)
        r.sadd(f"url_pool:platform:{test_url['platform']}", url_id)
        r.sadd("url_pool:status:active", url_id)
        r.sadd("url_pool:source:time_fields_fix_test", url_id)
        r.sadd("url_pool:url_hashes", url_hash)
        
        created_url_ids.append(url_id)
        print(f"  ✅ URL创建成功: {url_id}")
        
        # 步骤2: 创建任务（使用明确的时间配置）
        print(f"\n📋 步骤2: 创建任务（使用明确的时间配置）")
        
        create_data = {
            "name": "时间字段修复测试任务",
            "description": "用于测试编辑页面时间字段修复的任务",
            "url_ids": [url_id],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "19:15",  # 每日执行时间（存储在time字段）
                "end_time": "23:30",  # 结束时间
                "timezone": "Asia/Shanghai",
                "max_runs": 40,
                "enable_random_delay": True,
                "random_delay_min": 20,
                "random_delay_max": 90
            },
            "config": {
                "platform": "mercadolibre",
                "priority": "high",
                "batch_size": 30,
                "timeout": 600,
                "retry_count": 7,
                "concurrent_limit": 18,
                "enable_notifications": True
            },
            "tags": ["time_fields_fix_test"]
        }
        
        print(f"  创建任务时的时间配置:")
        print(f"    每日执行时间 (time): {create_data['schedule']['time']}")
        print(f"    结束时间 (end_time): {create_data['schedule']['end_time']}")
        print(f"    调度类型: {create_data['schedule']['type']}")
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create-from-urls",
            json=create_data,
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            task_id = create_result.get('task_id')
            task_ids.append(task_id)
            print(f"  ✅ 任务创建成功: {task_id}")
            
            # 步骤3: 验证后端存储
            print(f"\n📋 步骤3: 验证后端存储")
            
            detail_response = requests.get(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
            if detail_response.status_code == 200:
                detail_result = detail_response.json()
                task_data = detail_result.get('data', {})
                api_schedule = task_data.get('schedule', {})
                
                print(f"  后端API返回的调度配置:")
                print(f"    type: {api_schedule.get('type')}")
                print(f"    time: {api_schedule.get('time')} (每日执行时间)")
                print(f"    end_time: {api_schedule.get('end_time')} (结束时间)")
                print(f"    timezone: {api_schedule.get('timezone')}")
                
                # 验证存储正确性
                storage_correct = (
                    api_schedule.get('type') == 'daily' and
                    api_schedule.get('time') == '19:15' and
                    api_schedule.get('end_time') == '23:30'
                )
                
                if storage_correct:
                    print(f"  ✅ 后端存储完全正确")
                else:
                    print(f"  ❌ 后端存储有问题")
                    return
                
                # 步骤4: 前端修复验证指导
                print(f"\n📋 步骤4: 前端修复验证指导")
                
                print(f"  🌐 监控任务页面: http://localhost:3000/monitoring-tasks")
                print(f"  📝 任务ID: {task_id}")
                print(f"  🎯 测试任务名称: 时间字段修复测试任务")
                
                print(f"\n  📋 详细测试步骤:")
                print(f"    1. 访问监控任务页面")
                print(f"    2. 找到测试任务（搜索'时间字段修复测试任务'）")
                print(f"    3. 点击'编辑'按钮")
                print(f"    4. 进入调度配置步骤")
                print(f"    5. 检查时间字段显示:")
                
                print(f"\n  ✅ 修复后的期望效果:")
                print(f"    📍 每日执行时间:")
                print(f"      - 应该正确显示: 19:15")
                print(f"      - 不应该显示: undefined 或空白")
                print(f"      - 字段来源: scheduleConfig.time (修复后)")
                print(f"    📍 结束时间:")
                print(f"      - 应该正确显示: 23:30")
                print(f"      - 不应该显示: Invalid Date")
                print(f"      - 不应该有警告: TimePicker警告")
                
                print(f"\n  🔧 修复的关键问题:")
                print(f"    1. ✅ 字段映射修复:")
                print(f"       - daily类型: 从time字段读取执行时间")
                print(f"       - 其他类型: 从start_time字段读取执行时间")
                print(f"    2. ✅ Invalid Date处理:")
                print(f"       - 添加了字符串验证")
                print(f"       - 添加了时间范围验证")
                print(f"       - 添加了错误处理")
                
                print(f"\n  🐛 修复前的问题:")
                print(f"    ❌ 字段映射错误: 将time字段映射到start_time")
                print(f"    ❌ Invalid Date: 时间解析失败导致TimePicker警告")
                print(f"    ❌ 显示错误: 每日执行时间显示为undefined")
                
                print(f"\n  🔍 调试信息检查:")
                print(f"    - 打开浏览器开发者工具")
                print(f"    - 查看Console中的ScheduleConfigStep日志")
                print(f"    - 确认currentScheduleConfig中:")
                print(f"      * time: '19:15' (不是undefined)")
                print(f"      * end_time: '23:30' (不是'Invalid Date')")
                
                # 等待用户测试
                print(f"\n💡 请按照上述步骤进行前端测试")
                print(f"🎯 重点验证时间字段的正确显示和无警告")
                input("测试完成后按回车键继续清理测试数据...")
                
            else:
                print(f"  ❌ 获取任务详情失败: {detail_response.text}")
        else:
            print(f"  ❌ 任务创建失败: {create_response.text}")
        
        print(f"\n🎉 时间字段修复测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print(f"\n📋 清理测试数据")
        
        # 清理测试任务
        for task_id in task_ids:
            try:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print(f"  ✅ 测试任务已清理: {task_id[:8]}...")
                else:
                    print(f"  ⚠️ 测试任务清理失败: {task_id[:8]}...")
            except Exception as e:
                print(f"  ⚠️ 清理任务时出错: {task_id[:8]}..., {e}")
        
        # 清理测试URL
        if created_url_ids:
            try:
                r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                for url_id in created_url_ids:
                    # 删除URL数据
                    url_key = f"url_pool:items:{url_id}"
                    r.delete(url_key)
                    
                    # 从索引中移除
                    r.srem("url_pool:all_ids", url_id)
                    r.srem("url_pool:platform:mercadolibre", url_id)
                    r.srem("url_pool:status:active", url_id)
                    r.srem("url_pool:source:time_fields_fix_test", url_id)
                    
                    # 清理可能残留的反向关联
                    url_tasks_key = f"monitoring_tasks:url_tasks:{url_id}"
                    r.delete(url_tasks_key)
                
                print(f"  ✅ 测试URL已清理: {len(created_url_ids)} 个")
            except Exception as e:
                print(f"  ⚠️ 清理URL时出错: {e}")


if __name__ == "__main__":
    test_edit_time_fields_fix()
