# 🚀 Grafana快速设置指南 - 5分钟创建有内容的监控面板

## 📋 准备工作
- ✅ Grafana运行中: http://localhost:3001
- ✅ 登录信息: admin/admin123
- ✅ 8个可用指标已验证

## 🎯 目标
创建一个显示真实数据的监控面板，包含：
- 服务状态 (UP/DOWN)
- CPU使用率 (当前约5.4%)
- 内存使用率 (当前约9.2%)
- 系统负载
- 网络流量

## 📊 步骤1: 创建Dashboard (1分钟)

1. **打开Grafana**: http://localhost:3001
2. **登录**: admin/admin123
3. **创建Dashboard**: 
   - 点击左侧 "+" 
   - 选择 "Dashboard"
   - 点击 "Add panel"

## 🟢 步骤2: 服务状态面板 (1分钟)

### 基础设置
- **Panel title**: Service Status
- **Visualization**: Stat

### 查询设置
```
Query: up
Legend: {{job}}
```

### 重要配置
- **Value mappings**:
  - 0 → "DOWN" (红色)
  - 1 → "UP" (绿色)
- **Color mode**: Background

**点击 "Apply" 保存**

## 📈 步骤3: CPU使用率面板 (1分钟)

### 基础设置
- **Panel title**: CPU Usage
- **Visualization**: Time series

### 查询设置
```
Query: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
Legend: CPU %
```

### 重要配置
- **Unit**: Percent (0-100)
- **Min**: 0, **Max**: 100
- **Thresholds**: 
  - Green: 0-70
  - Yellow: 70-90
  - Red: 90+

**点击 "Apply" 保存**

## 💾 步骤4: 内存使用率面板 (1分钟)

### 基础设置
- **Panel title**: Memory Usage
- **Visualization**: Time series

### 查询设置
```
Query: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100
Legend: Memory %
```

### 重要配置
- **Unit**: Percent (0-100)
- **Min**: 0, **Max**: 100
- **Thresholds**: 
  - Green: 0-80
  - Yellow: 80-95
  - Red: 95+

**点击 "Apply" 保存**

## ⚡ 步骤5: 系统负载面板 (1分钟)

### 基础设置
- **Panel title**: System Load
- **Visualization**: Time series

### 查询设置 (添加3个查询)
```
Query A: node_load1
Legend: 1m

Query B: node_load5
Legend: 5m

Query C: node_load15
Legend: 15m
```

### 重要配置
- **Unit**: Short
- **Min**: 0

**点击 "Apply" 保存**

## 💾 步骤6: 保存Dashboard

1. **保存**: 点击右上角保存图标
2. **名称**: "MonIt Live Monitoring"
3. **文件夹**: General
4. **保存**

## 🎯 预期结果

您现在应该看到：
- ✅ **服务状态**: 显示绿色 "UP" (Prometheus和Node Exporter)
- ✅ **CPU使用率**: 约5.4%的实时图表
- ✅ **内存使用率**: 约9.2%的实时图表  
- ✅ **系统负载**: 三条负载线 (1m: 0.03, 5m: 0.03, 15m: 0.05)

## 🔧 可选扩展 (额外2分钟)

### 网络流量面板
```
Query A: irate(node_network_receive_bytes_total{device!="lo"}[5m])
Legend: Received

Query B: irate(node_network_transmit_bytes_total{device!="lo"}[5m])  
Legend: Transmitted
```

## ⚙️ Dashboard设置

### 时间和刷新
- **时间范围**: Last 1 hour
- **刷新**: 30s
- **自动刷新**: ✅ 启用

## 🚨 故障排查

### 问题: 面板显示 "No data"
1. 检查查询语法是否完全正确
2. 确认Prometheus运行: http://localhost:9090
3. 在Prometheus中测试查询

### 问题: 数据不更新
1. 检查刷新设置
2. 确认时间范围设置
3. 验证数据源连接

## 📱 移动端优化

面板会自动适配移动设备，建议布局：
- 服务状态: 全宽
- CPU/内存: 各占一半宽度
- 系统负载: 全宽

## 🎉 完成！

恭喜！您现在有一个显示真实数据的监控面板：
- 🟢 实时服务状态
- 📊 CPU和内存使用趋势
- 📈 系统负载监控
- 🔄 30秒自动刷新

**总用时**: 约5分钟
**数据更新**: 实时
**可用性**: 24/7监控

---

**下一步**: 当Task 04 API开发完成后，可以添加更多业务指标面板！
