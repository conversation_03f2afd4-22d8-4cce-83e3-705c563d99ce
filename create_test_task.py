#!/usr/bin/env python3
"""
创建测试任务来验证Worker任务分配功能
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def create_test_task():
    """创建一个测试任务"""
    
    print("🔧 创建测试任务...")
    
    task_data = {
        "name": "测试任务-验证Worker分配",
        "description": "用于验证Worker任务分配功能的测试任务",
        "tags": ["test", "worker-assignment"],
        "schedule": {
            "type": "manual",
            "enabled": False
        },
        "urls": [
            "https://example.com/test1",
            "https://example.com/test2",
            "https://example.com/test3"
        ],
        "crawler_config": {
            "config_name": "测试配置",
            "user_agent": "TestBot/1.0",
            "delay_range": [1, 3],
            "timeout": 30,
            "max_retries": 3
        }
    }
    
    try:
        response = requests.post(f"{BASE_URL}/tasks/", json=task_data)
        if response.status_code == 201:
            task = response.json()
            print(f"✅ 测试任务创建成功:")
            print(f"   任务ID: {task['task_id']}")
            print(f"   任务名称: {task['name']}")
            print(f"   URL数量: {len(task.get('urls', []))}")
            return task['task_id']
        else:
            print(f"❌ 创建测试任务失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试任务异常: {e}")
        return None

def assign_task_to_worker(task_id, worker_id):
    """将任务分配给Worker"""
    
    print(f"\n🔧 将任务分配给Worker...")
    print(f"   任务ID: {task_id}")
    print(f"   Worker ID: {worker_id[:8]}...")
    
    assignment_data = {
        "task_id": task_id,
        "assignment_name": f"测试分配-{datetime.now().strftime('%H%M%S')}",
        "description": "测试Worker任务分配功能",
        "worker_ids": [worker_id],
        "assignment_strategy": "health_based",
        "auto_failover": True,
        "max_retries": 3
    }
    
    try:
        response = requests.post(f"{BASE_URL}/task-assignments/", json=assignment_data)
        if response.status_code == 201:
            assignment = response.json()
            print(f"✅ 任务分配创建成功:")
            print(f"   分配ID: {assignment['assignment_id']}")
            print(f"   分配名称: {assignment['assignment_name']}")
            print(f"   Worker数量: {len(assignment['worker_ids'])}")
            return assignment['assignment_id']
        else:
            print(f"❌ 创建任务分配失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 创建任务分配异常: {e}")
        return None

def test_worker_assignments(worker_id):
    """测试Worker任务分配显示"""
    
    print(f"\n🔍 测试Worker任务分配显示...")
    print(f"   Worker ID: {worker_id[:8]}...")
    
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}/assignments")
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ Worker任务分配查询成功:")
            print(f"   分配数量: {len(assignments)}")
            
            for i, assignment in enumerate(assignments, 1):
                print(f"   {i}. 分配: {assignment['assignment_name']}")
                print(f"      任务ID: {assignment['task_id']}")
                print(f"      状态: {assignment['assignment_status']}")
                print(f"      创建时间: {assignment['created_at']}")
            
            return assignments
        else:
            print(f"❌ Worker任务分配查询失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Worker任务分配查询异常: {e}")
        return []

def main():
    """主函数"""
    print("🚀 开始创建测试任务验证Worker分配功能\n")
    print("=" * 80)
    
    # 1. 创建测试任务
    task_id = create_test_task()
    if not task_id:
        print("❌ 无法创建测试任务，退出测试")
        return
    
    print("=" * 80)
    
    # 2. 获取一个Worker ID进行测试
    worker_id = "46ac2f77-1e6a-401c-8853-e22f03ccaa94"  # 11237 Worker
    
    # 3. 将任务分配给Worker
    assignment_id = assign_task_to_worker(task_id, worker_id)
    if not assignment_id:
        print("❌ 无法创建任务分配，退出测试")
        return
    
    print("=" * 80)
    
    # 4. 测试Worker任务分配显示
    assignments = test_worker_assignments(worker_id)
    
    print("=" * 80)
    
    # 5. 总结测试结果
    print(f"\n🎯 测试结果总结:")
    print(f"   测试任务创建: {'✅ 成功' if task_id else '❌ 失败'}")
    print(f"   任务分配创建: {'✅ 成功' if assignment_id else '❌ 失败'}")
    print(f"   Worker分配显示: {'✅ 成功' if assignments else '❌ 失败'}")
    
    if task_id and assignment_id and assignments:
        print(f"\n🎉 功能验证成功!")
        print(f"   ✅ 可以创建真实任务")
        print(f"   ✅ 可以创建任务分配")
        print(f"   ✅ Worker详情页面可以正确显示任务分配")
        print(f"   ✅ 无效任务分配被正确过滤")
    else:
        print(f"\n❌ 功能验证失败，需要进一步检查")
    
    print(f"\n📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📋 创建的资源:")
    if task_id:
        print(f"   - 任务ID: {task_id}")
    if assignment_id:
        print(f"   - 分配ID: {assignment_id}")

if __name__ == "__main__":
    main()
