# MonIt 部署和运维指南

## 📋 概述

本文档详细描述了MonIt系统新架构的部署方法、配置要求、监控方案和运维最佳实践。

## 🏗️ 系统架构部署

### 组件架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │     Redis       │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (Database)    │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Nginx       │
                    │  (Reverse Proxy)│
                    │   Port: 80/443  │
                    └─────────────────┘
```

## 🐳 Docker 部署

### 开发环境部署

#### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd MonIt

# 确保Docker和Docker Compose已安装
docker --version
docker-compose --version
```

#### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境变量
vim .env
```

**环境变量配置 (.env):**
```bash
# 基础配置
ENVIRONMENT=development
DEBUG=true

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# 前端配置
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws

# 安全配置
JWT_SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 监控配置
MONITORING_ENABLED=true
METRICS_PORT=9090
```

#### 3. 启动服务
```bash
# 开发环境启动
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f
```

### 生产环境部署

#### 1. 生产环境配置
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      - REACT_APP_API_URL=https://api.yourdomain.com
      - REACT_APP_WS_URL=wss://api.yourdomain.com/ws
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - REDIS_URL=redis://redis:6379/0
      - API_WORKERS=8
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    restart: unless-stopped

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    driver: bridge
```

#### 2. Nginx配置
```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server frontend:3000;
    }

    upstream backend {
        server backend:8000;
    }

    # Frontend
    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Backend API
    server {
        listen 80;
        server_name api.yourdomain.com;

        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket支持
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    # SSL配置 (使用Let's Encrypt)
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 🔧 配置管理

### 环境变量管理
```bash
# 生产环境变量
ENVIRONMENT=production
DEBUG=false
API_WORKERS=8
REDIS_PASSWORD=strong-password-here
JWT_SECRET_KEY=very-strong-secret-key
GRAFANA_PASSWORD=admin-password

# 数据库配置
REDIS_MAXMEMORY=2gb
REDIS_MAXMEMORY_POLICY=allkeys-lru

# 安全配置
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com
CORS_ORIGINS=https://yourdomain.com

# 监控配置
SENTRY_DSN=https://your-sentry-dsn
LOG_LEVEL=WARNING
```

### 配置文件模板
```python
# backend/app/config.py
import os
from typing import List

class Settings:
    # 基础配置
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # API配置
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8000"))
    API_WORKERS: int = int(os.getenv("API_WORKERS", "4"))
    
    # Redis配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # 安全配置
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "dev-secret-key")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_EXPIRE_MINUTES: int = int(os.getenv("JWT_EXPIRE_MINUTES", "1440"))
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = os.getenv("ALLOWED_HOSTS", "*").split(",")
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", "*").split(",")
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = os.getenv("LOG_FORMAT", "json")
    
    # 监控配置
    MONITORING_ENABLED: bool = os.getenv("MONITORING_ENABLED", "true").lower() == "true"
    METRICS_PORT: int = int(os.getenv("METRICS_PORT", "9090"))
    SENTRY_DSN: str = os.getenv("SENTRY_DSN", "")

settings = Settings()
```

## 📊 监控和日志

### Prometheus配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'monit-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana仪表板
```json
{
  "dashboard": {
    "title": "MonIt System Monitoring",
    "panels": [
      {
        "title": "API请求率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Worker状态",
        "type": "stat",
        "targets": [
          {
            "expr": "crawler_workers_active",
            "legendFormat": "活跃Worker"
          }
        ]
      },
      {
        "title": "任务分配成功率",
        "type": "gauge",
        "targets": [
          {
            "expr": "rate(task_assignments_successful[5m]) / rate(task_assignments_total[5m]) * 100",
            "legendFormat": "成功率"
          }
        ]
      }
    ]
  }
}
```

### 日志配置
```python
# backend/app/logging_config.py
import logging
import sys
from pythonjsonlogger import jsonlogger

def setup_logging():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # JSON格式日志
    handler = logging.StreamHandler(sys.stdout)
    formatter = jsonlogger.JsonFormatter(
        '%(asctime)s %(name)s %(levelname)s %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger
```

## 🔒 安全配置

### SSL/TLS配置
```bash
# 使用Let's Encrypt获取SSL证书
certbot certonly --webroot -w /var/www/html -d yourdomain.com -d www.yourdomain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 防火墙配置
```bash
# UFW配置
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

### 安全头配置
```nginx
# 在nginx配置中添加安全头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

## 🚀 性能优化

### Redis优化
```bash
# redis.conf优化配置
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
tcp-keepalive 300
timeout 0
```

### 应用优化
```python
# 连接池配置
REDIS_POOL_SIZE = 20
REDIS_POOL_MAX_CONNECTIONS = 50

# 缓存配置
CACHE_TTL = 300  # 5分钟
CACHE_MAX_SIZE = 1000

# 异步配置
ASYNC_WORKERS = 8
ASYNC_QUEUE_SIZE = 1000
```

## 📋 运维检查清单

### 日常检查
- [ ] 检查服务状态
- [ ] 查看错误日志
- [ ] 监控资源使用
- [ ] 检查磁盘空间
- [ ] 验证备份完整性

### 周期性维护
- [ ] 更新系统补丁
- [ ] 清理日志文件
- [ ] 优化数据库
- [ ] 检查SSL证书
- [ ] 性能测试

### 故障处理
```bash
# 服务重启
docker-compose restart backend
docker-compose restart frontend

# 查看详细日志
docker-compose logs -f --tail=100 backend

# 数据库备份
docker exec redis redis-cli BGSAVE

# 健康检查
curl -f http://localhost:8000/health || exit 1
```

## 📈 扩展部署

### 水平扩展
```yaml
# 多实例部署
services:
  backend:
    deploy:
      replicas: 3
    environment:
      - INSTANCE_ID=${HOSTNAME}

  nginx:
    depends_on:
      - backend
    volumes:
      - ./nginx/upstream.conf:/etc/nginx/conf.d/upstream.conf
```

### 负载均衡配置
```nginx
upstream backend_pool {
    least_conn;
    server backend_1:8000 weight=3;
    server backend_2:8000 weight=2;
    server backend_3:8000 weight=1;
}
```

## 🔄 备份和恢复

### 自动备份脚本
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Redis备份
docker exec redis redis-cli BGSAVE
docker cp redis:/data/dump.rdb $BACKUP_DIR/redis_$DATE.rdb

# 配置文件备份
tar -czf $BACKUP_DIR/config_$DATE.tar.gz .env docker-compose.prod.yml nginx/

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.rdb" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 恢复流程
```bash
# 停止服务
docker-compose down

# 恢复Redis数据
docker cp backup/redis_20240101_120000.rdb redis:/data/dump.rdb

# 恢复配置
tar -xzf backup/config_20240101_120000.tar.gz

# 重启服务
docker-compose up -d
```
