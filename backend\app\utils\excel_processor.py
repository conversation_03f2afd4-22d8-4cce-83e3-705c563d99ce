"""
Excel文件处理器

用于读取和处理Excel文件中的商品链接数据
"""

import pandas as pd
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import re

logger = logging.getLogger(__name__)


class ExcelProcessor:
    """Excel文件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def read_product_links(self, excel_path: str) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """读取Excel文件中的商品链接
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            Tuple[List[Dict[str, Any]], Dict[str, Any]]: (商品数据列表, 统计信息)
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_path)
            
            self.logger.info(f"Successfully loaded Excel file: {excel_path}")
            self.logger.info(f"Excel shape: {df.shape}")
            self.logger.info(f"Columns: {list(df.columns)}")
            
            # 根据ToDo.txt中的描述，列结构应该是：
            # A：中文品名
            # B：原始西班牙语品名  
            # C：商品链接
            
            # 检查列数
            if df.shape[1] < 3:
                raise ValueError(f"Excel file should have at least 3 columns, but got {df.shape[1]}")
            
            # 重命名列以便处理
            column_names = ['chinese_name', 'spanish_name', 'product_url']
            if df.shape[1] > 3:
                # 如果有更多列，保留它们
                for i in range(3, df.shape[1]):
                    column_names.append(f'extra_column_{i}')
            
            df.columns = column_names[:df.shape[1]]
            
            # 清理和验证数据
            products = []
            invalid_count = 0
            
            for index, row in df.iterrows():
                try:
                    # 提取基本信息
                    chinese_name = str(row['chinese_name']).strip() if pd.notna(row['chinese_name']) else ""
                    spanish_name = str(row['spanish_name']).strip() if pd.notna(row['spanish_name']) else ""
                    product_url = str(row['product_url']).strip() if pd.notna(row['product_url']) else ""
                    
                    # 验证URL
                    if not self._is_valid_url(product_url):
                        self.logger.warning(f"Invalid URL at row {index + 1}: {product_url}")
                        invalid_count += 1
                        continue
                    
                    # 检测平台
                    platform = self._detect_platform(product_url)
                    
                    product_data = {
                        'row_index': index + 1,
                        'chinese_name': chinese_name,
                        'spanish_name': spanish_name,
                        'product_url': product_url,
                        'platform': platform,
                        'source_file': Path(excel_path).name
                    }
                    
                    # 添加额外的列数据
                    for col in df.columns[3:]:
                        if pd.notna(row[col]):
                            product_data[col] = str(row[col]).strip()
                    
                    products.append(product_data)
                    
                except Exception as e:
                    self.logger.error(f"Error processing row {index + 1}: {e}")
                    invalid_count += 1
                    continue
            
            # 生成统计信息
            stats = {
                'total_rows': len(df),
                'valid_products': len(products),
                'invalid_rows': invalid_count,
                'platforms': self._get_platform_stats(products),
                'file_info': {
                    'name': Path(excel_path).name,
                    'size': Path(excel_path).stat().st_size,
                    'processed_at': datetime.now().isoformat()
                }
            }
            
            self.logger.info(f"Processed {len(products)} valid products from {len(df)} rows")
            self.logger.info(f"Platform distribution: {stats['platforms']}")
            
            return products, stats
            
        except Exception as e:
            self.logger.error(f"Failed to read Excel file {excel_path}: {e}")
            raise
    
    def _is_valid_url(self, url: str) -> bool:
        """验证URL是否有效
        
        Args:
            url: 待验证的URL
            
        Returns:
            bool: 是否有效
        """
        if not url or len(url.strip()) == 0:
            return False
        
        # 基本URL格式检查
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return bool(url_pattern.match(url.strip()))
    
    def _detect_platform(self, url: str) -> str:
        """检测URL所属的平台
        
        Args:
            url: 商品URL
            
        Returns:
            str: 平台名称
        """
        url_lower = url.lower()
        
        if 'mercadolibre' in url_lower or 'mercadolivre' in url_lower:
            return 'mercadolibre'
        elif 'amazon' in url_lower:
            return 'amazon'
        elif 'ebay' in url_lower:
            return 'ebay'
        elif 'aliexpress' in url_lower:
            return 'aliexpress'
        else:
            return 'unknown'
    
    def _get_platform_stats(self, products: List[Dict[str, Any]]) -> Dict[str, int]:
        """获取平台统计信息
        
        Args:
            products: 商品数据列表
            
        Returns:
            Dict[str, int]: 平台统计
        """
        platform_counts = {}
        for product in products:
            platform = product.get('platform', 'unknown')
            platform_counts[platform] = platform_counts.get(platform, 0) + 1
        
        return platform_counts
    
    def export_results_to_excel(
        self,
        results: List[Dict[str, Any]],
        output_path: str,
        include_metadata: bool = True
    ) -> str:
        """导出爬取结果到Excel
        
        Args:
            results: 爬取结果列表
            output_path: 输出文件路径
            include_metadata: 是否包含元数据
            
        Returns:
            str: 实际输出文件路径
        """
        try:
            # 准备数据
            export_data = []
            
            for result in results:
                row_data = {
                    'timestamp': result.get('timestamp', ''),
                    'spanish_name': result.get('spanish_name', ''),
                    'product_url': result.get('product_url', ''),
                    'image_url': result.get('image_url', ''),
                    'current_price': result.get('current_price', ''),
                    'original_price': result.get('original_price', ''),
                    'discount_rate': result.get('discount_rate', ''),
                    'sales_count': result.get('sales_count', ''),
                    'stock_quantity': result.get('stock_quantity', ''),
                    'rating_score': result.get('rating_score', ''),
                    'rating_count': result.get('rating_count', ''),
                }
                
                # 添加其他字段
                for key, value in result.items():
                    if key not in row_data:
                        row_data[key] = value
                
                export_data.append(row_data)
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 确保输出目录存在
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 如果文件名没有时间戳，添加时间戳
            if not re.search(r'\d{12,14}', output_path.stem):
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                output_path = output_path.parent / f"{output_path.stem}_{timestamp}{output_path.suffix}"
            
            # 导出到Excel
            with pd.ExcelWriter(str(output_path), engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
                
                # 如果包含元数据，添加统计信息
                if include_metadata:
                    metadata = {
                        'total_results': len(results),
                        'export_time': datetime.now().isoformat(),
                        'successful_crawls': len([r for r in results if r.get('success', False)]),
                        'failed_crawls': len([r for r in results if not r.get('success', False)])
                    }
                    
                    metadata_df = pd.DataFrame([metadata])
                    metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
            
            self.logger.info(f"Results exported to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Failed to export results to Excel: {e}")
            raise
    
    def validate_excel_structure(self, excel_path: str) -> Dict[str, Any]:
        """验证Excel文件结构
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            df = pd.read_excel(excel_path)
            
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'info': {
                    'total_rows': len(df),
                    'total_columns': len(df.columns),
                    'columns': list(df.columns)
                }
            }
            
            # 检查最少列数
            if len(df.columns) < 3:
                validation_result['is_valid'] = False
                validation_result['errors'].append(
                    f"Excel file should have at least 3 columns (中文品名, 西班牙语品名, 商品链接), "
                    f"but got {len(df.columns)}"
                )
            
            # 检查是否有数据
            if len(df) == 0:
                validation_result['is_valid'] = False
                validation_result['errors'].append("Excel file is empty")
            
            # 检查URL列的有效性
            if len(df.columns) >= 3:
                url_column = df.iloc[:, 2]  # 第三列应该是URL
                invalid_urls = 0
                
                for idx, url in enumerate(url_column):
                    if pd.notna(url) and not self._is_valid_url(str(url)):
                        invalid_urls += 1
                
                if invalid_urls > 0:
                    validation_result['warnings'].append(
                        f"Found {invalid_urls} invalid URLs in column C"
                    )
            
            return validation_result
            
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f"Failed to read Excel file: {str(e)}"],
                'warnings': [],
                'info': {}
            }
