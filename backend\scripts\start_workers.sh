#!/bin/bash

# Celery Worker 启动脚本
# 启动多个专门的worker来处理不同类型的任务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Redis连接
check_redis() {
    log_info "Checking Redis connection..."
    if redis-cli ping > /dev/null 2>&1; then
        log_info "Redis is running"
    else
        log_error "Redis is not running. Please start Redis first."
        exit 1
    fi
}

# 检查数据库连接
check_database() {
    log_info "Checking database connection..."
    python -c "
import asyncio
from app.database import get_async_session

async def test_db():
    try:
        async with get_async_session() as session:
            await session.execute('SELECT 1')
        print('Database connection OK')
    except Exception as e:
        print(f'Database connection failed: {e}')
        exit(1)

asyncio.run(test_db())
" || {
        log_error "Database connection failed"
        exit 1
    }
}

# 创建必要的目录
create_directories() {
    log_info "Creating necessary directories..."
    mkdir -p logs/celery
    mkdir -p data/celery
}

# 启动Celery Beat (定时任务调度器)
start_beat() {
    log_info "Starting Celery Beat..."
    celery -A app.celery_app beat \
        --loglevel=info \
        --logfile=logs/celery/beat.log \
        --pidfile=data/celery/beat.pid \
        --detach
    
    if [ $? -eq 0 ]; then
        log_info "Celery Beat started successfully"
    else
        log_error "Failed to start Celery Beat"
        exit 1
    fi
}

# 启动爬虫队列Worker
start_crawler_worker() {
    log_info "Starting Crawler Worker..."
    celery -A app.celery_app worker \
        --loglevel=info \
        --logfile=logs/celery/crawler_worker.log \
        --pidfile=data/celery/crawler_worker.pid \
        --queues=crawler_queue,crawler_queue_high \
        --concurrency=2 \
        --hostname=crawler_worker@%h \
        --detach
    
    if [ $? -eq 0 ]; then
        log_info "Crawler Worker started successfully"
    else
        log_error "Failed to start Crawler Worker"
        exit 1
    fi
}

# 启动监控队列Worker
start_monitor_worker() {
    log_info "Starting Monitor Worker..."
    celery -A app.celery_app worker \
        --loglevel=info \
        --logfile=logs/celery/monitor_worker.log \
        --pidfile=data/celery/monitor_worker.pid \
        --queues=monitor_queue,monitor_queue_high \
        --concurrency=1 \
        --hostname=monitor_worker@%h \
        --detach
    
    if [ $? -eq 0 ]; then
        log_info "Monitor Worker started successfully"
    else
        log_error "Failed to start Monitor Worker"
        exit 1
    fi
}

# 启动处理队列Worker
start_processor_worker() {
    log_info "Starting Processor Worker..."
    celery -A app.celery_app worker \
        --loglevel=info \
        --logfile=logs/celery/processor_worker.log \
        --pidfile=data/celery/processor_worker.pid \
        --queues=processor_queue \
        --concurrency=2 \
        --hostname=processor_worker@%h \
        --detach
    
    if [ $? -eq 0 ]; then
        log_info "Processor Worker started successfully"
    else
        log_error "Failed to start Processor Worker"
        exit 1
    fi
}

# 启动清理队列Worker
start_cleanup_worker() {
    log_info "Starting Cleanup Worker..."
    celery -A app.celery_app worker \
        --loglevel=info \
        --logfile=logs/celery/cleanup_worker.log \
        --pidfile=data/celery/cleanup_worker.pid \
        --queues=cleanup_queue_low \
        --concurrency=1 \
        --hostname=cleanup_worker@%h \
        --detach
    
    if [ $? -eq 0 ]; then
        log_info "Cleanup Worker started successfully"
    else
        log_error "Failed to start Cleanup Worker"
        exit 1
    fi
}

# 停止所有Worker
stop_workers() {
    log_info "Stopping all Celery workers..."
    
    # 停止Beat
    if [ -f data/celery/beat.pid ]; then
        kill $(cat data/celery/beat.pid) 2>/dev/null || true
        rm -f data/celery/beat.pid
        log_info "Celery Beat stopped"
    fi
    
    # 停止所有Worker
    for worker in crawler_worker monitor_worker processor_worker cleanup_worker; do
        if [ -f "data/celery/${worker}.pid" ]; then
            kill $(cat "data/celery/${worker}.pid") 2>/dev/null || true
            rm -f "data/celery/${worker}.pid"
            log_info "${worker} stopped"
        fi
    done
    
    # 强制杀死所有celery进程
    pkill -f "celery.*worker" 2>/dev/null || true
    pkill -f "celery.*beat" 2>/dev/null || true
    
    log_info "All workers stopped"
}

# 检查Worker状态
check_status() {
    log_info "Checking Celery workers status..."
    
    echo -e "\n${BLUE}=== Celery Inspect Status ===${NC}"
    celery -A app.celery_app inspect active
    
    echo -e "\n${BLUE}=== Celery Inspect Stats ===${NC}"
    celery -A app.celery_app inspect stats
    
    echo -e "\n${BLUE}=== Queue Lengths ===${NC}"
    python -c "
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
queues = ['crawler_queue', 'crawler_queue_high', 'monitor_queue', 'processor_queue', 'cleanup_queue_low']
for queue in queues:
    length = r.llen(queue)
    print(f'{queue}: {length} tasks')
"
}

# 显示日志
show_logs() {
    log_info "Showing recent Celery logs..."
    
    echo -e "\n${BLUE}=== Crawler Worker Logs ===${NC}"
    tail -n 20 logs/celery/crawler_worker.log 2>/dev/null || echo "No crawler worker logs found"
    
    echo -e "\n${BLUE}=== Monitor Worker Logs ===${NC}"
    tail -n 20 logs/celery/monitor_worker.log 2>/dev/null || echo "No monitor worker logs found"
    
    echo -e "\n${BLUE}=== Beat Logs ===${NC}"
    tail -n 20 logs/celery/beat.log 2>/dev/null || echo "No beat logs found"
}

# 重启所有Worker
restart_workers() {
    log_info "Restarting all Celery workers..."
    stop_workers
    sleep 3
    start_all_workers
}

# 启动所有Worker
start_all_workers() {
    log_info "Starting all Celery workers..."
    
    # 检查依赖
    check_redis
    check_database
    
    # 创建目录
    create_directories
    
    # 启动所有组件
    start_beat
    sleep 2
    start_crawler_worker
    sleep 1
    start_monitor_worker
    sleep 1
    start_processor_worker
    sleep 1
    start_cleanup_worker
    
    log_info "All workers started successfully!"
    log_info "Use 'celery -A app.celery_app flower' to start the monitoring web interface"
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            start_all_workers
            ;;
        stop)
            stop_workers
            ;;
        restart)
            restart_workers
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs
            ;;
        beat)
            start_beat
            ;;
        crawler)
            start_crawler_worker
            ;;
        monitor)
            start_monitor_worker
            ;;
        processor)
            start_processor_worker
            ;;
        cleanup)
            start_cleanup_worker
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|logs|beat|crawler|monitor|processor|cleanup}"
            echo ""
            echo "Commands:"
            echo "  start     - Start all workers and beat"
            echo "  stop      - Stop all workers and beat"
            echo "  restart   - Restart all workers and beat"
            echo "  status    - Show worker status and queue lengths"
            echo "  logs      - Show recent logs"
            echo "  beat      - Start only Celery Beat"
            echo "  crawler   - Start only Crawler Worker"
            echo "  monitor   - Start only Monitor Worker"
            echo "  processor - Start only Processor Worker"
            echo "  cleanup   - Start only Cleanup Worker"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
